                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bg1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedFC.c -o iedTree\gh_bg1.o -list=iedTree/iedFC.lst C:\Users\<USER>\AppData\Local\Temp\gh_bg1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_bg1.s
Source File: iedFC.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		iedTree/iedFC.c -o iedTree/iedFC.o

                      12 ;Source File:   iedTree/iedFC.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:49 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "iedFC.h"


                      23 ;2: 


                      24 ;3: 


                      25 ;4: enum IEDFCType


                      26 ;5: {    


                      27 ;6:     IED_FC_TYPE_ST = 0,


                      28 ;7:     IED_FC_TYPE_MX,


                      29 ;8:     IED_FC_TYPE_SP,


                      30 ;9:     IED_FC_TYPE_SV,


                      31 ;10:     IED_FC_TYPE_CF,


                      32 ;11:     IED_FC_TYPE_DC,


                      33 ;12:     IED_FC_TYPE_SG,


                      34 ;13:     IED_FC_TYPE_SE,


                      35 ;14:     IED_FC_TYPE_SR,


                      36 ;15:     IED_FC_TYPE_OR,


                      37 ;16:     IED_FC_TYPE_BL,


                      38 ;17:     IED_FC_TYPE_EX,


                      39 ;18: 


                      40 ;19:     // Не упоминаются в 7.3 Annex B


                      41 ;20:     IED_FC_TYPE_CO,


                      42 ;21:     IED_FC_TYPE_US,


                      43 ;22:     IED_FC_TYPE_MS,


                      44 ;23:     IED_FC_TYPE_RP,


                      45 ;24:     IED_FC_TYPE_BR,


                      46 ;25:     IED_FC_TYPE_GO,


                      47 ;26: 


                      48 ;27:     IED_FC_TYPE_COUNT,


                      49 ;28:     IED_FC_TYPE_UNKNOWN = 0xFF,


                      50 ;29: };



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bg1.s
                      51 ;30: 


                      52 ;31: const char* IEDFCTypeNames[IED_FC_TYPE_COUNT] = {


                      53 ;32:     "ST", 


                      54 ;33:     "MX", 


                      55 ;34:     "SP",


                      56 ;35:     "SV",


                      57 ;36:     "CF",


                      58 ;37:     "DC",


                      59 ;38:     "SG",


                      60 ;39:     "SE",


                      61 ;40:     "SR",


                      62 ;41:     "OR",


                      63 ;42:     "BL",


                      64 ;43:     "EX",    


                      65 ;44:     "CO",


                      66 ;45:     "US",


                      67 ;46:     "MS",


                      68 ;47:     "RP",


                      69 ;48:     "BR",


                      70 ;49:     "GO"


                      71 ;50: };


                      72 ;51: 


                      73 ;52: static enum IEDFCType getFCType(StringView* fcName)


                      74 

                      75 ;67: }


                      76 

                      77 ;68: 


                      78 ;69: static bool isReadOnlyFC(enum IEDFCType fcType)


                      79 

                      80 ;82:     }


                      81 ;83: }


                      82 

                      83 ;84: 


                      84 ;85: 


                      85 ;86: bool IEDFC_init(IEDEntity entity)


                      86 	.text

                      87 	.align	4

                      88 IEDFC_init::

00000000 e92d44f0     89 	stmfd	[sp]!,{r4-r7,r10,lr}

00000004 e1a05000     90 	mov	r5,r0

                      91 ;87: {


                      92 

                      93 ;88:     enum IEDFCType fcType;


                      94 ;89:     entity->type = IED_ENTITY_FC;


                      95 

00000008 e3a00004     96 	mov	r0,4

0000000c e5850050     97 	str	r0,[r5,80]

                      98 ;90:     fcType = getFCType(&entity->name);


                      99 

                     100 ;53: {    


                     101 

                     102 ;54:     int i;


                     103 ;55:     if(fcName->len != 2)


                     104 

00000010 e5950048    105 	ldr	r0,[r5,72]

00000014 e3500002    106 	cmp	r0,2

00000018 1a00004c    107 	bne	.L112

                     108 ;56:     {


                     109 

                     110 ;57:         return IED_FC_TYPE_UNKNOWN;


                     111 


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bg1.s
                     112 ;58:     }


                     113 ;59:     for(i = 0; i < IED_FC_TYPE_COUNT; i++)


                     114 

0000001c e3a04000    115 	mov	r4,0

00000020 e59fa148*   116 	ldr	r10,.L522

00000024 e3a07002    117 	mov	r7,2

00000028 e1a0600a    118 	mov	r6,r10

                     119 .L161:

0000002c e7961104    120 	ldr	r1,[r6,r4 lsl 2]

00000030 e2850048    121 	add	r0,r5,72

00000034 eb000000*   122 	bl	StringView_cmpCStr

00000038 e3500000    123 	cmp	r0,0

0000003c 0a000038    124 	beq	.L57

00000040 e2844001    125 	add	r4,r4,1

00000044 e7961104    126 	ldr	r1,[r6,r4 lsl 2]

00000048 e2850048    127 	add	r0,r5,72

0000004c eb000000*   128 	bl	StringView_cmpCStr

00000050 e3500000    129 	cmp	r0,0

00000054 0a000032    130 	beq	.L57

00000058 e2844001    131 	add	r4,r4,1

0000005c e7961104    132 	ldr	r1,[r6,r4 lsl 2]

00000060 e2850048    133 	add	r0,r5,72

00000064 eb000000*   134 	bl	StringView_cmpCStr

00000068 e3500000    135 	cmp	r0,0

0000006c 0a00002c    136 	beq	.L57

00000070 e2844001    137 	add	r4,r4,1

00000074 e7961104    138 	ldr	r1,[r6,r4 lsl 2]

00000078 e2850048    139 	add	r0,r5,72

0000007c eb000000*   140 	bl	StringView_cmpCStr

00000080 e3500000    141 	cmp	r0,0

00000084 0a000026    142 	beq	.L57

00000088 e2844001    143 	add	r4,r4,1

0000008c e7961104    144 	ldr	r1,[r6,r4 lsl 2]

00000090 e2850048    145 	add	r0,r5,72

00000094 eb000000*   146 	bl	StringView_cmpCStr

00000098 e3500000    147 	cmp	r0,0

0000009c 0a000020    148 	beq	.L57

000000a0 e2844001    149 	add	r4,r4,1

000000a4 e7961104    150 	ldr	r1,[r6,r4 lsl 2]

000000a8 e2850048    151 	add	r0,r5,72

000000ac eb000000*   152 	bl	StringView_cmpCStr

000000b0 e3500000    153 	cmp	r0,0

000000b4 0a00001a    154 	beq	.L57

000000b8 e2844001    155 	add	r4,r4,1

000000bc e7961104    156 	ldr	r1,[r6,r4 lsl 2]

000000c0 e2850048    157 	add	r0,r5,72

000000c4 eb000000*   158 	bl	StringView_cmpCStr

000000c8 e3500000    159 	cmp	r0,0

000000cc 0a000014    160 	beq	.L57

000000d0 e2844001    161 	add	r4,r4,1

000000d4 e7961104    162 	ldr	r1,[r6,r4 lsl 2]

000000d8 e2850048    163 	add	r0,r5,72

000000dc eb000000*   164 	bl	StringView_cmpCStr

000000e0 e3500000    165 	cmp	r0,0

000000e4 0a00000e    166 	beq	.L57

000000e8 e2844001    167 	add	r4,r4,1

000000ec e2577001    168 	subs	r7,r7,1

000000f0 1affffcd    169 	bne	.L161

000000f4 e3a07002    170 	mov	r7,2

000000f8 e08a6104    171 	add	r6,r10,r4 lsl 2

                     172 .L185:


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bg1.s
000000fc e5961000    173 	ldr	r1,[r6]

00000100 e2850048    174 	add	r0,r5,72

00000104 eb000000*   175 	bl	StringView_cmpCStr

00000108 e3500000    176 	cmp	r0,0

0000010c 0a000004    177 	beq	.L57

00000110 e2866004    178 	add	r6,r6,4

00000114 e2844001    179 	add	r4,r4,1

00000118 e2577001    180 	subs	r7,r7,1

0000011c 1afffff6    181 	bne	.L185

00000120 ea00000a    182 	b	.L112

                     183 .L57:

                     184 ;64:         }


                     185 ;65:     }


                     186 ;66:     return IED_FC_TYPE_UNKNOWN;


                     187 

                     188 ;91:     entity->subType = fcType;


                     189 

00000124 e5854054    190 	str	r4,[r5,84]

                     191 ;92:     if(isReadOnlyFC(fcType))


                     192 

                     193 ;70: {


                     194 

                     195 ;71:     switch(fcType)


                     196 

00000128 e2540001    197 	subs	r0,r4,1

0000012c 9a00000a    198 	bls	.L68

00000130 e2500002    199 	subs	r0,r0,2

00000134 0a000008    200 	beq	.L68

00000138 e2500002    201 	subs	r0,r0,2

0000013c 0a000006    202 	beq	.L68

00000140 e2500005    203 	subs	r0,r0,5

00000144 e3500001    204 	cmp	r0,1

00000148 9a000003    205 	bls	.L68

0000014c ea000005    206 	b	.L55

                     207 .L112:

00000150 e3a000ff    208 	mov	r0,255

00000154 e5850054    209 	str	r0,[r5,84]

                     210 ;95:     }


                     211 ;96:     return true;


                     212 

00000158 ea000002    213 	b	.L55

                     214 .L68:

                     215 ;72:     {


                     216 ;73:     case IED_FC_TYPE_ST:


                     217 ;74:     case IED_FC_TYPE_MX:


                     218 ;75:     case IED_FC_TYPE_SV:    


                     219 ;76:     case IED_FC_TYPE_DC:// У нас пока description ReadOnly        


                     220 ;77:     case IED_FC_TYPE_BL:


                     221 ;78:     case IED_FC_TYPE_EX:


                     222 ;79:         return true;


                     223 

                     224 ;80:     default:


                     225 ;81:         return false;


                     226 

                     227 ;93:     {


                     228 

                     229 ;94:         IEDEntity_setReadOnlyRecursive(entity, true);


                     230 

0000015c e1a00005    231 	mov	r0,r5

00000160 e3a01001    232 	mov	r1,1

00000164 eb000000*   233 	bl	IEDEntity_setReadOnlyRecursive


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bg1.s
                     234 ;95:     }


                     235 ;96:     return true;


                     236 

                     237 .L55:

00000168 e3a00001    238 	mov	r0,1

0000016c e8bd84f0    239 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     240 	.endf	IEDFC_init

                     241 	.align	4

                     242 ;i	r4	local

                     243 

                     244 ;entity	r5	param

                     245 

                     246 	.section ".bss","awb"

                     247 .L446:

                     248 	.section ".rodata","a"

                     249 .L447:;	"ST\000"

00000000 5453       250 	.data.b	83,84

00000002 00         251 	.data.b	0

                     252 .L448:;	"MX\000"

00000003 584d       253 	.data.b	77,88

00000005 00         254 	.data.b	0

                     255 .L449:;	"SP\000"

00000006 5053       256 	.data.b	83,80

00000008 00         257 	.data.b	0

                     258 .L450:;	"SV\000"

00000009 5653       259 	.data.b	83,86

0000000b 00         260 	.data.b	0

                     261 .L451:;	"CF\000"

0000000c 4643       262 	.data.b	67,70

0000000e 00         263 	.data.b	0

                     264 .L452:;	"DC\000"

0000000f 4344       265 	.data.b	68,67

00000011 00         266 	.data.b	0

                     267 .L453:;	"SG\000"

00000012 4753       268 	.data.b	83,71

00000014 00         269 	.data.b	0

                     270 .L454:;	"SE\000"

00000015 4553       271 	.data.b	83,69

00000017 00         272 	.data.b	0

                     273 .L455:;	"SR\000"

00000018 5253       274 	.data.b	83,82

0000001a 00         275 	.data.b	0

                     276 .L456:;	"OR\000"

0000001b 524f       277 	.data.b	79,82

0000001d 00         278 	.data.b	0

                     279 .L457:;	"BL\000"

0000001e 4c42       280 	.data.b	66,76

00000020 00         281 	.data.b	0

                     282 .L458:;	"EX\000"

00000021 5845       283 	.data.b	69,88

00000023 00         284 	.data.b	0

                     285 .L459:;	"CO\000"

00000024 4f43       286 	.data.b	67,79

00000026 00         287 	.data.b	0

                     288 .L460:;	"US\000"

00000027 5355       289 	.data.b	85,83

00000029 00         290 	.data.b	0

                     291 .L461:;	"MS\000"

0000002a 534d       292 	.data.b	77,83

0000002c 00         293 	.data.b	0

                     294 .L462:;	"RP\000"


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bg1.s
0000002d 5052       295 	.data.b	82,80

0000002f 00         296 	.data.b	0

                     297 .L463:;	"BR\000"

00000030 5242       298 	.data.b	66,82

00000032 00         299 	.data.b	0

                     300 .L464:;	"GO\000"

00000033 4f47       301 	.data.b	71,79

00000035 00         302 	.data.b	0

                     303 	.data

                     304 	.text

                     305 

                     306 ;97: }


                     307 	.align	4

                     308 .L522:

00000170 00000000*   309 	.data.w	IEDFCTypeNames

                     310 	.type	.L522,$object

                     311 	.size	.L522,4

                     312 

                     313 	.align	4

                     314 ;.L529	.L447	static

                     315 ;.L530	.L448	static

                     316 ;.L531	.L449	static

                     317 ;.L532	.L450	static

                     318 ;.L533	.L451	static

                     319 ;.L534	.L452	static

                     320 ;.L535	.L453	static

                     321 ;.L536	.L454	static

                     322 ;.L537	.L455	static

                     323 ;.L538	.L456	static

                     324 ;.L539	.L457	static

                     325 ;.L540	.L458	static

                     326 ;.L541	.L459	static

                     327 ;.L542	.L460	static

                     328 ;.L543	.L461	static

                     329 ;.L544	.L462	static

                     330 ;.L545	.L463	static

                     331 ;.L546	.L464	static

                     332 

                     333 	.data

                     334 .L550:

                     335 	.globl	IEDFCTypeNames

00000000 00000000*   336 IEDFCTypeNames:	.data.w	.L447

00000004 00000000*   337 	.data.w	.L448

00000008 00000000*   338 	.data.w	.L449

0000000c 00000000*   339 	.data.w	.L450

00000010 00000000*   340 	.data.w	.L451

00000014 00000000*   341 	.data.w	.L452

00000018 00000000*   342 	.data.w	.L453

0000001c 00000000*   343 	.data.w	.L454

00000020 00000000*   344 	.data.w	.L455

00000024 00000000*   345 	.data.w	.L456

00000028 00000000*   346 	.data.w	.L457

0000002c 00000000*   347 	.data.w	.L458

00000030 00000000*   348 	.data.w	.L459

00000034 00000000*   349 	.data.w	.L460

00000038 00000000*   350 	.data.w	.L461

0000003c 00000000*   351 	.data.w	.L462

00000040 00000000*   352 	.data.w	.L463

00000044 00000000*   353 	.data.w	.L464

                     354 	.type	IEDFCTypeNames,$object

                     355 	.size	IEDFCTypeNames,72


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bg1.s
                     356 	.ghsnote version,6

                     357 	.ghsnote tools,3

                     358 	.ghsnote options,0

                     359 	.text

                     360 	.align	4

                     361 	.data

                     362 	.align	4

                     363 	.text

