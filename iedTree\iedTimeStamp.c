#include "iedTimeStamp.h"

#include "../DataSlice.h"
#include "../mms_data.h"
#include "../BaseAsnTypes.h"

#include <debug.h>

#include <stdint.h>

bool IEDTimeStampDA_calcReadLen(IEDEntity entity, size_t *pLen)
{
    *pLen = 10;
    return true;
}

bool IEDTimeStampDA_encodeRead(IEDEntity entity, BufferView *outBufView)
{
    int dataLen;
    uint8_t *writeBuf;
    uint64_t timeStamp;

    TimeStamp* pTimeStamp = entity->extInfo;
    timeStamp = pTimeStamp->timeStamp;

    //Если значения времени в самом элементе нет, берём напрямую из DataSlice
    if(timeStamp == 0)
    {
        timeStamp= dataSliceGetTimeStamp();
    }

    writeBuf = outBufView->p + outBufView->pos;

    dataLen = MMSData_encodeTimeStamp(
        IEC61850_BER_TIMESTAMP, timeStamp,writeBuf,0);
    if(dataLen <=0)
    {
        ERROR_REPORT("Invalid read length");
        return false;
    }

    if(!BufferView_advance(outBufView, dataLen))
    {
        ERROR_REPORT("Buffer overflow");
        return false;
    }
    return true;
}

MmsDataAccessError IEDTimeStampDA_write(IEDEntity entity, BufferView *inBufView)
{
    uint8_t tag;
    size_t len;
    uint64_t timeStamp = 0;
    // Указатель на старший байт
    uint8_t *pValue = ((uint8_t*)&timeStamp) + 7;
    // Указатель на входные данные
    uint8_t *pInData;
    TimeStamp* pTimeStamp = entity->extInfo;
    size_t i;

    if(!BufferView_decodeTL(inBufView, &tag, &len, NULL))
    {
        return DATA_ACCESS_ERROR_UNKNOWN;
    }
    if(tag != IEC61850_BER_TIMESTAMP || len != 8)
    {
        return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;
    }

    pInData = inBufView->p + inBufView->pos;
    if(!BufferView_advance(inBufView, len))
    {
        return DATA_ACCESS_ERROR_UNKNOWN;
    }

    // Разворачиваем для little endian
    for(i = 0; i < 8; i++)
    {
        *pValue-- = *pInData++;
    }
    pTimeStamp->timeStamp = timeStamp;
    return DATA_ACCESS_ERROR_SUCCESS;
}
