                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c7c1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=control.c -o gh_c7c1.o -list=control.lst C:\Users\<USER>\AppData\Local\Temp\gh_c7c1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_c7c1.s
Source File: control.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile control.c -o

                      11 ;		control.o

                      12 ;Source File:   control.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:53 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "control.h"


                      23 ;2: 


                      24 ;3: #include "bufViewBER.h"


                      25 ;4: #include "BaseAsnTypes.h"


                      26 ;5: #include "iedmodel.h"


                      27 ;6: #include "mms_data.h"


                      28 ;7: #include "pwin_access.h"


                      29 ;8: #include "infoReport.h"


                      30 ;9: #include "mms.h"


                      31 ;10: #include "iedTree/iedEntity.h"


                      32 ;11: #include "iedTree/iedObjects.h"


                      33 ;12: 


                      34 ;13: #include <Clib.h>


                      35 ;14: #include <stdbool.h>


                      36 ;15: #include <stddef.h>


                      37 ;16: 


                      38 ;17: #define MAX_CTRL_OBJ_COUNT 40


                      39 ;18: 


                      40 ;19: static size_t ctrlObjCnt =0;


                      41 ;20: IEDEntity ctrlObjects[MAX_CTRL_OBJ_COUNT];


                      42 ;21: 


                      43 ;22: 


                      44 ;23: // Имя объекта управления (скорее всего pos) при записи


                      45 ;24: // используется для формирования CommandTermination information report


                      46 ;25: uint8_t cmdTermObjNameBuf[MAX_OBJECT_REFERENCE];


                      47 ;26: //Используются из потока отчетов


                      48 ;27: 


                      49 ;28: //Буфера для посылки CommandTermination InformationReport


                      50 ;29: //Используются из потока отчетов



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c7c1.s
                      51 ;30: static uint8_t cmdTermDataBuf[DEFAULT_REPORT_BUFFER_SIZE];


                      52 ;31: static uint8_t cmdTermMmsBuf[DEFAULT_REPORT_BUFFER_SIZE];


                      53 ;32: static uint8_t cmdTermPresentationBuf[DEFAULT_REPORT_BUFFER_SIZE];


                      54 ;33: 


                      55 ;34: bool Control_registerCtrlObj(IEDEntity entity)


                      56 	.text

                      57 	.align	4

                      58 Control_registerCtrlObj::

                      59 ;35: {


                      60 

                      61 ;36:     if(ctrlObjCnt >= MAX_CTRL_OBJ_COUNT)


                      62 

00000000 e59f2310*    63 	ldr	r2,.L52

00000004 e5921000     64 	ldr	r1,[r2]

00000008 e3510028     65 	cmp	r1,40

                      66 ;37:     {


                      67 

                      68 ;38:         return false;


                      69 

0000000c 359f3308*    70 	ldrlo	r3,.L53

00000010 23a00000     71 	movhs	r0,0

                      72 ;39:     }


                      73 ;40:     ctrlObjects[ctrlObjCnt] = entity;


                      74 

00000014 37830101     75 	strlo	r0,[r3,r1 lsl 2]

                      76 ;41:     ctrlObjCnt++;


                      77 

00000018 32810001     78 	addlo	r0,r1,1

0000001c 35820000     79 	strlo	r0,[r2]

                      80 ;42:     return true;


                      81 

00000020 33a00001     82 	movlo	r0,1

00000024 e12fff1e*    83 	ret	

                      84 	.endf	Control_registerCtrlObj

                      85 	.align	4

                      86 

                      87 ;entity	r0	param

                      88 

                      89 	.section ".bss","awb"

                      90 .L38:

                      91 	.data

                      92 .L39:

00000000 00000000     93 ctrlObjCnt:	.data.b	0,0,0,0

                      94 	.type	ctrlObjCnt,$object

                      95 	.size	ctrlObjCnt,4

                      96 	.section ".bss","awb"

00000000 00000000     97 cmdTermDataBuf:	.space	8192

00000004 00000000 
00000008 00000000 
0000000c 00000000 
00000010 00000000 
00000014 00000000 
00000018 00000000 
0000001c 00000000 
00000020 00000000 
00000024 00000000 
00000028 00000000 
0000002c 00000000 
00000030 00000000 
00000034 00000000 
00000038 00000000 

                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c7c1.s
0000003c 00000000 
00000040 00000000 
00000044 00000000 
00000048 00000000 
0000004c 00000000 
00000050 00000000 
00000054 00000000 
00000058 00000000 
0000005c 00000000 
00000060 00000000 
00000064 00000000 
00000068 00000000 
0000006c 00000000 
00000070 00000000 
00000074 00000000 
00000078 00000000 
0000007c 00000000 
00000080 00000000 
00000084 00000000 
00000088 00000000 
0000008c 00000000 
00000090 00000000 
00000094 00000000 
00000098 00000000 
0000009c 00000000 
000000a0 00000000 
000000a4 00000000 
000000a8 00000000 
000000ac 00000000 
000000b0 00000000 
000000b4 00000000 
000000b8 00000000 
000000bc 00000000 
000000c0 00000000 
000000c4 00000000 
000000c8 00000000 
000000cc 00000000 
000000d0 00000000 
000000d4 00000000 
000000d8 00000000 
000000dc 00000000 
000000e0 00000000 
000000e4 00000000 
000000e8 00000000 
000000ec 00000000 
000000f0 00000000 
000000f4 00000000 
000000f8 00000000 
000000fc 00000000 
00002000 00000000     98 cmdTermMmsBuf:	.space	8192

00002004 00000000 
00002008 00000000 
0000200c 00000000 
00002010 00000000 
00002014 00000000 
00002018 00000000 
0000201c 00000000 
00002020 00000000 
00002024 00000000 
00002028 00000000 
0000202c 00000000 

                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c7c1.s
00002030 00000000 
00002034 00000000 
00002038 00000000 
0000203c 00000000 
00002040 00000000 
00002044 00000000 
00002048 00000000 
0000204c 00000000 
00002050 00000000 
00002054 00000000 
00002058 00000000 
0000205c 00000000 
00002060 00000000 
00002064 00000000 
00002068 00000000 
0000206c 00000000 
00002070 00000000 
00002074 00000000 
00002078 00000000 
0000207c 00000000 
00002080 00000000 
00002084 00000000 
00002088 00000000 
0000208c 00000000 
00002090 00000000 
00002094 00000000 
00002098 00000000 
0000209c 00000000 
000020a0 00000000 
000020a4 00000000 
000020a8 00000000 
000020ac 00000000 
000020b0 00000000 
000020b4 00000000 
000020b8 00000000 
000020bc 00000000 
000020c0 00000000 
000020c4 00000000 
000020c8 00000000 
000020cc 00000000 
000020d0 00000000 
000020d4 00000000 
000020d8 00000000 
000020dc 00000000 
000020e0 00000000 
000020e4 00000000 
000020e8 00000000 
000020ec 00000000 
000020f0 00000000 
000020f4 00000000 
000020f8 00000000 
000020fc 00000000 
00004000 00000000     99 cmdTermPresentationBuf:	.space	8192

00004004 00000000 
00004008 00000000 
0000400c 00000000 
00004010 00000000 
00004014 00000000 
00004018 00000000 
0000401c 00000000 
00004020 00000000 

                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c7c1.s
00004024 00000000 
00004028 00000000 
0000402c 00000000 
00004030 00000000 
00004034 00000000 
00004038 00000000 
0000403c 00000000 
00004040 00000000 
00004044 00000000 
00004048 00000000 
0000404c 00000000 
00004050 00000000 
00004054 00000000 
00004058 00000000 
0000405c 00000000 
00004060 00000000 
00004064 00000000 
00004068 00000000 
0000406c 00000000 
00004070 00000000 
00004074 00000000 
00004078 00000000 
0000407c 00000000 
00004080 00000000 
00004084 00000000 
00004088 00000000 
0000408c 00000000 
00004090 00000000 
00004094 00000000 
00004098 00000000 
0000409c 00000000 
000040a0 00000000 
000040a4 00000000 
000040a8 00000000 
000040ac 00000000 
000040b0 00000000 
000040b4 00000000 
000040b8 00000000 
000040bc 00000000 
000040c0 00000000 
000040c4 00000000 
000040c8 00000000 
000040cc 00000000 
000040d0 00000000 
000040d4 00000000 
000040d8 00000000 
000040dc 00000000 
000040e0 00000000 
000040e4 00000000 
000040e8 00000000 
000040ec 00000000 
000040f0 00000000 
000040f4 00000000 
000040f8 00000000 
000040fc 00000000 
                     100 	.data

                     101 	.text

                     102 

                     103 ;43: }


                     104 

                     105 ;44: 



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c7c1.s
                     106 ;45: void Control_processCtrlObjects(void)


                     107 	.align	4

                     108 	.align	4

                     109 Control_processCtrlObjects::

00000028 e92d4070    110 	stmfd	[sp]!,{r4-r6,lr}

0000002c e59f62e4*   111 	ldr	r6,.L52

                     112 ;46: {


                     113 

                     114 ;47:     size_t i;


                     115 ;48:     for(i = 0; i < ctrlObjCnt; i++)


                     116 

00000030 e59f52e4*   117 	ldr	r5,.L53

00000034 e5960000    118 	ldr	r0,[r6]

00000038 e3a04000    119 	mov	r4,0

0000003c e1540000    120 	cmp	r4,r0

00000040 2a000005    121 	bhs	.L54

                     122 .L58:

                     123 ;49:     {


                     124 

                     125 ;50:         IEDControlDA_checkTerminate(ctrlObjects[i]);


                     126 

00000044 e4950004    127 	ldr	r0,[r5],4

00000048 eb000000*   128 	bl	IEDControlDA_checkTerminate

0000004c e5960000    129 	ldr	r0,[r6]

00000050 e2844001    130 	add	r4,r4,1

00000054 e1540000    131 	cmp	r4,r0

00000058 3afffff9    132 	blo	.L58

                     133 .L54:

0000005c e8bd8070    134 	ldmfd	[sp]!,{r4-r6,pc}

                     135 	.endf	Control_processCtrlObjects

                     136 	.align	4

                     137 ;i	r4	local

                     138 

                     139 	.data

                     140 	.text

                     141 

                     142 ;51:     }


                     143 ;52: }


                     144 

                     145 ;53: 


                     146 ;54: void Control_disableWaitingObjects(void)


                     147 	.align	4

                     148 	.align	4

                     149 Control_disableWaitingObjects::

00000060 e92d4070    150 	stmfd	[sp]!,{r4-r6,lr}

00000064 e59f62ac*   151 	ldr	r6,.L52

                     152 ;55: {


                     153 

                     154 ;56:     size_t i;


                     155 ;57:     for(i = 0; i < ctrlObjCnt; i++)


                     156 

00000068 e59f52ac*   157 	ldr	r5,.L53

0000006c e5960000    158 	ldr	r0,[r6]

00000070 e3a04000    159 	mov	r4,0

00000074 e1540000    160 	cmp	r4,r0

00000078 2a000005    161 	bhs	.L95

                     162 .L99:

                     163 ;58:     {


                     164 

                     165 ;59:         IEDControlDA_disconnect(ctrlObjects[i]);


                     166 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c7c1.s
0000007c e4950004    167 	ldr	r0,[r5],4

00000080 eb000000*   168 	bl	IEDControlDA_disconnect

00000084 e5960000    169 	ldr	r0,[r6]

00000088 e2844001    170 	add	r4,r4,1

0000008c e1540000    171 	cmp	r4,r0

00000090 3afffff9    172 	blo	.L99

                     173 .L95:

00000094 e8bd8070    174 	ldmfd	[sp]!,{r4-r6,pc}

                     175 	.endf	Control_disableWaitingObjects

                     176 	.align	4

                     177 ;i	r4	local

                     178 

                     179 	.data

                     180 	.text

                     181 

                     182 ;60:     }


                     183 ;61: }


                     184 

                     185 ;62: 


                     186 ;63: bool Control_sendServiceErrorReport(IsoConnection* isoConn,


                     187 	.align	4

                     188 	.align	4

                     189 Control_sendServiceErrorReport::

00000098 e92d44f0    190 	stmfd	[sp]!,{r4-r7,r10,lr}

                     191 ;64:                                     IEDEntity controlObject, uint8_t addCause)


                     192 ;65: {


                     193 

                     194 ;66:     MmsConnection* mmsConn = &isoConn->mmsConn;


                     195 

0000009c e1a0a002    196 	mov	r10,r2

000000a0 e24dd038    197 	sub	sp,sp,56

000000a4 e1a06001    198 	mov	r6,r1

000000a8 e1a05000    199 	mov	r5,r0

000000ac e3a00f8f    200 	mov	r0,0x023c

000000b0 e2600a49    201 	rsb	r0,r0,73<<12

000000b4 e0854000    202 	add	r4,r5,r0

000000b8 e5960000    203 	ldr	r0,[r6]

000000bc e1a07004    204 	mov	r7,r4

                     205 ;67:     BufferView wrBuf;


                     206 ;68:     StringView cntrlObjName;


                     207 ;69:     BufferView cntrlObjNameBuf;


                     208 ;70:     StringView orIdent;


                     209 ;71:     int32_t orCat;


                     210 ;72: 


                     211 ;73:     //Имя объекта управления


                     212 ;74:     if(controlObject->parent == NULL)


                     213 

000000c0 e3500000    214 	cmp	r0,0

000000c4 0a000025    215 	beq	.L158

                     216 ;75:     {


                     217 

                     218 ;76:         ERROR_REPORT("Invalid parent");


                     219 ;77:         return false;


                     220 

                     221 ;78:     }


                     222 ;79: 


                     223 ;80: 


                     224 ;81:     BufferView_init(&cntrlObjNameBuf, mmsConn->wrCrtlObjNameBuf,


                     225 

000000c8 e2870c61    226 	add	r0,r7,97<<8

000000cc e2801054    227 	add	r1,r0,84


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c7c1.s
000000d0 e28d0018    228 	add	r0,sp,24

000000d4 e3a03000    229 	mov	r3,0

000000d8 e3a02081    230 	mov	r2,129

000000dc eb000000*   231 	bl	BufferView_init

                     232 ;82:                     sizeof(mmsConn->wrCrtlObjNameBuf), 0);


                     233 ;83: 


                     234 ;84:     if(!IEDEntity_getFullName(controlObject->parent, &cntrlObjNameBuf  ))


                     235 

000000e0 e5960000    236 	ldr	r0,[r6]

000000e4 e28d1018    237 	add	r1,sp,24

000000e8 eb000000*   238 	bl	IEDEntity_getFullName

000000ec e3500000    239 	cmp	r0,0

000000f0 0a00001a    240 	beq	.L158

                     241 ;85:     {


                     242 

                     243 ;86:         return false;


                     244 

                     245 ;87:     }


                     246 ;88: 


                     247 ;89:     //Получаем имя объекта из буфера


                     248 ;90:     StringView_init(&cntrlObjName, (const char*)cntrlObjNameBuf.p, cntrlObjNameBuf.pos);


                     249 

000000f4 e59d201c    250 	ldr	r2,[sp,28]

000000f8 e59d1018    251 	ldr	r1,[sp,24]

000000fc e28d0024    252 	add	r0,sp,36

00000100 eb000000*   253 	bl	StringView_init

                     254 ;91: 


                     255 ;92:     BufferView_init(&wrBuf, mmsConn->infoReportDataBuf,


                     256 

00000104 e2871f51    257 	add	r1,r7,0x0144

00000108 e28d002c    258 	add	r0,sp,44

0000010c e3a03000    259 	mov	r3,0

00000110 e3a02d80    260 	mov	r2,1<<13

00000114 eb000000*   261 	bl	BufferView_init

                     262 ;93:                     sizeof(mmsConn->infoReportDataBuf), 0);


                     263 ;94: 


                     264 ;95:     if(!IEDControlDA_getOrIdent(controlObject, &orIdent))


                     265 

00000118 e28d1010    266 	add	r1,sp,16

0000011c e1a00006    267 	mov	r0,r6

00000120 eb000000*   268 	bl	IEDControlDA_getOrIdent

00000124 e3500000    269 	cmp	r0,0

00000128 0a00000c    270 	beq	.L158

                     271 ;96:     {


                     272 

                     273 ;97:         ERROR_REPORT("Unable to get orIdent");


                     274 ;98:         return false;


                     275 

                     276 ;99:     }


                     277 ;100: 


                     278 ;101:     if(!IEDControlDA_getOrCat(controlObject, &orCat))


                     279 

0000012c e28d100c    280 	add	r1,sp,12

00000130 e1a00006    281 	mov	r0,r6

00000134 eb000000*   282 	bl	IEDControlDA_getOrCat

00000138 e3500000    283 	cmp	r0,0

0000013c 0a000007    284 	beq	.L158

                     285 ;102:     {


                     286 

                     287 ;103:         ERROR_REPORT("Unable to get orCat");


                     288 ;104:         return false;



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c7c1.s
                     289 

                     290 ;105:     }


                     291 ;106: 


                     292 ;107:     if(!InfoReport_createLastApplErrorReport( &wrBuf,


                     293 

00000140 e3a02000    294 	mov	r2,0

00000144 e28d0010    295 	add	r0,sp,16

00000148 e88d0405    296 	stmea	[sp],{r0,r2,r10}

0000014c e5dd300c    297 	ldrb	r3,[sp,12]

00000150 e28d1024    298 	add	r1,sp,36

00000154 e28d002c    299 	add	r0,sp,44

00000158 eb000000*   300 	bl	InfoReport_createLastApplErrorReport

0000015c e3500000    301 	cmp	r0,0

                     302 .L158:

                     303 ;108:                                           &cntrlObjName, 0, orCat, &orIdent, 0, addCause))


                     304 ;109:     {


                     305 

                     306 ;110:         return false;


                     307 

00000160 03a00000    308 	moveq	r0,0

00000164 0a000009    309 	beq	.L143

                     310 .L157:

                     311 ;111:     }


                     312 ;112: 


                     313 ;113:     InfoReport_send(isoConn, wrBuf.p, wrBuf.pos,


                     314 

00000168 e2840c41    315 	add	r0,r4,65<<8

0000016c e2800044    316 	add	r0,r0,68

00000170 e58d0000    317 	str	r0,[sp]

00000174 e2840d84    318 	add	r0,r4,33<<8

00000178 e2803044    319 	add	r3,r0,68

0000017c e59d2030    320 	ldr	r2,[sp,48]

00000180 e59d102c    321 	ldr	r1,[sp,44]

00000184 e1a00005    322 	mov	r0,r5

00000188 eb000000*   323 	bl	InfoReport_send

                     324 ;114:                     isoConn->mmsConn.infoReportBuf,


                     325 ;115:                     isoConn->mmsConn.infoReportPresentationBuf);


                     326 ;116:     return true;


                     327 

0000018c e3a00001    328 	mov	r0,1

                     329 .L143:

00000190 e28dd038    330 	add	sp,sp,56

00000194 e8bd84f0    331 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     332 	.endf	Control_sendServiceErrorReport

                     333 	.align	4

                     334 ;mmsConn	r7	local

                     335 ;wrBuf	[sp,44]	local

                     336 ;cntrlObjName	[sp,36]	local

                     337 ;cntrlObjNameBuf	[sp,24]	local

                     338 ;orIdent	[sp,16]	local

                     339 ;orCat	[sp,12]	local

                     340 

                     341 ;isoConn	r5	param

                     342 ;controlObject	r6	param

                     343 ;addCause	r10	param

                     344 

                     345 	.section ".bss","awb"

                     346 .L234:

                     347 	.data

                     348 	.text

                     349 


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c7c1.s
                     350 ;117: }


                     351 

                     352 ;118: 


                     353 ;119: //функция вызывается из потока отчётов, поэтому использует глобальные


                     354 ;120: //буфера


                     355 ;121: bool Control_sendPositiveCmdTermReport(IsoConnection* isoConn,


                     356 	.align	4

                     357 	.align	4

                     358 Control_sendPositiveCmdTermReport::

00000198 e92d4070    359 	stmfd	[sp]!,{r4-r6,lr}

                     360 ;122:                                     IEDEntity controlObject)


                     361 ;123: {


                     362 

                     363 ;124:     StringView cntrlObjItemId;


                     364 ;125:     BufferView cntrlObjItemIdBuf;


                     365 ;126:     StringView* cntrlObjDomainId;


                     366 ;127:     BufferView wrBuf;


                     367 ;128: 


                     368 ;129: 


                     369 ;130:     //Получаем itemId


                     370 ;131:     BufferView_init(&cntrlObjItemIdBuf, cmdTermObjNameBuf,


                     371 

0000019c e59f417c*   372 	ldr	r4,.L340

000001a0 e24dd028    373 	sub	sp,sp,40

000001a4 e1a06000    374 	mov	r6,r0

000001a8 e1a05001    375 	mov	r5,r1

000001ac e59f1170*   376 	ldr	r1,.L341

000001b0 e28d0014    377 	add	r0,sp,20

000001b4 e3a03000    378 	mov	r3,0

000001b8 e3a02081    379 	mov	r2,129

000001bc eb000000*   380 	bl	BufferView_init

                     381 ;132:                     sizeof(cmdTermObjNameBuf), 0);


                     382 ;133: 


                     383 ;134:     if(!IEDEntity_getFullItemId(controlObject, &cntrlObjItemIdBuf ))


                     384 

000001c0 e28d1014    385 	add	r1,sp,20

000001c4 e1a00005    386 	mov	r0,r5

000001c8 eb000000*   387 	bl	IEDEntity_getFullItemId

000001cc e3500000    388 	cmp	r0,0

000001d0 0a000013    389 	beq	.L262

                     390 ;135:     {


                     391 

                     392 ;136:         return false;


                     393 

                     394 ;137:     }


                     395 ;138: 


                     396 ;139:     //Получаем itemId объекта из буфера


                     397 ;140:     StringView_init(&cntrlObjItemId, (const char*)cntrlObjItemIdBuf.p, cntrlObjItemIdBuf.pos);


                     398 

000001d4 e59d2018    399 	ldr	r2,[sp,24]

000001d8 e59d1014    400 	ldr	r1,[sp,20]

000001dc e28d0020    401 	add	r0,sp,32

000001e0 eb000000*   402 	bl	StringView_init

                     403 ;141: 


                     404 ;142:     //Получаем domainId


                     405 ;143:     if(!IEDEntity_getDomainId(controlObject, &cntrlObjDomainId ))


                     406 

000001e4 e28d1004    407 	add	r1,sp,4

000001e8 e1a00005    408 	mov	r0,r5

000001ec eb000000*   409 	bl	IEDEntity_getDomainId

000001f0 e3500000    410 	cmp	r0,0


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c7c1.s
000001f4 0a00000a    411 	beq	.L262

                     412 ;144:     {


                     413 

                     414 ;145:         return false;


                     415 

                     416 ;146:     }


                     417 ;147: 


                     418 ;148:     BufferView_init(&wrBuf, cmdTermDataBuf, sizeof(cmdTermDataBuf), 0);


                     419 

000001f8 e1a01004    420 	mov	r1,r4

000001fc e28d0008    421 	add	r0,sp,8

00000200 e3a03000    422 	mov	r3,0

00000204 e3a02d80    423 	mov	r2,1<<13

00000208 eb000000*   424 	bl	BufferView_init

                     425 ;149: 


                     426 ;150:     if(!InfoReport_createPositiveCmdTermReport(controlObject, &wrBuf, cntrlObjDomainId,


                     427 

0000020c e28d3020    428 	add	r3,sp,32

00000210 e59d2004    429 	ldr	r2,[sp,4]

00000214 e28d1008    430 	add	r1,sp,8

00000218 e1a00005    431 	mov	r0,r5

0000021c eb000000*   432 	bl	InfoReport_createPositiveCmdTermReport

00000220 e3500000    433 	cmp	r0,0

                     434 .L262:

                     435 ;151:                                                &cntrlObjItemId))


                     436 ;152:     {


                     437 

                     438 ;153:         return false;


                     439 

00000224 03a00000    440 	moveq	r0,0

00000228 0a000007    441 	beq	.L253

                     442 .L261:

                     443 ;154:     }


                     444 ;155: 


                     445 ;156:     InfoReport_send(isoConn, wrBuf.p, wrBuf.pos, cmdTermMmsBuf,


                     446 

0000022c e59f00f4*   447 	ldr	r0,.L342

00000230 e59f30f4*   448 	ldr	r3,.L343

00000234 e58d0000    449 	str	r0,[sp]

00000238 e59d200c    450 	ldr	r2,[sp,12]

0000023c e59d1008    451 	ldr	r1,[sp,8]

00000240 e1a00006    452 	mov	r0,r6

00000244 eb000000*   453 	bl	InfoReport_send

                     454 ;157:                     cmdTermPresentationBuf);


                     455 ;158:     return true;


                     456 

00000248 e3a00001    457 	mov	r0,1

                     458 .L253:

0000024c e28dd028    459 	add	sp,sp,40

00000250 e8bd8070    460 	ldmfd	[sp]!,{r4-r6,pc}

                     461 	.endf	Control_sendPositiveCmdTermReport

                     462 	.align	4

                     463 ;cntrlObjItemId	[sp,32]	local

                     464 ;cntrlObjItemIdBuf	[sp,20]	local

                     465 ;cntrlObjDomainId	[sp,4]	local

                     466 ;wrBuf	[sp,8]	local

                     467 

                     468 ;isoConn	r6	param

                     469 ;controlObject	r5	param

                     470 

                     471 	.data


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c7c1.s
                     472 	.text

                     473 

                     474 ;159: }


                     475 

                     476 ;160: 


                     477 ;161: //функция вызывается из потока отчётов, поэтому использует глобальные


                     478 ;162: //буфера


                     479 ;163: bool Control_sendNegativeCmdTermReport(IsoConnection* isoConn,


                     480 	.align	4

                     481 	.align	4

                     482 Control_sendNegativeCmdTermReport::

00000254 e92d40f0    483 	stmfd	[sp]!,{r4-r7,lr}

                     484 ;164:                                     IEDEntity controlObject, uint8_t addCause)


                     485 ;165: {


                     486 

                     487 ;166:     StringView cntrlObjItemId;


                     488 ;167:     BufferView cntrlObjItemIdBuf;


                     489 ;168:     StringView* cntrlObjDomainId;


                     490 ;169:     BufferView wrBuf;


                     491 ;170: 


                     492 ;171:     //Получаем itemId


                     493 ;172:     BufferView_init(&cntrlObjItemIdBuf, cmdTermObjNameBuf,


                     494 

00000258 e59f40c0*   495 	ldr	r4,.L340

0000025c e24dd028    496 	sub	sp,sp,40

00000260 e1a06000    497 	mov	r6,r0

00000264 e1a05001    498 	mov	r5,r1

00000268 e59f10b4*   499 	ldr	r1,.L341

0000026c e28d0014    500 	add	r0,sp,20

00000270 e3a03000    501 	mov	r3,0

00000274 e1a07002    502 	mov	r7,r2

00000278 e3a02081    503 	mov	r2,129

0000027c eb000000*   504 	bl	BufferView_init

                     505 ;173:                     sizeof(cmdTermObjNameBuf), 0);


                     506 ;174: 


                     507 ;175:     if(!IEDEntity_getFullItemId(controlObject, &cntrlObjItemIdBuf ))


                     508 

00000280 e28d1014    509 	add	r1,sp,20

00000284 e1a00005    510 	mov	r0,r5

00000288 eb000000*   511 	bl	IEDEntity_getFullItemId

0000028c e3500000    512 	cmp	r0,0

00000290 0a000014    513 	beq	.L353

                     514 ;176:     {


                     515 

                     516 ;177:         return false;


                     517 

                     518 ;178:     }


                     519 ;179: 


                     520 ;180:     //Получаем itemId объекта из буфера


                     521 ;181:     StringView_init(&cntrlObjItemId, (const char*)cntrlObjItemIdBuf.p, cntrlObjItemIdBuf.pos);


                     522 

00000294 e59d2018    523 	ldr	r2,[sp,24]

00000298 e59d1014    524 	ldr	r1,[sp,20]

0000029c e28d0020    525 	add	r0,sp,32

000002a0 eb000000*   526 	bl	StringView_init

                     527 ;182: 


                     528 ;183:     //Получаем domainId


                     529 ;184:     if(!IEDEntity_getDomainId(controlObject, &cntrlObjDomainId ))


                     530 

000002a4 e28d1004    531 	add	r1,sp,4

000002a8 e1a00005    532 	mov	r0,r5


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c7c1.s
000002ac eb000000*   533 	bl	IEDEntity_getDomainId

000002b0 e3500000    534 	cmp	r0,0

000002b4 0a00000b    535 	beq	.L353

                     536 ;185:     {


                     537 

                     538 ;186:         return false;


                     539 

                     540 ;187:     }


                     541 ;188: 


                     542 ;189:     BufferView_init(&wrBuf, cmdTermDataBuf, sizeof(cmdTermDataBuf), 0);


                     543 

000002b8 e1a01004    544 	mov	r1,r4

000002bc e28d0008    545 	add	r0,sp,8

000002c0 e3a03000    546 	mov	r3,0

000002c4 e3a02d80    547 	mov	r2,1<<13

000002c8 eb000000*   548 	bl	BufferView_init

                     549 ;190: 


                     550 ;191:     if(!InfoReport_createNegativeCmdTermReport(controlObject, &wrBuf,


                     551 

000002cc e28d3020    552 	add	r3,sp,32

000002d0 e58d7000    553 	str	r7,[sp]

000002d4 e59d2004    554 	ldr	r2,[sp,4]

000002d8 e28d1008    555 	add	r1,sp,8

000002dc e1a00005    556 	mov	r0,r5

000002e0 eb000000*   557 	bl	InfoReport_createNegativeCmdTermReport

000002e4 e3500000    558 	cmp	r0,0

                     559 .L353:

                     560 ;192:                                                cntrlObjDomainId, &cntrlObjItemId, addCause))


                     561 ;193:     {


                     562 

                     563 ;194:         return false;


                     564 

000002e8 03a00000    565 	moveq	r0,0

000002ec 0a000007    566 	beq	.L344

                     567 .L352:

                     568 ;195:     }


                     569 ;196:     InfoReport_send(isoConn, wrBuf.p, wrBuf.pos, cmdTermMmsBuf,


                     570 

000002f0 e59f0030*   571 	ldr	r0,.L342

000002f4 e59f3030*   572 	ldr	r3,.L343

000002f8 e58d0000    573 	str	r0,[sp]

000002fc e59d200c    574 	ldr	r2,[sp,12]

00000300 e59d1008    575 	ldr	r1,[sp,8]

00000304 e1a00006    576 	mov	r0,r6

00000308 eb000000*   577 	bl	InfoReport_send

                     578 ;197:                     cmdTermPresentationBuf);


                     579 ;198:     return true;


                     580 

0000030c e3a00001    581 	mov	r0,1

                     582 .L344:

00000310 e28dd028    583 	add	sp,sp,40

00000314 e8bd80f0    584 	ldmfd	[sp]!,{r4-r7,pc}

                     585 	.endf	Control_sendNegativeCmdTermReport

                     586 	.align	4

                     587 ;cntrlObjItemId	[sp,32]	local

                     588 ;cntrlObjItemIdBuf	[sp,20]	local

                     589 ;cntrlObjDomainId	[sp,4]	local

                     590 ;wrBuf	[sp,8]	local

                     591 

                     592 ;isoConn	r6	param

                     593 ;controlObject	r5	param


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c7c1.s
                     594 ;addCause	r7	param

                     595 

                     596 	.data

                     597 	.text

                     598 

                     599 ;199: }


                     600 	.align	4

                     601 .L52:

00000318 00000000*   602 	.data.w	.L39

                     603 	.type	.L52,$object

                     604 	.size	.L52,4

                     605 

                     606 .L53:

0000031c 00000000*   607 	.data.w	ctrlObjects

                     608 	.type	.L53,$object

                     609 	.size	.L53,4

                     610 

                     611 .L340:

00000320 00000000*   612 	.data.w	cmdTermDataBuf

                     613 	.type	.L340,$object

                     614 	.size	.L340,4

                     615 

                     616 .L341:

00000324 00000000*   617 	.data.w	cmdTermObjNameBuf

                     618 	.type	.L341,$object

                     619 	.size	.L341,4

                     620 

                     621 .L342:

00000328 00000000*   622 	.data.w	cmdTermPresentationBuf

                     623 	.type	.L342,$object

                     624 	.size	.L342,4

                     625 

                     626 .L343:

0000032c 00000000*   627 	.data.w	cmdTermMmsBuf

                     628 	.type	.L343,$object

                     629 	.size	.L343,4

                     630 

                     631 	.align	4

                     632 ;ctrlObjCnt	.L39	static

                     633 ;cmdTermDataBuf	cmdTermDataBuf	static

                     634 ;cmdTermMmsBuf	cmdTermMmsBuf	static

                     635 ;cmdTermPresentationBuf	cmdTermPresentationBuf	static

                     636 

                     637 	.data

                     638 	.comm	ctrlObjects,160,4

                     639 	.type	ctrlObjects,$object

                     640 	.size	ctrlObjects,160

                     641 	.comm	cmdTermObjNameBuf,132,4

                     642 	.type	cmdTermObjNameBuf,$object

                     643 	.size	cmdTermObjNameBuf,132

                     644 	.ghsnote version,6

                     645 	.ghsnote tools,3

                     646 	.ghsnote options,0

                     647 	.text

                     648 	.align	4

                     649 	.data

                     650 	.align	4

                     651 	.section ".bss","awb"

                     652 	.align	4

                     653 	.text

