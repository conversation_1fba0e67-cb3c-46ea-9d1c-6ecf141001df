                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_53c1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedBool.c -o iedTree\gh_53c1.o -list=iedTree/iedBool.lst C:\Users\<USER>\AppData\Local\Temp\gh_53c1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_53c1.s
Source File: iedBool.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		iedTree/iedBool.c -o iedTree/iedBool.o

                      12 ;Source File:   iedTree/iedBool.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:50 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "iedBool.h"


                      23 ;2: 


                      24 ;3: #include "iedFinalDA.h"


                      25 ;4: #include "iedTree.h"


                      26 ;5: #include "../AsnEncoding.h"


                      27 ;6: #include "../DataSlice.h"


                      28 ;7: #include "debug.h"


                      29 ;8: 


                      30 ;9: #include "IEDCompile/AccessInfo.h"


                      31 ;10: 


                      32 ;11: #define BOOL_ENCODED_SIZE 3


                      33 ;12: 


                      34 ;13: static bool calcReadLen(IEDEntity entity, size_t* pLen )


                      35 	.text

                      36 	.align	4

                      37 calcReadLen:

                      38 ;14: {


                      39 

                      40 ;15: 	*pLen = BOOL_ENCODED_SIZE;


                      41 

00000000 e3a00003     42 	mov	r0,3

00000004 e5810000     43 	str	r0,[r1]

                      44 ;16: 	return true;


                      45 

00000008 e3a00001     46 	mov	r0,1

0000000c e12fff1e*    47 	ret	

                      48 	.endf	calcReadLen

                      49 	.align	4

                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_53c1.s
                      51 ;entity	none	param

                      52 ;pLen	r1	param

                      53 

                      54 	.section ".bss","awb"

                      55 .L30:

                      56 	.data

                      57 	.text

                      58 

                      59 ;17: }


                      60 

                      61 ;18: 


                      62 ;19: static bool encodeRead(IEDEntity entity, BufferView* outBuf)


                      63 	.align	4

                      64 	.align	4

                      65 encodeRead:

00000010 e92d4000     66 	stmfd	[sp]!,{lr}

                      67 ;20: {			


                      68 

                      69 ;21: 	bool value = entity->boolValue;


                      70 

00000014 e5d02030     71 	ldrb	r2,[r0,48]

00000018 e1a00001     72 	mov	r0,r1

0000001c e3a01083     73 	mov	r1,131

00000020 eb000000*    74 	bl	BufferView_encodeBoolean

00000024 e3500000     75 	cmp	r0,0

00000028 13a00001     76 	movne	r0,1

                      77 ;22: 


                      78 ;23: 	if(!BufferView_encodeBoolean(outBuf, IEC61850_BER_BOOLEAN, value))


                      79 

                      80 ;24: 	{


                      81 

                      82 ;25: 		ERROR_REPORT("Error encoding bool");


                      83 ;26: 		return false;


                      84 

                      85 ;27: 	}


                      86 ;28: 	return true;


                      87 

0000002c e8bd4000     88 	ldmfd	[sp]!,{lr}

00000030 e12fff1e*    89 	ret	

                      90 	.endf	encodeRead

                      91 	.align	4

                      92 ;value	r2	local

                      93 

                      94 ;entity	r0	param

                      95 ;outBuf	r1	param

                      96 

                      97 	.section ".bss","awb"

                      98 .L72:

                      99 	.data

                     100 	.text

                     101 

                     102 ;29: }


                     103 

                     104 ;30: 


                     105 ;31: static void updateFromDataSlice(IEDEntity entity)


                     106 	.align	4

                     107 	.align	4

                     108 updateFromDataSlice:

00000034 e92d4030    109 	stmfd	[sp]!,{r4-r5,lr}

00000038 e1a05000    110 	mov	r5,r0

                     111 ;32: {



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_53c1.s
                     112 

                     113 ;33: 	int offset  = entity->dataSliceOffset;


                     114 

0000003c e595002c    115 	ldr	r0,[r5,44]

                     116 ;34: 	bool value = offset != -1 && DataSlice_getBoolFastCurrDS(offset);


                     117 

00000040 e3a04000    118 	mov	r4,0

00000044 e3700001    119 	cmn	r0,1

00000048 0a000004    120 	beq	.L82

0000004c e1a00800    121 	mov	r0,r0 lsl 16

00000050 e1a00820    122 	mov	r0,r0 lsr 16

00000054 eb000000*   123 	bl	DataSlice_getBoolFastCurrDS

00000058 e3500000    124 	cmp	r0,0

0000005c 13a04001    125 	movne	r4,1

                     126 .L82:

00000060 e5d50030    127 	ldrb	r0,[r5,48]

00000064 e20410ff    128 	and	r1,r4,255

                     129 ;35: 


                     130 ;36: 	if(entity->boolValue == value)


                     131 

00000068 e1500001    132 	cmp	r0,r1

                     133 ;37: 	{


                     134 

                     135 ;38: 		entity->changed = TRGOP_NONE;


                     136 

0000006c 03a00000    137 	moveq	r0,0

00000070 05850028    138 	streq	r0,[r5,40]

00000074 0a000007    139 	beq	.L79

                     140 ;39: 	}


                     141 ;40: 	else


                     142 ;41: 	{


                     143 

                     144 ;42: 		entity->changed = entity->trgOps;


                     145 

00000078 e5950024    146 	ldr	r0,[r5,36]

0000007c e5c51030    147 	strb	r1,[r5,48]

                     148 ;44: 		IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());


                     149 

00000080 e5850028    150 	str	r0,[r5,40]

                     151 ;43: 		entity->boolValue = value;


                     152 

00000084 eb000000*   153 	bl	dataSliceGetTimeStamp

00000088 e1a02001    154 	mov	r2,r1

0000008c e1a01000    155 	mov	r1,r0

00000090 e1a00005    156 	mov	r0,r5

00000094 eb000000*   157 	bl	IEDEntity_setTimeStamp

                     158 .L79:

00000098 e8bd4030    159 	ldmfd	[sp]!,{r4-r5,lr}

0000009c e12fff1e*   160 	ret	

                     161 	.endf	updateFromDataSlice

                     162 	.align	4

                     163 ;offset	r0	local

                     164 ;value	r1	local

                     165 

                     166 ;entity	r5	param

                     167 

                     168 	.section ".bss","awb"

                     169 .L150:

                     170 	.data

                     171 	.text

                     172 


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_53c1.s
                     173 ;45: 	}


                     174 ;46: }


                     175 

                     176 ;47: 


                     177 ;48: void IEDBool_init(IEDEntity entity)


                     178 	.align	4

                     179 	.align	4

                     180 IEDBool_init::

000000a0 e92d4070    181 	stmfd	[sp]!,{r4-r6,lr}

000000a4 e280405c    182 	add	r4,r0,92

000000a8 e5140004    183 	ldr	r0,[r4,-4]

                     184 ;51: 	IntBoolAccessInfo* accessInfo = extInfo->accessInfo;


                     185 

000000ac e5900000    186 	ldr	r0,[r0]

                     187 ;52: 


                     188 ;53: 	//Если будет ошибка, то запишется -1;


                     189 ;54: 	entity->dataSliceOffset = DataSlice_getBoolOffset(accessInfo->valueOffset);


                     190 

000000b0 e59f5028*   191 	ldr	r5,.L197

000000b4 e5900004    192 	ldr	r0,[r0,4]

000000b8 e59f6024*   193 	ldr	r6,.L198

                     194 ;49: {


                     195 

                     196 ;50: 	TerminalItem* extInfo = entity->extInfo;


                     197 

000000bc eb000000*   198 	bl	DataSlice_getBoolOffset

000000c0 e5040030    199 	str	r0,[r4,-48]

                     200 ;55: 


                     201 ;56: 	entity->calcReadLen = calcReadLen;


                     202 

                     203 ;57: 	entity->encodeRead = encodeRead;


                     204 

000000c4 e1a00006    205 	mov	r0,r6

000000c8 e8840021    206 	stmea	[r4],{r0,r5}

                     207 ;58: 	entity->updateFromDataSlice = updateFromDataSlice;


                     208 

000000cc e59f0014*   209 	ldr	r0,.L199

000000d0 e584000c    210 	str	r0,[r4,12]

                     211 ;59: 


                     212 ;60: 	IEDTree_addToCmpList(entity);


                     213 

000000d4 e244005c    214 	sub	r0,r4,92

000000d8 e8bd4070    215 	ldmfd	[sp]!,{r4-r6,lr}

000000dc ea000000*   216 	b	IEDTree_addToCmpList

                     217 	.endf	IEDBool_init

                     218 	.align	4

                     219 ;extInfo	r0	local

                     220 ;accessInfo	r0	local

                     221 

                     222 ;entity	r4	param

                     223 

                     224 	.section ".bss","awb"

                     225 .L190:

                     226 	.data

                     227 	.text

                     228 

                     229 ;61: }


                     230 	.align	4

                     231 .L197:

000000e0 00000000*   232 	.data.w	calcReadLen

                     233 	.type	.L197,$object


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_53c1.s
                     234 	.size	.L197,4

                     235 

                     236 .L198:

000000e4 00000000*   237 	.data.w	encodeRead

                     238 	.type	.L198,$object

                     239 	.size	.L198,4

                     240 

                     241 .L199:

000000e8 00000000*   242 	.data.w	updateFromDataSlice

                     243 	.type	.L199,$object

                     244 	.size	.L199,4

                     245 

                     246 	.align	4

                     247 

                     248 	.data

                     249 	.ghsnote version,6

                     250 	.ghsnote tools,3

                     251 	.ghsnote options,0

                     252 	.text

                     253 	.align	4

