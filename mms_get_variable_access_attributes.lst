                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7k01.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_get_variable_access_attributes.c -o gh_7k01.o -list=mms_get_variable_access_attributes.lst C:\Users\<USER>\AppData\Local\Temp\gh_7k01.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_7k01.s
Source File: mms_get_variable_access_attributes.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		mms_get_variable_access_attributes.c -o

                      12 ;		mms_get_variable_access_attributes.o

                      13 ;Source File:   mms_get_variable_access_attributes.c

                      14 ;Directory:     

                      15 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      16 ;Compile Date:  Mon Jul 28 12:31:03 2025

                      17 ;Host OS:       Win32

                      18 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      19 ;Release:       MULTI v4.2.3

                      20 ;Revision Date: Wed Mar 29 05:25:47 2006

                      21 ;Release Date:  Fri Mar 31 10:02:14 2006

                      22 

                      23 ;1: #include "mms_get_variable_access_attributes.h"


                      24 ;2: #include "AsnEncoding.h"  


                      25 ;3: #include "MmsConst.h"


                      26 ;4: #include "mmsservices.h"


                      27 ;5: #include "iedmodel.h"


                      28 ;6: #include "mms_error.h"


                      29 ;7: #include <debug.h>


                      30 ;8: 


                      31 ;9: #include <stddef.h>


                      32 ;10: #include <string.h>


                      33 ;11: 


                      34 ;12: static int encodeGetVariableAccessAttrResponse(unsigned int invokeId, int objectPos,


                      35 

                      36 ;57: }


                      37 

                      38 ;58: 


                      39 ;59: static int getVariableAccessAttr(unsigned int invokeId,


                      40 

                      41 ;81: }


                      42 

                      43 ;82: 


                      44 ;83: int mms_handleGetVariableAccessAttr(MmsConnection* mmsConn,


                      45 	.text

                      46 	.align	4

                      47 mms_handleGetVariableAccessAttr::

00000000 e92d4df0     48 	stmfd	[sp]!,{r4-r8,r10-fp,lr}

                      49 ;84:                                  unsigned char* inBuf, int bufPos, int maxBufPos,


                      50 ;85:                                   unsigned int invokeId, unsigned char* response)



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7k01.s
                      51 ;86: {


                      52 

00000004 e1a05001     53 	mov	r5,r1

00000008 e24dd024     54 	sub	sp,sp,36

0000000c e59d4048     55 	ldr	r4,[sp,72]

00000010 e3a00000     56 	mov	r0,0

00000014 e58d000c     57 	str	r0,[sp,12]

00000018 e58d0010     58 	str	r0,[sp,16]

                      59 ;87:     /*


                      60 ;88:     return mms_createMmsRejectPdu(invokeId,


                      61 ;89:                            MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, response);


                      62 ;90:                            */


                      63 ;91: 


                      64 ;92:     uint8_t* domainIdStr = NULL;


                      65 

                      66 ;93:     uint8_t* itemIdStr = NULL;


                      67 

                      68 ;94:     int domainIdLen;


                      69 ;95:     int itemIdLen;


                      70 ;96:     uint8_t tag;


                      71 ;97:     int length;


                      72 ;98: 	int result;


                      73 ;99: 


                      74 ;100:     while (bufPos < maxBufPos)


                      75 

0000001c e28da014     76 	add	r10,sp,20

00000020 e1a0c002     77 	mov	r12,r2

00000024 e1a06003     78 	mov	r6,r3

00000028 e15c0006     79 	cmp	r12,r6

0000002c aa00001e     80 	bge	.L60

                      81 .L61:

                      82 ;101:     {


                      83 

                      84 ;102:         tag = inBuf[bufPos++];


                      85 

00000030 e7d5700c     86 	ldrb	r7,[r5,r12]

00000034 e28c2001     87 	add	r2,r12,1

                      88 ;103: 


                      89 ;104:         bufPos = BerDecoder_decodeLength(inBuf, &length, bufPos, maxBufPos);


                      90 

00000038 e1a03006     91 	mov	r3,r6

0000003c e28d101c     92 	add	r1,sp,28

00000040 e1a00005     93 	mov	r0,r5

00000044 eb000000*    94 	bl	BerDecoder_decodeLength

00000048 e1b0c000     95 	movs	r12,r0

                      96 ;105: 


                      97 ;106:         if (bufPos < 0)


                      98 

                      99 ;107:         {


                     100 

                     101 ;108:             return 0;


                     102 

0000004c 43a00000    103 	movmi	r0,0

00000050 4a00005a    104 	bmi	.L57

                     105 ;109:         }


                     106 ;110: 


                     107 ;111:         if (ASN_VARIABLE_SPECIFICATION_OBJECT_NAME == tag)


                     108 

00000054 e35700a0    109 	cmp	r7,160

00000058 1a00000d    110 	bne	.L65

                     111 ;112:         {



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7k01.s
                     112 

                     113 ;113:             bufPos = BerDecoder_DecodeObjectName(inBuf, bufPos, maxBufPos,


                     114 

0000005c e28d100c    115 	add	r1,sp,12

00000060 e28d0018    116 	add	r0,sp,24

00000064 e88d0403    117 	stmea	[sp],{r0-r1,r10}

00000068 e28d3010    118 	add	r3,sp,16

0000006c e1a02006    119 	mov	r2,r6

00000070 e1a0100c    120 	mov	r1,r12

00000074 e1a00005    121 	mov	r0,r5

00000078 eb000000*   122 	bl	BerDecoder_DecodeObjectName

0000007c e1b0c000    123 	movs	r12,r0

                     124 ;114:                 &itemIdStr, &itemIdLen, &domainIdStr, &domainIdLen);


                     125 ;115:             if (bufPos < 0)


                     126 

00000080 5a000007    127 	bpl	.L59

                     128 ;116:             {


                     129 

                     130 ;117:                 debugSendText("!!!!!Object name error");


                     131 

00000084 e28f0000*   132 	adr	r0,.L261

00000088 eb000000*   133 	bl	debugSendText

                     134 ;118:                 return -1;


                     135 

0000008c e3e00000    136 	mvn	r0,0

00000090 ea00004a    137 	b	.L57

                     138 .L65:

                     139 ;119:             }            


                     140 ;120:         }


                     141 ;121:         else {


                     142 

                     143 ;122:             debugSendText("!!!!!Unknown tag");


                     144 

00000094 e28f0000*   145 	adr	r0,.L262

00000098 eb000000*   146 	bl	debugSendText

                     147 ;123:             return -1;


                     148 

0000009c e3e00000    149 	mvn	r0,0

000000a0 ea000046    150 	b	.L57

                     151 .L59:

000000a4 e15c0006    152 	cmp	r12,r6

000000a8 baffffe0    153 	blt	.L61

                     154 .L60:

                     155 ;124:         }


                     156 ;125:     }		


                     157 ;126: 


                     158 ;127: 	result = getVariableAccessAttr(invokeId,


                     159 

000000ac e59db044    160 	ldr	fp,[sp,68]

                     161 ;60:                              uint8_t* domainId, int domainIdLen,


                     162 ;61:                              uint8_t* itemId, int itemIdLen,


                     163 ;62:                              unsigned char* outBuf)


                     164 ;63: {


                     165 

                     166 ;64:     //TODO выделить функцию получения объекта по именам


                     167 ;65:     int ldPos;


                     168 ;66:     int objectPos;


                     169 ;67: 


                     170 ;68:     ldPos = findDomainSection(IED_VMD_DATA_SECTION, domainId,domainIdLen);


                     171 

000000b0 e59d2014    172 	ldr	r2,[sp,20]


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7k01.s
000000b4 e59d100c    173 	ldr	r1,[sp,12]

000000b8 e3a000ec    174 	mov	r0,236

000000bc eb000000*   175 	bl	findDomainSection

000000c0 e1a05000    176 	mov	r5,r0

                     177 ;69:     debugSendUshort("ldPos:", ldPos);


                     178 

000000c4 e1a01805    179 	mov	r1,r5 lsl 16

000000c8 e59f0124*   180 	ldr	r0,.L263

000000cc e1a01821    181 	mov	r1,r1 lsr 16

000000d0 eb000000*   182 	bl	debugSendUshort

                     183 ;70:     if(ldPos == 0)


                     184 

000000d4 e3550000    185 	cmp	r5,0

000000d8 0a000009    186 	beq	.L77

                     187 ;71:     {


                     188 

                     189 ;72:         return 0;


                     190 

                     191 ;73:     }


                     192 ;74:     objectPos = findObjectByPath(ldPos,itemId,itemIdLen);


                     193 

000000dc e59d2018    194 	ldr	r2,[sp,24]

000000e0 e59d1010    195 	ldr	r1,[sp,16]

000000e4 e1a00005    196 	mov	r0,r5

000000e8 eb000000*   197 	bl	findObjectByPath

000000ec e1a05000    198 	mov	r5,r0

                     199 ;75:     debugSendUshort("objectPos:", objectPos);


                     200 

000000f0 e1a01805    201 	mov	r1,r5 lsl 16

000000f4 e59f00fc*   202 	ldr	r0,.L264

000000f8 e1a01821    203 	mov	r1,r1 lsr 16

000000fc eb000000*   204 	bl	debugSendUshort

                     205 ;76:     if(objectPos == 0)


                     206 

00000100 e3550000    207 	cmp	r5,0

                     208 .L77:

                     209 ;77:     {


                     210 

                     211 ;78:         return 0;


                     212 

00000104 03a00000    213 	moveq	r0,0

                     214 ;128: 		domainIdStr, domainIdLen, itemIdStr, itemIdLen,


                     215 ;129: 		response);


                     216 ;130: 	if (result < 1)


                     217 

00000108 0a000028    218 	beq	.L81

                     219 .L78:

                     220 ;79:     }


                     221 ;80:     return encodeGetVariableAccessAttrResponse(invokeId, objectPos, outBuf);


                     222 

                     223 ;13:                               unsigned char* outBuf)


                     224 ;14: {


                     225 

                     226 ;15:     int bufPos = 0;


                     227 

                     228 ;16:     unsigned int accessResultSize;


                     229 ;17:     unsigned int invokeIdSize;


                     230 ;18:     unsigned int confirmedResponseContentSize;


                     231 ;19:     unsigned int fullConfirmedResponseSize;


                     232 ;20: 


                     233 ;21:     //==============determine BER encoded message sizes==============



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7k01.s
                     234 ;22:     //Общий размер всех значений


                     235 ;23:     accessResultSize = encodeObjectAccessAttrs(NULL, bufPos, objectPos, TRUE, TRUE);


                     236 

0000010c e3a06001    237 	mov	r6,1

00000110 e58d6000    238 	str	r6,[sp]

00000114 e1a02005    239 	mov	r2,r5

00000118 e1a03006    240 	mov	r3,r6

0000011c e3a01000    241 	mov	r1,0

00000120 e1a00001    242 	mov	r0,r1

00000124 eb000000*   243 	bl	encodeObjectAccessAttrs

                     244 ;24: 


                     245 ;25:     //Вместе с тэгом 0xA6 (тэг + размер размера данных + размер данных)


                     246 ;26:     confirmedResponseContentSize = 1


                     247 

00000128 e1a07000    248 	mov	r7,r0

0000012c eb000000*   249 	bl	BerEncoder_determineLengthSize

00000130 e0800007    250 	add	r0,r0,r7

00000134 e2808001    251 	add	r8,r0,1

                     252 ;27:             + BerEncoder_determineLengthSize(accessResultSize)


                     253 ;28:             + accessResultSize;


                     254 ;29: 


                     255 ;30:     invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;


                     256 

00000138 e1a0000b    257 	mov	r0,fp

0000013c eb000000*   258 	bl	BerEncoder_UInt32determineEncodedSize

00000140 e280a002    259 	add	r10,r0,2

                     260 ;31: 


                     261 ;32: 


                     262 ;33:     //Для тэга 0xA1


                     263 ;34:     fullConfirmedResponseSize = invokeIdSize


                     264 

00000144 e088100a    265 	add	r1,r8,r10

                     266 ;35:             + confirmedResponseContentSize;


                     267 ;36: 


                     268 ;37: 


                     269 ;38:     //================ encode message ============================


                     270 ;39:     bufPos = 0;


                     271 

                     272 ;40: 


                     273 ;41:     // confirmed response PDU


                     274 ;42:     bufPos = BerEncoder_encodeTL(0xa1,  fullConfirmedResponseSize, outBuf, bufPos);


                     275 

00000148 e1a02004    276 	mov	r2,r4

0000014c e3a03000    277 	mov	r3,0

00000150 e3a000a1    278 	mov	r0,161

00000154 eb000000*   279 	bl	BerEncoder_encodeTL

                     280 ;43: 


                     281 ;44:     // invoke id


                     282 ;45:     bufPos = BerEncoder_encodeTL(ASN_INTEGER, invokeIdSize - 2, outBuf, bufPos);


                     283 

00000158 e1a02004    284 	mov	r2,r4

0000015c e24a1002    285 	sub	r1,r10,2

00000160 e1a03000    286 	mov	r3,r0

00000164 e3a00002    287 	mov	r0,2

00000168 eb000000*   288 	bl	BerEncoder_encodeTL

                     289 ;46:     bufPos = BerEncoder_encodeUInt32(invokeId, outBuf, bufPos);


                     290 

0000016c e1a01004    291 	mov	r1,r4

00000170 e1a02000    292 	mov	r2,r0

00000174 e1a0000b    293 	mov	r0,fp

00000178 eb000000*   294 	bl	BerEncoder_encodeUInt32


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7k01.s
                     295 ;47: 


                     296 ;48:     // confirmed-service-response getVariableAccessAttributes


                     297 ;49:     bufPos = BerEncoder_encodeTL(0xa6, accessResultSize,


                     298 

0000017c e1a02004    299 	mov	r2,r4

00000180 e1a01007    300 	mov	r1,r7

00000184 e1a03000    301 	mov	r3,r0

00000188 e3a000a6    302 	mov	r0,166

0000018c eb000000*   303 	bl	BerEncoder_encodeTL

                     304 ;50:                                  outBuf, bufPos);


                     305 ;51: 


                     306 ;52:     // encode access results


                     307 ;53:     bufPos = encodeObjectAccessAttrs(outBuf, bufPos, objectPos, FALSE, TRUE);


                     308 

00000190 e58d6000    309 	str	r6,[sp]

00000194 e1a02005    310 	mov	r2,r5

00000198 e1a01000    311 	mov	r1,r0

0000019c e1a00004    312 	mov	r0,r4

000001a0 e3a03000    313 	mov	r3,0

000001a4 eb000000*   314 	bl	encodeObjectAccessAttrs

                     315 ;54: 


                     316 ;55: 


                     317 ;56:     return bufPos;


                     318 

                     319 ;128: 		domainIdStr, domainIdLen, itemIdStr, itemIdLen,


                     320 ;129: 		response);


                     321 ;130: 	if (result < 1)


                     322 

000001a8 e3500000    323 	cmp	r0,0

000001ac ca000003    324 	bgt	.L57

                     325 .L81:

                     326 ;131: 	{


                     327 

                     328 ;132: 		return CreateMmsConfirmedErrorPdu(invokeId, response,


                     329 

000001b0 e1a01004    330 	mov	r1,r4

000001b4 e59d0044    331 	ldr	r0,[sp,68]

000001b8 e3a02051    332 	mov	r2,81

000001bc eb000000*   333 	bl	CreateMmsConfirmedErrorPdu

                     334 ;133: 			MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);


                     335 ;134: 	}


                     336 ;135: 


                     337 ;136: 	//TODO Обработать случай когда domainIdStr и itemIdStr неинициализированы


                     338 ;137: 	return result;


                     339 

                     340 .L57:

000001c0 e28dd024    341 	add	sp,sp,36

000001c4 e8bd8df0    342 	ldmfd	[sp]!,{r4-r8,r10-fp,pc}

                     343 	.endf	mms_handleGetVariableAccessAttr

                     344 	.align	4

                     345 ;domainIdStr	[sp,12]	local

                     346 ;itemIdStr	[sp,16]	local

                     347 ;domainIdLen	[sp,20]	local

                     348 ;itemIdLen	[sp,24]	local

                     349 ;tag	r7	local

                     350 ;length	[sp,28]	local

                     351 ;result	r0	local

                     352 ;.L209	.L215	static

                     353 ;.L210	.L216	static

                     354 ;invokeId	fp	local

                     355 ;ldPos	r5	local


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7k01.s
                     356 ;objectPos	r5	local

                     357 ;accessResultSize	r7	local

                     358 ;invokeIdSize	r10	local

                     359 ;confirmedResponseContentSize	r8	local

                     360 

                     361 ;mmsConn	none	param

                     362 ;inBuf	r5	param

                     363 ;bufPos	r12	param

                     364 ;maxBufPos	r6	param

                     365 ;invokeId	[sp,68]	param

                     366 ;response	r4	param

                     367 

                     368 	.section ".bss","awb"

                     369 .L208:

                     370 	.section ".rodata","a"

                     371 .L211:

                     372 __UNNAMED_1_static_in_getVariableAccessAttr:;	"ldPos:\000"

00000000 6f50646c    373 	.data.b	108,100,80,111

00000004 3a73       374 	.data.b	115,58

00000006 00         375 	.data.b	0

00000007 00         376 	.space	1

                     377 	.type	__UNNAMED_1_static_in_getVariableAccessAttr,$object

                     378 	.size	__UNNAMED_1_static_in_getVariableAccessAttr,8

                     379 .L212:

                     380 __UNNAMED_2_static_in_getVariableAccessAttr:;	"objectPos:\000"

00000008 656a626f    381 	.data.b	111,98,106,101

0000000c 6f507463    382 	.data.b	99,116,80,111

00000010 3a73       383 	.data.b	115,58

00000012 00         384 	.data.b	0

00000013 00         385 	.space	1

                     386 	.type	__UNNAMED_2_static_in_getVariableAccessAttr,$object

                     387 	.size	__UNNAMED_2_static_in_getVariableAccessAttr,12

                     388 	.data

                     389 	.text

                     390 

                     391 ;138: }


                     392 	.align	4

                     393 .L261:

                     394 ;	"!!!!!Object name error\000"

000001c8 21212121    395 	.data.b	33,33,33,33

000001cc 6a624f21    396 	.data.b	33,79,98,106

000001d0 20746365    397 	.data.b	101,99,116,32

000001d4 656d616e    398 	.data.b	110,97,109,101

000001d8 72726520    399 	.data.b	32,101,114,114

000001dc 726f       400 	.data.b	111,114

000001de 00         401 	.data.b	0

000001df 00         402 	.align 4

                     403 

                     404 	.type	.L261,$object

                     405 	.size	.L261,4

                     406 

                     407 .L262:

                     408 ;	"!!!!!Unknown tag\000"

000001e0 21212121    409 	.data.b	33,33,33,33

000001e4 6b6e5521    410 	.data.b	33,85,110,107

000001e8 6e776f6e    411 	.data.b	110,111,119,110

000001ec 67617420    412 	.data.b	32,116,97,103

000001f0 00         413 	.data.b	0

000001f1 000000     414 	.align 4

                     415 

                     416 	.type	.L262,$object


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7k01.s
                     417 	.size	.L262,4

                     418 

                     419 .L263:

000001f4 00000000*   420 	.data.w	.L211

                     421 	.type	.L263,$object

                     422 	.size	.L263,4

                     423 

                     424 .L264:

000001f8 00000000*   425 	.data.w	.L212

                     426 	.type	.L264,$object

                     427 	.size	.L264,4

                     428 

                     429 	.align	4

                     430 ;__UNNAMED_1_static_in_getVariableAccessAttr	.L211	static

                     431 ;__UNNAMED_2_static_in_getVariableAccessAttr	.L212	static

                     432 

                     433 	.data

                     434 	.ghsnote version,6

                     435 	.ghsnote tools,3

                     436 	.ghsnote options,0

                     437 	.text

                     438 	.align	4

                     439 	.section ".rodata","a"

                     440 	.align	4

                     441 	.text

