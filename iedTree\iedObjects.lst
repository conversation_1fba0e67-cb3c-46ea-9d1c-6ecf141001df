                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedObjects.c -o iedTree\gh_cn01.o -list=iedTree/iedObjects.lst C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
Source File: iedObjects.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		iedTree/iedObjects.c -o iedTree/iedObjects.o

                      12 ;Source File:   iedTree/iedObjects.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:49 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "iedObjects.h"


                      23 ;2: 


                      24 ;3: #include "iedFinalDA.h"


                      25 ;4: #include "iedControlModel.h"


                      26 ;5: 


                      27 ;6: #include "../iedmodel.h"


                      28 ;7: #include "../BaseAsnTypes.h"


                      29 ;8: #include "../AsnEncoding.h"


                      30 ;9: #include "IEDCompile/InnerAttributeTypes.h"


                      31 ;10: #include "IEDCompile/AccessInfo.h"


                      32 ;11: #include "../mms_gocb.h"


                      33 ;12: #include "../mms_rcb.h"


                      34 ;13: #include "../pwin_access.h"


                      35 ;14: #include "../control.h"


                      36 ;15: #include "../timers.h"


                      37 ;16: #include <Clib.h>


                      38 ;17: #include <string.h>


                      39 ;18: 


                      40 ;19: 


                      41 ;20: typedef struct {


                      42 ;21:     IEDEntity dataSection;


                      43 ;22:     IEDEntity dataSetSection;


                      44 ;23: } IEDLD;


                      45 ;24: 


                      46 ;25: bool IEDLD_init(IEDEntity entity)


                      47 	.text

                      48 	.align	4

                      49 IEDLD_init::

00000000 e92d4070     50 	stmfd	[sp]!,{r4-r6,lr}


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
00000004 e1a06000     51 	mov	r6,r0

                      52 ;26: {


                      53 

                      54 ;27:     IEDLD* extInfo = IEDEntity_alloc(sizeof(IEDLD));


                      55 

00000008 e3a00008     56 	mov	r0,8

0000000c eb000000*    57 	bl	IEDEntity_alloc

00000010 e1b05000     58 	movs	r5,r0

                      59 ;28:     if(extInfo == NULL)


                      60 

                      61 ;29:     {


                      62 

                      63 ;30:         return false;


                      64 

00000014 020500ff     65 	andeq	r0,r5,255

00000018 0a00000b     66 	beq	.L21

                      67 ;31:     }


                      68 ;32:     entity->extInfo = extInfo;


                      69 

0000001c e5865058     70 	str	r5,[r6,88]

                      71 ;33:     entity->type = IED_ENTITY_LD;


                      72 

00000020 e3a04001     73 	mov	r4,1

00000024 e5864050     74 	str	r4,[r6,80]

                      75 ;34:     extInfo->dataSection = IEDEntity_getChildByTag(entity, IED_VMD_DATA_SECTION);


                      76 

00000028 e1a00006     77 	mov	r0,r6

0000002c e3a010ec     78 	mov	r1,236

00000030 eb000000*    79 	bl	IEDEntity_getChildByTag

00000034 e5850000     80 	str	r0,[r5]

                      81 ;35:     extInfo->dataSetSection = IEDEntity_getChildByTag(entity, IED_VMD_DATA_SET_SECTION);


                      82 

00000038 e1a00006     83 	mov	r0,r6

0000003c e3a010ee     84 	mov	r1,238

00000040 eb000000*    85 	bl	IEDEntity_getChildByTag

00000044 e5850004     86 	str	r0,[r5,4]

                      87 ;36:     return true;


                      88 

00000048 e1a00004     89 	mov	r0,r4

                      90 .L21:

0000004c e8bd8070     91 	ldmfd	[sp]!,{r4-r6,pc}

                      92 	.endf	IEDLD_init

                      93 	.align	4

                      94 ;extInfo	r5	local

                      95 

                      96 ;entity	r6	param

                      97 

                      98 	.section ".bss","awb"

                      99 .L69:

                     100 	.data

                     101 	.text

                     102 

                     103 ;37: }


                     104 

                     105 ;38: 


                     106 ;39: IEDEntity IEDLD_getDataSection(IEDEntity ld)


                     107 	.align	4

                     108 	.align	4

                     109 IEDLD_getDataSection::

                     110 ;40: {


                     111 


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
                     112 ;41:     IEDLD* extInfo = ld->extInfo;


                     113 

00000050 e5900058    114 	ldr	r0,[r0,88]

                     115 ;42:     if(extInfo == NULL)


                     116 

00000054 e3500000    117 	cmp	r0,0

                     118 ;43:     {


                     119 

                     120 ;44:         return NULL;


                     121 

                     122 ;45:     }


                     123 ;46:     return extInfo->dataSection;


                     124 

00000058 15900000    125 	ldrne	r0,[r0]

0000005c e12fff1e*   126 	ret	

                     127 	.endf	IEDLD_getDataSection

                     128 	.align	4

                     129 ;extInfo	r0	local

                     130 

                     131 ;ld	r0	param

                     132 

                     133 	.section ".bss","awb"

                     134 .L133:

                     135 	.data

                     136 	.text

                     137 

                     138 ;47: }


                     139 

                     140 ;48: 


                     141 ;49: IEDEntity IEDLD_getDataSetSection(IEDEntity ld)


                     142 	.align	4

                     143 	.align	4

                     144 IEDLD_getDataSetSection::

                     145 ;50: {


                     146 

                     147 ;51:     IEDLD* extInfo = ld->extInfo;


                     148 

00000060 e5900058    149 	ldr	r0,[r0,88]

                     150 ;52:     if(extInfo == NULL)


                     151 

00000064 e3500000    152 	cmp	r0,0

                     153 ;53:     {


                     154 

                     155 ;54:         return NULL;


                     156 

                     157 ;55:     }


                     158 ;56:     return extInfo->dataSetSection;


                     159 

00000068 15900004    160 	ldrne	r0,[r0,4]

0000006c e12fff1e*   161 	ret	

                     162 	.endf	IEDLD_getDataSetSection

                     163 	.align	4

                     164 ;extInfo	r0	local

                     165 

                     166 ;ld	r0	param

                     167 

                     168 	.section ".bss","awb"

                     169 .L197:

                     170 	.data

                     171 	.text

                     172 


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
                     173 ;57: }


                     174 

                     175 ;58: 


                     176 ;59: bool IEDComplexObj_init(IEDEntity entity)


                     177 	.align	4

                     178 	.align	4

                     179 IEDComplexObj_init::

                     180 ;60: {


                     181 

                     182 ;61: 	switch(entity->tag)


                     183 

00000070 e5d01020    184 	ldrb	r1,[r0,32]

00000074 e25110e0    185 	subs	r1,r1,224

                     186 ;68: 		break;


                     187 ;69: 	case IED_FC:


                     188 ;70: 		entity->type = IED_ENTITY_FC;


                     189 

00000078 03a01004    190 	moveq	r1,4

                     191 ;75: 	}


                     192 ;76: 	return true;


                     193 

0000007c 05801050    194 	streq	r1,[r0,80]

00000080 03a00001    195 	moveq	r0,1

00000084 0a000009    196 	beq	.L210

00000088 e2511004    197 	subs	r1,r1,4

                     198 ;65: 		break;


                     199 ;66: 	case IED_LN:


                     200 ;67: 		entity->type = IED_ENTITY_LN;		


                     201 

0000008c 03a01003    202 	moveq	r1,3

                     203 ;75: 	}


                     204 ;76: 	return true;


                     205 

00000090 05801050    206 	streq	r1,[r0,80]

00000094 03a00001    207 	moveq	r0,1

00000098 0a000004    208 	beq	.L210

0000009c e3510008    209 	cmp	r1,8

                     210 ;71: 		break;


                     211 ;72: 	default:


                     212 ;73: 		ERROR_REPORT("Invalid object tag");


                     213 ;74: 		return false;


                     214 

000000a0 13a00000    215 	movne	r0,0

                     216 ;62: 	{


                     217 ;63: 	case IED_VMD_DATA_SECTION:


                     218 ;64: 		entity->type = IED_ENTITY_DATA_SECTION;


                     219 

000000a4 03a01002    220 	moveq	r1,2

                     221 ;75: 	}


                     222 ;76: 	return true;


                     223 

000000a8 05801050    224 	streq	r1,[r0,80]

000000ac 03a00001    225 	moveq	r0,1

                     226 .L210:

000000b0 e12fff1e*   227 	ret	

                     228 	.endf	IEDComplexObj_init

                     229 	.align	4

                     230 

                     231 ;entity	r0	param

                     232 

                     233 	.section ".bss","awb"


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
                     234 .L266:

                     235 	.data

                     236 	.text

                     237 

                     238 ;77: }


                     239 

                     240 ;78: 


                     241 ;79: bool IEDDO_init(IEDEntity entity)


                     242 	.align	4

                     243 	.align	4

                     244 IEDDO_init::

000000b4 e92d4010    245 	stmfd	[sp]!,{r4,lr}

                     246 ;80: {


                     247 

                     248 ;81: 	IEDEntity timeStampDA;


                     249 ;82:     IEDEntity oper;


                     250 ;83:     entity->type = IED_ENTITY_DO;


                     251 

000000b8 e3a01005    252 	mov	r1,5

000000bc e5801050    253 	str	r1,[r0,80]

                     254 ;84: 


                     255 ;85: 	// Если у этого DO есть t, то для него и всех его


                     256 ;86: 	// детей устанавлмваем указатель на объект timeStamp


                     257 ;87: 	timeStampDA = IEDDO_getTimeStampDA(entity);


                     258 

000000c0 e1a04000    259 	mov	r4,r0

000000c4 eb00000f*   260 	bl	IEDDO_getTimeStampDA

                     261 ;88: 	if (timeStampDA != NULL)


                     262 

000000c8 e3500000    263 	cmp	r0,0

                     264 ;89: 	{


                     265 

                     266 ;90: 		TimeStamp* pTimeStamp = timeStampDA->extInfo;


                     267 

000000cc 15901058    268 	ldrne	r1,[r0,88]

                     269 ;91: 		IEDEntity_attachTimeStamp(entity, pTimeStamp);


                     270 

000000d0 11a00004    271 	movne	r0,r4

000000d4 1b000000*   272 	blne	IEDEntity_attachTimeStamp

                     273 ;92: 	}


                     274 ;93: 


                     275 ;94:     oper = IEDEntity_getChildByCStrName(entity, "Oper");


                     276 

000000d8 e28f1000*   277 	adr	r1,.L383

000000dc e1a00004    278 	mov	r0,r4

000000e0 eb000000*   279 	bl	IEDEntity_getChildByCStrName

                     280 ;95: 	if(oper != NULL && IEDControlDA_isControlDA(oper))


                     281 

000000e4 e3500000    282 	cmp	r0,0

000000e8 0a000004    283 	beq	.L287

000000ec eb000000*   284 	bl	IEDControlDA_isControlDA

000000f0 e3500000    285 	cmp	r0,0

                     286 ;96:     {


                     287 

                     288 ;97:         return IEDControlDO_init(entity);


                     289 

000000f4 11a00004    290 	movne	r0,r4

000000f8 18bd4010    291 	ldmnefd	[sp]!,{r4,lr}

000000fc 1a000000*   292 	bne	IEDControlDO_init

                     293 .L287:

                     294 ;98:     }



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
                     295 ;99:     return true;


                     296 

00000100 e3a00001    297 	mov	r0,1

00000104 e8bd8010    298 	ldmfd	[sp]!,{r4,pc}

                     299 	.endf	IEDDO_init

                     300 	.align	4

                     301 ;timeStampDA	r0	local

                     302 ;oper	r1	local

                     303 ;.L361	.L364	static

                     304 

                     305 ;entity	r4	param

                     306 

                     307 	.section ".bss","awb"

                     308 .L360:

                     309 	.data

                     310 	.text

                     311 

                     312 ;100: }


                     313 

                     314 ;101: 


                     315 ;102: IEDEntity IEDDO_getTimeStampDA(IEDEntity entity)


                     316 	.align	4

                     317 	.align	4

                     318 IEDDO_getTimeStampDA::

00000108 e92d4010    319 	stmfd	[sp]!,{r4,lr}

                     320 ;103: {


                     321 

                     322 ;104: 	IEDEntity child = entity->firstChild;


                     323 

0000010c e5904004    324 	ldr	r4,[r0,4]

                     325 ;105: 	while(child != NULL)


                     326 

00000110 e3540000    327 	cmp	r4,0

00000114 0a000009    328 	beq	.L387

                     329 .L388:

                     330 ;106: 	{


                     331 

                     332 ;107: 		if(child->type == IED_ENTITY_DA_TIMESTAMP


                     333 

00000118 e5940050    334 	ldr	r0,[r4,80]

0000011c e3500009    335 	cmp	r0,9

00000120 1a000003    336 	bne	.L389

00000124 e28f1000*   337 	adr	r1,.L487

00000128 e2840048    338 	add	r0,r4,72

0000012c eb000000*   339 	bl	StringView_cmpCStr

00000130 e3500000    340 	cmp	r0,0

                     341 .L389:

                     342 ;108: 		   //Timestamp бывает не только t, а нас интересует только t


                     343 ;109: 		   && StringView_cmpCStr(&child->name, "t") == 0)


                     344 ;110: 		{


                     345 

                     346 ;111: 			return child;


                     347 

                     348 ;112: 		}


                     349 ;113: 		child = child->next;


                     350 

00000134 1594400c    351 	ldrne	r4,[r4,12]

00000138 13540000    352 	cmpne	r4,0

0000013c 1afffff5    353 	bne	.L388

                     354 .L387:

                     355 ;114: 	}



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
                     356 ;115: 	return NULL;


                     357 

00000140 e1a00004    358 	mov	r0,r4

00000144 e8bd8010    359 	ldmfd	[sp]!,{r4,pc}

                     360 	.endf	IEDDO_getTimeStampDA

                     361 	.align	4

                     362 ;child	r4	local

                     363 ;.L470	.L473	static

                     364 

                     365 ;entity	r0	param

                     366 

                     367 	.section ".bss","awb"

                     368 .L469:

                     369 	.data

                     370 	.text

                     371 

                     372 ;116: }


                     373 

                     374 ;117: 


                     375 ;118: bool IEDDA_init(IEDEntity entity)


                     376 	.align	4

                     377 	.align	4

                     378 IEDDA_init::

00000148 e92d4010    379 	stmfd	[sp]!,{r4,lr}

                     380 ;119: {


                     381 

                     382 ;120:     uint8_t tag;


                     383 ;121:     BufferView ber = entity->ber;


                     384 

0000014c e24dd010    385 	sub	sp,sp,16

00000150 e28d3004    386 	add	r3,sp,4

00000154 e1a04000    387 	mov	r4,r0

00000158 e2840014    388 	add	r0,r4,20

0000015c e8900007    389 	ldmfd	[r0],{r0-r2}

00000160 e8830007    390 	stmea	[r3],{r0-r2}

                     391 ;122:     entity->type = IED_ENTITY_DA;


                     392 

00000164 e3a00006    393 	mov	r0,6

00000168 e5840050    394 	str	r0,[r4,80]

                     395 ;123: 


                     396 ;124:     //Проверяем наличие информации о сервисе Control


                     397 ;125:     if(entity->name.p == NULL)


                     398 

0000016c e594004c    399 	ldr	r0,[r4,76]

00000170 e3500000    400 	cmp	r0,0

00000174 0a000016    401 	beq	.L504

                     402 ;126:     {


                     403 

                     404 ;127:         //Если нет имени, то и дополнительной информации нет


                     405 ;128:         return true;


                     406 

                     407 ;129:     }


                     408 ;130: 


                     409 ;131:     //Пропустить тэг и длину


                     410 ;132:     BufferView_decodeTL(&ber, NULL, NULL, NULL);


                     411 

00000178 e1a00003    412 	mov	r0,r3

0000017c e3a03000    413 	mov	r3,0

00000180 e1a02003    414 	mov	r2,r3

00000184 e1a01003    415 	mov	r1,r3

00000188 eb000000*   416 	bl	BufferView_decodeTL


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
                     417 ;133: 


                     418 ;134:     if(BufferView_endOfBuf(&ber))


                     419 

0000018c e59d100c    420 	ldr	r1,[sp,12]

00000190 e59d0008    421 	ldr	r0,[sp,8]

00000194 e1500001    422 	cmp	r0,r1

00000198 0a00000d    423 	beq	.L504

                     424 ;135:     {


                     425 

                     426 ;136:         return true;


                     427 

0000019c e28d0004    428 	add	r0,sp,4

000001a0 e3a02000    429 	mov	r2,0

000001a4 e3a0101a    430 	mov	r1,26

000001a8 eb000000*   431 	bl	BufferView_skipObject

                     432 ;137:     }


                     433 ;138: 


                     434 ;139:     //Пропустить имя


                     435 ;140:     if(!BufferView_skipObject(&ber, ASN_VISIBLE_STRING, false))


                     436 

000001ac e3500000    437 	cmp	r0,0

                     438 ;141:     {


                     439 

                     440 ;142:         ERROR_REPORT("Name is not found");


                     441 ;143:         return false;


                     442 

000001b0 0a00000c    443 	beq	.L488

                     444 ;144:     }


                     445 ;145: 


                     446 ;146:     if(!BufferView_peekTag(&ber, &tag))


                     447 

000001b4 e28d1003    448 	add	r1,sp,3

000001b8 e28d0004    449 	add	r0,sp,4

000001bc eb000000*   450 	bl	BufferView_peekTag

000001c0 e3500000    451 	cmp	r0,0

000001c4 0a000002    452 	beq	.L504

                     453 ;147:     {


                     454 

                     455 ;148:         return true;


                     456 

                     457 ;149:     }


                     458 ;150: 


                     459 ;151:     if(tag != IED_CONTROL_INFO)


                     460 

000001c8 e5dd0003    461 	ldrb	r0,[sp,3]

000001cc e35000f1    462 	cmp	r0,241

000001d0 0a000001    463 	beq	.L503

                     464 .L504:

                     465 ;152:     {


                     466 

                     467 ;153:         return true;


                     468 

000001d4 e3a00001    469 	mov	r0,1

000001d8 ea000002    470 	b	.L488

                     471 .L503:

                     472 ;154:     }


                     473 ;155: 


                     474 ;156:     return IEDControlDA_init(entity, &ber);


                     475 

000001dc e28d1004    476 	add	r1,sp,4

000001e0 e1a00004    477 	mov	r0,r4


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
000001e4 eb000000*   478 	bl	IEDControlDA_init

                     479 .L488:

000001e8 e28dd010    480 	add	sp,sp,16

000001ec e8bd8010    481 	ldmfd	[sp]!,{r4,pc}

                     482 	.endf	IEDDA_init

                     483 	.align	4

                     484 ;tag	[sp,3]	local

                     485 ;ber	[sp,4]	local

                     486 

                     487 ;entity	r4	param

                     488 

                     489 	.section ".bss","awb"

                     490 .L618:

                     491 	.data

                     492 	.text

                     493 

                     494 ;157: }


                     495 

                     496 ;158: 


                     497 ;159: bool IEDConstDA_calcReadLen(IEDEntity entity, size_t *pLen)


                     498 	.align	4

                     499 	.align	4

                     500 IEDConstDA_calcReadLen::

000001f0 e92d4010    501 	stmfd	[sp]!,{r4,lr}

000001f4 e1a04001    502 	mov	r4,r1

                     503 ;160: {


                     504 

                     505 ;161:     int dataLen;


                     506 ;162:     int constPos;


                     507 ;163: 


                     508 ;164:     if(entity->type != IED_ENTITY_DA_CONST)


                     509 

000001f8 e5901050    510 	ldr	r1,[r0,80]

000001fc e3510007    511 	cmp	r1,7

00000200 1a000008    512 	bne	.L653

                     513 ;165:     {


                     514 

                     515 ;166:         ERROR_REPORT("Invalid terminal item DA");


                     516 ;167:         return false;


                     517 

                     518 ;168:     }


                     519 ;169: 


                     520 ;170:     //Получаем позицию в модели для вызова старой функции


                     521 ;171:     constPos = (int)entity->extInfo;


                     522 

00000204 e3a03001    523 	mov	r3,1

00000208 e5902058    524 	ldr	r2,[r0,88]

                     525 ;172: 


                     526 ;173:     dataLen = encodeReadConst(0, 0, constPos, true);


                     527 

0000020c e3a01000    528 	mov	r1,0

00000210 e1a00001    529 	mov	r0,r1

00000214 eb000000*   530 	bl	encodeReadConst

                     531 ;174:     if(dataLen <=0)


                     532 

00000218 e3500000    533 	cmp	r0,0

                     534 ;178:     }


                     535 ;179: 	*pLen = dataLen;


                     536 

0000021c c5840000    537 	strgt	r0,[r4]

                     538 ;180: 



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
                     539 ;181:     return true;


                     540 

00000220 c3a00001    541 	movgt	r0,1

00000224 ca000000    542 	bgt	.L647

                     543 .L653:

                     544 ;175:     {


                     545 

                     546 ;176:         ERROR_REPORT("Invalid read length");


                     547 ;177:         return false;


                     548 

00000228 e3a00000    549 	mov	r0,0

                     550 .L647:

0000022c e8bd8010    551 	ldmfd	[sp]!,{r4,pc}

                     552 	.endf	IEDConstDA_calcReadLen

                     553 	.align	4

                     554 ;dataLen	r0	local

                     555 

                     556 ;entity	r0	param

                     557 ;pLen	r4	param

                     558 

                     559 	.section ".bss","awb"

                     560 .L700:

                     561 	.data

                     562 	.text

                     563 

                     564 ;182: }


                     565 

                     566 ;183: 


                     567 ;184: bool IEDConstDA_encodeRead(IEDEntity entity, BufferView *outBufView)


                     568 	.align	4

                     569 	.align	4

                     570 IEDConstDA_encodeRead::

00000230 e92d4010    571 	stmfd	[sp]!,{r4,lr}

00000234 e1a04001    572 	mov	r4,r1

                     573 ;185: {


                     574 

                     575 ;186:     int dataLen;


                     576 ;187:     int constPos;


                     577 ;188:     uint8_t *writeBuf;


                     578 ;189: 


                     579 ;190:     if(entity->type != IED_ENTITY_DA_CONST)


                     580 

00000238 e5901050    581 	ldr	r1,[r0,80]

0000023c e3510007    582 	cmp	r1,7

00000240 1a00000c    583 	bne	.L726

                     584 ;191:     {


                     585 

                     586 ;192:         ERROR_REPORT("Invalid terminal item DA");


                     587 ;193:         return false;


                     588 

                     589 ;194:     }


                     590 ;195: 


                     591 ;196:     //Получаем позицию в модели для вызова старой функции


                     592 ;197:     constPos = (int)entity->extInfo;


                     593 

00000244 e5902058    594 	ldr	r2,[r0,88]

                     595 ;198: 


                     596 ;199:     writeBuf = outBufView->p + outBufView->pos;


                     597 

00000248 e894000a    598 	ldmfd	[r4],{r1,r3}

0000024c e0830001    599 	add	r0,r3,r1


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
                     600 ;200: 


                     601 ;201:     dataLen = encodeReadConst(writeBuf, 0, constPos, false);


                     602 

00000250 e3a03000    603 	mov	r3,0

00000254 e1a01003    604 	mov	r1,r3

00000258 eb000000*   605 	bl	encodeReadConst

0000025c e2501000    606 	subs	r1,r0,0

                     607 ;202:     if(dataLen <=0)


                     608 

00000260 da000004    609 	ble	.L726

                     610 ;203:     {


                     611 

                     612 ;204:         ERROR_REPORT("Invalid read length");


                     613 ;205:         return false;


                     614 

                     615 ;206:     }


                     616 ;207: 


                     617 ;208:     if(!BufferView_advance(outBufView, dataLen))


                     618 

00000264 e1a00004    619 	mov	r0,r4

00000268 eb000000*   620 	bl	BufferView_advance

0000026c e3500000    621 	cmp	r0,0

                     622 ;212:     }


                     623 ;213:     return true;


                     624 

00000270 13a00001    625 	movne	r0,1

00000274 1a000000    626 	bne	.L717

                     627 .L726:

                     628 ;209:     {


                     629 

                     630 ;210:         ERROR_REPORT("Buffer overflow");


                     631 ;211:         return false;


                     632 

00000278 e3a00000    633 	mov	r0,0

                     634 .L717:

0000027c e8bd8010    635 	ldmfd	[sp]!,{r4,pc}

                     636 	.endf	IEDConstDA_encodeRead

                     637 	.align	4

                     638 ;dataLen	r1	local

                     639 ;writeBuf	r0	local

                     640 

                     641 ;entity	r0	param

                     642 ;outBufView	r4	param

                     643 

                     644 	.section ".bss","awb"

                     645 .L790:

                     646 	.data

                     647 	.text

                     648 

                     649 ;214: }


                     650 

                     651 ;215: 


                     652 ;216: 


                     653 ;217: 


                     654 ;218: MmsDataAccessError IEDComplexObj_write(IEDEntity entity,


                     655 	.align	4

                     656 	.align	4

                     657 IEDComplexObj_write::

00000280 e92d44f0    658 	stmfd	[sp]!,{r4-r7,r10,lr}

                     659 ;219:                                            IsoConnection* isoConn, BufferView* value)


                     660 ;220: {



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
                     661 

                     662 ;221:     uint8_t tag;


                     663 ;222:     size_t len;


                     664 ;223:     IEDEntity nextChild;


                     665 ;224:     BufferView controlValue;


                     666 ;225: 


                     667 ;226:     if(entity->type != IED_ENTITY_DA  && entity->type != IED_ENTITY_DO)


                     668 

00000284 e1a07001    669 	mov	r7,r1

00000288 e24dd014    670 	sub	sp,sp,20

0000028c e1a06000    671 	mov	r6,r0

00000290 e5960050    672 	ldr	r0,[r6,80]

00000294 e1a05002    673 	mov	r5,r2

00000298 e3500006    674 	cmp	r0,6

0000029c 13500005    675 	cmpne	r0,5

                     676 ;227:     {


                     677 

                     678 ;228:         ERROR_REPORT("Invalid object to write");


                     679 ;229:         return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;


                     680 

000002a0 13a00007    681 	movne	r0,7

000002a4 1a00002f    682 	bne	.L810

                     683 ;230:     }


                     684 ;231: 


                     685 ;232:     if(!BufferView_decodeTL(value, &tag, &len, NULL))


                     686 

000002a8 e28d2004    687 	add	r2,sp,4

000002ac e28d1003    688 	add	r1,sp,3

000002b0 e1a00005    689 	mov	r0,r5

000002b4 e3a03000    690 	mov	r3,0

000002b8 eb000000*   691 	bl	BufferView_decodeTL

000002bc e3500000    692 	cmp	r0,0

000002c0 0a000002    693 	beq	.L820

                     694 ;233:     {


                     695 

                     696 ;234:         ERROR_REPORT("Invalid write value");


                     697 ;235:         return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                     698 

                     699 ;236:     }


                     700 ;237:     if(tag != 0xA2)


                     701 

000002c4 e5dd0003    702 	ldrb	r0,[sp,3]

000002c8 e35000a2    703 	cmp	r0,162

000002cc 0a000001    704 	beq	.L819

                     705 .L820:

                     706 ;238:     {


                     707 

                     708 ;239:         ERROR_REPORT("Structure tag expected");


                     709 ;240:         return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                     710 

000002d0 e3a0000b    711 	mov	r0,11

000002d4 ea000023    712 	b	.L810

                     713 .L819:

                     714 ;241:     }


                     715 ;242: 


                     716 ;243: 	if(IEDControlDA_isControlDA(entity))


                     717 

000002d8 e1a00006    718 	mov	r0,r6

000002dc eb000000*   719 	bl	IEDControlDA_isControlDA

000002e0 e3500000    720 	cmp	r0,0

000002e4 0a00000a    721 	beq	.L822


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
                     722 ;244:     {


                     723 

                     724 ;245:         if(IEDControlDA_isReady(entity))


                     725 

000002e8 e1a00006    726 	mov	r0,r6

000002ec eb000000*   727 	bl	IEDControlDA_isReady

000002f0 e3500000    728 	cmp	r0,0

                     729 ;246:         {


                     730 

                     731 ;247:             return DATA_ACCESS_ERROR_TEMPORARILY_UNAVAILABLE;


                     732 

000002f4 13a00002    733 	movne	r0,2

000002f8 1a00001a    734 	bne	.L810

                     735 ;248:         }


                     736 ;249:         //Сохраняем значение для control объектов


                     737 ;250:         BufferView_init(&controlValue, value->p + value->pos, len, 0);


                     738 

000002fc e59d2004    739 	ldr	r2,[sp,4]

00000300 e895000a    740 	ldmfd	[r5],{r1,r3}

00000304 e28d0008    741 	add	r0,sp,8

00000308 e0831001    742 	add	r1,r3,r1

0000030c e3a03000    743 	mov	r3,0

00000310 eb000000*   744 	bl	BufferView_init

                     745 .L822:

                     746 ;251:     }


                     747 ;252: 


                     748 ;253: 


                     749 ;254:     nextChild = entity->firstChild;


                     750 

00000314 e5964004    751 	ldr	r4,[r6,4]

                     752 ;255:     while(nextChild != NULL)


                     753 

00000318 e3e0a000    754 	mvn	r10,0

0000031c e3540000    755 	cmp	r4,0

00000320 0a000008    756 	beq	.L828

                     757 .L829:

                     758 ;256:     {


                     759 

                     760 ;257:         MmsDataAccessError result =


                     761 

00000324 e1a02005    762 	mov	r2,r5

00000328 e1a01007    763 	mov	r1,r7

0000032c e1a00004    764 	mov	r0,r4

00000330 eb000000*   765 	bl	IEDEntity_write

                     766 ;258:                 IEDEntity_write(nextChild, isoConn, value);


                     767 ;259:         if(result != DATA_ACCESS_ERROR_SUCCESS)


                     768 

00000334 e150000a    769 	cmp	r0,r10

                     770 ;260:         {


                     771 

                     772 ;261:             return result;


                     773 

00000338 1a00000a    774 	bne	.L810

                     775 ;262:         }


                     776 ;263:         nextChild = nextChild->next;


                     777 

0000033c e594400c    778 	ldr	r4,[r4,12]

00000340 e3540000    779 	cmp	r4,0

00000344 1afffff6    780 	bne	.L829

                     781 .L828:

                     782 ;264:     }



                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
                     783 ;265: 


                     784 ;266: 	if(IEDControlDA_isControlDA(entity))


                     785 

00000348 e1a00006    786 	mov	r0,r6

0000034c eb000000*   787 	bl	IEDControlDA_isControlDA

00000350 e3500000    788 	cmp	r0,0

                     789 ;269:     }


                     790 ;270:     else


                     791 ;271:     {


                     792 

                     793 ;272:         return DATA_ACCESS_ERROR_SUCCESS;


                     794 

00000354 128d2008    795 	addne	r2,sp,8

00000358 11a01007    796 	movne	r1,r7

0000035c 03e00000    797 	mvneq	r0,0

                     798 ;267:     {


                     799 

                     800 ;268: 		return IEDControlDA_write(entity, isoConn, &controlValue);


                     801 

00000360 11a00006    802 	movne	r0,r6

00000364 1b000000*   803 	blne	IEDControlDA_write

                     804 .L810:

00000368 e28dd014    805 	add	sp,sp,20

0000036c e8bd84f0    806 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     807 	.endf	IEDComplexObj_write

                     808 	.align	4

                     809 .L383:

                     810 ;	"Oper\000"

00000370 7265704f    811 	.data.b	79,112,101,114

00000374 00         812 	.data.b	0

00000375 000000     813 	.align 4

                     814 

                     815 	.type	.L383,$object

                     816 	.size	.L383,4

                     817 

                     818 .L487:

                     819 ;	"t\000"

00000378 0074       820 	.data.b	116,0

0000037a 0000       821 	.align 4

                     822 

                     823 	.type	.L487,$object

                     824 	.size	.L487,4

                     825 

                     826 	.align	4

                     827 ;tag	[sp,3]	local

                     828 ;len	[sp,4]	local

                     829 ;nextChild	r4	local

                     830 ;controlValue	[sp,8]	local

                     831 ;result	r0	local

                     832 

                     833 ;entity	r6	param

                     834 ;isoConn	r7	param

                     835 ;value	r5	param

                     836 

                     837 	.section ".bss","awb"

                     838 .L982:

                     839 	.data

                     840 	.text

                     841 

                     842 ;273:     }


                     843 ;274: 



                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
                     844 ;275: }


                     845 

                     846 ;276: 


                     847 ;277: static bool calcChildrenReadLen(IEDEntity entity, size_t* pLen )


                     848 	.align	4

                     849 	.align	4

                     850 calcChildrenReadLen:

0000037c e92d40f0    851 	stmfd	[sp]!,{r4-r7,lr}

                     852 ;278: {


                     853 

00000380 e1a05001    854 	mov	r5,r1

00000384 e3a04000    855 	mov	r4,0

                     856 ;279:     IEDEntity nextChild;


                     857 ;280:     size_t childrenLen = 0;


                     858 

                     859 ;281: 


                     860 ;282:     nextChild = entity->firstChild;


                     861 

00000388 e24dd004    862 	sub	sp,sp,4

0000038c e5906004    863 	ldr	r6,[r0,4]

                     864 ;283:     while(nextChild != NULL)


                     865 

00000390 e1a0700d    866 	mov	r7,sp

00000394 e3560000    867 	cmp	r6,0

00000398 0a00000b    868 	beq	.L1036

                     869 .L1037:

                     870 ;284:     {


                     871 

                     872 ;285:         size_t childLen;


                     873 ;286: 		bool result = nextChild->calcReadLen(nextChild, &childLen);


                     874 

0000039c e596c060    875 	ldr	r12,[r6,96]

000003a0 e1a01007    876 	mov	r1,r7

000003a4 e1a00006    877 	mov	r0,r6

000003a8 e1a0e00f    878 	mov	lr,pc

000003ac e12fff1c*   879 	bx	r12

                     880 ;287:         if(!result)


                     881 

000003b0 e3500000    882 	cmp	r0,0

                     883 ;288:         {


                     884 

                     885 ;289:             return false;


                     886 

000003b4 0a000006    887 	beq	.L1033

                     888 ;290:         }


                     889 ;291:         childrenLen += childLen;


                     890 

000003b8 e59d0000    891 	ldr	r0,[sp]

000003bc e596600c    892 	ldr	r6,[r6,12]

000003c0 e0844000    893 	add	r4,r4,r0

                     894 ;292:         nextChild = nextChild->next;


                     895 

000003c4 e3560000    896 	cmp	r6,0

000003c8 1afffff3    897 	bne	.L1037

                     898 .L1036:

                     899 ;293:     }


                     900 ;294:     *pLen = childrenLen;


                     901 

000003cc e5854000    902 	str	r4,[r5]

                     903 ;295:     return true;


                     904 


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
000003d0 e3a00001    905 	mov	r0,1

                     906 .L1033:

000003d4 e28dd004    907 	add	sp,sp,4

000003d8 e8bd40f0    908 	ldmfd	[sp]!,{r4-r7,lr}

000003dc e12fff1e*   909 	ret	

                     910 	.endf	calcChildrenReadLen

                     911 	.align	4

                     912 ;nextChild	r6	local

                     913 ;childrenLen	r4	local

                     914 ;childLen	[sp]	local

                     915 ;result	r0	local

                     916 

                     917 ;entity	r0	param

                     918 ;pLen	r5	param

                     919 

                     920 	.section ".bss","awb"

                     921 .L1114:

                     922 	.data

                     923 	.text

                     924 

                     925 ;296: }


                     926 

                     927 ;297: 


                     928 ;298: bool IEDComplexObj_calcReadLen(IEDEntity entity, size_t* pLen)


                     929 	.align	4

                     930 	.align	4

                     931 IEDComplexObj_calcReadLen::

000003e0 e92d4010    932 	stmfd	[sp]!,{r4,lr}

000003e4 e24dd004    933 	sub	sp,sp,4

000003e8 e1a04001    934 	mov	r4,r1

000003ec e1a0100d    935 	mov	r1,sp

000003f0 ebffffe1*   936 	bl	calcChildrenReadLen

                     937 ;299: {


                     938 

                     939 ;300:     size_t childrenLen;


                     940 ;301: 


                     941 ;302:     if(!calcChildrenReadLen(entity, &childrenLen))


                     942 

000003f4 e3500000    943 	cmp	r0,0

                     944 ;303:     {


                     945 

                     946 ;304:         ERROR_REPORT("calcChildrenReadLen error");


                     947 ;305:         return false;


                     948 

000003f8 0a000006    949 	beq	.L1135

                     950 ;306:     }


                     951 ;307: 


                     952 ;308:     *pLen = 1 + BerEncoder_determineLengthSize(childrenLen) + childrenLen;


                     953 

000003fc e59d0000    954 	ldr	r0,[sp]

00000400 eb000000*   955 	bl	BerEncoder_determineLengthSize

00000404 e59d1000    956 	ldr	r1,[sp]

00000408 e2800001    957 	add	r0,r0,1

0000040c e0810000    958 	add	r0,r1,r0

00000410 e5840000    959 	str	r0,[r4]

                     960 ;309:     return true;


                     961 

00000414 e3a00001    962 	mov	r0,1

                     963 .L1135:

00000418 e28dd004    964 	add	sp,sp,4

0000041c e8bd8010    965 	ldmfd	[sp]!,{r4,pc}


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
                     966 	.endf	IEDComplexObj_calcReadLen

                     967 	.align	4

                     968 ;childrenLen	[sp]	local

                     969 

                     970 ;entity	none	param

                     971 ;pLen	r4	param

                     972 

                     973 	.section ".bss","awb"

                     974 .L1189:

                     975 	.data

                     976 	.text

                     977 

                     978 ;310: }


                     979 

                     980 ;311: 


                     981 ;312: bool IEDComplexObj_encodeRead(IEDEntity entity, BufferView* outBuf)


                     982 	.align	4

                     983 	.align	4

                     984 IEDComplexObj_encodeRead::

00000420 e92d4030    985 	stmfd	[sp]!,{r4-r5,lr}

00000424 e24dd004    986 	sub	sp,sp,4

00000428 e1a04000    987 	mov	r4,r0

0000042c e1a05001    988 	mov	r5,r1

                     989 ;313: {


                     990 

                     991 ;314:     IEDEntity nextChild;


                     992 ;315:     size_t childrenReadLen;


                     993 ;316: 


                     994 ;317:     if(entity->type != IED_ENTITY_LN


                     995 

00000430 e5941050    996 	ldr	r1,[r4,80]

00000434 e3510003    997 	cmp	r1,3

00000438 13510004    998 	cmpne	r1,4

0000043c 13510005    999 	cmpne	r1,5

00000440 13510006   1000 	cmpne	r1,6

00000444 1a000009   1001 	bne	.L1215

                    1002 ;318:             && entity->type != IED_ENTITY_FC


                    1003 ;319:             && entity->type != IED_ENTITY_DO


                    1004 ;320:             && entity->type != IED_ENTITY_DA)


                    1005 ;321:     {


                    1006 

                    1007 ;322:         ERROR_REPORT("Invalid complex object");


                    1008 ;323:         return false;


                    1009 

                    1010 ;324:     }


                    1011 ;325: 


                    1012 ;326:     if(!calcChildrenReadLen(entity, &childrenReadLen))


                    1013 

00000448 e1a0100d   1014 	mov	r1,sp

0000044c ebffffca*  1015 	bl	calcChildrenReadLen

00000450 e3500000   1016 	cmp	r0,0

00000454 0a000005   1017 	beq	.L1215

                    1018 ;327:     {


                    1019 

                    1020 ;328:         ERROR_REPORT("calcChildrenReadLen error");


                    1021 ;329:         return false;


                    1022 

                    1023 ;330:     }


                    1024 ;331: 


                    1025 ;332:     //Тэг и размер структуры


                    1026 ;333:     if(!BufferView_encodeTL(outBuf, 0xA2, childrenReadLen))



                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
                    1027 

00000458 e59d2000   1028 	ldr	r2,[sp]

0000045c e1a00005   1029 	mov	r0,r5

00000460 e3a010a2   1030 	mov	r1,162

00000464 eb000000*  1031 	bl	BufferView_encodeTL

00000468 e3500000   1032 	cmp	r0,0

0000046c 1a000001   1033 	bne	.L1214

                    1034 .L1215:

                    1035 ;334:     {


                    1036 

                    1037 ;335:         ERROR_REPORT("Children TL encoding error");


                    1038 ;336:         return false;


                    1039 

00000470 e3a00000   1040 	mov	r0,0

00000474 ea00000d   1041 	b	.L1203

                    1042 .L1214:

                    1043 ;337:     }


                    1044 ;338: 


                    1045 ;339:     nextChild = entity->firstChild;


                    1046 

00000478 e5944004   1047 	ldr	r4,[r4,4]

                    1048 ;340:     while(nextChild != NULL)


                    1049 

0000047c e3540000   1050 	cmp	r4,0

00000480 0a000009   1051 	beq	.L1218

                    1052 .L1219:

                    1053 ;341:     {


                    1054 

                    1055 ;342: 		bool result = nextChild->encodeRead(nextChild, outBuf);


                    1056 

00000484 e594c05c   1057 	ldr	r12,[r4,92]

00000488 e1a01005   1058 	mov	r1,r5

0000048c e1a00004   1059 	mov	r0,r4

00000490 e1a0e00f   1060 	mov	lr,pc

00000494 e12fff1c*  1061 	bx	r12

                    1062 ;343:         if(!result)


                    1063 

00000498 e3500000   1064 	cmp	r0,0

0000049c 0afffff3   1065 	beq	.L1215

                    1066 ;344:         {


                    1067 

                    1068 ;345:             ERROR_REPORT("Error reading child");


                    1069 ;346:             return false;


                    1070 

                    1071 ;347:         }


                    1072 ;348:         nextChild = nextChild->next;


                    1073 

000004a0 e594400c   1074 	ldr	r4,[r4,12]

000004a4 e3540000   1075 	cmp	r4,0

000004a8 1afffff5   1076 	bne	.L1219

                    1077 .L1218:

                    1078 ;349:     }


                    1079 ;350:     return true;


                    1080 

000004ac e3a00001   1081 	mov	r0,1

                    1082 .L1203:

000004b0 e28dd004   1083 	add	sp,sp,4

000004b4 e8bd8030   1084 	ldmfd	[sp]!,{r4-r5,pc}

                    1085 	.endf	IEDComplexObj_encodeRead

                    1086 	.align	4

                    1087 ;nextChild	r4	local


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cn01.s
                    1088 ;childrenReadLen	[sp]	local

                    1089 

                    1090 ;entity	r4	param

                    1091 ;outBuf	r5	param

                    1092 

                    1093 	.section ".bss","awb"

                    1094 .L1340:

                    1095 	.data

                    1096 	.text

                    1097 

                    1098 ;351: }


                    1099 	.align	4

                    1100 

                    1101 	.data

                    1102 	.ghsnote version,6

                    1103 	.ghsnote tools,3

                    1104 	.ghsnote options,0

                    1105 	.text

                    1106 	.align	4

