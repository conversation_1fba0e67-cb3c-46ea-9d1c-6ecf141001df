                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=BusError.c -o gh_c9c1.o -list=BusError.lst C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
Source File: BusError.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile BusError.c -o

                      11 ;		BusError.o

                      12 ;Source File:   BusError.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:55 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "BusError.h"


                      23 ;2: 


                      24 ;3: #include "timers.h"


                      25 ;4: #include "netTools.h"


                      26 ;5: #include <debug.h>


                      27 ;6: 


                      28 ;7: // Выдержка времени в миллисекундах для выдачи ошибки шины


                      29 ;8: #define BUS_ERROR_TIMEOUT 5000


                      30 ;9: 


                      31 ;10: // Выдержка времени для признания ошибки шины


                      32 ;11: static volatile uint32_t _busErrorTimeOutCounter = 0;


                      33 ;12: 


                      34 ;13: static void busCheckCallBack(void)


                      35 	.text

                      36 	.align	4

                      37 busCheckCallBack:

00000000 e92d4010     38 	stmfd	[sp]!,{r4,lr}

00000004 e59f4060*    39 	ldr	r4,.L79

                      40 ;14: {


                      41 

                      42 ;15: 	if(NetTools_busOK())


                      43 

00000008 eb000000*    44 	bl	NetTools_busOK

0000000c e3500000     45 	cmp	r0,0

                      46 ;16: 	{


                      47 

                      48 ;17: 		if(_busErrorTimeOutCounter)


                      49 

                      50 ;18: 		{



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
                      51 

                      52 ;19: 			TRACE("Bus OK");


                      53 ;20: 		}


                      54 ;21: 


                      55 ;22: 		_busErrorTimeOutCounter = 0;


                      56 

00000010 13a00000     57 	movne	r0,0

00000014 15840000     58 	strne	r0,[r4]

00000018 1a000007     59 	bne	.L2

0000001c e5941000     60 	ldr	r1,[r4]

                      61 ;23: 	}


                      62 ;24: 	else if(_busErrorTimeOutCounter < BUS_ERROR_TIMEOUT)


                      63 

00000020 e3a00d4c     64 	mov	r0,19<<8

00000024 e2800088     65 	add	r0,r0,136

00000028 e1510000     66 	cmp	r1,r0

                      67 ;25: 	{


                      68 

                      69 ;26: 		_busErrorTimeOutCounter++;


                      70 

0000002c 35940000     71 	ldrlo	r0,[r4]

00000030 32800001     72 	addlo	r0,r0,1

00000034 35840000     73 	strlo	r0,[r4]

                      74 ;27: 


                      75 ;28: 		if(_busErrorTimeOutCounter == BUS_ERROR_TIMEOUT)


                      76 

00000038 35940000     77 	ldrlo	r0,[r4]

                      78 .L2:

                      79 ;29: 		{


                      80 

0000003c e8bd4010     81 	ldmfd	[sp]!,{r4,lr}

00000040 e12fff1e*    82 	ret	

                      83 	.endf	busCheckCallBack

                      84 	.align	4

                      85 

                      86 	.section ".bss","awb"

                      87 .L62:

                      88 	.data

                      89 .L65:

00000000 00000000     90 _busErrorTimeOutCounter:	.data.b	0,0,0,0

                      91 	.type	_busErrorTimeOutCounter,$object

                      92 	.size	_busErrorTimeOutCounter,4

                      93 	.text

                      94 

                      95 ;30: 			TRACE("Bus error set");


                      96 ;31: 		}


                      97 ;32: 	}


                      98 ;33: }


                      99 

                     100 ;34: 


                     101 ;35: void BusError_init(void)


                     102 	.align	4

                     103 	.align	4

                     104 BusError_init::

00000044 e59f0024*   105 	ldr	r0,.L117

                     106 ;36: {


                     107 

                     108 ;37: 	Timers_setNetBusChek1msCallback(busCheckCallBack);


                     109 

00000048 ea000000*   110 	b	Timers_setNetBusChek1msCallback

                     111 	.endf	BusError_init


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c9c1.s
                     112 	.align	4

                     113 

                     114 	.section ".bss","awb"

                     115 .L110:

                     116 	.data

                     117 	.text

                     118 

                     119 ;38: }


                     120 

                     121 ;39: 


                     122 ;40: bool BusError_check(void)


                     123 	.align	4

                     124 	.align	4

                     125 BusError_check::

0000004c e59fc018*   126 	ldr	r12,.L79

                     127 ;41: {


                     128 

                     129 ;42: 	bool result = _busErrorTimeOutCounter < BUS_ERROR_TIMEOUT;


                     130 

00000050 e3a01d4c    131 	mov	r1,19<<8

00000054 e59c0000    132 	ldr	r0,[r12]

00000058 e2811088    133 	add	r1,r1,136

0000005c e1500001    134 	cmp	r0,r1

00000060 33a00001    135 	movlo	r0,1

00000064 23a00000    136 	movhs	r0,0

                     137 ;43: 


                     138 ;44: 	return result;


                     139 

00000068 e12fff1e*   140 	ret	

                     141 	.endf	BusError_check

                     142 	.align	4

                     143 

                     144 	.data

                     145 	.text

                     146 

                     147 ;45: }


                     148 	.align	4

                     149 .L79:

0000006c 00000000*   150 	.data.w	.L65

                     151 	.type	.L79,$object

                     152 	.size	.L79,4

                     153 

                     154 .L117:

00000070 00000000*   155 	.data.w	busCheckCallBack

                     156 	.type	.L117,$object

                     157 	.size	.L117,4

                     158 

                     159 	.align	4

                     160 ;_busErrorTimeOutCounter	.L65	static

                     161 

                     162 	.data

                     163 	.ghsnote version,6

                     164 	.ghsnote tools,3

                     165 	.ghsnote options,0

                     166 	.text

                     167 	.align	4

                     168 	.data

                     169 	.align	4

                     170 	.text

