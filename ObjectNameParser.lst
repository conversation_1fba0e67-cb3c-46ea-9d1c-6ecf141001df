                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=ObjectNameParser.c -o gh_a481.o -list=ObjectNameParser.lst C:\Users\<USER>\AppData\Local\Temp\gh_a481.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
Source File: ObjectNameParser.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		ObjectNameParser.c -o ObjectNameParser.o

                      12 ;Source File:   ObjectNameParser.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:52 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "ObjectNameParser.h"


                      23 ;2: 


                      24 ;3: 


                      25 ;4: #include "iedTree/iedTree.h"


                      26 ;5: #include "iedTree/iedNoEntity.h"


                      27 ;6: 


                      28 ;7: #include "BaseAsnTypes.h"


                      29 ;8: 


                      30 ;9: #include <debug.h>


                      31 ;10: 


                      32 ;11: // Получает имя из BER и находит подобъект объекта entity.


                      33 ;12: // Смещение bv должно указывать на BER со строкой имени.


                      34 ;13: // Если объект не найден, возвращает NoEntity.


                      35 ;14: // При прочих ошибках возвращает NULL


                      36 ;15: static IEDEntity parseName(BufferView* bv, uint8_t tag, IEDEntity entity)


                      37 ;16: {


                      38 ;17:     StringView name;


                      39 ;18:     if(!BufferView_decodeStringViewTL(bv, tag, &name))


                      40 ;19:     {


                      41 ;20:         ERROR_REPORT("Error parsing alternate object name");


                      42 ;21:         return NULL;


                      43 ;22:     }


                      44 ;23:     entity = IEDEntity_getChildByName(entity, &name);


                      45 ;24:     if(entity == NULL)


                      46 ;25:     {


                      47 ;26:         // Элемент не найден в дереве. Возвращаем NoEntity


                      48 ;27:         return IEDNoEntity_get();


                      49 ;28:     }


                      50 ;29:     return entity;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                      51 ;30: }


                      52 ;31:     


                      53 ;32: 


                      54 ;33: // Смещение bv должно указывать на первый байт (0xA0).


                      55 ;34: // Буфер должен заканчиваться с последний байтом имени.


                      56 ;35: // При ошибке разбора возвращает NULL


                      57 ;36: // Если объект с указанным именем не найден, возвращает NoEntity


                      58 ;37: static IEDEntity parseAlternateSpec(BufferView* bv, IEDEntity entity)


                      59 

                      60 ;86:         }        


                      61 ;87:     }


                      62 ;88:     


                      63 ;89: }


                      64 

                      65 	.text

                      66 	.align	4

                      67 parseName:

00000000 e92d4010     68 	stmfd	[sp]!,{r4,lr}

00000004 e24dd008     69 	sub	sp,sp,8

00000008 e1a04002     70 	mov	r4,r2

0000000c e1a0200d     71 	mov	r2,sp

00000010 eb000000*    72 	bl	BufferView_decodeStringViewTL

00000014 e3500000     73 	cmp	r0,0

00000018 0a000004     74 	beq	.L128

0000001c e1a0100d     75 	mov	r1,sp

00000020 e1a00004     76 	mov	r0,r4

00000024 eb000000*    77 	bl	IEDEntity_getChildByName

00000028 e1b04000     78 	movs	r4,r0

0000002c 0b000000*    79 	bleq	IEDNoEntity_get

                      80 .L128:

00000030 e28dd008     81 	add	sp,sp,8

00000034 e8bd4010     82 	ldmfd	[sp]!,{r4,lr}

00000038 e12fff1e*    83 	ret	

                      84 	.endf	parseName

                      85 	.align	4

                      86 ;name	[sp]	local

                      87 

                      88 ;bv	none	param

                      89 ;tag	none	param

                      90 ;entity	r4	param

                      91 

                      92 	.section ".bss","awb"

                      93 .L195:

                      94 	.data

                      95 	.text

                      96 

                      97 

                      98 ;90: 


                      99 ;91: // Парсит элемент списка спецификации имён объектов.


                     100 ;92: // bv должен указывать на элемент списка, т.е. на ASN_SEQUENCE (0x30).


                     101 ;93: IEDEntity ObjectNameParser_parse(BufferView* bv)


                     102 	.align	4

                     103 	.align	4

                     104 ObjectNameParser_parse::

0000003c e92d4070    105 	stmfd	[sp]!,{r4-r6,lr}

                     106 ;94: {


                     107 

                     108 ;95:     uint8_t tag;


                     109 ;96:     size_t len;


                     110 ;97:     BufferView nameSpecification;


                     111 ;98:     StringView ldName;



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     112 ;99:     StringView objectName;


                     113 ;100:     IEDEntity entity;


                     114 ;101: 


                     115 ;102:     if(!BufferView_decodeTL(bv, &tag, &len, NULL) || tag != ASN_SEQUENCE)


                     116 

00000040 e24dd028    117 	sub	sp,sp,40

00000044 e28d2004    118 	add	r2,sp,4

00000048 e28d1002    119 	add	r1,sp,2

0000004c e1a04000    120 	mov	r4,r0

00000050 e3a03000    121 	mov	r3,0

00000054 eb000000*   122 	bl	BufferView_decodeTL

00000058 e3500000    123 	cmp	r0,0

0000005c 0a000045    124 	beq	.L245

00000060 e5dd0002    125 	ldrb	r0,[sp,2]

00000064 e3500030    126 	cmp	r0,48

00000068 1a000042    127 	bne	.L245

                     128 ;103:     {    


                     129 

                     130 ;104:         ERROR_REPORT("Error parsing object name");


                     131 ;105:         return NULL;


                     132 

                     133 ;106:     }


                     134 ;107: 


                     135 ;108:     BufferView_init(&nameSpecification, BufferView_currentPtr(bv), len, 0);


                     136 

0000006c e59d2004    137 	ldr	r2,[sp,4]

00000070 e894000a    138 	ldmfd	[r4],{r1,r3}

00000074 e28d001c    139 	add	r0,sp,28

00000078 e0831001    140 	add	r1,r3,r1

0000007c e3a03000    141 	mov	r3,0

00000080 eb000000*   142 	bl	BufferView_init

                     143 ;109: 


                     144 ;110:     // Продвигаемся вперёд на всю длину элемента списка чтобы следующий


                     145 ;111:     // вызов ObjectNameParser_parse попал на следующий элемент списка.


                     146 ;112:     // Дальше будем работать через nameSpecification


                     147 ;113:     if(!BufferView_advance(bv, len))


                     148 

00000084 e59d1004    149 	ldr	r1,[sp,4]

00000088 e1a00004    150 	mov	r0,r4

0000008c eb000000*   151 	bl	BufferView_advance

00000090 e3500000    152 	cmp	r0,0

00000094 0a000037    153 	beq	.L245

                     154 ;114:     {


                     155 

                     156 ;115:         ERROR_REPORT("Error 1 parsing object name");


                     157 ;116:         return NULL;


                     158 

                     159 ;117:     }


                     160 ;118: 


                     161 ;119:     if(!BufferView_decodeTL(&nameSpecification, &tag, &len, NULL) 


                     162 

00000098 e28d2004    163 	add	r2,sp,4

0000009c e28d1002    164 	add	r1,sp,2

000000a0 e28d001c    165 	add	r0,sp,28

000000a4 e3a03000    166 	mov	r3,0

000000a8 eb000000*   167 	bl	BufferView_decodeTL

000000ac e3500000    168 	cmp	r0,0

000000b0 0a000030    169 	beq	.L245

000000b4 e5dd0002    170 	ldrb	r0,[sp,2]

000000b8 e35000a0    171 	cmp	r0,160

000000bc 1a00002d    172 	bne	.L245


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     173 ;120:         || tag != 0xA0)


                     174 ;121:     {


                     175 

                     176 ;122:         ERROR_REPORT("Error 2 parsing object name");


                     177 ;123:         return NULL;


                     178 

                     179 ;124:     }


                     180 ;125:     if(!BufferView_decodeTL(&nameSpecification, &tag, &len, NULL) 


                     181 

000000c0 e28d2004    182 	add	r2,sp,4

000000c4 e28d1002    183 	add	r1,sp,2

000000c8 e28d001c    184 	add	r0,sp,28

000000cc e3a03000    185 	mov	r3,0

000000d0 eb000000*   186 	bl	BufferView_decodeTL

000000d4 e3500000    187 	cmp	r0,0

000000d8 0a000026    188 	beq	.L245

000000dc e5dd0002    189 	ldrb	r0,[sp,2]

000000e0 e35000a1    190 	cmp	r0,161

000000e4 1a000023    191 	bne	.L245

                     192 ;126:         || tag != 0xA1)


                     193 ;127:     {


                     194 

                     195 ;128:         ERROR_REPORT("Error 3 parsing object name");


                     196 ;129:         return NULL;


                     197 

                     198 ;130:     }


                     199 ;131: 


                     200 ;132:     // Здесь должно быть две строки - LD и имя объекта


                     201 ;133:     if(!BufferView_decodeStringViewTL(&nameSpecification, ASN_VISIBLE_STRING,


                     202 

000000e8 e28d2014    203 	add	r2,sp,20

000000ec e28d001c    204 	add	r0,sp,28

000000f0 e3a0101a    205 	mov	r1,26

000000f4 eb000000*   206 	bl	BufferView_decodeStringViewTL

000000f8 e3500000    207 	cmp	r0,0

000000fc 0a00001d    208 	beq	.L245

                     209 ;134:         &ldName))


                     210 ;135:     {


                     211 

                     212 ;136:         ERROR_REPORT("Error parsing object LD name string");


                     213 ;137:         return NULL;


                     214 

                     215 ;138:     }


                     216 ;139:     if(!BufferView_decodeStringViewTL(&nameSpecification, ASN_VISIBLE_STRING,


                     217 

00000100 e28d200c    218 	add	r2,sp,12

00000104 e28d001c    219 	add	r0,sp,28

00000108 e3a0101a    220 	mov	r1,26

0000010c eb000000*   221 	bl	BufferView_decodeStringViewTL

00000110 e3500000    222 	cmp	r0,0

00000114 0a000017    223 	beq	.L245

                     224 ;140:         &objectName))


                     225 ;141:     {


                     226 

                     227 ;142:         ERROR_REPORT("Error parsing object name string");


                     228 ;143:         return NULL;


                     229 

                     230 ;144:     }


                     231 ;145:         


                     232 ;146:     // Ищем элемент дерева полному имени


                     233 ;147:     entity = IEDTree_findDataByFullName(&ldName, &objectName);



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     234 

00000118 e28d100c    235 	add	r1,sp,12

0000011c e28d0014    236 	add	r0,sp,20

00000120 eb000000*   237 	bl	IEDTree_findDataByFullName

00000124 e1b04000    238 	movs	r4,r0

                     239 ;148:     if(entity == NULL)


                     240 

00000128 1a000001    241 	bne	.L237

                     242 ;149:     {


                     243 

                     244 ;150:         // Элемент не найден в дереве. Возвращаем NoEntity


                     245 ;151:         return IEDNoEntity_get();


                     246 

0000012c eb000000*   247 	bl	IEDNoEntity_get

00000130 ea000045    248 	b	.L213

                     249 .L237:

                     250 ;152:     }


                     251 ;153: 


                     252 ;154:     if(BufferView_endOfBuf(&nameSpecification))


                     253 

00000134 e59d1024    254 	ldr	r1,[sp,36]

00000138 e59d0020    255 	ldr	r0,[sp,32]

0000013c e1500001    256 	cmp	r0,r1

                     257 ;155:     {


                     258 

                     259 ;156:         // Это был последний элемент в спецификации имени


                     260 ;157:         return entity;


                     261 

00000140 01a00004    262 	moveq	r0,r4

00000144 0a000040    263 	beq	.L213

                     264 ;158:     }


                     265 ;159: 


                     266 ;160:     // Alternate access


                     267 ;161:     if(!BufferView_decodeTL(&nameSpecification, &tag, &len, NULL) 


                     268 

00000148 e28d2004    269 	add	r2,sp,4

0000014c e28d1002    270 	add	r1,sp,2

00000150 e28d001c    271 	add	r0,sp,28

00000154 e3a03000    272 	mov	r3,0

00000158 eb000000*   273 	bl	BufferView_decodeTL

0000015c e3500000    274 	cmp	r0,0

00000160 0a000004    275 	beq	.L245

00000164 e5dd0002    276 	ldrb	r0,[sp,2]

00000168 e35000a5    277 	cmp	r0,165

                     278 ;166:     }


                     279 ;167: 


                     280 ;168:     return parseAlternateSpec(&nameSpecification, entity);


                     281 

0000016c 028d501c    282 	addeq	r5,sp,28

00000170 028d6003    283 	addeq	r6,sp,3

00000174 0a000001    284 	beq	.L251

                     285 .L245:

                     286 ;162:         || tag != 0xA5)


                     287 ;163:     {


                     288 

                     289 ;164:         ERROR_REPORT("Error parsing alternate access");


                     290 ;165:         return NULL;


                     291 

00000178 e3a00000    292 	mov	r0,0

0000017c ea000032    293 	b	.L213

                     294 .L251:


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     295 ;38: {


                     296 

                     297 ;39:     uint8_t tag;


                     298 ;40:     size_t len;


                     299 ;41:     


                     300 ;42:     while (true)


                     301 

                     302 ;43:     {


                     303 

                     304 ;44:         if(!BufferView_peekTag(bv, &tag))


                     305 

00000180 e1a01006    306 	mov	r1,r6

00000184 e1a00005    307 	mov	r0,r5

00000188 eb000000*   308 	bl	BufferView_peekTag

0000018c e3500000    309 	cmp	r0,0

00000190 0a00002c    310 	beq	.L270

                     311 ;45:         {


                     312 

                     313 ;46:             ERROR_REPORT("Error 7 parsing alternate object name");


                     314 ;47:             return NULL;


                     315 

                     316 ;48:         }


                     317 ;49:         


                     318 ;50:         if(tag == 0x81)


                     319 

00000194 e5dd1003    320 	ldrb	r1,[sp,3]

00000198 e1a00005    321 	mov	r0,r5

0000019c e3510081    322 	cmp	r1,129

000001a0 1a000002    323 	bne	.L255

                     324 ;51:         {


                     325 

                     326 ;52:             // Alternate access из одного имени


                     327 ;53:             return parseName(bv, tag, entity);            


                     328 

000001a4 e1a02004    329 	mov	r2,r4

000001a8 ebffff94*   330 	bl	parseName

000001ac ea000026    331 	b	.L213

                     332 .L255:

                     333 ;54:         }


                     334 ;55: 


                     335 ;56: 


                     336 ;57:         if(!BufferView_decodeTL(bv, &tag, &len, NULL))


                     337 

000001b0 e28d2008    338 	add	r2,sp,8

000001b4 e1a01006    339 	mov	r1,r6

000001b8 e3a03000    340 	mov	r3,0

000001bc eb000000*   341 	bl	BufferView_decodeTL

000001c0 e3500000    342 	cmp	r0,0

000001c4 0a00001f    343 	beq	.L270

                     344 ;58:         {


                     345 

                     346 ;59:             ERROR_REPORT("Error 2 parsing alternate object name");


                     347 ;60:             return NULL;


                     348 

                     349 ;61:         };


                     350 ;62:         if(tag != 0xA0)


                     351 

000001c8 e5dd1003    352 	ldrb	r1,[sp,3]

000001cc e35100a0    353 	cmp	r1,160

000001d0 1a00001c    354 	bne	.L270

                     355 ;63:         {



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     356 

                     357 ;64:             ERROR_REPORT("Error 3 parsing alternate object name");


                     358 ;65:             return NULL;


                     359 

                     360 ;66:         }


                     361 ;67:         if(!BufferView_peekTag(bv, &tag) || (tag != 0x80 && tag != 0x81))


                     362 

000001d4 e1a01006    363 	mov	r1,r6

000001d8 e1a00005    364 	mov	r0,r5

000001dc eb000000*   365 	bl	BufferView_peekTag

000001e0 e3500000    366 	cmp	r0,0

000001e4 0a000017    367 	beq	.L270

000001e8 e5dd1003    368 	ldrb	r1,[sp,3]

000001ec e3510080    369 	cmp	r1,128

000001f0 13510081    370 	cmpne	r1,129

000001f4 1a000013    371 	bne	.L270

                     372 ;68:         {


                     373 

                     374 ;69:             ERROR_REPORT("Error 4 parsing alternate object name");


                     375 ;70:             return NULL;


                     376 

                     377 ;71:         }


                     378 ;72:         entity = parseName(bv, tag, entity);


                     379 

000001f8 e1a02004    380 	mov	r2,r4

000001fc e1a00005    381 	mov	r0,r5

00000200 ebffff7e*   382 	bl	parseName

00000204 e1b04000    383 	movs	r4,r0

                     384 ;73:         if(entity == NULL)


                     385 

00000208 0a00000e    386 	beq	.L270

                     387 ;74:         {


                     388 

                     389 ;75:             return NULL;


                     390 

                     391 ;76:         }


                     392 ;77:         if(BufferView_endOfBuf(bv))


                     393 

0000020c e59d2024    394 	ldr	r2,[sp,36]

00000210 e59d1020    395 	ldr	r1,[sp,32]

00000214 e1510002    396 	cmp	r1,r2

                     397 ;78:         {


                     398 

                     399 ;79:             return entity;


                     400 

00000218 01a00004    401 	moveq	r0,r4

0000021c 0a00000a    402 	beq	.L213

                     403 ;80:         }


                     404 ;81:         


                     405 ;82:         if(!BufferView_decodeTL(bv, &tag, &len, NULL) || tag != ASN_SEQUENCE)


                     406 

00000220 e28d2008    407 	add	r2,sp,8

00000224 e1a01006    408 	mov	r1,r6

00000228 e1a00005    409 	mov	r0,r5

0000022c e3a03000    410 	mov	r3,0

00000230 eb000000*   411 	bl	BufferView_decodeTL

00000234 e3500000    412 	cmp	r0,0

00000238 0a000002    413 	beq	.L270

0000023c e5dd1003    414 	ldrb	r1,[sp,3]

00000240 e3510030    415 	cmp	r1,48

00000244 0affffcd    416 	beq	.L251


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     417 .L270:

                     418 ;83:         {


                     419 

                     420 ;84:             ERROR_REPORT("Error 6 parsing alternate object name");


                     421 ;85:             return NULL;


                     422 

00000248 e3a00000    423 	mov	r0,0

                     424 .L213:

0000024c e28dd028    425 	add	sp,sp,40

00000250 e8bd8070    426 	ldmfd	[sp]!,{r4-r6,pc}

                     427 	.endf	ObjectNameParser_parse

                     428 	.align	4

                     429 ;tag	[sp,2]	local

                     430 ;len	[sp,4]	local

                     431 ;nameSpecification	[sp,28]	local

                     432 ;ldName	[sp,20]	local

                     433 ;objectName	[sp,12]	local

                     434 ;entity	r4	local

                     435 ;entity	r4	local

                     436 ;tag	[sp,3]	local

                     437 ;len	[sp,8]	local

                     438 

                     439 ;bv	r4	param

                     440 

                     441 	.section ".bss","awb"

                     442 .L594:

                     443 	.data

                     444 	.text

                     445 

                     446 ;169: }


                     447 	.align	4

                     448 

                     449 	.data

                     450 	.ghsnote version,6

                     451 	.ghsnote tools,3

                     452 	.ghsnote options,0

                     453 	.text

                     454 	.align	4

