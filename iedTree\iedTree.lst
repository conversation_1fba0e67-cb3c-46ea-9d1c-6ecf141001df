                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7281.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedTree.c -o iedTree\gh_7281.o -list=iedTree/iedTree.lst C:\Users\<USER>\AppData\Local\Temp\gh_7281.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_7281.s
Source File: iedTree.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		iedTree/iedTree.c -o iedTree/iedTree.o

                      12 ;Source File:   iedTree/iedTree.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:48 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "iedTree.h"


                      23 ;2: 


                      24 ;3: #include "iedEntity.h"


                      25 ;4: #include "iedObjects.h"


                      26 ;5: #include "iedNoEntity.h"


                      27 ;6: 


                      28 ;7: #include <platform_critical_section.h>


                      29 ;8: 


                      30 ;9: #include <stddef.h>


                      31 ;10: #include <debug.h>


                      32 ;11: 


                      33 ;12: static IEDEntity iedTreeRoot = NULL;


                      34 ;13: 


                      35 ;14: static CriticalSection iedTreeCS;


                      36 ;15: 


                      37 ;16: // Список элементов, которые нужно проверать на изменения


                      38 ;17: // при получении нового DataSlice


                      39 ;18: // Xоть следующие переменные используются локально, здесь нельзя писать static, т.к.


                      40 ;19: // GHS генерирует неверный код в функции IEDTree_addToCmpList


                      41 ;20: IEDEntity dataSliceUpdateList = NULL;


                      42 ;21: IEDEntity* dataSliceUpdateListTail = &dataSliceUpdateList;


                      43 ;22: 


                      44 ;23: bool IEDTree_init(uint8_t *iedModel, size_t modelSize)


                      45 	.text

                      46 	.align	4

                      47 IEDTree_init::

00000000 e92d4030     48 	stmfd	[sp]!,{r4-r5,lr}

                      49 ;24: {


                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7281.s
                      51 ;25:     IEDEntity root;


                      52 ;26:     BufferView ber;


                      53 ;27: 


                      54 ;28: 


                      55 ;29:     CriticalSection_Init(&iedTreeCS);


                      56 

00000004 e24dd010     57 	sub	sp,sp,16

00000008 e1a04000     58 	mov	r4,r0

0000000c e59f018c*    59 	ldr	r0,.L74

00000010 e1a05001     60 	mov	r5,r1

00000014 eb000000*    61 	bl	CriticalSection_Init

                      62 ;30: 


                      63 ;31:     BufferView_init(&ber, iedModel, modelSize, 0);


                      64 

00000018 e1a02005     65 	mov	r2,r5

0000001c e1a01004     66 	mov	r1,r4

00000020 e28d0004     67 	add	r0,sp,4

00000024 e3a03000     68 	mov	r3,0

00000028 eb000000*    69 	bl	BufferView_init

                      70 ;32: 


                      71 ;33:     if(!IEDEntity_createFromBER(&root, &ber , NULL))


                      72 

0000002c e28d1004     73 	add	r1,sp,4

00000030 e1a0000d     74 	mov	r0,sp

00000034 e3a02000     75 	mov	r2,0

00000038 eb000000*    76 	bl	IEDEntity_createFromBER

0000003c e3500000     77 	cmp	r0,0

00000040 0a000004     78 	beq	.L8

                      79 ;34:     {


                      80 

                      81 ;35:         return false;


                      82 

                      83 ;36:     }


                      84 ;37:     iedTreeRoot = root;


                      85 

00000044 e59f1158*    86 	ldr	r1,.L75

00000048 e59d0000     87 	ldr	r0,[sp]

0000004c e5810000     88 	str	r0,[r1]

                      89 ;38: 


                      90 ;39:     //Дополнительная инициализация для элементов,


                      91 ;40:     //требующих готового дерева


                      92 ;41:     if(!IEDEntity_postCreate(root))


                      93 

00000050 eb000000*    94 	bl	IEDEntity_postCreate

00000054 e3500000     95 	cmp	r0,0

                      96 .L8:

                      97 ;42:     {


                      98 

                      99 ;43:         return false;


                     100 

00000058 03a00000    101 	moveq	r0,0

0000005c 0a000001    102 	beq	.L2

                     103 .L7:

                     104 ;44:     }


                     105 ;45: 


                     106 ;46:     IEDNoEntity_init();


                     107 

00000060 eb000000*   108 	bl	IEDNoEntity_init

                     109 ;47: 


                     110 ;48:     return true;


                     111 


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7281.s
00000064 e3a00001    112 	mov	r0,1

                     113 .L2:

00000068 e28dd010    114 	add	sp,sp,16

0000006c e8bd8030    115 	ldmfd	[sp]!,{r4-r5,pc}

                     116 	.endf	IEDTree_init

                     117 	.align	4

                     118 ;root	[sp]	local

                     119 ;ber	[sp,4]	local

                     120 

                     121 ;iedModel	r4	param

                     122 ;modelSize	r5	param

                     123 

                     124 	.section ".bss","awb"

                     125 .L60:

                     126 	.data

                     127 .L61:

00000000 00000000    128 iedTreeRoot:	.data.b	0,0,0,0

                     129 	.type	iedTreeRoot,$object

                     130 	.size	iedTreeRoot,4

                     131 	.section ".bss","awb"

00000000 00         132 iedTreeCS:	.space	1

                     133 	.data

                     134 	.text

                     135 

                     136 ;49: }


                     137 

                     138 ;50: 


                     139 ;51: void IEDTree_lock(void)


                     140 	.align	4

                     141 	.align	4

                     142 IEDTree_lock::

                     143 ;52: {


                     144 

                     145 ;53:     CriticalSection_Lock(&iedTreeCS);


                     146 

00000070 e59f0128*   147 	ldr	r0,.L74

00000074 ea000000*   148 	b	CriticalSection_Lock

                     149 	.endf	IEDTree_lock

                     150 	.align	4

                     151 

                     152 	.data

                     153 	.text

                     154 

                     155 ;54: }


                     156 

                     157 ;55: 


                     158 ;56: void IEDTree_unlock(void)


                     159 	.align	4

                     160 	.align	4

                     161 IEDTree_unlock::

                     162 ;57: {


                     163 

                     164 ;58:     CriticalSection_Unlock(&iedTreeCS);


                     165 

00000078 e59f0120*   166 	ldr	r0,.L74

0000007c ea000000*   167 	b	CriticalSection_Unlock

                     168 	.endf	IEDTree_unlock

                     169 	.align	4

                     170 

                     171 	.data

                     172 	.text


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7281.s
                     173 

                     174 ;59: }


                     175 

                     176 ;60: 


                     177 ;61: IEDEntity IEDTree_findDataByFullName(StringView* ldName,


                     178 	.align	4

                     179 	.align	4

                     180 IEDTree_findDataByFullName::

00000080 e92d4010    181 	stmfd	[sp]!,{r4,lr}

                     182 ;62:                                      StringView* dataName)


                     183 ;63: {


                     184 

                     185 ;64:     IEDEntity dataSection;


                     186 ;65:     IEDEntity ld = IEDEntity_getChildByName(iedTreeRoot, ldName);


                     187 

00000084 e1a04001    188 	mov	r4,r1

00000088 e59f2114*   189 	ldr	r2,.L75

0000008c e1a01000    190 	mov	r1,r0

00000090 e5920000    191 	ldr	r0,[r2]

00000094 eb000000*   192 	bl	IEDEntity_getChildByName

                     193 ;66:     if(ld == NULL)


                     194 

00000098 e3500000    195 	cmp	r0,0

0000009c 0a000004    196 	beq	.L138

                     197 ;67:     {


                     198 

                     199 ;68:         return NULL;


                     200 

                     201 ;69:     }


                     202 ;70:     dataSection = IEDLD_getDataSection(ld);


                     203 

000000a0 eb000000*   204 	bl	IEDLD_getDataSection

                     205 ;71:     if(dataSection == NULL)


                     206 

000000a4 e3500000    207 	cmp	r0,0

                     208 ;74:     }


                     209 ;75:     return IEDEntity_getChildByFullName(dataSection, dataName);


                     210 

000000a8 11a01004    211 	movne	r1,r4

000000ac 18bd4010    212 	ldmnefd	[sp]!,{r4,lr}

000000b0 1a000000*   213 	bne	IEDEntity_getChildByFullName

                     214 .L138:

                     215 ;72:     {


                     216 

                     217 ;73:         return NULL;


                     218 

000000b4 e3a00000    219 	mov	r0,0

000000b8 e8bd8010    220 	ldmfd	[sp]!,{r4,pc}

                     221 	.endf	IEDTree_findDataByFullName

                     222 	.align	4

                     223 ;dataSection	r2	local

                     224 ;ld	r2	local

                     225 

                     226 ;ldName	r0	param

                     227 ;dataName	r4	param

                     228 

                     229 	.section ".bss","awb"

                     230 .L188:

                     231 	.data

                     232 	.text

                     233 


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7281.s
                     234 ;76: }


                     235 

                     236 ;77: 


                     237 ;78: IEDEntity IEDTree_findDataSetByFullName(StringView* ldName,


                     238 	.align	4

                     239 	.align	4

                     240 IEDTree_findDataSetByFullName::

000000bc e92d4010    241 	stmfd	[sp]!,{r4,lr}

                     242 ;79:                                      StringView* dataSetName)


                     243 ;80: {


                     244 

                     245 ;81:     IEDEntity dataSetSection;


                     246 ;82:     IEDEntity ld = IEDEntity_getChildByName(iedTreeRoot, ldName);


                     247 

000000c0 e1a04001    248 	mov	r4,r1

000000c4 e59f20d8*   249 	ldr	r2,.L75

000000c8 e1a01000    250 	mov	r1,r0

000000cc e5920000    251 	ldr	r0,[r2]

000000d0 eb000000*   252 	bl	IEDEntity_getChildByName

                     253 ;83:     if(ld == NULL)


                     254 

000000d4 e3500000    255 	cmp	r0,0

000000d8 0a000004    256 	beq	.L211

                     257 ;84:     {


                     258 

                     259 ;85:         return NULL;


                     260 

                     261 ;86:     }


                     262 ;87:     dataSetSection = IEDLD_getDataSetSection(ld);


                     263 

000000dc eb000000*   264 	bl	IEDLD_getDataSetSection

                     265 ;88:     if(dataSetSection == NULL)


                     266 

000000e0 e3500000    267 	cmp	r0,0

                     268 ;91:     }


                     269 ;92:     return IEDEntity_getChildByFullName(dataSetSection, dataSetName);


                     270 

000000e4 11a01004    271 	movne	r1,r4

000000e8 18bd4010    272 	ldmnefd	[sp]!,{r4,lr}

000000ec 1a000000*   273 	bne	IEDEntity_getChildByFullName

                     274 .L211:

                     275 ;89:     {


                     276 

                     277 ;90:         return NULL;


                     278 

000000f0 e3a00000    279 	mov	r0,0

000000f4 e8bd8010    280 	ldmfd	[sp]!,{r4,pc}

                     281 	.endf	IEDTree_findDataSetByFullName

                     282 	.align	4

                     283 ;dataSetSection	r2	local

                     284 ;ld	r2	local

                     285 

                     286 ;ldName	r0	param

                     287 ;dataSetName	r4	param

                     288 

                     289 	.section ".bss","awb"

                     290 .L268:

                     291 	.data

                     292 	.text

                     293 

                     294 ;93: }



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7281.s
                     295 

                     296 ;94: 


                     297 ;95: IEDEntity IEDTree_findDataSetBySingleName(StringView* dataSetName)


                     298 	.align	4

                     299 	.align	4

                     300 IEDTree_findDataSetBySingleName::

000000f8 e92d4000    301 	stmfd	[sp]!,{lr}

000000fc e24dd010    302 	sub	sp,sp,16

00000100 e1a0300d    303 	mov	r3,sp

00000104 e28d2008    304 	add	r2,sp,8

00000108 e3a0102f    305 	mov	r1,47

0000010c eb000000*   306 	bl	StringView_splitChar

                     307 ;96: {


                     308 

                     309 ;97:     StringView ldName;


                     310 ;98:     StringView remainingName;


                     311 ;99:     if(!StringView_splitChar(dataSetName, '/', &ldName, &remainingName))


                     312 

00000110 e3500000    313 	cmp	r0,0

                     314 ;100:     {


                     315 

                     316 ;101:         return NULL;


                     317 

                     318 ;102:     }


                     319 ;103:     return IEDTree_findDataSetByFullName(&ldName, &remainingName);


                     320 

00000114 11a0100d    321 	movne	r1,sp

00000118 128d0008    322 	addne	r0,sp,8

0000011c 1bffffe6*   323 	blne	IEDTree_findDataSetByFullName

00000120 e28dd010    324 	add	sp,sp,16

00000124 e8bd8000    325 	ldmfd	[sp]!,{pc}

                     326 	.endf	IEDTree_findDataSetBySingleName

                     327 	.align	4

                     328 ;ldName	[sp,8]	local

                     329 ;remainingName	[sp]	local

                     330 

                     331 ;dataSetName	none	param

                     332 

                     333 	.section ".bss","awb"

                     334 .L341:

                     335 	.data

                     336 	.text

                     337 

                     338 ;104: }


                     339 

                     340 ;105: 


                     341 ;106: MmsDataAccessError IEDTree_write(StringView* ldName,


                     342 	.align	4

                     343 	.align	4

                     344 IEDTree_write::

00000128 e92d4030    345 	stmfd	[sp]!,{r4-r5,lr}

0000012c e1a04002    346 	mov	r4,r2

00000130 e1a05003    347 	mov	r5,r3

                     348 ;107:                                  StringView* itemName, IsoConnection* isoConn,


                     349 ;108:                                    BufferView* value)


                     350 ;109: {


                     351 

                     352 ;110:     IEDEntity entity = IEDTree_findDataByFullName(ldName, itemName);


                     353 

00000134 ebffffd1*   354 	bl	IEDTree_findDataByFullName

                     355 ;111:     if(entity == NULL)



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7281.s
                     356 

00000138 e3500000    357 	cmp	r0,0

                     358 ;115:     }


                     359 ;116:     return IEDEntity_write(entity, isoConn, value);


                     360 

0000013c 11a02005    361 	movne	r2,r5

00000140 11a01004    362 	movne	r1,r4

00000144 18bd4030    363 	ldmnefd	[sp]!,{r4-r5,lr}

00000148 1a000000*   364 	bne	IEDEntity_write

                     365 ;112:     {


                     366 

                     367 ;113:         ERROR_REPORT("Item is not found");


                     368 ;114:         return DATA_ACCESS_ERROR_OBJECT_NONE_EXISTENT;


                     369 

0000014c e3a0000a    370 	mov	r0,10

00000150 e8bd8030    371 	ldmfd	[sp]!,{r4-r5,pc}

                     372 	.endf	IEDTree_write

                     373 	.align	4

                     374 ;entity	r1	local

                     375 

                     376 ;ldName	none	param

                     377 ;itemName	none	param

                     378 ;isoConn	r4	param

                     379 ;value	r5	param

                     380 

                     381 	.section ".bss","awb"

                     382 .L390:

                     383 	.data

                     384 	.text

                     385 

                     386 ;117: }


                     387 

                     388 ;118: 


                     389 ;119: void IEDTree_updateFromDataSlice(void)


                     390 	.align	4

                     391 	.align	4

                     392 IEDTree_updateFromDataSlice::

00000154 e92d4010    393 	stmfd	[sp]!,{r4,lr}

                     394 ;120: {


                     395 

                     396 ;121:     IEDEntity currEntity = dataSliceUpdateList;


                     397 

00000158 e59f0048*   398 	ldr	r0,.L448

0000015c e5904000    399 	ldr	r4,[r0]

                     400 ;122: 


                     401 ;123:     while(currEntity != NULL)


                     402 

00000160 e3540000    403 	cmp	r4,0

00000164 0a000006    404 	beq	.L404

                     405 .L408:

                     406 ;124:     {


                     407 

                     408 ;125:         currEntity->updateFromDataSlice(currEntity);


                     409 

00000168 e594c068    410 	ldr	r12,[r4,104]

0000016c e1a00004    411 	mov	r0,r4

00000170 e1a0e00f    412 	mov	lr,pc

00000174 e12fff1c*   413 	bx	r12

                     414 ;126:         currEntity = currEntity->nextCompare;


                     415 

00000178 e5944010    416 	ldr	r4,[r4,16]


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7281.s
0000017c e3540000    417 	cmp	r4,0

00000180 1afffff8    418 	bne	.L408

                     419 .L404:

00000184 e8bd8010    420 	ldmfd	[sp]!,{r4,pc}

                     421 	.endf	IEDTree_updateFromDataSlice

                     422 	.align	4

                     423 ;currEntity	r4	local

                     424 

                     425 	.section ".bss","awb"

                     426 .L438:

                     427 	.data

                     428 	.text

                     429 

                     430 ;127:     }


                     431 ;128: }


                     432 

                     433 ;129: 


                     434 ;130: void IEDTree_addToCmpList(IEDEntity da)


                     435 	.align	4

                     436 	.align	4

                     437 IEDTree_addToCmpList::

                     438 ;131: {


                     439 

                     440 ;132:     *dataSliceUpdateListTail = da;


                     441 

00000188 e59f201c*   442 	ldr	r2,.L485

0000018c e5921000    443 	ldr	r1,[r2]

00000190 e5810000    444 	str	r0,[r1]

                     445 ;133:     dataSliceUpdateListTail = &da->nextCompare;


                     446 

00000194 e2800010    447 	add	r0,r0,16

00000198 e5820000    448 	str	r0,[r2]

0000019c e12fff1e*   449 	ret	

                     450 	.endf	IEDTree_addToCmpList

                     451 	.align	4

                     452 

                     453 ;da	r0	param

                     454 

                     455 	.section ".bss","awb"

                     456 .L478:

                     457 	.data

                     458 	.text

                     459 

                     460 ;134: }


                     461 	.align	4

                     462 .L74:

000001a0 00000000*   463 	.data.w	iedTreeCS

                     464 	.type	.L74,$object

                     465 	.size	.L74,4

                     466 

                     467 .L75:

000001a4 00000000*   468 	.data.w	.L61

                     469 	.type	.L75,$object

                     470 	.size	.L75,4

                     471 

                     472 .L448:

000001a8 00000000*   473 	.data.w	dataSliceUpdateList

                     474 	.type	.L448,$object

                     475 	.size	.L448,4

                     476 

                     477 .L485:


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7281.s
000001ac 00000000*   478 	.data.w	dataSliceUpdateListTail

                     479 	.type	.L485,$object

                     480 	.size	.L485,4

                     481 

                     482 	.align	4

                     483 ;iedTreeRoot	.L61	static

                     484 ;iedTreeCS	iedTreeCS	static

                     485 

                     486 	.data

                     487 .L500:

                     488 	.globl	dataSliceUpdateList

00000004 00000000    489 dataSliceUpdateList:	.data.b	0,0,0,0

                     490 	.type	dataSliceUpdateList,$object

                     491 	.size	dataSliceUpdateList,4

                     492 .L501:

                     493 	.globl	dataSliceUpdateListTail

00000008 00000000*   494 dataSliceUpdateListTail:	.data.w	.L500

                     495 	.type	dataSliceUpdateListTail,$object

                     496 	.size	dataSliceUpdateListTail,4

                     497 	.ghsnote version,6

                     498 	.ghsnote tools,3

                     499 	.ghsnote options,0

                     500 	.text

                     501 	.align	4

                     502 	.data

                     503 	.align	4

                     504 	.text

