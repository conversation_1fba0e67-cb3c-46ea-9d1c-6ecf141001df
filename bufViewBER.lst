                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=bufViewBER.c -o gh_ba81.o -list=bufViewBER.lst C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
Source File: bufViewBER.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile bufViewBER.c

                      11 ;		-o bufViewBER.o

                      12 ;Source File:   bufViewBER.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:57 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "bufViewBER.h"


                      23 ;2: #include "AsnEncoding.h"


                      24 ;3: 


                      25 ;4: bool BufferView_peekTag(BufferView* bv, uint8_t* tag)


                      26 	.text

                      27 	.align	4

                      28 BufferView_peekTag::

                      29 ;5: {


                      30 

                      31 ;6:     if (bv->pos < bv->len)


                      32 

00000000 e990000c     33 	ldmed	[r0],{r2-r3}

00000004 e1520003     34 	cmp	r2,r3

                      35 ;7:     {


                      36 

                      37 ;8:         *tag = bv->p[bv->pos];


                      38 

00000008 35900000     39 	ldrlo	r0,[r0]

0000000c 37d00002     40 	ldrlob	r0,[r0,r2]

00000010 35c10000     41 	strlob	r0,[r1]

                      42 ;9:         return TRUE;


                      43 

00000014 33a00001     44 	movlo	r0,1

                      45 ;10:     }


                      46 ;11:     else


                      47 ;12:     {


                      48 

                      49 ;13:         return FALSE;


                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
00000018 23a00000     51 	movhs	r0,0

0000001c e12fff1e*    52 	ret	

                      53 	.endf	BufferView_peekTag

                      54 	.align	4

                      55 

                      56 ;bv	r0	param

                      57 ;tag	r1	param

                      58 

                      59 	.section ".bss","awb"

                      60 .L54:

                      61 	.data

                      62 	.text

                      63 

                      64 ;14:     }


                      65 ;15: }


                      66 

                      67 ;16: 


                      68 ;17: bool BufferView_skipObject(BufferView* bv, uint8_t tag, bool strict)


                      69 	.align	4

                      70 	.align	4

                      71 BufferView_skipObject::

00000020 e92d4070     72 	stmfd	[sp]!,{r4-r6,lr}

00000024 e1a06002     73 	mov	r6,r2

00000028 e24dd008     74 	sub	sp,sp,8

0000002c e1a04001     75 	mov	r4,r1

00000030 e28d1003     76 	add	r1,sp,3

00000034 e1a05000     77 	mov	r5,r0

00000038 ebfffff0*    78 	bl	BufferView_peekTag

                      79 ;18: {


                      80 

                      81 ;19:     uint8_t objTag;


                      82 ;20:     size_t len;


                      83 ;21: 


                      84 ;22:     if (!BufferView_peekTag(bv, &objTag))


                      85 

0000003c e3500000     86 	cmp	r0,0

                      87 ;23:     {


                      88 

                      89 ;24:         return FALSE;


                      90 

00000040 0a000014     91 	beq	.L67

                      92 ;25:     }


                      93 ;26:     if (objTag != tag)


                      94 

00000044 e5dd0003     95 	ldrb	r0,[sp,3]

00000048 e1500004     96 	cmp	r0,r4

0000004c 0a000003     97 	beq	.L72

                      98 ;27:     {


                      99 

                     100 ;28:         return !strict;


                     101 

00000050 e3560000    102 	cmp	r6,0

00000054 03a00001    103 	moveq	r0,1

00000058 13a00000    104 	movne	r0,0

0000005c ea00000d    105 	b	.L67

                     106 .L72:

                     107 ;29:     }


                     108 ;30:     return BufferView_decodeTL(bv, NULL, &len, NULL)


                     109 

00000060 e28d2004    110 	add	r2,sp,4

00000064 e1a00005    111 	mov	r0,r5


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
00000068 e3a04000    112 	mov	r4,0

0000006c e1a03004    113 	mov	r3,r4

00000070 e1a01004    114 	mov	r1,r4

00000074 eb00001b*   115 	bl	BufferView_decodeTL

00000078 e3500000    116 	cmp	r0,0

0000007c 0a000004    117 	beq	.L76

00000080 e59d1004    118 	ldr	r1,[sp,4]

00000084 e1a00005    119 	mov	r0,r5

00000088 eb000000*   120 	bl	BufferView_advance

0000008c e3500000    121 	cmp	r0,0

00000090 13a04001    122 	movne	r4,1

                     123 .L76:

00000094 e20400ff    124 	and	r0,r4,255

                     125 .L67:

00000098 e28dd008    126 	add	sp,sp,8

0000009c e8bd8070    127 	ldmfd	[sp]!,{r4-r6,pc}

                     128 	.endf	BufferView_skipObject

                     129 	.align	4

                     130 ;objTag	[sp,3]	local

                     131 ;len	[sp,4]	local

                     132 

                     133 ;bv	r5	param

                     134 ;tag	r4	param

                     135 ;strict	r6	param

                     136 

                     137 	.section ".bss","awb"

                     138 .L176:

                     139 	.data

                     140 	.text

                     141 

                     142 ;31:         && BufferView_advance(bv, len);


                     143 ;32: }


                     144 

                     145 ;33: 


                     146 ;34: bool BufferView_skipAnyObject(BufferView* bv)


                     147 	.align	4

                     148 	.align	4

                     149 BufferView_skipAnyObject::

000000a0 e92d4030    150 	stmfd	[sp]!,{r4-r5,lr}

                     151 ;35: {


                     152 

                     153 ;36:     size_t len;


                     154 ;37:     return BufferView_decodeTL(bv, NULL, &len, NULL)


                     155 

000000a4 e24dd004    156 	sub	sp,sp,4

000000a8 e1a0200d    157 	mov	r2,sp

000000ac e1a05000    158 	mov	r5,r0

000000b0 e3a04000    159 	mov	r4,0

000000b4 e1a03004    160 	mov	r3,r4

000000b8 e1a01004    161 	mov	r1,r4

000000bc eb000009*   162 	bl	BufferView_decodeTL

000000c0 e3500000    163 	cmp	r0,0

000000c4 0a000004    164 	beq	.L207

000000c8 e59d1000    165 	ldr	r1,[sp]

000000cc e1a00005    166 	mov	r0,r5

000000d0 eb000000*   167 	bl	BufferView_advance

000000d4 e3500000    168 	cmp	r0,0

000000d8 13a04001    169 	movne	r4,1

                     170 .L207:

000000dc e20400ff    171 	and	r0,r4,255

000000e0 e28dd004    172 	add	sp,sp,4


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
000000e4 e8bd8030    173 	ldmfd	[sp]!,{r4-r5,pc}

                     174 	.endf	BufferView_skipAnyObject

                     175 	.align	4

                     176 ;len	[sp]	local

                     177 

                     178 ;bv	r5	param

                     179 

                     180 	.section ".bss","awb"

                     181 .L266:

                     182 	.data

                     183 	.text

                     184 

                     185 ;38:         && BufferView_advance(bv, len);


                     186 ;39: }


                     187 

                     188 ;40: 


                     189 ;41: bool BufferView_decodeTL(BufferView* bv, uint8_t* pTag, size_t* pLen, size_t* pFullLen)


                     190 	.align	4

                     191 	.align	4

                     192 BufferView_decodeTL::

000000e8 e92d4cf0    193 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

000000ec e24dd004    194 	sub	sp,sp,4

000000f0 e1a04000    195 	mov	r4,r0

000000f4 e1a06001    196 	mov	r6,r1

000000f8 e1a07002    197 	mov	r7,r2

000000fc e1a0a003    198 	mov	r10,r3

                     199 ;42: {


                     200 

                     201 ;43:     uint8_t tag;


                     202 ;44:     int len;


                     203 ;45:     int objPos = bv->pos;


                     204 

00000100 e9940009    205 	ldmed	[r4],{r0,r3}

00000104 e1a05000    206 	mov	r5,r0

                     207 ;46: 


                     208 ;47:     if (bv->len - bv->pos < 2)


                     209 

00000108 e0430005    210 	sub	r0,r3,r5

0000010c e3500002    211 	cmp	r0,2

00000110 3a000008    212 	blo	.L290

                     213 ;48:     {


                     214 

                     215 ;49:         return FALSE;


                     216 

                     217 ;50:     }


                     218 ;51: 


                     219 ;52:     tag = BufferView_readTag(bv);


                     220 

00000114 e5940000    221 	ldr	r0,[r4]

00000118 e2852001    222 	add	r2,r5,1

0000011c e5842004    223 	str	r2,[r4,4]

00000120 e7d0b005    224 	ldrb	fp,[r0,r5]

                     225 ;53:     bv->pos = BerDecoder_decodeLength(bv->p, &len, bv->pos, bv->len);


                     226 

00000124 e1a0100d    227 	mov	r1,sp

00000128 eb000000*   228 	bl	BerDecoder_decodeLength

0000012c e5840004    229 	str	r0,[r4,4]

                     230 ;54:     if (bv->pos <= 0)


                     231 

00000130 e3500000    232 	cmp	r0,0

00000134 1a000001    233 	bne	.L289


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
                     234 .L290:

                     235 ;55:     {


                     236 

                     237 ;56:         return FALSE;


                     238 

00000138 e3a00000    239 	mov	r0,0

0000013c ea00000b    240 	b	.L283

                     241 .L289:

                     242 ;57:     }


                     243 ;58: 


                     244 ;59:     if (pTag != NULL)


                     245 

00000140 e3560000    246 	cmp	r6,0

                     247 ;60:     {


                     248 

                     249 ;61:         *pTag = tag;


                     250 

00000144 15c6b000    251 	strneb	fp,[r6]

                     252 ;62:     }


                     253 ;63: 


                     254 ;64:     if (pLen != NULL)


                     255 

00000148 e3570000    256 	cmp	r7,0

                     257 ;65:     {


                     258 

                     259 ;66:         *pLen = len;


                     260 

0000014c 159d0000    261 	ldrne	r0,[sp]

00000150 15870000    262 	strne	r0,[r7]

                     263 ;67:     }


                     264 ;68: 


                     265 ;69:     if (pFullLen != NULL)


                     266 

00000154 e35a0000    267 	cmp	r10,0

                     268 ;70:     {


                     269 

                     270 ;71:         *pFullLen = bv->pos - objPos + len;


                     271 

00000158 15940004    272 	ldrne	r0,[r4,4]

0000015c 159d1000    273 	ldrne	r1,[sp]

00000160 10400005    274 	subne	r0,r0,r5

00000164 10810000    275 	addne	r0,r1,r0

00000168 158a0000    276 	strne	r0,[r10]

                     277 ;72:     }


                     278 ;73: 


                     279 ;74:     return TRUE;


                     280 

0000016c e3a00001    281 	mov	r0,1

                     282 .L283:

00000170 e28dd004    283 	add	sp,sp,4

00000174 e8bd8cf0    284 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                     285 	.endf	BufferView_decodeTL

                     286 	.align	4

                     287 ;tag	fp	local

                     288 ;len	[sp]	local

                     289 ;objPos	r5	local

                     290 

                     291 ;bv	r4	param

                     292 ;pTag	r6	param

                     293 ;pLen	r7	param

                     294 ;pFullLen	r10	param


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
                     295 

                     296 	.section ".bss","awb"

                     297 .L390:

                     298 	.data

                     299 	.text

                     300 

                     301 ;75: }


                     302 

                     303 ;76: 


                     304 ;77: bool BufferView_decodeExtTL(BufferView* bv, uint32_t* pTag, size_t* pLen, size_t* pFullLen)


                     305 	.align	4

                     306 	.align	4

                     307 BufferView_decodeExtTL::

00000178 e92d4cf0    308 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

0000017c e24dd004    309 	sub	sp,sp,4

00000180 e1a04000    310 	mov	r4,r0

00000184 e1a0b001    311 	mov	fp,r1

00000188 e1a07002    312 	mov	r7,r2

0000018c e1a0a003    313 	mov	r10,r3

                     314 ;78: {


                     315 

                     316 ;79:     uint32_t tag;


                     317 ;80:     //Запоминаем начало объекта чтобы потом посчитать полную длину


                     318 ;81:     size_t objPos = bv->pos;


                     319 

00000190 e9940009    320 	ldmed	[r4],{r0,r3}

00000194 e1a06000    321 	mov	r6,r0

                     322 ;82:     size_t len;


                     323 ;83:     int newPos;


                     324 ;84: 


                     325 ;85: 


                     326 ;86:     if (bv->len - bv->pos < 2)


                     327 

00000198 e0430006    328 	sub	r0,r3,r6

0000019c e3500002    329 	cmp	r0,2

000001a0 3a000011    330 	blo	.L433

                     331 ;87:     {


                     332 

                     333 ;88:         return false;


                     334 

                     335 ;89:     }


                     336 ;90: 


                     337 ;91:     tag = BufferView_readTag(bv);


                     338 

000001a4 e5940000    339 	ldr	r0,[r4]

000001a8 e2861001    340 	add	r1,r6,1

000001ac e5841004    341 	str	r1,[r4,4]

000001b0 e7d05006    342 	ldrb	r5,[r0,r6]

                     343 ;92:     if ((tag & 0x1f) == 0x1f)


                     344 

000001b4 e205201f    345 	and	r2,r5,31

000001b8 e352001f    346 	cmp	r2,31

000001bc 1a000005    347 	bne	.L426

                     348 ;93:     {


                     349 

                     350 ;94:         //Multibyte tag


                     351 ;95:         tag <<= 8;


                     352 

                     353 ;96:         tag |= BufferView_readTag(bv);


                     354 

000001c0 e2812001    355 	add	r2,r1,1


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
000001c4 e5842004    356 	str	r2,[r4,4]

000001c8 e7d01001    357 	ldrb	r1,[r0,r1]

000001cc e1815405    358 	orr	r5,r1,r5 lsl 8

                     359 ;97:         if (tag & 0x80)


                     360 

000001d0 e3150080    361 	tst	r5,128

000001d4 1a000004    362 	bne	.L433

                     363 .L426:

                     364 ;98:         {


                     365 

                     366 ;99:             //Тэг больше двух байт. С такими пока не работаем.


                     367 ;100:             return false;


                     368 

                     369 ;101:         }


                     370 ;102:     }


                     371 ;103: 


                     372 ;104:     newPos = BerDecoder_decodeLength(bv->p, (int*)&len, bv->pos, bv->len);


                     373 

000001d8 e5942004    374 	ldr	r2,[r4,4]

000001dc e1a0100d    375 	mov	r1,sp

000001e0 eb000000*   376 	bl	BerDecoder_decodeLength

                     377 ;105:     if (newPos <= 0)


                     378 

000001e4 e3500000    379 	cmp	r0,0

000001e8 ca000001    380 	bgt	.L432

                     381 .L433:

                     382 ;106:     {


                     383 

                     384 ;107:         return false;


                     385 

000001ec e3a00000    386 	mov	r0,0

000001f0 ea00000c    387 	b	.L420

                     388 .L432:

                     389 ;108:     }


                     390 ;109:     bv->pos = newPos;


                     391 

000001f4 e5840004    392 	str	r0,[r4,4]

                     393 ;110: 


                     394 ;111:     if (pTag != NULL)


                     395 

000001f8 e35b0000    396 	cmp	fp,0

                     397 ;112:     {


                     398 

                     399 ;113:         *pTag = tag;


                     400 

000001fc 158b5000    401 	strne	r5,[fp]

                     402 ;114:     }


                     403 ;115: 


                     404 ;116:     if (pLen != NULL)


                     405 

00000200 e3570000    406 	cmp	r7,0

                     407 ;117:     {


                     408 

                     409 ;118:         *pLen = len;


                     410 

00000204 159d0000    411 	ldrne	r0,[sp]

00000208 15870000    412 	strne	r0,[r7]

                     413 ;119:     }


                     414 ;120: 


                     415 ;121:     if (pFullLen != NULL)


                     416 


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
0000020c e35a0000    417 	cmp	r10,0

                     418 ;122:     {


                     419 

                     420 ;123:         *pFullLen = bv->pos - objPos + len;


                     421 

00000210 15940004    422 	ldrne	r0,[r4,4]

00000214 10401006    423 	subne	r1,r0,r6

00000218 159d0000    424 	ldrne	r0,[sp]

0000021c 10800001    425 	addne	r0,r0,r1

00000220 158a0000    426 	strne	r0,[r10]

                     427 ;124:     }


                     428 ;125: 


                     429 ;126:     return true;


                     430 

00000224 e3a00001    431 	mov	r0,1

                     432 .L420:

00000228 e28dd004    433 	add	sp,sp,4

0000022c e8bd8cf0    434 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                     435 	.endf	BufferView_decodeExtTL

                     436 	.align	4

                     437 ;tag	r5	local

                     438 ;objPos	r6	local

                     439 ;len	[sp]	local

                     440 ;newPos	r0	local

                     441 

                     442 ;bv	r4	param

                     443 ;pTag	fp	param

                     444 ;pLen	r7	param

                     445 ;pFullLen	r10	param

                     446 

                     447 	.section ".bss","awb"

                     448 .L554:

                     449 	.data

                     450 	.text

                     451 

                     452 ;127: }


                     453 

                     454 ;128: 


                     455 ;129: bool BufferView_decodeInt32(BufferView* bv, size_t intLen, int32_t* result)


                     456 	.align	4

                     457 	.align	4

                     458 BufferView_decodeInt32::

00000230 e92d4070    459 	stmfd	[sp]!,{r4-r6,lr}

00000234 e1a04000    460 	mov	r4,r0

00000238 e1a05001    461 	mov	r5,r1

0000023c e1a06002    462 	mov	r6,r2

                     463 ;130: {


                     464 

                     465 ;131:     if (bv->pos + intLen > bv->len)


                     466 

00000240 e994000c    467 	ldmed	[r4],{r2-r3}

00000244 e0820005    468 	add	r0,r2,r5

00000248 e1500003    469 	cmp	r0,r3

                     470 ;132:     {


                     471 

                     472 ;133:         return false;


                     473 

0000024c 83a00000    474 	movhi	r0,0

00000250 8a000006    475 	bhi	.L590

                     476 ;134:     }


                     477 ;135:     *result = BerDecoder_decodeInt32(bv->p, intLen, bv->pos);



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
                     478 

00000254 e5940000    479 	ldr	r0,[r4]

00000258 eb000000*   480 	bl	BerDecoder_decodeInt32

0000025c e5860000    481 	str	r0,[r6]

                     482 ;136:     bv->pos += intLen;


                     483 

00000260 e5940004    484 	ldr	r0,[r4,4]

00000264 e0800005    485 	add	r0,r0,r5

00000268 e5840004    486 	str	r0,[r4,4]

                     487 ;137:     return true;


                     488 

0000026c e3a00001    489 	mov	r0,1

                     490 .L590:

00000270 e8bd8070    491 	ldmfd	[sp]!,{r4-r6,pc}

                     492 	.endf	BufferView_decodeInt32

                     493 	.align	4

                     494 

                     495 ;bv	r4	param

                     496 ;intLen	r5	param

                     497 ;result	r6	param

                     498 

                     499 	.section ".bss","awb"

                     500 .L630:

                     501 	.data

                     502 	.text

                     503 

                     504 ;138: }


                     505 

                     506 ;139: 


                     507 ;140: bool BufferView_decodeUInt32(BufferView* bv, size_t intLen, uint32_t* result)


                     508 	.align	4

                     509 	.align	4

                     510 BufferView_decodeUInt32::

00000274 e92d4070    511 	stmfd	[sp]!,{r4-r6,lr}

00000278 e1a04000    512 	mov	r4,r0

0000027c e1a05001    513 	mov	r5,r1

00000280 e1a06002    514 	mov	r6,r2

                     515 ;141: {


                     516 

                     517 ;142:     if (bv->pos + intLen > bv->len)


                     518 

00000284 e994000c    519 	ldmed	[r4],{r2-r3}

00000288 e0820005    520 	add	r0,r2,r5

0000028c e1500003    521 	cmp	r0,r3

                     522 ;143:     {


                     523 

                     524 ;144:         return FALSE;


                     525 

00000290 83a00000    526 	movhi	r0,0

00000294 8a000006    527 	bhi	.L644

                     528 ;145:     }


                     529 ;146:     *result = BerDecoder_decodeUint32(bv->p, intLen, bv->pos);


                     530 

00000298 e5940000    531 	ldr	r0,[r4]

0000029c eb000000*   532 	bl	BerDecoder_decodeUint32

000002a0 e5860000    533 	str	r0,[r6]

                     534 ;147:     bv->pos += intLen;


                     535 

000002a4 e5940004    536 	ldr	r0,[r4,4]

000002a8 e0800005    537 	add	r0,r0,r5

000002ac e5840004    538 	str	r0,[r4,4]


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
                     539 ;148:     return TRUE;


                     540 

000002b0 e3a00001    541 	mov	r0,1

                     542 .L644:

000002b4 e8bd8070    543 	ldmfd	[sp]!,{r4-r6,pc}

                     544 	.endf	BufferView_decodeUInt32

                     545 	.align	4

                     546 

                     547 ;bv	r4	param

                     548 ;intLen	r5	param

                     549 ;result	r6	param

                     550 

                     551 	.section ".bss","awb"

                     552 .L678:

                     553 	.data

                     554 	.text

                     555 

                     556 ;149: }


                     557 

                     558 ;150: 


                     559 ;151: bool BufferView_decodeInt32TL(BufferView* bv, uint8_t expectedTag,


                     560 	.align	4

                     561 	.align	4

                     562 BufferView_decodeInt32TL::

000002b8 e92d4070    563 	stmfd	[sp]!,{r4-r6,lr}

                     564 ;152:     int32_t* result)


                     565 ;153: {


                     566 

                     567 ;154:     uint8_t tag;


                     568 ;155:     size_t intLen;


                     569 ;156:     if (!BufferView_decodeTL(bv, &tag, &intLen, NULL) || tag != expectedTag)


                     570 

000002bc e1a06002    571 	mov	r6,r2

000002c0 e24dd008    572 	sub	sp,sp,8

000002c4 e28d2004    573 	add	r2,sp,4

000002c8 e1a05001    574 	mov	r5,r1

000002cc e28d1003    575 	add	r1,sp,3

000002d0 e1a04000    576 	mov	r4,r0

000002d4 e3a03000    577 	mov	r3,0

000002d8 ebffff82*   578 	bl	BufferView_decodeTL

000002dc e3500000    579 	cmp	r0,0

000002e0 0a000002    580 	beq	.L695

000002e4 e5dd0003    581 	ldrb	r0,[sp,3]

000002e8 e1500005    582 	cmp	r0,r5

000002ec 0a000001    583 	beq	.L694

                     584 .L695:

                     585 ;157:     {


                     586 

                     587 ;158:         return false;


                     588 

000002f0 e3a00000    589 	mov	r0,0

000002f4 ea000003    590 	b	.L692

                     591 .L694:

                     592 ;159:     }


                     593 ;160: 


                     594 ;161:     return BufferView_decodeInt32(bv, intLen, result);


                     595 

000002f8 e1a02006    596 	mov	r2,r6

000002fc e59d1004    597 	ldr	r1,[sp,4]

00000300 e1a00004    598 	mov	r0,r4

00000304 ebffffc9*   599 	bl	BufferView_decodeInt32


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
                     600 .L692:

00000308 e28dd008    601 	add	sp,sp,8

0000030c e8bd8070    602 	ldmfd	[sp]!,{r4-r6,pc}

                     603 	.endf	BufferView_decodeInt32TL

                     604 	.align	4

                     605 ;tag	[sp,3]	local

                     606 ;intLen	[sp,4]	local

                     607 

                     608 ;bv	r4	param

                     609 ;expectedTag	r5	param

                     610 ;result	r6	param

                     611 

                     612 	.section ".bss","awb"

                     613 .L746:

                     614 	.data

                     615 	.text

                     616 

                     617 ;162: }


                     618 

                     619 ;163: 


                     620 ;164: bool BufferView_decodeUInt32TL(BufferView* bv, uint8_t expectedTag,


                     621 	.align	4

                     622 	.align	4

                     623 BufferView_decodeUInt32TL::

00000310 e92d4070    624 	stmfd	[sp]!,{r4-r6,lr}

                     625 ;165:     uint32_t* result)


                     626 ;166: {


                     627 

                     628 ;167:     uint8_t tag;


                     629 ;168:     size_t intLen;


                     630 ;169:     if (!BufferView_decodeTL(bv, &tag, &intLen, NULL) || tag != expectedTag)


                     631 

00000314 e1a06002    632 	mov	r6,r2

00000318 e24dd008    633 	sub	sp,sp,8

0000031c e28d2004    634 	add	r2,sp,4

00000320 e1a05001    635 	mov	r5,r1

00000324 e28d1003    636 	add	r1,sp,3

00000328 e1a04000    637 	mov	r4,r0

0000032c e3a03000    638 	mov	r3,0

00000330 ebffff6c*   639 	bl	BufferView_decodeTL

00000334 e3500000    640 	cmp	r0,0

00000338 0a000002    641 	beq	.L762

0000033c e5dd0003    642 	ldrb	r0,[sp,3]

00000340 e1500005    643 	cmp	r0,r5

00000344 0a000001    644 	beq	.L761

                     645 .L762:

                     646 ;170:     {


                     647 

                     648 ;171:         return FALSE;


                     649 

00000348 e3a00000    650 	mov	r0,0

0000034c ea000003    651 	b	.L759

                     652 .L761:

                     653 ;172:     }


                     654 ;173: 


                     655 ;174:     return BufferView_decodeUInt32(bv, intLen, result);


                     656 

00000350 e1a02006    657 	mov	r2,r6

00000354 e59d1004    658 	ldr	r1,[sp,4]

00000358 e1a00004    659 	mov	r0,r4

0000035c ebffffc4*   660 	bl	BufferView_decodeUInt32


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
                     661 .L759:

00000360 e28dd008    662 	add	sp,sp,8

00000364 e8bd8070    663 	ldmfd	[sp]!,{r4-r6,pc}

                     664 	.endf	BufferView_decodeUInt32TL

                     665 	.align	4

                     666 ;tag	[sp,3]	local

                     667 ;intLen	[sp,4]	local

                     668 

                     669 ;bv	r4	param

                     670 ;expectedTag	r5	param

                     671 ;result	r6	param

                     672 

                     673 	.section ".bss","awb"

                     674 .L810:

                     675 	.data

                     676 	.text

                     677 

                     678 ;175: }


                     679 

                     680 ;176: 


                     681 ;177: bool BufferView_decodeStringViewTL(BufferView* bv, uint8_t expectedTag,


                     682 	.align	4

                     683 	.align	4

                     684 BufferView_decodeStringViewTL::

00000368 e92d4070    685 	stmfd	[sp]!,{r4-r6,lr}

                     686 ;178:     StringView* result)


                     687 ;179: {


                     688 

                     689 ;180:     uint8_t tag;


                     690 ;181:     size_t len;


                     691 ;182:     if (!BufferView_decodeTL(bv, &tag, &len, NULL) || tag != expectedTag)


                     692 

0000036c e1a05002    693 	mov	r5,r2

00000370 e24dd008    694 	sub	sp,sp,8

00000374 e28d2004    695 	add	r2,sp,4

00000378 e1a06001    696 	mov	r6,r1

0000037c e28d1003    697 	add	r1,sp,3

00000380 e1a04000    698 	mov	r4,r0

00000384 e3a03000    699 	mov	r3,0

00000388 ebffff56*   700 	bl	BufferView_decodeTL

0000038c e3500000    701 	cmp	r0,0

00000390 0a000002    702 	beq	.L826

00000394 e5dd0003    703 	ldrb	r0,[sp,3]

00000398 e1500006    704 	cmp	r0,r6

0000039c 0a000001    705 	beq	.L825

                     706 .L826:

                     707 ;183:     {


                     708 

                     709 ;184:         return FALSE;


                     710 

000003a0 e3a00000    711 	mov	r0,0

000003a4 ea000009    712 	b	.L823

                     713 .L825:

                     714 ;185:     }


                     715 ;186:     StringView_init(result, (const char*)bv->p + bv->pos, len);


                     716 

000003a8 e59d2004    717 	ldr	r2,[sp,4]

000003ac e894000a    718 	ldmfd	[r4],{r1,r3}

000003b0 e1a00005    719 	mov	r0,r5

000003b4 e0831001    720 	add	r1,r3,r1

000003b8 eb000000*   721 	bl	StringView_init


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
                     722 ;187:     bv->pos += len;


                     723 

000003bc e5940004    724 	ldr	r0,[r4,4]

000003c0 e59d1004    725 	ldr	r1,[sp,4]

000003c4 e0800001    726 	add	r0,r0,r1

000003c8 e5840004    727 	str	r0,[r4,4]

                     728 ;188:     return TRUE;


                     729 

000003cc e3a00001    730 	mov	r0,1

                     731 .L823:

000003d0 e28dd008    732 	add	sp,sp,8

000003d4 e8bd8070    733 	ldmfd	[sp]!,{r4-r6,pc}

                     734 	.endf	BufferView_decodeStringViewTL

                     735 	.align	4

                     736 ;tag	[sp,3]	local

                     737 ;len	[sp,4]	local

                     738 

                     739 ;bv	r4	param

                     740 ;expectedTag	r6	param

                     741 ;result	r5	param

                     742 

                     743 	.section ".bss","awb"

                     744 .L868:

                     745 	.data

                     746 	.text

                     747 

                     748 ;189: }


                     749 

                     750 ;190: 


                     751 ;191: bool BufferView_writeTag(BufferView* bv, uint8_t tag)


                     752 	.align	4

                     753 	.align	4

                     754 BufferView_writeTag::

000003d8 e92d0100    755 	stmfd	[sp]!,{r8}

                     756 ;192: {


                     757 

                     758 ;193:     if (bv->pos + 1 > bv->len)


                     759 

000003dc e9901100    760 	ldmed	[r0],{r8,r12}

000003e0 e2882001    761 	add	r2,r8,1

000003e4 e1a0300c    762 	mov	r3,r12

000003e8 e1520003    763 	cmp	r2,r3

                     764 ;194:     {


                     765 

                     766 ;195:         //Не лезет в буфер


                     767 ;196:         return FALSE;


                     768 

000003ec 83a00000    769 	movhi	r0,0

                     770 ;197:     }


                     771 ;198:     bv->p[bv->pos++] = tag;


                     772 

000003f0 95903000    773 	ldrls	r3,[r0]

000003f4 95802004    774 	strls	r2,[r0,4]

000003f8 97c31008    775 	strlsb	r1,[r3,r8]

                     776 ;199:     return TRUE;


                     777 

000003fc 93a00001    778 	movls	r0,1

00000400 e8bd0100    779 	ldmfd	[sp]!,{r8}

00000404 e12fff1e*   780 	ret	

                     781 	.endf	BufferView_writeTag

                     782 	.align	4


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
                     783 

                     784 ;bv	r0	param

                     785 ;tag	r1	param

                     786 

                     787 	.section ".bss","awb"

                     788 .L918:

                     789 	.data

                     790 	.text

                     791 

                     792 ;200: }


                     793 

                     794 ;201: 


                     795 ;202: bool BufferView_encodeStringView(BufferView* bv, uint8_t tag,


                     796 	.align	4

                     797 	.align	4

                     798 BufferView_encodeStringView::

00000408 e92d4070    799 	stmfd	[sp]!,{r4-r6,lr}

0000040c e1a04000    800 	mov	r4,r0

00000410 e1a05002    801 	mov	r5,r2

                     802 ;203:     const StringView* strView)


                     803 ;204: {


                     804 

                     805 ;205:     size_t fullSize = BerEncoder_determineFullObjectSize(strView->len);


                     806 

00000414 e5950000    807 	ldr	r0,[r5]

00000418 e1a06001    808 	mov	r6,r1

0000041c eb000000*   809 	bl	BerEncoder_determineFullObjectSize

                     810 ;206:     size_t freeSpace = bv->len - bv->pos;


                     811 

00000420 e9940006    812 	ldmed	[r4],{r1-r2}

00000424 e0422001    813 	sub	r2,r2,r1

                     814 ;207:     if (fullSize > freeSpace)


                     815 

00000428 e1500002    816 	cmp	r0,r2

                     817 ;208:     {


                     818 

                     819 ;209:         return FALSE;


                     820 

0000042c 83a00000    821 	movhi	r0,0

00000430 8a000014    822 	bhi	.L931

                     823 ;210:     }


                     824 ;211:     //Пишем тэг


                     825 ;212:     bv->p[bv->pos++] = tag;


                     826 

00000434 e5940000    827 	ldr	r0,[r4]

00000438 e2812001    828 	add	r2,r1,1

0000043c e5842004    829 	str	r2,[r4,4]

00000440 e7c06001    830 	strb	r6,[r0,r1]

00000444 e5950000    831 	ldr	r0,[r5]

00000448 e1b03000    832 	movs	r3,r0

                     833 ;213: 


                     834 ;214:     if (strView->len == 0)


                     835 

0000044c e8940006    836 	ldmfd	[r4],{r1-r2}

00000450 1a000006    837 	bne	.L936

                     838 ;215:     {


                     839 

                     840 ;216:         //Пишем длину


                     841 ;217:         bv->p[bv->pos++] = 0;


                     842 

00000454 e2820001    843 	add	r0,r2,1


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
00000458 e5840004    844 	str	r0,[r4,4]

0000045c e7c13002    845 	strb	r3,[r1,r2]

                     846 ;224:     }


                     847 ;225:     return BufferView_writeStringView(bv, strView);


                     848 

00000460 e1a01005    849 	mov	r1,r5

00000464 e1a00004    850 	mov	r0,r4

00000468 e8bd4070    851 	ldmfd	[sp]!,{r4-r6,lr}

0000046c ea000000*   852 	b	BufferView_writeStringView

                     853 .L936:

                     854 ;218:         //Больше писать нечего - пустая строка


                     855 ;219:     }


                     856 ;220:     else


                     857 ;221:     {


                     858 

                     859 ;222:         //Пишем длину


                     860 ;223:         bv->pos = BerEncoder_encodeLength(strView->len, bv->p, bv->pos);


                     861 

00000470 eb000000*   862 	bl	BerEncoder_encodeLength

00000474 e1a01005    863 	mov	r1,r5

00000478 e5840004    864 	str	r0,[r4,4]

                     865 ;224:     }


                     866 ;225:     return BufferView_writeStringView(bv, strView);


                     867 

0000047c e1a00004    868 	mov	r0,r4

00000480 e8bd4070    869 	ldmfd	[sp]!,{r4-r6,lr}

00000484 ea000000*   870 	b	BufferView_writeStringView

                     871 .L931:

00000488 e8bd8070    872 	ldmfd	[sp]!,{r4-r6,pc}

                     873 	.endf	BufferView_encodeStringView

                     874 	.align	4

                     875 ;freeSpace	r2	local

                     876 

                     877 ;bv	r4	param

                     878 ;tag	r6	param

                     879 ;strView	r5	param

                     880 

                     881 	.section ".bss","awb"

                     882 .L1010:

                     883 	.data

                     884 	.text

                     885 

                     886 ;226: }


                     887 

                     888 ;227: 


                     889 ;228: bool BufferView_encodeStr(BufferView* bv, uint8_t tag, const char* str)


                     890 	.align	4

                     891 	.align	4

                     892 BufferView_encodeStr::

0000048c e92d4030    893 	stmfd	[sp]!,{r4-r5,lr}

                     894 ;229: {


                     895 

                     896 ;230:     StringView strView;


                     897 ;231:     StringView_fromCStr(&strView, (char*)str);


                     898 

00000490 e1a05001    899 	mov	r5,r1

00000494 e1a01002    900 	mov	r1,r2

00000498 e24dd008    901 	sub	sp,sp,8

0000049c e1a04000    902 	mov	r4,r0

000004a0 e1a0000d    903 	mov	r0,sp

000004a4 eb000000*   904 	bl	StringView_fromCStr


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
                     905 ;232:     return BufferView_encodeStringView(bv, tag, &strView);


                     906 

000004a8 e1a0200d    907 	mov	r2,sp

000004ac e1a01005    908 	mov	r1,r5

000004b0 e1a00004    909 	mov	r0,r4

000004b4 ebffffd3*   910 	bl	BufferView_encodeStringView

000004b8 e28dd008    911 	add	sp,sp,8

000004bc e8bd8030    912 	ldmfd	[sp]!,{r4-r5,pc}

                     913 	.endf	BufferView_encodeStr

                     914 	.align	4

                     915 ;strView	[sp]	local

                     916 

                     917 ;bv	r4	param

                     918 ;tag	r5	param

                     919 ;str	r2	param

                     920 

                     921 	.section ".bss","awb"

                     922 .L1057:

                     923 	.data

                     924 	.text

                     925 

                     926 ;233: }


                     927 

                     928 ;234: 


                     929 ;235: bool BufferView_encodeTL(BufferView* bv, uint8_t tag, size_t length)


                     930 	.align	4

                     931 	.align	4

                     932 BufferView_encodeTL::

000004c0 e92d4070    933 	stmfd	[sp]!,{r4-r6,lr}

                     934 ;236: {


                     935 

                     936 ;237:     size_t lenSize = BerEncoder_determineLengthSize(length);


                     937 

000004c4 e1a06001    938 	mov	r6,r1

000004c8 e1a04000    939 	mov	r4,r0

000004cc e1a05002    940 	mov	r5,r2

000004d0 e1a00005    941 	mov	r0,r5

000004d4 eb000000*   942 	bl	BerEncoder_determineLengthSize

                     943 ;238:     if (1 + lenSize > bv->len - bv->pos)


                     944 

000004d8 e9941008    945 	ldmed	[r4],{r3,r12}

000004dc e2800001    946 	add	r0,r0,1

000004e0 e04c1003    947 	sub	r1,r12,r3

000004e4 e1500001    948 	cmp	r0,r1

                     949 ;239:     {


                     950 

                     951 ;240:         return FALSE;


                     952 

000004e8 83a00000    953 	movhi	r0,0

000004ec 8a000005    954 	bhi	.L1064

                     955 ;241:     }


                     956 ;242:     bv->pos = BerEncoder_encodeTL(tag, length, bv->p, bv->pos);


                     957 

000004f0 e5942000    958 	ldr	r2,[r4]

000004f4 e1a01005    959 	mov	r1,r5

000004f8 e1a00006    960 	mov	r0,r6

000004fc eb000000*   961 	bl	BerEncoder_encodeTL

00000500 e5840004    962 	str	r0,[r4,4]

                     963 ;243:     return TRUE;


                     964 

00000504 e3a00001    965 	mov	r0,1


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
                     966 .L1064:

00000508 e8bd8070    967 	ldmfd	[sp]!,{r4-r6,pc}

                     968 	.endf	BufferView_encodeTL

                     969 	.align	4

                     970 

                     971 ;bv	r4	param

                     972 ;tag	r6	param

                     973 ;length	r5	param

                     974 

                     975 	.section ".bss","awb"

                     976 .L1094:

                     977 	.data

                     978 	.text

                     979 

                     980 ;244: }


                     981 

                     982 ;245: 


                     983 ;246: bool BufferView_encodeExtTL(BufferView* bv, uint16_t extTag, size_t length)


                     984 	.align	4

                     985 	.align	4

                     986 BufferView_encodeExtTL::

0000050c e92d4070    987 	stmfd	[sp]!,{r4-r6,lr}

                     988 ;247: {


                     989 

                     990 ;248:     size_t lenSize = BerEncoder_determineLengthSize(length);


                     991 

00000510 e1a05001    992 	mov	r5,r1

00000514 e1a04000    993 	mov	r4,r0

00000518 e1a06002    994 	mov	r6,r2

0000051c e1a00006    995 	mov	r0,r6

00000520 eb000000*   996 	bl	BerEncoder_determineLengthSize

                     997 ;249:     if (2 + lenSize > bv->len - bv->pos)


                     998 

00000524 e2801002    999 	add	r1,r0,2

00000528 e9940005   1000 	ldmed	[r4],{r0,r2}

0000052c e0422000   1001 	sub	r2,r2,r0

00000530 e1510002   1002 	cmp	r1,r2

                    1003 ;250:     {


                    1004 

                    1005 ;251:         return FALSE;


                    1006 

00000534 83a00000   1007 	movhi	r0,0

00000538 8a00000a   1008 	bhi	.L1108

                    1009 ;252:     }


                    1010 ;253:     bv->p[bv->pos++] = extTag >> 8;


                    1011 

0000053c e5941000   1012 	ldr	r1,[r4]

00000540 e2802001   1013 	add	r2,r0,1

00000544 e5842004   1014 	str	r2,[r4,4]

00000548 e1a02445   1015 	mov	r2,r5 asr 8

0000054c e7c12000   1016 	strb	r2,[r1,r0]

                    1017 ;254:     bv->pos = BerEncoder_encodeTL(extTag & 0xFF, length, bv->p, bv->pos);


                    1018 

00000550 e894000c   1019 	ldmfd	[r4],{r2-r3}

00000554 e1a01006   1020 	mov	r1,r6

00000558 e20500ff   1021 	and	r0,r5,255

0000055c eb000000*  1022 	bl	BerEncoder_encodeTL

00000560 e5840004   1023 	str	r0,[r4,4]

                    1024 ;255:     return TRUE;


                    1025 

00000564 e3a00001   1026 	mov	r0,1


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
                    1027 .L1108:

00000568 e8bd8070   1028 	ldmfd	[sp]!,{r4-r6,pc}

                    1029 	.endf	BufferView_encodeExtTL

                    1030 	.align	4

                    1031 

                    1032 ;bv	r4	param

                    1033 ;extTag	r5	param

                    1034 ;length	r6	param

                    1035 

                    1036 	.section ".bss","awb"

                    1037 .L1142:

                    1038 	.data

                    1039 	.text

                    1040 

                    1041 ;256: }


                    1042 

                    1043 ;257: 


                    1044 ;258: bool BufferView_encodeInt32(BufferView* bv, uint8_t tag, int32_t value)


                    1045 	.align	4

                    1046 	.align	4

                    1047 BufferView_encodeInt32::

0000056c e92d4070   1048 	stmfd	[sp]!,{r4-r6,lr}

                    1049 ;259: {


                    1050 

                    1051 ;260:     int encodedLen = 2 //тэг + длина


                    1052 

00000570 e1a06001   1053 	mov	r6,r1

00000574 e1a04000   1054 	mov	r4,r0

00000578 e1a05002   1055 	mov	r5,r2

0000057c e1a00005   1056 	mov	r0,r5

00000580 eb000000*  1057 	bl	BerEncoder_Int32DetermineEncodedSize

00000584 e2800002   1058 	add	r0,r0,2

                    1059 ;261:         + BerEncoder_Int32DetermineEncodedSize(value);


                    1060 ;262:     if (bv->pos + encodedLen > bv->len)


                    1061 

00000588 e9941008   1062 	ldmed	[r4],{r3,r12}

0000058c e0800003   1063 	add	r0,r0,r3

00000590 e150000c   1064 	cmp	r0,r12

                    1065 ;263:     {


                    1066 

                    1067 ;264:         //Не лезет в буфер


                    1068 ;265:         return false;


                    1069 

00000594 83a00000   1070 	movhi	r0,0

00000598 8a000005   1071 	bhi	.L1156

                    1072 ;266:     }


                    1073 ;267:     bv->pos = BerEncoder_EncodeInt32WithTL(tag, value, bv->p, bv->pos);


                    1074 

0000059c e5942000   1075 	ldr	r2,[r4]

000005a0 e1a01005   1076 	mov	r1,r5

000005a4 e1a00006   1077 	mov	r0,r6

000005a8 eb000000*  1078 	bl	BerEncoder_EncodeInt32WithTL

000005ac e5840004   1079 	str	r0,[r4,4]

                    1080 ;268:     return true;


                    1081 

000005b0 e3a00001   1082 	mov	r0,1

                    1083 .L1156:

000005b4 e8bd8070   1084 	ldmfd	[sp]!,{r4-r6,pc}

                    1085 	.endf	BufferView_encodeInt32

                    1086 	.align	4

                    1087 ;encodedLen	r0	local


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
                    1088 

                    1089 ;bv	r4	param

                    1090 ;tag	r6	param

                    1091 ;value	r5	param

                    1092 

                    1093 	.section ".bss","awb"

                    1094 .L1190:

                    1095 	.data

                    1096 	.text

                    1097 

                    1098 ;269: }


                    1099 

                    1100 ;270: 


                    1101 ;271: bool BufferView_encodeUInt32(BufferView* bv, uint8_t tag, uint32_t value)


                    1102 	.align	4

                    1103 	.align	4

                    1104 BufferView_encodeUInt32::

000005b8 e92d4070   1105 	stmfd	[sp]!,{r4-r6,lr}

                    1106 ;272: {


                    1107 

                    1108 ;273:     int encodedLen = 2 //тэг + длина


                    1109 

000005bc e1a06001   1110 	mov	r6,r1

000005c0 e1a04000   1111 	mov	r4,r0

000005c4 e1a05002   1112 	mov	r5,r2

000005c8 e1a00005   1113 	mov	r0,r5

000005cc eb000000*  1114 	bl	BerEncoder_UInt32determineEncodedSize

000005d0 e2800002   1115 	add	r0,r0,2

                    1116 ;274:         + BerEncoder_UInt32determineEncodedSize(value);


                    1117 ;275:     if (bv->pos + encodedLen > bv->len)


                    1118 

000005d4 e9941008   1119 	ldmed	[r4],{r3,r12}

000005d8 e0800003   1120 	add	r0,r0,r3

000005dc e150000c   1121 	cmp	r0,r12

                    1122 ;276:     {


                    1123 

                    1124 ;277:         //Не лезет в буфер


                    1125 ;278:         return FALSE;


                    1126 

000005e0 83a00000   1127 	movhi	r0,0

000005e4 8a000005   1128 	bhi	.L1204

                    1129 ;279:     }


                    1130 ;280:     bv->pos = BerEncoder_encodeUInt32WithTL(tag, value, bv->p, bv->pos);


                    1131 

000005e8 e5942000   1132 	ldr	r2,[r4]

000005ec e1a01005   1133 	mov	r1,r5

000005f0 e1a00006   1134 	mov	r0,r6

000005f4 eb000000*  1135 	bl	BerEncoder_encodeUInt32WithTL

000005f8 e5840004   1136 	str	r0,[r4,4]

                    1137 ;281:     return TRUE;


                    1138 

000005fc e3a00001   1139 	mov	r0,1

                    1140 .L1204:

00000600 e8bd8070   1141 	ldmfd	[sp]!,{r4-r6,pc}

                    1142 	.endf	BufferView_encodeUInt32

                    1143 	.align	4

                    1144 ;encodedLen	r0	local

                    1145 

                    1146 ;bv	r4	param

                    1147 ;tag	r6	param

                    1148 ;value	r5	param


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
                    1149 

                    1150 	.section ".bss","awb"

                    1151 .L1238:

                    1152 	.data

                    1153 	.text

                    1154 

                    1155 ;282: }


                    1156 

                    1157 ;283: 


                    1158 ;284: bool BufferView_encodeBoolean(BufferView* bv, uint8_t tag, bool val)


                    1159 	.align	4

                    1160 	.align	4

                    1161 BufferView_encodeBoolean::

00000604 e92d4030   1162 	stmfd	[sp]!,{r4-r5,lr}

                    1163 ;285: {


                    1164 

                    1165 ;286:     if (bv->pos + 3 > bv->len)


                    1166 

00000608 e1a0c001   1167 	mov	r12,r1

0000060c e1a05000   1168 	mov	r5,r0

00000610 e5953004   1169 	ldr	r3,[r5,4]

00000614 e5951008   1170 	ldr	r1,[r5,8]

00000618 e2830003   1171 	add	r0,r3,3

0000061c e1500001   1172 	cmp	r0,r1

                    1173 ;287:     {


                    1174 

                    1175 ;288:         //Не лезет в буфер


                    1176 ;289:         return FALSE;


                    1177 

00000620 83a00000   1178 	movhi	r0,0

00000624 8a000005   1179 	bhi	.L1252

00000628 e1a01002   1180 	mov	r1,r2

                    1181 ;290:     }


                    1182 ;291:     bv->pos = BerEncoder_encodeBoolean(tag, val, bv->p, bv->pos);


                    1183 

0000062c e5952000   1184 	ldr	r2,[r5]

00000630 e1a0000c   1185 	mov	r0,r12

00000634 eb000000*  1186 	bl	BerEncoder_encodeBoolean

00000638 e5850004   1187 	str	r0,[r5,4]

                    1188 ;292:     return TRUE;


                    1189 

0000063c e3a00001   1190 	mov	r0,1

                    1191 .L1252:

00000640 e8bd8030   1192 	ldmfd	[sp]!,{r4-r5,pc}

                    1193 	.endf	BufferView_encodeBoolean

                    1194 	.align	4

                    1195 

                    1196 ;bv	r5	param

                    1197 ;tag	r12	param

                    1198 ;val	r4	param

                    1199 

                    1200 	.section ".bss","awb"

                    1201 .L1286:

                    1202 	.data

                    1203 	.text

                    1204 

                    1205 ;293: }


                    1206 

                    1207 ;294: 


                    1208 ;295: bool BufferView_encodeOctetString(BufferView* bv, uint8_t tag, void* data,


                    1209 	.align	4


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
                    1210 	.align	4

                    1211 BufferView_encodeOctetString::

00000644 e92d40f0   1212 	stmfd	[sp]!,{r4-r7,lr}

                    1213 ;296:     size_t dataLen)


                    1214 ;297: {


                    1215 

                    1216 ;298:     int encodedLen = BerEncoder_determineFullObjectSize(dataLen);


                    1217 

00000648 e24dd004   1218 	sub	sp,sp,4

0000064c e1a06001   1219 	mov	r6,r1

00000650 e1a07002   1220 	mov	r7,r2

00000654 e1a04000   1221 	mov	r4,r0

00000658 e1a05003   1222 	mov	r5,r3

0000065c e1a00005   1223 	mov	r0,r5

00000660 eb000000*  1224 	bl	BerEncoder_determineFullObjectSize

                    1225 ;299:     if (bv->pos + encodedLen > bv->len)


                    1226 

00000664 e9940006   1227 	ldmed	[r4],{r1-r2}

00000668 e0800001   1228 	add	r0,r0,r1

0000066c e1500002   1229 	cmp	r0,r2

                    1230 ;300:     {


                    1231 

                    1232 ;301:         //Не лезет в буфер


                    1233 ;302:         return FALSE;


                    1234 

00000670 83a00000   1235 	movhi	r0,0

00000674 8a000007   1236 	bhi	.L1300

                    1237 ;303:     }


                    1238 ;304:     bv->pos = BerEncoder_encodeOctetString(tag, data, dataLen, bv->p, bv->pos);


                    1239 

00000678 e58d1000   1240 	str	r1,[sp]

0000067c e5943000   1241 	ldr	r3,[r4]

00000680 e1a02005   1242 	mov	r2,r5

00000684 e1a01007   1243 	mov	r1,r7

00000688 e1a00006   1244 	mov	r0,r6

0000068c eb000000*  1245 	bl	BerEncoder_encodeOctetString

00000690 e5840004   1246 	str	r0,[r4,4]

                    1247 ;305:     return TRUE;


                    1248 

00000694 e3a00001   1249 	mov	r0,1

                    1250 .L1300:

00000698 e28dd004   1251 	add	sp,sp,4

0000069c e8bd80f0   1252 	ldmfd	[sp]!,{r4-r7,pc}

                    1253 	.endf	BufferView_encodeOctetString

                    1254 	.align	4

                    1255 

                    1256 ;bv	r4	param

                    1257 ;tag	r6	param

                    1258 ;data	r7	param

                    1259 ;dataLen	r5	param

                    1260 

                    1261 	.section ".bss","awb"

                    1262 .L1334:

                    1263 	.data

                    1264 	.text

                    1265 

                    1266 ;306: }


                    1267 

                    1268 ;307: 


                    1269 ;308: bool BufferView_encodeBufferView(BufferView* bv, uint8_t tag, BufferView* data)


                    1270 	.align	4


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
                    1271 	.align	4

                    1272 BufferView_encodeBufferView::

                    1273 ;309: {


                    1274 

                    1275 ;310:     return BufferView_encodeOctetString(bv, tag, data->p, data->pos);


                    1276 

000006a0 e892000c   1277 	ldmfd	[r2],{r2-r3}

000006a4 eaffffe6*  1278 	b	BufferView_encodeOctetString

                    1279 	.endf	BufferView_encodeBufferView

                    1280 	.align	4

                    1281 

                    1282 ;bv	none	param

                    1283 ;tag	none	param

                    1284 ;data	r2	param

                    1285 

                    1286 	.section ".bss","awb"

                    1287 .L1377:

                    1288 	.data

                    1289 	.text

                    1290 

                    1291 ;311: }


                    1292 

                    1293 ;312: 


                    1294 ;313: bool BufferView_reverseWrite(BufferView *bv, const void *src, size_t len)


                    1295 	.align	4

                    1296 	.align	4

                    1297 BufferView_reverseWrite::

000006a8 e92d0030   1298 	stmfd	[sp]!,{r4-r5}

                    1299 ;314: {


                    1300 

                    1301 ;315:     size_t i;


                    1302 ;316:     const uint8_t* pSrc = src;


                    1303 

                    1304 ;317: 


                    1305 ;318:     if (bv->pos + len > bv->len)


                    1306 

000006ac e9901008   1307 	ldmed	[r0],{r3,r12}

000006b0 e0823003   1308 	add	r3,r2,r3

000006b4 e153000c   1309 	cmp	r3,r12

                    1310 ;319:     {


                    1311 

                    1312 ;320:         return false;


                    1313 

000006b8 83a00000   1314 	movhi	r0,0

000006bc 8a00003c   1315 	bhi	.L1384

                    1316 ;321:     }


                    1317 ;322: 


                    1318 ;323:     pSrc += len - 1;


                    1319 

000006c0 e0823001   1320 	add	r3,r2,r1

000006c4 e2431001   1321 	sub	r1,r3,1

                    1322 ;324: 


                    1323 ;325:     for (i = 0; i < len; i++)


                    1324 

000006c8 e3520000   1325 	cmp	r2,0

000006cc a1a03002   1326 	movge	r3,r2

000006d0 b3a03000   1327 	movlt	r3,0

000006d4 e1b021a3   1328 	movs	r2,r3 lsr 3

000006d8 0a000029   1329 	beq	.L1426

                    1330 .L1442:

000006dc e8901010   1331 	ldmfd	[r0],{r4,r12}


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
000006e0 e28c5001   1332 	add	r5,r12,1

000006e4 e5805004   1333 	str	r5,[r0,4]

000006e8 e4515008   1334 	ldrb	r5,[r1],-8

000006ec e7c4500c   1335 	strb	r5,[r4,r12]

000006f0 e8901010   1336 	ldmfd	[r0],{r4,r12}

000006f4 e28c5001   1337 	add	r5,r12,1

000006f8 e5805004   1338 	str	r5,[r0,4]

000006fc e5d15007   1339 	ldrb	r5,[r1,7]

00000700 e7c4500c   1340 	strb	r5,[r4,r12]

00000704 e8901010   1341 	ldmfd	[r0],{r4,r12}

00000708 e28c5001   1342 	add	r5,r12,1

0000070c e5805004   1343 	str	r5,[r0,4]

00000710 e5d15006   1344 	ldrb	r5,[r1,6]

00000714 e7c4500c   1345 	strb	r5,[r4,r12]

00000718 e8901010   1346 	ldmfd	[r0],{r4,r12}

0000071c e28c5001   1347 	add	r5,r12,1

00000720 e5805004   1348 	str	r5,[r0,4]

00000724 e5d15005   1349 	ldrb	r5,[r1,5]

00000728 e7c4500c   1350 	strb	r5,[r4,r12]

0000072c e8901010   1351 	ldmfd	[r0],{r4,r12}

00000730 e28c5001   1352 	add	r5,r12,1

00000734 e5805004   1353 	str	r5,[r0,4]

00000738 e5d15004   1354 	ldrb	r5,[r1,4]

0000073c e7c4500c   1355 	strb	r5,[r4,r12]

00000740 e8901010   1356 	ldmfd	[r0],{r4,r12}

00000744 e28c5001   1357 	add	r5,r12,1

00000748 e5805004   1358 	str	r5,[r0,4]

0000074c e5d15003   1359 	ldrb	r5,[r1,3]

00000750 e7c4500c   1360 	strb	r5,[r4,r12]

00000754 e8901010   1361 	ldmfd	[r0],{r4,r12}

00000758 e28c5001   1362 	add	r5,r12,1

0000075c e5805004   1363 	str	r5,[r0,4]

00000760 e5d15002   1364 	ldrb	r5,[r1,2]

00000764 e7c4500c   1365 	strb	r5,[r4,r12]

00000768 e8901010   1366 	ldmfd	[r0],{r4,r12}

0000076c e28c5001   1367 	add	r5,r12,1

00000770 e5805004   1368 	str	r5,[r0,4]

00000774 e5d15001   1369 	ldrb	r5,[r1,1]

00000778 e2522001   1370 	subs	r2,r2,1

0000077c e7c4500c   1371 	strb	r5,[r4,r12]

00000780 1affffd5   1372 	bne	.L1442

                    1373 .L1426:

00000784 e2132007   1374 	ands	r2,r3,7

00000788 0a000008   1375 	beq	.L1389

                    1376 .L1446:

0000078c e8900018   1377 	ldmfd	[r0],{r3-r4}

00000790 e1a0c003   1378 	mov	r12,r3

00000794 e1a03004   1379 	mov	r3,r4

00000798 e2834001   1380 	add	r4,r3,1

0000079c e5804004   1381 	str	r4,[r0,4]

000007a0 e4514001   1382 	ldrb	r4,[r1],-1

000007a4 e2522001   1383 	subs	r2,r2,1

000007a8 e7cc4003   1384 	strb	r4,[r12,r3]

000007ac 1afffff6   1385 	bne	.L1446

                    1386 .L1389:

                    1387 ;328:     }


                    1388 ;329:     return true;


                    1389 

000007b0 e3a00001   1390 	mov	r0,1

                    1391 .L1384:

000007b4 e8bd0030   1392 	ldmfd	[sp]!,{r4-r5}


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ba81.s
000007b8 e12fff1e*  1393 	ret	

                    1394 	.endf	BufferView_reverseWrite

                    1395 	.align	4

                    1396 ;pSrc	r1	local

                    1397 

                    1398 ;bv	r0	param

                    1399 ;src	r1	param

                    1400 ;len	r2	param

                    1401 

                    1402 	.section ".bss","awb"

                    1403 .L1617:

                    1404 	.data

                    1405 	.text

                    1406 

                    1407 ;330: }


                    1408 	.align	4

                    1409 

                    1410 	.data

                    1411 	.ghsnote version,6

                    1412 	.ghsnote tools,3

                    1413 	.ghsnote options,0

                    1414 	.text

                    1415 	.align	4

