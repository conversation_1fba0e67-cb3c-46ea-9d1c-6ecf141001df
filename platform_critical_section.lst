                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_11o1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=platform_critical_section.c -o gh_11o1.o -list=platform_critical_section.lst C:\Users\<USER>\AppData\Local\Temp\gh_11o1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_11o1.s
Source File: platform_critical_section.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		platform_critical_section.c

                      12 ;Source File:   platform_critical_section.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:04 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include <platform_critical_section.h>


                      23 ;2: 


                      24 ;3: #include <process.h>


                      25 ;4: 


                      26 ;5: void CriticalSection_Init(CriticalSection *cs)


                      27 ;6: {


                      28 ;7:     CSInit(cs);


                      29 ;8: }


                      30 ;9: 


                      31 ;10: void CriticalSection_Done(CriticalSection *cs)


                      32 

                      33 ;12: }


                      34 

                      35 	.text

                      36 	.align	4

                      37 CriticalSection_Init::

00000000 ea000000*    38 	b	CSInit

                      39 	.endf	CriticalSection_Init

                      40 	.align	4

                      41 

                      42 ;cs	none	param

                      43 

                      44 	.section ".bss","awb"

                      45 .L46:

                      46 	.data

                      47 	.text

                      48 

                      49 

                      50 ;13: 



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_11o1.s
                      51 ;14: void CriticalSection_Lock(CriticalSection *cs)


                      52 	.align	4

                      53 	.align	4

                      54 CriticalSection_Lock::

                      55 ;15: {


                      56 

                      57 ;16:     CSLock(cs);


                      58 

00000004 ea000000*    59 	b	CSLock

                      60 	.endf	CriticalSection_Lock

                      61 	.align	4

                      62 

                      63 ;cs	none	param

                      64 

                      65 	.section ".bss","awb"

                      66 .L78:

                      67 	.data

                      68 	.text

                      69 

                      70 ;17: }


                      71 

                      72 ;18: 


                      73 ;19: void CriticalSection_Unlock(CriticalSection *cs)


                      74 	.align	4

                      75 	.align	4

                      76 CriticalSection_Unlock::

                      77 ;20: {


                      78 

                      79 ;21:     CSUnlock(cs);


                      80 

00000008 ea000000*    81 	b	CSUnlock

                      82 	.endf	CriticalSection_Unlock

                      83 	.align	4

                      84 

                      85 ;cs	none	param

                      86 

                      87 	.section ".bss","awb"

                      88 .L110:

                      89 	.data

                      90 	.text

                      91 

                      92 ;22: }


                      93 	.align	4

                      94 	.align	4

                      95 CriticalSection_Done::

                      96 ;11: {


                      97 

0000000c e12fff1e*    98 	ret	

                      99 	.endf	CriticalSection_Done

                     100 	.align	4

                     101 

                     102 ;cs	none	param

                     103 

                     104 	.section ".bss","awb"

                     105 .L142:

                     106 	.data

                     107 	.text

                     108 	.align	4

                     109 

                     110 	.data

                     111 	.ghsnote version,6


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_11o1.s
                     112 	.ghsnote tools,3

                     113 	.ghsnote options,0

                     114 	.text

                     115 	.align	4

