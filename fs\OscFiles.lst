                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=OscFiles.c -o fs\gh_b4o1.o -list=fs/OscFiles.lst C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
Source File: OscFiles.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile fs/OscFiles.c

                      11 ;		-o fs/OscFiles.o

                      12 ;Source File:   fs/OscFiles.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:55 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "OscFiles.h"


                      23 ;2: #include "OscDescr.h"


                      24 ;3: #include "../pwin_access.h"


                      25 ;4: #include <string.h>


                      26 ;5: #include <stdlib.h>


                      27 ;6: #include "../tlsf/tlsf.h"


                      28 ;7: #include "OscConverter.h"


                      29 ;8: #include "platform_critical_section.h"


                      30 ;9: #define OSC_POOL_SIZE	(8 * 1024 * 1024)


                      31 ;10: //! пул для конвертилки осцилограммы


                      32 ;11: tlsf_t osc_tlsf;


                      33 ;12: //! для malloc,free


                      34 ;13: #pragma alignvar (4)


                      35 ;14: CriticalSection tlsfCS;


                      36 ;15: 


                      37 ;16: bool OSCFS_init(void)


                      38 ;17: {


                      39 ;18: 	void *osc_pool = malloc(OSC_POOL_SIZE);


                      40 ;19: 	


                      41 ;20: 	if (!osc_pool)


                      42 ;21: 	{


                      43 ;22: 		return FALSE;


                      44 ;23: 	}


                      45 ;24: 


                      46 ;25: 	osc_tlsf = tlsf_create_with_pool(osc_pool, OSC_POOL_SIZE);


                      47 ;26: 	if (!osc_tlsf)


                      48 ;27: 	{


                      49 ;28: 		return FALSE;


                      50 ;29: 	}



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
                      51 ;30: 


                      52 ;31: 	CriticalSection_Init(&tlsfCS);


                      53 ;32: 	


                      54 ;33: 


                      55 ;34: 	if (!OscConverter_init())


                      56 ;35: 	{


                      57 ;36: 		return FALSE;


                      58 ;37: 	}


                      59 ;38: 	// инициализация описаний


                      60 ;39: 	if (!OSCDescr_init())


                      61 ;40: 	{


                      62 ;41: 		return FALSE;


                      63 ;42: 	}


                      64 ;43: 	// инициализация заголовка


                      65 ;44: 	if (!OSCInfo_init())


                      66 ;45: 	{


                      67 ;46: 		return FALSE;


                      68 ;47: 	}


                      69 ;48: 


                      70 ;49: 	return TRUE;


                      71 ;50: }


                      72 ;51: 


                      73 ;52: void * OscFiles_malloc(size_t size)


                      74 ;53: {


                      75 ;54: 	void *result;


                      76 ;55: 	CriticalSection_Lock(&tlsfCS);


                      77 ;56: 	result = tlsf_malloc(osc_tlsf, size);


                      78 ;57: 	//result = malloc(size);


                      79 ;58: 	CriticalSection_Unlock(&tlsfCS);


                      80 ;59: 	return result;


                      81 ;60: }


                      82 ;61: void * OscFiles_realloc(void *p, size_t size)


                      83 ;62: {


                      84 ;63: 	void *result;


                      85 ;64: 	CriticalSection_Lock(&tlsfCS);


                      86 ;65: 	result = tlsf_realloc(osc_tlsf, p, size);


                      87 ;66: 	//result = realloc(p, size);


                      88 ;67: 	CriticalSection_Unlock(&tlsfCS);


                      89 ;68: 


                      90 ;69: 	return result;


                      91 ;70: }


                      92 ;71: 


                      93 ;72: void OscFiles_free(void *p)


                      94 ;73: {


                      95 ;74: 	CriticalSection_Lock(&tlsfCS);


                      96 ;75: 	tlsf_free(osc_tlsf, p);


                      97 ;76: 	//free(p);


                      98 ;77: 	CriticalSection_Unlock(&tlsfCS);


                      99 ;78: }


                     100 ;79: 


                     101 ;80: static FNameErrCode fillFindData(FSFindData* findData, BufferView* fnameBuf,


                     102 ;81: 	int findResult, PWFileInfo* fileInfo)


                     103 ;82: {


                     104 ;83: 	//Сюда запишем номер осциллограммы в текстовом виде


                     105 ;84:     char* pNum;


                     106 ;85: 	if (findResult == 0)


                     107 ;86: 	{


                     108 ;87: 		//Больше нет осциллограмм


                     109 ;88: 		return FNAME_NOT_FOUND;


                     110 ;89: 	}


                     111 ;90: 	else if (findResult < 0)



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
                     112 ;91: 	{


                     113 ;92: 		//Ошибка


                     114 ;93: 		return FNAME_ERROR;


                     115 ;94: 	}


                     116 ;95: 


                     117 ;96: 	findData->fileIndex = findResult;


                     118 ;97: 	findData->attr.fileSize = 0;


                     119 ;98: 	findData->attr.time = fileInfo->t;


                     120 ;99: 	findData->attr.ms = fileInfo->ms;


                     121 ;100: 


                     122 ;101: 	//Пишем имя


                     123 ;102: 	if (!BufferView_writeStr(fnameBuf, "osc"))


                     124 ;103: 	{


                     125 ;104: 		return FNAME_BUF_ERROR;


                     126 ;105: 	}


                     127 ;106:     if (!BufferView_alloc(fnameBuf, 11, (uint8_t**)&pNum))


                     128 ;107: 	{


                     129 ;108: 		return FNAME_BUF_ERROR;


                     130 ;109: 	}


                     131 ;110: 	_itoa(fileInfo->name, pNum, 10);


                     132 ;111: 	fnameBuf->pos += strlen(pNum);


                     133 ;112: 	if (!BufferView_writeStr(fnameBuf, ".zip"))


                     134 ;113: 	{


                     135 ;114: 		return FNAME_BUF_ERROR;


                     136 ;115: 	}


                     137 ;116: 	return FNAME_OK;


                     138 ;117: }


                     139 ;118: 


                     140 ;119: FNameErrCode OSCFS_findFirst(StringView* startFileName, FSFindData* findData,


                     141 ;120: 	BufferView* fnameBuf)


                     142 ;121: {


                     143 ;122: 	PWFileInfo fileInfo;


                     144 ;123: 	int findResult;	


                     145 ;124: 	int oscNum = 0;


                     146 ;125: 	if (startFileName != NULL) {


                     147 ;126: 		//Пропускаем "osc", а все оставшиеся цифры интерпертируем как номер


                     148 ;127:         oscNum = atoi((char*)startFileName->p + 3);


                     149 ;128: 		if (oscNum < 1)


                     150 ;129: 		{


                     151 ;130: 			return FNAME_NOT_FOUND;


                     152 ;131: 		}


                     153 ;132: 	}


                     154 ;133: 


                     155 ;134: 	findResult = pwaOscFindFirst(oscNum, &fileInfo);		


                     156 ;135: 	return fillFindData(findData, fnameBuf, findResult, &fileInfo);


                     157 ;136: }


                     158 ;137: 


                     159 ;138: FNameErrCode OSCFS_findNext(FSFindData* findData, BufferView* fnameBuf)


                     160 ;139: {


                     161 ;140: 	PWFileInfo fileInfo;


                     162 ;141: 	int findResult = pwaOscFindNext(findData->fileIndex, &fileInfo);		


                     163 ;142: 	return fillFindData(findData, fnameBuf, findResult, &fileInfo);


                     164 ;143: }


                     165 ;144: 


                     166 ;145: void OSCFS_findClose(FSFindData *findData)


                     167 ;146: {


                     168 ;147: 	pwaOscFindClose();


                     169 ;148: }


                     170 ;149: 


                     171 ;150: //! читает осцилограмму в OscWriteBuffer согласно его размеру


                     172 ;151: static int readOsc(FRSM *frsm, int offset, OscWriteBuffer *wb)



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
                     173 ;152: {


                     174 ;153: 	PWFileInfo *fileInfo;


                     175 ;154: 	int result;


                     176 ;155: 	unsigned char *pData;


                     177 ;156: 	int readSize;


                     178 ;157: 


                     179 ;158: 	fileInfo = &frsm->readOscContext->fileInfo;


                     180 ;159: 


                     181 ;160: 	OscWriteBuffer_reset(wb);


                     182 ;161: 	pData = OscWriteBuffer_data(wb);


                     183 ;162: 	readSize = OscWriteBuffer_size(wb);


                     184 ;163: 


                     185 ;164: 	// читается сколько влезет в буфер


                     186 ;165: 	result = pwaOscOscRead(fileInfo, offset, pData, readSize);


                     187 ;166: 	if (result > 0)


                     188 ;167: 	{


                     189 ;168: 		// успешно прочитали, новый размер


                     190 ;169: 		if (!OscWriteBuffer_resize(wb, result))


                     191 ;170: 		{


                     192 ;171: 			return -1;


                     193 ;172: 		}


                     194 ;173: 	}


                     195 ;174: 


                     196 ;175: 	return result;


                     197 ;176: }


                     198 ;177: 


                     199 ;178: //! из имени файла в номер осцилограмма


                     200 ;179: static int fileNameToOscNum(StringView* fileName)


                     201 

                     202 ;184: }


                     203 

                     204 ;185: 


                     205 ;186: //! создает контекст чтения осцилограммы


                     206 ;187: static OscReadFileContext *createOscReadContext(StringView* fileName)


                     207 

                     208 ;207: }


                     209 

                     210 	.text

                     211 	.align	4

                     212 OSCFS_init::

00000000 e92d4000    213 	stmfd	[sp]!,{lr}

00000004 e3a00880    214 	mov	r0,1<<23

00000008 eb000000*   215 	bl	malloc

0000000c e3500000    216 	cmp	r0,0

00000010 0a000010    217 	beq	.L72

00000014 e3a01880    218 	mov	r1,1<<23

00000018 eb000000*   219 	bl	tlsf_create_with_pool

0000001c e59f11e8*   220 	ldr	r1,.L180

00000020 e3500000    221 	cmp	r0,0

00000024 e5810000    222 	str	r0,[r1]

00000028 0a00000a    223 	beq	.L72

0000002c e59f01dc*   224 	ldr	r0,.L181

00000030 eb000000*   225 	bl	CriticalSection_Init

00000034 eb000000*   226 	bl	OscConverter_init

00000038 e3500000    227 	cmp	r0,0

0000003c 0a000005    228 	beq	.L72

00000040 eb000000*   229 	bl	OSCDescr_init

00000044 e3500000    230 	cmp	r0,0

00000048 0a000002    231 	beq	.L72

0000004c eb000000*   232 	bl	OSCInfo_init

00000050 e3500000    233 	cmp	r0,0


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
00000054 13a00001    234 	movne	r0,1

                     235 .L72:

00000058 03a00000    236 	moveq	r0,0

                     237 .L57:

0000005c e8bd8000    238 	ldmfd	[sp]!,{pc}

                     239 	.endf	OSCFS_init

                     240 	.align	4

                     241 ;osc_pool	r1	local

                     242 

                     243 	.section ".bss","awb"

                     244 .L154:

                     245 	.data

                     246 	.text

                     247 

                     248 

                     249 	.align	4

                     250 	.align	4

                     251 OscFiles_malloc::

00000060 e92d4010    252 	stmfd	[sp]!,{r4,lr}

00000064 e1a04000    253 	mov	r4,r0

00000068 e59f01a0*   254 	ldr	r0,.L181

0000006c eb000000*   255 	bl	CriticalSection_Lock

00000070 e1a01004    256 	mov	r1,r4

00000074 e59f4190*   257 	ldr	r4,.L180

00000078 e5940000    258 	ldr	r0,[r4]

0000007c eb000000*   259 	bl	tlsf_malloc

00000080 e1a04000    260 	mov	r4,r0

00000084 e59f0184*   261 	ldr	r0,.L181

00000088 eb000000*   262 	bl	CriticalSection_Unlock

0000008c e1a00004    263 	mov	r0,r4

00000090 e8bd8010    264 	ldmfd	[sp]!,{r4,pc}

                     265 	.endf	OscFiles_malloc

                     266 	.align	4

                     267 

                     268 ;size	r4	param

                     269 

                     270 	.section ".bss","awb"

                     271 .L206:

                     272 	.data

                     273 	.text

                     274 

                     275 

                     276 	.align	4

                     277 	.align	4

                     278 OscFiles_realloc::

00000094 e92d4030    279 	stmfd	[sp]!,{r4-r5,lr}

00000098 e1a04000    280 	mov	r4,r0

0000009c e59f016c*   281 	ldr	r0,.L181

000000a0 e1a05001    282 	mov	r5,r1

000000a4 eb000000*   283 	bl	CriticalSection_Lock

000000a8 e1a01004    284 	mov	r1,r4

000000ac e59f4158*   285 	ldr	r4,.L180

000000b0 e5940000    286 	ldr	r0,[r4]

000000b4 e1a02005    287 	mov	r2,r5

000000b8 eb000000*   288 	bl	tlsf_realloc

000000bc e1a04000    289 	mov	r4,r0

000000c0 e59f0148*   290 	ldr	r0,.L181

000000c4 eb000000*   291 	bl	CriticalSection_Unlock

000000c8 e1a00004    292 	mov	r0,r4

000000cc e8bd8030    293 	ldmfd	[sp]!,{r4-r5,pc}

                     294 	.endf	OscFiles_realloc


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
                     295 	.align	4

                     296 

                     297 ;p	r4	param

                     298 ;size	r5	param

                     299 

                     300 	.section ".bss","awb"

                     301 .L238:

                     302 	.data

                     303 	.text

                     304 

                     305 

                     306 	.align	4

                     307 	.align	4

                     308 OscFiles_free::

000000d0 e92d4010    309 	stmfd	[sp]!,{r4,lr}

000000d4 e1a04000    310 	mov	r4,r0

000000d8 e59f0130*   311 	ldr	r0,.L181

000000dc eb000000*   312 	bl	CriticalSection_Lock

000000e0 e1a01004    313 	mov	r1,r4

000000e4 e59f4120*   314 	ldr	r4,.L180

000000e8 e5940000    315 	ldr	r0,[r4]

000000ec eb000000*   316 	bl	tlsf_free

000000f0 e59f0118*   317 	ldr	r0,.L181

000000f4 e8bd4010    318 	ldmfd	[sp]!,{r4,lr}

000000f8 ea000000*   319 	b	CriticalSection_Unlock

                     320 	.endf	OscFiles_free

                     321 	.align	4

                     322 

                     323 ;p	r4	param

                     324 

                     325 	.section ".bss","awb"

                     326 .L270:

                     327 	.data

                     328 	.text

                     329 

                     330 

                     331 	.align	4

                     332 	.align	4

                     333 fillFindData:

000000fc e92d4030    334 	stmfd	[sp]!,{r4-r5,lr}

00000100 e24dd004    335 	sub	sp,sp,4

00000104 e3520000    336 	cmp	r2,0

00000108 03a00001    337 	moveq	r0,1

0000010c 0a000025    338 	beq	.L277

00000110 e1a04001    339 	mov	r4,r1

00000114 e1a05003    340 	mov	r5,r3

00000118 e3520000    341 	cmp	r2,0

0000011c b3a00003    342 	movlt	r0,3

00000120 ba000020    343 	blt	.L277

00000124 e5802004    344 	str	r2,[r0,4]

00000128 e3a01000    345 	mov	r1,0

0000012c e5801014    346 	str	r1,[r0,20]

00000130 e5951008    347 	ldr	r1,[r5,8]

00000134 e5801018    348 	str	r1,[r0,24]

00000138 e5951010    349 	ldr	r1,[r5,16]

0000013c e580101c    350 	str	r1,[r0,28]

00000140 e28f1000*   351 	adr	r1,.L427

00000144 e1a00004    352 	mov	r0,r4

00000148 eb000000*   353 	bl	BufferView_writeStr

0000014c e3500000    354 	cmp	r0,0

00000150 0a000013    355 	beq	.L293


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
00000154 e1a0200d    356 	mov	r2,sp

00000158 e1a00004    357 	mov	r0,r4

0000015c e3a0100b    358 	mov	r1,11

00000160 eb000000*   359 	bl	BufferView_alloc

00000164 e3500000    360 	cmp	r0,0

00000168 0a00000d    361 	beq	.L293

0000016c e59d1000    362 	ldr	r1,[sp]

00000170 e5950000    363 	ldr	r0,[r5]

00000174 e3a0200a    364 	mov	r2,10

00000178 eb000000*   365 	bl	_itoa

0000017c e59d0000    366 	ldr	r0,[sp]

00000180 eb000000*   367 	bl	strlen

00000184 e5941004    368 	ldr	r1,[r4,4]

00000188 e0811000    369 	add	r1,r1,r0

0000018c e5841004    370 	str	r1,[r4,4]

00000190 e28f1000*   371 	adr	r1,.L428

00000194 e1a00004    372 	mov	r0,r4

00000198 eb000000*   373 	bl	BufferView_writeStr

0000019c e3500000    374 	cmp	r0,0

000001a0 13a00000    375 	movne	r0,0

                     376 .L293:

000001a4 03a00002    377 	moveq	r0,2

                     378 .L277:

000001a8 e28dd004    379 	add	sp,sp,4

000001ac e8bd4030    380 	ldmfd	[sp]!,{r4-r5,lr}

000001b0 e12fff1e*   381 	ret	

                     382 	.endf	fillFindData

                     383 	.align	4

                     384 ;pNum	[sp]	local

                     385 ;.L394	.L398	static

                     386 ;.L395	.L399	static

                     387 

                     388 ;findData	r0	param

                     389 ;fnameBuf	r4	param

                     390 ;findResult	r2	param

                     391 ;fileInfo	r5	param

                     392 

                     393 	.section ".bss","awb"

                     394 .L393:

                     395 	.data

                     396 	.text

                     397 

                     398 

                     399 	.align	4

                     400 	.align	4

                     401 OSCFS_findFirst::

000001b4 e92d4030    402 	stmfd	[sp]!,{r4-r5,lr}

000001b8 e24dd01c    403 	sub	sp,sp,28

000001bc e1a04001    404 	mov	r4,r1

000001c0 e1a05002    405 	mov	r5,r2

000001c4 e1b03000    406 	movs	r3,r0

000001c8 e3a00000    407 	mov	r0,0

000001cc 0a000005    408 	beq	.L431

000001d0 e5930004    409 	ldr	r0,[r3,4]

000001d4 e2800003    410 	add	r0,r0,3

000001d8 eb000000*   411 	bl	atoi

000001dc e3500000    412 	cmp	r0,0

000001e0 d3a00001    413 	movle	r0,1

000001e4 da000006    414 	ble	.L429

                     415 .L431:

000001e8 e1a0100d    416 	mov	r1,sp


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
000001ec eb000000*   417 	bl	pwaOscFindFirst

000001f0 e1a0300d    418 	mov	r3,sp

000001f4 e1a01005    419 	mov	r1,r5

000001f8 e1a02000    420 	mov	r2,r0

000001fc e1a00004    421 	mov	r0,r4

00000200 ebffffbd*   422 	bl	fillFindData

                     423 .L429:

00000204 e28dd01c    424 	add	sp,sp,28

00000208 e8bd8030    425 	ldmfd	[sp]!,{r4-r5,pc}

                     426 	.endf	OSCFS_findFirst

                     427 	.align	4

                     428 .L180:

0000020c 00000000*   429 	.data.w	osc_tlsf

                     430 	.type	.L180,$object

                     431 	.size	.L180,4

                     432 

                     433 .L181:

00000210 00000000*   434 	.data.w	tlsfCS

                     435 	.type	.L181,$object

                     436 	.size	.L181,4

                     437 

                     438 .L427:

                     439 ;	"osc\000"

00000214 0063736f    440 	.data.b	111,115,99,0

                     441 	.align 4

                     442 

                     443 	.type	.L427,$object

                     444 	.size	.L427,4

                     445 

                     446 .L428:

                     447 ;	".zip\000"

00000218 70697a2e    448 	.data.b	46,122,105,112

0000021c 00         449 	.data.b	0

0000021d 000000     450 	.align 4

                     451 

                     452 	.type	.L428,$object

                     453 	.size	.L428,4

                     454 

                     455 	.align	4

                     456 ;fileInfo	[sp]	local

                     457 ;oscNum	r0	local

                     458 

                     459 ;startFileName	r3	param

                     460 ;findData	r4	param

                     461 ;fnameBuf	r5	param

                     462 

                     463 	.section ".bss","awb"

                     464 .L484:

                     465 	.data

                     466 	.text

                     467 

                     468 

                     469 	.align	4

                     470 	.align	4

                     471 OSCFS_findNext::

00000220 e92d4030    472 	stmfd	[sp]!,{r4-r5,lr}

00000224 e1a05001    473 	mov	r5,r1

00000228 e24dd01c    474 	sub	sp,sp,28

0000022c e1a04000    475 	mov	r4,r0

00000230 e5940004    476 	ldr	r0,[r4,4]

00000234 e1a0100d    477 	mov	r1,sp


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
00000238 eb000000*   478 	bl	pwaOscFindNext

0000023c e1a0300d    479 	mov	r3,sp

00000240 e1a01005    480 	mov	r1,r5

00000244 e1a02000    481 	mov	r2,r0

00000248 e1a00004    482 	mov	r0,r4

0000024c ebffffaa*   483 	bl	fillFindData

00000250 e28dd01c    484 	add	sp,sp,28

00000254 e8bd8030    485 	ldmfd	[sp]!,{r4-r5,pc}

                     486 	.endf	OSCFS_findNext

                     487 	.align	4

                     488 ;fileInfo	[sp]	local

                     489 

                     490 ;findData	r4	param

                     491 ;fnameBuf	r5	param

                     492 

                     493 	.section ".bss","awb"

                     494 .L526:

                     495 	.data

                     496 	.text

                     497 

                     498 

                     499 	.align	4

                     500 	.align	4

                     501 OSCFS_findClose::

00000258 ea000000*   502 	b	pwaOscFindClose

                     503 	.endf	OSCFS_findClose

                     504 	.align	4

                     505 

                     506 ;findData	none	param

                     507 

                     508 	.section ".bss","awb"

                     509 .L558:

                     510 	.data

                     511 	.text

                     512 

                     513 

                     514 	.align	4

                     515 	.align	4

                     516 readOsc:

0000025c e92d40f0    517 	stmfd	[sp]!,{r4-r7,lr}

00000260 e1a07001    518 	mov	r7,r1

00000264 e1a05002    519 	mov	r5,r2

00000268 e5904018    520 	ldr	r4,[r0,24]

0000026c e1a00005    521 	mov	r0,r5

00000270 eb000000*   522 	bl	OscWriteBuffer_reset

00000274 e1a00005    523 	mov	r0,r5

00000278 eb000000*   524 	bl	OscWriteBuffer_data

0000027c e1a06000    525 	mov	r6,r0

00000280 e1a00005    526 	mov	r0,r5

00000284 eb000000*   527 	bl	OscWriteBuffer_size

00000288 e1a02006    528 	mov	r2,r6

0000028c e1a01007    529 	mov	r1,r7

00000290 e1a03000    530 	mov	r3,r0

00000294 e1a00004    531 	mov	r0,r4

00000298 eb000000*   532 	bl	pwaOscOscRead

0000029c e2504000    533 	subs	r4,r0,0

000002a0 da000005    534 	ble	.L567

000002a4 e1a01004    535 	mov	r1,r4

000002a8 e1a00005    536 	mov	r0,r5

000002ac eb000000*   537 	bl	OscWriteBuffer_resize

000002b0 e3500000    538 	cmp	r0,0


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
000002b4 03e00000    539 	mvneq	r0,0

000002b8 0a000000    540 	beq	.L565

                     541 .L567:

000002bc e1a00004    542 	mov	r0,r4

                     543 .L565:

000002c0 e8bd40f0    544 	ldmfd	[sp]!,{r4-r7,lr}

000002c4 e12fff1e*   545 	ret	

                     546 	.endf	readOsc

                     547 	.align	4

                     548 ;fileInfo	r4	local

                     549 ;result	r4	local

                     550 ;pData	r6	local

                     551 

                     552 ;frsm	r0	param

                     553 ;offset	r7	param

                     554 ;wb	r5	param

                     555 

                     556 	.section ".bss","awb"

                     557 .L612:

                     558 	.data

                     559 	.text

                     560 

                     561 

                     562 ;208: 


                     563 ;209: //! освобождает контекст чтения, всегда возвращает FALSE


                     564 ;210: static bool freeOscReadContext(FRSM* frsm)


                     565 	.align	4

                     566 	.align	4

                     567 freeOscReadContext:

000002c8 e92d4030    568 	stmfd	[sp]!,{r4-r5,lr}

000002cc e1a05000    569 	mov	r5,r0

                     570 ;211: {


                     571 

                     572 ;212: 	OscReadFileContext *readFileContext = frsm->readOscContext;


                     573 

000002d0 e5950018    574 	ldr	r0,[r5,24]

000002d4 e1a04000    575 	mov	r4,r0

                     576 ;213: 	// т.к. oscInfo создается createOscReadContext, освобождаем здесь


                     577 ;214: 	if (readFileContext->oscInfo)


                     578 

000002d8 e594102c    579 	ldr	r1,[r4,44]

000002dc e3510000    580 	cmp	r1,0

000002e0 0a000004    581 	beq	.L631

                     582 ;215: 	{


                     583 

                     584 ;216: 		OSCInfo_destroy(readFileContext->oscInfo);


                     585 

000002e4 e1a00001    586 	mov	r0,r1

000002e8 eb000000*   587 	bl	OSCInfo_destroy

                     588 ;217: 		readFileContext->oscInfo = NULL;


                     589 

000002ec e3a00000    590 	mov	r0,0

000002f0 e584002c    591 	str	r0,[r4,44]

000002f4 e5950018    592 	ldr	r0,[r5,24]

                     593 .L631:

                     594 ;218: 	}


                     595 ;219: 


                     596 ;220: 	if (frsm->readOscContext)


                     597 

000002f8 e3500000    598 	cmp	r0,0

                     599 ;221: 	{



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
                     600 

                     601 ;222: 		OscReadFileContext_destroy(frsm->readOscContext);


                     602 

000002fc 1b000000*   603 	blne	OscReadFileContext_destroy

                     604 ;223: 	}


                     605 ;224: 


                     606 ;225: 	return FALSE;


                     607 

00000300 e3a00000    608 	mov	r0,0

00000304 e8bd4030    609 	ldmfd	[sp]!,{r4-r5,lr}

00000308 e12fff1e*   610 	ret	

                     611 	.endf	freeOscReadContext

                     612 	.align	4

                     613 ;readFileContext	r4	local

                     614 

                     615 ;frsm	r5	param

                     616 

                     617 	.section ".bss","awb"

                     618 .L681:

                     619 	.data

                     620 	.text

                     621 

                     622 ;226: }


                     623 

                     624 ;227: bool OSCFS_openFile(StringView* fileName, FRSM* frsm, FSFileAttr* attr)


                     625 	.align	4

                     626 	.align	4

                     627 OSCFS_openFile::

0000030c e92d40f0    628 	stmfd	[sp]!,{r4-r7,lr}

                     629 ;228: {


                     630 

                     631 ;229: 	int readSize;


                     632 ;230: 	OscWriteBuffer *headerBuf;


                     633 ;231: 	OscWriteBuffer *contentBuf;


                     634 ;232: 	OSCInfoStruct *oscInfo;


                     635 ;233: 


                     636 ;234: 	// контекст чтения


                     637 ;235: 	frsm->readOscContext = createOscReadContext(fileName);


                     638 

                     639 ;188: {


                     640 

                     641 ;189: 	int findResult;


                     642 ;190: 	PWFileInfo fileInfo;


                     643 ;191: 


                     644 ;192: 	int oscNum = fileNameToOscNum(fileName);


                     645 

                     646 ;180: {


                     647 

                     648 ;181: 	// приходит что-то вроде osc10.zip


                     649 ;182: 	// срезается osc, а .zip не конвертится


                     650 ;183: 	return atoi((char*)fileName->p + 3);


                     651 

00000310 e1a04001    652 	mov	r4,r1

00000314 e24dd01c    653 	sub	sp,sp,28

00000318 e5900004    654 	ldr	r0,[r0,4]

0000031c e1a05002    655 	mov	r5,r2

00000320 e2800003    656 	add	r0,r0,3

00000324 eb000000*   657 	bl	atoi

                     658 ;193: 	if (oscNum < 1)


                     659 

00000328 e3500000    660 	cmp	r0,0


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
0000032c da000004    661 	ble	.L707

                     662 ;194: 	{


                     663 

                     664 ;195: 		return NULL;


                     665 

                     666 ;196: 	}


                     667 ;197: 


                     668 ;198: 	// ищем осцилограмму с соответствущим именем


                     669 ;199: 	findResult = pwaOscFindFirst(oscNum, &fileInfo);


                     670 

00000330 e1a0100d    671 	mov	r1,sp

00000334 eb000000*   672 	bl	pwaOscFindFirst

                     673 ;200: 	if (findResult <= 0)


                     674 

00000338 e1a06000    675 	mov	r6,r0

0000033c eb000000*   676 	bl	pwaOscFindClose

00000340 e3560000    677 	cmp	r6,0

                     678 .L707:

                     679 ;201: 	{


                     680 

                     681 ;202: 		pwaOscFindClose();


                     682 

                     683 ;203: 		return NULL;


                     684 

00000344 d3a00000    685 	movle	r0,0

00000348 d5840018    686 	strle	r0,[r4,24]

                     687 ;236: 	if (!frsm->readOscContext)


                     688 

0000034c da000004    689 	ble	.L711

                     690 .L708:

                     691 ;204: 	}


                     692 ;205: 	pwaOscFindClose();


                     693 

                     694 ;206: 	return OscReadFileContext_create(&fileInfo);


                     695 

00000350 e1a0000d    696 	mov	r0,sp

00000354 eb000000*   697 	bl	OscReadFileContext_create

00000358 e5840018    698 	str	r0,[r4,24]

                     699 ;236: 	if (!frsm->readOscContext)


                     700 

0000035c e3500000    701 	cmp	r0,0

00000360 1a000001    702 	bne	.L710

                     703 .L711:

                     704 ;237: 	{


                     705 

                     706 ;238: 		return FALSE;


                     707 

00000364 e20000ff    708 	and	r0,r0,255

00000368 ea00002d    709 	b	.L700

                     710 .L710:

                     711 ;239: 	}


                     712 ;240: 	


                     713 ;241: 	// чтение заголовка об осcилограмме


                     714 ;242: 	// указатель на временный буфер


                     715 ;243: 	headerBuf = OSCInfo_lockHeaderBuf();


                     716 

0000036c eb000000*   717 	bl	OSCInfo_lockHeaderBuf

00000370 e1a06000    718 	mov	r6,r0

                     719 ;244: 	readSize = readOsc(frsm, 0, headerBuf);


                     720 

00000374 e1a02006    721 	mov	r2,r6


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
00000378 e1a00004    722 	mov	r0,r4

0000037c e3a01000    723 	mov	r1,0

00000380 ebffffb5*   724 	bl	readOsc

                     725 ;245: 	if ( readSize <= 0)


                     726 

00000384 e3500000    727 	cmp	r0,0

00000388 ca000003    728 	bgt	.L713

                     729 ;246: 	{


                     730 

                     731 ;247: 		OSCInfo_unlockHeaderBuf();


                     732 

0000038c eb000000*   733 	bl	OSCInfo_unlockHeaderBuf

                     734 ;248: 		return freeOscReadContext(frsm);


                     735 

00000390 e1a00004    736 	mov	r0,r4

00000394 ebffffcb*   737 	bl	freeOscReadContext

00000398 ea000021    738 	b	.L700

                     739 .L713:

                     740 ;249: 	}


                     741 ;250: 


                     742 ;251: 	// инициализация заголовка осцилограммы


                     743 ;252: 	oscInfo = OSCInfo_create(headerBuf);


                     744 

0000039c e1a00006    745 	mov	r0,r6

000003a0 eb000000*   746 	bl	OSCInfo_create

000003a4 e1a06000    747 	mov	r6,r0

                     748 ;253: 	OSCInfo_unlockHeaderBuf();


                     749 

000003a8 eb000000*   750 	bl	OSCInfo_unlockHeaderBuf

                     751 ;254: 	if (!oscInfo)


                     752 

000003ac e3560000    753 	cmp	r6,0

000003b0 0a000010    754 	beq	.L723

                     755 ;255: 	{


                     756 

                     757 ;256: 		return freeOscReadContext(frsm);


                     758 

                     759 ;257: 	}


                     760 ;258: 	frsm->readOscContext->oscInfo = oscInfo;


                     761 

000003b4 e5940018    762 	ldr	r0,[r4,24]

000003b8 e580602c    763 	str	r6,[r0,44]

                     764 ;259: 


                     765 ;260: 	


                     766 ;261: 	// инициализация состава


                     767 ;262: 	// буфер под состав


                     768 ;263: 	contentBuf = OSCInfo_getBufferContent(oscInfo);


                     769 

000003bc e1a00006    770 	mov	r0,r6

000003c0 eb000000*   771 	bl	OSCInfo_getBufferContent

000003c4 e1a07000    772 	mov	r7,r0

                     773 ;264: 	readSize = readOsc(frsm, OSCInfo_getHeaderSize(oscInfo), contentBuf);


                     774 

000003c8 e1a00006    775 	mov	r0,r6

000003cc eb000000*   776 	bl	OSCInfo_getHeaderSize

000003d0 e1a02007    777 	mov	r2,r7

000003d4 e1a01000    778 	mov	r1,r0

000003d8 e1a00004    779 	mov	r0,r4

000003dc ebffff9e*   780 	bl	readOsc

                     781 ;265: 	if ( readSize <= 0)


                     782 


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
000003e0 e3500000    783 	cmp	r0,0

000003e4 da000003    784 	ble	.L723

                     785 ;266: 	{


                     786 

                     787 ;267: 		return freeOscReadContext(frsm);


                     788 

                     789 ;268: 	}


                     790 ;269: 


                     791 ;270: 	// состав осцилограммы


                     792 ;271: 	// oscInfo содержит указатель на contentBuf, поэтому он не передается


                     793 ;272: 	if (!OSCInfo_initContent(oscInfo))


                     794 

000003e8 e1a00006    795 	mov	r0,r6

000003ec eb000000*   796 	bl	OSCInfo_initContent

000003f0 e3500000    797 	cmp	r0,0

000003f4 1a000002    798 	bne	.L722

                     799 .L723:

                     800 ;273: 	{


                     801 

                     802 ;274: 		return freeOscReadContext(frsm);


                     803 

000003f8 e1a00004    804 	mov	r0,r4

000003fc ebffffb1*   805 	bl	freeOscReadContext

00000400 ea000007    806 	b	.L700

                     807 .L722:

                     808 ;275: 	}


                     809 ;276: 	attr->fileSize = 0;


                     810 

00000404 e3a00000    811 	mov	r0,0

00000408 e5850000    812 	str	r0,[r5]

                     813 ;277: 	attr->time = frsm->readOscContext->fileInfo.t;


                     814 

0000040c e5940018    815 	ldr	r0,[r4,24]

00000410 e5901008    816 	ldr	r1,[r0,8]

00000414 e5851004    817 	str	r1,[r5,4]

                     818 ;278: 	attr->ms = frsm->readOscContext->fileInfo.ms;


                     819 

00000418 e5900010    820 	ldr	r0,[r0,16]

0000041c e5850008    821 	str	r0,[r5,8]

                     822 ;279: 	return TRUE;


                     823 

00000420 e3a00001    824 	mov	r0,1

                     825 .L700:

00000424 e28dd01c    826 	add	sp,sp,28

00000428 e8bd80f0    827 	ldmfd	[sp]!,{r4-r7,pc}

                     828 	.endf	OSCFS_openFile

                     829 	.align	4

                     830 ;headerBuf	r6	local

                     831 ;contentBuf	r7	local

                     832 ;oscInfo	r6	local

                     833 ;fileInfo	[sp]	local

                     834 ;oscNum	r1	local

                     835 

                     836 ;fileName	r0	param

                     837 ;frsm	r4	param

                     838 ;attr	r5	param

                     839 

                     840 	.section ".bss","awb"

                     841 .L905:

                     842 	.data

                     843 	.text


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
                     844 

                     845 ;280: }


                     846 

                     847 ;281: 


                     848 ;282: bool OSCFS_closeFile(FRSM* frsm)


                     849 	.align	4

                     850 	.align	4

                     851 OSCFS_closeFile::

0000042c e92d4000    852 	stmfd	[sp]!,{lr}

                     853 ;283: {


                     854 

                     855 ;284: 	freeOscReadContext(frsm);


                     856 

00000430 ebffffa4*   857 	bl	freeOscReadContext

                     858 ;285: 	return TRUE;


                     859 

00000434 e3a00001    860 	mov	r0,1

00000438 e8bd8000    861 	ldmfd	[sp]!,{pc}

                     862 	.endf	OSCFS_closeFile

                     863 	.align	4

                     864 

                     865 ;frsm	none	param

                     866 

                     867 	.section ".bss","awb"

                     868 .L958:

                     869 	.data

                     870 	.text

                     871 

                     872 ;286: }


                     873 

                     874 ;287: 


                     875 ;288: bool OSCFS_readFile(FRSM* frsm, BufferView* readBuf, bool* moreFollows)


                     876 	.align	4

                     877 	.align	4

                     878 OSCFS_readFile::

0000043c e92d4ff0    879 	stmfd	[sp]!,{r4-fp,lr}

                     880 ;289: {


                     881 

                     882 ;290: 	OscReadFileContext *readFileContext = frsm->readOscContext;


                     883 

00000440 e1a07001    884 	mov	r7,r1

00000444 e24dd008    885 	sub	sp,sp,8

00000448 e1a0b000    886 	mov	fp,r0

0000044c e59b0018    887 	ldr	r0,[fp,24]

00000450 e1a06002    888 	mov	r6,r2

00000454 e1b04000    889 	movs	r4,r0

                     890 ;291: 	OSCInfoStruct *oscInfo;


                     891 ;292: 	OscWriteBuffer *oscPeriodBuf;


                     892 ;293: 	int readSize;


                     893 ;294: 	size_t frameOffset;


                     894 ;295: 	unsigned int frameNum;


                     895 ;296: 	OscWriteBuffer *streamBuffer = &readFileContext->streamBuffer;


                     896 

00000458 1590502c    897 	ldrne	r5,[r0,44]

                     898 ;304: 	if (!oscInfo)


                     899 

0000045c e2848e4a    900 	add	r8,r4,0x04a0

                     901 ;297: 


                     902 ;298: 	if (!readFileContext)


                     903 

                     904 ;299: 	{



                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
                     905 

                     906 ;300: 		return FALSE;


                     907 

                     908 ;301: 	}


                     909 ;302: 


                     910 ;303: 	oscInfo = frsm->readOscContext->oscInfo;


                     911 

00000460 13550000    912 	cmpne	r5,0

00000464 0a000064    913 	beq	.L1012

                     914 ;305: 	{


                     915 

                     916 ;306: 		return FALSE;


                     917 

                     918 ;307: 	}


                     919 ;308: 


                     920 ;309: 	oscPeriodBuf = OSCInfo_getFrameBuffer(oscInfo);


                     921 

00000468 e1a00005    922 	mov	r0,r5

0000046c eb000000*   923 	bl	OSCInfo_getFrameBuffer

00000470 e1a09000    924 	mov	r9,r0

                     925 ;310: 	if (!oscPeriodBuf)


                     926 

00000474 e3500000    927 	cmp	r0,0

00000478 0a00005f    928 	beq	.L1012

                     929 ;311: 	{


                     930 

                     931 ;312: 		return FALSE;


                     932 

                     933 ;313: 	}


                     934 ;314: 


                     935 ;315: 	// пока есть буфер с предыдущего запуска - пишем его


                     936 ;316: 	if (!OscWriteBuffer_empty(streamBuffer))


                     937 

0000047c e1a00008    938 	mov	r0,r8

00000480 eb000000*   939 	bl	OscWriteBuffer_empty

00000484 e3500000    940 	cmp	r0,0

00000488 0a00005d    941 	beq	.L1000

                     942 ;317: 	{


                     943 

                     944 ;318: 		OscWriteBuffer_toBufferView(readBuf, streamBuffer);


                     945 

                     946 ;319: 		*moreFollows = TRUE;


                     947 

                     948 ;320: 		return TRUE;


                     949 

                     950 ;321: 	}


                     951 ;322: 


                     952 ;323: 


                     953 ;324: 	frameNum = readFileContext->curFrame;


                     954 

0000048c e594a030    955 	ldr	r10,[r4,48]

                     956 ;325: 	// все фреймы обработаны, можем обрабатывать остальные файлы


                     957 ;326: 	if (frameNum >= OSCInfo_getFrameCount(oscInfo))


                     958 

00000490 e1a00005    959 	mov	r0,r5

00000494 eb000000*   960 	bl	OSCInfo_getFrameCount

00000498 e15a0000    961 	cmp	r10,r0

0000049c 3a000019    962 	blo	.L979

                     963 ;327: 	{


                     964 

                     965 ;328: 		// cfg файл



                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
                     966 ;329: 		OscWriteBuffer *cfgBuffer = &readFileContext->cfgBuffer;


                     967 

000004a0 e2845e45    968 	add	r5,r4,0x0450

                     969 ;330: 		// hdr file


                     970 ;331: 		OscWriteBuffer *hdrBuffer = &readFileContext->hdrBuffer;


                     971 

000004a4 e2840e40    972 	add	r0,r4,1<<10

000004a8 e2804078    973 	add	r4,r0,120

                     974 ;332: 		if (!OscWriteBuffer_empty(cfgBuffer))


                     975 

000004ac e1a00005    976 	mov	r0,r5

000004b0 eb000000*   977 	bl	OscWriteBuffer_empty

000004b4 e3500000    978 	cmp	r0,0

000004b8 1a000005    979 	bne	.L981

                     980 ;333: 		{


                     981 

                     982 ;334: 			OscWriteBuffer_toBufferView(readBuf, cfgBuffer);


                     983 

000004bc e1a01005    984 	mov	r1,r5

000004c0 e1a00007    985 	mov	r0,r7

000004c4 eb000000*   986 	bl	OscWriteBuffer_toBufferView

                     987 ;335: 			*moreFollows = TRUE;


                     988 

000004c8 e3a00001    989 	mov	r0,1

000004cc e5c60000    990 	strb	r0,[r6]

                     991 ;336: 			return TRUE;


                     992 

000004d0 ea000050    993 	b	.L965

                     994 .L981:

                     995 ;337: 		}


                     996 ;338: 


                     997 ;339: 


                     998 ;340: 		if (!OscWriteBuffer_empty(hdrBuffer))


                     999 

000004d4 e1a00004   1000 	mov	r0,r4

000004d8 eb000000*  1001 	bl	OscWriteBuffer_empty

000004dc e3500000   1002 	cmp	r0,0

                    1003 ;345: 		}


                    1004 ;346: 


                    1005 ;347: 		*moreFollows = FALSE;


                    1006 

000004e0 13a00000   1007 	movne	r0,0

000004e4 15c60000   1008 	strneb	r0,[r6]

                    1009 ;348: 		return TRUE;


                    1010 

000004e8 13a00001   1011 	movne	r0,1

000004ec 1a000049   1012 	bne	.L965

                    1013 ;341: 		{


                    1014 

                    1015 ;342: 			OscWriteBuffer_toBufferView(readBuf, hdrBuffer);


                    1016 

000004f0 e1a01004   1017 	mov	r1,r4

000004f4 e1a00007   1018 	mov	r0,r7

000004f8 eb000000*  1019 	bl	OscWriteBuffer_toBufferView

                    1020 ;343: 			*moreFollows = TRUE;


                    1021 

000004fc e3a00001   1022 	mov	r0,1

00000500 e5c60000   1023 	strb	r0,[r6]

                    1024 ;344: 			return TRUE;


                    1025 

00000504 ea000043   1026 	b	.L965


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
                    1027 .L979:

                    1028 ;349: 	}


                    1029 ;350: 


                    1030 ;351: 	// смещение нового фрейма


                    1031 ;352: 	frameOffset = OSCInfo_getFrameOffset(oscInfo, frameNum);


                    1032 

00000508 e1a0100a   1033 	mov	r1,r10

0000050c e1a00005   1034 	mov	r0,r5

00000510 eb000000*  1035 	bl	OSCInfo_getFrameOffset

                    1036 ;353: 	// чтение фрейма


                    1037 ;354: 	readSize = readOsc(frsm, frameOffset, oscPeriodBuf);


                    1038 

00000514 e1a02009   1039 	mov	r2,r9

00000518 e1a01000   1040 	mov	r1,r0

0000051c e1a0000b   1041 	mov	r0,fp

00000520 ebffff4d*  1042 	bl	readOsc

                    1043 ;355: 


                    1044 ;356: 	// нечего читать (по идее сюда дойти не должно), т.к. 


                    1045 ;357: 	// выше сработает проверка if (frameNum >= OSCInfo_getFrameCount(oscInfo))


                    1046 ;358: 	if (readSize == 0)


                    1047 

00000524 e3500000   1048 	cmp	r0,0

00000528 0a000033   1049 	beq	.L1012

                    1050 ;359: 	{


                    1051 

                    1052 ;360: 		return FALSE;


                    1053 

                    1054 ;361: 	}


                    1055 ;362: 	// ошибка чтения


                    1056 ;363: 	if (readSize < 0)


                    1057 

0000052c e3500000   1058 	cmp	r0,0

00000530 ba000031   1059 	blt	.L1012

                    1060 ;364: 	{


                    1061 

                    1062 ;365: 		return FALSE;


                    1063 

                    1064 ;366: 	}


                    1065 ;367: 


                    1066 ;368: 	// конвертация периода


                    1067 ;369: 	if (!OscConverter_processPeriod(readFileContext))


                    1068 

00000534 e1a00004   1069 	mov	r0,r4

00000538 eb000000*  1070 	bl	OscConverter_processPeriod

0000053c e3500000   1071 	cmp	r0,0

00000540 0a00002d   1072 	beq	.L1012

                    1073 ;370: 	{


                    1074 

                    1075 ;371: 		return FALSE;


                    1076 

                    1077 ;372: 	}


                    1078 ;373: 	


                    1079 ;374: 	// 


                    1080 ;375: 	if (!OscReadFileContext_writeDatToStream(readFileContext))


                    1081 

00000544 e1a00004   1082 	mov	r0,r4

00000548 eb000000*  1083 	bl	OscReadFileContext_writeDatToStream

0000054c e3500000   1084 	cmp	r0,0

00000550 0a000029   1085 	beq	.L1012

                    1086 ;376: 	{


                    1087 


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
                    1088 ;377: 		return FALSE;


                    1089 

                    1090 ;378: 	}


                    1091 ;379: 


                    1092 ;380: 


                    1093 ;381: 	readFileContext->curFrame++;


                    1094 

00000554 e594a030   1095 	ldr	r10,[r4,48]

00000558 e1a00005   1096 	mov	r0,r5

0000055c e28aa001   1097 	add	r10,r10,1

00000560 e584a030   1098 	str	r10,[r4,48]

                    1099 ;382: 	// последний фрейм, нужно записать cfg


                    1100 ;383: 	if (readFileContext->curFrame >= OSCInfo_getFrameCount(oscInfo))


                    1101 

00000564 eb000000*  1102 	bl	OSCInfo_getFrameCount

00000568 e15a0000   1103 	cmp	r10,r0

0000056c 3a000024   1104 	blo	.L1000

                    1105 ;384: 	{


                    1106 

                    1107 ;385: 		


                    1108 ;386: 		// закрываем dat и пишем все остальные файлы


                    1109 ;387: 		bool result = 


                    1110 

00000570 e3a05000   1111 	mov	r5,0

00000574 e1a00004   1112 	mov	r0,r4

00000578 eb000000*  1113 	bl	OscReadFileContext_closeDat

0000057c e3500000   1114 	cmp	r0,0

00000580 0a00001b   1115 	beq	.L1003

00000584 e1a00004   1116 	mov	r0,r4

00000588 eb000000*  1117 	bl	OscConverter_processCfg

0000058c e3500000   1118 	cmp	r0,0

00000590 0a000017   1119 	beq	.L1003

00000594 e1a00004   1120 	mov	r0,r4

00000598 eb000000*  1121 	bl	OscReadFileContext_writeCfgToStream

0000059c e3500000   1122 	cmp	r0,0

000005a0 0a000013   1123 	beq	.L1003

000005a4 e1a00004   1124 	mov	r0,r4

000005a8 eb000000*  1125 	bl	OscReadFileContext_closeCfg

000005ac e3500000   1126 	cmp	r0,0

000005b0 0a00000f   1127 	beq	.L1003

000005b4 e1a00004   1128 	mov	r0,r4

000005b8 eb000000*  1129 	bl	OscConverter_processHdr

000005bc e3500000   1130 	cmp	r0,0

000005c0 0a00000b   1131 	beq	.L1003

000005c4 e1a00004   1132 	mov	r0,r4

000005c8 eb000000*  1133 	bl	OscReadFileContext_writeHdrToStream

000005cc e3500000   1134 	cmp	r0,0

000005d0 0a000007   1135 	beq	.L1003

000005d4 e1a00004   1136 	mov	r0,r4

000005d8 eb000000*  1137 	bl	OscReadFileContext_closeHdr

000005dc e3500000   1138 	cmp	r0,0

000005e0 0a000003   1139 	beq	.L1003

000005e4 e1a00004   1140 	mov	r0,r4

000005e8 eb000000*  1141 	bl	OscReadFileContext_flushAndClose

000005ec e3500000   1142 	cmp	r0,0

000005f0 13a05001   1143 	movne	r5,1

                    1144 .L1003:

000005f4 e31500ff   1145 	tst	r5,255

                    1146 ;388: 			// dat


                    1147 ;389: 			OscReadFileContext_closeDat(readFileContext) && 


                    1148 ;390: 			// cfg



                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
                    1149 ;391: 			OscConverter_processCfg(readFileContext) &&


                    1150 ;392: 			OscReadFileContext_writeCfgToStream(readFileContext) &&


                    1151 ;393: 			OscReadFileContext_closeCfg(readFileContext) &&


                    1152 ;394: 			// hdr


                    1153 ;395: 			OscConverter_processHdr(readFileContext) &&


                    1154 ;396: 			OscReadFileContext_writeHdrToStream(readFileContext) &&


                    1155 ;397: 			OscReadFileContext_closeHdr(readFileContext) &&


                    1156 ;398: 			// все записать в stream


                    1157 ;399: 			OscReadFileContext_flushAndClose(readFileContext);


                    1158 ;400: 	


                    1159 ;401: 		if (!result)


                    1160 

000005f8 1a000001   1161 	bne	.L1000

                    1162 .L1012:

                    1163 ;402: 		{


                    1164 

                    1165 ;403: 			return FALSE;


                    1166 

000005fc e3a00000   1167 	mov	r0,0

00000600 ea000004   1168 	b	.L965

                    1169 .L1000:

                    1170 ;404: 		}


                    1171 ;405: 


                    1172 ;406: 	}


                    1173 ;407: 


                    1174 ;408: 


                    1175 ;409: 	// пишем сколько влезет в readBuf


                    1176 ;410: 	OscWriteBuffer_toBufferView(readBuf, streamBuffer);


                    1177 

00000604 e1a01008   1178 	mov	r1,r8

00000608 e1a00007   1179 	mov	r0,r7

0000060c eb000000*  1180 	bl	OscWriteBuffer_toBufferView

                    1181 ;411: 	*moreFollows = TRUE;


                    1182 

00000610 e3a00001   1183 	mov	r0,1

00000614 e5c60000   1184 	strb	r0,[r6]

                    1185 ;412: 


                    1186 ;413: 	return TRUE;


                    1187 

                    1188 .L965:

00000618 e28dd008   1189 	add	sp,sp,8

0000061c e8bd8ff0   1190 	ldmfd	[sp]!,{r4-fp,pc}

                    1191 	.endf	OSCFS_readFile

                    1192 	.align	4

                    1193 ;readFileContext	r4	local

                    1194 ;oscInfo	r5	local

                    1195 ;oscPeriodBuf	r9	local

                    1196 ;readSize	r0	local

                    1197 ;frameNum	r10	local

                    1198 ;streamBuffer	r8	local

                    1199 ;cfgBuffer	r5	local

                    1200 ;hdrBuffer	r4	local

                    1201 ;result	r5	local

                    1202 

                    1203 ;frsm	fp	param

                    1204 ;readBuf	r7	param

                    1205 ;moreFollows	r6	param

                    1206 

                    1207 	.section ".bss","awb"

                    1208 .L1254:

                    1209 	.data


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b4o1.s
                    1210 	.text

                    1211 

                    1212 ;414: }


                    1213 	.align	4

                    1214 

                    1215 	.data

                    1216 	.comm	osc_tlsf,4,4

                    1217 	.type	osc_tlsf,$object

                    1218 	.size	osc_tlsf,4

                    1219 	.comm	tlsfCS,4,4

                    1220 	.type	tlsfCS,$object

                    1221 	.size	tlsfCS,4

                    1222 	.ghsnote version,6

                    1223 	.ghsnote tools,3

                    1224 	.ghsnote options,0

                    1225 	.text

                    1226 	.align	4

