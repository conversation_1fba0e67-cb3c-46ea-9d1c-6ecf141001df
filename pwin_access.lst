                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aks1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=pwin_access.c -o gh_aks1.o -list=pwin_access.lst C:\Users\<USER>\AppData\Local\Temp\gh_aks1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_aks1.s
Source File: pwin_access.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile pwin_access.c

                      11 ;		-o pwin_access.o

                      12 ;Source File:   pwin_access.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:06 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "pwin_access.h"


                      23 ;2: 


                      24 ;3: #include "../../../../AT91CORE/modules/pw/pwinlib/config.h"


                      25 ;4: #include "../../../../AT91CORE/modules/pw/pwinlib/pwinlib.h"


                      26 ;5: #include "../../../../AT91CORE/modules/pw/pwinlib/lasterr.h"


                      27 ;6: 


                      28 ;7: #include <debug.h>


                      29 ;8: #include <types.h>


                      30 ;9: #include <process.h>


                      31 ;10: 


                      32 ;11: #define PWIN_DEFAULT_TIMEOUT 1000


                      33 ;12: 


                      34 ;13: static _HANDLE pwlHandle;


                      35 ;14: static _PWL_GetAnalogValue pwlGetAnalogValue;


                      36 ;15: static _LoadRomFromAddr pwlLoadRomFromAddr;


                      37 ;16: static _PWL_initLibIf pwlInitLibIf;


                      38 ;17: 


                      39 ;18: static PWinLibIf pwinLibIf;


                      40 ;19: 


                      41 ;20: bool initPWin(void)


                      42 	.text

                      43 	.align	4

                      44 initPWin::

00000000 e92d4070     45 	stmfd	[sp]!,{r4-r6,lr}

                      46 ;21: {


                      47 

                      48 ;22:     _PWL_Open pwlOpen;


                      49 ;23:     _HANDLE hModule = _GetModuleHandle("PWINLIB");


                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aks1.s
00000004 e28f0000*    51 	adr	r0,.L145

00000008 e59f4084*    52 	ldr	r4,.L146

0000000c eb000000*    53 	bl	_GetModuleHandle

00000010 e1b06000     54 	movs	r6,r0

                      55 ;24:     if(! hModule)


                      56 

00000014 0a000015     57 	beq	.L17

                      58 ;25:     {


                      59 

                      60 ;26:         return FALSE;


                      61 

                      62 ;27:     }


                      63 ;28: 


                      64 ;29:     pwlOpen = (_PWL_Open)_GetProcAddress(hModule,(char*)PWL_OPEN);


                      65 

00000018 e3a01360     66 	mov	r1,0x80000001

0000001c eb000000*    67 	bl	_GetProcAddress

00000020 e1b05000     68 	movs	r5,r0

                      69 ;30:     if(! pwlOpen)


                      70 

00000024 0a000011     71 	beq	.L17

                      72 ;31:     {


                      73 

                      74 ;32:         return FALSE;


                      75 

                      76 ;33:     }


                      77 ;34: 


                      78 ;35:     pwlGetAnalogValue = (_PWL_GetAnalogValue)


                      79 

00000028 e3a01288     80 	mov	r1,0x80000008

0000002c e1a00006     81 	mov	r0,r6

00000030 eb000000*    82 	bl	_GetProcAddress

00000034 e5840004     83 	str	r0,[r4,4]

                      84 ;36:             _GetProcAddress(hModule,(char*)PWL_GET_ANALOG_VALUE);


                      85 ;37:     if(! pwlGetAnalogValue)


                      86 

00000038 e3500000     87 	cmp	r0,0

0000003c 0a00000b     88 	beq	.L17

                      89 ;38:     {


                      90 

                      91 ;39:         return FALSE;


                      92 

                      93 ;40:     }


                      94 ;41: 


                      95 ;42:     pwlLoadRomFromAddr = (_LoadRomFromAddr)


                      96 

00000040 e3a01268     97 	mov	r1,0x80000006

00000044 e1a00006     98 	mov	r0,r6

00000048 eb000000*    99 	bl	_GetProcAddress

0000004c e5840008    100 	str	r0,[r4,8]

                     101 ;43:             _GetProcAddress(hModule,(char*)PWL_LOAD_ROMM_FROM_ADDR);


                     102 ;44:     if(! pwlLoadRomFromAddr)


                     103 

00000050 e3500000    104 	cmp	r0,0

00000054 0a000005    105 	beq	.L17

                     106 ;45:     {


                     107 

                     108 ;46:         return FALSE;


                     109 

                     110 ;47:     }


                     111 ;48: 



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aks1.s
                     112 ;49:     pwlInitLibIf = (_PWL_initLibIf)


                     113 

00000058 e3a01480    114 	mov	r1,1<<31

0000005c e2811064    115 	add	r1,r1,100

00000060 e1a00006    116 	mov	r0,r6

00000064 eb000000*   117 	bl	_GetProcAddress

00000068 e1b0c000    118 	movs	r12,r0

0000006c e584c00c    119 	str	r12,[r4,12]

                     120 ;50:             _GetProcAddress(hModule,(char*)PWL_INIT_IF);


                     121 ;51:     if(! pwlInitLibIf)


                     122 

                     123 .L17:

                     124 ;52:     {


                     125 

                     126 ;53:         return FALSE;


                     127 

00000070 03a00000    128 	moveq	r0,0

00000074 0a000019    129 	beq	.L2

                     130 .L16:

                     131 ;54:     }


                     132 ;55:     pwlInitLibIf(&pwinLibIf,sizeof(PWinLibIf));


                     133 

00000078 e2840010    134 	add	r0,r4,16

0000007c e3a01074    135 	mov	r1,116

00000080 e1a0e00f    136 	mov	lr,pc

00000084 e12fff1c*   137 	bx	r12

                     138 ;56: 


                     139 ;57: 


                     140 ;58:     do


                     141 

                     142 .L21:

                     143 ;59:     {


                     144 

                     145 ;60:         Idle();


                     146 

00000088 ea000002    147 	b	.L147

                     148 	.align	4

                     149 .L145:

                     150 ;	"PWINLIB\000"

0000008c 4e495750    151 	.data.b	80,87,73,78

00000090 0042494c    152 	.data.b	76,73,66,0

                     153 	.align 4

                     154 

                     155 	.type	.L145,$object

                     156 	.size	.L145,4

                     157 

                     158 .L146:

00000094 00000000*   159 	.data.w	.L120

                     160 	.type	.L146,$object

                     161 	.size	.L146,4

                     162 

                     163 .L147:

                     164 

00000098 e6000010    165 	.word	0xE6000010

                     166 

                     167 ;61:         pwlHandle = pwlOpen();


                     168 

0000009c e1a0e00f    169 	mov	lr,pc

000000a0 e12fff15*   170 	bx	r5

000000a4 e5840000    171 	str	r0,[r4]

000000a8 e3500000    172 	cmp	r0,0


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aks1.s
000000ac 0afffff5    173 	beq	.L21

                     174 ;62:     }


                     175 ;63:     while(!pwlHandle);


                     176 ;64: 


                     177 ;65: 	pwinLibIf.configure(pwlHandle,PWL_CONFIGURE_WAIT_READY_BEFORE_OPERATION,0);


                     178 

000000b0 e594c064    179 	ldr	r12,[r4,100]

000000b4 e3a02000    180 	mov	r2,0

000000b8 e3a01002    181 	mov	r1,2

000000bc e1a0e00f    182 	mov	lr,pc

000000c0 e12fff1c*   183 	bx	r12

                     184 ;66: 	pwinLibIf.configure(pwlHandle,PWL_CONFIGURE_TIMEOUT,PWIN_DEFAULT_TIMEOUT);


                     185 

000000c4 e594c064    186 	ldr	r12,[r4,100]

000000c8 e5940000    187 	ldr	r0,[r4]

000000cc e3a02ffa    188 	mov	r2,0x03e8

000000d0 e3a01001    189 	mov	r1,1

000000d4 e1a0e00f    190 	mov	lr,pc

000000d8 e12fff1c*   191 	bx	r12

                     192 ;67: 


                     193 ;68:     return TRUE;


                     194 

000000dc e3a00001    195 	mov	r0,1

                     196 .L2:

000000e0 e8bd8070    197 	ldmfd	[sp]!,{r4-r6,pc}

                     198 	.endf	initPWin

                     199 	.align	4

                     200 ;pwlOpen	r5	local

                     201 ;hModule	r6	local

                     202 ;.L121	.L124	static

                     203 

                     204 	.section ".bss","awb"

                     205 .L120:

00000000 00000000    206 pwlHandle:	.space	4

00000004 00000000    207 pwlGetAnalogValue:	.space	4

00000008 00000000    208 pwlLoadRomFromAddr:	.space	4

0000000c 00000000    209 pwlInitLibIf:	.space	4

00000010 00000000    210 pwinLibIf:	.space	116

00000014 00000000 
00000018 00000000 
0000001c 00000000 
00000020 00000000 
00000024 00000000 
00000028 00000000 
0000002c 00000000 
00000030 00000000 
00000034 00000000 
00000038 00000000 
0000003c 00000000 
00000040 00000000 
00000044 00000000 
00000048 00000000 
0000004c 00000000 
00000050 00000000 
00000054 00000000 
00000058 00000000 
0000005c 00000000 
00000060 00000000 
00000064 00000000 
00000068 00000000 
0000006c 00000000 

                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aks1.s
00000070 00000000 
00000074 00000000 
00000078 00000000 
0000007c 00000000 
00000080 00000000 
                     211 	.data

                     212 	.text

                     213 

                     214 ;69: }


                     215 

                     216 ;70: 


                     217 ;71: bool loadRomModule(uint32_t signature, void** pRomModule, uint8_t** pRomModuleData,


                     218 	.align	4

                     219 	.align	4

                     220 loadRomModule::

000000e4 e92d4cf0    221 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                     222 ;72:     size_t* pRomModuleDataSize, __time32_t *time)


                     223 ;73: {


                     224 

                     225 ;74:     RMODULE* module;


                     226 ;75: 	


                     227 ;76: 	while (true)


                     228 

000000e8 e1a05000    229 	mov	r5,r0

000000ec e1a0b001    230 	mov	fp,r1

000000f0 e1a06002    231 	mov	r6,r2

000000f4 e1a07003    232 	mov	r7,r3

000000f8 e59da01c    233 	ldr	r10,[sp,28]

000000fc e59f43e8*   234 	ldr	r4,.L298

                     235 .L152:

                     236 ;77: 	{


                     237 

                     238 ;78: 		module = pwlLoadRomFromAddr(pwlHandle, 0, signature, TRUE);


                     239 

00000100 e594c008    240 	ldr	r12,[r4,8]

00000104 e1a02005    241 	mov	r2,r5

00000108 e5940000    242 	ldr	r0,[r4]

0000010c e3a03001    243 	mov	r3,1

00000110 e3a01000    244 	mov	r1,0

00000114 e1a0e00f    245 	mov	lr,pc

00000118 e12fff1c*   246 	bx	r12

                     247 ;79: 		// нашли модель


                     248 ;80: 		if (module)


                     249 

0000011c e3500000    250 	cmp	r0,0

00000120 1a000007    251 	bne	.L155

                     252 ;81: 		{


                     253 

                     254 ;82: 			break;


                     255 

                     256 ;83: 		}


                     257 ;84: 		


                     258 ;85: 		// ошибок нет, не найден роммодуль


                     259 ;86: 		if (pwinLibIf.getLastError(pwlHandle) == PWL_ERROR_ROMMODULE_NOT_FOUND)


                     260 

00000124 e5940000    261 	ldr	r0,[r4]

00000128 e594c070    262 	ldr	r12,[r4,112]

0000012c e1a0e00f    263 	mov	lr,pc

00000130 e12fff1c*   264 	bx	r12

00000134 e3500002    265 	cmp	r0,2

00000138 1afffff0    266 	bne	.L152


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aks1.s
                     267 ;87: 			return FALSE;


                     268 

0000013c e3a00000    269 	mov	r0,0

00000140 ea00000c    270 	b	.L148

                     271 .L155:

                     272 ;88: 		


                     273 ;89: 		// была ошибка по pwin - повторяем поиск


                     274 ;90: 	}


                     275 ;91: 	


                     276 ;92: 	if (pRomModule)


                     277 

00000144 e35b0000    278 	cmp	fp,0

                     279 ;93: 	{		


                     280 

                     281 ;94: 		*pRomModule = module;


                     282 

00000148 158b0000    283 	strne	r0,[fp]

                     284 ;95: 	}


                     285 ;96: 	


                     286 ;97: 	if (pRomModuleData)


                     287 

0000014c e3560000    288 	cmp	r6,0

                     289 ;98: 	{


                     290 

                     291 ;99: 	   *pRomModuleData = (uint8_t*)(module + 1);


                     292 

00000150 12801030    293 	addne	r1,r0,48

00000154 15861000    294 	strne	r1,[r6]

                     295 ;100: 	}


                     296 ;101: 	// размер


                     297 ;102: 	if (pRomModuleDataSize)


                     298 

00000158 e3570000    299 	cmp	r7,0

                     300 ;103: 	{


                     301 

                     302 ;104:         *pRomModuleDataSize = module->MSize - sizeof(RMODULE);


                     303 

0000015c 15901004    304 	ldrne	r1,[r0,4]

00000160 12411030    305 	subne	r1,r1,48

00000164 15871000    306 	strne	r1,[r7]

                     307 ;105: 	}


                     308 ;106: 	// время


                     309 ;107: 	if (time)


                     310 

00000168 e35a0000    311 	cmp	r10,0

                     312 ;108: 	{


                     313 

                     314 ;109: 		*time = module->DateTime;


                     315 

0000016c 1590002c    316 	ldrne	r0,[r0,44]

00000170 158a0000    317 	strne	r0,[r10]

                     318 ;110: 	}


                     319 ;111:     return TRUE;


                     320 

00000174 e3a00001    321 	mov	r0,1

                     322 .L148:

00000178 e8bd8cf0    323 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                     324 	.endf	loadRomModule

                     325 	.align	4

                     326 ;module	r0	local

                     327 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aks1.s
                     328 ;signature	r5	param

                     329 ;pRomModule	fp	param

                     330 ;pRomModuleData	r6	param

                     331 ;pRomModuleDataSize	r7	param

                     332 ;time	r10	param

                     333 

                     334 	.data

                     335 	.text

                     336 

                     337 ;112: }


                     338 

                     339 ;113: 


                     340 ;114: uint8_t* loadIedModel(int* pSize )


                     341 	.align	4

                     342 	.align	4

                     343 loadIedModel::

0000017c e92d4010    344 	stmfd	[sp]!,{r4,lr}

                     345 ;115: {


                     346 

00000180 e24dd008    347 	sub	sp,sp,8

00000184 e1a04000    348 	mov	r4,r0

00000188 e3a01000    349 	mov	r1,0

0000018c e1a00001    350 	mov	r0,r1

00000190 e88d0003    351 	stmea	[sp],{r0-r1}

                     352 ;116: 	


                     353 ;117: 	uint8_t *modelPtr = NULL;


                     354 

                     355 ;118: 	


                     356 ;119: 	// Если не удалось загрузить загрузить модель, изготовленную из CID-файла, 


                     357 ;120: 	// загружается модель по-умолчанию


                     358 ;121: 	loadRomModule('IED1',NULL,&modelPtr,(size_t*)pSize,NULL) || 


                     359 

00000194 e1a03004    360 	mov	r3,r4

00000198 e59f0350*   361 	ldr	r0,.L373

0000019c e28d2004    362 	add	r2,sp,4

000001a0 ebffffcf*   363 	bl	loadRomModule

000001a4 e3500000    364 	cmp	r0,0

000001a8 1a000005    365 	bne	.L302

000001ac e3a01000    366 	mov	r1,0

000001b0 e58d1000    367 	str	r1,[sp]

000001b4 e1a03004    368 	mov	r3,r4

000001b8 e59f0334*   369 	ldr	r0,.L374

000001bc e28d2004    370 	add	r2,sp,4

000001c0 ebffffc7*   371 	bl	loadRomModule

                     372 .L302:

                     373 ;122: 		loadRomModule('IED0',NULL,&modelPtr,(size_t*)pSize,NULL);


                     374 ;123: 	


                     375 ;124:     return modelPtr;


                     376 

000001c4 e59d0004    377 	ldr	r0,[sp,4]

000001c8 e28dd008    378 	add	sp,sp,8

000001cc e8bd8010    379 	ldmfd	[sp]!,{r4,pc}

                     380 	.endf	loadIedModel

                     381 	.align	4

                     382 ;modelPtr	[sp,4]	local

                     383 

                     384 ;pSize	r4	param

                     385 

                     386 	.section ".bss","awb"

                     387 .L362:

                     388 	.data


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aks1.s
                     389 	.text

                     390 

                     391 ;125: }


                     392 

                     393 ;126: 


                     394 ;127: int getFloatValue(short offset)


                     395 	.align	4

                     396 	.align	4

                     397 getFloatValue::

000001d0 e92d4010    398 	stmfd	[sp]!,{r4,lr}

                     399 ;128: {


                     400 

                     401 ;129:     long value;


                     402 ;130:     bool result = pwlGetAnalogValue(pwlHandle, offset, &value);


                     403 

000001d4 e24dd004    404 	sub	sp,sp,4

000001d8 e59fc30c*   405 	ldr	r12,.L298

000001dc e1a0200d    406 	mov	r2,sp

000001e0 e1a01800    407 	mov	r1,r0 lsl 16

000001e4 e1a01821    408 	mov	r1,r1 lsr 16

000001e8 e89c0011    409 	ldmfd	[r12],{r0,r4}

000001ec e1a0e00f    410 	mov	lr,pc

000001f0 e12fff14*   411 	bx	r4

                     412 ;131:     if( !result  )


                     413 

000001f4 e3500000    414 	cmp	r0,0

                     415 ;134:     }


                     416 ;135:     else


                     417 ;136:     {


                     418 

                     419 ;137:         return value;


                     420 

000001f8 159d0000    421 	ldrne	r0,[sp]

                     422 ;132:     {


                     423 

                     424 ;133:         return 0x7FFFFFFF;


                     425 

000001fc 03e00480    426 	mvneq	r0,1<<31

00000200 e28dd004    427 	add	sp,sp,4

00000204 e8bd8010    428 	ldmfd	[sp]!,{r4,pc}

                     429 	.endf	getFloatValue

                     430 	.align	4

                     431 ;value	[sp]	local

                     432 

                     433 ;offset	r0	param

                     434 

                     435 	.data

                     436 	.text

                     437 

                     438 ;138:     }


                     439 ;139: }


                     440 

                     441 ;140: 


                     442 ;141: void writeTele(uint32_t offset)


                     443 	.align	4

                     444 	.align	4

                     445 writeTele::

00000208 e92d4010    446 	stmfd	[sp]!,{r4,lr}

0000020c e59fc2d8*   447 	ldr	r12,.L298

                     448 ;142: {


                     449 


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aks1.s
                     450 ;143:     pwinLibIf.sendTele(pwlHandle, offset);


                     451 

00000210 e1a01800    452 	mov	r1,r0 lsl 16

00000214 e59c404c    453 	ldr	r4,[r12,76]

00000218 e59c0000    454 	ldr	r0,[r12]

0000021c e1a01821    455 	mov	r1,r1 lsr 16

00000220 e1a0e00f    456 	mov	lr,pc

00000224 e12fff14*   457 	bx	r4

00000228 e8bd8010    458 	ldmfd	[sp]!,{r4,pc}

                     459 	.endf	writeTele

                     460 	.align	4

                     461 

                     462 ;offset	r0	param

                     463 

                     464 	.data

                     465 	.text

                     466 

                     467 ;144: }


                     468 

                     469 ;145: 


                     470 ;146: //***************************************************************


                     471 ;147: // Этим функциям здесь не место, их нужно перенести в dataslice.c


                     472 ;148: // Оставлены только для проверки.


                     473 ;149: #include "../../../../DataSlice/DataSliceClient/datasliceif.h"


                     474 ;150: 


                     475 ;151: int getFloatSett(unsigned short offset)


                     476 	.align	4

                     477 	.align	4

                     478 getFloatSett::

0000022c e92d4000    479 	stmfd	[sp]!,{lr}

00000230 e59fc2c0*   480 	ldr	r12,.L514

00000234 e24dd004    481 	sub	sp,sp,4

00000238 e59c1000    482 	ldr	r1,[r12]

0000023c e591c014    483 	ldr	r12,[r1,20]

00000240 e1a0100d    484 	mov	r1,sp

00000244 e1a0e00f    485 	mov	lr,pc

00000248 e12fff1c*   486 	bx	r12

                     487 ;152: {


                     488 

                     489 ;153: 	extern DataSliceSetts* settsDataSlice;


                     490 ;154: 	int32_t result;


                     491 ;155: 	if (!settsDataSlice->getWordValue(offset, &result))


                     492 

0000024c e3500000    493 	cmp	r0,0

                     494 ;158: 	}


                     495 ;159: 	return result;


                     496 

00000250 159d0000    497 	ldrne	r0,[sp]

                     498 ;156: 	{


                     499 

                     500 ;157: 		return 0;


                     501 

00000254 e28dd004    502 	add	sp,sp,4

00000258 e8bd8000    503 	ldmfd	[sp]!,{pc}

                     504 	.endf	getFloatSett

                     505 	.align	4

                     506 ;result	[sp]	local

                     507 

                     508 ;offset	none	param

                     509 

                     510 	.section ".bss","awb"


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aks1.s
                     511 .L501:

                     512 	.data

                     513 	.text

                     514 

                     515 ;160: }


                     516 

                     517 ;161: 


                     518 ;162: float getRealSett(unsigned short offset)


                     519 	.align	4

                     520 	.align	4

                     521 getRealSett::

0000025c e92d4000    522 	stmfd	[sp]!,{lr}

                     523 ;163: {


                     524 

                     525 ;164: 	


                     526 ;165: 	extern DataSliceSetts* settsDataSlice;


                     527 ;166: 	float result;


                     528 ;167: 	if (!settsDataSlice->getWordValue(offset, (int32_t*)(void*)&result))


                     529 

00000260 e59fc290*   530 	ldr	r12,.L514

00000264 e24dd004    531 	sub	sp,sp,4

00000268 e59c1000    532 	ldr	r1,[r12]

0000026c e591c014    533 	ldr	r12,[r1,20]

00000270 e1a0100d    534 	mov	r1,sp

00000274 e1a0e00f    535 	mov	lr,pc

00000278 e12fff1c*   536 	bx	r12

0000027c e3500000    537 	cmp	r0,0

                     538 ;170: 	}


                     539 ;171: 	return result;


                     540 

00000280 159d0000    541 	ldrne	r0,[sp]

                     542 ;168: 	{


                     543 

                     544 ;169: 		return 0.f;


                     545 

00000284 e28dd004    546 	add	sp,sp,4

00000288 e8bd8000    547 	ldmfd	[sp]!,{pc}

                     548 	.endf	getRealSett

                     549 	.align	4

                     550 ;result	[sp]	local

                     551 

                     552 ;offset	none	param

                     553 

                     554 	.section ".bss","awb"

                     555 .L555:

                     556 	.data

                     557 	.text

                     558 

                     559 ;172: }


                     560 

                     561 ;173: 


                     562 ;174: 


                     563 ;175: int getIntSett(int offset)


                     564 	.align	4

                     565 	.align	4

                     566 getIntSett::

0000028c e92d4000    567 	stmfd	[sp]!,{lr}

00000290 e59fc260*   568 	ldr	r12,.L514

00000294 e24dd004    569 	sub	sp,sp,4

00000298 e59c1000    570 	ldr	r1,[r12]

0000029c e1a00800    571 	mov	r0,r0 lsl 16


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aks1.s
000002a0 e591c014    572 	ldr	r12,[r1,20]

000002a4 e1a0100d    573 	mov	r1,sp

000002a8 e1a00820    574 	mov	r0,r0 lsr 16

000002ac e1a0e00f    575 	mov	lr,pc

000002b0 e12fff1c*   576 	bx	r12

                     577 ;176: {


                     578 

                     579 ;177: 	extern DataSliceSetts* settsDataSlice;


                     580 ;178: 	int32_t result;


                     581 ;179: 	if (!settsDataSlice->getWordValue(offset, &result))


                     582 

000002b4 e3500000    583 	cmp	r0,0

                     584 ;182: 	}


                     585 ;183: 	return result;


                     586 

000002b8 159d0000    587 	ldrne	r0,[sp]

                     588 ;180: 	{


                     589 

                     590 ;181: 		return 0;


                     591 

000002bc e28dd004    592 	add	sp,sp,4

000002c0 e8bd8000    593 	ldmfd	[sp]!,{pc}

                     594 	.endf	getIntSett

                     595 	.align	4

                     596 ;result	[sp]	local

                     597 

                     598 ;offset	r0	param

                     599 

                     600 	.section ".bss","awb"

                     601 .L613:

                     602 	.data

                     603 	.text

                     604 

                     605 ;184: }


                     606 

                     607 ;185: 


                     608 ;186: //****************************************************************


                     609 ;187: 


                     610 ;188: bool pwaWriteFloatSett(unsigned short offset, int value)


                     611 	.align	4

                     612 	.align	4

                     613 pwaWriteFloatSett::

000002c4 e92d44f0    614 	stmfd	[sp]!,{r4-r7,r10,lr}

000002c8 e59f521c*   615 	ldr	r5,.L298

                     616 ;189: {


                     617 

                     618 ;190:     return pwinLibIf.prepareSetts(pwlHandle, WRITE_TYPE_SETT) 


                     619 

000002cc e1a0a001    620 	mov	r10,r1

000002d0 e2856010    621 	add	r6,r5,16

000002d4 e3a04000    622 	mov	r4,0

000002d8 e595c038    623 	ldr	r12,[r5,56]

000002dc e1a07000    624 	mov	r7,r0

000002e0 e5950000    625 	ldr	r0,[r5]

000002e4 e1a01004    626 	mov	r1,r4

000002e8 e1a0e00f    627 	mov	lr,pc

000002ec e12fff1c*   628 	bx	r12

000002f0 e3500000    629 	cmp	r0,0

000002f4 0a00000e    630 	beq	.L629

000002f8 e595c048    631 	ldr	r12,[r5,72]

000002fc e1a0300a    632 	mov	r3,r10


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aks1.s
00000300 e1a02007    633 	mov	r2,r7

00000304 e5950000    634 	ldr	r0,[r5]

00000308 e3a01000    635 	mov	r1,0

0000030c e1a0e00f    636 	mov	lr,pc

00000310 e12fff1c*   637 	bx	r12

00000314 e3500000    638 	cmp	r0,0

00000318 0a000005    639 	beq	.L629

0000031c e5950000    640 	ldr	r0,[r5]

00000320 e596c02c    641 	ldr	r12,[r6,44]

00000324 e1a0e00f    642 	mov	lr,pc

00000328 e12fff1c*   643 	bx	r12

0000032c e3500000    644 	cmp	r0,0

00000330 13a04001    645 	movne	r4,1

                     646 .L629:

00000334 e20400ff    647 	and	r0,r4,255

00000338 e8bd84f0    648 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     649 	.endf	pwaWriteFloatSett

                     650 	.align	4

                     651 

                     652 ;offset	r7	param

                     653 ;value	r10	param

                     654 

                     655 	.data

                     656 	.text

                     657 

                     658 ;191:         && pwinLibIf.writeAnalogSett(pwlHandle, 0, offset, value) 


                     659 ;192:         && pwinLibIf.finalizeSetts(pwlHandle);    


                     660 ;193: }


                     661 

                     662 ;194: 


                     663 ;195: bool pwaWriteRealSett(unsigned short offset, float value)


                     664 	.align	4

                     665 	.align	4

                     666 pwaWriteRealSett::

0000033c e92d44f2    667 	stmfd	[sp]!,{r1,r4-r7,r10,lr}

                     668 ;196: {


                     669 

                     670 ;197:     int* pValue = (void*)&value;


                     671 

00000340 e59f61a4*   672 	ldr	r6,.L298

00000344 e1a07000    673 	mov	r7,r0

00000348 e1a0400d    674 	mov	r4,sp

                     675 ;198:     int intValue = *pValue;


                     676 

0000034c e2140003    677 	ands	r0,r4,3

00000350 1a000001    678 	bne	.L770

00000354 e594c000    679 	ldr	r12,[r4]

00000358 ea000005    680 	b	.L771

                     681 .L770:

                     682 

0000035c e0444000    683 	sub	r4,r4,r0

00000360 e1a00180    684 	mov	r0,r0 lsl 3

00000364 e8941020    685 	ldmfd	[r4],{r5,r12}

00000368 e1a05035    686 	mov	r5,r5 lsr r0

0000036c e2600020    687 	rsb	r0,r0,32

00000370 e185c01c    688 	orr	r12,r5,r12 lsl r0

                     689 .L771:

                     690 

00000374 e286a010    691 	add	r10,r6,16

00000378 e3a04000    692 	mov	r4,0

0000037c e1a0500c    693 	mov	r5,r12


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aks1.s
                     694 ;199: 


                     695 ;200:     return pwinLibIf.prepareSetts(pwlHandle, WRITE_TYPE_SETT) 


                     696 

00000380 e596c038    697 	ldr	r12,[r6,56]

00000384 e5960000    698 	ldr	r0,[r6]

00000388 e1a01004    699 	mov	r1,r4

0000038c e1a0e00f    700 	mov	lr,pc

00000390 e12fff1c*   701 	bx	r12

00000394 e3500000    702 	cmp	r0,0

00000398 0a00000e    703 	beq	.L712

0000039c e596c048    704 	ldr	r12,[r6,72]

000003a0 e1a03005    705 	mov	r3,r5

000003a4 e1a02007    706 	mov	r2,r7

000003a8 e5960000    707 	ldr	r0,[r6]

000003ac e3a01000    708 	mov	r1,0

000003b0 e1a0e00f    709 	mov	lr,pc

000003b4 e12fff1c*   710 	bx	r12

000003b8 e3500000    711 	cmp	r0,0

000003bc 0a000005    712 	beq	.L712

000003c0 e5960000    713 	ldr	r0,[r6]

000003c4 e59ac02c    714 	ldr	r12,[r10,44]

000003c8 e1a0e00f    715 	mov	lr,pc

000003cc e12fff1c*   716 	bx	r12

000003d0 e3500000    717 	cmp	r0,0

000003d4 13a04001    718 	movne	r4,1

                     719 .L712:

000003d8 e20400ff    720 	and	r0,r4,255

000003dc e8bd84f2    721 	ldmfd	[sp]!,{r1,r4-r7,r10,pc}

                     722 	.endf	pwaWriteRealSett

                     723 	.align	4

                     724 ;pValue	r4	local

                     725 ;intValue	r5	local

                     726 

                     727 ;offset	r7	param

                     728 ;value	[sp]	param

                     729 

                     730 	.data

                     731 	.text

                     732 

                     733 ;201:         && pwinLibIf.writeAnalogSett(pwlHandle, 0, offset, intValue) 


                     734 ;202:         && pwinLibIf.finalizeSetts(pwlHandle);    


                     735 ;203: }


                     736 

                     737 ;204: 


                     738 ;205: 


                     739 ;206: bool pwaWriteIntSett(unsigned short offset, int value)


                     740 	.align	4

                     741 	.align	4

                     742 pwaWriteIntSett::

000003e0 e92d44f0    743 	stmfd	[sp]!,{r4-r7,r10,lr}

000003e4 e59f5100*   744 	ldr	r5,.L298

                     745 ;207: {        


                     746 

                     747 ;208:     return pwinLibIf.prepareSetts(pwlHandle, WRITE_TYPE_SETT) 


                     748 

000003e8 e1a0a001    749 	mov	r10,r1

000003ec e2856010    750 	add	r6,r5,16

000003f0 e3a04000    751 	mov	r4,0

000003f4 e595c038    752 	ldr	r12,[r5,56]

000003f8 e1a07000    753 	mov	r7,r0

000003fc e5950000    754 	ldr	r0,[r5]


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aks1.s
00000400 e1a01004    755 	mov	r1,r4

00000404 e1a0e00f    756 	mov	lr,pc

00000408 e12fff1c*   757 	bx	r12

0000040c e3500000    758 	cmp	r0,0

00000410 0a00000e    759 	beq	.L794

00000414 e595c044    760 	ldr	r12,[r5,68]

00000418 e1a0300a    761 	mov	r3,r10

0000041c e1a02007    762 	mov	r2,r7

00000420 e5950000    763 	ldr	r0,[r5]

00000424 e3a01000    764 	mov	r1,0

00000428 e1a0e00f    765 	mov	lr,pc

0000042c e12fff1c*   766 	bx	r12

00000430 e3500000    767 	cmp	r0,0

00000434 0a000005    768 	beq	.L794

00000438 e5950000    769 	ldr	r0,[r5]

0000043c e596c02c    770 	ldr	r12,[r6,44]

00000440 e1a0e00f    771 	mov	lr,pc

00000444 e12fff1c*   772 	bx	r12

00000448 e3500000    773 	cmp	r0,0

0000044c 13a04001    774 	movne	r4,1

                     775 .L794:

00000450 e20400ff    776 	and	r0,r4,255

00000454 e8bd84f0    777 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     778 	.endf	pwaWriteIntSett

                     779 	.align	4

                     780 

                     781 ;offset	r7	param

                     782 ;value	r10	param

                     783 

                     784 	.data

                     785 	.text

                     786 

                     787 ;209:         && pwinLibIf.writeIntSett(pwlHandle, 0, offset, value) 


                     788 ;210:         && pwinLibIf.finalizeSetts(pwlHandle);   


                     789 ;211: }


                     790 

                     791 ;212: 


                     792 ;213: int pwaOscFindFirst(unsigned int oscNum, PWFileInfo *fileInfo)


                     793 	.align	4

                     794 	.align	4

                     795 pwaOscFindFirst::

00000458 e92d4010    796 	stmfd	[sp]!,{r4,lr}

                     797 ;214: {


                     798 

                     799 ;215:    return pwinLibIf.oscFindFirst(pwlHandle,oscNum,fileInfo);


                     800 

0000045c e59fc088*   801 	ldr	r12,.L298

00000460 e1a02001    802 	mov	r2,r1

00000464 e1a01000    803 	mov	r1,r0

00000468 e59c4050    804 	ldr	r4,[r12,80]

0000046c e59c0000    805 	ldr	r0,[r12]

00000470 e1a0e00f    806 	mov	lr,pc

00000474 e12fff14*   807 	bx	r4

00000478 e8bd8010    808 	ldmfd	[sp]!,{r4,pc}

                     809 	.endf	pwaOscFindFirst

                     810 	.align	4

                     811 

                     812 ;oscNum	r0	param

                     813 ;fileInfo	r1	param

                     814 

                     815 	.data


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aks1.s
                     816 	.text

                     817 

                     818 ;216: }


                     819 

                     820 ;217: 


                     821 ;218: int pwaOscFindNext(int findResult, PWFileInfo *fileInfo)


                     822 	.align	4

                     823 	.align	4

                     824 pwaOscFindNext::

0000047c e92d4010    825 	stmfd	[sp]!,{r4,lr}

                     826 ;219: {


                     827 

                     828 ;220:     return pwinLibIf.oscFindNext(pwlHandle,findResult,fileInfo);


                     829 

00000480 e59fc064*   830 	ldr	r12,.L298

00000484 e1a02001    831 	mov	r2,r1

00000488 e1a01000    832 	mov	r1,r0

0000048c e59c4054    833 	ldr	r4,[r12,84]

00000490 e59c0000    834 	ldr	r0,[r12]

00000494 e1a0e00f    835 	mov	lr,pc

00000498 e12fff14*   836 	bx	r4

0000049c e8bd8010    837 	ldmfd	[sp]!,{r4,pc}

                     838 	.endf	pwaOscFindNext

                     839 	.align	4

                     840 

                     841 ;findResult	r0	param

                     842 ;fileInfo	r1	param

                     843 

                     844 	.data

                     845 	.text

                     846 

                     847 ;221: }


                     848 

                     849 ;222: void pwaOscFindClose(void)


                     850 	.align	4

                     851 	.align	4

                     852 pwaOscFindClose::

000004a0 e92d4010    853 	stmfd	[sp]!,{r4,lr}

                     854 ;223: {


                     855 

                     856 ;224: 	pwinLibIf.oscFindClose(pwlHandle);


                     857 

000004a4 e59fc040*   858 	ldr	r12,.L298

000004a8 e59c4058    859 	ldr	r4,[r12,88]

000004ac e59c0000    860 	ldr	r0,[r12]

000004b0 e1a0e00f    861 	mov	lr,pc

000004b4 e12fff14*   862 	bx	r4

000004b8 e8bd8010    863 	ldmfd	[sp]!,{r4,pc}

                     864 	.endf	pwaOscFindClose

                     865 	.align	4

                     866 

                     867 	.data

                     868 	.text

                     869 

                     870 ;225: }


                     871 

                     872 ;226: 


                     873 ;227: int pwaOscOscRead(PWFileInfo *fileInfo, unsigned int offset, unsigned char *data, int maxDataSize)


                     874 	.align	4

                     875 	.align	4

                     876 pwaOscOscRead::


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aks1.s
000004bc e92d4010    877 	stmfd	[sp]!,{r4,lr}

                     878 ;228: {


                     879 

                     880 ;229: 	return pwinLibIf.oscRead(pwlHandle,fileInfo,offset,data,maxDataSize);


                     881 

000004c0 e59fc024*   882 	ldr	r12,.L298

000004c4 e59c405c    883 	ldr	r4,[r12,92]

000004c8 e52d3004    884 	str	r3,[sp,-4]!

000004cc e1a03002    885 	mov	r3,r2

000004d0 e1a02001    886 	mov	r2,r1

000004d4 e1a01000    887 	mov	r1,r0

000004d8 e59c0000    888 	ldr	r0,[r12]

000004dc e1a0e00f    889 	mov	lr,pc

000004e0 e12fff14*   890 	bx	r4

000004e4 e28dd004    891 	add	sp,sp,4

000004e8 e8bd8010    892 	ldmfd	[sp]!,{r4,pc}

                     893 	.endf	pwaOscOscRead

                     894 	.align	4

                     895 

                     896 ;fileInfo	r0	param

                     897 ;offset	r1	param

                     898 ;data	r2	param

                     899 ;maxDataSize	r3	param

                     900 

                     901 	.data

                     902 	.text

                     903 

                     904 ;230: }


                     905 	.align	4

                     906 .L298:

000004ec 00000000*   907 	.data.w	pwlHandle

                     908 	.type	.L298,$object

                     909 	.size	.L298,4

                     910 

                     911 .L373:

000004f0 49454431    912 	.data.w	0x49454431

                     913 	.type	.L373,$object

                     914 	.size	.L373,4

                     915 

                     916 .L374:

000004f4 49454430    917 	.data.w	0x49454430

                     918 	.type	.L374,$object

                     919 	.size	.L374,4

                     920 

                     921 .L514:

000004f8 00000000*   922 	.data.w	settsDataSlice

                     923 	.type	.L514,$object

                     924 	.size	.L514,4

                     925 

                     926 	.align	4

                     927 ;pwlHandle	pwlHandle	static

                     928 ;pwlGetAnalogValue	pwlGetAnalogValue	static

                     929 ;pwlLoadRomFromAddr	pwlLoadRomFromAddr	static

                     930 ;pwlInitLibIf	pwlInitLibIf	static

                     931 ;pwinLibIf	pwinLibIf	static

                     932 ;settsDataSlice	settsDataSlice	import

                     933 

                     934 	.data

                     935 	.ghsnote version,6

                     936 	.ghsnote tools,1

                     937 	.ghsnote options,0


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aks1.s
                     938 	.text

                     939 	.align	4

                     940 	.section ".bss","awb"

                     941 	.align	4

                     942 	.text

