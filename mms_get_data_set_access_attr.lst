                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bl01.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_get_data_set_access_attr.c -o gh_bl01.o -list=mms_get_data_set_access_attr.lst C:\Users\<USER>\AppData\Local\Temp\gh_bl01.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_bl01.s
Source File: mms_get_data_set_access_attr.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		mms_get_data_set_access_attr.c -o mms_get_data_set_access_attr.o

                      12 ;Source File:   mms_get_data_set_access_attr.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:03 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "mms_get_data_set_access_attr.h"


                      23 ;2: 


                      24 ;3: #include "stringView.h"


                      25 ;4: #include "MmsConst.h"


                      26 ;5: #include "iedmodel.h"


                      27 ;6: #include "BaseAsnTypes.h"


                      28 ;7: #include "AsnEncoding.h"


                      29 ;8: #include "mms_error.h"


                      30 ;9: #include "debug.h"


                      31 ;10: #include <string.h>


                      32 ;11: 


                      33 ;12: 


                      34 ;13: //Одна ссылка


                      35 ;14: int encodeDataSetRefAttr(uint8_t* outBuf, int bufPos, int objectPos, bool determineSize)


                      36 	.text

                      37 	.align	4

                      38 encodeDataSetRefAttr::

00000000 e92d4cf0     39 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                      40 ;15: {


                      41 

                      42 ;16:     int domainPos;


                      43 ;17:     int domainSize;


                      44 ;18:     int namePos;


                      45 ;19:     int nameSize;


                      46 ;20:     int size1;


                      47 ;21:     int size2;


                      48 ;22:     int seqenceSize;


                      49 ;23:     uint8_t tag;


                      50 ;24:     int pos = objectPos;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bl01.s
                      51 

                      52 ;25: 


                      53 ;26: 


                      54 ;27:     //============Определяем размеры и позиции==============


                      55 ;28:     pos = readTL(pos, &tag, NULL, NULL);


                      56 

00000004 e1a05001     57 	mov	r5,r1

00000008 e24dd00c     58 	sub	sp,sp,12

0000000c e28d1002     59 	add	r1,sp,2

00000010 e1a06000     60 	mov	r6,r0

00000014 e1a00002     61 	mov	r0,r2

00000018 e5cd3003     62 	strb	r3,[sp,3]

0000001c e3a03000     63 	mov	r3,0

00000020 e1a02003     64 	mov	r2,r3

00000024 eb000000*    65 	bl	readTL

00000028 e1b04000     66 	movs	r4,r0

                      67 ;29: 


                      68 ;30:     if(pos == 0 || tag != ASN_SEQUENCE)


                      69 

0000002c 0a000018     70 	beq	.L13

00000030 e5dd0002     71 	ldrb	r0,[sp,2]

00000034 e3500030     72 	cmp	r0,48

00000038 1a000015     73 	bne	.L13

                      74 ;31:     {


                      75 

                      76 ;32:         ERROR_REPORT("Error reading reference TL");


                      77 ;33:         return 0;


                      78 

                      79 ;34:     }


                      80 ;35:     


                      81 ;36:     //Домен


                      82 ;37:     domainPos = pos;


                      83 

                      84 ;38:     pos = readTL(domainPos, &tag, NULL, &domainSize);


                      85 

0000003c e28d3004     86 	add	r3,sp,4

00000040 e28d1002     87 	add	r1,sp,2

00000044 e1a00004     88 	mov	r0,r4

00000048 e3a02000     89 	mov	r2,0

0000004c eb000000*    90 	bl	readTL

                      91 ;39:     if(pos == 0 || tag != ASN_VISIBLE_STRING)


                      92 

00000050 e3500000     93 	cmp	r0,0

00000054 0a00000e     94 	beq	.L13

00000058 e5dd0002     95 	ldrb	r0,[sp,2]

0000005c e350001a     96 	cmp	r0,26

00000060 1a00000b     97 	bne	.L13

                      98 ;40:     {


                      99 

                     100 ;41:         ERROR_REPORT("Error reading domain name TL");


                     101 ;42:         return 0;


                     102 

                     103 ;43:     }


                     104 ;44:     namePos = domainPos + domainSize;


                     105 

00000064 e28d3008    106 	add	r3,sp,8

00000068 e59d7004    107 	ldr	r7,[sp,4]

0000006c e28d1002    108 	add	r1,sp,2

00000070 e0877004    109 	add	r7,r7,r4

                     110 ;45: 


                     111 ;46:     //Имя



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bl01.s
                     112 ;47:     pos = readTL(namePos, &tag, NULL, &nameSize);


                     113 

00000074 e1a00007    114 	mov	r0,r7

00000078 e3a02000    115 	mov	r2,0

0000007c eb000000*   116 	bl	readTL

                     117 ;48:     if(pos == 0 || tag != ASN_VISIBLE_STRING)


                     118 

00000080 e3500000    119 	cmp	r0,0

00000084 0a000002    120 	beq	.L13

00000088 e5dd0002    121 	ldrb	r0,[sp,2]

0000008c e350001a    122 	cmp	r0,26

00000090 0a000001    123 	beq	.L12

                     124 .L13:

                     125 ;49:     {


                     126 

                     127 ;50:         ERROR_REPORT("Error reading object name TL");


                     128 ;51:         return 0;


                     129 

00000094 e3a00000    130 	mov	r0,0

00000098 ea000030    131 	b	.L2

                     132 .L12:

                     133 ;52:     }


                     134 ;53: 


                     135 ;54:     //A1


                     136 ;55:     size1 = 1 + BerEncoder_determineLengthSize(domainSize + nameSize)


                     137 

0000009c e99d0006    138 	ldmed	[sp],{r1-r2}

000000a0 e0820001    139 	add	r0,r2,r1

000000a4 eb000000*   140 	bl	BerEncoder_determineLengthSize

000000a8 e99d000c    141 	ldmed	[sp],{r2-r3}

000000ac e0830000    142 	add	r0,r3,r0

000000b0 e0820000    143 	add	r0,r2,r0

000000b4 e280a001    144 	add	r10,r0,1

                     145 ;56:             + domainSize + nameSize;


                     146 ;57: 


                     147 ;58:     //A0


                     148 ;59:     size2 = 1 + BerEncoder_determineLengthSize(size1) + size1;


                     149 

000000b8 e1a0000a    150 	mov	r0,r10

000000bc eb000000*   151 	bl	BerEncoder_determineLengthSize

000000c0 e08a0000    152 	add	r0,r10,r0

000000c4 e280b001    153 	add	fp,r0,1

                     154 ;60: 


                     155 ;61:     //Sequence


                     156 ;62:     seqenceSize = 1 + BerEncoder_determineLengthSize(size2) + size2;


                     157 

000000c8 e1a0000b    158 	mov	r0,fp

000000cc eb000000*   159 	bl	BerEncoder_determineLengthSize

                     160 ;63: 


                     161 ;64:     if(determineSize)


                     162 

000000d0 e5dd1003    163 	ldrb	r1,[sp,3]

000000d4 e3510000    164 	cmp	r1,0

000000d8 108b0000    165 	addne	r0,fp,r0

000000dc 12800001    166 	addne	r0,r0,1

                     167 ;65:     {


                     168 

                     169 ;66:         return seqenceSize;


                     170 

000000e0 1a00001e    171 	bne	.L2

                     172 ;67:     }



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bl01.s
                     173 ;68: 


                     174 ;69:     //================Кодируем====================


                     175 ;70:     //Sequence


                     176 ;71:     bufPos = BerEncoder_encodeTL(ASN_SEQUENCE, size2, outBuf, bufPos);


                     177 

000000e4 e1a03005    178 	mov	r3,r5

000000e8 e1a02006    179 	mov	r2,r6

000000ec e1a0100b    180 	mov	r1,fp

000000f0 e3a00030    181 	mov	r0,48

000000f4 eb000000*   182 	bl	BerEncoder_encodeTL

                     183 ;72:     //A0


                     184 ;73:     bufPos = BerEncoder_encodeTL(0xA0, size1, outBuf, bufPos);


                     185 

000000f8 e1a02006    186 	mov	r2,r6

000000fc e1a0100a    187 	mov	r1,r10

00000100 e1a03000    188 	mov	r3,r0

00000104 e3a000a0    189 	mov	r0,160

00000108 eb000000*   190 	bl	BerEncoder_encodeTL

                     191 ;74:     //A1


                     192 ;75:     bufPos = BerEncoder_encodeTL(0xA1, domainSize + nameSize, outBuf, bufPos);


                     193 

0000010c e1a03000    194 	mov	r3,r0

00000110 e1a02006    195 	mov	r2,r6

00000114 e99d0022    196 	ldmed	[sp],{r1,r5}

00000118 e3a000a1    197 	mov	r0,161

0000011c e0851001    198 	add	r1,r5,r1

00000120 eb000000*   199 	bl	BerEncoder_encodeTL

00000124 e59fa24c*   200 	ldr	r10,.L157

00000128 e1a05000    201 	mov	r5,r0

                     202 ;76:     //Домен


                     203 ;77:     memcpy(outBuf + bufPos, iedModel + domainPos, domainSize);


                     204 

0000012c e59a0000    205 	ldr	r0,[r10]

00000130 e59d2004    206 	ldr	r2,[sp,4]

00000134 e0841000    207 	add	r1,r4,r0

00000138 e0850006    208 	add	r0,r5,r6

0000013c eb000000*   209 	bl	memcpy

                     210 ;78:     bufPos += domainSize;


                     211 

00000140 e99d0005    212 	ldmed	[sp],{r0,r2}

00000144 e0855000    213 	add	r5,r5,r0

                     214 ;79:     //Имя


                     215 ;80:     memcpy(outBuf + bufPos, iedModel + namePos, nameSize);


                     216 

00000148 e59a0000    217 	ldr	r0,[r10]

0000014c e0871000    218 	add	r1,r7,r0

00000150 e0850006    219 	add	r0,r5,r6

00000154 eb000000*   220 	bl	memcpy

                     221 ;81:     bufPos += nameSize;


                     222 

00000158 e59d0008    223 	ldr	r0,[sp,8]

0000015c e0850000    224 	add	r0,r5,r0

                     225 ;82:     return bufPos;


                     226 

                     227 .L2:

00000160 e28dd00c    228 	add	sp,sp,12

00000164 e8bd8cf0    229 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                     230 	.endf	encodeDataSetRefAttr

                     231 	.align	4

                     232 ;domainPos	r4	local

                     233 ;domainSize	[sp,4]	local


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bl01.s
                     234 ;namePos	r7	local

                     235 ;nameSize	[sp,8]	local

                     236 ;size1	r10	local

                     237 ;size2	fp	local

                     238 ;seqenceSize	r0	local

                     239 ;tag	[sp,2]	local

                     240 ;pos	r4	local

                     241 

                     242 ;outBuf	r6	param

                     243 ;bufPos	r5	param

                     244 ;objectPos	r2	param

                     245 ;determineSize	[sp,3]	param

                     246 

                     247 	.section ".bss","awb"

                     248 .L122:

                     249 	.data

                     250 	.text

                     251 

                     252 ;83: }


                     253 

                     254 ;84: 


                     255 ;85: 


                     256 ;86: static int encodeDataSetRefAttrs(uint8_t* outBuf, int bufPos, int dataSetPos,


                     257 	.align	4

                     258 	.align	4

                     259 encodeDataSetRefAttrs:

00000168 e92d4cf0    260 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                     261 ;87: 	bool determineSize)


                     262 ;88: {


                     263 

0000016c e24dd004    264 	sub	sp,sp,4

00000170 e1a0b000    265 	mov	fp,r0

00000174 e1a00002    266 	mov	r0,r2

00000178 e1a0200d    267 	mov	r2,sp

0000017c e1a07003    268 	mov	r7,r3

00000180 e3a06000    269 	mov	r6,0

                     270 ;89:     int endPos;


                     271 ;90:     int dataSetLen;


                     272 ;91:     int refPos;


                     273 ;92:     uint8_t tag;


                     274 ;93: 	int size = 0;


                     275 

                     276 ;94:     int pos = readTL(dataSetPos, NULL, &dataSetLen, NULL );


                     277 

00000184 e1a03006    278 	mov	r3,r6

00000188 e1a04001    279 	mov	r4,r1

0000018c e1a01006    280 	mov	r1,r6

00000190 eb000000*   281 	bl	readTL

                     282 ;95:     endPos = pos + dataSetLen;


                     283 

00000194 e59da000    284 	ldr	r10,[sp]

00000198 e08aa000    285 	add	r10,r10,r0

                     286 ;96:     //Пропускаем имя


                     287 ;97:     pos = skipObject(pos);


                     288 

0000019c eb000000*   289 	bl	skipObject

000001a0 e59f21d0*   290 	ldr	r2,.L157

000001a4 e5921000    291 	ldr	r1,[r2]

000001a8 e1a05000    292 	mov	r5,r0

                     293 ;98: 	VERIFY(pos);


                     294 ;99: 



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bl01.s
                     295 ;100:     //Пропускаем дополнительную инфу для отчётов, если она есть.


                     296 ;101:     //Она должна быть всегда, но в отладочных моделях может и отсутствовать.


                     297 ;102:     tag = iedModel[pos];


                     298 

000001ac e7d11005    299 	ldrb	r1,[r1,r5]

                     300 ;103:     if(tag == IED_DATASET_DESCRIPTION)


                     301 

000001b0 e3510004    302 	cmp	r1,4

000001b4 1a000001    303 	bne	.L162

                     304 ;104:     {


                     305 

                     306 ;105:         pos = skipObject(pos);


                     307 

000001b8 eb000000*   308 	bl	skipObject

000001bc e1a05000    309 	mov	r5,r0

                     310 .L162:

                     311 ;106:     }


                     312 ;107:     else


                     313 ;108:     {


                     314 

                     315 ;109:         TRACE("Dataset description is not found");


                     316 ;110:     }


                     317 ;111: 


                     318 ;112:     refPos = pos;


                     319 

                     320 ;113:     while(refPos < endPos)


                     321 

000001c0 e155000a    322 	cmp	r5,r10

000001c4 aa00000d    323 	bge	.L164

                     324 .L165:

                     325 ;114:     {


                     326 

                     327 ;115: 		bufPos = encodeDataSetRefAttr(outBuf, bufPos, refPos, determineSize);


                     328 

000001c8 e1a03007    329 	mov	r3,r7

000001cc e1a02005    330 	mov	r2,r5

000001d0 e1a01004    331 	mov	r1,r4

000001d4 e1a0000b    332 	mov	r0,fp

000001d8 ebffff88*   333 	bl	encodeDataSetRefAttr

000001dc e3570000    334 	cmp	r7,0

                     335 ;118: 		{


                     336 

                     337 ;119: 			size += bufPos;


                     338 

000001e0 e1a04000    339 	mov	r4,r0

                     340 ;116: 		VERIFY(bufPos);		


                     341 ;117: 		if (determineSize)


                     342 

000001e4 10866004    343 	addne	r6,r6,r4

                     344 ;120: 			bufPos = size;


                     345 

000001e8 11a04006    346 	movne	r4,r6

                     347 ;121: 		}				


                     348 ;122: 		refPos = skipObject(refPos);


                     349 

000001ec e1a00005    350 	mov	r0,r5

000001f0 eb000000*   351 	bl	skipObject

000001f4 e1a05000    352 	mov	r5,r0

000001f8 e155000a    353 	cmp	r5,r10

000001fc bafffff1    354 	blt	.L165

                     355 .L164:


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bl01.s
                     356 ;123: 		VERIFY(refPos);


                     357 ;124:     }


                     358 ;125: 	return bufPos;


                     359 

00000200 e1a00004    360 	mov	r0,r4

00000204 e28dd004    361 	add	sp,sp,4

00000208 e8bd4cf0    362 	ldmfd	[sp]!,{r4-r7,r10-fp,lr}

0000020c e12fff1e*   363 	ret	

                     364 	.endf	encodeDataSetRefAttrs

                     365 	.align	4

                     366 ;endPos	r10	local

                     367 ;dataSetLen	[sp]	local

                     368 ;refPos	r5	local

                     369 ;tag	r1	local

                     370 ;size	r6	local

                     371 ;pos	r5	local

                     372 

                     373 ;outBuf	fp	param

                     374 ;bufPos	r4	param

                     375 ;dataSetPos	r12	param

                     376 ;determineSize	r7	param

                     377 

                     378 	.section ".bss","awb"

                     379 .L232:

                     380 	.data

                     381 	.text

                     382 

                     383 ;126: }


                     384 

                     385 ;127: 


                     386 ;128: //Весь dataset


                     387 ;129: int encodeDataSetAttrs(uint32_t invokeId ,uint8_t* outBuf, int dataSetPos)


                     388 	.align	4

                     389 	.align	4

                     390 encodeDataSetAttrs::

00000210 e92d4cf0    391 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                     392 ;130: {


                     393 

                     394 ;131: 	int allRefsSize;


                     395 ;132: 	int accessResultSize;


                     396 ;133: 	int fullConfirmedResponseSize;


                     397 ;134: 	int invokeIdSize;


                     398 ;135: 	int bufPos = 0;


                     399 

                     400 ;136: 


                     401 ;137: 


                     402 ;138: 	//==================Определяем размеры====================	


                     403 ;139: 	allRefsSize = encodeDataSetRefAttrs(outBuf, bufPos, dataSetPos, TRUE);


                     404 

00000214 e1a0a002    405 	mov	r10,r2

00000218 e1a07000    406 	mov	r7,r0

0000021c e1a04001    407 	mov	r4,r1

00000220 e1a00004    408 	mov	r0,r4

00000224 e3a03001    409 	mov	r3,1

00000228 e3a01000    410 	mov	r1,0

0000022c ebffffcd*   411 	bl	encodeDataSetRefAttrs

00000230 e1a05000    412 	mov	r5,r0

                     413 ;140: 


                     414 ;141: 	invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;


                     415 

00000234 e1a00007    416 	mov	r0,r7


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bl01.s
00000238 eb000000*   417 	bl	BerEncoder_UInt32determineEncodedSize

0000023c e280b002    418 	add	fp,r0,2

                     419 ;142: 


                     420 ;143: 	accessResultSize = allRefsSize


                     421 

00000240 e1a00005    422 	mov	r0,r5

00000244 eb000000*   423 	bl	BerEncoder_determineLengthSize

00000248 e0850000    424 	add	r0,r5,r0

0000024c e2806004    425 	add	r6,r0,4

                     426 ;144: 		+ BerEncoder_determineLengthSize(allRefsSize) + 1 // A1 TL


                     427 ;145: 		+ 3; //Размер флага Deletable


                     428 ;146: 	fullConfirmedResponseSize = accessResultSize


                     429 

00000250 e1a00006    430 	mov	r0,r6

00000254 eb000000*   431 	bl	BerEncoder_determineLengthSize

00000258 e1a02004    432 	mov	r2,r4

0000025c e3a03000    433 	mov	r3,0

00000260 e0860000    434 	add	r0,r6,r0

00000264 e08b0000    435 	add	r0,fp,r0

00000268 e2801001    436 	add	r1,r0,1

                     437 ;147: 		//confirmed response


                     438 ;148: 		+ BerEncoder_determineLengthSize(accessResultSize) + 1


                     439 ;149: 		//invokeId


                     440 ;150: 		+ invokeIdSize;


                     441 ;151: 		


                     442 ;152:     //==================Кодируем====================


                     443 ;153:     bufPos = 0;


                     444 

                     445 ;154: 


                     446 ;155:     // confirmed response PDU


                     447 ;156:     bufPos = BerEncoder_encodeTL(MMS_CONFIRMED_RESPONSE_PDU,  


                     448 

0000026c e3a000a1    449 	mov	r0,161

00000270 eb000000*   450 	bl	BerEncoder_encodeTL

                     451 ;157: 		fullConfirmedResponseSize, outBuf, bufPos);


                     452 ;158: 


                     453 ;159:     // invoke id  


                     454 ;160: 	bufPos = BerEncoder_encodeUInt32WithTL(ASN_INTEGER, invokeId, outBuf,


                     455 

00000274 e1a02004    456 	mov	r2,r4

00000278 e1a01007    457 	mov	r1,r7

0000027c e1a03000    458 	mov	r3,r0

00000280 e3a00002    459 	mov	r0,2

00000284 eb000000*   460 	bl	BerEncoder_encodeUInt32WithTL

                     461 ;161: 		bufPos);


                     462 ;162: 		


                     463 ;163:     // confirmed-service-response getNamedVariableAccessAttributes


                     464 ;164:     bufPos = BerEncoder_encodeTL(0xAC, accessResultSize,


                     465 

00000288 e1a02004    466 	mov	r2,r4

0000028c e1a01006    467 	mov	r1,r6

00000290 e1a03000    468 	mov	r3,r0

00000294 e3a000ac    469 	mov	r0,172

00000298 eb000000*   470 	bl	BerEncoder_encodeTL

                     471 ;165:                                  outBuf, bufPos);


                     472 ;166: 


                     473 ;167: 	//Deletable


                     474 ;168: 	bufPos = BerEncoder_encodeBoolean(VAR_DELETABLE, FALSE, outBuf, bufPos);	


                     475 

0000029c e1a02004    476 	mov	r2,r4

000002a0 e3a01000    477 	mov	r1,0


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bl01.s
000002a4 e1a03000    478 	mov	r3,r0

000002a8 e3a00080    479 	mov	r0,128

000002ac eb000000*   480 	bl	BerEncoder_encodeBoolean

                     481 ;169: 	// A1


                     482 ;170: 	bufPos = BerEncoder_encodeTL(0xa1, allRefsSize, outBuf, bufPos);


                     483 

000002b0 e1a02004    484 	mov	r2,r4

000002b4 e1a01005    485 	mov	r1,r5

000002b8 e1a03000    486 	mov	r3,r0

000002bc e3a000a1    487 	mov	r0,161

000002c0 eb000000*   488 	bl	BerEncoder_encodeTL

                     489 ;171: 


                     490 ;172: 	// кодируем ссылки


                     491 ;173: 	bufPos = encodeDataSetRefAttrs(outBuf, bufPos, dataSetPos, FALSE);


                     492 

000002c4 e1a0200a    493 	mov	r2,r10

000002c8 e1a01000    494 	mov	r1,r0

000002cc e1a00004    495 	mov	r0,r4

000002d0 e3a03000    496 	mov	r3,0

000002d4 e8bd4cf0    497 	ldmfd	[sp]!,{r4-r7,r10-fp,lr}

000002d8 eaffffa2*   498 	b	encodeDataSetRefAttrs

                     499 	.endf	encodeDataSetAttrs

                     500 	.align	4

                     501 ;allRefsSize	r5	local

                     502 ;accessResultSize	r6	local

                     503 ;invokeIdSize	fp	local

                     504 

                     505 ;invokeId	r7	param

                     506 ;outBuf	r4	param

                     507 ;dataSetPos	r10	param

                     508 

                     509 	.section ".bss","awb"

                     510 .L286:

                     511 	.data

                     512 	.text

                     513 

                     514 ;176: }


                     515 

                     516 ;177: 


                     517 ;178: 


                     518 ;179: int getDataSetAccessAttrs(unsigned int invokeId, 


                     519 	.align	4

                     520 	.align	4

                     521 getDataSetAccessAttrs::

000002dc e92d4030    522 	stmfd	[sp]!,{r4-r5,lr}

                     523 ;180: 	StringView* domainId, StringView* itemId, uint8_t* outBuf)


                     524 ;181: {


                     525 

                     526 ;182:     //=============Находим dataset===============


                     527 ;183:     int dataSetPos = findObjectByFullName(IED_VMD_DATA_SET_SECTION, domainId, itemId);


                     528 

000002e0 e1a05003    529 	mov	r5,r3

000002e4 e1a04000    530 	mov	r4,r0

000002e8 e3a000ee    531 	mov	r0,238

000002ec eb000000*   532 	bl	findObjectByFullName

000002f0 e1a01005    533 	mov	r1,r5

000002f4 e1b02000    534 	movs	r2,r0

                     535 ;184: 	if (dataSetPos == 0)


                     536 

000002f8 e1a00004    537 	mov	r0,r4

                     538 ;187: 			MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bl01.s
                     539 ;188: 	}


                     540 ;189: 


                     541 ;190: 	return encodeDataSetAttrs(invokeId, outBuf, dataSetPos);


                     542 

000002fc 18bd4030    543 	ldmnefd	[sp]!,{r4-r5,lr}

00000300 1affffc2*   544 	bne	encodeDataSetAttrs

                     545 ;185: 	{


                     546 

                     547 ;186: 		return CreateMmsConfirmedErrorPdu(invokeId, outBuf,


                     548 

00000304 e3a02051    549 	mov	r2,81

00000308 e8bd4030    550 	ldmfd	[sp]!,{r4-r5,lr}

0000030c ea000000*   551 	b	CreateMmsConfirmedErrorPdu

                     552 	.endf	getDataSetAccessAttrs

                     553 	.align	4

                     554 ;dataSetPos	r2	local

                     555 

                     556 ;invokeId	r4	param

                     557 ;domainId	none	param

                     558 ;itemId	none	param

                     559 ;outBuf	r5	param

                     560 

                     561 	.section ".bss","awb"

                     562 .L326:

                     563 	.data

                     564 	.text

                     565 

                     566 ;191: }


                     567 

                     568 ;192: 


                     569 ;193: 


                     570 ;194: int mms_handleGetDataSetAccessAttr(MmsConnection* mmsConn,


                     571 	.align	4

                     572 	.align	4

                     573 mms_handleGetDataSetAccessAttr::

00000310 e92d4060    574 	stmfd	[sp]!,{r5-r6,lr}

                     575 ;195:                                 unsigned char* inBuf, int bufPos, int maxBufPos,


                     576 ;196:                                 unsigned int invokeId, unsigned char* response)


                     577 ;197: {


                     578 

                     579 ;198: 	StringView domainId;


                     580 ;199: 	StringView itemId;		


                     581 ;200: 	


                     582 ;201: 	bufPos = BerDecoder_DecodeObjectNameToStringView(inBuf, bufPos, 


                     583 

00000314 e1a0c001    584 	mov	r12,r1

00000318 e1a01002    585 	mov	r1,r2

0000031c e1a02003    586 	mov	r2,r3

00000320 e24dd014    587 	sub	sp,sp,20

00000324 e28d300c    588 	add	r3,sp,12

00000328 e59d5020    589 	ldr	r5,[sp,32]

0000032c e59d6024    590 	ldr	r6,[sp,36]

00000330 e28d0004    591 	add	r0,sp,4

00000334 e58d0000    592 	str	r0,[sp]

00000338 e1a0000c    593 	mov	r0,r12

0000033c eb000000*   594 	bl	BerDecoder_DecodeObjectNameToStringView

                     595 ;202: 		maxBufPos, &domainId, &itemId);


                     596 ;203: 	if (bufPos < 0)


                     597 

00000340 e3500000    598 	cmp	r0,0

00000344 aa000004    599 	bge	.L342


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bl01.s
                     600 ;204: 	{


                     601 

                     602 ;205: 		ERROR_REPORT("Unable to find object");


                     603 ;206: 		return CreateMmsConfirmedErrorPdu(invokeId, response,


                     604 

00000348 e1a01006    605 	mov	r1,r6

0000034c e1a00005    606 	mov	r0,r5

00000350 e3a02051    607 	mov	r2,81

00000354 eb000000*   608 	bl	CreateMmsConfirmedErrorPdu

00000358 ea000004    609 	b	.L340

                     610 .L342:

                     611 ;207: 			MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);


                     612 ;208: 	}	


                     613 ;209: 	


                     614 ;210: 	//TODO Обработать случай когда domainId и itemId не инициализированы


                     615 ;211: 	return getDataSetAccessAttrs(invokeId, &domainId, &itemId, response);


                     616 

0000035c e1a03006    617 	mov	r3,r6

00000360 e28d2004    618 	add	r2,sp,4

00000364 e28d100c    619 	add	r1,sp,12

00000368 e1a00005    620 	mov	r0,r5

0000036c ebffffda*   621 	bl	getDataSetAccessAttrs

                     622 .L340:

00000370 e28dd014    623 	add	sp,sp,20

00000374 e8bd8060    624 	ldmfd	[sp]!,{r5-r6,pc}

                     625 	.endf	mms_handleGetDataSetAccessAttr

                     626 	.align	4

                     627 ;domainId	[sp,12]	local

                     628 ;itemId	[sp,4]	local

                     629 

                     630 ;mmsConn	none	param

                     631 ;inBuf	r12	param

                     632 ;bufPos	none	param

                     633 ;maxBufPos	r4	param

                     634 ;invokeId	r5	param

                     635 ;response	r6	param

                     636 

                     637 	.section ".bss","awb"

                     638 .L374:

                     639 	.data

                     640 	.text

                     641 

                     642 ;212: }


                     643 	.align	4

                     644 .L157:

00000378 00000000*   645 	.data.w	iedModel

                     646 	.type	.L157,$object

                     647 	.size	.L157,4

                     648 

                     649 	.align	4

                     650 ;iedModel	iedModel	import

                     651 

                     652 	.data

                     653 	.ghsnote version,6

                     654 	.ghsnote tools,3

                     655 	.ghsnote options,0

                     656 	.text

                     657 	.align	4

