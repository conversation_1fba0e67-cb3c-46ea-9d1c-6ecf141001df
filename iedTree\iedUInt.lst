                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1mo1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedUInt.c -o iedTree\gh_1mo1.o -list=iedTree/iedUInt.lst C:\Users\<USER>\AppData\Local\Temp\gh_1mo1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_1mo1.s
Source File: iedUInt.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		iedTree/iedUInt.c -o iedTree/iedUInt.o

                      12 ;Source File:   iedTree/iedUInt.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:51 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "iedUInt.h"


                      23 ;2: 


                      24 ;3: #include "iedTree.h"


                      25 ;4: #include "iedFinalDA.h"


                      26 ;5: 


                      27 ;6: #include "../DataSlice.h"


                      28 ;7: #include "../AsnEncoding.h"


                      29 ;8: 


                      30 ;9: #include "debug.h"


                      31 ;10: 


                      32 ;11: #include "IEDCompile/AccessInfo.h"


                      33 ;12: 


                      34 ;13: #define MAX_UINT32_ENCODED_SIZE 7


                      35 ;14: 


                      36 ;15: static void updateFromDataSlice(IEDEntity entity)


                      37 	.text

                      38 	.align	4

                      39 updateFromDataSlice:

00000000 e92d4010     40 	stmfd	[sp]!,{r4,lr}

00000004 e1a04000     41 	mov	r4,r0

                      42 ;16: {


                      43 

                      44 ;17: 	int offset  = entity->dataSliceOffset;


                      45 

00000008 e594002c     46 	ldr	r0,[r4,44]

                      47 ;18:     uint32_t value;


                      48 ;19: 


                      49 ;20: 	if(offset == -1)


                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1mo1.s
0000000c e3700001     51 	cmn	r0,1

00000010 0a00000f     52 	beq	.L2

                      53 ;21: 	{


                      54 

                      55 ;22: 		return;


                      56 

                      57 ;23: 	}


                      58 ;24: 


                      59 ;25:     value = DataSlice_getUInt32FastCurrDS(offset);


                      60 

00000014 e1a00800     61 	mov	r0,r0 lsl 16

00000018 e1a00820     62 	mov	r0,r0 lsr 16

0000001c eb000000*    63 	bl	DataSlice_getUInt32FastCurrDS

                      64 ;26: 


                      65 ;27:     if(entity->uintValue == value)


                      66 

00000020 e5941030     67 	ldr	r1,[r4,48]

00000024 e1510000     68 	cmp	r1,r0

                      69 ;28: 	{


                      70 

                      71 ;29: 		entity->changed = TRGOP_NONE;


                      72 

00000028 03a00000     73 	moveq	r0,0

0000002c 05840028     74 	streq	r0,[r4,40]

00000030 0a000007     75 	beq	.L2

                      76 ;30: 	}


                      77 ;31: 	else


                      78 ;32: 	{


                      79 

                      80 ;33: 		entity->changed = entity->trgOps;


                      81 

00000034 e5941024     82 	ldr	r1,[r4,36]

00000038 e5840030     83 	str	r0,[r4,48]

                      84 ;35: 		IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());


                      85 

0000003c e5841028     86 	str	r1,[r4,40]

                      87 ;34:         entity->uintValue = value;


                      88 

00000040 eb000000*    89 	bl	dataSliceGetTimeStamp

00000044 e1a02001     90 	mov	r2,r1

00000048 e1a01000     91 	mov	r1,r0

0000004c e1a00004     92 	mov	r0,r4

00000050 eb000000*    93 	bl	IEDEntity_setTimeStamp

                      94 .L2:

00000054 e8bd4010     95 	ldmfd	[sp]!,{r4,lr}

00000058 e12fff1e*    96 	ret	

                      97 	.endf	updateFromDataSlice

                      98 	.align	4

                      99 ;offset	r0	local

                     100 ;value	r0	local

                     101 

                     102 ;entity	r4	param

                     103 

                     104 	.section ".bss","awb"

                     105 .L56:

                     106 	.data

                     107 	.text

                     108 

                     109 ;36: 	}


                     110 ;37: }


                     111 


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1mo1.s
                     112 ;38: 


                     113 ;39: static bool calcReadLen(IEDEntity entity, size_t* pLen )


                     114 	.align	4

                     115 	.align	4

                     116 calcReadLen:

0000005c e92d4010    117 	stmfd	[sp]!,{r4,lr}

                     118 ;40: {


                     119 

                     120 ;41: 	// +2 for tag and length


                     121 ;42:     *pLen = BerEncoder_UInt32determineEncodedSize(entity->uintValue) + 2;


                     122 

00000060 e5900030    123 	ldr	r0,[r0,48]

00000064 e1a04001    124 	mov	r4,r1

00000068 eb000000*   125 	bl	BerEncoder_UInt32determineEncodedSize

0000006c e2800002    126 	add	r0,r0,2

00000070 e5840000    127 	str	r0,[r4]

                     128 ;43: 


                     129 ;44: 	return true;


                     130 

00000074 e3a00001    131 	mov	r0,1

00000078 e8bd4010    132 	ldmfd	[sp]!,{r4,lr}

0000007c e12fff1e*   133 	ret	

                     134 	.endf	calcReadLen

                     135 	.align	4

                     136 

                     137 ;entity	r0	param

                     138 ;pLen	r4	param

                     139 

                     140 	.section ".bss","awb"

                     141 .L97:

                     142 	.data

                     143 	.text

                     144 

                     145 ;45: }


                     146 

                     147 ;46: 


                     148 ;47: static bool encodeRead(IEDEntity entity, BufferView* outBuf)


                     149 	.align	4

                     150 	.align	4

                     151 encodeRead:

00000080 e92d4030    152 	stmfd	[sp]!,{r4-r5,lr}

00000084 e24dd004    153 	sub	sp,sp,4

00000088 e1a0200d    154 	mov	r2,sp

0000008c e1a05000    155 	mov	r5,r0

00000090 e1a04001    156 	mov	r4,r1

00000094 e1a00004    157 	mov	r0,r4

00000098 e3a01007    158 	mov	r1,7

0000009c eb000000*   159 	bl	BufferView_alloc

                     160 ;48: {


                     161 

                     162 ;49: 	uint8_t* encodeBuf;


                     163 ;50: 	int fullEncodedLen;


                     164 ;51: 


                     165 ;52: 	//Запрашиваем в буфере максимум места чтобы не вычислять.


                     166 ;53: 	//Это фактически только проверка, поэтому небольшая жадность не повредит.


                     167 ;54:     if(!BufferView_alloc(outBuf,MAX_UINT32_ENCODED_SIZE, &encodeBuf))


                     168 

000000a0 e3500000    169 	cmp	r0,0

                     170 ;55: 	{


                     171 

                     172 ;56: 		ERROR_REPORT("Unable to allocate buffer");



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1mo1.s
                     173 ;57: 		return false;


                     174 

000000a4 0a000008    175 	beq	.L104

                     176 ;58: 	}


                     177 ;59: 


                     178 ;60: 	//Функция возвращает новое смещение в буфере, но поскольку начальное


                     179 ;61: 	//смещение 0, можно считать это размером.


                     180 ;62:     fullEncodedLen = BerEncoder_encodeUInt32WithTL(


                     181 

000000a8 e59d2000    182 	ldr	r2,[sp]

000000ac e5951030    183 	ldr	r1,[r5,48]

000000b0 e3a03000    184 	mov	r3,0

000000b4 e3a00086    185 	mov	r0,134

000000b8 eb000000*   186 	bl	BerEncoder_encodeUInt32WithTL

                     187 ;63:                 IEC61850_BER_UNSIGNED_INTEGER, entity->uintValue, encodeBuf, 0);


                     188 ;64: 


                     189 ;65: 	outBuf->pos += fullEncodedLen;


                     190 

000000bc e5941004    191 	ldr	r1,[r4,4]

000000c0 e0811000    192 	add	r1,r1,r0

000000c4 e5841004    193 	str	r1,[r4,4]

                     194 ;66: 	return true;


                     195 

000000c8 e3a00001    196 	mov	r0,1

                     197 .L104:

000000cc e28dd004    198 	add	sp,sp,4

000000d0 e8bd4030    199 	ldmfd	[sp]!,{r4-r5,lr}

000000d4 e12fff1e*   200 	ret	

                     201 	.endf	encodeRead

                     202 	.align	4

                     203 ;encodeBuf	[sp]	local

                     204 

                     205 ;entity	r5	param

                     206 ;outBuf	r4	param

                     207 

                     208 	.section ".bss","awb"

                     209 .L154:

                     210 	.data

                     211 	.text

                     212 

                     213 ;67: }


                     214 

                     215 ;68: 


                     216 ;69: 


                     217 ;70: void IEDUInt32_init(IEDEntity entity)


                     218 	.align	4

                     219 	.align	4

                     220 IEDUInt32_init::

000000d8 e92d4070    221 	stmfd	[sp]!,{r4-r6,lr}

000000dc e280405c    222 	add	r4,r0,92

000000e0 e5140004    223 	ldr	r0,[r4,-4]

                     224 ;73: 	IntBoolAccessInfo* accessInfo = extInfo->accessInfo;


                     225 

000000e4 e5900000    226 	ldr	r0,[r0]

                     227 ;74: 


                     228 ;75: 	//Если будет ошибка, то запишется -1;


                     229 ;76: 	entity->dataSliceOffset = DataSlice_getIntOffset(accessInfo->valueOffset);


                     230 

000000e8 e59f5028*   231 	ldr	r5,.L197

000000ec e5900004    232 	ldr	r0,[r0,4]

000000f0 e59f6024*   233 	ldr	r6,.L198


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1mo1.s
                     234 ;71: {


                     235 

                     236 ;72: 	TerminalItem* extInfo = entity->extInfo;


                     237 

000000f4 eb000000*   238 	bl	DataSlice_getIntOffset

000000f8 e5040030    239 	str	r0,[r4,-48]

                     240 ;77: 


                     241 ;78: 	entity->calcReadLen = calcReadLen;


                     242 

                     243 ;79: 	entity->encodeRead = encodeRead;


                     244 

000000fc e1a00006    245 	mov	r0,r6

00000100 e8840021    246 	stmea	[r4],{r0,r5}

                     247 ;80: 	entity->updateFromDataSlice = updateFromDataSlice;


                     248 

00000104 e59f0014*   249 	ldr	r0,.L199

00000108 e584000c    250 	str	r0,[r4,12]

                     251 ;81: 


                     252 ;82: 	IEDTree_addToCmpList(entity);


                     253 

0000010c e244005c    254 	sub	r0,r4,92

00000110 e8bd4070    255 	ldmfd	[sp]!,{r4-r6,lr}

00000114 ea000000*   256 	b	IEDTree_addToCmpList

                     257 	.endf	IEDUInt32_init

                     258 	.align	4

                     259 ;extInfo	r0	local

                     260 ;accessInfo	r0	local

                     261 

                     262 ;entity	r4	param

                     263 

                     264 	.section ".bss","awb"

                     265 .L190:

                     266 	.data

                     267 	.text

                     268 

                     269 ;83: }


                     270 	.align	4

                     271 .L197:

00000118 00000000*   272 	.data.w	calcReadLen

                     273 	.type	.L197,$object

                     274 	.size	.L197,4

                     275 

                     276 .L198:

0000011c 00000000*   277 	.data.w	encodeRead

                     278 	.type	.L198,$object

                     279 	.size	.L198,4

                     280 

                     281 .L199:

00000120 00000000*   282 	.data.w	updateFromDataSlice

                     283 	.type	.L199,$object

                     284 	.size	.L199,4

                     285 

                     286 	.align	4

                     287 

                     288 	.data

                     289 	.ghsnote version,6

                     290 	.ghsnote tools,3

                     291 	.ghsnote options,0

                     292 	.text

                     293 	.align	4

