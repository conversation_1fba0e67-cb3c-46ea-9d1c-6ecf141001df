                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cok1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=connections.c -o gh_cok1.o -list=connections.lst C:\Users\<USER>\AppData\Local\Temp\gh_cok1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_cok1.s
Source File: connections.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile connections.c

                      11 ;		-o connections.o

                      12 ;Source File:   connections.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:09 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "connections.h"


                      23 ;2: 


                      24 ;3: #include "mms.h"


                      25 ;4: 


                      26 ;5: #include <stddef.h>


                      27 ;6: #include <stdlib.h>


                      28 ;7: #include <string.h>


                      29 ;8: 


                      30 ;9: #include <debug.h>


                      31 ;10: 


                      32 ;11: IsoConnection* allocateConnection(void)


                      33 	.text

                      34 	.align	4

                      35 allocateConnection::

00000000 e92d4010     36 	stmfd	[sp]!,{r4,lr}

                      37 ;12: {


                      38 

                      39 ;13: 	IsoConnection* allocated = malloc(sizeof(IsoConnection));


                      40 

00000004 e3a00fd8     41 	mov	r0,0x0360

00000008 e2800a56     42 	add	r0,r0,86<<12

0000000c eb000000*    43 	bl	malloc

00000010 e1b04000     44 	movs	r4,r0

                      45 ;14: 	if (allocated == NULL)


                      46 

                      47 ;15: 	{


                      48 

                      49 ;16: 		return NULL;


                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cok1.s
                      51 ;17: 	}


                      52 ;18: 	memset(allocated, 0, sizeof(IsoConnection));


                      53 

00000014 13a02fd8     54 	movne	r2,0x0360

00000018 12822a56     55 	addne	r2,r2,86<<12

0000001c 13a01000     56 	movne	r1,0

00000020 1b000000*    57 	blne	memset

                      58 ;19: 	return allocated;


                      59 

00000024 e1a00004     60 	mov	r0,r4

00000028 e8bd8010     61 	ldmfd	[sp]!,{r4,pc}

                      62 	.endf	allocateConnection

                      63 	.align	4

                      64 ;allocated	r4	local

                      65 

                      66 	.section ".bss","awb"

                      67 .L56:

                      68 	.data

                      69 	.text

                      70 

                      71 ;20: }


                      72 

                      73 ;21: 


                      74 ;22: void freeConnection(IsoConnection* conn)


                      75 	.align	4

                      76 	.align	4

                      77 freeConnection::

                      78 ;23: {


                      79 

                      80 ;24: 	TRACE("Free connection memory");


                      81 ;25: 	free(conn);


                      82 

0000002c ea000000*    83 	b	free

                      84 	.endf	freeConnection

                      85 	.align	4

                      86 

                      87 ;conn	none	param

                      88 

                      89 	.section ".bss","awb"

                      90 .L94:

                      91 	.data

                      92 	.text

                      93 

                      94 ;26: }


                      95 	.align	4

                      96 

                      97 	.data

                      98 	.ghsnote version,6

                      99 	.ghsnote tools,3

                     100 	.ghsnote options,0

                     101 	.text

                     102 	.align	4

