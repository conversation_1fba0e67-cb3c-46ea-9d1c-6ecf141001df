                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aug1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_error.c -o gh_aug1.o -list=mms_error.lst C:\Users\<USER>\AppData\Local\Temp\gh_aug1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_aug1.s
Source File: mms_error.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile mms_error.c -o

                      11 ;		mms_error.o

                      12 ;Source File:   mms_error.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:06 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "mms_error.h"


                      23 ;2: 


                      24 ;3: #include "AsnEncoding.h"


                      25 ;4: #include "MmsConst.h"


                      26 ;5: #include <debug.h>


                      27 ;6: #include <stddef.h>


                      28 ;7: //Для memcpy


                      29 ;8: #include <string.h>


                      30 ;9: 


                      31 ;10: 


                      32 ;11: #define MMS_REJECT_CONFIRMED_REQUEST 1


                      33 ;12: //#define MMS_REJECT_CONFIRMED_RESPONSE 2


                      34 ;13: //#define MMS_REJECT_CONFIRMED_ERROR 3


                      35 ;14: //#define MMS_REJECT_UNCONFIRMED 4


                      36 ;15: #define MMS_REJECT_PDU_ERROR 5


                      37 ;16: //#define MMS_REJECT_CANCEL_REQUEST 6


                      38 ;17: //#define MMS_REJECT_CANCEL_RESPONSE 7


                      39 ;18: //#define MMS_REJECT_CANCEL_ERROR 8


                      40 ;19: //#define MMS_REJECT_CONCLUDE_REQUEST 9


                      41 ;20: //#define MMS_REJECT_CONCLUDE_RESPONSE 10


                      42 ;21: //#define MMS_REJECT_CONCLUDE_ERROR 11


                      43 ;22: 


                      44 ;23: #define MMS_REJECT_CONFIRMED_REQUEST_OTHER 0


                      45 ;24: #define MMS_REJECT_CONFIRMED_REQUEST_UNRECOGNIZED_SERVICE 1


                      46 ;25: //#define MMS_REJECT_CONFIRMED_REQUEST_UNRECOGNIZED_MODIFIER 2


                      47 ;26: //#define MMS_REJECT_CONFIRMED_REQUEST_INVALID_INVOKE_ID 3


                      48 ;27: #define MMS_REJECT_CONFIRMED_REQUEST_INVALID_ARGUMENT 4


                      49 ;28: //#define MMS_REJECT_CONFIRMED_REQUEST_INVALID_MODIFIER 5


                      50 ;29: //#define MMS_REJECT_CONFIRMED_REQUEST_MAX_SERV_OUTSTANDING_EXCEEDED 6



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aug1.s
                      51 ;30: //#define MMS_REJECT_CONFIRMED_REQUEST_MAX_RECURSION_EXCEEDED 8


                      52 ;31: //#define MMS_REJECT_CONFIRMED_REQUEST_VALUE_OUT_OF_RANGE 9


                      53 ;32: 


                      54 ;33: #define MMS_REJECT_PDU_ERROR_UNKNOWN_PDU_TYPE 0


                      55 ;34: #define MMS_REJECT_PDU_ERROR_INVALID_PDU 1


                      56 ;35: //#define MMS_REJECT_PDU_ERROR_ILLEGAL_ACSI_MAPPING 2


                      57 ;36: 


                      58 ;37: static int encodeMmsRejectPdu(unsigned int* invokeId, int rejectType, int rejectReason,


                      59 

                      60 ;62: }


                      61 

                      62 ;63: 


                      63 ;64: int mms_createMmsRejectPdu(unsigned int* invokeId, int reason, uint8_t* outBuf)


                      64 	.text

                      65 	.align	4

                      66 mms_createMmsRejectPdu::

00000000 e92d44f0     67 	stmfd	[sp]!,{r4-r7,r10,lr}

                      68 ;65: {


                      69 

                      70 ;66:     int rejectType = 0;


                      71 

                      72 ;67:     int rejectReason = 0;


                      73 

                      74 ;68: 


                      75 ;69:     switch (reason) {


                      76 

00000004 e2511065     77 	subs	r1,r1,101

00000008 3a00000d     78 	blo	.L33

                      79 ;74:         break;


                      80 ;75: 


                      81 ;76:     case MMS_ERROR_REJECT_UNKNOWN_PDU_TYPE:


                      82 ;77:         rejectType = MMS_REJECT_PDU_ERROR;


                      83 

0000000c 03a06005     84 	moveq	r6,5

                      85 ;78:         rejectReason = MMS_REJECT_PDU_ERROR_UNKNOWN_PDU_TYPE;


                      86 

00000010 03a07000     87 	moveq	r7,0

00000014 0a00000c     88 	beq	.L27

00000018 e2511002     89 	subs	r1,r1,2

                      90 ;84:         break;


                      91 ;85: 


                      92 ;86:     case MMS_ERROR_REJECT_INVALID_PDU:


                      93 ;87:         rejectType = MMS_REJECT_PDU_ERROR;


                      94 

0000001c 33a06005     95 	movlo	r6,5

                      96 ;88:         rejectReason = MMS_REJECT_PDU_ERROR_INVALID_PDU;


                      97 

00000020 33a07001     98 	movlo	r7,1

00000024 3a000008     99 	blo	.L27

                     100 ;70: 


                     101 ;71:     case MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE:


                     102 ;72:         rejectType = MMS_REJECT_CONFIRMED_REQUEST;


                     103 

00000028 03a06001    104 	moveq	r6,1

                     105 ;73:         rejectReason = MMS_REJECT_CONFIRMED_REQUEST_UNRECOGNIZED_SERVICE;


                     106 

0000002c 01a07006    107 	moveq	r7,r6

00000030 0a000005    108 	beq	.L27

00000034 e3510002    109 	cmp	r1,2

                     110 ;79:         break;


                     111 ;80: 



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aug1.s
                     112 ;81:     case MMS_ERROR_REJECT_REQUEST_INVALID_ARGUMENT:


                     113 ;82:         rejectType = MMS_REJECT_CONFIRMED_REQUEST;


                     114 

00000038 03a06001    115 	moveq	r6,1

                     116 ;83:         rejectReason = MMS_REJECT_CONFIRMED_REQUEST_INVALID_ARGUMENT;


                     117 

0000003c 03a07004    118 	moveq	r7,4

00000040 0a000001    119 	beq	.L27

                     120 .L33:

                     121 ;89:         break;


                     122 ;90: 


                     123 ;91:     default:


                     124 ;92:         rejectType = MMS_REJECT_CONFIRMED_REQUEST;


                     125 

00000044 e3a06001    126 	mov	r6,1

                     127 ;93:         rejectReason = MMS_REJECT_CONFIRMED_REQUEST_OTHER;


                     128 

00000048 e3a07000    129 	mov	r7,0

                     130 .L27:

                     131 ;94:     }


                     132 ;95: 


                     133 ;96:     return encodeMmsRejectPdu(invokeId, rejectType, rejectReason, outBuf);


                     134 

0000004c e1b05000    135 	movs	r5,r0

00000050 e1a04002    136 	mov	r4,r2

                     137 ;38:                                uint8_t* outBuf)


                     138 ;39: {


                     139 

00000054 e3a0a000    140 	mov	r10,0

00000058 e3a01003    141 	mov	r1,3

                     142 ;40:     int outBufPos = 0;


                     143 

                     144 ;41:     unsigned int invokeIdLength = 0;


                     145 

                     146 ;42:     unsigned int rejectPduLength = 3;


                     147 

                     148 ;43: 


                     149 ;44:     if (invokeId != NULL) {


                     150 

0000005c 0a000003    151 	beq	.L38

                     152 ;45:         invokeIdLength = BerEncoder_UInt32determineEncodedSize(*invokeId);


                     153 

00000060 e5950000    154 	ldr	r0,[r5]

00000064 eb000000*   155 	bl	BerEncoder_UInt32determineEncodedSize

00000068 e1a0a000    156 	mov	r10,r0

                     157 ;46:         rejectPduLength += 2 + invokeIdLength;


                     158 

0000006c e28a1005    159 	add	r1,r10,5

                     160 .L38:

                     161 ;47:     }


                     162 ;48: 


                     163 ;49:     /* Encode reject PDU */


                     164 ;50:     outBufPos = BerEncoder_encodeTL(0xa4, rejectPduLength, outBuf, outBufPos);


                     165 

00000070 e1a02004    166 	mov	r2,r4

00000074 e3a03000    167 	mov	r3,0

00000078 e3a000a4    168 	mov	r0,164

0000007c eb000000*   169 	bl	BerEncoder_encodeTL

00000080 e1a03000    170 	mov	r3,r0

                     171 ;51: 


                     172 ;52:     if (invokeId != NULL) {



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aug1.s
                     173 

00000084 e3550000    174 	cmp	r5,0

00000088 0a000008    175 	beq	.L40

                     176 ;53:        outBufPos = BerEncoder_encodeTL(0x80, invokeIdLength, outBuf, outBufPos);


                     177 

0000008c e1a02004    178 	mov	r2,r4

00000090 e1a0100a    179 	mov	r1,r10

00000094 e3a00080    180 	mov	r0,128

00000098 eb000000*   181 	bl	BerEncoder_encodeTL

                     182 ;54:        outBufPos = BerEncoder_encodeUInt32(*invokeId, outBuf, outBufPos);


                     183 

0000009c e1a02000    184 	mov	r2,r0

000000a0 e5950000    185 	ldr	r0,[r5]

000000a4 e1a01004    186 	mov	r1,r4

000000a8 eb000000*   187 	bl	BerEncoder_encodeUInt32

000000ac e1a03000    188 	mov	r3,r0

                     189 .L40:

                     190 ;55:     }


                     191 ;56: 


                     192 ;57:     outBuf[outBufPos++] = (uint8_t) (0x80 + rejectType);


                     193 

000000b0 e2860080    194 	add	r0,r6,128

000000b4 e7c40003    195 	strb	r0,[r4,r3]

000000b8 e2833001    196 	add	r3,r3,1

                     197 ;58:     outBuf[outBufPos++] = 0x01;


                     198 

000000bc e3a00001    199 	mov	r0,1

000000c0 e7c40003    200 	strb	r0,[r4,r3]

000000c4 e2833001    201 	add	r3,r3,1

                     202 ;59:     outBuf[outBufPos++] = (uint8_t) rejectReason;


                     203 

000000c8 e7c47003    204 	strb	r7,[r4,r3]

                     205 ;60: 


                     206 ;61:     return outBufPos;


                     207 

000000cc e2830001    208 	add	r0,r3,1

000000d0 e8bd84f0    209 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     210 	.endf	mms_createMmsRejectPdu

                     211 	.align	4

                     212 ;rejectType	r6	local

                     213 ;rejectReason	r7	local

                     214 ;invokeId	r5	local

                     215 ;outBuf	r4	local

                     216 ;outBufPos	r3	local

                     217 ;invokeIdLength	r10	local

                     218 ;rejectPduLength	r1	local

                     219 

                     220 ;invokeId	r0	param

                     221 ;reason	r1	param

                     222 ;outBuf	r2	param

                     223 

                     224 	.section ".bss","awb"

                     225 .L119:

                     226 	.data

                     227 	.text

                     228 

                     229 ;97: }


                     230 

                     231 ;98: 


                     232 ;99: 


                     233 ;100: int CreateMmsConfirmedErrorPdu( unsigned int iInvokeId, unsigned char* pResponseBuffer,



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aug1.s
                     234 	.align	4

                     235 	.align	4

                     236 CreateMmsConfirmedErrorPdu::

000000d4 e92d44f0    237 	stmfd	[sp]!,{r4-r7,r10,lr}

000000d8 e1a05001    238 	mov	r5,r1

                     239 ;101:                                 MmsError ErrorType )


                     240 ;102: {


                     241 

000000dc e59f1128*   242 	ldr	r1,.L315

000000e0 e5913000    243 	ldr	r3,[r1]

000000e4 e1a04002    244 	mov	r4,r2

000000e8 e52d3008    245 	str	r3,[sp,-8]!

000000ec e1d130b4    246 	ldrh	r3,[r1,4]

000000f0 e1a07000    247 	mov	r7,r0

000000f4 e1cd30b4    248 	strh	r3,[sp,4]

000000f8 e5d11006    249 	ldrb	r1,[r1,6]

000000fc e3a0a000    250 	mov	r10,0

                     251 ;103:     unsigned char ErrorCodeBuf[] = { 0xa2, 0x05, 0xa0, 0x03, 0x87, 0x01, 0x01 };


                     252 

                     253 ;104:     int iErrorCodeBufSize = 7;


                     254 

                     255 ;105: 


                     256 ;106:     int iIvokeIdSize = 2 + BerEncoder_UInt32determineEncodedSize( iInvokeId );


                     257 

00000100 e5cd1006    258 	strb	r1,[sp,6]

00000104 eb000000*   259 	bl	BerEncoder_UInt32determineEncodedSize

00000108 e2806004    260 	add	r6,r0,4

                     261 ;107:     int iPduLength = 2 + iIvokeIdSize;


                     262 

                     263 ;108: 


                     264 ;109:     int iBufPos = 0;


                     265 

                     266 ;110:     int exists_error = 0;


                     267 

                     268 ;111: 


                     269 ;112:     if (ErrorType == MMS_ERROR_ACCESS_OTHER) {


                     270 

0000010c e3540050    271 	cmp	r4,80

                     272 ;113:         ErrorCodeBuf[6] = 0x00;


                     273 

00000110 03a00000    274 	moveq	r0,0

00000114 05cd0006    275 	streqb	r0,[sp,6]

                     276 ;114:         exists_error = 1;


                     277 

00000118 03a0a001    278 	moveq	r10,1

                     279 ;115:     }


                     280 ;116:     if (ErrorType == MMS_ERROR_ACCESS_OBJECT_ACCESS_UNSUPPORTED) {


                     281 

0000011c e3540052    282 	cmp	r4,82

                     283 ;117:         ErrorCodeBuf[6] = 0x01;


                     284 

00000120 03a0a001    285 	moveq	r10,1

00000124 05cda006    286 	streqb	r10,[sp,6]

                     287 ;118:         exists_error = 1;


                     288 

                     289 ;119:     }


                     290 ;120:     if (ErrorType == MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT) {


                     291 

00000128 e3540051    292 	cmp	r4,81

                     293 ;121:         ErrorCodeBuf[6] = 0x02;


                     294 


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aug1.s
0000012c 03a00002    295 	moveq	r0,2

00000130 05cd0006    296 	streqb	r0,[sp,6]

                     297 ;122:         exists_error = 1;


                     298 

00000134 03a0a001    299 	moveq	r10,1

                     300 ;123:     }


                     301 ;124:     if (ErrorType == MMS_ERROR_ACCESS_OBJECT_ACCESS_DENIED) {


                     302 

00000138 e3540053    303 	cmp	r4,83

                     304 ;125:         ErrorCodeBuf[6] = 0x03;


                     305 

0000013c 03a00003    306 	moveq	r0,3

00000140 05cd0006    307 	streqb	r0,[sp,6]

                     308 ;126:         exists_error = 1;


                     309 

00000144 03a0a001    310 	moveq	r10,1

                     311 ;127:     }


                     312 ;128:     if (ErrorType == MMS_ERROR_ACCESS_OBJECT_INVALIDATED) {


                     313 

00000148 e3540054    314 	cmp	r4,84

                     315 ;129:         ErrorCodeBuf[6] = 0x04;


                     316 

0000014c 03a00004    317 	moveq	r0,4

00000150 05cd0006    318 	streqb	r0,[sp,6]

                     319 ;130:         exists_error = 1;


                     320 

00000154 0a000002    321 	beq	.L167

                     322 ;131:     }


                     323 ;132: 


                     324 ;133:     if (exists_error != 0) {


                     325 

00000158 e35a0000    326 	cmp	r10,0

                     327 ;141:     }


                     328 ;142:     else {


                     329 

                     330 ;143:         return -1;


                     331 

0000015c 03e00000    332 	mvneq	r0,0

00000160 0a00000e    333 	beq	.L154

                     334 .L167:

                     335 ;134:         iPduLength += iErrorCodeBufSize;


                     336 

                     337 ;135:         iBufPos = BerEncoder_encodeTL(MMS_CONFIRMED_ERRROR_PDU, iPduLength - 2,


                     338 

00000164 e1a02005    339 	mov	r2,r5

00000168 e2861005    340 	add	r1,r6,5

0000016c e3a03000    341 	mov	r3,0

00000170 e3a000a2    342 	mov	r0,162

00000174 eb000000*   343 	bl	BerEncoder_encodeTL

                     344 ;136:             pResponseBuffer, iBufPos);


                     345 ;137:         iBufPos = BerEncoder_encodeUInt32WithTL(0x80, iInvokeId,


                     346 

00000178 e1a02005    347 	mov	r2,r5

0000017c e1a01007    348 	mov	r1,r7

00000180 e1a03000    349 	mov	r3,r0

00000184 e3a00080    350 	mov	r0,128

00000188 eb000000*   351 	bl	BerEncoder_encodeUInt32WithTL

                     352 ;138:             pResponseBuffer, iBufPos);


                     353 ;139:         memcpy(&pResponseBuffer[iBufPos], ErrorCodeBuf, iErrorCodeBufSize);


                     354 

0000018c e1a0100d    355 	mov	r1,sp


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aug1.s
00000190 e0800005    356 	add	r0,r0,r5

00000194 e3a02007    357 	mov	r2,7

00000198 eb000000*   358 	bl	memcpy

0000019c e2860007    359 	add	r0,r6,7

                     360 ;140:         return iPduLength;


                     361 

                     362 .L154:

000001a0 e28dd008    363 	add	sp,sp,8

000001a4 e8bd84f0    364 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     365 	.endf	CreateMmsConfirmedErrorPdu

                     366 	.align	4

                     367 ;ErrorCodeBuf	[sp]	local

                     368 ;.L279	.L282	static

                     369 ;iPduLength	r6	local

                     370 ;exists_error	r10	local

                     371 

                     372 ;iInvokeId	r7	param

                     373 ;pResponseBuffer	r5	param

                     374 ;ErrorType	r4	param

                     375 

                     376 	.section ".bss","awb"

                     377 .L278:

                     378 	.section ".rodata","a"

00000000 03a005a2    379 .L282:	.data.b	162,5,160,3

00000004 0187       380 	.data.b	135,1

00000006 01         381 	.data.b	1

00000007 00         382 	.space	1

                     383 	.type	.L282,$object

                     384 	.size	.L282,8

                     385 	.data

                     386 	.text

                     387 

                     388 ;144:     }


                     389 ;145: }


                     390 

                     391 ;146: 


                     392 ;147: 


                     393 ;148: bool MMSError_createConfirmedErrorPdu(uint32_t invokeId, MmsError errorType,


                     394 	.align	4

                     395 	.align	4

                     396 MMSError_createConfirmedErrorPdu::

000001a8 e92d4070    397 	stmfd	[sp]!,{r4-r6,lr}

                     398 ;149:     BufferView* outBufView)


                     399 ;150: {   


                     400 

                     401 ;151:     // Это обёртка над CreateMmsConfirmedErrorPdu


                     402 ;152:     size_t maxErrPduLen = 15;


                     403 

                     404 ;153:     int pduLen;


                     405 ;154:     uint8_t* pduBuf;


                     406 ;155:     if (!BufferView_alloc(outBufView, maxErrPduLen, &pduBuf))


                     407 

000001ac e24dd004    408 	sub	sp,sp,4

000001b0 e1a04002    409 	mov	r4,r2

000001b4 e1a0200d    410 	mov	r2,sp

000001b8 e1a05000    411 	mov	r5,r0

000001bc e1a00004    412 	mov	r0,r4

000001c0 e1a06001    413 	mov	r6,r1

000001c4 e3a0100f    414 	mov	r1,15

000001c8 eb000000*   415 	bl	BufferView_alloc

000001cc e3500000    416 	cmp	r0,0


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aug1.s
000001d0 0a00000a    417 	beq	.L325

                     418 ;156:     {


                     419 

                     420 ;157:         ERROR_REPORT("Unable to allocate buffer");


                     421 ;158:         return false;


                     422 

                     423 ;159:     }


                     424 ;160:     pduLen = CreateMmsConfirmedErrorPdu(invokeId, pduBuf, errorType);


                     425 

000001d4 e1a02006    426 	mov	r2,r6

000001d8 e59d1000    427 	ldr	r1,[sp]

000001dc e1a00005    428 	mov	r0,r5

000001e0 ebffffbb*   429 	bl	CreateMmsConfirmedErrorPdu

000001e4 e1b01000    430 	movs	r1,r0

                     431 ;161:     if (pduLen < 0)


                     432 

000001e8 4a000004    433 	bmi	.L325

                     434 ;162:     {


                     435 

                     436 ;163:         ERROR_REPORT("Unable to create MMS error PDU");


                     437 ;164:         return false;


                     438 

                     439 ;165:     }


                     440 ;166:     if (!BufferView_advance(outBufView, pduLen))


                     441 

000001ec e1a00004    442 	mov	r0,r4

000001f0 eb000000*   443 	bl	BufferView_advance

000001f4 e3500000    444 	cmp	r0,0

                     445 ;170:     }


                     446 ;171:     return true; 


                     447 

000001f8 13a00001    448 	movne	r0,1

000001fc 1a000000    449 	bne	.L316

                     450 .L325:

                     451 ;167:     {


                     452 

                     453 ;168:         ERROR_REPORT("Unable to advance buffer view");


                     454 ;169:         return false;


                     455 

00000200 e3a00000    456 	mov	r0,0

                     457 .L316:

00000204 e28dd004    458 	add	sp,sp,4

00000208 e8bd8070    459 	ldmfd	[sp]!,{r4-r6,pc}

                     460 	.endf	MMSError_createConfirmedErrorPdu

                     461 	.align	4

                     462 ;pduLen	r1	local

                     463 ;pduBuf	[sp]	local

                     464 

                     465 ;invokeId	r5	param

                     466 ;errorType	r6	param

                     467 ;outBufView	r4	param

                     468 

                     469 	.section ".bss","awb"

                     470 .L390:

                     471 	.data

                     472 	.text

                     473 

                     474 ;172: }


                     475 	.align	4

                     476 .L315:

0000020c 00000000*   477 	.data.w	.L282


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aug1.s
                     478 	.type	.L315,$object

                     479 	.size	.L315,4

                     480 

                     481 	.align	4

                     482 

                     483 	.data

                     484 	.ghsnote version,6

                     485 	.ghsnote tools,3

                     486 	.ghsnote options,0

                     487 	.text

                     488 	.align	4

                     489 	.section ".rodata","a"

                     490 	.align	4

                     491 	.text

