                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ais1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedQuality.c -o iedTree\gh_ais1.o -list=iedTree/iedQuality.lst C:\Users\<USER>\AppData\Local\Temp\gh_ais1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_ais1.s
Source File: iedQuality.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		iedTree/iedQuality.c -o iedTree/iedQuality.o

                      12 ;Source File:   iedTree/iedQuality.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:50 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "iedQuality.h"


                      23 ;2: 


                      24 ;3: 


                      25 ;4: #include "debug.h"


                      26 ;5: #include "../DataSlice.h"


                      27 ;6: #include "iedFinalDA.h"


                      28 ;7: #include "iedTree.h"


                      29 ;8: #include "../AsnEncoding.h"


                      30 ;9: #include "../BusError.h"


                      31 ;10: 


                      32 ;11: #define QUALITY_ENCODED_SIZE 5


                      33 ;12: 


                      34 ;13: 


                      35 ;14: // Получает Quality из текущего "захваченного" DataSlice.


                      36 ;15: // Поддерживаются только два самых важных бита.


                      37 ;16: // Возвращаемое значение подготовлено к отправке в виде


                      38 ;17: // двухбайтного буфера в формате BER bit string: little endian и LSB->MSB.


                      39 ;18: // То есть bit 0 Quality это bit 7 результата функции.


                      40 ;19: // А bit 1 Quality это bit 6 результата


                      41 ;20: static uint16_t getQualityFastCurrDS(QualityAccsessInfo* accessInfo)


                      42 

                      43 ;41: }


                      44 

                      45 ;42: 


                      46 ;43: static void updateFromDataSlice(IEDEntity entity)


                      47 	.text

                      48 	.align	4

                      49 updateFromDataSlice:

00000000 e92d4070     50 	stmfd	[sp]!,{r4-r6,lr}


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ais1.s
00000004 e1a05000     51 	mov	r5,r0

                      52 ;44: {


                      53 

                      54 ;45: 	TerminalItem* termItem = entity->extInfo;


                      55 

00000008 e5956058     56 	ldr	r6,[r5,88]

                      57 ;46: 	uint16_t value;


                      58 ;47: 


                      59 ;48: 	if(!BusError_check())


                      60 

0000000c eb000000*    61 	bl	BusError_check

00000010 e3500000     62 	cmp	r0,0

00000014 1a000004     63 	bne	.L34

                      64 ;49: 	{


                      65 

                      66 ;50: 		//Ставим bit 0


                      67 ;51: 		// Из за хитрого формата quality, который здесь используется


                      68 ;52: 		// (смотри функцию getQualityFastCurrDS), bit 0 это bit 7


                      69 ;53: 		value = 0x40;


                      70 

00000018 e1d503b0     71 	ldrh	r0,[r5,48]

0000001c e3a04040     72 	mov	r4,64

                      73 ;58: 	}


                      74 ;59: 


                      75 ;60: 	if(entity->qualityValue == value)


                      76 

00000020 e3500040     77 	cmp	r0,64

00000024 1a000017     78 	bne	.L42

00000028 ea000013     79 	b	.L43

                      80 .L34:

                      81 ;54: 	}


                      82 ;55: 	else


                      83 ;56: 	{


                      84 

                      85 ;57: 		 value = getQualityFastCurrDS(&termItem->q.accessInfo);


                      86 

                      87 ;21: {


                      88 

0000002c e5960008     89 	ldr	r0,[r6,8]

                      90 ;29: 	if(offset != -1 && DataSlice_getBoolFastCurrDS(offset))


                      91 

00000030 e3a04000     92 	mov	r4,0

                      93 ;22: 	int offset;


                      94 ;23: 	uint16_t quality = 0;


                      95 

                      96 ;24: 


                      97 ;25: 	// Функция поддерживает только 2 бита Q потому что


                      98 ;26: 	// остальные не нужны(пока?) и	 только добавят тормозов


                      99 ;27: 


                     100 ;28: 	offset = accessInfo->goodInvalidOffset;


                     101 

00000034 e3700001    102 	cmn	r0,1

00000038 0a000004    103 	beq	.L37

0000003c e1a00800    104 	mov	r0,r0 lsl 16

00000040 e1a00820    105 	mov	r0,r0 lsr 16

00000044 eb000000*   106 	bl	DataSlice_getBoolFastCurrDS

00000048 e3500000    107 	cmp	r0,0

                     108 ;30: 	{


                     109 

                     110 ;31: 		quality |= (1 << 6);


                     111 


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ais1.s
0000004c 13a04040    112 	movne	r4,64

                     113 .L37:

                     114 ;32: 	}


                     115 ;33: 


                     116 ;34: 	offset = accessInfo->reservedQuestionableOffset;


                     117 

00000050 e596000c    118 	ldr	r0,[r6,12]

                     119 ;35: 	if(offset != -1 && DataSlice_getBoolFastCurrDS(offset))


                     120 

00000054 e3700001    121 	cmn	r0,1

00000058 0a000004    122 	beq	.L32

0000005c e1a00800    123 	mov	r0,r0 lsl 16

00000060 e1a00820    124 	mov	r0,r0 lsr 16

00000064 eb000000*   125 	bl	DataSlice_getBoolFastCurrDS

00000068 e3500000    126 	cmp	r0,0

                     127 ;36: 	{


                     128 

                     129 ;37: 		quality |= (1 << 7);


                     130 

0000006c 13844080    131 	orrne	r4,r4,128

                     132 .L32:

                     133 ;38: 	}


                     134 ;39: 


                     135 ;40: 	return quality;


                     136 

                     137 ;58: 	}


                     138 ;59: 


                     139 ;60: 	if(entity->qualityValue == value)


                     140 

00000070 e1d503b0    141 	ldrh	r0,[r5,48]

00000074 e1500004    142 	cmp	r0,r4

00000078 1a000002    143 	bne	.L42

                     144 .L43:

                     145 ;61: 	{


                     146 

                     147 ;62: 		entity->changed = TRGOP_NONE;


                     148 

0000007c e3a00000    149 	mov	r0,0

00000080 e5850028    150 	str	r0,[r5,40]

00000084 ea000007    151 	b	.L27

                     152 .L42:

                     153 ;63: 	}


                     154 ;64: 	else


                     155 ;65: 	{


                     156 

                     157 ;66: 		entity->changed = entity->trgOps;


                     158 

00000088 e5950024    159 	ldr	r0,[r5,36]

0000008c e1c543b0    160 	strh	r4,[r5,48]

                     161 ;68: 		IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());


                     162 

00000090 e5850028    163 	str	r0,[r5,40]

                     164 ;67: 		entity->qualityValue = value;


                     165 

00000094 eb000000*   166 	bl	dataSliceGetTimeStamp

00000098 e1a02001    167 	mov	r2,r1

0000009c e1a01000    168 	mov	r1,r0

000000a0 e1a00005    169 	mov	r0,r5

000000a4 eb000000*   170 	bl	IEDEntity_setTimeStamp

                     171 .L27:

000000a8 e8bd4070    172 	ldmfd	[sp]!,{r4-r6,lr}


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ais1.s
000000ac e12fff1e*   173 	ret	

                     174 	.endf	updateFromDataSlice

                     175 	.align	4

                     176 ;termItem	r6	local

                     177 ;value	r4	local

                     178 ;offset	r0	local

                     179 ;quality	r4	local

                     180 

                     181 ;entity	r5	param

                     182 

                     183 	.section ".bss","awb"

                     184 .L142:

                     185 	.data

                     186 	.text

                     187 

                     188 ;69: 	}


                     189 ;70: }


                     190 

                     191 ;71: 


                     192 ;72: static bool calcReadLen(IEDEntity entity, size_t* pLen )


                     193 	.align	4

                     194 	.align	4

                     195 calcReadLen:

                     196 ;73: {


                     197 

                     198 ;74: 	*pLen = QUALITY_ENCODED_SIZE;


                     199 

000000b0 e3a00005    200 	mov	r0,5

000000b4 e5810000    201 	str	r0,[r1]

                     202 ;75: 	return true;


                     203 

000000b8 e3a00001    204 	mov	r0,1

000000bc e12fff1e*   205 	ret	

                     206 	.endf	calcReadLen

                     207 	.align	4

                     208 

                     209 ;entity	none	param

                     210 ;pLen	r1	param

                     211 

                     212 	.section ".bss","awb"

                     213 .L206:

                     214 	.data

                     215 	.text

                     216 

                     217 ;76: }


                     218 

                     219 ;77: 


                     220 ;78: static bool encodeRead(IEDEntity entity, BufferView* outBuf)


                     221 	.align	4

                     222 	.align	4

                     223 encodeRead:

000000c0 e92d4030    224 	stmfd	[sp]!,{r4-r5,lr}

000000c4 e24dd008    225 	sub	sp,sp,8

000000c8 e28d2004    226 	add	r2,sp,4

000000cc e1a05000    227 	mov	r5,r0

000000d0 e1a04001    228 	mov	r4,r1

000000d4 e1a00004    229 	mov	r0,r4

000000d8 e3a01005    230 	mov	r1,5

000000dc eb000000*   231 	bl	BufferView_alloc

                     232 ;79: {


                     233 


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ais1.s
                     234 ;80: 	uint8_t* encodeBuf;


                     235 ;81: 


                     236 ;82: 	if(!BufferView_alloc(outBuf,QUALITY_ENCODED_SIZE, &encodeBuf))


                     237 

000000e0 e3500000    238 	cmp	r0,0

                     239 ;83: 	{


                     240 

                     241 ;84: 		ERROR_REPORT("Unable to allocat buffer");


                     242 ;85: 		return false;


                     243 

000000e4 0a00000a    244 	beq	.L213

                     245 ;86: 	}


                     246 ;87: 


                     247 ;88: 	// Возвращаемое значение не нужно,


                     248 ;89: 	// потому что функция не возвращает ошибки, а размер известен заранее


                     249 ;90: 	BerEncoder_encodeBitStringUshortBuf(ASN_TYPEDESCRIPTION_BIT_STRING,


                     250 

000000e8 e3a00000    251 	mov	r0,0

000000ec e58d0000    252 	str	r0,[sp]

000000f0 e59d3004    253 	ldr	r3,[sp,4]

000000f4 e1d523b0    254 	ldrh	r2,[r5,48]

000000f8 e3a0100d    255 	mov	r1,13

000000fc e3a00084    256 	mov	r0,132

00000100 eb000000*   257 	bl	BerEncoder_encodeBitStringUshortBuf

                     258 ;91: 		13, entity->qualityValue, encodeBuf, 0);


                     259 ;92: 	outBuf->pos += QUALITY_ENCODED_SIZE;


                     260 

00000104 e5941004    261 	ldr	r1,[r4,4]

00000108 e3a00001    262 	mov	r0,1

0000010c e2811005    263 	add	r1,r1,5

00000110 e5841004    264 	str	r1,[r4,4]

                     265 ;93: 	return true;


                     266 

                     267 .L213:

00000114 e28dd008    268 	add	sp,sp,8

00000118 e8bd4030    269 	ldmfd	[sp]!,{r4-r5,lr}

0000011c e12fff1e*   270 	ret	

                     271 	.endf	encodeRead

                     272 	.align	4

                     273 ;encodeBuf	[sp,4]	local

                     274 

                     275 ;entity	r5	param

                     276 ;outBuf	r4	param

                     277 

                     278 	.section ".bss","awb"

                     279 .L266:

                     280 	.data

                     281 	.text

                     282 

                     283 ;94: }


                     284 

                     285 ;95: 


                     286 ;96: void IEDQuality_init(IEDEntity entity)


                     287 	.align	4

                     288 	.align	4

                     289 IEDQuality_init::

00000120 e92d44f0    290 	stmfd	[sp]!,{r4-r7,r10,lr}

00000124 e59f60e4*   291 	ldr	r6,.L312

00000128 e280505c    292 	add	r5,r0,92

0000012c e5154004    293 	ldr	r4,[r5,-4]

                     294 ;107: 	accessInfo = extInfo->accessInfo;



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ais1.s
                     295 

00000130 e59f70dc*   296 	ldr	r7,.L313

                     297 ;97: {


                     298 

                     299 ;98: 	TerminalItem* extInfo;


                     300 ;99: 	// accessInfo в элементе дерева, настраивается на смещения в DataSlice


                     301 ;100: 	QualityAccsessInfo* qAccessInfo;


                     302 ;101: 	//accessInfo из бинарника модели


                     303 ;102: 	QualityAccsessInfo* accessInfo;


                     304 ;103: 


                     305 ;104: 


                     306 ;105: 


                     307 ;106: 	extInfo = entity->extInfo;


                     308 

00000134 e594a000    309 	ldr	r10,[r4]

                     310 ;108: 	qAccessInfo = &extInfo->q.accessInfo;


                     311 

00000138 e284c004    312 	add	r12,r4,4

                     313 ;109: 	*qAccessInfo = *accessInfo;


                     314 

0000013c e8ba000f    315 	ldmfd	[r10]!,{r0-r3}

00000140 e8ac000f    316 	stmea	[r12]!,{r0-r3}

00000144 e8ba000f    317 	ldmfd	[r10]!,{r0-r3}

00000148 e8ac000f    318 	stmea	[r12]!,{r0-r3}

0000014c e8ba000f    319 	ldmfd	[r10]!,{r0-r3}

00000150 e8ac000f    320 	stmea	[r12]!,{r0-r3}

00000154 e89a0007    321 	ldmfd	[r10],{r0-r2}

00000158 e88c0007    322 	stmea	[r12],{r0-r2}

                     323 ;110: 


                     324 ;111: 


                     325 ;112: 


                     326 ;113: 	//Заменяем абсолютные смещения на смещения в DataSlice


                     327 ;114: 	qAccessInfo->goodInvalidOffset =


                     328 

0000015c e5940008    329 	ldr	r0,[r4,8]

00000160 eb000000*   330 	bl	DataSlice_getBoolOffset

00000164 e5840008    331 	str	r0,[r4,8]

                     332 ;115: 			DataSlice_getBoolOffset(qAccessInfo->goodInvalidOffset);


                     333 ;116: 	qAccessInfo->reservedQuestionableOffset =


                     334 

00000168 e594000c    335 	ldr	r0,[r4,12]

0000016c eb000000*   336 	bl	DataSlice_getBoolOffset

00000170 e584000c    337 	str	r0,[r4,12]

                     338 ;117: 			DataSlice_getBoolOffset(qAccessInfo->reservedQuestionableOffset);


                     339 ;118: 	qAccessInfo->overflowOffset =


                     340 

00000174 e5940010    341 	ldr	r0,[r4,16]

00000178 eb000000*   342 	bl	DataSlice_getBoolOffset

0000017c e5840010    343 	str	r0,[r4,16]

                     344 ;119: 			DataSlice_getBoolOffset(qAccessInfo->overflowOffset);


                     345 ;120: 	qAccessInfo->outOfRangeOffset =


                     346 

00000180 e5940014    347 	ldr	r0,[r4,20]

00000184 eb000000*   348 	bl	DataSlice_getBoolOffset

00000188 e5840014    349 	str	r0,[r4,20]

                     350 ;121: 			DataSlice_getBoolOffset(qAccessInfo->outOfRangeOffset);


                     351 ;122: 	qAccessInfo->badReferenceOffset =


                     352 

0000018c e5940018    353 	ldr	r0,[r4,24]

00000190 eb000000*   354 	bl	DataSlice_getBoolOffset

00000194 e5840018    355 	str	r0,[r4,24]


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ais1.s
                     356 ;123: 			DataSlice_getBoolOffset(qAccessInfo->badReferenceOffset);


                     357 ;124: 	qAccessInfo->oscillatoryOffset =


                     358 

00000198 e594001c    359 	ldr	r0,[r4,28]

0000019c eb000000*   360 	bl	DataSlice_getBoolOffset

000001a0 e584001c    361 	str	r0,[r4,28]

                     362 ;125: 			DataSlice_getBoolOffset(qAccessInfo->oscillatoryOffset);


                     363 ;126: 	qAccessInfo->failureOffset =


                     364 

000001a4 e5940020    365 	ldr	r0,[r4,32]

000001a8 eb000000*   366 	bl	DataSlice_getBoolOffset

000001ac e5840020    367 	str	r0,[r4,32]

                     368 ;127: 			DataSlice_getBoolOffset(qAccessInfo->failureOffset);


                     369 ;128: 	qAccessInfo->oldDataOffset =


                     370 

000001b0 e5940024    371 	ldr	r0,[r4,36]

000001b4 eb000000*   372 	bl	DataSlice_getBoolOffset

000001b8 e5840024    373 	str	r0,[r4,36]

                     374 ;129: 			DataSlice_getBoolOffset(qAccessInfo->oldDataOffset);


                     375 ;130: 	qAccessInfo->inconsistentOffset =


                     376 

000001bc e5940028    377 	ldr	r0,[r4,40]

000001c0 eb000000*   378 	bl	DataSlice_getBoolOffset

000001c4 e5840028    379 	str	r0,[r4,40]

                     380 ;131: 			DataSlice_getBoolOffset(qAccessInfo->inconsistentOffset);


                     381 ;132: 	qAccessInfo->inaccurateOffset =


                     382 

000001c8 e594002c    383 	ldr	r0,[r4,44]

000001cc eb000000*   384 	bl	DataSlice_getBoolOffset

000001d0 e584002c    385 	str	r0,[r4,44]

                     386 ;133: 			DataSlice_getBoolOffset(qAccessInfo->inaccurateOffset);


                     387 ;134: 	qAccessInfo->processSubstitutedOffset =


                     388 

000001d4 e5940030    389 	ldr	r0,[r4,48]

000001d8 eb000000*   390 	bl	DataSlice_getBoolOffset

000001dc e5840030    391 	str	r0,[r4,48]

                     392 ;135: 			DataSlice_getBoolOffset(qAccessInfo->processSubstitutedOffset);


                     393 ;136: 	qAccessInfo->testOffset =


                     394 

000001e0 e5940034    395 	ldr	r0,[r4,52]

000001e4 eb000000*   396 	bl	DataSlice_getBoolOffset

000001e8 e5840034    397 	str	r0,[r4,52]

                     398 ;137: 			DataSlice_getBoolOffset(qAccessInfo->testOffset);


                     399 ;138: 	qAccessInfo->operatorBlockedOffset =


                     400 

000001ec e5940038    401 	ldr	r0,[r4,56]

000001f0 eb000000*   402 	bl	DataSlice_getBoolOffset

000001f4 e5840038    403 	str	r0,[r4,56]

                     404 ;139: 			DataSlice_getBoolOffset(qAccessInfo->operatorBlockedOffset);


                     405 ;140: 


                     406 ;141: 	entity->updateFromDataSlice = updateFromDataSlice;


                     407 

000001f8 e59f0018*   408 	ldr	r0,.L314

000001fc e585600c    409 	str	r6,[r5,12]

                     410 ;142: 	entity->calcReadLen = calcReadLen;


                     411 

                     412 ;143: 	entity->encodeRead = encodeRead;


                     413 

00000200 e8850081    414 	stmea	[r5],{r0,r7}

                     415 ;144: 


                     416 ;145: 	IEDTree_addToCmpList(entity);



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ais1.s
                     417 

00000204 e245005c    418 	sub	r0,r5,92

00000208 e8bd44f0    419 	ldmfd	[sp]!,{r4-r7,r10,lr}

0000020c ea000000*   420 	b	IEDTree_addToCmpList

                     421 	.endf	IEDQuality_init

                     422 	.align	4

                     423 ;extInfo	r4	local

                     424 

                     425 ;entity	r5	param

                     426 

                     427 	.section ".bss","awb"

                     428 .L305:

                     429 	.data

                     430 	.text

                     431 

                     432 ;146: }


                     433 	.align	4

                     434 .L312:

00000210 00000000*   435 	.data.w	updateFromDataSlice

                     436 	.type	.L312,$object

                     437 	.size	.L312,4

                     438 

                     439 .L313:

00000214 00000000*   440 	.data.w	calcReadLen

                     441 	.type	.L313,$object

                     442 	.size	.L313,4

                     443 

                     444 .L314:

00000218 00000000*   445 	.data.w	encodeRead

                     446 	.type	.L314,$object

                     447 	.size	.L314,4

                     448 

                     449 	.align	4

                     450 

                     451 	.data

                     452 	.ghsnote version,6

                     453 	.ghsnote tools,3

                     454 	.ghsnote options,0

                     455 	.text

                     456 	.align	4

