                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bbk1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=timetools.c -o gh_bbk1.o -list=timetools.lst C:\Users\<USER>\AppData\Local\Temp\gh_bbk1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_bbk1.s
Source File: timetools.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile timetools.c -o

                      11 ;		timetools.o

                      12 ;Source File:   timetools.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:00 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #pragma once


                      23 ;2: #include "timetools.h"


                      24 ;3: 


                      25 ;4: bool TimeTools_gmtime32(struct tm* tmDest, const __time32_t* sourceTime)


                      26 	.text

                      27 	.align	4

                      28 TimeTools_gmtime32::

00000000 e92d4000     29 	stmfd	[sp]!,{lr}

00000004 e1a02001     30 	mov	r2,r1

                      31 ;5: {


                      32 

                      33 ;6: 	__gmtime(*sourceTime,tmDest);


                      34 

00000008 e1a01000     35 	mov	r1,r0

0000000c e5920000     36 	ldr	r0,[r2]

00000010 eb000000*    37 	bl	__gmtime

                      38 ;7: 	return true;


                      39 

00000014 e3a00001     40 	mov	r0,1

00000018 e8bd8000     41 	ldmfd	[sp]!,{pc}

                      42 	.endf	TimeTools_gmtime32

                      43 	.align	4

                      44 

                      45 ;tmDest	r0	param

                      46 ;sourceTime	r2	param

                      47 

                      48 	.section ".bss","awb"

                      49 .L30:

                      50 	.data


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bbk1.s
                      51 	.text

                      52 

                      53 ;8: }


                      54 

                      55 ;9: 


                      56 ;10: bool TimeTools_localtime32(tm *tmDest, const __time32_t *sourceTime)


                      57 	.align	4

                      58 	.align	4

                      59 TimeTools_localtime32::

0000001c e92d4000     60 	stmfd	[sp]!,{lr}

00000020 e1a02001     61 	mov	r2,r1

                      62 ;11: {


                      63 

                      64 ;12: 	__localtime(*sourceTime,tmDest);


                      65 

00000024 e1a01000     66 	mov	r1,r0

00000028 e5920000     67 	ldr	r0,[r2]

0000002c eb000000*    68 	bl	__localtime

                      69 ;13: 	return true;


                      70 

00000030 e3a00001     71 	mov	r0,1

00000034 e8bd8000     72 	ldmfd	[sp]!,{pc}

                      73 	.endf	TimeTools_localtime32

                      74 	.align	4

                      75 

                      76 ;tmDest	r0	param

                      77 ;sourceTime	r2	param

                      78 

                      79 	.section ".bss","awb"

                      80 .L62:

                      81 	.data

                      82 	.text

                      83 

                      84 ;14: }


                      85 	.align	4

                      86 

                      87 	.data

                      88 	.ghsnote version,6

                      89 	.ghsnote tools,3

                      90 	.ghsnote options,0

                      91 	.text

                      92 	.align	4

