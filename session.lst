                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdk1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=session.c -o gh_bdk1.o -list=session.lst C:\Users\<USER>\AppData\Local\Temp\gh_bdk1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_bdk1.s
Source File: session.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile session.c -o

                      11 ;		session.o

                      12 ;Source File:   session.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:01 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include <debug.h>


                      23 ;2: #include "session.h"


                      24 ;3: 


                      25 ;4: #include <string.h>


                      26 ;5: 


                      27 ;6: #define SESSION_CONNECT_CODE	0x0d	//Соединение


                      28 ;7: #define SESSION_ACCEPT_CODE 0x0e	//Прием


                      29 ;8: #define SESSION_DATA_CODE 0x01	//Передача данных


                      30 ;9: #define SESSION_ABORT_CODE 0x19	//Прерывание активности


                      31 ;10: 


                      32 ;11: #define USER_DATA_PARAMETER 0xC1


                      33 ;12: #define PROTOCOL_OPTIONS_PARAMETER 0x13


                      34 ;13: #define VERSION_NUMBER_PARAMETER 0x16


                      35 ;14: 


                      36 ;15: static const unsigned char dataSpdu[] = { 0x01, 0x00, 0x01, 0x00 };


                      37 ;16: 


                      38 ;17: static int encodeConnectAcceptItem(unsigned char* buf, int offset)


                      39 

                      40 ;28: }


                      41 

                      42 ;29: 


                      43 ;30: 


                      44 ;31: static int encodeSessionRequirement(unsigned char* buf, int offset)


                      45 

                      46 ;38: }


                      47 

                      48 ;39: 


                      49 ;40: 


                      50 ;41: IsoSessionIndication parseSessionMessage(unsigned char* message, int inLen,



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdk1.s
                      51 	.text

                      52 	.align	4

                      53 parseSessionMessage::

00000000 e92d4030     54 	stmfd	[sp]!,{r4-r5,lr}

                      55 ;42:                                          unsigned char** pUserData, int* pUserDataSize)


                      56 ;43: {


                      57 

                      58 ;44:     unsigned char id;


                      59 ;45:     unsigned char headerLen;


                      60 ;46:     if(inLen <= 1)


                      61 

00000004 e3510001     62 	cmp	r1,1

00000008 da000017     63 	ble	.L50

                      64 ;47:     {


                      65 

                      66 ;48:         return SESSION_ERROR;


                      67 

                      68 ;49:     }


                      69 ;50:     id = message[0];


                      70 

0000000c e5d0c000     71 	ldrb	r12,[r0]

                      72 ;51:     headerLen = message[1];


                      73 

00000010 e5d05001     74 	ldrb	r5,[r0,1]

                      75 ;52: 


                      76 ;53:     switch (id) {


                      77 

00000014 e25c4001     78 	subs	r4,r12,1

00000018 0a000005     79 	beq	.L46

0000001c e354000c     80 	cmp	r4,12

00000020 1a000013     81 	bne	.L56

                      82 ;54:     case SESSION_CONNECT_CODE:


                      83 ;55:         debugSendText("Received Session connect message");


                      84 

00000024 e28f0000*    85 	adr	r0,.L179

00000028 eb000000*    86 	bl	debugSendText

                      87 ;56:         return SESSION_CONNECT;


                      88 

0000002c e3a00002     89 	mov	r0,2

00000030 ea000013     90 	b	.L37

                      91 .L46:

                      92 ;57:     case SESSION_DATA_CODE:


                      93 ;58:         if (inLen < 4)


                      94 

00000034 e3510004     95 	cmp	r1,4

00000038 ba00000b     96 	blt	.L50

                      97 ;59:         {


                      98 

                      99 ;60:             return SESSION_ERROR;


                     100 

                     101 ;61:         }


                     102 ;62:         if ((headerLen == 0) && (message[2] == 1) && (message[3] == 0))


                     103 

0000003c e3550000    104 	cmp	r5,0

00000040 05d0c002    105 	ldreqb	r12,[r0,2]

00000044 035c0001    106 	cmpeq	r12,1

00000048 05d0c003    107 	ldreqb	r12,[r0,3]

0000004c 035c0000    108 	cmpeq	r12,0

00000050 1a000005    109 	bne	.L50

                     110 ;63:         {


                     111 


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdk1.s
                     112 ;64:             *pUserData = message + 4;


                     113 

00000054 e2800004    114 	add	r0,r0,4

00000058 e5820000    115 	str	r0,[r2]

                     116 ;65:             *pUserDataSize = inLen -4;


                     117 

0000005c e2410004    118 	sub	r0,r1,4

00000060 e5830000    119 	str	r0,[r3]

                     120 ;66:             return SESSION_DATA;


                     121 

00000064 e3a00004    122 	mov	r0,4

00000068 ea000005    123 	b	.L37

                     124 .L50:

                     125 ;67:         }


                     126 ;68:         return SESSION_ERROR;        


                     127 

0000006c e3a00001    128 	mov	r0,1

00000070 ea000003    129 	b	.L37

                     130 .L56:

                     131 ;69:     default:


                     132 ;70:         debugSendUshort("Unknown session message ID:", id);


                     133 

00000074 e28f0000*   134 	adr	r0,.L180

00000078 e1a0100c    135 	mov	r1,r12

0000007c eb000000*   136 	bl	debugSendUshort

                     137 ;71:         break;


                     138 ;72:     }


                     139 ;73:     return SESSION_ERROR;


                     140 

00000080 e3a00001    141 	mov	r0,1

                     142 .L37:

00000084 e8bd8030    143 	ldmfd	[sp]!,{r4-r5,pc}

                     144 	.endf	parseSessionMessage

                     145 	.align	4

                     146 ;id	r12	local

                     147 ;headerLen	r5	local

                     148 ;.L153	.L158	static

                     149 ;.L154	.L157	static

                     150 

                     151 ;message	r0	param

                     152 ;inLen	r1	param

                     153 ;pUserData	r2	param

                     154 ;pUserDataSize	r3	param

                     155 

                     156 	.section ".bss","awb"

                     157 .L152:

                     158 	.data

                     159 	.text

                     160 

                     161 ;74: }


                     162 

                     163 ;75: 


                     164 ;76: int createAcceptSPDU( unsigned char* buf, unsigned char* userData, int userDataLen)


                     165 	.align	4

                     166 	.align	4

                     167 createAcceptSPDU::

00000088 e92d4030    168 	stmfd	[sp]!,{r4-r5,lr}

                     169 ;77: {


                     170 

                     171 ;78:     int offset = 0;


                     172 


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdk1.s
                     173 ;79: 


                     174 ;80:     buf[offset++] = SESSION_ACCEPT_CODE;


                     175 

0000008c e3a0c005    176 	mov	r12,5

00000090 e1a04000    177 	mov	r4,r0

00000094 e5c4c002    178 	strb	r12,[r4,2]

                     179 ;20:     buf[offset++] = 6;


                     180 

00000098 e3a0c006    181 	mov	r12,6

0000009c e5c4c003    182 	strb	r12,[r4,3]

                     183 ;21:     buf[offset++] = PROTOCOL_OPTIONS_PARAMETER;


                     184 

000000a0 e3a0c013    185 	mov	r12,19

000000a4 e5c4c004    186 	strb	r12,[r4,4]

                     187 ;22:     buf[offset++] = 1;


                     188 

000000a8 e3a0c001    189 	mov	r12,1

000000ac e5c4c005    190 	strb	r12,[r4,5]

                     191 ;23:     buf[offset++] = 0; //options


                     192 

000000b0 e5c4c008    193 	strb	r12,[r4,8]

                     194 ;26:     buf[offset++] = 2; /* Version = 2 */


                     195 

000000b4 e3a0c002    196 	mov	r12,2

000000b8 e5c4c009    197 	strb	r12,[r4,9]

                     198 ;27:     return offset;


                     199 

                     200 ;84: 


                     201 ;85:     offset = encodeSessionRequirement(buf, offset);


                     202 

                     203 ;32: {


                     204 

                     205 ;33:     buf[offset++] = 0x14;


                     206 

000000bc e5c4c00b    207 	strb	r12,[r4,11]

                     208 ;35:     buf[offset++] = 0;//(uint8_t) (self->sessionRequirement / 0x100);


                     209 

000000c0 e5c4c00d    210 	strb	r12,[r4,13]

                     211 ;37:     return offset;


                     212 

                     213 ;86:     //offset = encodeCalledSessionSelector(self, buf, offset);


                     214 ;87: 


                     215 ;88:     buf[offset++] = USER_DATA_PARAMETER;


                     216 

000000c4 e3a03016    217 	mov	r3,22

000000c8 e5c43007    218 	strb	r3,[r4,7]

                     219 ;25:     buf[offset++] = 1;


                     220 

000000cc e3a03014    221 	mov	r3,20

000000d0 e5c4300a    222 	strb	r3,[r4,10]

                     223 ;34:     buf[offset++] = 2;


                     224 

000000d4 e3a030c1    225 	mov	r3,193

000000d8 e5c4300e    226 	strb	r3,[r4,14]

                     227 ;89:     buf[offset++] = userDataLen;


                     228 

000000dc e5c4200f    229 	strb	r2,[r4,15]

                     230 ;90:     memcpy(buf + offset, userData, userDataLen);


                     231 

000000e0 e1a05002    232 	mov	r5,r2

000000e4 e3a0000e    233 	mov	r0,14


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdk1.s
000000e8 e5c40000    234 	strb	r0,[r4]

                     235 ;81:     offset++;//Пропускаем длину


                     236 

                     237 ;82: 


                     238 ;83:     offset = encodeConnectAcceptItem(buf, offset);    


                     239 

                     240 ;18: {


                     241 

                     242 ;19:     buf[offset++] = 5;


                     243 

000000ec e3a00000    244 	mov	r0,0

000000f0 e5c40006    245 	strb	r0,[r4,6]

                     246 ;24:     buf[offset++] = VERSION_NUMBER_PARAMETER; /* Version Number */


                     247 

000000f4 e5c4000c    248 	strb	r0,[r4,12]

                     249 ;36:     buf[offset++] = 2;//(uint8_t) (self->sessionRequirement & 0x00ff);


                     250 

000000f8 e2840010    251 	add	r0,r4,16

000000fc eb000000*   252 	bl	memcpy

                     253 ;91:     offset += userDataLen;


                     254 

                     255 ;92: 


                     256 ;93:     buf[1] = offset - 2;//Длина


                     257 

00000100 e285000e    258 	add	r0,r5,14

00000104 e5c40001    259 	strb	r0,[r4,1]

                     260 ;94: 


                     261 ;95:     return offset;


                     262 

00000108 e2850010    263 	add	r0,r5,16

0000010c e8bd8030    264 	ldmfd	[sp]!,{r4-r5,pc}

                     265 	.endf	createAcceptSPDU

                     266 	.align	4

                     267 ;offset	r0	local

                     268 ;offset	r3	local

                     269 ;offset	r3	local

                     270 

                     271 ;buf	r4	param

                     272 ;userData	none	param

                     273 ;userDataLen	r5	param

                     274 

                     275 	.section ".bss","awb"

                     276 .L206:

                     277 	.data

                     278 	.text

                     279 

                     280 ;96: }


                     281 

                     282 ;97: 


                     283 ;98: int isoSession_createDataSpdu(unsigned char* buf, int bufSize,


                     284 	.align	4

                     285 	.align	4

                     286 	.align	4

                     287 isoSession_createDataSpdu::

00000110 e92d4070    288 	stmfd	[sp]!,{r4-r6,lr}

00000114 e1a05000    289 	mov	r5,r0

00000118 e1a04003    290 	mov	r4,r3

                     291 ;99:                                unsigned char* userData, int userDataLen)


                     292 ;100: {


                     293 

                     294 ;101: 	if (SESION_DATA_PACKET_HEADER_SIZE + userDataLen > bufSize)



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdk1.s
                     295 

0000011c e2840004    296 	add	r0,r4,4

00000120 e1500001    297 	cmp	r0,r1

                     298 ;102: 	{


                     299 

                     300 ;103: 		ERROR_REPORT("Session buffer overflow");


                     301 ;104: 		return 0;


                     302 

00000124 c3a00000    303 	movgt	r0,0

00000128 ca000009    304 	bgt	.L213

0000012c e59f1064*   305 	ldr	r1,.L261

00000130 e1a00005    306 	mov	r0,r5

00000134 e1a06002    307 	mov	r6,r2

                     308 ;105: 	}


                     309 ;106: 


                     310 ;107:     memcpy( buf, dataSpdu, SESION_DATA_PACKET_HEADER_SIZE );


                     311 

00000138 e3a02004    312 	mov	r2,4

0000013c eb000000*   313 	bl	memcpy

                     314 ;108:     buf += SESION_DATA_PACKET_HEADER_SIZE;


                     315 

00000140 e2850004    316 	add	r0,r5,4

                     317 ;109:     memcpy( buf, userData, userDataLen );


                     318 

00000144 e1a02004    319 	mov	r2,r4

00000148 e1a01006    320 	mov	r1,r6

0000014c eb000000*   321 	bl	memcpy

                     322 ;110:     return SESION_DATA_PACKET_HEADER_SIZE + userDataLen;


                     323 

00000150 e2840004    324 	add	r0,r4,4

                     325 .L213:

00000154 e8bd8070    326 	ldmfd	[sp]!,{r4-r6,pc}

                     327 	.endf	isoSession_createDataSpdu

                     328 	.align	4

                     329 

                     330 ;buf	r5	param

                     331 ;bufSize	r1	param

                     332 ;userData	r6	param

                     333 ;userDataLen	r4	param

                     334 

                     335 	.section ".bss","awb"

                     336 .L246:

                     337 	.section ".rodata","a"

                     338 .L247:

00000000 01         339 dataSpdu:	.data.b	1

00000001 00         340 	.space	1

00000002 01         341 	.data.b	1

00000003 00         342 	.space	1

                     343 	.type	dataSpdu,$object

                     344 	.size	dataSpdu,4

                     345 	.data

                     346 	.text

                     347 

                     348 ;111: }


                     349 	.align	4

                     350 .L179:

                     351 ;	"Received Session connect message\000"

00000158 65636552    352 	.data.b	82,101,99,101

0000015c 64657669    353 	.data.b	105,118,101,100

00000160 73655320    354 	.data.b	32,83,101,115

00000164 6e6f6973    355 	.data.b	115,105,111,110


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdk1.s
00000168 6e6f6320    356 	.data.b	32,99,111,110

0000016c 7463656e    357 	.data.b	110,101,99,116

00000170 73656d20    358 	.data.b	32,109,101,115

00000174 65676173    359 	.data.b	115,97,103,101

00000178 00         360 	.data.b	0

00000179 000000     361 	.align 4

                     362 

                     363 	.type	.L179,$object

                     364 	.size	.L179,4

                     365 

                     366 .L180:

                     367 ;	"Unknown session message ID:\000"

0000017c 6e6b6e55    368 	.data.b	85,110,107,110

00000180 206e776f    369 	.data.b	111,119,110,32

00000184 73736573    370 	.data.b	115,101,115,115

00000188 206e6f69    371 	.data.b	105,111,110,32

0000018c 7373656d    372 	.data.b	109,101,115,115

00000190 20656761    373 	.data.b	97,103,101,32

00000194 003a4449    374 	.data.b	73,68,58,0

                     375 	.align 4

                     376 

                     377 	.type	.L180,$object

                     378 	.size	.L180,4

                     379 

                     380 .L261:

00000198 00000000*   381 	.data.w	.L247

                     382 	.type	.L261,$object

                     383 	.size	.L261,4

                     384 

                     385 	.align	4

                     386 ;dataSpdu	.L247	static

                     387 

                     388 	.data

                     389 .L276:

                     390 	.globl	g_DataSpdu

00000000 01         391 g_DataSpdu:	.data.b	1

00000001 00         392 	.space	1

00000002 01         393 	.data.b	1

00000003 00         394 	.space	1

                     395 	.type	g_DataSpdu,$object

                     396 	.size	g_DataSpdu,4

                     397 	.ghsnote version,6

                     398 	.ghsnote tools,3

                     399 	.ghsnote options,0

                     400 	.text

                     401 	.align	4

                     402 	.data

                     403 	.align	4

                     404 	.section ".rodata","a"

                     405 	.align	4

                     406 	.text

