                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cbs1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=presentation.c -o gh_cbs1.o -list=presentation.lst C:\Users\<USER>\AppData\Local\Temp\gh_cbs1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_cbs1.s
Source File: presentation.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile presentation.c

                      11 ;		-o presentation.o

                      12 ;Source File:   presentation.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:02 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "presentation.h"


                      23 ;2: #include "AsnEncoding.h"  


                      24 ;3: 


                      25 ;4: #include <stddef.h>


                      26 ;5: #include <string.h>


                      27 ;6: 


                      28 ;7: //параметры CONNECTION PRESENTATION


                      29 ;8: #define	MODE_SELECTOR_PARAMETER 0xa0


                      30 ;9: #define MODE_PARAMETER 0x80


                      31 ;10: #define NORMAL_MODE_PARAMETERS 0xa2


                      32 ;11: #define CALLING_PRESENTATION_SELECTOR 0x81


                      33 ;12: #define CALLED_PRESENTATION_SELECTOR 0x82


                      34 ;13: #define PRESENTATION_CONTEXT_DIFINITION_LIST 0xa4


                      35 ;14: #define PRESENTATION_USER_DATA 0x61


                      36 ;15: #define RESPONDING_PRESENTATION_SELECTOR 0x83


                      37 ;16: 


                      38 ;17: //параметры ACCEPT PRESENTATION


                      39 ;18: #define CONTEXT_DIFINITION_RESULT_LIST 0xa5


                      40 ;19: #define PRESENTATION_CONTEXT_DIFINITION_LIST_RESULT 0x80


                      41 ;20: #define	TRANFER_SYNTAX_NAME_PARAMETER 0x81


                      42 ;21: #define PRESENTATION_USER_DATA_NEXT	 0xa0


                      43 ;22: 


                      44 ;23: #define	PRESENTATION_DATA_PACKET_HEADER_SIZE	9


                      45 ;24: 


                      46 ;25: unsigned char berId[] = { 0x51, 0x01 };


                      47 ;26: unsigned char calledPresentationSelector[] = { 0x00, 0x00, 0x00, 0x01 };


                      48 ;27: 


                      49 ;28: static int encodeAcceptBer( unsigned char* buf, int bufPos )


                      50 	.text


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cbs1.s
                      51 	.align	4

                      52 encodeAcceptBer:

00000000 e92d4010     53 	stmfd	[sp]!,{r4,lr}

00000004 e1a04000     54 	mov	r4,r0

                      55 ;29: {


                      56 

                      57 ;30: 


                      58 ;31:     bufPos = BerEncoder_encodeTL( ASN_SEQUENCE, 7, buf, bufPos );


                      59 

00000008 e1a02004     60 	mov	r2,r4

0000000c e1a03001     61 	mov	r3,r1

00000010 e3a01007     62 	mov	r1,7

00000014 e3a00030     63 	mov	r0,48

00000018 eb000000*    64 	bl	BerEncoder_encodeTL

                      65 ;32:     bufPos = BerEncoder_encodeTL( PRESENTATION_CONTEXT_DIFINITION_LIST_RESULT,


                      66 

0000001c e1a02004     67 	mov	r2,r4

00000020 e3a01001     68 	mov	r1,1

00000024 e1a03000     69 	mov	r3,r0

00000028 e3a00080     70 	mov	r0,128

0000002c eb000000*    71 	bl	BerEncoder_encodeTL

                      72 ;33:                                   1, buf, bufPos );


                      73 ;34:     buf[bufPos++] = 0;


                      74 

00000030 e1a02004     75 	mov	r2,r4

00000034 e3a01000     76 	mov	r1,0

00000038 e7c41000     77 	strb	r1,[r4,r0]

                      78 ;35:     bufPos = BerEncoder_encodeTL( TRANFER_SYNTAX_NAME_PARAMETER, 2, buf,


                      79 

0000003c e3a01002     80 	mov	r1,2

00000040 e2803001     81 	add	r3,r0,1

00000044 e3a00081     82 	mov	r0,129

00000048 eb000000*    83 	bl	BerEncoder_encodeTL

                      84 ;36:                                   bufPos );


                      85 ;37:     buf[bufPos++] = berId[0];		//0x51;


                      86 

0000004c e59f2370*    87 	ldr	r2,.L37

00000050 e5d23000     88 	ldrb	r3,[r2]

00000054 e2801001     89 	add	r1,r0,1

00000058 e7c43000     90 	strb	r3,[r4,r0]

                      91 ;38:     buf[bufPos++] = berId[1];		//0x01;


                      92 

0000005c e5d20001     93 	ldrb	r0,[r2,1]

00000060 e7c40001     94 	strb	r0,[r4,r1]

                      95 ;39: 


                      96 ;40:     return bufPos;


                      97 

00000064 e2810001     98 	add	r0,r1,1

00000068 e8bd4010     99 	ldmfd	[sp]!,{r4,lr}

0000006c e12fff1e*   100 	ret	

                     101 	.endf	encodeAcceptBer

                     102 	.align	4

                     103 

                     104 ;buf	r4	param

                     105 ;bufPos	r1	param

                     106 

                     107 	.data

                     108 .L30:

                     109 	.text

                     110 

                     111 ;41: }



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cbs1.s
                     112 

                     113 ;42: 


                     114 ;43: 


                     115 ;44: //Пишет "шапку" UserData(для ACSE) или просто определяет её размер


                     116 ;45: //encode:


                     117 ;46: //  1 - реально писать в буфер. //Возвращает новую позицию в буфере


                     118 ;47: //  0 - или только определить размер. Возвращает размер


                     119 ;48: static int encodeUserData( unsigned char* buffer, int bufPos, int userDataLength,


                     120 	.align	4

                     121 	.align	4

                     122 encodeUserData:

00000070 e92d4cf0    123 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

00000074 e1a06002    124 	mov	r6,r2

                     125 ;49:                     unsigned char encode, unsigned char contextId )


                     126 ;50: {


                     127 

00000078 e2864004    128 	add	r4,r6,4

                     129 ;51:     int fullyEncodedDataLength;


                     130 ;52:     int encodedDataSetLength = 3; /* presentation-selector */


                     131 

                     132 ;53: 


                     133 ;54:     // presentation-data


                     134 ;55:     encodedDataSetLength += userDataLength + 1;


                     135 

                     136 ;56:     encodedDataSetLength += BerEncoder_determineLengthSize(userDataLength);


                     137 

0000007c e1a0b001    138 	mov	fp,r1

00000080 e1a07003    139 	mov	r7,r3

00000084 e5dda01c    140 	ldrb	r10,[sp,28]

00000088 e1a05000    141 	mov	r5,r0

0000008c e1a00006    142 	mov	r0,r6

00000090 eb000000*   143 	bl	BerEncoder_determineLengthSize

00000094 e0844000    144 	add	r4,r4,r0

                     145 ;57: 


                     146 ;58:     fullyEncodedDataLength = encodedDataSetLength;


                     147 

                     148 ;59: 


                     149 ;60:     fullyEncodedDataLength += BerEncoder_determineLengthSize(encodedDataSetLength) + 1;


                     150 

00000098 e1a00004    151 	mov	r0,r4

0000009c eb000000*   152 	bl	BerEncoder_determineLengthSize

000000a0 e0800004    153 	add	r0,r0,r4

000000a4 e2800001    154 	add	r0,r0,1

                     155 ;61: 


                     156 ;62:     if (encode) {


                     157 

000000a8 e3570000    158 	cmp	r7,0

000000ac 0a000015    159 	beq	.L40

                     160 ;63:         /* fully-encoded-data */


                     161 ;64:         bufPos = BerEncoder_encodeTL(PRESENTATION_USER_DATA,


                     162 

000000b0 e1a0300b    163 	mov	r3,fp

000000b4 e1a02005    164 	mov	r2,r5

000000b8 e1a01000    165 	mov	r1,r0

000000bc e3a00061    166 	mov	r0,97

000000c0 eb000000*   167 	bl	BerEncoder_encodeTL

                     168 ;65:                                      fullyEncodedDataLength, buffer, bufPos);


                     169 ;66:         bufPos = BerEncoder_encodeTL(ASN_SEQUENCE, encodedDataSetLength, buffer,


                     170 

000000c4 e1a02005    171 	mov	r2,r5

000000c8 e1a01004    172 	mov	r1,r4


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cbs1.s
000000cc e1a03000    173 	mov	r3,r0

000000d0 e3a00030    174 	mov	r0,48

000000d4 eb000000*   175 	bl	BerEncoder_encodeTL

                     176 ;67:                                      bufPos);


                     177 ;68: 


                     178 ;69:         /* presentation-selector acse */


                     179 ;70:         bufPos = BerEncoder_encodeTL(ASN_INTEGER, 1, buffer, bufPos);


                     180 

000000d8 e1a02005    181 	mov	r2,r5

000000dc e3a01001    182 	mov	r1,1

000000e0 e1a03000    183 	mov	r3,r0

000000e4 e3a00002    184 	mov	r0,2

000000e8 eb000000*   185 	bl	BerEncoder_encodeTL

                     186 ;71:         buffer[bufPos++] = contextId;


                     187 

000000ec e1a02005    188 	mov	r2,r5

000000f0 e1a01006    189 	mov	r1,r6

000000f4 e2803001    190 	add	r3,r0,1

000000f8 e7c5a000    191 	strb	r10,[r5,r0]

                     192 ;72: 


                     193 ;73:         /* presentation-data (= acse payload) */


                     194 ;74:         bufPos = BerEncoder_encodeTL(PRESENTATION_USER_DATA_NEXT,


                     195 

000000fc e3a000a0    196 	mov	r0,160

00000100 eb000000*   197 	bl	BerEncoder_encodeTL

                     198 ;75:                                      userDataLength, buffer, bufPos);


                     199 ;76: 


                     200 ;77:         return bufPos;


                     201 

00000104 ea000002    202 	b	.L38

                     203 .L40:

                     204 ;78:     }


                     205 ;79:     else {


                     206 

                     207 ;80:         int encodedUserDataLength = fullyEncodedDataLength + 1;


                     208 

00000108 e2804001    209 	add	r4,r0,1

                     210 ;81:         encodedUserDataLength += BerEncoder_determineLengthSize(fullyEncodedDataLength);


                     211 

0000010c eb000000*   212 	bl	BerEncoder_determineLengthSize

00000110 e0840000    213 	add	r0,r4,r0

                     214 ;82: 


                     215 ;83:         return encodedUserDataLength;


                     216 

                     217 .L38:

00000114 e8bd4cf0    218 	ldmfd	[sp]!,{r4-r7,r10-fp,lr}

00000118 e12fff1e*   219 	ret	

                     220 	.endf	encodeUserData

                     221 	.align	4

                     222 ;fullyEncodedDataLength	r0	local

                     223 ;encodedDataSetLength	r4	local

                     224 ;encodedUserDataLength	r4	local

                     225 

                     226 ;buffer	r5	param

                     227 ;bufPos	fp	param

                     228 ;userDataLength	r6	param

                     229 ;encode	r7	param

                     230 ;contextId	r10	param

                     231 

                     232 	.section ".bss","awb"

                     233 .L75:


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cbs1.s
                     234 	.data

                     235 	.text

                     236 

                     237 ;84:     }


                     238 ;85: }


                     239 

                     240 ;86: 


                     241 ;87: void initPresentation(IsoPresentation* presentation)


                     242 	.align	4

                     243 	.align	4

                     244 initPresentation::

                     245 ;88: {


                     246 

                     247 ;89:     //TODO acseContextId


                     248 ;90:     presentation->presentationContextId = 3;


                     249 

0000011c e3a01003    250 	mov	r1,3

00000120 e5c01008    251 	strb	r1,[r0,8]

                     252 ;91:     presentation->acseContextId = 1;


                     253 

00000124 e3a01001    254 	mov	r1,1

00000128 e5c0100a    255 	strb	r1,[r0,10]

0000012c e12fff1e*   256 	ret	

                     257 	.endf	initPresentation

                     258 	.align	4

                     259 

                     260 ;presentation	r0	param

                     261 

                     262 	.section ".bss","awb"

                     263 .L110:

                     264 	.data

                     265 	.text

                     266 

                     267 ;92: }


                     268 

                     269 ;93: 


                     270 ;94: int isoPresentation_createCpaMessage(IsoPresentation* presentation,


                     271 	.align	4

                     272 	.align	4

                     273 isoPresentation_createCpaMessage::

00000130 e92d4cf0    274 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                     275 ;95:                                  unsigned char* buf, unsigned char* userData, int userDataLen)


                     276 ;96: {


                     277 

                     278 ;97:     int contentLength = 0;


                     279 

                     280 ;98:     int normalModeLength = 0;


                     281 

                     282 ;99:     int bufPos = 0;


                     283 

                     284 ;100: 


                     285 ;101:     // mode-selector


                     286 ;102:     contentLength += 5;


                     287 

                     288 ;103:     normalModeLength += 6; // responding-presentation-selector


                     289 

                     290 ;104:     normalModeLength += 20; // context-definition-result-list


                     291 

                     292 ;105:     normalModeLength += encodeUserData(NULL, 0, userDataLen, 0/*encode*/,


                     293 

00000134 e1a0b002    294 	mov	fp,r2


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cbs1.s
00000138 e1a05003    295 	mov	r5,r3

0000013c e1a02005    296 	mov	r2,r5

00000140 e1a04001    297 	mov	r4,r1

00000144 e3a03000    298 	mov	r3,0

00000148 e1a07000    299 	mov	r7,r0

0000014c e5d7000a    300 	ldrb	r0,[r7,10]

00000150 e1a01003    301 	mov	r1,r3

00000154 e52d0004    302 	str	r0,[sp,-4]!

00000158 e1a00003    303 	mov	r0,r3

0000015c ebffffc3*   304 	bl	encodeUserData

00000160 e280601a    305 	add	r6,r0,26

                     306 ;106:                                        presentation->acseContextId);


                     307 ;107: 


                     308 ;108:     contentLength += normalModeLength;


                     309 

00000164 e280a01f    310 	add	r10,r0,31

                     311 ;109: 


                     312 ;110:     contentLength += BerEncoder_determineLengthSize(normalModeLength) + 1;


                     313 

00000168 e1a00006    314 	mov	r0,r6

0000016c eb000000*   315 	bl	BerEncoder_determineLengthSize

00000170 e1a02004    316 	mov	r2,r4

00000174 e3a03000    317 	mov	r3,0

00000178 e080000a    318 	add	r0,r0,r10

0000017c e2801001    319 	add	r1,r0,1

                     320 ;111: 


                     321 ;112:     bufPos = BerEncoder_encodeTL(ASN_SET, contentLength, buf, bufPos);


                     322 

00000180 e3a00031    323 	mov	r0,49

00000184 eb000000*   324 	bl	BerEncoder_encodeTL

                     325 ;113: 


                     326 ;114:     /* mode-selector */


                     327 ;115:     bufPos = BerEncoder_encodeTL(MODE_SELECTOR_PARAMETER, 3, buf, bufPos);


                     328 

00000188 e1a02004    329 	mov	r2,r4

0000018c e3a01003    330 	mov	r1,3

00000190 e1a03000    331 	mov	r3,r0

00000194 e3a000a0    332 	mov	r0,160

00000198 eb000000*   333 	bl	BerEncoder_encodeTL

                     334 ;116:     bufPos = BerEncoder_encodeTL(MODE_PARAMETER, 1, buf, bufPos);


                     335 

0000019c e1a02004    336 	mov	r2,r4

000001a0 e3a01001    337 	mov	r1,1

000001a4 e1a03000    338 	mov	r3,r0

000001a8 e3a00080    339 	mov	r0,128

000001ac eb000000*   340 	bl	BerEncoder_encodeTL

                     341 ;117:     buf[bufPos++] = 1; /* 1 = normal-mode */


                     342 

000001b0 e1a02004    343 	mov	r2,r4

000001b4 e1a01006    344 	mov	r1,r6

000001b8 e2803001    345 	add	r3,r0,1

000001bc e3a0a001    346 	mov	r10,1

000001c0 e7c4a000    347 	strb	r10,[r4,r0]

                     348 ;118: 


                     349 ;119:     /* normal-mode-parameters */


                     350 ;120:     bufPos = BerEncoder_encodeTL(NORMAL_MODE_PARAMETERS, normalModeLength, buf, bufPos);


                     351 

000001c4 e3a000a2    352 	mov	r0,162

000001c8 eb000000*   353 	bl	BerEncoder_encodeTL

                     354 ;121: 


                     355 ;122:     /* responding-presentation-selector */



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cbs1.s
                     356 ;123:     bufPos = BerEncoder_encodeTL(RESPONDING_PRESENTATION_SELECTOR, 4, buf, bufPos);


                     357 

000001cc e1a02004    358 	mov	r2,r4

000001d0 e3a01004    359 	mov	r1,4

000001d4 e1a03000    360 	mov	r3,r0

000001d8 e3a00083    361 	mov	r0,131

000001dc eb000000*   362 	bl	BerEncoder_encodeTL

000001e0 e59f11e0*   363 	ldr	r1,.L152

000001e4 e1a06000    364 	mov	r6,r0

                     365 ;124:     memcpy(buf + bufPos, calledPresentationSelector, 4);


                     366 

000001e8 e0860004    367 	add	r0,r6,r4

000001ec e3a02004    368 	mov	r2,4

000001f0 eb000000*   369 	bl	memcpy

                     370 ;125:     bufPos += 4;


                     371 

000001f4 e2863004    372 	add	r3,r6,4

                     373 ;126: 


                     374 ;127:     /* context-definition-result-list */


                     375 ;128:     bufPos = BerEncoder_encodeTL(CONTEXT_DIFINITION_RESULT_LIST, 18, buf, bufPos);


                     376 

000001f8 e1a02004    377 	mov	r2,r4

000001fc e3a01012    378 	mov	r1,18

00000200 e3a000a5    379 	mov	r0,165

00000204 eb000000*   380 	bl	BerEncoder_encodeTL

                     381 ;129:     bufPos = encodeAcceptBer(buf, bufPos); /* accept for acse */


                     382 

00000208 e1a01000    383 	mov	r1,r0

0000020c e1a00004    384 	mov	r0,r4

00000210 ebffff7a*   385 	bl	encodeAcceptBer

                     386 ;130:     bufPos = encodeAcceptBer(buf, bufPos); /* accept for mms */


                     387 

00000214 e1a01000    388 	mov	r1,r0

00000218 e1a00004    389 	mov	r0,r4

0000021c ebffff77*   390 	bl	encodeAcceptBer

                     391 ;131: 


                     392 ;132:     /* encode user data */


                     393 ;133:     //Пишем "шапку"


                     394 ;134:     bufPos = encodeUserData(buf, bufPos, userDataLen, 1, presentation->acseContextId);


                     395 

00000220 e5d7100a    396 	ldrb	r1,[r7,10]

00000224 e1a02005    397 	mov	r2,r5

00000228 e58d1000    398 	str	r1,[sp]

0000022c e1a01000    399 	mov	r1,r0

00000230 e1a00004    400 	mov	r0,r4

00000234 e1a0300a    401 	mov	r3,r10

00000238 ebffff8c*   402 	bl	encodeUserData

0000023c e1a02005    403 	mov	r2,r5

00000240 e1a0100b    404 	mov	r1,fp

00000244 e1a06000    405 	mov	r6,r0

                     406 ;135:     //Пишем сами данные


                     407 ;136:     memcpy( buf + bufPos, userData, userDataLen );


                     408 

00000248 e0860004    409 	add	r0,r6,r4

0000024c eb000000*   410 	bl	memcpy

                     411 ;137:     return bufPos + userDataLen;


                     412 

00000250 e0860005    413 	add	r0,r6,r5

00000254 e28dd004    414 	add	sp,sp,4

00000258 e8bd8cf0    415 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                     416 	.endf	isoPresentation_createCpaMessage


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cbs1.s
                     417 	.align	4

                     418 ;contentLength	r10	local

                     419 ;normalModeLength	r6	local

                     420 ;bufPos	r6	local

                     421 

                     422 ;presentation	r7	param

                     423 ;buf	r4	param

                     424 ;userData	fp	param

                     425 ;userDataLen	r5	param

                     426 

                     427 	.section ".bss","awb"

                     428 .L145:

                     429 	.data

                     430 	.text

                     431 

                     432 ;138: }


                     433 

                     434 ;139: 


                     435 ;140: 


                     436 ;141: int isoPresentation_parseUserData(IsoPresentation* presentation,


                     437 	.align	4

                     438 	.align	4

                     439 isoPresentation_parseUserData::

0000025c e92d40f0    440 	stmfd	[sp]!,{r4-r7,lr}

                     441 ;142:                                   unsigned char* inBuf, int inLen,


                     442 ;143:                                   unsigned char** pOutUserData)


                     443 ;144: {        


                     444 

                     445 ;145:     int len;//dummy?


                     446 ;146:     int userDataLength;


                     447 ;147:     int bufPos = 0;


                     448 

                     449 ;148: 


                     450 ;149:     if (inLen < PRESENTATION_DATA_PACKET_HEADER_SIZE)


                     451 

00000260 e24dd008    452 	sub	sp,sp,8

00000264 e1a06000    453 	mov	r6,r0

00000268 e1a04001    454 	mov	r4,r1

0000026c e1a05002    455 	mov	r5,r2

00000270 e3550009    456 	cmp	r5,9

00000274 e3a02000    457 	mov	r2,0

                     458 ;150:     {


                     459 

                     460 ;151:         return -1;


                     461 

                     462 ;152:     }


                     463 ;153: 


                     464 ;154:     if (inBuf[bufPos++] != PRESENTATION_USER_DATA)


                     465 

00000278 a7d40002    466 	ldrgeb	r0,[r4,r2]

0000027c e1a07003    467 	mov	r7,r3

00000280 a3500061    468 	cmpge	r0,97

00000284 1a000019    469 	bne	.L171

                     470 ;155:     {


                     471 

                     472 ;156:         return -1;


                     473 

                     474 ;157:     }


                     475 ;158:     bufPos = BerDecoder_decodeLength(inBuf, &len, bufPos, inLen);


                     476 

00000288 e1a03005    477 	mov	r3,r5


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cbs1.s
0000028c e1a0100d    478 	mov	r1,sp

00000290 e1a00004    479 	mov	r0,r4

00000294 e3a02001    480 	mov	r2,1

00000298 eb000000*   481 	bl	BerDecoder_decodeLength

                     482 ;159: 


                     483 ;160:     if (inBuf[bufPos++] != ASN_SEQUENCE)


                     484 

0000029c e2802001    485 	add	r2,r0,1

000002a0 e7d40000    486 	ldrb	r0,[r4,r0]

000002a4 e3500030    487 	cmp	r0,48

000002a8 1a000010    488 	bne	.L171

                     489 ;161:     {


                     490 

                     491 ;162:         return -1;


                     492 

                     493 ;163:     }


                     494 ;164: 


                     495 ;165:     bufPos = BerDecoder_decodeLength(inBuf, &len, bufPos, inLen);


                     496 

000002ac e1a03005    497 	mov	r3,r5

000002b0 e1a0100d    498 	mov	r1,sp

000002b4 e1a00004    499 	mov	r0,r4

000002b8 eb000000*   500 	bl	BerDecoder_decodeLength

                     501 ;166: 


                     502 ;167:     if (inBuf[bufPos++] != ASN_INTEGER)


                     503 

000002bc e2802001    504 	add	r2,r0,1

000002c0 e7d40000    505 	ldrb	r0,[r4,r0]

000002c4 e3500002    506 	cmp	r0,2

                     507 ;168:     {


                     508 

                     509 ;169:         return -1;


                     510 

                     511 ;170:     }


                     512 ;171: 


                     513 ;172:     if (inBuf[bufPos++] != 0x01)


                     514 

000002c8 07d40002    515 	ldreqb	r0,[r4,r2]

000002cc 02822001    516 	addeq	r2,r2,1

000002d0 03500001    517 	cmpeq	r0,1

000002d4 1a000005    518 	bne	.L171

                     519 ;173:     {


                     520 

                     521 ;174:         return -1;


                     522 

                     523 ;175:     }


                     524 ;176: 


                     525 ;177:     presentation->nextContextId = inBuf[bufPos++];


                     526 

000002d8 e7d40002    527 	ldrb	r0,[r4,r2]

000002dc e2822001    528 	add	r2,r2,1

000002e0 e5c60009    529 	strb	r0,[r6,9]

                     530 ;178: 


                     531 ;179:     if (inBuf[bufPos++] != PRESENTATION_USER_DATA_NEXT)


                     532 

000002e4 e7d40002    533 	ldrb	r0,[r4,r2]

000002e8 e2822001    534 	add	r2,r2,1

000002ec e35000a0    535 	cmp	r0,160

                     536 .L171:

                     537 ;180:     {


                     538 


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cbs1.s
                     539 ;181:         return -1;


                     540 

000002f0 13e00000    541 	mvnne	r0,0

000002f4 1a000006    542 	bne	.L153

                     543 .L170:

                     544 ;182:     }


                     545 ;183: 


                     546 ;184:     bufPos = BerDecoder_decodeLength(inBuf, &userDataLength, bufPos, inLen);


                     547 

000002f8 e1a03005    548 	mov	r3,r5

000002fc e28d1004    549 	add	r1,sp,4

00000300 e1a00004    550 	mov	r0,r4

00000304 eb000000*   551 	bl	BerDecoder_decodeLength

                     552 ;185: 


                     553 ;186:     *pOutUserData = inBuf +bufPos;


                     554 

00000308 e0800004    555 	add	r0,r0,r4

0000030c e5870000    556 	str	r0,[r7]

                     557 ;187: 


                     558 ;188:     return userDataLength;


                     559 

00000310 e59d0004    560 	ldr	r0,[sp,4]

                     561 .L153:

00000314 e28dd008    562 	add	sp,sp,8

00000318 e8bd80f0    563 	ldmfd	[sp]!,{r4-r7,pc}

                     564 	.endf	isoPresentation_parseUserData

                     565 	.align	4

                     566 ;len	[sp]	local

                     567 ;userDataLength	[sp,4]	local

                     568 ;bufPos	r2	local

                     569 

                     570 ;presentation	r6	param

                     571 ;inBuf	r4	param

                     572 ;inLen	r5	param

                     573 ;pOutUserData	r7	param

                     574 

                     575 	.section ".bss","awb"

                     576 .L260:

                     577 	.data

                     578 	.text

                     579 

                     580 ;189: }


                     581 

                     582 ;190: 


                     583 ;191: int IsoPresentation_createUserData(IsoPresentation* presentation,


                     584 	.align	4

                     585 	.align	4

                     586 IsoPresentation_createUserData::

0000031c e92d44f0    587 	stmfd	[sp]!,{r4-r7,r10,lr}

                     588 ;192:                                    unsigned char* buf, unsigned char* userData, int userDataLen)


                     589 ;193: {


                     590 

                     591 ;194:     int bufPos = 0;


                     592 

                     593 ;195: 


                     594 ;196:     int userDataLengthFieldSize = BerEncoder_determineLengthSize(userDataLen);


                     595 

00000320 e1a04001    596 	mov	r4,r1

00000324 e1a07002    597 	mov	r7,r2

00000328 e1a06000    598 	mov	r6,r0

0000032c e1a05003    599 	mov	r5,r3


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cbs1.s
00000330 e1a00005    600 	mov	r0,r5

00000334 eb000000*   601 	bl	BerEncoder_determineLengthSize

                     602 ;197: 


                     603 ;198:     int pdvListLength = userDataLen + (userDataLengthFieldSize + 4);


                     604 

00000338 e0800005    605 	add	r0,r0,r5

0000033c e280a004    606 	add	r10,r0,4

                     607 ;199: 


                     608 ;200:     int pdvListLengthFieldSize = BerEncoder_determineLengthSize(pdvListLength);


                     609 

00000340 e1a0000a    610 	mov	r0,r10

00000344 eb000000*   611 	bl	BerEncoder_determineLengthSize

                     612 ;201:     int presentationLength = pdvListLength + (pdvListLengthFieldSize + 1);


                     613 

00000348 e1a02004    614 	mov	r2,r4

0000034c e3a03000    615 	mov	r3,0

00000350 e08a0000    616 	add	r0,r10,r0

00000354 e2801001    617 	add	r1,r0,1

                     618 ;202: 


                     619 ;203:     bufPos = BerEncoder_encodeTL(PRESENTATION_USER_DATA, presentationLength,


                     620 

00000358 e3a00061    621 	mov	r0,97

0000035c eb000000*   622 	bl	BerEncoder_encodeTL

                     623 ;204:                                  buf, bufPos);


                     624 ;205: 


                     625 ;206:     bufPos = BerEncoder_encodeTL(ASN_SEQUENCE, pdvListLength, buf, bufPos);


                     626 

00000360 e1a02004    627 	mov	r2,r4

00000364 e1a0100a    628 	mov	r1,r10

00000368 e1a03000    629 	mov	r3,r0

0000036c e3a00030    630 	mov	r0,48

00000370 eb000000*   631 	bl	BerEncoder_encodeTL

                     632 ;207: 


                     633 ;208:     buf[bufPos++] = ASN_INTEGER;


                     634 

00000374 e280a001    635 	add	r10,r0,1

00000378 e3a01002    636 	mov	r1,2

0000037c e7c41000    637 	strb	r1,[r4,r0]

                     638 ;209:     buf[bufPos++] = 0x01;


                     639 

00000380 e3a00001    640 	mov	r0,1

00000384 e7c4000a    641 	strb	r0,[r4,r10]

00000388 e28aa001    642 	add	r10,r10,1

                     643 ;210:     buf[bufPos++] = presentation->presentationContextId;


                     644 

0000038c e28a3001    645 	add	r3,r10,1

                     646 ;211: 


                     647 ;212:     bufPos = BerEncoder_encodeTL(PRESENTATION_USER_DATA_NEXT, userDataLen,


                     648 

00000390 e1a02004    649 	mov	r2,r4

00000394 e5d60008    650 	ldrb	r0,[r6,8]

00000398 e1a01005    651 	mov	r1,r5

0000039c e7c4000a    652 	strb	r0,[r4,r10]

000003a0 e3a000a0    653 	mov	r0,160

000003a4 eb000000*   654 	bl	BerEncoder_encodeTL

000003a8 e1a02005    655 	mov	r2,r5

000003ac e1a01007    656 	mov	r1,r7

000003b0 e1a0a000    657 	mov	r10,r0

                     658 ;213:                                  buf, bufPos);


                     659 ;214: 


                     660 ;215:     memcpy( buf + bufPos, userData, userDataLen );



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cbs1.s
                     661 

000003b4 e08a0004    662 	add	r0,r10,r4

000003b8 eb000000*   663 	bl	memcpy

                     664 ;216:     return bufPos + userDataLen;


                     665 

000003bc e08a0005    666 	add	r0,r10,r5

000003c0 e8bd84f0    667 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     668 	.endf	IsoPresentation_createUserData

                     669 	.align	4

                     670 ;bufPos	r10	local

                     671 ;pdvListLength	r10	local

                     672 

                     673 ;presentation	r6	param

                     674 ;buf	r4	param

                     675 ;userData	r7	param

                     676 ;userDataLen	r5	param

                     677 

                     678 	.section ".bss","awb"

                     679 .L318:

                     680 	.data

                     681 	.text

                     682 

                     683 ;217: }


                     684 	.align	4

                     685 .L37:

000003c4 00000000*   686 	.data.w	berId

                     687 	.type	.L37,$object

                     688 	.size	.L37,4

                     689 

                     690 .L152:

000003c8 00000000*   691 	.data.w	calledPresentationSelector

                     692 	.type	.L152,$object

                     693 	.size	.L152,4

                     694 

                     695 	.align	4

                     696 

                     697 	.data

                     698 .L340:

                     699 	.globl	berId

00000000 0151       700 berId:	.data.b	81,1

                     701 	.type	berId,$object

                     702 	.size	berId,2

00000002 0000       703 	.space	2

                     704 .L341:

                     705 	.globl	calledPresentationSelector

00000004 00         706 calledPresentationSelector:	.space	1

00000005 00         707 	.space	1

00000006 00         708 	.space	1

00000007 01         709 	.data.b	1

                     710 	.type	calledPresentationSelector,$object

                     711 	.size	calledPresentationSelector,4

                     712 .L342:

                     713 	.globl	Asn_Id_Acse

00000008 0152       714 Asn_Id_Acse:	.data.b	82,1

0000000a 00         715 	.space	1

0000000b 01         716 	.data.b	1

                     717 	.type	Asn_Id_Acse,$object

                     718 	.size	Asn_Id_Acse,4

                     719 .L343:

                     720 	.globl	Asn_Id_Mms

0000000c 0222ca28    721 Asn_Id_Mms:	.data.b	40,202,34,2


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cbs1.s
00000010 01         722 	.data.b	1

00000011 000000     723 	.space	3

                     724 	.type	Asn_Id_Mms,$object

                     725 	.size	Asn_Id_Mms,8

                     726 	.ghsnote version,6

                     727 	.ghsnote tools,3

                     728 	.ghsnote options,0

                     729 	.text

                     730 	.align	4

                     731 	.data

                     732 	.align	4

                     733 	.text

