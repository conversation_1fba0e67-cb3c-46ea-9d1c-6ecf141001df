                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_52s1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedInt.c -o iedTree\gh_52s1.o -list=iedTree/iedInt.lst C:\Users\<USER>\AppData\Local\Temp\gh_52s1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_52s1.s
Source File: iedInt.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		iedTree/iedInt.c -o iedTree/iedInt.o

                      12 ;Source File:   iedTree/iedInt.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:51 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "iedInt.h"


                      23 ;2: 


                      24 ;3: #include "iedTree.h"


                      25 ;4: #include "iedFinalDA.h"


                      26 ;5: 


                      27 ;6: #include "../DataSlice.h"


                      28 ;7: #include "../AsnEncoding.h"


                      29 ;8: 


                      30 ;9: #include "debug.h"


                      31 ;10: 


                      32 ;11: #include "IEDCompile/AccessInfo.h"


                      33 ;12: 


                      34 ;13: #define MAX_INT32_ENCODED_SIZE 6


                      35 ;14: 


                      36 ;15: static void updateFromDataSlice(IEDEntity entity)


                      37 	.text

                      38 	.align	4

                      39 updateFromDataSlice:

00000000 e92d4010     40 	stmfd	[sp]!,{r4,lr}

00000004 e1a04000     41 	mov	r4,r0

                      42 ;16: {


                      43 

                      44 ;17: 	int offset  = entity->dataSliceOffset;


                      45 

00000008 e594002c     46 	ldr	r0,[r4,44]

                      47 ;18: 	int32_t value;


                      48 ;19: 


                      49 ;20: 	if(offset == -1)


                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_52s1.s
0000000c e3700001     51 	cmn	r0,1

00000010 0a00000f     52 	beq	.L2

                      53 ;21: 	{


                      54 

                      55 ;22: 		return;


                      56 

                      57 ;23: 	}


                      58 ;24: 


                      59 ;25: 	value = DataSlice_getInt32FastCurrDS(offset);


                      60 

00000014 e1a00800     61 	mov	r0,r0 lsl 16

00000018 e1a00820     62 	mov	r0,r0 lsr 16

0000001c eb000000*    63 	bl	DataSlice_getInt32FastCurrDS

                      64 ;26: 


                      65 ;27: 


                      66 ;28: 	if(entity->intValue == value)


                      67 

00000020 e5941030     68 	ldr	r1,[r4,48]

00000024 e1510000     69 	cmp	r1,r0

                      70 ;29: 	{


                      71 

                      72 ;30: 		entity->changed = TRGOP_NONE;


                      73 

00000028 03a00000     74 	moveq	r0,0

0000002c 05840028     75 	streq	r0,[r4,40]

00000030 0a000007     76 	beq	.L2

                      77 ;31: 	}


                      78 ;32: 	else


                      79 ;33: 	{


                      80 

                      81 ;34: 		entity->changed = entity->trgOps;


                      82 

00000034 e5941024     83 	ldr	r1,[r4,36]

00000038 e5840030     84 	str	r0,[r4,48]

                      85 ;36: 		IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());


                      86 

0000003c e5841028     87 	str	r1,[r4,40]

                      88 ;35: 		entity->intValue = value;


                      89 

00000040 eb000000*    90 	bl	dataSliceGetTimeStamp

00000044 e1a02001     91 	mov	r2,r1

00000048 e1a01000     92 	mov	r1,r0

0000004c e1a00004     93 	mov	r0,r4

00000050 eb000000*    94 	bl	IEDEntity_setTimeStamp

                      95 .L2:

00000054 e8bd4010     96 	ldmfd	[sp]!,{r4,lr}

00000058 e12fff1e*    97 	ret	

                      98 	.endf	updateFromDataSlice

                      99 	.align	4

                     100 ;offset	r0	local

                     101 ;value	r0	local

                     102 

                     103 ;entity	r4	param

                     104 

                     105 	.section ".bss","awb"

                     106 .L56:

                     107 	.data

                     108 	.text

                     109 

                     110 ;37: 	}


                     111 ;38: }



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_52s1.s
                     112 

                     113 ;39: 


                     114 ;40: 


                     115 ;41: static bool calcReadLen(IEDEntity entity, size_t* pLen )


                     116 	.align	4

                     117 	.align	4

                     118 calcReadLen:

0000005c e92d4010    119 	stmfd	[sp]!,{r4,lr}

                     120 ;42: {


                     121 

                     122 ;43: 	// +2 for tag and length


                     123 ;44: 	*pLen = BerEncoder_Int32DetermineEncodedSize(entity->intValue) + 2;


                     124 

00000060 e5900030    125 	ldr	r0,[r0,48]

00000064 e1a04001    126 	mov	r4,r1

00000068 eb000000*   127 	bl	BerEncoder_Int32DetermineEncodedSize

0000006c e2800002    128 	add	r0,r0,2

00000070 e5840000    129 	str	r0,[r4]

                     130 ;45: 	return true;


                     131 

00000074 e3a00001    132 	mov	r0,1

00000078 e8bd4010    133 	ldmfd	[sp]!,{r4,lr}

0000007c e12fff1e*   134 	ret	

                     135 	.endf	calcReadLen

                     136 	.align	4

                     137 

                     138 ;entity	r0	param

                     139 ;pLen	r4	param

                     140 

                     141 	.section ".bss","awb"

                     142 .L97:

                     143 	.data

                     144 	.text

                     145 

                     146 ;46: }


                     147 

                     148 ;47: 


                     149 ;48: static bool encodeRead(IEDEntity entity, BufferView* outBuf)


                     150 	.align	4

                     151 	.align	4

                     152 encodeRead:

00000080 e92d4030    153 	stmfd	[sp]!,{r4-r5,lr}

00000084 e24dd004    154 	sub	sp,sp,4

00000088 e1a0200d    155 	mov	r2,sp

0000008c e1a05000    156 	mov	r5,r0

00000090 e1a04001    157 	mov	r4,r1

00000094 e1a00004    158 	mov	r0,r4

00000098 e3a01006    159 	mov	r1,6

0000009c eb000000*   160 	bl	BufferView_alloc

                     161 ;49: {


                     162 

                     163 ;50: 	uint8_t* encodeBuf;


                     164 ;51: 	int fullEncodedLen;


                     165 ;52: 


                     166 ;53: 	//Запрашиваем в буфере максимум места чтобы не вычислять.


                     167 ;54: 	//Это фактически только проверка, поэтому небольшая жадность не повредит.


                     168 ;55: 	if(!BufferView_alloc(outBuf,MAX_INT32_ENCODED_SIZE, &encodeBuf))


                     169 

000000a0 e3500000    170 	cmp	r0,0

                     171 ;56: 	{


                     172 


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_52s1.s
                     173 ;57: 		ERROR_REPORT("Unable to allocate buffer");


                     174 ;58: 		return false;


                     175 

000000a4 0a000008    176 	beq	.L104

                     177 ;59: 	}


                     178 ;60: 


                     179 ;61: 	//Функция возвращает новое смещение в буфере, но поскольку начальное


                     180 ;62: 	//смещение 0, можно считать это размером.


                     181 ;63:     fullEncodedLen = BerEncoder_EncodeInt32WithTL(


                     182 

000000a8 e59d2000    183 	ldr	r2,[sp]

000000ac e5951030    184 	ldr	r1,[r5,48]

000000b0 e3a03000    185 	mov	r3,0

000000b4 e3a00085    186 	mov	r0,133

000000b8 eb000000*   187 	bl	BerEncoder_EncodeInt32WithTL

                     188 ;64:                 IEC61850_BER_INTEGER, entity->intValue, encodeBuf, 0);


                     189 ;65: 


                     190 ;66: 	outBuf->pos += fullEncodedLen;


                     191 

000000bc e5941004    192 	ldr	r1,[r4,4]

000000c0 e0811000    193 	add	r1,r1,r0

000000c4 e5841004    194 	str	r1,[r4,4]

                     195 ;67: 	return true;


                     196 

000000c8 e3a00001    197 	mov	r0,1

                     198 .L104:

000000cc e28dd004    199 	add	sp,sp,4

000000d0 e8bd4030    200 	ldmfd	[sp]!,{r4-r5,lr}

000000d4 e12fff1e*   201 	ret	

                     202 	.endf	encodeRead

                     203 	.align	4

                     204 ;encodeBuf	[sp]	local

                     205 

                     206 ;entity	r5	param

                     207 ;outBuf	r4	param

                     208 

                     209 	.section ".bss","awb"

                     210 .L154:

                     211 	.data

                     212 	.text

                     213 

                     214 ;68: }


                     215 

                     216 ;69: 


                     217 ;70: 


                     218 ;71: void IEDInt32_init(IEDEntity entity)


                     219 	.align	4

                     220 	.align	4

                     221 IEDInt32_init::

000000d8 e92d4070    222 	stmfd	[sp]!,{r4-r6,lr}

000000dc e280405c    223 	add	r4,r0,92

000000e0 e5140004    224 	ldr	r0,[r4,-4]

                     225 ;74: 	IntBoolAccessInfo* accessInfo = extInfo->accessInfo;


                     226 

000000e4 e5900000    227 	ldr	r0,[r0]

                     228 ;75: 


                     229 ;76: 	//Если будет ошибка, то запишется -1;


                     230 ;77: 	entity->dataSliceOffset = DataSlice_getIntOffset(accessInfo->valueOffset);


                     231 

000000e8 e59f5028*   232 	ldr	r5,.L197

000000ec e5900004    233 	ldr	r0,[r0,4]


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_52s1.s
000000f0 e59f6024*   234 	ldr	r6,.L198

                     235 ;72: {


                     236 

                     237 ;73: 	TerminalItem* extInfo = entity->extInfo;


                     238 

000000f4 eb000000*   239 	bl	DataSlice_getIntOffset

000000f8 e5040030    240 	str	r0,[r4,-48]

                     241 ;78: 


                     242 ;79: 	entity->calcReadLen = calcReadLen;


                     243 

                     244 ;80: 	entity->encodeRead = encodeRead;


                     245 

000000fc e1a00006    246 	mov	r0,r6

00000100 e8840021    247 	stmea	[r4],{r0,r5}

                     248 ;81: 	entity->updateFromDataSlice = updateFromDataSlice;


                     249 

00000104 e59f0014*   250 	ldr	r0,.L199

00000108 e584000c    251 	str	r0,[r4,12]

                     252 ;82: 


                     253 ;83: 	IEDTree_addToCmpList(entity);


                     254 

0000010c e244005c    255 	sub	r0,r4,92

00000110 e8bd4070    256 	ldmfd	[sp]!,{r4-r6,lr}

00000114 ea000000*   257 	b	IEDTree_addToCmpList

                     258 	.endf	IEDInt32_init

                     259 	.align	4

                     260 ;extInfo	r0	local

                     261 ;accessInfo	r0	local

                     262 

                     263 ;entity	r4	param

                     264 

                     265 	.section ".bss","awb"

                     266 .L190:

                     267 	.data

                     268 	.text

                     269 

                     270 ;84: }


                     271 	.align	4

                     272 .L197:

00000118 00000000*   273 	.data.w	calcReadLen

                     274 	.type	.L197,$object

                     275 	.size	.L197,4

                     276 

                     277 .L198:

0000011c 00000000*   278 	.data.w	encodeRead

                     279 	.type	.L198,$object

                     280 	.size	.L198,4

                     281 

                     282 .L199:

00000120 00000000*   283 	.data.w	updateFromDataSlice

                     284 	.type	.L199,$object

                     285 	.size	.L199,4

                     286 

                     287 	.align	4

                     288 

                     289 	.data

                     290 	.ghsnote version,6

                     291 	.ghsnote tools,3

                     292 	.ghsnote options,0

                     293 	.text

                     294 	.align	4


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_52s1.s
