                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=fdzipstream.c -o fdzipstream\gh_1as1.o -list=fdzipstream/fdzipstream.lst C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
Source File: fdzipstream.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		fdzipstream/fdzipstream.c -o fdzipstream/fdzipstream.o

                      12 ;Source File:   fdzipstream/fdzipstream.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:00 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: /***************************************************************************

                      23 ;2:  * fdzipstream.c

                      24 ;3:  *

                      25 ;4:  * Create ZIP archives in streaming fashion, writing to a file

                      26 ;5:  * descriptor.  The output stream (file descriptor) does not need to

                      27 ;6:  * be seekable and can be a pipe or a network socket.  The entire

                      28 ;7:  * archive contents does not need to be in memory at once.

                      29 ;8:  *

                      30 ;9:  * zlib is required for deflate compression: http://www.zlib.net/

                      31 ;10:  *

                      32 ;11:  * What this will do for you:

                      33 ;12:  *

                      34 ;13:  * - Create a ZIP archive in a streaming fashion, writing to an output

                      35 ;14:  *   stream (file descriptor, pipe, network socket) without seeking.

                      36 ;15:  * - Compress the archive entries (using zlib).  Support for the STORE

                      37 ;16:  *   and DEFLATE methods is included, others may be implemented through

                      38 ;17:  *   callback functions.

                      39 ;18:  * - Add ZIP64 structures as needed to support large (>4GB) archives.

                      40 ;19:  * - Simple creation of ZIP archives even if not streaming.

                      41 ;20:  *

                      42 ;21:  * What this will NOT do for you:

                      43 ;22:  *

                      44 ;23:  * - Open/close files or sockets.

                      45 ;24:  * - Support advanced ZIP archive features (e.g. file attributes, encryption).

                      46 ;25:  * - Allow archiving of individual files/entries larger than 4GB, the total

                      47 ;26:  *    of all files can be larger than 4GB but not individual entries.

                      48 ;27:  *

                      49 ;28:  * ZIP archive file/entry modifiation times are stored in UTC.

                      50 ;29:  *


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                      51 ;30:  * Usage pattern

                      52 ;31:  *

                      53 ;32:  * Creating a ZIP archive when entire files/entries are in memory:

                      54 ;33:  *  zs_init ()

                      55 ;34:  *    for each entry:

                      56 ;35:  *      zs_writeentry ()

                      57 ;36:  *  zs_finish ()

                      58 ;37:  *  zs_free ()

                      59 ;38:  *

                      60 ;39:  * Creating a ZIP archive when files/entries are chunked:

                      61 ;40:  *  zs_init ()

                      62 ;41:  *    for each entry:

                      63 ;42:  *      zs_entrybegin ()

                      64 ;43:  *        for each chunk of entry:

                      65 ;44:  *          zs_entrydata()

                      66 ;45:  *      zs_entryend()

                      67 ;46:  *  zs_finish ()

                      68 ;47:  *  zs_free ()

                      69 ;48:  *

                      70 ;49:  ****

                      71 ;50:  * To use archive entry compression methods other than the included

                      72 ;51:  * STORE and DEFLATE methods you must create and register callback

                      73 ;52:  * funtions as follows:

                      74 ;53:  *

                      75 ;54:  * int32_t init (ZIPstream *zstream, ZIPentry *zentry)

                      76 ;55:  *

                      77 ;56:  *   This optional function is called at the beginning of each entry.

                      78 ;57:  *   Return: 0 on success and non-zero on error.

                      79 ;58:  *

                      80 ;59:  * int32_t process (ZIPstream *zstream, ZIPentry *zentry,

                      81 ;60:  *                  uint8_t *entry, int64_t entrySize, int64_t *entryConsumed,

                      82 ;61:  *                  uint8_t* writeBuffer, int64_t writeBufferSize)

                      83 ;62:  *

                      84 ;63:  *   This required function is called to process entry content data.

                      85 ;64:  *   Data to write into the archive should be returned in writeBuffer.

                      86 ;65:  *   When entry is NULL internal buffers should be flushed.

                      87 ;66:  *   Return: Count of bytes ready in writeBuffer, 0 on completion and <0 on error

                      88 ;67:  *

                      89 ;68:  * int32_t finish (ZIPstream *zstream, ZIPentry *zentry)

                      90 ;69:  *

                      91 ;70:  *   This optional function is called at the end of each entry.

                      92 ;71:  *   Return: 0 on success and non-zero on error.

                      93 ;72:  *

                      94 ;73:  * These three functions must be registered, through zs_registermethod(),

                      95 ;74:  * with any ZIPstream that will use them.

                      96 ;75:  ****

                      97 ;76:  * LICENSE

                      98 ;77:  *

                      99 ;78:  * Copyright 2019 CTrabant

                     100 ;79:  *

                     101 ;80:  * Licensed under the Apache License, Version 2.0 (the "License");

                     102 ;81:  * you may not use this file except in compliance with the License.

                     103 ;82:  * You may obtain a copy of the License at

                     104 ;83:  *

                     105 ;84:  *     http://www.apache.org/licenses/LICENSE-2.0

                     106 ;85:  *

                     107 ;86:  * Unless required by applicable law or agreed to in writing, software

                     108 ;87:  * distributed under the License is distributed on an "AS IS" BASIS,

                     109 ;88:  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

                     110 ;89:  * See the License for the specific language governing permissions and

                     111 ;90:  * limitations under the License.


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                     112 ;91:  ***************************************************************************/

                     113 ;93: /* Allow this code to be skipped by declaring NOFDZIP */

                     114 ;94: #ifndef NOFDZIP

                     115 ;96: #define FDZIPVERSION 2.2

                     116 ;98: #define fprintf(...)

                     117 ;100: #include <stdlib.h>

                     118 ;101: #include <stdint.h>

                     119 ;102: #include <string.h>

                     120 ;105: #include "fdzipstream.h"

                     121 ;106: #include "crc32.h"

                     122 ;107: #include <time.h>

                     123 ;108: #include <string.h>

                     124 ;109: #include "../fs/OscFiles.h"

                     125 ;110: #include "../timetools.h"

                     126 ;112: #define BIT_SET(a,b) ((a) |= (1<<(b)))

                     127 ;114: static int64_t zs_writedata ( ZIPstream *zstream, uint8_t *writeBuffer, int64_t writeBufferSize );

                     128 ;115: static uint32_t zs_datetime_unixtodos ( time_t t );

                     129 ;116: static void zs_packunit16 (ZIPstream *ZS, int *O, uint16_t V);

                     130 ;117: static void zs_packunit32 (ZIPstream *ZS, int *O, uint32_t V);

                     131 ;118: static void zs_packunit64 (ZIPstream *ZS, int *O, uint64_t V);

                     132 ;120: static void * allocMemory(size_t elsize)

                     133 ;121: {

                     134 ;122:     void *p;

                     135 ;124:     p = OscFiles_malloc(elsize);

                     136 ;125:     if (p == 0)

                     137 ;126:         return (p);

                     138 ;128:     memset (p, 0, elsize);

                     139 ;129:     return (p);

                     140 ;130: }

                     141 ;132: void freeMemory(void *p)

                     142 ;133: {

                     143 ;134: 	OscFiles_free(p);

                     144 ;135: }

                     145 ;137: /***************************************************************************

                     146 ;138:  * zs_store_process:

                     147 ;139:  *

                     148 ;140:  * The process() callback for the STORE method.

                     149 ;141:  *

                     150 ;142:  * @return number of bytes ready for writing in writeBuffer or <0 on error.

                     151 ;143:  ***************************************************************************/

                     152 ;144: static int32_t

                     153 ;145: zs_store_process ( ZIPstream *zstream, ZIPentry *zentry,

                     154 ;146:                    uint8_t *entry, int64_t entrySize, int64_t *entryConsumed,

                     155 ;147:                    uint8_t *writeBuffer, int64_t writeBufferSize )

                     156 ;148: {

                     157 ;149:   if ( ! entry || entrySize <= 0 )

                     158 ;150:     return 0;

                     159 ;152:   if ( entrySize < writeBufferSize )

                     160 ;153:     {

                     161 ;154:       writeBufferSize = entrySize;

                     162 ;155:     }

                     163 ;157:   memcpy ( writeBuffer, entry, (size_t)writeBufferSize );

                     164 ;159:   if ( entryConsumed )

                     165 ;160:     {

                     166 ;161:       *entryConsumed = writeBufferSize;

                     167 ;162:     }

                     168 ;164:   return (int32_t)writeBufferSize;

                     169 ;165: }  /* End of zs_store_process() */

                     170 ;169: /***************************************************************************

                     171 ;170:  * zs_registermethod:

                     172 ;171:  *


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                     173 ;172:  * Initialize a new ZIPmethod entry and add it to the method list for

                     174 ;173:  * the supplied ZIPstream.

                     175 ;174:  *

                     176 ;175:  * Each method requires an ID, mind that the ZIP APPNOTE defines some

                     177 ;176:  * specific IDs already.  Each method is also required to provide

                     178 ;177:  * three functions:

                     179 ;178:  *

                     180 ;179:  * init()     : Initialization to start an entry, optional.

                     181 ;180:  * process()  : Process new data and/or flush to finalize an entry, required.

                     182 ;181:  * finish()   : Finalize an entry, cleanup, optional.

                     183 ;182:  *

                     184 ;183:  * Optional function pointers should NULL if no action is needed.

                     185 ;184:  *

                     186 ;185:  * @return a pointer to a ZIPmethod struct on success or NULL on error.

                     187 ;186:  ***************************************************************************/

                     188 ;187: ZIPmethod *

                     189 ;188: zs_registermethod ( ZIPstream *zs, int32_t methodID,

                     190 ;189:                     int32_t (*init)( ZIPstream*, ZIPentry* ),

                     191 ;190:                     int32_t (*process)( ZIPstream*, ZIPentry*,

                     192 ;191:                                         uint8_t*, int64_t, int64_t*,

                     193 ;192:                                         uint8_t*, int64_t ),

                     194 ;193:                     int32_t (*finish)( ZIPstream*, ZIPentry* )

                     195 ;194:                     )

                     196 ;195: {

                     197 ;196:   ZIPmethod *method = zs->firstMethod;

                     198 ;198:   /* Require a process() callback for the method */

                     199 ;199:   if ( ! process )

                     200 ;200:     {

                     201 ;201:       fprintf (stderr, "Compression method (%d) must provide a process() callback\n",

                     202 ;202:                methodID);

                     203 ;203:       return NULL;

                     204 ;204:     }

                     205 ;206:   /* Search for existing method */

                     206 ;207:   while ( method )

                     207 ;208:     {

                     208 ;209:       if ( method->ID == methodID )

                     209 ;210:         {

                     210 ;211:           fprintf (stderr, "Compression method (%d) already registered\n",

                     211 ;212:                    methodID);

                     212 ;213:           return NULL;

                     213 ;214:         }

                     214 ;216:       method = method->next;

                     215 ;217:     }

                     216 ;219:   /* Allocate and initialize new method */

                     217 ;220:   method = (ZIPmethod *)allocMemory(sizeof(ZIPmethod));

                     218 ;222:   if ( method == NULL )

                     219 ;223:     {

                     220 ;224:       fprintf (stderr, "Cannot allocate memory for method\n");

                     221 ;225:       return NULL;

                     222 ;226:     }

                     223 ;228:   method->ID = methodID;

                     224 ;229:   method->init = init;

                     225 ;230:   method->process = process;

                     226 ;231:   method->finish = finish;

                     227 ;233:   /* Add new method to ZIPstream list */

                     228 ;234:   method->next = zs->firstMethod;

                     229 ;235:   zs->firstMethod = method;

                     230 ;237:   return method;

                     231 ;238: }  /* End of zs_registermethod() */

                     232 ;241: /***************************************************************************

                     233 ;242:  * zs_init:


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                     234 ;243:  *

                     235 ;244:  * Initialize and return an ZIPstream struct. If a pointer to an

                     236 ;245:  * existing ZIPstream is supplied it will be re-initizlied, otherwise

                     237 ;246:  * memory will be allocated.

                     238 ;247:  *

                     239 ;248:  * @return a pointer to a ZIPstream struct on success or NULL on error.

                     240 ;249:  ***************************************************************************/

                     241 ;250: ZIPstream *

                     242 ;251: zs_init ( int fd, ZIPstream *zs )

                     243 ;252: {

                     244 ;253:   ZIPentry *zentry, *zefree;

                     245 ;254:   ZIPmethod *method, *mfree;

                     246 ;256:   if ( ! zs )

                     247 ;257:     {

                     248 ;258:       zs = (ZIPstream *) allocMemory (sizeof(ZIPstream));

                     249 ;259:     }

                     250 ;260:   else

                     251 ;261:     {

                     252 ;262:       zentry = zs->FirstEntry;

                     253 ;263:       while ( zentry )

                     254 ;264:         {

                     255 ;265:           zefree = zentry;

                     256 ;266:           zentry = zentry->next;

                     257 ;267:           freeMemory (zefree);

                     258 ;268:         }

                     259 ;270:       method = zs->firstMethod;

                     260 ;271:       while ( method )

                     261 ;272:         {

                     262 ;273:           mfree = method;

                     263 ;274:           method = method->next;

                     264 ;275: 		  freeMemory(mfree);

                     265 ;276:         }

                     266 ;277:     }

                     267 ;279:   if ( zs == NULL )

                     268 ;280:     {

                     269 ;281:       fprintf (stderr, "zs_init: Cannot allocate memory for ZIPstream\n");

                     270 ;282:       return NULL;

                     271 ;283:     }

                     272 ;285:   memset (zs, 0, sizeof (ZIPstream));

                     273 ;287:   zs->fd = fd;

                     274 ;289:   /* Register the included ZS_STORE and ZS_DEFLATE compression methods */

                     275 ;290:   if ( ! zs_registermethod ( zs, ZS_STORE,

                     276 ;291:                              NULL,

                     277 ;292:                              zs_store_process,

                     278 ;293:                              NULL ) )

                     279 ;294:     {

                     280 ;295: 	  freeMemory(zs);

                     281 ;296:       return NULL;

                     282 ;297:     }

                     283 ;299:   return zs;

                     284 ;300: }  /* End of zs_init() */

                     285 ;303: /***************************************************************************

                     286 ;304:  * zs_free:

                     287 ;305:  *

                     288 ;306:  * Free all memory associated with a ZIPstream including all ZIPentry

                     289 ;307:  * structures.

                     290 ;308:  ***************************************************************************/

                     291 ;309: void

                     292 ;310: zs_free ( ZIPstream *zs )

                     293 ;311: {

                     294 ;312:   ZIPentry *zentry, *zefree;


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                     295 ;313:   ZIPmethod *method, *mfree;

                     296 ;315:   if ( ! zs )

                     297 ;316:     return;

                     298 ;318:   zentry = zs->FirstEntry;

                     299 ;319:   while ( zentry )

                     300 ;320:     {

                     301 ;321:       zefree = zentry;

                     302 ;322:       zentry = zentry->next;

                     303 ;323: 	  freeMemory(zefree);

                     304 ;324:     }

                     305 ;326:   method = zs->firstMethod;

                     306 ;327:   while ( method )

                     307 ;328:     {

                     308 ;329:       mfree = method;

                     309 ;330:       method = method->next;

                     310 ;331: 	  freeMemory(mfree);

                     311 ;332:     }

                     312 ;334:   freeMemory(zs);

                     313 ;336: }  /* End of zs_free() */

                     314 ;339: /***************************************************************************

                     315 ;340:  * zs_writeentry:

                     316 ;341:  *

                     317 ;342:  * Write ZIP archive entry contained in a memory buffer using the

                     318 ;343:  * specified compression methodID.

                     319 ;344:  *

                     320 ;345:  * The methodID argument specifies the compression methodID to be used

                     321 ;346:  * for this entry.  Included methods are:

                     322 ;347:  *   Z_STORE   - no compression

                     323 ;348:  *   Z_DEFLATE - deflate compression

                     324 ;349:  *

                     325 ;350:  * The entry modified time (modtime) is stored in UTC.

                     326 ;351:  *

                     327 ;352:  * If specified, writestatus will be set to the output of write() when

                     328 ;353:  * a write error occurs, otherwise it will be set to 0.

                     329 ;354:  *

                     330 ;355:  * @return pointer to ZIPentry on success and NULL on error.

                     331 ;356:  ***************************************************************************/

                     332 ;357: ZIPentry *

                     333 ;358: zs_writeentry ( ZIPstream *zstream, uint8_t *entry, int64_t entrySize,

                     334 ;359:                 char *name, time_t modtime, int methodID, int64_t *writestatus )

                     335 ;360: {

                     336 ;361:   ZIPentry *zentry = NULL;

                     337 ;363:   if ( writestatus )

                     338 ;364:     *writestatus = 0;

                     339 ;366:   if ( ! zstream )

                     340 ;367:     return NULL;

                     341 ;369:   if ( entrySize > 0xFFFFFFFF )

                     342 ;370:     {

                     343 ;371:       fprintf (stderr, "zs_writeentry(%s): Individual entries cannot exceed %lld bytes\n",

                     344 ;372:                (name) ? name : "", (long long) 0xFFFFFFFF);

                     345 ;373:       return NULL;

                     346 ;374:     }

                     347 ;376:   /* Begin entry */

                     348 ;377:   if ( ! (zentry = zs_entrybegin ( zstream, name, modtime, methodID, writestatus )) )

                     349 ;378:     {

                     350 ;379:       return NULL;

                     351 ;380:     }

                     352 ;382:   /* Process entry data and flush */

                     353 ;383:   if ( ! zs_entrydata (zstream, zentry, entry, entrySize, writestatus) )

                     354 ;384:     {

                     355 ;385:       return NULL;


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                     356 ;386:     }

                     357 ;388:   /* End entry */

                     358 ;389:   if ( ! zs_entryend (zstream, zentry, writestatus) )

                     359 ;390:     {

                     360 ;391:       return NULL;

                     361 ;392:     }

                     362 ;394:   return zentry;

                     363 ;395: }  /* End of zs_writeentry() */

                     364 ;399: /***************************************************************************

                     365 ;400:  * zs_entrybegin:

                     366 ;401:  *

                     367 ;402:  * Begin a streaming entry by writing a Local File Header to the

                     368 ;403:  * output stream.  The modtime argument sets the modification time

                     369 ;404:  * stamp for the entry.

                     370 ;405:  *

                     371 ;406:  * The methodID argument specifies the compression method to be used

                     372 ;407:  * for this entry.  Included methods are:

                     373 ;408:  *   Z_STORE   - no compression

                     374 ;409:  *   Z_DEFLATE - deflate compression

                     375 ;410:  *

                     376 ;411:  * The entry modified time (modtime) is stored in UTC.

                     377 ;412:  *

                     378 ;413:  * If specified, writestatus will be set to the output of write() when

                     379 ;414:  * a write error occurs, otherwise it will be set to 0.

                     380 ;415:  *

                     381 ;416:  * @return pointer to ZIPentry on success and NULL on error.

                     382 ;417:  ***************************************************************************/

                     383 ;418: ZIPentry *

                     384 ;419: zs_entrybegin ( ZIPstream *zstream, char *name, time_t modtime, int methodID,

                     385 ;420:                 int64_t *writestatus )

                     386 ;421: {

                     387 ;422:   ZIPentry *zentry;

                     388 ;423:   ZIPmethod *method;

                     389 ;424:   int64_t lwritestatus;

                     390 ;425:   int32_t packed;

                     391 ;426:   uint32_t u32;

                     392 ;428:   if ( writestatus )

                     393 ;429:     *writestatus = 0;

                     394 ;431:   if ( ! zstream || ! name )

                     395 ;432:     return NULL;

                     396 ;434:   /* Search for method ID */

                     397 ;435:   method = zstream->firstMethod;

                     398 ;436:   while ( method )

                     399 ;437:     {

                     400 ;438:       if ( method->ID == methodID )

                     401 ;439:         break;

                     402 ;441:       method = method->next;

                     403 ;442:     }

                     404 ;444:   if ( ! method )

                     405 ;445:     {

                     406 ;446:       fprintf (stderr, "Cannot find method ID %d\n", methodID);

                     407 ;447:       return NULL;

                     408 ;448:     }

                     409 ;450:   /* Allocate and initialize new entry */

                     410 ;451:   zentry = (ZIPentry *)allocMemory(sizeof(ZIPentry));

                     411 ;452:   if ( zentry == NULL )

                     412 ;453:     {

                     413 ;454:       fprintf (stderr, "Cannot allocate memory for entry\n");

                     414 ;455:       return NULL;

                     415 ;456:     }

                     416 ;458:   zentry->ZipVersion = 20;  /* Default version for extraction (2.0) */


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                     417 ;459:   zentry->GeneralFlag = 0;

                     418 ;460:   u32 = zs_datetime_unixtodos (modtime);

                     419 ;461:   zentry->CompressionMethod = methodID;

                     420 ;462:   zentry->DOSDate = (uint16_t) (u32 >> 16);

                     421 ;463:   zentry->DOSTime = (uint16_t) (u32 & 0xFFFF);

                     422 ;464:   zentry->CRC32 = crc32 (0L, 0, 0); //Z_NULL

                     423 ;465:   zentry->CompressedSize = 0;

                     424 ;466:   zentry->UncompressedSize = 0;

                     425 ;467:   zentry->LocalHeaderOffset = zstream->WriteOffset;

                     426 ;468:   strncpy (zentry->Name, (name)?name:"", ZENTRY_NAME_LENGTH - 1);

                     427 ;469:   zentry->NameLength = (int16_t)strlen (zentry->Name);

                     428 ;470:   zentry->method = method;

                     429 ;471:   zentry->methoddata = NULL;

                     430 ;473:   /* Add new entry to stream list */

                     431 ;474:   if ( ! zstream->FirstEntry )

                     432 ;475:     {

                     433 ;476:       zstream->FirstEntry = zentry;

                     434 ;477:       zstream->LastEntry = zentry;

                     435 ;478:     }

                     436 ;479:   else

                     437 ;480:     {

                     438 ;481:       zstream->LastEntry->next = zentry;

                     439 ;482:       zstream->LastEntry = zentry;

                     440 ;483:     }

                     441 ;485:   zstream->EntryCount++;

                     442 ;487:   /* Set bit to denote streaming */

                     443 ;488:   BIT_SET (zentry->GeneralFlag, 3);

                     444 ;490:   /* Method initialization callback */

                     445 ;491:   if ( zentry->method->init &&

                     446 ;492:        zentry->method->init (zstream, zentry) )

                     447 ;493:     {

                     448 ;494:       fprintf (stderr, "Error with method (%d) init callback\n",

                     449 ;495:                zentry->method->ID);

                     450 ;496:       return NULL;

                     451 ;497:     }

                     452 ;499:   /* Write the Local File Header, with zero'd CRC and sizes (for streaming) */

                     453 ;500:   packed = 0;

                     454 ;501:   zs_packunit32 (zstream, &packed, LOCALHEADERSIG);              /* Data Description signature */

                     455 ;502:   zs_packunit16 (zstream, &packed, zentry->ZipVersion);

                     456 ;503:   zs_packunit16 (zstream, &packed, zentry->GeneralFlag);

                     457 ;504:   zs_packunit16 (zstream, &packed, zentry->CompressionMethod);

                     458 ;505:   zs_packunit16 (zstream, &packed, zentry->DOSTime);             /* DOS file modification time */

                     459 ;506:   zs_packunit16 (zstream, &packed, zentry->DOSDate);             /* DOS file modification date */

                     460 ;507:   zs_packunit32 (zstream, &packed, zentry->CRC32);               /* CRC-32 value of entry */

                     461 ;508:   zs_packunit32 (zstream, &packed, (int32_t)zentry->CompressedSize);      /* Compressed entry size */

                     462 ;509:   zs_packunit32 (zstream, &packed, (int32_t)zentry->UncompressedSize);    /* Uncompressed entry size */

                     463 ;510:   zs_packunit16 (zstream, &packed, zentry->NameLength);          /* File/entry name length */

                     464 ;511:   zs_packunit16 (zstream, &packed, 0);                           /* Extra field length */

                     465 ;512:   /* File/entry name */

                     466 ;513:   memcpy (zstream->buffer+packed, zentry->Name, zentry->NameLength); packed += zentry->NameLength;

                     467 ;515:   lwritestatus = zs_writedata (zstream, zstream->buffer, packed);

                     468 ;516:   if ( lwritestatus != packed )

                     469 ;517:     {

                     470 ;518:       fprintf (stderr, "Error writing ZIP local header: %s\n", strerror(errno));

                     471 ;520:       if ( writestatus )

                     472 ;521:         *writestatus = lwritestatus;

                     473 ;523:       return NULL;

                     474 ;524:     }

                     475 ;526:   return zentry;

                     476 ;527: }  /* End of zs_entrybegin() */

                     477 ;530: /***************************************************************************


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                     478 ;531:  * zs_entrydata:

                     479 ;532:  *

                     480 ;533:  * Write a chunk of entry data, of size entrySize, to the output

                     481 ;534:  * stream according to the parameters already set for the stream and

                     482 ;535:  * entry.

                     483 ;536:  *

                     484 ;537:  * When entry is NULL this signals a flush of any internal buffers.

                     485 ;538:  * No further data is expected after this.

                     486 ;539:  *

                     487 ;540:  * If specified, writestatus will be set to the output of write() when

                     488 ;541:  * a write error occurs, otherwise it will be set to 0.

                     489 ;542:  *

                     490 ;543:  * @return pointer to ZIPentry on success and NULL on error.

                     491 ;544:  ***************************************************************************/

                     492 ;545: ZIPentry *

                     493 ;546: zs_entrydata ( ZIPstream *zstream, ZIPentry *zentry, uint8_t *entry,

                     494 ;547:                int64_t entrySize, int64_t *writestatus )

                     495 ;548: {

                     496 ;549:   int32_t writeSize = 0;

                     497 ;550:   int64_t lwritestatus;

                     498 ;551:   int64_t consumed = 0;

                     499 ;552:   int64_t remaining = 0;

                     500 ;554:   if ( writestatus )

                     501 ;555:     *writestatus = 0;

                     502 ;557:   if ( ! zstream || ! zentry )

                     503 ;558:     return NULL;

                     504 ;560:   if ( entry )

                     505 ;561:     {

                     506 ;562:       /* Calculate, or continue calculation of, CRC32 */

                     507 ;563:       zentry->CRC32 = crc32 (zentry->CRC32, (uint8_t *)entry, (int32_t)entrySize);

                     508 ;565:       remaining = entrySize;

                     509 ;566:     }

                     510 ;568:   /* Call method callback for processing data until all input is consumed */

                     511 ;569:   while ( (writeSize = zentry->method->process( zstream, zentry,

                     512 ;570:                                                 entry, remaining, &consumed,

                     513 ;571:                                                 zstream->buffer,

                     514 ;572:                                                 sizeof(zstream->buffer)) ) > 0 )

                     515 ;573:     {

                     516 ;574:       /* Write processed data to stream */

                     517 ;575:       lwritestatus = zs_writedata (zstream, zstream->buffer, writeSize);

                     518 ;576:       if ( lwritestatus != writeSize )

                     519 ;577:         {

                     520 ;578:           fprintf (stderr, "zs_entrydata: Error writing ZIP entry data (%d): %s\n",

                     521 ;579:                    zstream->fd, strerror(errno));

                     522 ;581:           if ( writestatus )

                     523 ;582:             *writestatus = lwritestatus;

                     524 ;584:           return NULL;

                     525 ;585:         }

                     526 ;587:       zentry->CompressedSize += writeSize;

                     527 ;589:       if ( entry )

                     528 ;590:         {

                     529 ;591:           entry += consumed;

                     530 ;592:           remaining -= consumed;

                     531 ;594:           if ( remaining <= 0 )

                     532 ;595:             break;

                     533 ;596:         }

                     534 ;597:     }

                     535 ;599:   if ( writeSize < 0 )

                     536 ;600:     {

                     537 ;601:       fprintf (stderr, "zs_entrydata: Process callback failed\n");

                     538 ;602:       return NULL;


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                     539 ;603:     }

                     540 ;605:   if ( entry )

                     541 ;606:     {

                     542 ;607:       zentry->UncompressedSize += entrySize;

                     543 ;608:     }

                     544 ;610:   return zentry;

                     545 ;611: }  /* End of zs_entrydata() */

                     546 ;614: /***************************************************************************

                     547 ;615:  * zs_entryend:

                     548 ;616:  *

                     549 ;617:  * End a streaming entry by writing a Data Description record to

                     550 ;618:  * output stream.

                     551 ;619:  *

                     552 ;620:  * If specified, writestatus will be set to the output of write() when

                     553 ;621:  * a write error occurs, otherwise it will be set to 0.

                     554 ;622:  *

                     555 ;623:  * @return pointer to ZIPentry on success and NULL on error.

                     556 ;624:  ***************************************************************************/

                     557 ;625: ZIPentry *

                     558 ;626: zs_entryend ( ZIPstream *zstream, ZIPentry *zentry, int64_t *writestatus)

                     559 ;627: {

                     560 ;628:   int64_t lwritestatus;

                     561 ;629:   int32_t packed;

                     562 ;631:   if ( writestatus )

                     563 ;632:     *writestatus = 0;

                     564 ;634:   if ( ! zstream || ! zentry )

                     565 ;635:     return NULL;

                     566 ;637:   /* Flush the entry */

                     567 ;638:   if ( ! zs_entrydata (zstream, zentry, NULL, 0, writestatus) )

                     568 ;639:     {

                     569 ;640:       fprintf (stderr, "Error flushing entry (writestatus: %lld)\n",

                     570 ;641:                (long long int)*writestatus);

                     571 ;642:       return NULL;

                     572 ;643:     }

                     573 ;645:   /* Method finish callback */

                     574 ;646:   if ( zentry->method->finish &&

                     575 ;647:        zentry->method->finish (zstream, zentry) )

                     576 ;648:     {

                     577 ;649:       fprintf (stderr, "Error with method (%d) finish callback\n",

                     578 ;650:                zentry->method->ID);

                     579 ;651:       return NULL;

                     580 ;652:     }

                     581 ;654:   /* Write Data Description */

                     582 ;655:   packed = 0;

                     583 ;656:   zs_packunit32 (zstream, &packed, DATADESCRIPTIONSIG);       /* Data Description signature */

                     584 ;657:   zs_packunit32 (zstream, &packed, zentry->CRC32);            /* CRC-32 value of entry */

                     585 ;658:   zs_packunit32 (zstream, &packed, (int32_t)zentry->CompressedSize);   /* Compressed entry size */

                     586 ;659:   zs_packunit32 (zstream, &packed, (int32_t)zentry->UncompressedSize); /* Uncompressed entry size */

                     587 ;661:   lwritestatus = zs_writedata (zstream, zstream->buffer, packed);

                     588 ;662:   if ( lwritestatus != packed )

                     589 ;663:     {

                     590 ;664:       fprintf (stderr, "Error writing streaming ZIP data description: %s\n", strerror(errno));

                     591 ;666:       if ( writestatus )

                     592 ;667:         *writestatus = lwritestatus;

                     593 ;669:       return NULL;

                     594 ;670:     }

                     595 ;672:   return zentry;

                     596 ;673: }  /* End of zs_entryend() */

                     597 ;676: /***************************************************************************

                     598 ;677:  * zs_finish:

                     599 ;678:  *


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                     600 ;679:  * Write end of ZIP archive structures (Central Directory, etc.).

                     601 ;680:  *

                     602 ;681:  * ZIP64 structures will be added to the Central Directory when the

                     603 ;682:  * total length of the archive exceeds 0xFFFFFFFF bytes.

                     604 ;683:  *

                     605 ;684:  * If specified, writestatus will be set to the output of write() when

                     606 ;685:  * a write error occurs, otherwise it will be set to 0.

                     607 ;686:  *

                     608 ;687:  * @return 0 on success and non-zero on error.

                     609 ;688:  ***************************************************************************/

                     610 ;689: int

                     611 ;690: zs_finish ( ZIPstream *zstream, int64_t *writestatus )

                     612 ;691: {

                     613 ;692:   ZIPentry *zentry;

                     614 ;693:   int64_t lwritestatus;

                     615 ;694:   int packed;

                     616 ;696:   uint64_t cdsize;

                     617 ;697:   uint64_t zip64endrecord;

                     618 ;698:   int zip64 = 0;

                     619 ;700:   if ( writestatus )

                     620 ;701:     *writestatus = 0;

                     621 ;703:   if ( ! zstream )

                     622 ;704:     return -1;

                     623 ;706:   /* Store offset of Central Directory */

                     624 ;707:   zstream->CentralDirectoryOffset = zstream->WriteOffset;

                     625 ;709:   zentry = zstream->FirstEntry;

                     626 ;710:   while ( zentry )

                     627 ;711:     {

                     628 ;712:       zip64 = ( zentry->LocalHeaderOffset > 0xFFFFFFFF ) ? 1 : 0;

                     629 ;714:       /* Write Central Directory Header, packing into write buffer and swapped to little-endian order */

                     630 ;715:       packed = 0;

                     631 ;716:       zs_packunit32 (zstream, &packed, CENTRALHEADERSIG);    /* Central File Header signature */

                     632 ;717:       zs_packunit16 (zstream, &packed, 0);                   /* Version made by */

                     633 ;718:       zs_packunit16 (zstream, &packed, zentry->ZipVersion);  /* Version needed to extract */

                     634 ;719:       zs_packunit16 (zstream, &packed, zentry->GeneralFlag); /* General purpose bit flag */

                     635 ;720:       zs_packunit16 (zstream, &packed, zentry->CompressionMethod); /* Compression method */

                     636 ;721:       zs_packunit16 (zstream, &packed, zentry->DOSTime);     /* DOS file modification time */

                     637 ;722:       zs_packunit16 (zstream, &packed, zentry->DOSDate);     /* DOS file modification date */

                     638 ;723:       zs_packunit32 (zstream, &packed, zentry->CRC32);       /* CRC-32 value of entry */

                     639 ;724:       zs_packunit32 (zstream, &packed, (int32_t)zentry->CompressedSize); /* Compressed entry size */

                     640 ;725:       zs_packunit32 (zstream, &packed, (int32_t)zentry->UncompressedSize); /* Uncompressed entry size */

                     641 ;726:       zs_packunit16 (zstream, &packed, zentry->NameLength);  /* File/entry name length */

                     642 ;727:       zs_packunit16 (zstream, &packed, ( zip64 ) ? 12 : 0 ); /* Extra field length, switch for ZIP64 */

                     643 ;728:       zs_packunit16 (zstream, &packed, 0);                   /* File/entry comment length */

                     644 ;729:       zs_packunit16 (zstream, &packed, 0);                   /* Disk number start */

                     645 ;730:       zs_packunit16 (zstream, &packed, 0);                   /* Internal file attributes */

                     646 ;731:       zs_packunit32 (zstream, &packed, 0);                   /* External file attributes */

                     647 ;732:       zs_packunit32 (zstream, &packed, ( zip64 ) ?

                     648 ;733:                      0xFFFFFFFF : (int32_t)zentry->LocalHeaderOffset); /* Relative offset of Local Header */

                     649 ;735:       /* File/entry name */

                     650 ;736:       memcpy (zstream->buffer+packed, zentry->Name, zentry->NameLength);

                     651 ;737:       packed += zentry->NameLength;

                     652 ;739:       if ( zip64 )  /* ZIP64 Extra Field */

                     653 ;740:         {

                     654 ;741:           zs_packunit16 (zstream, &packed, 1);      /* Extra field ID, 1 = ZIP64 */

                     655 ;742:           zs_packunit16 (zstream, &packed, 8);      /* Extra field data length */

                     656 ;743:           zs_packunit64 (zstream, &packed, zentry->LocalHeaderOffset); /* Offset to Local Header */

                     657 ;744:         }

                     658 ;746:       lwritestatus = zs_writedata (zstream, zstream->buffer, packed);

                     659 ;747:       if ( lwritestatus != packed )

                     660 ;748:         {


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                     661 ;749:           fprintf (stderr, "Error writing ZIP central directory header: %s\n", strerror(errno));

                     662 ;751:           if ( writestatus )

                     663 ;752:             *writestatus = lwritestatus;

                     664 ;754:           return -1;

                     665 ;755:         }

                     666 ;757:       zentry = zentry->next;

                     667 ;758:     }

                     668 ;760:   /* Calculate size of Central Directory */

                     669 ;761:   cdsize = zstream->WriteOffset - zstream->CentralDirectoryOffset;

                     670 ;763:   /* Add ZIP64 structures if offset to Central Directory is beyond limit */

                     671 ;764:   if ( zstream->CentralDirectoryOffset > 0xFFFFFFFF )

                     672 ;765:     {

                     673 ;766:       /* Note offset of ZIP64 End of Central Directory Record */

                     674 ;767:       zip64endrecord = zstream->WriteOffset;

                     675 ;769:       /* Write ZIP64 End of Central Directory Record, packing into write buffer and swapped to little-endian order */

                     676 ;770:       packed = 0;

                     677 ;771:       zs_packunit32 (zstream, &packed, ZIP64ENDRECORDSIG); /* ZIP64 End of Central Dir record */

                     678 ;772:       zs_packunit64 (zstream, &packed, 44);                /* Size of this record after this field */

                     679 ;773:       zs_packunit16 (zstream, &packed, 30);                /* Version made by */

                     680 ;774:       zs_packunit16 (zstream, &packed, 45);                /* Version needed to extract */

                     681 ;775:       zs_packunit32 (zstream, &packed, 0);                 /* Number of this disk */

                     682 ;776:       zs_packunit32 (zstream, &packed, 0);                 /* Disk with start of the CD */

                     683 ;777:       zs_packunit64 (zstream, &packed, zstream->EntryCount); /* Number of CD entries on this disk */

                     684 ;778:       zs_packunit64 (zstream, &packed, zstream->EntryCount); /* Total number of CD entries */

                     685 ;779:       zs_packunit64 (zstream, &packed, cdsize);            /* Size of Central Directory */

                     686 ;780:       zs_packunit64 (zstream, &packed, zstream->CentralDirectoryOffset); /* Offset to Central Directory */

                     687 ;782:       lwritestatus = zs_writedata (zstream, zstream->buffer, packed);

                     688 ;783:       if ( lwritestatus != packed )

                     689 ;784:         {

                     690 ;785:           fprintf (stderr, "Error writing ZIP64 end of central directory record: %s\n", strerror(errno));

                     691 ;787:           if ( writestatus )

                     692 ;788:             *writestatus = lwritestatus;

                     693 ;790:           return -1;

                     694 ;791:         }

                     695 ;793:       /* Write ZIP64 End of Central Directory Locator, packing into write buffer and swapped to little-endian order */

                     696 ;794:       packed = 0;

                     697 ;795:       zs_packunit32 (zstream, &packed, ZIP64ENDLOCATORSIG); /* ZIP64 End of Central Dir Locator */

                     698 ;796:       zs_packunit32 (zstream, &packed, 0);                  /* Number of disk w/ ZIP64 End of CD */

                     699 ;797:       zs_packunit64 (zstream, &packed, zip64endrecord);     /* Offset to ZIP64 End of CD */

                     700 ;798:       zs_packunit32 (zstream, &packed, 1);                  /* Total number of disks */

                     701 ;800:       lwritestatus = zs_writedata (zstream, zstream->buffer, packed);

                     702 ;801:       if ( lwritestatus != packed )

                     703 ;802:         {

                     704 ;803:           fprintf (stderr, "Error writing ZIP64 end of central directory locator: %s\n", strerror(errno));

                     705 ;805:           if ( writestatus )

                     706 ;806:             *writestatus = lwritestatus;

                     707 ;808:           return -1;

                     708 ;809:         }

                     709 ;810:     }

                     710 ;812:   /* Write End of Central Directory Record, packing into write buffer and swapped to little-endian order */

                     711 ;813:   packed = 0;

                     712 ;814:   zs_packunit32 (zstream, &packed, ENDHEADERSIG);     /* End of Central Dir signature */

                     713 ;815:   zs_packunit16 (zstream, &packed, 0);                /* Number of this disk */

                     714 ;816:   zs_packunit16 (zstream, &packed, 0);                /* Number of disk with CD */

                     715 ;817:   zs_packunit16 (zstream, &packed, zstream->EntryCount); /* Number of entries in CD this disk */

                     716 ;818:   zs_packunit16 (zstream, &packed, zstream->EntryCount); /* Number of entries in CD */

                     717 ;819:   zs_packunit32 (zstream, &packed, (int32_t)cdsize);           /* Size of Central Directory */

                     718 ;820:   zs_packunit32 (zstream, &packed, (zstream->CentralDirectoryOffset > 0xFFFFFFFF) ?

                     719 ;821:                  0xFFFFFFFF : (int32_t)zstream->CentralDirectoryOffset); /* Offset to start of CD */

                     720 ;822:   zs_packunit16 (zstream, &packed, 0);                /* ZIP file comment length */

                     721 ;824:   lwritestatus = zs_writedata (zstream, zstream->buffer, packed);


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                     722 ;825:   if ( lwritestatus != packed )

                     723 ;826:     {

                     724 ;827:       fprintf (stderr, "Error writing end of central directory record: %s\n", strerror(errno));

                     725 ;829:       if ( writestatus )

                     726 ;830:         *writestatus = lwritestatus;

                     727 ;832:       return -1;

                     728 ;833:     }

                     729 ;835:   return 0;

                     730 ;836: }  /* End of zs_finish() */

                     731 ;839: /***************************************************************************

                     732 ;840:  * zs_writedata:

                     733 ;841:  *

                     734 ;842:  * Write data to output descriptor in blocks of ZS_WRITE_SIZE bytes.

                     735 ;843:  *

                     736 ;844:  * The ZIPstream.WriteOffset value will be incremented accordingly.

                     737 ;845:  *

                     738 ;846:  * @return number of bytes written on success and return value of write() on error.

                     739 ;847:  ***************************************************************************/

                     740 ;848: static int64_t

                     741 ;849: zs_writedata ( ZIPstream *zstream, uint8_t *writeBuffer, int64_t writeBufferSize )

                     742 ;850: {

                     743 ;851:   int64_t lwritestatus;

                     744 ;852:   size_t writeLength;

                     745 ;853:   int64_t written;

                     746 ;855:   if ( ! zstream || ! writeBuffer )

                     747 ;856:     return 0;

                     748 ;858:   /* Write blocks of ZS_WRITE_SIZE until done */

                     749 ;859:   written = 0;

                     750 ;860:   while ( written < writeBufferSize )

                     751 ;861:     {

                     752 ;862:       writeLength = (int32_t)( (writeBufferSize - (int32_t)written) > ZS_WRITE_SIZE ) ?

                     753 ;863:         ZS_WRITE_SIZE : ((int32_t)writeBufferSize - (int32_t)written);

                     754 ;865:       lwritestatus = zs_user_writeToStream (zstream->fd, writeBuffer+written, writeLength);

                     755 ;867:       if ( lwritestatus <= 0 )

                     756 ;868:         {

                     757 ;869:           return lwritestatus;

                     758 ;870:         }

                     759 ;872:       zstream->WriteOffset += lwritestatus;

                     760 ;873:       written += lwritestatus;

                     761 ;874:     }

                     762 ;876:   return written;

                     763 ;877: }  /* End of zs_writedata() */

                     764 ;880: /* DOS time start date is January 1, 1980 */

                     765 ;881: #define DOSTIME_STARTDATE  0x00210000L

                     766 ;883: /***************************************************************************

                     767 ;884:  * zs_datetime_unixtodos:

                     768 ;885:  *

                     769 ;886:  * Convert Unix time_t to 4 byte DOS date and time.

                     770 ;887:  *

                     771 ;888:  * Routine adapted from sources:

                     772 ;889:  *  Copyright (C) 2006 Michael Liebscher <<EMAIL>>

                     773 ;890:  *

                     774 ;891:  * @return converted 4-byte quantity on success and 0 on error.

                     775 ;892:  ***************************************************************************/

                     776 ;893: static uint32_t zs_datetime_unixtodos ( time_t t )

                     777 

                     778 ;901:            (((uint32_t)(s.tm_year) - 1980) << 25) |

                     779 ;902:            ((uint32_t)(s.tm_mon) << 21) |

                     780 ;903:            ((uint32_t)(s.tm_mday) << 16) |

                     781 ;904:            ((uint32_t)(s.tm_hour) << 11) |

                     782 ;905:            ((uint32_t)(s.tm_min) << 5) |


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                     783 ;906:            ((uint32_t)(s.tm_sec) >> 1) );

                     784 ;907: }

                     785 

                     786 	.text

                     787 	.align	4

                     788 allocMemory:

00000000 e92d4030    789 	stmfd	[sp]!,{r4-r5,lr}

00000004 e1a05000    790 	mov	r5,r0

00000008 eb000000*   791 	bl	OscFiles_malloc

0000000c e1b04000    792 	movs	r4,r0

00000010 11a02005    793 	movne	r2,r5

00000014 13a01000    794 	movne	r1,0

00000018 1b000000*   795 	blne	memset

0000001c e1a00004    796 	mov	r0,r4

00000020 e8bd4030    797 	ldmfd	[sp]!,{r4-r5,lr}

00000024 e12fff1e*   798 	ret	

                     799 	.endf	allocMemory

                     800 	.align	4

                     801 ;p	r4	local

                     802 

                     803 ;elsize	r5	param

                     804 

                     805 	.section ".bss","awb"

                     806 .L67:

                     807 	.data

                     808 	.text

                     809 

                     810 

                     811 	.align	4

                     812 	.align	4

                     813 freeMemory::

00000028 ea000000*   814 	b	OscFiles_free

                     815 	.endf	freeMemory

                     816 	.align	4

                     817 

                     818 ;p	none	param

                     819 

                     820 	.section ".bss","awb"

                     821 .L110:

                     822 	.data

                     823 	.text

                     824 

                     825 

                     826 	.align	4

                     827 	.align	4

                     828 zs_store_process:

0000002c e1a0c00d    829 	mov	r12,sp

00000030 e92d0008    830 	stmfd	[sp]!,{r3}

00000034 e92d5730    831 	stmfd	[sp]!,{r4-r5,r8-r10,r12,lr}

00000038 e1b0c002    832 	movs	r12,r2

0000003c e59d4024    833 	ldr	r4,[sp,36]

00000040 e59d5028    834 	ldr	r5,[sp,40]

00000044 0a000004    835 	beq	.L120

00000048 e59d201c    836 	ldr	r2,[sp,28]

0000004c e59d1020    837 	ldr	r1,[sp,32]

00000050 e3520001    838 	cmp	r2,1

00000054 e2d10000    839 	sbcs	r0,r1,0

00000058 aa000001    840 	bge	.L119

                     841 .L120:

0000005c e3a00000    842 	mov	r0,0

00000060 ea000012    843 	b	.L117


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                     844 .L119:

00000064 e1a00002    845 	mov	r0,r2

00000068 e59d202c    846 	ldr	r2,[sp,44]

0000006c e59d3030    847 	ldr	r3,[sp,48]

00000070 e1500002    848 	cmp	r0,r2

00000074 e0d10003    849 	sbcs	r0,r1,r3

00000078 b28d001c    850 	addlt	r0,sp,28

0000007c b8900600    851 	ldmltfd	[r0],{r9-r10}

00000080 b28d002c    852 	addlt	r0,sp,44

00000084 b8800600    853 	stmltea	[r0],{r9-r10}

00000088 b59d202c    854 	ldrlt	r2,[sp,44]

0000008c e1a0100c    855 	mov	r1,r12

00000090 e1a00005    856 	mov	r0,r5

00000094 eb000000*   857 	bl	memcpy

00000098 e3540000    858 	cmp	r4,0

0000009c 159d002c    859 	ldrne	r0,[sp,44]

000000a0 15840000    860 	strne	r0,[r4,0]

000000a4 159d0030    861 	ldrne	r0,[sp,48]

000000a8 15840004    862 	strne	r0,[r4,4]

000000ac e59d002c    863 	ldr	r0,[sp,44]

                     864 .L117:

000000b0 e89d6730    865 	ldmfd	[sp],{r4-r5,r8-r10,sp-lr}

000000b4 e12fff1e*   866 	ret	

                     867 	.endf	zs_store_process

                     868 	.align	4

                     869 

                     870 ;zstream	none	param

                     871 ;zentry	none	param

                     872 ;entry	r12	param

                     873 ;entrySize	[sp,28]	param

                     874 ;entryConsumed	r4	param

                     875 ;writeBuffer	r5	param

                     876 ;writeBufferSize	[sp,44]	param

                     877 

                     878 	.section ".bss","awb"

                     879 .L188:

                     880 	.data

                     881 	.text

                     882 

                     883 

                     884 	.align	4

                     885 	.align	4

                     886 zs_registermethod::

000000b8 e92d44f0    887 	stmfd	[sp]!,{r4-r7,r10,lr}

000000bc e1a04001    888 	mov	r4,r1

000000c0 e1a07002    889 	mov	r7,r2

000000c4 e1b06003    890 	movs	r6,r3

000000c8 e59da018    891 	ldr	r10,[sp,24]

000000cc e1a05000    892 	mov	r5,r0

000000d0 e5950020    893 	ldr	r0,[r5,32]

000000d4 0a00000a    894 	beq	.L226

000000d8 e3500000    895 	cmp	r0,0

000000dc 0a000005    896 	beq	.L220

                     897 .L221:

000000e0 e5901000    898 	ldr	r1,[r0]

000000e4 e1510004    899 	cmp	r1,r4

000000e8 0a000005    900 	beq	.L226

000000ec e5900010    901 	ldr	r0,[r0,16]

000000f0 e3500000    902 	cmp	r0,0

000000f4 1afffff9    903 	bne	.L221

                     904 .L220:


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
000000f8 e3a00014    905 	mov	r0,20

000000fc ebffffbf*   906 	bl	allocMemory

00000100 e3500000    907 	cmp	r0,0

                     908 .L226:

00000104 03a00000    909 	moveq	r0,0

00000108 0a000006    910 	beq	.L214

                     911 .L225:

0000010c e1a0c006    912 	mov	r12,r6

00000110 e1a06007    913 	mov	r6,r7

00000114 e1a0700c    914 	mov	r7,r12

00000118 e88004d0    915 	stmea	[r0],{r4,r6-r7,r10}

0000011c e5951020    916 	ldr	r1,[r5,32]

00000120 e5801010    917 	str	r1,[r0,16]

00000124 e5850020    918 	str	r0,[r5,32]

                     919 .L214:

00000128 e8bd84f0    920 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     921 	.endf	zs_registermethod

                     922 	.align	4

                     923 ;method	r0	local

                     924 

                     925 ;zs	r5	param

                     926 ;methodID	r4	param

                     927 ;init	r7	param

                     928 ;process	r6	param

                     929 ;finish	r10	param

                     930 

                     931 	.section ".bss","awb"

                     932 .L324:

                     933 	.data

                     934 	.text

                     935 

                     936 

                     937 	.align	4

                     938 	.align	4

                     939 zs_init::

0000012c e92d4070    940 	stmfd	[sp]!,{r4-r6,lr}

00000130 e24dd004    941 	sub	sp,sp,4

00000134 e1a06000    942 	mov	r6,r0

00000138 e1b04001    943 	movs	r4,r1

0000013c 1a000005    944 	bne	.L346

00000140 e3a00c40    945 	mov	r0,1<<14

00000144 e2800024    946 	add	r0,r0,36

00000148 ebffffac*   947 	bl	allocMemory

0000014c e1b04000    948 	movs	r4,r0

00000150 1a000012    949 	bne	.L355

00000154 ea000023    950 	b	.L358

                     951 .L346:

00000158 e5945018    952 	ldr	r5,[r4,24]

0000015c e3550000    953 	cmp	r5,0

00000160 0a000004    954 	beq	.L350

                     955 .L351:

00000164 e1a00005    956 	mov	r0,r5

00000168 e5955134    957 	ldr	r5,[r5,308]

0000016c ebffffad*   958 	bl	freeMemory

00000170 e3550000    959 	cmp	r5,0

00000174 1afffffa    960 	bne	.L351

                     961 .L350:

00000178 e5945020    962 	ldr	r5,[r4,32]

0000017c e3550000    963 	cmp	r5,0

00000180 0a000004    964 	beq	.L348

                     965 .L354:


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
00000184 e1a00005    966 	mov	r0,r5

00000188 e5955010    967 	ldr	r5,[r5,16]

0000018c ebffffa5*   968 	bl	freeMemory

00000190 e3550000    969 	cmp	r5,0

00000194 1afffffa    970 	bne	.L354

                     971 .L348:

00000198 e3540000    972 	cmp	r4,0

0000019c 0a000011    973 	beq	.L358

                     974 .L355:

000001a0 e3a02c40    975 	mov	r2,1<<14

000001a4 e2822024    976 	add	r2,r2,36

000001a8 e1a00004    977 	mov	r0,r4

000001ac e3a01000    978 	mov	r1,0

000001b0 eb000000*   979 	bl	memset

000001b4 e5846000    980 	str	r6,[r4]

000001b8 e3a02000    981 	mov	r2,0

000001bc e58d2000    982 	str	r2,[sp]

000001c0 e59f3d80*   983 	ldr	r3,.L504

000001c4 e1a00004    984 	mov	r0,r4

000001c8 e1a01002    985 	mov	r1,r2

000001cc ebffffb9*   986 	bl	zs_registermethod

000001d0 e3500000    987 	cmp	r0,0

000001d4 1a000003    988 	bne	.L358

000001d8 e1a00004    989 	mov	r0,r4

000001dc ebffff91*   990 	bl	freeMemory

000001e0 e3a00000    991 	mov	r0,0

000001e4 ea000000    992 	b	.L344

                     993 .L358:

000001e8 e1a00004    994 	mov	r0,r4

                     995 .L344:

000001ec e28dd004    996 	add	sp,sp,4

000001f0 e8bd8070    997 	ldmfd	[sp]!,{r4-r6,pc}

                     998 	.endf	zs_init

                     999 	.align	4

                    1000 ;zentry	r5	local

                    1001 ;method	r5	local

                    1002 

                    1003 ;fd	r6	param

                    1004 ;zs	r4	param

                    1005 

                    1006 	.section ".bss","awb"

                    1007 .L477:

                    1008 	.data

                    1009 	.text

                    1010 

                    1011 

                    1012 	.align	4

                    1013 	.align	4

                    1014 zs_free::

000001f4 e92d4030   1015 	stmfd	[sp]!,{r4-r5,lr}

000001f8 e1b05000   1016 	movs	r5,r0

000001fc 0a000012   1017 	beq	.L505

00000200 e5954018   1018 	ldr	r4,[r5,24]

00000204 e3540000   1019 	cmp	r4,0

00000208 0a000004   1020 	beq	.L511

                    1021 .L512:

0000020c e1a00004   1022 	mov	r0,r4

00000210 e5944134   1023 	ldr	r4,[r4,308]

00000214 ebffff83*  1024 	bl	freeMemory

00000218 e3540000   1025 	cmp	r4,0

0000021c 1afffffa   1026 	bne	.L512


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                    1027 .L511:

00000220 e5954020   1028 	ldr	r4,[r5,32]

00000224 e3540000   1029 	cmp	r4,0

00000228 0a000004   1030 	beq	.L514

                    1031 .L515:

0000022c e1a00004   1032 	mov	r0,r4

00000230 e5944010   1033 	ldr	r4,[r4,16]

00000234 ebffff7b*  1034 	bl	freeMemory

00000238 e3540000   1035 	cmp	r4,0

0000023c 1afffffa   1036 	bne	.L515

                    1037 .L514:

00000240 e1a00005   1038 	mov	r0,r5

00000244 e8bd4030   1039 	ldmfd	[sp]!,{r4-r5,lr}

00000248 eaffff76*  1040 	b	freeMemory

                    1041 .L505:

0000024c e8bd8030   1042 	ldmfd	[sp]!,{r4-r5,pc}

                    1043 	.endf	zs_free

                    1044 	.align	4

                    1045 ;zentry	r4	local

                    1046 ;method	r4	local

                    1047 

                    1048 ;zs	r5	param

                    1049 

                    1050 	.section ".bss","awb"

                    1051 .L582:

                    1052 	.data

                    1053 	.text

                    1054 

                    1055 

                    1056 	.align	4

                    1057 	.align	4

                    1058 zs_writeentry::

00000250 e92d48f0   1059 	stmfd	[sp]!,{r4-r7,fp,lr}

00000254 e24dd010   1060 	sub	sp,sp,16

00000258 e58d2008   1061 	str	r2,[sp,8]

0000025c e58d300c   1062 	str	r3,[sp,12]

00000260 e59d7028   1063 	ldr	r7,[sp,40]

00000264 e59dc02c   1064 	ldr	r12,[sp,44]

00000268 e59d6030   1065 	ldr	r6,[sp,48]

0000026c e1a0b001   1066 	mov	fp,r1

00000270 e59d4034   1067 	ldr	r4,[sp,52]

00000274 e1a05000   1068 	mov	r5,r0

00000278 e3540000   1069 	cmp	r4,0

0000027c 13a00000   1070 	movne	r0,0

00000280 11a01000   1071 	movne	r1,r0

00000284 18840003   1072 	stmneea	[r4],{r0-r1}

00000288 e3550000   1073 	cmp	r5,0

0000028c 0a000020   1074 	beq	.L618

00000290 e3a01000   1075 	mov	r1,0

00000294 e3e00000   1076 	mvn	r0,0

00000298 e59d2008   1077 	ldr	r2,[sp,8]

0000029c e59d300c   1078 	ldr	r3,[sp,12]

000002a0 e1500002   1079 	cmp	r0,r2

000002a4 e0d10003   1080 	sbcs	r0,r1,r3

000002a8 ba000019   1081 	blt	.L618

000002ac e58d4000   1082 	str	r4,[sp]

000002b0 e1a03006   1083 	mov	r3,r6

000002b4 e1a0200c   1084 	mov	r2,r12

000002b8 e1a01007   1085 	mov	r1,r7

000002bc e1a00005   1086 	mov	r0,r5

000002c0 eb000016*  1087 	bl	zs_entrybegin


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
000002c4 e1b06000   1088 	movs	r6,r0

000002c8 0a000011   1089 	beq	.L618

000002cc e24dd004   1090 	sub	sp,sp,4

000002d0 e59d2010   1091 	ldr	r2,[sp,16]

000002d4 e59d100c   1092 	ldr	r1,[sp,12]

000002d8 e88d0016   1093 	stmea	[sp],{r1-r2,r4}

000002dc e8bd0008   1094 	ldmfd	[sp]!,{r3}

000002e0 e1a0200b   1095 	mov	r2,fp

000002e4 e1a01006   1096 	mov	r1,r6

000002e8 e1a00005   1097 	mov	r0,r5

000002ec eb0000c8*  1098 	bl	zs_entrydata

000002f0 e3500000   1099 	cmp	r0,0

000002f4 0a000006   1100 	beq	.L618

000002f8 e1a02004   1101 	mov	r2,r4

000002fc e1a01006   1102 	mov	r1,r6

00000300 e1a00005   1103 	mov	r0,r5

00000304 eb00012b*  1104 	bl	zs_entryend

00000308 e3500000   1105 	cmp	r0,0

0000030c 11a00006   1106 	movne	r0,r6

00000310 1a000000   1107 	bne	.L601

                    1108 .L618:

00000314 e3a00000   1109 	mov	r0,0

                    1110 .L601:

00000318 e28dd010   1111 	add	sp,sp,16

0000031c e8bd88f0   1112 	ldmfd	[sp]!,{r4-r7,fp,pc}

                    1113 	.endf	zs_writeentry

                    1114 	.align	4

                    1115 ;zentry	r6	local

                    1116 

                    1117 ;zstream	r5	param

                    1118 ;entry	fp	param

                    1119 ;entrySize	[sp,8]	param

                    1120 ;name	r7	param

                    1121 ;modtime	r12	param

                    1122 ;methodID	r6	param

                    1123 ;writestatus	r4	param

                    1124 

                    1125 	.section ".bss","awb"

                    1126 .L712:

                    1127 	.data

                    1128 	.text

                    1129 

                    1130 

                    1131 	.align	4

                    1132 	.align	4

                    1133 zs_entrybegin::

00000320 e92d48f6   1134 	stmfd	[sp]!,{r1-r2,r4-r7,fp,lr}

00000324 e24dd02c   1135 	sub	sp,sp,44

00000328 e58d2030   1136 	str	r2,[sp,48]

0000032c e1a07003   1137 	mov	r7,r3

00000330 e58d102c   1138 	str	r1,[sp,44]

00000334 e59db04c   1139 	ldr	fp,[sp,76]

00000338 e1a06000   1140 	mov	r6,r0

0000033c e35b0000   1141 	cmp	fp,0

00000340 13a00000   1142 	movne	r0,0

00000344 11a01000   1143 	movne	r1,r0

00000348 188b0003   1144 	stmneea	[fp],{r0-r1}

0000034c e3560000   1145 	cmp	r6,0

00000350 159d002c   1146 	ldrne	r0,[sp,44]

00000354 13500000   1147 	cmpne	r0,0

00000358 15965020   1148 	ldrne	r5,[r6,32]


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
0000035c 13550000   1149 	cmpne	r5,0

00000360 0a0000a8   1150 	beq	.L782

                    1151 .L752:

00000364 e5950000   1152 	ldr	r0,[r5]

00000368 e1500007   1153 	cmp	r0,r7

0000036c 0a000003   1154 	beq	.L757

00000370 e5955010   1155 	ldr	r5,[r5,16]

00000374 e3550000   1156 	cmp	r5,0

00000378 1afffff9   1157 	bne	.L752

0000037c ea0000a1   1158 	b	.L782

                    1159 .L757:

00000380 e3a00f4e   1160 	mov	r0,0x0138

00000384 ebffff1d*  1161 	bl	allocMemory

00000388 e1b04000   1162 	movs	r4,r0

0000038c 0a00009d   1163 	beq	.L782

00000390 e3a00014   1164 	mov	r0,20

00000394 e1c400b0   1165 	strh	r0,[r4]

00000398 e3a00000   1166 	mov	r0,0

0000039c e1c400b2   1167 	strh	r0,[r4,2]

000003a0 e59d0030   1168 	ldr	r0,[sp,48]

000003a4 e28d1004   1169 	add	r1,sp,4

000003a8 e58d0004   1170 	str	r0,[sp,4]

                    1171 ;894: {

                    1172 

                    1173 ;895:   struct tm s;

                    1174 ;896:   TimeTools_gmtime32(&s, (__time32_t*)&t);

                    1175 

000003ac e28d0008   1176 	add	r0,sp,8

000003b0 eb000000*  1177 	bl	TimeTools_gmtime32

                    1178 ;897:   s.tm_year += 1900;

                    1179 

000003b4 e59d001c   1180 	ldr	r0,[sp,28]

000003b8 e3a0ce70   1181 	mov	r12,7<<8

000003bc e2803e70   1182 	add	r3,r0,7<<8

000003c0 e283006c   1183 	add	r0,r3,108

000003c4 e59d3018   1184 	ldr	r3,[sp,24]

000003c8 e58d001c   1185 	str	r0,[sp,28]

                    1186 ;898:   s.tm_mon += 1;

                    1187 

000003cc e2833001   1188 	add	r3,r3,1

000003d0 e58d3018   1189 	str	r3,[sp,24]

                    1190 ;900:   return ( ((s.tm_year) < 1980) ? DOSTIME_STARTDATE :

                    1191 

000003d4 e28cc0bc   1192 	add	r12,r12,188

000003d8 e150000c   1193 	cmp	r0,r12

000003dc b3a00984   1194 	movlt	r0,33<<16

000003e0 ba000008   1195 	blt	.L772

000003e4 e1a00c80   1196 	mov	r0,r0 lsl 25

000003e8 e2400478   1197 	sub	r0,r0,15<<27

000003ec e1800a83   1198 	orr	r0,r0,r3 lsl 21

000003f0 e28d3008   1199 	add	r3,sp,8

000003f4 e8935006   1200 	ldmfd	[r3],{r1-r2,r12,lr}

000003f8 e180080e   1201 	orr	r0,r0,lr lsl 16

000003fc e180058c   1202 	orr	r0,r0,r12 lsl 11

00000400 e1800282   1203 	orr	r0,r0,r2 lsl 5

00000404 e18000a1   1204 	orr	r0,r0,r1 lsr 1

                    1205 .L772:

00000408 e1c470b4   1206 	strh	r7,[r4,4]

0000040c e1a03820   1207 	mov	r3,r0 lsr 16

00000410 e1c430b6   1208 	strh	r3,[r4,6]

00000414 e3a02000   1209 	mov	r2,0


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
00000418 e1a01002   1210 	mov	r1,r2

0000041c e1c400b8   1211 	strh	r0,[r4,8]

00000420 e1a00002   1212 	mov	r0,r2

00000424 eb000000*  1213 	bl	crc32

00000428 e3a02000   1214 	mov	r2,0

0000042c e1a03002   1215 	mov	r3,r2

00000430 e1a07002   1216 	mov	r7,r2

00000434 e1a0c002   1217 	mov	r12,r2

00000438 e1a01000   1218 	mov	r1,r0

0000043c e2840008   1219 	add	r0,r4,8

00000440 e980108e   1220 	stmfa	[r0],{r1-r3,r7,r12}

00000444 e9960009   1221 	ldmed	[r6],{r0,r3}

00000448 e5840020   1222 	str	r0,[r4,32]

0000044c e5843024   1223 	str	r3,[r4,36]

00000450 e59d102c   1224 	ldr	r1,[sp,44]

00000454 e284002a   1225 	add	r0,r4,42

00000458 e3a020ff   1226 	mov	r2,255

0000045c eb000000*  1227 	bl	strncpy

00000460 e284002a   1228 	add	r0,r4,42

00000464 eb000000*  1229 	bl	strlen

00000468 e1c402b8   1230 	strh	r0,[r4,40]

0000046c e3a00000   1231 	mov	r0,0

00000470 e5840130   1232 	str	r0,[r4,304]

00000474 e5960018   1233 	ldr	r0,[r6,24]

00000478 e584512c   1234 	str	r5,[r4,300]

0000047c e3500000   1235 	cmp	r0,0

00000480 1a00000b   1236 	bne	.L773

00000484 e5960014   1237 	ldr	r0,[r6,20]

00000488 e5864018   1238 	str	r4,[r6,24]

0000048c e2800001   1239 	add	r0,r0,1

00000490 e5860014   1240 	str	r0,[r6,20]

00000494 e1d400b2   1241 	ldrh	r0,[r4,2]

00000498 e586401c   1242 	str	r4,[r6,28]

0000049c e3800008   1243 	orr	r0,r0,8

000004a0 e595c004   1244 	ldr	r12,[r5,4]

000004a4 e1c400b2   1245 	strh	r0,[r4,2]

000004a8 e35c0000   1246 	cmp	r12,0

000004ac 0a000012   1247 	beq	.L776

000004b0 ea00000b   1248 	b	.L778

                    1249 .L773:

000004b4 e596001c   1250 	ldr	r0,[r6,28]

000004b8 e595c004   1251 	ldr	r12,[r5,4]

000004bc e5804134   1252 	str	r4,[r0,308]

000004c0 e5960014   1253 	ldr	r0,[r6,20]

000004c4 e586401c   1254 	str	r4,[r6,28]

000004c8 e2800001   1255 	add	r0,r0,1

000004cc e5860014   1256 	str	r0,[r6,20]

000004d0 e1d400b2   1257 	ldrh	r0,[r4,2]

000004d4 e35c0000   1258 	cmp	r12,0

000004d8 e3800008   1259 	orr	r0,r0,8

000004dc e1c400b2   1260 	strh	r0,[r4,2]

000004e0 0a000005   1261 	beq	.L776

                    1262 .L778:

000004e4 e1a01004   1263 	mov	r1,r4

000004e8 e1a00006   1264 	mov	r0,r6

000004ec e1a0e00f   1265 	mov	lr,pc

000004f0 e12fff1c*  1266 	bx	r12

000004f4 e3500000   1267 	cmp	r0,0

000004f8 1a000042   1268 	bne	.L782

                    1269 .L776:

000004fc e3a07000   1270 	mov	r7,0


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
00000500 e58d7000   1271 	str	r7,[sp]

00000504 e59f2a40*  1272 	ldr	r2,.L1156

00000508 e1a0100d   1273 	mov	r1,sp

0000050c e1a00006   1274 	mov	r0,r6

00000510 eb00027b*  1275 	bl	zs_packunit32

00000514 e1d420b0   1276 	ldrh	r2,[r4]

00000518 e1a0100d   1277 	mov	r1,sp

0000051c e1a00006   1278 	mov	r0,r6

00000520 eb000263*  1279 	bl	zs_packunit16

00000524 e1d420b2   1280 	ldrh	r2,[r4,2]

00000528 e1a0100d   1281 	mov	r1,sp

0000052c e1a00006   1282 	mov	r0,r6

00000530 eb00025f*  1283 	bl	zs_packunit16

00000534 e1d420b4   1284 	ldrh	r2,[r4,4]

00000538 e1a0100d   1285 	mov	r1,sp

0000053c e1a00006   1286 	mov	r0,r6

00000540 eb00025b*  1287 	bl	zs_packunit16

00000544 e1d420b8   1288 	ldrh	r2,[r4,8]

00000548 e1a0100d   1289 	mov	r1,sp

0000054c e1a00006   1290 	mov	r0,r6

00000550 eb000257*  1291 	bl	zs_packunit16

00000554 e1d420b6   1292 	ldrh	r2,[r4,6]

00000558 e1a0100d   1293 	mov	r1,sp

0000055c e1a00006   1294 	mov	r0,r6

00000560 eb000253*  1295 	bl	zs_packunit16

00000564 e594200c   1296 	ldr	r2,[r4,12]

00000568 e1a0100d   1297 	mov	r1,sp

0000056c e1a00006   1298 	mov	r0,r6

00000570 eb000263*  1299 	bl	zs_packunit32

00000574 e5942010   1300 	ldr	r2,[r4,16]

00000578 e1a0100d   1301 	mov	r1,sp

0000057c e1a00006   1302 	mov	r0,r6

00000580 eb00025f*  1303 	bl	zs_packunit32

00000584 e5942018   1304 	ldr	r2,[r4,24]

00000588 e1a0100d   1305 	mov	r1,sp

0000058c e1a00006   1306 	mov	r0,r6

00000590 eb00025b*  1307 	bl	zs_packunit32

00000594 e1d422b8   1308 	ldrh	r2,[r4,40]

00000598 e1a0100d   1309 	mov	r1,sp

0000059c e1a00006   1310 	mov	r0,r6

000005a0 eb000243*  1311 	bl	zs_packunit16

000005a4 e1a0100d   1312 	mov	r1,sp

000005a8 e1a00006   1313 	mov	r0,r6

000005ac e1a02007   1314 	mov	r2,r7

000005b0 eb00023f*  1315 	bl	zs_packunit16

000005b4 e1d422b8   1316 	ldrh	r2,[r4,40]

000005b8 e284102a   1317 	add	r1,r4,42

000005bc e59d0000   1318 	ldr	r0,[sp]

000005c0 e2865024   1319 	add	r5,r6,36

000005c4 e0800005   1320 	add	r0,r0,r5

000005c8 eb000000*  1321 	bl	memcpy

000005cc e1d432b8   1322 	ldrh	r3,[r4,40]

000005d0 e59d0000   1323 	ldr	r0,[sp]

000005d4 e1a01005   1324 	mov	r1,r5

000005d8 e0802003   1325 	add	r2,r0,r3

000005dc e58d2000   1326 	str	r2,[sp]

000005e0 e1a03fc2   1327 	mov	r3,r2 asr 31

000005e4 e1a00006   1328 	mov	r0,r6

000005e8 eb0001d0*  1329 	bl	zs_writedata

000005ec e59d3000   1330 	ldr	r3,[sp]

000005f0 e1510fc3   1331 	cmp	r1,r3 asr 31


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
000005f4 01500003   1332 	cmpeq	r0,r3

000005f8 01a00004   1333 	moveq	r0,r4

000005fc 0a000002   1334 	beq	.L742

00000600 e35b0000   1335 	cmp	fp,0

00000604 188b0003   1336 	stmneea	[fp],{r0-r1}

                    1337 .L782:

00000608 e3a00000   1338 	mov	r0,0

                    1339 .L742:

0000060c e28dd02c   1340 	add	sp,sp,44

00000610 e8bd88f6   1341 	ldmfd	[sp]!,{r1-r2,r4-r7,fp,pc}

                    1342 	.endf	zs_entrybegin

                    1343 	.align	4

                    1344 ;zentry	r4	local

                    1345 ;method	r5	local

                    1346 ;lwritestatus	r0	local

                    1347 ;packed	[sp]	local

                    1348 ;t	[sp,4]	local

                    1349 ;s	[sp,8]	local

                    1350 

                    1351 ;zstream	r6	param

                    1352 ;name	[sp,44]	param

                    1353 ;modtime	[sp,48]	param

                    1354 ;methodID	r7	param

                    1355 ;writestatus	fp	param

                    1356 

                    1357 	.section ".bss","awb"

                    1358 .L1096:

                    1359 	.data

                    1360 	.text

                    1361 

                    1362 

                    1363 	.align	4

                    1364 	.align	4

                    1365 zs_entrydata::

00000614 e1a0c00d   1366 	mov	r12,sp

00000618 e92d0008   1367 	stmfd	[sp]!,{r3}

0000061c e92d5ff0   1368 	stmfd	[sp]!,{r4-r12,lr}

00000620 e1a07001   1369 	mov	r7,r1

00000624 e3a01000   1370 	mov	r1,0

00000628 e1a03001   1371 	mov	r3,r1

0000062c e1a0c001   1372 	mov	r12,r1

00000630 e1a0b002   1373 	mov	fp,r2

00000634 e1a02001   1374 	mov	r2,r1

00000638 e24dd02c   1375 	sub	sp,sp,44

0000063c e28d5018   1376 	add	r5,sp,24

00000640 e885100f   1377 	stmea	[r5],{r0-r3,r12}

00000644 e59d005c   1378 	ldr	r0,[sp,92]

00000648 e3500000   1379 	cmp	r0,0

0000064c 13a02000   1380 	movne	r2,0

00000650 18800006   1381 	stmneea	[r0],{r1-r2}

00000654 e59d0018   1382 	ldr	r0,[sp,24]

00000658 e3500000   1383 	cmp	r0,0

0000065c 13570000   1384 	cmpne	r7,0

00000660 0a000045   1385 	beq	.L1182

00000664 e35b0000   1386 	cmp	fp,0

00000668 0a00002a   1387 	beq	.L1167

0000066c e59d2054   1388 	ldr	r2,[sp,84]

00000670 e597000c   1389 	ldr	r0,[r7,12]

00000674 e1a0100b   1390 	mov	r1,fp

00000678 eb000000*  1391 	bl	crc32

0000067c e587000c   1392 	str	r0,[r7,12]


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
00000680 e28d0054   1393 	add	r0,sp,84

00000684 e8900600   1394 	ldmfd	[r0],{r9-r10}

00000688 e28d001c   1395 	add	r0,sp,28

0000068c e8800600   1396 	stmea	[r0],{r9-r10}

00000690 ea000020   1397 	b	.L1167

                    1398 .L1169:

00000694 e1a02000   1399 	mov	r2,r0

00000698 e59d0018   1400 	ldr	r0,[sp,24]

0000069c e1a03fc2   1401 	mov	r3,r2 asr 31

000006a0 e2801024   1402 	add	r1,r0,36

000006a4 eb0001a1*  1403 	bl	zs_writedata

000006a8 e1a02004   1404 	mov	r2,r4

000006ac e1510fc2   1405 	cmp	r1,r2 asr 31

000006b0 01500002   1406 	cmpeq	r0,r2

000006b4 0a000005   1407 	beq	.L1170

000006b8 e59d205c   1408 	ldr	r2,[sp,92]

000006bc e3520000   1409 	cmp	r2,0

000006c0 18820003   1410 	stmneea	[r2],{r0-r1}

000006c4 13a00000   1411 	movne	r0,0

000006c8 1a000038   1412 	bne	.L1157

000006cc ea00002a   1413 	b	.L1182

                    1414 .L1170:

000006d0 e5970010   1415 	ldr	r0,[r7,16]

000006d4 e5971014   1416 	ldr	r1,[r7,20]

000006d8 e0923000   1417 	adds	r3,r2,r0

000006dc e5873010   1418 	str	r3,[r7,16]

000006e0 e0a14fc2   1419 	adc	r4,r1,r2 asr 31

000006e4 e5874014   1420 	str	r4,[r7,20]

000006e8 e35b0000   1421 	cmp	fp,0

000006ec 0a000009   1422 	beq	.L1167

000006f0 e28d101c   1423 	add	r1,sp,28

000006f4 e8910071   1424 	ldmfd	[r1],{r0,r4-r6}

000006f8 e095b00b   1425 	adds	fp,r5,fp

000006fc e0503005   1426 	subs	r3,r0,r5

00000700 e58d301c   1427 	str	r3,[sp,28]

00000704 e0c40006   1428 	sbc	r0,r4,r6

00000708 e58d0020   1429 	str	r0,[sp,32]

0000070c e3530001   1430 	cmp	r3,1

00000710 e2d01000   1431 	sbcs	r1,r0,0

00000714 ba000015   1432 	blt	.L1179

                    1433 .L1167:

00000718 e3a05c40   1434 	mov	r5,1<<14

0000071c e597112c   1435 	ldr	r1,[r7,300]

00000720 e3a06000   1436 	mov	r6,0

00000724 e591c008   1437 	ldr	r12,[r1,8]

00000728 e28d1018   1438 	add	r1,sp,24

0000072c e8910105   1439 	ldmfd	[r1],{r0,r2,r8}

00000730 e2804024   1440 	add	r4,r0,36

00000734 e28d3024   1441 	add	r3,sp,36

00000738 e24dd004   1442 	sub	sp,sp,4

0000073c e1a01002   1443 	mov	r1,r2

00000740 e1a02008   1444 	mov	r2,r8

00000744 e88d007e   1445 	stmea	[sp],{r1-r6}

00000748 e8bd0008   1446 	ldmfd	[sp]!,{r3}

0000074c e1a0200b   1447 	mov	r2,fp

00000750 e59d0018   1448 	ldr	r0,[sp,24]

00000754 e1a01007   1449 	mov	r1,r7

00000758 e1a0e00f   1450 	mov	lr,pc

0000075c e12fff1c*  1451 	bx	r12

00000760 e58d0014   1452 	str	r0,[sp,20]

00000764 e1a04000   1453 	mov	r4,r0


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
00000768 e3500000   1454 	cmp	r0,0

0000076c caffffc8   1455 	bgt	.L1169

                    1456 .L1179:

00000770 e59d0014   1457 	ldr	r0,[sp,20]

00000774 e3500000   1458 	cmp	r0,0

00000778 aa000001   1459 	bge	.L1181

                    1460 .L1182:

0000077c e3a00000   1461 	mov	r0,0

00000780 ea00000a   1462 	b	.L1157

                    1463 .L1181:

00000784 e35b0000   1464 	cmp	fp,0

00000788 0a000007   1465 	beq	.L1184

0000078c e59d1054   1466 	ldr	r1,[sp,84]

00000790 e5973018   1467 	ldr	r3,[r7,24]

00000794 e597001c   1468 	ldr	r0,[r7,28]

00000798 e0911003   1469 	adds	r1,r1,r3

0000079c e59dc058   1470 	ldr	r12,[sp,88]

000007a0 e5871018   1471 	str	r1,[r7,24]

000007a4 e0ac0000   1472 	adc	r0,r12,r0

000007a8 e587001c   1473 	str	r0,[r7,28]

                    1474 .L1184:

000007ac e1a00007   1475 	mov	r0,r7

                    1476 .L1157:

000007b0 e28dd02c   1477 	add	sp,sp,44

000007b4 e89daff0   1478 	ldmfd	[sp],{r4-fp,sp,pc}

                    1479 	.endf	zs_entrydata

                    1480 	.align	4

                    1481 ;writeSize	[sp,20]	local

                    1482 ;lwritestatus	r0	local

                    1483 ;consumed	[sp,36]	local

                    1484 ;remaining	[sp,28]	local

                    1485 

                    1486 ;zstream	[sp,24]	param

                    1487 ;zentry	r7	param

                    1488 ;entry	fp	param

                    1489 ;entrySize	[sp,84]	param

                    1490 ;writestatus	[sp,92]	param

                    1491 

                    1492 	.section ".bss","awb"

                    1493 .L1342:

                    1494 	.data

                    1495 	.text

                    1496 

                    1497 

                    1498 	.align	4

                    1499 	.align	4

                    1500 zs_entryend::

000007b8 e92d4070   1501 	stmfd	[sp]!,{r4-r6,lr}

000007bc e24dd00c   1502 	sub	sp,sp,12

000007c0 e1a04001   1503 	mov	r4,r1

000007c4 e1a06000   1504 	mov	r6,r0

000007c8 e1b05002   1505 	movs	r5,r2

000007cc 13a00000   1506 	movne	r0,0

000007d0 11a01000   1507 	movne	r1,r0

000007d4 18850003   1508 	stmneea	[r5],{r0-r1}

000007d8 e3560000   1509 	cmp	r6,0

000007dc 13540000   1510 	cmpne	r4,0

000007e0 0a000031   1511 	beq	.L1411

000007e4 e3a00000   1512 	mov	r0,0

000007e8 e52d0004   1513 	str	r0,[sp,-4]!

000007ec e98d0021   1514 	stmfa	[sp],{r0,r5}


                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
000007f0 e8bd0008   1515 	ldmfd	[sp]!,{r3}

000007f4 e1a01004   1516 	mov	r1,r4

000007f8 e1a00006   1517 	mov	r0,r6

000007fc e3a02000   1518 	mov	r2,0

00000800 ebffff83*  1519 	bl	zs_entrydata

00000804 e3500000   1520 	cmp	r0,0

00000808 0a000027   1521 	beq	.L1411

0000080c e594012c   1522 	ldr	r0,[r4,300]

00000810 e590c00c   1523 	ldr	r12,[r0,12]

00000814 e35c0000   1524 	cmp	r12,0

00000818 0a000005   1525 	beq	.L1405

0000081c e1a01004   1526 	mov	r1,r4

00000820 e1a00006   1527 	mov	r0,r6

00000824 e1a0e00f   1528 	mov	lr,pc

00000828 e12fff1c*  1529 	bx	r12

0000082c e3500000   1530 	cmp	r0,0

00000830 1a00001d   1531 	bne	.L1411

                    1532 .L1405:

00000834 e59f2714*  1533 	ldr	r2,.L1568

00000838 e28d1008   1534 	add	r1,sp,8

0000083c e3a00000   1535 	mov	r0,0

00000840 e58d0008   1536 	str	r0,[sp,8]

00000844 e1a00006   1537 	mov	r0,r6

00000848 eb0001ad*  1538 	bl	zs_packunit32

0000084c e594200c   1539 	ldr	r2,[r4,12]

00000850 e28d1008   1540 	add	r1,sp,8

00000854 e1a00006   1541 	mov	r0,r6

00000858 eb0001a9*  1542 	bl	zs_packunit32

0000085c e5942010   1543 	ldr	r2,[r4,16]

00000860 e28d1008   1544 	add	r1,sp,8

00000864 e1a00006   1545 	mov	r0,r6

00000868 eb0001a5*  1546 	bl	zs_packunit32

0000086c e5942018   1547 	ldr	r2,[r4,24]

00000870 e28d1008   1548 	add	r1,sp,8

00000874 e1a00006   1549 	mov	r0,r6

00000878 eb0001a1*  1550 	bl	zs_packunit32

0000087c e59d2008   1551 	ldr	r2,[sp,8]

00000880 e2861024   1552 	add	r1,r6,36

00000884 e1a03fc2   1553 	mov	r3,r2 asr 31

00000888 e1a00006   1554 	mov	r0,r6

0000088c eb000127*  1555 	bl	zs_writedata

00000890 e59d2008   1556 	ldr	r2,[sp,8]

00000894 e1510fc2   1557 	cmp	r1,r2 asr 31

00000898 01500002   1558 	cmpeq	r0,r2

0000089c 01a00004   1559 	moveq	r0,r4

000008a0 0a000002   1560 	beq	.L1394

000008a4 e3550000   1561 	cmp	r5,0

000008a8 18850003   1562 	stmneea	[r5],{r0-r1}

                    1563 .L1411:

000008ac e3a00000   1564 	mov	r0,0

                    1565 .L1394:

000008b0 e28dd00c   1566 	add	sp,sp,12

000008b4 e8bd8070   1567 	ldmfd	[sp]!,{r4-r6,pc}

                    1568 	.endf	zs_entryend

                    1569 	.align	4

                    1570 ;lwritestatus	r0	local

                    1571 ;packed	[sp,8]	local

                    1572 

                    1573 ;zstream	r6	param

                    1574 ;zentry	r4	param

                    1575 ;writestatus	r5	param


                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                    1576 

                    1577 	.section ".bss","awb"

                    1578 .L1532:

                    1579 	.data

                    1580 	.text

                    1581 

                    1582 

                    1583 	.align	4

                    1584 	.align	4

                    1585 zs_finish::

000008b8 e92d48f2   1586 	stmfd	[sp]!,{r1,r4-r7,fp,lr}

000008bc e24dd00c   1587 	sub	sp,sp,12

000008c0 e58d100c   1588 	str	r1,[sp,12]

000008c4 e1a03001   1589 	mov	r3,r1

000008c8 e1a04000   1590 	mov	r4,r0

000008cc e3510000   1591 	cmp	r1,0

000008d0 13a00000   1592 	movne	r0,0

000008d4 11a01000   1593 	movne	r1,r0

000008d8 18830003   1594 	stmneea	[r3],{r0-r1}

000008dc e3540000   1595 	cmp	r4,0

000008e0 0a00010f   1596 	beq	.L1608

000008e4 e9940005   1597 	ldmed	[r4],{r0,r2}

000008e8 e584000c   1598 	str	r0,[r4,12]

000008ec e5842010   1599 	str	r2,[r4,16]

000008f0 e1a0600d   1600 	mov	r6,sp

000008f4 e5945018   1601 	ldr	r5,[r4,24]

000008f8 e3a0b000   1602 	mov	fp,0

000008fc e3550000   1603 	cmp	r5,0

00000900 0a000075   1604 	beq	.L1577

                    1605 .L1578:

00000904 e5950020   1606 	ldr	r0,[r5,32]

00000908 e5951024   1607 	ldr	r1,[r5,36]

0000090c e3e02000   1608 	mvn	r2,0

00000910 e3510000   1609 	cmp	r1,0

00000914 01500002   1610 	cmpeq	r0,r2

00000918 83a07001   1611 	movhi	r7,1

0000091c 93a07000   1612 	movls	r7,0

00000920 e58db000   1613 	str	fp,[sp]

00000924 e59f2628*  1614 	ldr	r2,.L1917

00000928 e1a01006   1615 	mov	r1,r6

0000092c e1a00004   1616 	mov	r0,r4

00000930 eb000173*  1617 	bl	zs_packunit32

00000934 e1a01006   1618 	mov	r1,r6

00000938 e1a00004   1619 	mov	r0,r4

0000093c e3a02000   1620 	mov	r2,0

00000940 eb00015b*  1621 	bl	zs_packunit16

00000944 e1d520b0   1622 	ldrh	r2,[r5]

00000948 e1a01006   1623 	mov	r1,r6

0000094c e1a00004   1624 	mov	r0,r4

00000950 eb000157*  1625 	bl	zs_packunit16

00000954 e1d520b2   1626 	ldrh	r2,[r5,2]

00000958 e1a01006   1627 	mov	r1,r6

0000095c e1a00004   1628 	mov	r0,r4

00000960 eb000153*  1629 	bl	zs_packunit16

00000964 e1d520b4   1630 	ldrh	r2,[r5,4]

00000968 e1a01006   1631 	mov	r1,r6

0000096c e1a00004   1632 	mov	r0,r4

00000970 eb00014f*  1633 	bl	zs_packunit16

00000974 e1d520b8   1634 	ldrh	r2,[r5,8]

00000978 e1a01006   1635 	mov	r1,r6

0000097c e1a00004   1636 	mov	r0,r4


                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
00000980 eb00014b*  1637 	bl	zs_packunit16

00000984 e1d520b6   1638 	ldrh	r2,[r5,6]

00000988 e1a01006   1639 	mov	r1,r6

0000098c e1a00004   1640 	mov	r0,r4

00000990 eb000147*  1641 	bl	zs_packunit16

00000994 e595200c   1642 	ldr	r2,[r5,12]

00000998 e1a01006   1643 	mov	r1,r6

0000099c e1a00004   1644 	mov	r0,r4

000009a0 eb000157*  1645 	bl	zs_packunit32

000009a4 e5952010   1646 	ldr	r2,[r5,16]

000009a8 e1a01006   1647 	mov	r1,r6

000009ac e1a00004   1648 	mov	r0,r4

000009b0 eb000153*  1649 	bl	zs_packunit32

000009b4 e5952018   1650 	ldr	r2,[r5,24]

000009b8 e1a01006   1651 	mov	r1,r6

000009bc e1a00004   1652 	mov	r0,r4

000009c0 eb00014f*  1653 	bl	zs_packunit32

000009c4 e1d522b8   1654 	ldrh	r2,[r5,40]

000009c8 e1a01006   1655 	mov	r1,r6

000009cc e1a00004   1656 	mov	r0,r4

000009d0 eb000137*  1657 	bl	zs_packunit16

000009d4 e3a02000   1658 	mov	r2,0

000009d8 e3570000   1659 	cmp	r7,0

000009dc 13a0200c   1660 	movne	r2,12

000009e0 e1a01006   1661 	mov	r1,r6

000009e4 e1a00004   1662 	mov	r0,r4

000009e8 eb000131*  1663 	bl	zs_packunit16

000009ec e1a01006   1664 	mov	r1,r6

000009f0 e1a00004   1665 	mov	r0,r4

000009f4 e3a02000   1666 	mov	r2,0

000009f8 eb00012d*  1667 	bl	zs_packunit16

000009fc e1a01006   1668 	mov	r1,r6

00000a00 e1a00004   1669 	mov	r0,r4

00000a04 e3a02000   1670 	mov	r2,0

00000a08 eb000129*  1671 	bl	zs_packunit16

00000a0c e1a01006   1672 	mov	r1,r6

00000a10 e1a00004   1673 	mov	r0,r4

00000a14 e3a02000   1674 	mov	r2,0

00000a18 eb000125*  1675 	bl	zs_packunit16

00000a1c e1a01006   1676 	mov	r1,r6

00000a20 e1a00004   1677 	mov	r0,r4

00000a24 e3a02000   1678 	mov	r2,0

00000a28 eb000135*  1679 	bl	zs_packunit32

00000a2c e3570000   1680 	cmp	r7,0

00000a30 13e02000   1681 	mvnne	r2,0

00000a34 05952020   1682 	ldreq	r2,[r5,32]

00000a38 e1a01006   1683 	mov	r1,r6

00000a3c e1a00004   1684 	mov	r0,r4

00000a40 eb00012f*  1685 	bl	zs_packunit32

00000a44 e1d522b8   1686 	ldrh	r2,[r5,40]

00000a48 e285102a   1687 	add	r1,r5,42

00000a4c e59d3000   1688 	ldr	r3,[sp]

00000a50 e2840024   1689 	add	r0,r4,36

00000a54 e0830000   1690 	add	r0,r3,r0

00000a58 eb000000*  1691 	bl	memcpy

00000a5c e1d522b8   1692 	ldrh	r2,[r5,40]

00000a60 e59d0000   1693 	ldr	r0,[sp]

00000a64 e3570000   1694 	cmp	r7,0

00000a68 e0800002   1695 	add	r0,r0,r2

00000a6c e58d0000   1696 	str	r0,[sp]

00000a70 0a00000d   1697 	beq	.L1584


                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
00000a74 e1a01006   1698 	mov	r1,r6

00000a78 e1a00004   1699 	mov	r0,r4

00000a7c e3a02001   1700 	mov	r2,1

00000a80 eb00010b*  1701 	bl	zs_packunit16

00000a84 e1a01006   1702 	mov	r1,r6

00000a88 e1a00004   1703 	mov	r0,r4

00000a8c e3a02008   1704 	mov	r2,8

00000a90 eb000107*  1705 	bl	zs_packunit16

00000a94 e5952020   1706 	ldr	r2,[r5,32]

00000a98 e5953024   1707 	ldr	r3,[r5,36]

00000a9c e1a01006   1708 	mov	r1,r6

00000aa0 e1a00004   1709 	mov	r0,r4

00000aa4 eb00012f*  1710 	bl	zs_packunit64

00000aa8 e59d0000   1711 	ldr	r0,[sp]

                    1712 .L1584:

00000aac e1a02000   1713 	mov	r2,r0

00000ab0 e1a03fc2   1714 	mov	r3,r2 asr 31

00000ab4 e2841024   1715 	add	r1,r4,36

00000ab8 e1a00004   1716 	mov	r0,r4

00000abc eb00009b*  1717 	bl	zs_writedata

00000ac0 e59d2000   1718 	ldr	r2,[sp]

00000ac4 e1510fc2   1719 	cmp	r1,r2 asr 31

00000ac8 01500002   1720 	cmpeq	r0,r2

00000acc 1a000091   1721 	bne	.L1607

00000ad0 e5955134   1722 	ldr	r5,[r5,308]

00000ad4 e3550000   1723 	cmp	r5,0

00000ad8 1affff89   1724 	bne	.L1578

                    1725 .L1577:

00000adc e99408e0   1726 	ldmed	[r4],{r5-r7,fp}

00000ae0 e0550007   1727 	subs	r0,r5,r7

00000ae4 e0c6200b   1728 	sbc	r2,r6,fp

00000ae8 e98d0005   1729 	stmfa	[sp],{r0,r2}

00000aec e3a03000   1730 	mov	r3,0

00000af0 e3e02000   1731 	mvn	r2,0

00000af4 e1520007   1732 	cmp	r2,r7

00000af8 e0d3000b   1733 	sbcs	r0,r3,fp

00000afc aa000053   1734 	bge	.L1591

00000b00 e1a07006   1735 	mov	r7,r6

00000b04 e1a06005   1736 	mov	r6,r5

00000b08 e3a05000   1737 	mov	r5,0

00000b0c e58d5000   1738 	str	r5,[sp]

00000b10 e59f2440*  1739 	ldr	r2,.L1918

00000b14 e1a0100d   1740 	mov	r1,sp

00000b18 e1a00004   1741 	mov	r0,r4

00000b1c eb0000f8*  1742 	bl	zs_packunit32

00000b20 e1a0100d   1743 	mov	r1,sp

00000b24 e1a00004   1744 	mov	r0,r4

00000b28 e3a0202c   1745 	mov	r2,44

00000b2c e1a03005   1746 	mov	r3,r5

00000b30 eb00010c*  1747 	bl	zs_packunit64

00000b34 e1a0100d   1748 	mov	r1,sp

00000b38 e1a00004   1749 	mov	r0,r4

00000b3c e3a0201e   1750 	mov	r2,30

00000b40 eb0000db*  1751 	bl	zs_packunit16

00000b44 e1a0100d   1752 	mov	r1,sp

00000b48 e1a00004   1753 	mov	r0,r4

00000b4c e3a0202d   1754 	mov	r2,45

00000b50 eb0000d7*  1755 	bl	zs_packunit16

00000b54 e1a0100d   1756 	mov	r1,sp

00000b58 e1a00004   1757 	mov	r0,r4

00000b5c e1a02005   1758 	mov	r2,r5


                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
00000b60 eb0000e7*  1759 	bl	zs_packunit32

00000b64 e1a0100d   1760 	mov	r1,sp

00000b68 e1a00004   1761 	mov	r0,r4

00000b6c e1a02005   1762 	mov	r2,r5

00000b70 eb0000e3*  1763 	bl	zs_packunit32

00000b74 e5942014   1764 	ldr	r2,[r4,20]

00000b78 e1a0100d   1765 	mov	r1,sp

00000b7c e1a03fc2   1766 	mov	r3,r2 asr 31

00000b80 e1a00004   1767 	mov	r0,r4

00000b84 eb0000f7*  1768 	bl	zs_packunit64

00000b88 e5942014   1769 	ldr	r2,[r4,20]

00000b8c e1a0100d   1770 	mov	r1,sp

00000b90 e1a03fc2   1771 	mov	r3,r2 asr 31

00000b94 e1a00004   1772 	mov	r0,r4

00000b98 eb0000f2*  1773 	bl	zs_packunit64

00000b9c e99d000c   1774 	ldmed	[sp],{r2-r3}

00000ba0 e1a0100d   1775 	mov	r1,sp

00000ba4 e1a00004   1776 	mov	r0,r4

00000ba8 eb0000ee*  1777 	bl	zs_packunit64

00000bac e594200c   1778 	ldr	r2,[r4,12]

00000bb0 e5943010   1779 	ldr	r3,[r4,16]

00000bb4 e1a0100d   1780 	mov	r1,sp

00000bb8 e1a00004   1781 	mov	r0,r4

00000bbc eb0000e9*  1782 	bl	zs_packunit64

00000bc0 e59d2000   1783 	ldr	r2,[sp]

00000bc4 e2841024   1784 	add	r1,r4,36

00000bc8 e1a03fc2   1785 	mov	r3,r2 asr 31

00000bcc e1a00004   1786 	mov	r0,r4

00000bd0 eb000056*  1787 	bl	zs_writedata

00000bd4 e59d2000   1788 	ldr	r2,[sp]

00000bd8 e1510fc2   1789 	cmp	r1,r2 asr 31

00000bdc 01500002   1790 	cmpeq	r0,r2

00000be0 1a00004c   1791 	bne	.L1607

00000be4 e58d5000   1792 	str	r5,[sp]

00000be8 e59f236c*  1793 	ldr	r2,.L1919

00000bec e1a0100d   1794 	mov	r1,sp

00000bf0 e1a00004   1795 	mov	r0,r4

00000bf4 eb0000c2*  1796 	bl	zs_packunit32

00000bf8 e1a0100d   1797 	mov	r1,sp

00000bfc e1a00004   1798 	mov	r0,r4

00000c00 e1a02005   1799 	mov	r2,r5

00000c04 eb0000be*  1800 	bl	zs_packunit32

00000c08 e1a02006   1801 	mov	r2,r6

00000c0c e1a03007   1802 	mov	r3,r7

00000c10 e1a0100d   1803 	mov	r1,sp

00000c14 e1a00004   1804 	mov	r0,r4

00000c18 eb0000d2*  1805 	bl	zs_packunit64

00000c1c e1a0100d   1806 	mov	r1,sp

00000c20 e1a00004   1807 	mov	r0,r4

00000c24 e3a02001   1808 	mov	r2,1

00000c28 eb0000b5*  1809 	bl	zs_packunit32

00000c2c e59d2000   1810 	ldr	r2,[sp]

00000c30 e2841024   1811 	add	r1,r4,36

00000c34 e1a03fc2   1812 	mov	r3,r2 asr 31

00000c38 e1a00004   1813 	mov	r0,r4

00000c3c eb00003b*  1814 	bl	zs_writedata

00000c40 e59d2000   1815 	ldr	r2,[sp]

00000c44 e1510fc2   1816 	cmp	r1,r2 asr 31

00000c48 01500002   1817 	cmpeq	r0,r2

00000c4c 1a000031   1818 	bne	.L1607

                    1819 .L1591:


                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
00000c50 e3a05000   1820 	mov	r5,0

00000c54 e58d5000   1821 	str	r5,[sp]

00000c58 e59f2300*  1822 	ldr	r2,.L1920

00000c5c e1a0100d   1823 	mov	r1,sp

00000c60 e1a00004   1824 	mov	r0,r4

00000c64 eb0000a6*  1825 	bl	zs_packunit32

00000c68 e1a0100d   1826 	mov	r1,sp

00000c6c e1a00004   1827 	mov	r0,r4

00000c70 e1a02005   1828 	mov	r2,r5

00000c74 eb00008e*  1829 	bl	zs_packunit16

00000c78 e1a0100d   1830 	mov	r1,sp

00000c7c e1a00004   1831 	mov	r0,r4

00000c80 e1a02005   1832 	mov	r2,r5

00000c84 eb00008a*  1833 	bl	zs_packunit16

00000c88 e1d421b4   1834 	ldrh	r2,[r4,20]

00000c8c e1a0100d   1835 	mov	r1,sp

00000c90 e1a00004   1836 	mov	r0,r4

00000c94 eb000086*  1837 	bl	zs_packunit16

00000c98 e1d421b4   1838 	ldrh	r2,[r4,20]

00000c9c e1a0100d   1839 	mov	r1,sp

00000ca0 e1a00004   1840 	mov	r0,r4

00000ca4 eb000082*  1841 	bl	zs_packunit16

00000ca8 e59d2004   1842 	ldr	r2,[sp,4]

00000cac e1a0100d   1843 	mov	r1,sp

00000cb0 e1a00004   1844 	mov	r0,r4

00000cb4 eb000092*  1845 	bl	zs_packunit32

00000cb8 e594000c   1846 	ldr	r0,[r4,12]

00000cbc e5941010   1847 	ldr	r1,[r4,16]

00000cc0 e3e02000   1848 	mvn	r2,0

00000cc4 e1520000   1849 	cmp	r2,r0

00000cc8 e0d52001   1850 	sbcs	r2,r5,r1

00000ccc e1a0100d   1851 	mov	r1,sp

00000cd0 b3e02000   1852 	mvnlt	r2,0

00000cd4 a1a02000   1853 	movge	r2,r0

00000cd8 e1a00004   1854 	mov	r0,r4

00000cdc eb000088*  1855 	bl	zs_packunit32

00000ce0 e1a0100d   1856 	mov	r1,sp

00000ce4 e1a00004   1857 	mov	r0,r4

00000ce8 e1a02005   1858 	mov	r2,r5

00000cec eb000070*  1859 	bl	zs_packunit16

00000cf0 e59d2000   1860 	ldr	r2,[sp]

00000cf4 e2841024   1861 	add	r1,r4,36

00000cf8 e1a03fc2   1862 	mov	r3,r2 asr 31

00000cfc e1a00004   1863 	mov	r0,r4

00000d00 eb00000a*  1864 	bl	zs_writedata

00000d04 e59d2000   1865 	ldr	r2,[sp]

00000d08 e1510fc2   1866 	cmp	r1,r2 asr 31

00000d0c 01500002   1867 	cmpeq	r0,r2

00000d10 03a00000   1868 	moveq	r0,0

00000d14 0a000003   1869 	beq	.L1569

                    1870 .L1607:

00000d18 e59d200c   1871 	ldr	r2,[sp,12]

00000d1c e3520000   1872 	cmp	r2,0

00000d20 18820003   1873 	stmneea	[r2],{r0-r1}

                    1874 .L1608:

00000d24 e3e00000   1875 	mvn	r0,0

                    1876 .L1569:

00000d28 e28dd00c   1877 	add	sp,sp,12

00000d2c e8bd88f2   1878 	ldmfd	[sp]!,{r1,r4-r7,fp,pc}

                    1879 	.endf	zs_finish

                    1880 	.align	4


                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                    1881 ;zentry	r5	local

                    1882 ;lwritestatus	r0	local

                    1883 ;packed	[sp]	local

                    1884 ;cdsize	[sp,4]	local

                    1885 ;zip64endrecord	r6	local

                    1886 ;zip64	r7	local

                    1887 

                    1888 ;zstream	r4	param

                    1889 ;writestatus	[sp,12]	param

                    1890 

                    1891 	.section ".bss","awb"

                    1892 .L1850:

                    1893 	.data

                    1894 	.text

                    1895 

                    1896 

                    1897 	.align	4

                    1898 	.align	4

                    1899 zs_writedata:

00000d30 e92d4870   1900 	stmfd	[sp]!,{r4-r6,fp,lr}

00000d34 e24dd014   1901 	sub	sp,sp,20

00000d38 e1b0b000   1902 	movs	fp,r0

00000d3c e1a04001   1903 	mov	r4,r1

00000d40 e98d000c   1904 	stmfa	[sp],{r2-r3}

00000d44 13510000   1905 	cmpne	r1,0

00000d48 03a00000   1906 	moveq	r0,0

00000d4c 01a01000   1907 	moveq	r1,r0

00000d50 0a000026   1908 	beq	.L1921

00000d54 e3a05000   1909 	mov	r5,0

00000d58 e58d500c   1910 	str	r5,[sp,12]

00000d5c e58d5010   1911 	str	r5,[sp,16]

00000d60 e99d000c   1912 	ldmed	[sp],{r2-r3}

00000d64 e1550002   1913 	cmp	r5,r2

00000d68 e0d50003   1914 	sbcs	r0,r5,r3

00000d6c aa00001d   1915 	bge	.L1928

                    1916 .L1929:

00000d70 e1a00003   1917 	mov	r0,r3

00000d74 e1a03005   1918 	mov	r3,r5

00000d78 e1a05002   1919 	mov	r5,r2

00000d7c e0552003   1920 	subs	r2,r5,r3

00000d80 e0c01fc3   1921 	sbc	r1,r0,r3 asr 31

00000d84 e3a00c40   1922 	mov	r0,1<<14

00000d88 e2800001   1923 	add	r0,r0,1

00000d8c e1520000   1924 	cmp	r2,r0

00000d90 e2d10000   1925 	sbcs	r0,r1,0

00000d94 a3a02c40   1926 	movge	r2,1<<14

00000d98 b0452003   1927 	sublt	r2,r5,r3

00000d9c e59b0000   1928 	ldr	r0,[fp]

00000da0 e0841003   1929 	add	r1,r4,r3

00000da4 eb000000*  1930 	bl	zs_user_writeToStream

00000da8 e1a01fc0   1931 	mov	r1,r0 asr 31

00000dac e3500001   1932 	cmp	r0,1

00000db0 e2d1c000   1933 	sbcs	r12,r1,0

00000db4 ba00000d   1934 	blt	.L1921

00000db8 e99b000c   1935 	ldmed	[fp],{r2-r3}

00000dbc e0902002   1936 	adds	r2,r0,r2

00000dc0 e0a13003   1937 	adc	r3,r1,r3

00000dc4 e98b000c   1938 	stmfa	[fp],{r2-r3}

00000dc8 e99d500c   1939 	ldmed	[sp],{r2-r3,r12,lr}

00000dcc e090500c   1940 	adds	r5,r0,r12

00000dd0 e58d500c   1941 	str	r5,[sp,12]


                                                                      Page 33
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
00000dd4 e0a1000e   1942 	adc	r0,r1,lr

00000dd8 e58d0010   1943 	str	r0,[sp,16]

00000ddc e1550002   1944 	cmp	r5,r2

00000de0 e0d00003   1945 	sbcs	r0,r0,r3

00000de4 baffffe1   1946 	blt	.L1929

                    1947 .L1928:

00000de8 e59d000c   1948 	ldr	r0,[sp,12]

00000dec e59d1010   1949 	ldr	r1,[sp,16]

                    1950 .L1921:

00000df0 e28dd014   1951 	add	sp,sp,20

00000df4 e8bd4870   1952 	ldmfd	[sp]!,{r4-r6,fp,lr}

00000df8 e12fff1e*  1953 	ret	

                    1954 	.endf	zs_writedata

                    1955 	.align	4

                    1956 ;lwritestatus	r0	local

                    1957 ;written	[sp,12]	local

                    1958 

                    1959 ;zstream	fp	param

                    1960 ;writeBuffer	r4	param

                    1961 ;writeBufferSize	[sp,4]	param

                    1962 

                    1963 	.section ".bss","awb"

                    1964 .L2012:

                    1965 	.data

                    1966 	.text

                    1967 

                    1968 

                    1969 ;910: /***************************************************************************

                    1970 ;911:  * Byte swapping routine:

                    1971 ;912:  *

                    1972 ;913:  * Functions for generalized, in-place byte swapping from host order

                    1973 ;914:  * to little-endian.  A run-time test of byte order is conducted on

                    1974 ;915:  * the first usage and a static variable is used to store the result

                    1975 ;916:  * for later use.

                    1976 ;917:  *

                    1977 ;918:  * The byte-swapping requires memory-aligned quantities.

                    1978 ;919:  *

                    1979 ;920:  ***************************************************************************/

                    1980 ;921: static void

                    1981 ;922: zs_htolx ( void *data, int size )

                    1982 	.align	4

                    1983 	.align	4

                    1984 zs_htolx:

00000dfc e92d0100   1985 	stmfd	[sp]!,{r8}

                    1986 ;923: {

                    1987 

00000e00 e24dd004   1988 	sub	sp,sp,4

00000e04 e3a0c001   1989 	mov	r12,1

00000e08 e59f3154*  1990 	ldr	r3,.L2161

00000e0c e1cdc0b2   1991 	strh	r12,[sp,2]

                    1992 ;924:   static int le = -1;

                    1993 ;925:   int16_t host = 1;

                    1994 

                    1995 ;927:   uint16_t *data2;

                    1996 ;928:   uint32_t *data4;

                    1997 ;929:   uint32_t h0, h1;

                    1998 ;931:   /* Determine byte order, test for little-endianness */

                    1999 ;932:   if ( le < 0 )

                    2000 

00000e10 e5932000   2001 	ldr	r2,[r3]

00000e14 e3520000   2002 	cmp	r2,0


                                                                      Page 34
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                    2003 ;933:     {

                    2004 

                    2005 ;934:       le = (*((int8_t *)(&host)));

                    2006 

00000e18 b1a0200c   2007 	movlt	r2,r12

00000e1c b5832000   2008 	strlt	r2,[r3]

                    2009 ;935:     }

                    2010 ;937:   /* Swap bytes if not little-endian, requires memory-aligned quantities */

                    2011 ;938:   if ( le == 0 )

                    2012 

00000e20 e3520000   2013 	cmp	r2,0

00000e24 1a00001f   2014 	bne	.L2044

                    2015 ;939:     {

                    2016 

                    2017 ;940:       switch ( size )

                    2018 

00000e28 e2511002   2019 	subs	r1,r1,2

00000e2c 0a000004   2020 	beq	.L2052

00000e30 e2511002   2021 	subs	r1,r1,2

00000e34 0a000009   2022 	beq	.L2053

00000e38 e3510004   2023 	cmp	r1,4

00000e3c 0a00000e   2024 	beq	.L2054

00000e40 ea000018   2025 	b	.L2044

                    2026 .L2052:

                    2027 ;941:         {

                    2028 ;942:         case 2:

                    2029 ;943:           data2 = (uint16_t *) data;

                    2030 

                    2031 ;944:           *data2=(((*data2>>8)&0xff) | ((*data2&0xff)<<8));

                    2032 

00000e44 e1d010b0   2033 	ldrh	r1,[r0]

00000e48 e1a02801   2034 	mov	r2,r1 lsl 16

00000e4c e1a01c01   2035 	mov	r1,r1 lsl 24

00000e50 e1a01821   2036 	mov	r1,r1 lsr 16

00000e54 e1811c22   2037 	orr	r1,r1,r2 lsr 24

00000e58 e1c010b0   2038 	strh	r1,[r0]

00000e5c ea000011   2039 	b	.L2044

                    2040 .L2053:

                    2041 ;945:           break;

                    2042 ;946:         case 4:

                    2043 ;947:           data4 = (uint32_t *) data;

                    2044 

                    2045 ;948:           *data4=(((*data4>>24)&0xff) | ((*data4&0xff)<<24) |

                    2046 

00000e60 e5901000   2047 	ldr	r1,[r0]

00000e64 e1a02461   2048 	mov	r2,r1 ror 8

00000e68 e0211861   2049 	eor	r1,r1,r1 ror 16

00000e6c e3c118ff   2050 	bic	r1,r1,255<<16

00000e70 e0221421   2051 	eor	r1,r2,r1 lsr 8

00000e74 e5801000   2052 	str	r1,[r0]

00000e78 ea00000a   2053 	b	.L2044

                    2054 .L2054:

                    2055 ;949:                   ((*data4>>8)&0xff00) | ((*data4&0xff00)<<8));

                    2056 ;950:           break;

                    2057 ;951:         case 8:

                    2058 ;952:           data4 = (uint32_t *) data;

                    2059 

                    2060 ;954:           h0 = data4[0];

                    2061 

00000e7c e890000a   2062 	ldmfd	[r0],{r1,r3}

                    2063 ;955:           h0 = (((h0>>24)&0xff) | ((h0&0xff)<<24) |


                                                                      Page 35
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                    2064 

00000e80 e1a02461   2065 	mov	r2,r1 ror 8

00000e84 e0211861   2066 	eor	r1,r1,r1 ror 16

00000e88 e3c118ff   2067 	bic	r1,r1,255<<16

00000e8c e0222421   2068 	eor	r2,r2,r1 lsr 8

                    2069 ;956:                 ((h0>>8)&0xff00) | ((h0&0xff00)<<8));

                    2070 ;958:           h1 = data4[1];

                    2071 

00000e90 e1a01003   2072 	mov	r1,r3

                    2073 ;959:           h1 = (((h1>>24)&0xff) | ((h1&0xff)<<24) |

                    2074 

00000e94 e1a03461   2075 	mov	r3,r1 ror 8

00000e98 e0211861   2076 	eor	r1,r1,r1 ror 16

00000e9c e3c118ff   2077 	bic	r1,r1,255<<16

00000ea0 e0231421   2078 	eor	r1,r3,r1 lsr 8

                    2079 ;960:                 ((h1>>8)&0xff00) | ((h1&0xff00)<<8));

                    2080 ;962:           data4[0] = h1;

                    2081 

00000ea4 e8800006   2082 	stmea	[r0],{r1-r2}

                    2083 ;963:           data4[1] = h0;

                    2084 

                    2085 .L2044:

00000ea8 e28dd004   2086 	add	sp,sp,4

00000eac e8bd0100   2087 	ldmfd	[sp]!,{r8}

00000eb0 e12fff1e*  2088 	ret	

                    2089 	.endf	zs_htolx

                    2090 	.align	4

                    2091 ;le	.L2138	static

                    2092 ;host	[sp,2]	local

                    2093 ;h0	r1	local

                    2094 ;h1	r1	local

                    2095 

                    2096 ;data	r0	param

                    2097 ;size	r1	param

                    2098 

                    2099 	.section ".bss","awb"

                    2100 .L2135:

                    2101 	.data

00000000 ffffffff   2102 .L2138:	.data.b	255,255,255,255

                    2103 	.text

                    2104 

                    2105 ;964:           break;

                    2106 ;965:         }

                    2107 ;966:     }

                    2108 ;967: }

                    2109 

                    2110 ;970: /***************************************************************************

                    2111 ;971:  *

                    2112 ;972:  * Helper functions to write little-endian integer values to a

                    2113 ;973:  * specified offset in the ZIPstream buffer and increment offset.

                    2114 ;974:  *

                    2115 ;975:  ***************************************************************************/

                    2116 ;976: static void zs_packunit16 (ZIPstream *ZS, int *O, uint16_t V)

                    2117 	.align	4

                    2118 	.align	4

                    2119 zs_packunit16:

00000eb4 e92d4030   2120 	stmfd	[sp]!,{r4-r5,lr}

                    2121 ;977: {

                    2122 

                    2123 ;978:   memcpy (ZS->buffer+*O, &V, 2);

                    2124 


                                                                      Page 36
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
00000eb8 e24dd004   2125 	sub	sp,sp,4

00000ebc e1a04001   2126 	mov	r4,r1

00000ec0 e2805024   2127 	add	r5,r0,36

00000ec4 e5940000   2128 	ldr	r0,[r4]

00000ec8 e28d1002   2129 	add	r1,sp,2

00000ecc e0800005   2130 	add	r0,r0,r5

00000ed0 e1cd20b2   2131 	strh	r2,[sp,2]

00000ed4 e3a02002   2132 	mov	r2,2

00000ed8 eb000000*  2133 	bl	memcpy

                    2134 ;979:   zs_htolx(ZS->buffer+*O, 2);

                    2135 

00000edc e5940000   2136 	ldr	r0,[r4]

00000ee0 e3a01002   2137 	mov	r1,2

00000ee4 e0800005   2138 	add	r0,r0,r5

00000ee8 ebffffc3*  2139 	bl	zs_htolx

                    2140 ;980:   *O += 2;

                    2141 

00000eec e5940000   2142 	ldr	r0,[r4]

00000ef0 e2800002   2143 	add	r0,r0,2

00000ef4 e5840000   2144 	str	r0,[r4]

00000ef8 e28dd004   2145 	add	sp,sp,4

00000efc e8bd4030   2146 	ldmfd	[sp]!,{r4-r5,lr}

00000f00 e12fff1e*  2147 	ret	

                    2148 	.endf	zs_packunit16

                    2149 	.align	4

                    2150 

                    2151 ;ZS	r0	param

                    2152 ;O	r4	param

                    2153 ;V	[sp,2]	param

                    2154 

                    2155 	.section ".bss","awb"

                    2156 .L2193:

                    2157 	.data

                    2158 	.text

                    2159 

                    2160 ;981: }

                    2161 

                    2162 ;982: static void zs_packunit32 (ZIPstream *ZS, int *O, uint32_t V)

                    2163 	.align	4

                    2164 	.align	4

                    2165 zs_packunit32:

00000f04 e92d4034   2166 	stmfd	[sp]!,{r2,r4-r5,lr}

                    2167 ;983: {

                    2168 

                    2169 ;984:   memcpy (ZS->buffer+*O, &V, 4);

                    2170 

00000f08 e1a04001   2171 	mov	r4,r1

00000f0c e2805024   2172 	add	r5,r0,36

00000f10 e5940000   2173 	ldr	r0,[r4]

00000f14 e1a0100d   2174 	mov	r1,sp

00000f18 e0800005   2175 	add	r0,r0,r5

00000f1c e3a02004   2176 	mov	r2,4

00000f20 eb000000*  2177 	bl	memcpy

                    2178 ;985:   zs_htolx(ZS->buffer+*O, 4);

                    2179 

00000f24 e5940000   2180 	ldr	r0,[r4]

00000f28 e3a01004   2181 	mov	r1,4

00000f2c e0800005   2182 	add	r0,r0,r5

00000f30 ebffffb1*  2183 	bl	zs_htolx

                    2184 ;986:   *O += 4;

                    2185 


                                                                      Page 37
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
00000f34 e5940000   2186 	ldr	r0,[r4]

00000f38 e2800004   2187 	add	r0,r0,4

00000f3c e5840000   2188 	str	r0,[r4]

00000f40 e8bd4034   2189 	ldmfd	[sp]!,{r2,r4-r5,lr}

00000f44 e12fff1e*  2190 	ret	

                    2191 	.endf	zs_packunit32

                    2192 	.align	4

                    2193 .L504:

00000f48 00000000*  2194 	.data.w	zs_store_process

                    2195 	.type	.L504,$object

                    2196 	.size	.L504,4

                    2197 

                    2198 .L1156:

00000f4c 04034b50   2199 	.data.w	0x04034b50

                    2200 	.type	.L1156,$object

                    2201 	.size	.L1156,4

                    2202 

                    2203 .L1568:

00000f50 08074b50   2204 	.data.w	0x08074b50

                    2205 	.type	.L1568,$object

                    2206 	.size	.L1568,4

                    2207 

                    2208 .L1917:

00000f54 02014b50   2209 	.data.w	0x02014b50

                    2210 	.type	.L1917,$object

                    2211 	.size	.L1917,4

                    2212 

                    2213 .L1918:

00000f58 06064b50   2214 	.data.w	0x06064b50

                    2215 	.type	.L1918,$object

                    2216 	.size	.L1918,4

                    2217 

                    2218 .L1919:

00000f5c 07064b50   2219 	.data.w	0x07064b50

                    2220 	.type	.L1919,$object

                    2221 	.size	.L1919,4

                    2222 

                    2223 .L1920:

00000f60 06054b50   2224 	.data.w	0x06054b50

                    2225 	.type	.L1920,$object

                    2226 	.size	.L1920,4

                    2227 

                    2228 .L2161:

00000f64 00000000*  2229 	.data.w	.L2138

                    2230 	.type	.L2161,$object

                    2231 	.size	.L2161,4

                    2232 

                    2233 	.align	4

                    2234 

                    2235 ;ZS	r0	param

                    2236 ;O	r4	param

                    2237 ;V	[sp]	param

                    2238 

                    2239 	.section ".bss","awb"

                    2240 .L2225:

                    2241 	.data

                    2242 	.text

                    2243 

                    2244 ;987: }

                    2245 

                    2246 ;988: static void zs_packunit64 (ZIPstream *ZS, int *O, uint64_t V)


                                                                      Page 38
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1as1.s
                    2247 	.align	4

                    2248 	.align	4

                    2249 zs_packunit64:

00000f68 e92d4030   2250 	stmfd	[sp]!,{r4-r5,lr}

00000f6c e24dd008   2251 	sub	sp,sp,8

00000f70 e1a04001   2252 	mov	r4,r1

00000f74 e88d000c   2253 	stmea	[sp],{r2-r3}

                    2254 ;989: {

                    2255 

                    2256 ;990:   memcpy (ZS->buffer+*O, &V, 8);

                    2257 

00000f78 e2805024   2258 	add	r5,r0,36

00000f7c e5940000   2259 	ldr	r0,[r4]

00000f80 e1a0100d   2260 	mov	r1,sp

00000f84 e0800005   2261 	add	r0,r0,r5

00000f88 e3a02008   2262 	mov	r2,8

00000f8c eb000000*  2263 	bl	memcpy

                    2264 ;991:   zs_htolx(ZS->buffer+*O, 8);

                    2265 

00000f90 e5940000   2266 	ldr	r0,[r4]

00000f94 e3a01008   2267 	mov	r1,8

00000f98 e0800005   2268 	add	r0,r0,r5

00000f9c ebffff96*  2269 	bl	zs_htolx

                    2270 ;992:   *O += 8;

                    2271 

00000fa0 e5940000   2272 	ldr	r0,[r4]

00000fa4 e2800008   2273 	add	r0,r0,8

00000fa8 e5840000   2274 	str	r0,[r4]

00000fac e28dd008   2275 	add	sp,sp,8

00000fb0 e8bd4030   2276 	ldmfd	[sp]!,{r4-r5,lr}

00000fb4 e12fff1e*  2277 	ret	

                    2278 	.endf	zs_packunit64

                    2279 	.align	4

                    2280 

                    2281 ;ZS	r0	param

                    2282 ;O	r4	param

                    2283 ;V	[sp]	param

                    2284 

                    2285 	.section ".bss","awb"

                    2286 .L2257:

                    2287 	.data

                    2288 	.text

                    2289 

                    2290 ;993: }

                    2291 	.align	4

                    2292 

                    2293 	.data

                    2294 	.ghsnote version,6

                    2295 	.ghsnote tools,3

                    2296 	.ghsnote options,0

                    2297 	.text

                    2298 	.align	4

                    2299 	.data

                    2300 	.align	4

                    2301 	.text

