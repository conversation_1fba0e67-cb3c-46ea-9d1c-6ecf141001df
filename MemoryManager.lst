                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_89k1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=MemoryManager.c -o gh_89k1.o -list=MemoryManager.lst C:\Users\<USER>\AppData\Local\Temp\gh_89k1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_89k1.s
Source File: MemoryManager.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		MemoryManager.c -o MemoryManager.o

                      12 ;Source File:   MemoryManager.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:55 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "MemoryManager.h"


                      23 ;2: #include <stdint.h>


                      24 ;3: #include <stdlib.h>


                      25 ;4: 


                      26 ;5: //Комментарий для проверки кодировки


                      27 ;6: 


                      28 ;7: #define MEMORY_SIZE (2048 * 1024)


                      29 ;8: 


                      30 ;9: static uint8_t* g_memory = NULL;


                      31 ;10: static size_t g_pos = 0;


                      32 ;11: 


                      33 ;12: bool MM_init(void)


                      34 ;13: {


                      35 ;14:     g_pos = 0;


                      36 ;15:     g_memory = malloc(MEMORY_SIZE);


                      37 ;16:     if(g_memory != NULL)


                      38 ;17:     {


                      39 ;18: 


                      40 ;19:         return true;


                      41 ;20:     }


                      42 ;21:     else


                      43 ;22:     {


                      44 ;23:         return false;


                      45 ;24:     }


                      46 ;25: }


                      47 ;26: 


                      48 ;27: void* MM_alloc(size_t size)


                      49 ;28: {


                      50 ;29:     void* allocated;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_89k1.s
                      51 ;30:     if (g_memory == NULL)


                      52 ;31:     {


                      53 ;32:         return NULL;


                      54 ;33:     }


                      55 ;34:     //Округляем в большую сторону до 4


                      56 ;35:     if ((size & 3) != 0)


                      57 ;36:     {


                      58 ;37:         size += 4 - (size & 3);


                      59 ;38:     }


                      60 ;39: 


                      61 ;40:     if (g_pos + size > MEMORY_SIZE)


                      62 ;41:     {


                      63 ;42:         return false;


                      64 ;43:     }


                      65 ;44:     allocated = g_memory + g_pos;


                      66 ;45:     g_pos += size;


                      67 ;46:     return allocated;


                      68 ;47: }


                      69 ;48: 


                      70 ;49: size_t MM_getAllocated(void)


                      71 

                      72 ;52: }


                      73 

                      74 	.text

                      75 	.align	4

                      76 MM_init::

00000000 e92d4000     77 	stmfd	[sp]!,{lr}

00000004 e59f1068*    78 	ldr	r1,.L62

00000008 e3a00000     79 	mov	r0,0

0000000c e5810000     80 	str	r0,[r1]

00000010 e3a00980     81 	mov	r0,1<<21

00000014 eb000000*    82 	bl	malloc

00000018 e59f1058*    83 	ldr	r1,.L63

0000001c e3500000     84 	cmp	r0,0

00000020 e5810000     85 	str	r0,[r1]

00000024 13a00001     86 	movne	r0,1

00000028 e8bd8000     87 	ldmfd	[sp]!,{pc}

                      88 	.endf	MM_init

                      89 	.align	4

                      90 

                      91 	.section ".bss","awb"

                      92 .L53:

                      93 	.data

                      94 .L54:

00000000 00000000     95 g_memory:	.data.b	0,0,0,0

                      96 	.type	g_memory,$object

                      97 	.size	g_memory,4

                      98 .L55:

00000004 00000000     99 g_pos:	.data.b	0,0,0,0

                     100 	.type	g_pos,$object

                     101 	.size	g_pos,4

                     102 	.text

                     103 

                     104 

                     105 	.align	4

                     106 	.align	4

                     107 MM_alloc::

0000002c e59fc044*   108 	ldr	r12,.L63

00000030 e59c3000    109 	ldr	r3,[r12]

00000034 e3530000    110 	cmp	r3,0

00000038 0a000008    111 	beq	.L72


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_89k1.s
0000003c e3100003    112 	tst	r0,3

00000040 13c00003    113 	bicne	r0,r0,3

00000044 e59c1004    114 	ldr	r1,[r12,4]

00000048 12800004    115 	addne	r0,r0,4

0000004c e0812000    116 	add	r2,r1,r0

00000050 e3520980    117 	cmp	r2,1<<21

00000054 90810003    118 	addls	r0,r1,r3

00000058 958c2004    119 	strls	r2,[r12,4]

0000005c 9a000000    120 	bls	.L64

                     121 .L72:

00000060 e3a00000    122 	mov	r0,0

                     123 .L64:

00000064 e12fff1e*   124 	ret	

                     125 	.endf	MM_alloc

                     126 	.align	4

                     127 

                     128 ;size	r0	param

                     129 

                     130 	.data

                     131 	.text

                     132 

                     133 	.align	4

                     134 	.align	4

                     135 MM_getAllocated::

                     136 ;50: {


                     137 

                     138 ;51:     return g_pos;


                     139 

00000068 e59fc004*   140 	ldr	r12,.L62

0000006c e59c0000    141 	ldr	r0,[r12]

00000070 e12fff1e*   142 	ret	

                     143 	.endf	MM_getAllocated

                     144 	.align	4

                     145 

                     146 	.data

                     147 	.text

                     148 	.align	4

                     149 .L62:

00000074 00000000*   150 	.data.w	.L55

                     151 	.type	.L62,$object

                     152 	.size	.L62,4

                     153 

                     154 .L63:

00000078 00000000*   155 	.data.w	.L54

                     156 	.type	.L63,$object

                     157 	.size	.L63,4

                     158 

                     159 	.align	4

                     160 ;g_memory	.L54	static

                     161 ;g_pos	.L55	static

                     162 

                     163 	.data

                     164 	.ghsnote version,6

                     165 	.ghsnote tools,3

                     166 	.ghsnote options,0

                     167 	.text

                     168 	.align	4

                     169 	.data

                     170 	.align	4

                     171 	.text

