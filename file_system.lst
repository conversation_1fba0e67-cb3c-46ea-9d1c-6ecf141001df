                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cjk1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=file_system.c -o gh_cjk1.o -list=file_system.lst C:\Users\<USER>\AppData\Local\Temp\gh_cjk1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_cjk1.s
Source File: file_system.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile file_system.c

                      11 ;		-o file_system.o

                      12 ;Source File:   file_system.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:57 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "file_system.h"


                      23 ;2: 


                      24 ;3: #include "BaseAsnTypes.h"


                      25 ;4: #include "local_types.h"


                      26 ;5: #include "types.h"


                      27 ;6: #include "bufView.h"


                      28 ;7: #include "frsm.h"


                      29 ;8: #include "FS/ConfigFiles.h"


                      30 ;9: #include "FS/OscFiles.h"


                      31 ;10: #include <stddef.h>


                      32 ;11: 


                      33 ;12: #define CFG_DIR_NAME "config"


                      34 ;13: #define OSC_DIR_NAME "osc"


                      35 ;14: 


                      36 ;15: inline bool isDelimiter(char c)


                      37 

                      38 ;18: }


                      39 

                      40 ;19: 


                      41 ;20: // Ищет первый разделитель от конца строки к началу


                      42 ;21: // Если не найден, возвращает FALSE;


                      43 ;22: static bool findDelimiter(const StringView* fullName, size_t* delimiterPos )


                      44 

                      45 ;35: }


                      46 

                      47 ;36: 


                      48 ;37: // Удаляет разделитель директориев слева, если есть.


                      49 ;38: static void stripLeftDelim(StringView* name)


                      50 ;39: {



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cjk1.s
                      51 ;40:     //Срезаем лидирующий разделитель, если есть


                      52 ;41:     if (name->len > 0 && isDelimiter(name->p[0]))


                      53 ;42:     {


                      54 ;43:         StringView_init(name, name->p + 1, name->len - 1);


                      55 ;44:     }


                      56 ;45: }


                      57 ;46: 


                      58 ;47: //Разделяет полное имя файла на собственно имя файла и имя директория


                      59 ;48: //Имена всегда возвращаются без лидирущих разделителей.


                      60 ;49: static void splitFullFileName(const StringView* fullName,


                      61 

                      62 ;71:     }


                      63 ;72: }


                      64 

                      65 ;73: 


                      66 ;74: 


                      67 ;75: bool fs_init(void)


                      68 ;76: {


                      69 ;77:     return frsm_init() && CFGFS_init() && OSCFS_init();


                      70 ;78: }


                      71 ;79: 


                      72 ;80: 


                      73 ;81: static FNameErrCode oscFindFirst(StringView* startFileName,


                      74 ;82:     FSFindData* fileInfo, BufferView* bufForName)


                      75 ;83: {


                      76 ;84:     fileInfo->subsystem = FS_SUB_OSC;


                      77 ;85:     BufferView_writeStr(bufForName, "/" OSC_DIR_NAME "/");


                      78 ;86:     return OSCFS_findFirst(startFileName, fileInfo, bufForName);


                      79 ;87: }


                      80 ;88: 


                      81 ;89: FNameErrCode fs_findFirst(StringView* dirName, StringView* startFileName,


                      82 ;90:     FSFindData* fileInfo, BufferView* bufForName)


                      83 ;91: {


                      84 ;92:     FNameErrCode result;


                      85 ;93: 


                      86 ;94:     //dirName без лидирующего разделителя


                      87 ;95:     StringView strippedDirName;


                      88 ;96:     StringView_fromStringView(&strippedDirName, dirName);


                      89 ;97:     stripLeftDelim(&strippedDirName);


                      90 ;98: 


                      91 ;99:     //Если пустое имя директория, значит нужен список корневого директория


                      92 ;100:     fileInfo->rootDir = (strippedDirName.len == 0);


                      93 ;101:     if (fileInfo->rootDir


                      94 ;102:         || StringView_cmpCStr(&strippedDirName, CFG_DIR_NAME) == 0)


                      95 ;103:     {


                      96 ;104:         //Поиск в конфигурационных файлах


                      97 ;105:         //Сохраняем позицию буфера имени для восстановления, если


                      98 ;106:         //конфигурационного файла не найдено


                      99 ;107:         size_t oldNamePos = bufForName->pos;


                     100 ;108:         fileInfo->subsystem = FS_SUB_CFG;


                     101 ;109:         BufferView_writeStr(bufForName, "/" CFG_DIR_NAME "/");


                     102 ;110:         result = CFGFS_findFirst(startFileName, fileInfo, bufForName);


                     103 ;111:         //Если обходим корневоей директорий и ничего не нашли в конфигах,


                     104 ;112:         //переходим к поиску осциллограмм


                     105 ;113:         if (result == FNAME_NOT_FOUND && fileInfo->rootDir)


                     106 ;114:         {


                     107 ;115:             //восстанавливаем позицию буфера имени


                     108 ;116:             bufForName->pos = oldNamePos;


                     109 ;117:             result = oscFindFirst(startFileName, fileInfo, bufForName);


                     110 ;118:         }


                     111 ;119:     }



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cjk1.s
                     112 ;120:     else if(StringView_cmpCStr(&strippedDirName, OSC_DIR_NAME) == 0)


                     113 ;121:     {


                     114 ;122:         //Поиск в осциллограммах


                     115 ;123:         result = oscFindFirst(startFileName, fileInfo, bufForName);


                     116 ;124:     }


                     117 ;125:     else


                     118 ;126:     {


                     119 ;127:         result = FNAME_NOT_FOUND;


                     120 ;128:     }


                     121 ;129:     if (result == FNAME_OK)


                     122 ;130:     {


                     123 ;131:         StringView_init(&fileInfo->fileName, (char*)bufForName->p, bufForName->pos);


                     124 ;132:     }


                     125 ;133:     return result;


                     126 ;134: }


                     127 ;135: 


                     128 ;136: FNameErrCode fs_findNext(FSFindData* fileInfo, BufferView* bufForName)


                     129 ;137: {


                     130 ;138:     FNameErrCode result;


                     131 ;139: 


                     132 ;140:     if (fileInfo->subsystem == FS_SUB_CFG)


                     133 ;141:     {


                     134 ;142:         //Поиск в конфигурационных файлах


                     135 ;143:         //Сохраняем позицию буфера имени для восстановления, если


                     136 ;144:         //конфигурационного файла не найдено


                     137 ;145:         size_t oldNamePos = bufForName->pos;


                     138 ;146:         BufferView_writeStr(bufForName, "/" CFG_DIR_NAME "/");


                     139 ;147:         result = CFGFS_findNext(fileInfo, bufForName);


                     140 ;148:         //Если обходим корневоей директорий и ничего не нашли в конфигах,


                     141 ;149:         //переходим к поиску осциллограмм


                     142 ;150:         if (result == FNAME_NOT_FOUND && fileInfo->rootDir)


                     143 ;151:         {


                     144 ;152:             // закрытие поиска, может здесь должен быть вызов CFGFS_findClose(fileInfo)?


                     145 ;153:             fs_findClose(fileInfo);


                     146 ;154:             //восстанавливаем позицию буфера имени


                     147 ;155:             bufForName->pos = oldNamePos;


                     148 ;156:             result = oscFindFirst(NULL, fileInfo, bufForName);


                     149 ;157:         }


                     150 ;158:     }


                     151 ;159:     else if (fileInfo->subsystem == FS_SUB_OSC)


                     152 ;160:     {


                     153 ;161:         BufferView_writeStr(bufForName, "/" OSC_DIR_NAME "/");


                     154 ;162:         result = OSCFS_findNext(fileInfo, bufForName);


                     155 ;163:     }


                     156 ;164:     else


                     157 ;165:     {


                     158 ;166:         result = FNAME_NOT_FOUND;


                     159 ;167:     }


                     160 ;168:     if (result == FNAME_OK)


                     161 ;169:     {


                     162 ;170:         StringView_init(&fileInfo->fileName, (char*)bufForName->p, bufForName->pos);


                     163 ;171:     }


                     164 ;172:     return result;


                     165 ;173: }


                     166 ;174: void fs_findClose(FSFindData* fileInfo)


                     167 ;175: {


                     168 ;176:     if (fileInfo->subsystem == FS_SUB_CFG)


                     169 ;177:     {


                     170 ;178:         CFGFS_findClose(fileInfo);


                     171 ;179:     }


                     172 ;180:     else if (fileInfo->subsystem == FS_SUB_OSC)



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cjk1.s
                     173 ;181:     {


                     174 ;182:         OSCFS_findClose(fileInfo);


                     175 ;183:     }


                     176 ;184: }


                     177 ;185: static bool fileOpen(StringView* dirName, StringView* fileName, FRSM* frsm,


                     178 

                     179 ;199: }


                     180 

                     181 	.text

                     182 	.align	4

                     183 stripLeftDelim:

00000000 e92d4000    184 	stmfd	[sp]!,{lr}

00000004 e5902000    185 	ldr	r2,[r0]

00000008 e3520000    186 	cmp	r2,0

0000000c 0a000006    187 	beq	.L121

00000010 e5903004    188 	ldr	r3,[r0,4]

00000014 e5d31000    189 	ldrb	r1,[r3]

                     190 ;16: {


                     191 

                     192 ;17:     return c == '\\' || c == '/';


                     193 

00000018 e351005c    194 	cmp	r1,92

0000001c 1351002f    195 	cmpne	r1,47

00000020 02422001    196 	subeq	r2,r2,1

00000024 02831001    197 	addeq	r1,r3,1

00000028 0b000000*   198 	bleq	StringView_init

                     199 .L121:

0000002c e8bd4000    200 	ldmfd	[sp]!,{lr}

00000030 e12fff1e*   201 	ret	

                     202 	.endf	stripLeftDelim

                     203 	.align	4

                     204 ;c	r1	local

                     205 

                     206 ;name	r0	param

                     207 

                     208 	.section ".bss","awb"

                     209 .L226:

                     210 	.data

                     211 	.text

                     212 

                     213 

                     214 	.align	4

                     215 	.align	4

                     216 fs_init::

00000034 e92d4010    217 	stmfd	[sp]!,{r4,lr}

00000038 e3a04000    218 	mov	r4,0

0000003c eb000000*   219 	bl	frsm_init

00000040 e3500000    220 	cmp	r0,0

00000044 0a000005    221 	beq	.L244

00000048 eb000000*   222 	bl	CFGFS_init

0000004c e3500000    223 	cmp	r0,0

00000050 0a000002    224 	beq	.L244

00000054 eb000000*   225 	bl	OSCFS_init

00000058 e3500000    226 	cmp	r0,0

0000005c 13a04001    227 	movne	r4,1

                     228 .L244:

00000060 e20400ff    229 	and	r0,r4,255

00000064 e8bd8010    230 	ldmfd	[sp]!,{r4,pc}

                     231 	.endf	fs_init

                     232 	.align	4

                     233 


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cjk1.s
                     234 	.section ".bss","awb"

                     235 .L306:

                     236 	.data

                     237 	.text

                     238 

                     239 

                     240 	.align	4

                     241 	.align	4

                     242 oscFindFirst:

00000068 e92d4070    243 	stmfd	[sp]!,{r4-r6,lr}

0000006c e1a05002    244 	mov	r5,r2

00000070 e1a06000    245 	mov	r6,r0

00000074 e3a00001    246 	mov	r0,1

00000078 e1a04001    247 	mov	r4,r1

0000007c e5840008    248 	str	r0,[r4,8]

00000080 e28f1000*   249 	adr	r1,.L359

00000084 e1a00005    250 	mov	r0,r5

00000088 eb000000*   251 	bl	BufferView_writeStr

0000008c e1a02005    252 	mov	r2,r5

00000090 e1a01004    253 	mov	r1,r4

00000094 e1a00006    254 	mov	r0,r6

00000098 eb000000*   255 	bl	OSCFS_findFirst

0000009c e8bd4070    256 	ldmfd	[sp]!,{r4-r6,lr}

000000a0 e12fff1e*   257 	ret	

                     258 	.endf	oscFindFirst

                     259 	.align	4

                     260 ;.L351	.L354	static

                     261 

                     262 ;startFileName	r6	param

                     263 ;fileInfo	r4	param

                     264 ;bufForName	r5	param

                     265 

                     266 	.section ".bss","awb"

                     267 .L350:

                     268 	.data

                     269 	.text

                     270 

                     271 

                     272 	.align	4

                     273 	.align	4

                     274 fs_findFirst::

000000a4 e92d44f0    275 	stmfd	[sp]!,{r4-r7,r10,lr}

000000a8 e1a06002    276 	mov	r6,r2

000000ac e1a05003    277 	mov	r5,r3

000000b0 e24dd008    278 	sub	sp,sp,8

000000b4 e1a07001    279 	mov	r7,r1

000000b8 e1a01000    280 	mov	r1,r0

000000bc e1a0000d    281 	mov	r0,sp

000000c0 eb000000*   282 	bl	StringView_fromStringView

000000c4 e1a0000d    283 	mov	r0,sp

000000c8 ebffffcc*   284 	bl	stripLeftDelim

000000cc e59d0000    285 	ldr	r0,[sp]

000000d0 e3500000    286 	cmp	r0,0

000000d4 03a00001    287 	moveq	r0,1

000000d8 13a00000    288 	movne	r0,0

000000dc e5c60000    289 	strb	r0,[r6]

000000e0 e3500000    290 	cmp	r0,0

000000e4 1a000004    291 	bne	.L363

000000e8 e28f1000*   292 	adr	r1,.L534

000000ec e1a0000d    293 	mov	r0,sp

000000f0 eb000000*   294 	bl	StringView_cmpCStr


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cjk1.s
000000f4 e3500000    295 	cmp	r0,0

000000f8 1a000017    296 	bne	.L362

                     297 .L363:

000000fc e595a004    298 	ldr	r10,[r5,4]

00000100 e3a00000    299 	mov	r0,0

00000104 e5860008    300 	str	r0,[r6,8]

00000108 e28f1000*   301 	adr	r1,.L535

0000010c e1a00005    302 	mov	r0,r5

00000110 eb000000*   303 	bl	BufferView_writeStr

00000114 e1a02005    304 	mov	r2,r5

00000118 e1a01006    305 	mov	r1,r6

0000011c e1a00007    306 	mov	r0,r7

00000120 eb000000*   307 	bl	CFGFS_findFirst

00000124 e1a04000    308 	mov	r4,r0

00000128 e3540001    309 	cmp	r4,1

0000012c 1a000015    310 	bne	.L368

00000130 e5d60000    311 	ldrb	r0,[r6]

00000134 e3500000    312 	cmp	r0,0

00000138 0a000012    313 	beq	.L368

0000013c e585a004    314 	str	r10,[r5,4]

00000140 e1a02005    315 	mov	r2,r5

00000144 e1a01006    316 	mov	r1,r6

00000148 e1a00007    317 	mov	r0,r7

0000014c ebffffc5*   318 	bl	oscFindFirst

00000150 e1b04000    319 	movs	r4,r0

00000154 1a000010    320 	bne	.L372

00000158 ea00000c    321 	b	.L373

                     322 .L362:

0000015c e28f1000*   323 	adr	r1,.L536

00000160 e1a0000d    324 	mov	r0,sp

00000164 eb000000*   325 	bl	StringView_cmpCStr

00000168 e3500000    326 	cmp	r0,0

0000016c 13a00001    327 	movne	r0,1

00000170 1a00000a    328 	bne	.L360

00000174 e1a02005    329 	mov	r2,r5

00000178 e1a01006    330 	mov	r1,r6

0000017c e1a00007    331 	mov	r0,r7

00000180 ebffffb8*   332 	bl	oscFindFirst

00000184 e1a04000    333 	mov	r4,r0

                     334 .L368:

00000188 e3540000    335 	cmp	r4,0

0000018c 1a000002    336 	bne	.L372

                     337 .L373:

00000190 e8950006    338 	ldmfd	[r5],{r1-r2}

00000194 e286000c    339 	add	r0,r6,12

00000198 eb000000*   340 	bl	StringView_init

                     341 .L372:

0000019c e1a00004    342 	mov	r0,r4

                     343 .L360:

000001a0 e28dd008    344 	add	sp,sp,8

000001a4 e8bd84f0    345 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     346 	.endf	fs_findFirst

                     347 	.align	4

                     348 ;result	r4	local

                     349 ;strippedDirName	[sp]	local

                     350 ;.L491	.L497	static

                     351 ;oldNamePos	r10	local

                     352 ;.L492	.L498	static

                     353 ;.L493	.L496	static

                     354 

                     355 ;dirName	r0	param


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cjk1.s
                     356 ;startFileName	r7	param

                     357 ;fileInfo	r6	param

                     358 ;bufForName	r5	param

                     359 

                     360 	.section ".bss","awb"

                     361 .L490:

                     362 	.data

                     363 	.text

                     364 

                     365 

                     366 	.align	4

                     367 	.align	4

                     368 fs_findNext::

000001a8 e92d40f0    369 	stmfd	[sp]!,{r4-r7,lr}

000001ac e1a06000    370 	mov	r6,r0

000001b0 e5960008    371 	ldr	r0,[r6,8]

000001b4 e1a05001    372 	mov	r5,r1

000001b8 e3500000    373 	cmp	r0,0

000001bc 1a000016    374 	bne	.L539

000001c0 e5957004    375 	ldr	r7,[r5,4]

000001c4 e28f1000*   376 	adr	r1,.L535

000001c8 e1a00005    377 	mov	r0,r5

000001cc eb000000*   378 	bl	BufferView_writeStr

000001d0 e1a01005    379 	mov	r1,r5

000001d4 e1a00006    380 	mov	r0,r6

000001d8 eb000000*   381 	bl	CFGFS_findNext

000001dc e1a04000    382 	mov	r4,r0

000001e0 e3540001    383 	cmp	r4,1

000001e4 1a000018    384 	bne	.L544

000001e8 e5d60000    385 	ldrb	r0,[r6]

000001ec e3500000    386 	cmp	r0,0

000001f0 0a000015    387 	beq	.L544

000001f4 e1a00006    388 	mov	r0,r6

000001f8 eb000022*   389 	bl	fs_findClose

000001fc e5857004    390 	str	r7,[r5,4]

00000200 e1a02005    391 	mov	r2,r5

00000204 e1a01006    392 	mov	r1,r6

00000208 e3a00000    393 	mov	r0,0

0000020c ebffff95*   394 	bl	oscFindFirst

00000210 e1b04000    395 	movs	r4,r0

00000214 1a000011    396 	bne	.L548

00000218 ea00000d    397 	b	.L549

                     398 .L539:

0000021c e3500001    399 	cmp	r0,1

00000220 13a00001    400 	movne	r0,1

00000224 1a00000e    401 	bne	.L537

00000228 e28f1000*   402 	adr	r1,.L359

0000022c e1a00005    403 	mov	r0,r5

00000230 eb000000*   404 	bl	BufferView_writeStr

00000234 e1a01005    405 	mov	r1,r5

00000238 e1a00006    406 	mov	r0,r6

0000023c eb000000*   407 	bl	OSCFS_findNext

00000240 e1b04000    408 	movs	r4,r0

00000244 1a000005    409 	bne	.L548

00000248 ea000001    410 	b	.L549

                     411 .L544:

0000024c e3540000    412 	cmp	r4,0

00000250 1a000002    413 	bne	.L548

                     414 .L549:

00000254 e8950006    415 	ldmfd	[r5],{r1-r2}

00000258 e286000c    416 	add	r0,r6,12


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cjk1.s
0000025c eb000000*   417 	bl	StringView_init

                     418 .L548:

00000260 e1a00004    419 	mov	r0,r4

                     420 .L537:

00000264 e8bd80f0    421 	ldmfd	[sp]!,{r4-r7,pc}

                     422 	.endf	fs_findNext

                     423 	.align	4

                     424 .L359:

                     425 ;	"/osc/\000"

00000268 63736f2f    426 	.data.b	47,111,115,99

0000026c 002f       427 	.data.b	47,0

0000026e 0000       428 	.align 4

                     429 

                     430 	.type	.L359,$object

                     431 	.size	.L359,4

                     432 

                     433 .L534:

                     434 ;	"config\000"

00000270 666e6f63    435 	.data.b	99,111,110,102

00000274 6769       436 	.data.b	105,103

00000276 00         437 	.data.b	0

00000277 00         438 	.align 4

                     439 

                     440 	.type	.L534,$object

                     441 	.size	.L534,4

                     442 

                     443 .L535:

                     444 ;	"/config/\000"

00000278 6e6f632f    445 	.data.b	47,99,111,110

0000027c 2f676966    446 	.data.b	102,105,103,47

00000280 00         447 	.data.b	0

00000281 000000     448 	.align 4

                     449 

                     450 	.type	.L535,$object

                     451 	.size	.L535,4

                     452 

                     453 .L536:

                     454 ;	"osc\000"

00000284 0063736f    455 	.data.b	111,115,99,0

                     456 	.align 4

                     457 

                     458 	.type	.L536,$object

                     459 	.size	.L536,4

                     460 

                     461 	.align	4

                     462 ;result	r4	local

                     463 ;oldNamePos	r7	local

                     464 ;.L641	.L646	static

                     465 ;.L642	.L645	static

                     466 

                     467 ;fileInfo	r6	param

                     468 ;bufForName	r5	param

                     469 

                     470 	.section ".bss","awb"

                     471 .L640:

                     472 	.data

                     473 	.text

                     474 

                     475 

                     476 	.align	4

                     477 	.align	4


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cjk1.s
                     478 fs_findClose::

00000288 e5901008    479 	ldr	r1,[r0,8]

0000028c e3510000    480 	cmp	r1,0

00000290 0a000000*   481 	beq	CFGFS_findClose

00000294 e3510001    482 	cmp	r1,1

00000298 0a000000*   483 	beq	OSCFS_findClose

0000029c e12fff1e*   484 	ret	

                     485 	.endf	fs_findClose

                     486 	.align	4

                     487 

                     488 ;fileInfo	r0	param

                     489 

                     490 	.section ".bss","awb"

                     491 .L722:

                     492 	.data

                     493 	.text

                     494 

                     495 

                     496 ;200: 


                     497 ;201: bool fs_fileOpen(StringView* fullFileName, size_t startPos, uint32_t* frsmID,


                     498 	.align	4

                     499 	.align	4

                     500 	.align	4

                     501 fs_fileOpen::

000002a0 e92d40f0    502 	stmfd	[sp]!,{r4-r7,lr}

000002a4 e24dd014    503 	sub	sp,sp,20

000002a8 e1a07000    504 	mov	r7,r0

000002ac e1a04002    505 	mov	r4,r2

000002b0 e1a05003    506 	mov	r5,r3

                     507 ;202:     FSFileAttr* attr)


                     508 ;203: {


                     509 

                     510 ;204:     StringView dirName;


                     511 ;205:     StringView fileName;


                     512 ;206:     FRSM* frsm;


                     513 ;207: 


                     514 ;208:     splitFullFileName(fullFileName, &dirName, &fileName);


                     515 

                     516 ;50:     StringView* dirName, StringView* fileName)


                     517 ;51: {


                     518 

                     519 ;52:     size_t delimiterPos;


                     520 ;53:     if (findDelimiter(fullName, &delimiterPos))


                     521 

                     522 ;23: {


                     523 

                     524 ;24:     size_t pos = fullName->len;


                     525 

000002b4 e8970003    526 	ldmfd	[r7],{r0-r1}

000002b8 e1a03000    527 	mov	r3,r0

000002bc e1b06003    528 	movs	r6,r3

                     529 ;25:     while (pos > 0)


                     530 

000002c0 e0860001    531 	add	r0,r6,r1

                     532 ;29:         {


                     533 

                     534 ;30:             *delimiterPos = pos;


                     535 

                     536 ;31:             return TRUE;


                     537 

000002c4 0a000017    538 	beq	.L752


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cjk1.s
                     539 .L743:

                     540 ;26:     {


                     541 

                     542 ;27:         --pos;


                     543 

000002c8 e5702001    544 	ldrb	r2,[r0,-1]!

                     545 ;16: {


                     546 

                     547 ;17:     return c == '\\' || c == '/';


                     548 

000002cc e2466001    549 	sub	r6,r6,1

                     550 ;28:         if (isDelimiter(fullName->p[pos]))


                     551 

000002d0 e352005c    552 	cmp	r2,92

000002d4 1352002f    553 	cmpne	r2,47

000002d8 0a000002    554 	beq	.L751

                     555 ;29:         {


                     556 

                     557 ;30:             *delimiterPos = pos;


                     558 

                     559 ;31:             return TRUE;


                     560 

000002dc e3560000    561 	cmp	r6,0

000002e0 1afffff8    562 	bne	.L743

000002e4 ea00000f    563 	b	.L752

                     564 .L751:

                     565 ;32:         }


                     566 ;33:     }


                     567 ;34:     return FALSE;


                     568 

                     569 ;54:     {


                     570 

                     571 ;55:         //Разделитель найден


                     572 ;56:         //Имя файла


                     573 ;57:         size_t fileNameStart = delimiterPos + 1;


                     574 

000002e8 e2860001    575 	add	r0,r6,1

                     576 ;58:         StringView_init(fileName, fullName->p + fileNameStart,


                     577 

000002ec e0432000    578 	sub	r2,r3,r0

000002f0 e0801001    579 	add	r1,r0,r1

000002f4 e28d0004    580 	add	r0,sp,4

000002f8 eb000000*   581 	bl	StringView_init

                     582 ;59:             fullName->len - fileNameStart);


                     583 ;60:         //Директорий


                     584 ;61:         StringView_init(dirName, fullName->p, delimiterPos);


                     585 

000002fc e1a02006    586 	mov	r2,r6

00000300 e5971004    587 	ldr	r1,[r7,4]

00000304 e28d000c    588 	add	r0,sp,12

00000308 eb000000*   589 	bl	StringView_init

                     590 ;62:         //Срезаем лидирующий разделитель, если есть


                     591 ;63:         stripLeftDelim(dirName);


                     592 

0000030c e28d000c    593 	add	r0,sp,12

00000310 ebffff3a*   594 	bl	stripLeftDelim

00000314 e1a00004    595 	mov	r0,r4

00000318 eb000000*   596 	bl	frsm_alloc

                     597 ;209:     if( ! frsm_alloc(frsmID))


                     598 

0000031c e3500000    599 	cmp	r0,0


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cjk1.s
00000320 0a00000a    600 	beq	.L755

00000324 ea00000a    601 	b	.L754

                     602 .L752:

                     603 ;64:     }


                     604 ;65:     else


                     605 ;66:     {


                     606 

                     607 ;67:         //Разделитель не найден.


                     608 ;68:         //Только имя файла. Директорий пустой.


                     609 ;69:         StringView_init(dirName, fullName->p, 0);


                     610 

00000328 e28d000c    611 	add	r0,sp,12

0000032c e3a02000    612 	mov	r2,0

00000330 eb000000*   613 	bl	StringView_init

                     614 ;70:         StringView_init(fileName, fullName->p, fullName->len);


                     615 

00000334 e8970003    616 	ldmfd	[r7],{r0-r1}

00000338 e1a02000    617 	mov	r2,r0

0000033c e28d0004    618 	add	r0,sp,4

00000340 eb000000*   619 	bl	StringView_init

00000344 e1a00004    620 	mov	r0,r4

00000348 eb000000*   621 	bl	frsm_alloc

                     622 ;209:     if( ! frsm_alloc(frsmID))


                     623 

0000034c e3500000    624 	cmp	r0,0

                     625 .L755:

                     626 ;210:     {


                     627 

                     628 ;211:         return FALSE;


                     629 

00000350 0a000021    630 	beq	.L738

                     631 .L754:

                     632 ;212:     }


                     633 ;213:     frsm_getById(*frsmID, &frsm);


                     634 

00000354 e5940000    635 	ldr	r0,[r4]

00000358 e1a0100d    636 	mov	r1,sp

0000035c eb000000*   637 	bl	frsm_getById

00000360 e59f113c*   638 	ldr	r1,.L1061

00000364 e28d000c    639 	add	r0,sp,12

00000368 eb000000*   640 	bl	StringView_cmpCStr

                     641 ;214:     if(!fileOpen(&dirName, &fileName, frsm, attr))


                     642 

                     643 ;186:                        FSFileAttr* attr)


                     644 ;187: {


                     645 

                     646 ;188:         if(StringView_cmpCStr(dirName, CFG_DIR_NAME) == 0 )


                     647 

0000036c e3500000    648 	cmp	r0,0

00000370 1a000007    649 	bne	.L763

                     650 ;189:         {


                     651 

                     652 ;190:             frsm->subsystem = FS_SUB_CFG;


                     653 

00000374 e59d1000    654 	ldr	r1,[sp]

00000378 e1a02005    655 	mov	r2,r5

0000037c e5810004    656 	str	r0,[r1,4]

                     657 ;191:             return CFGFS_openFile(fileName, frsm, attr);


                     658 

00000380 e28d0004    659 	add	r0,sp,4

00000384 eb000000*   660 	bl	CFGFS_openFile


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cjk1.s
00000388 e3500000    661 	cmp	r0,0

0000038c 0a00000d    662 	beq	.L758

00000390 ea000010    663 	b	.L757

                     664 .L763:

                     665 ;192:         }


                     666 ;193:         else if(StringView_cmpCStr(dirName, OSC_DIR_NAME) == 0 )


                     667 

00000394 e59f110c*   668 	ldr	r1,.L1062

00000398 e28d000c    669 	add	r0,sp,12

0000039c eb000000*   670 	bl	StringView_cmpCStr

000003a0 e3500000    671 	cmp	r0,0

                     672 ;197:         }


                     673 ;198:         return FALSE;


                     674 

000003a4 1a000007    675 	bne	.L758

                     676 ;194:         {


                     677 

                     678 ;195:             frsm->subsystem = FS_SUB_OSC;


                     679 

000003a8 e1a02005    680 	mov	r2,r5

000003ac e59d1000    681 	ldr	r1,[sp]

000003b0 e3a00001    682 	mov	r0,1

000003b4 e5810004    683 	str	r0,[r1,4]

                     684 ;196:             return OSCFS_openFile(fileName, frsm, attr);


                     685 

000003b8 e28d0004    686 	add	r0,sp,4

000003bc eb000000*   687 	bl	OSCFS_openFile

000003c0 e3500000    688 	cmp	r0,0

000003c4 1a000003    689 	bne	.L757

                     690 .L758:

                     691 ;215:     {


                     692 

                     693 ;216:         frsm_free(*frsmID);


                     694 

000003c8 e5940000    695 	ldr	r0,[r4]

000003cc eb000000*   696 	bl	frsm_free

                     697 ;217:         return FALSE;


                     698 

000003d0 e3a00000    699 	mov	r0,0

000003d4 ea000000    700 	b	.L738

                     701 .L757:

                     702 ;218:     }


                     703 ;219:     return TRUE;


                     704 

000003d8 e3a00001    705 	mov	r0,1

                     706 .L738:

000003dc e28dd014    707 	add	sp,sp,20

000003e0 e8bd80f0    708 	ldmfd	[sp]!,{r4-r7,pc}

                     709 	.endf	fs_fileOpen

                     710 	.align	4

                     711 ;dirName	[sp,12]	local

                     712 ;fileName	[sp,4]	local

                     713 ;frsm	[sp]	local

                     714 ;pos	r6	local

                     715 ;c	r2	local

                     716 ;fileNameStart	r0	local

                     717 

                     718 ;fullFileName	r7	param

                     719 ;startPos	none	param

                     720 ;frsmID	r4	param

                     721 ;attr	r5	param


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cjk1.s
                     722 

                     723 	.section ".bss","awb"

                     724 .L1014:

                     725 	.section ".rodata","a"

                     726 .L1015:

                     727 __UNNAMED_2_static_in_fileOpen:;	"osc\000"

00000000 0063736f    728 	.data.b	111,115,99,0

                     729 	.type	__UNNAMED_2_static_in_fileOpen,$object

                     730 	.size	__UNNAMED_2_static_in_fileOpen,4

                     731 .L1016:

                     732 __UNNAMED_1_static_in_fileOpen:;	"config\000"

00000004 666e6f63    733 	.data.b	99,111,110,102

00000008 6769       734 	.data.b	105,103

0000000a 00         735 	.data.b	0

0000000b 00         736 	.space	1

                     737 	.type	__UNNAMED_1_static_in_fileOpen,$object

                     738 	.size	__UNNAMED_1_static_in_fileOpen,8

                     739 	.data

                     740 	.text

                     741 

                     742 ;220: }


                     743 

                     744 ;221: 


                     745 ;222: bool fs_fileClose(uint32_t frsmID)


                     746 	.align	4

                     747 	.align	4

                     748 fs_fileClose::

000003e4 e92d4030    749 	stmfd	[sp]!,{r4-r5,lr}

000003e8 e24dd004    750 	sub	sp,sp,4

000003ec e1a0100d    751 	mov	r1,sp

000003f0 e1a05000    752 	mov	r5,r0

000003f4 eb000000*   753 	bl	frsm_getById

                     754 ;223: {


                     755 

                     756 ;224:     FRSM* frsm;


                     757 ;225:     bool result;


                     758 ;226:     if (!frsm_getById(frsmID, &frsm))


                     759 

000003f8 e3500000    760 	cmp	r0,0

                     761 ;227:     {


                     762 

                     763 ;228:         return FALSE;


                     764 

000003fc 0a000010    765 	beq	.L1063

                     766 ;229:     }


                     767 ;230:     switch (frsm->subsystem)


                     768 

00000400 e59d0000    769 	ldr	r0,[sp]

00000404 e5901004    770 	ldr	r1,[r0,4]

00000408 e3510001    771 	cmp	r1,1

0000040c 3a000002    772 	blo	.L1070

                     773 ;237:         break;


                     774 ;238:     default:


                     775 ;239:         result = FALSE;


                     776 

00000410 13a00000    777 	movne	r0,0

                     778 ;244:     }


                     779 ;245:     return result;


                     780 

00000414 1a00000a    781 	bne	.L1063

00000418 ea000003    782 	b	.L1071


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cjk1.s
                     783 .L1070:

                     784 ;231:     {


                     785 ;232:     case FS_SUB_CFG:


                     786 ;233:         result = CFGFS_closeFile(frsm);


                     787 

0000041c eb000000*   788 	bl	CFGFS_closeFile

00000420 e1b04000    789 	movs	r4,r0

                     790 ;240:     }


                     791 ;241:     if (result)


                     792 

00000424 0a000005    793 	beq	.L1073

00000428 ea000002    794 	b	.L1074

                     795 .L1071:

                     796 ;234:         break;


                     797 ;235:     case FS_SUB_OSC:


                     798 ;236:         result = OSCFS_closeFile(frsm);


                     799 

0000042c eb000000*   800 	bl	OSCFS_closeFile

00000430 e1b04000    801 	movs	r4,r0

                     802 ;240:     }


                     803 ;241:     if (result)


                     804 

00000434 0a000001    805 	beq	.L1073

                     806 .L1074:

                     807 ;242:     {


                     808 

                     809 ;243:         frsm_free(frsmID);


                     810 

00000438 e1a00005    811 	mov	r0,r5

0000043c eb000000*   812 	bl	frsm_free

                     813 .L1073:

                     814 ;244:     }


                     815 ;245:     return result;


                     816 

00000440 e1a00004    817 	mov	r0,r4

                     818 .L1063:

00000444 e28dd004    819 	add	sp,sp,4

00000448 e8bd8030    820 	ldmfd	[sp]!,{r4-r5,pc}

                     821 	.endf	fs_fileClose

                     822 	.align	4

                     823 ;frsm	[sp]	local

                     824 ;result	r4	local

                     825 

                     826 ;frsmID	r5	param

                     827 

                     828 	.section ".bss","awb"

                     829 .L1191:

                     830 	.data

                     831 	.text

                     832 

                     833 ;246: }


                     834 

                     835 ;247: 


                     836 ;248: bool fs_fileRead(uint32_t frsmID, BufferView* fileReadBuf, bool *moreFollows)


                     837 	.align	4

                     838 	.align	4

                     839 fs_fileRead::

0000044c e92d4030    840 	stmfd	[sp]!,{r4-r5,lr}

00000450 e1a05002    841 	mov	r5,r2

00000454 e24dd004    842 	sub	sp,sp,4

00000458 e1a04001    843 	mov	r4,r1


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cjk1.s
0000045c e1a0100d    844 	mov	r1,sp

00000460 eb000000*   845 	bl	frsm_getById

                     846 ;249: {


                     847 

                     848 ;250:     FRSM* frsm;


                     849 ;251:     bool result;


                     850 ;252:     if (!frsm_getById(frsmID, &frsm))


                     851 

00000464 e3500000    852 	cmp	r0,0

                     853 ;253:     {


                     854 

                     855 ;254:         return FALSE;


                     856 

00000468 0a00000b    857 	beq	.L1219

                     858 ;255:     }


                     859 ;256:     switch (frsm->subsystem)


                     860 

0000046c e59d0000    861 	ldr	r0,[sp]

00000470 e5901004    862 	ldr	r1,[r0,4]

00000474 e3510001    863 	cmp	r1,1

00000478 3a000004    864 	blo	.L1226

                     865 ;263:         break;


                     866 ;264:     default:


                     867 ;265:         result = FALSE;


                     868 

0000047c 13a00000    869 	movne	r0,0

                     870 ;266:     }


                     871 ;267:     return result;


                     872 

                     873 ;260:         break;


                     874 ;261:     case FS_SUB_OSC:


                     875 ;262:         result = OSCFS_readFile(frsm, fileReadBuf, moreFollows);


                     876 

00000480 01a02005    877 	moveq	r2,r5

00000484 01a01004    878 	moveq	r1,r4

00000488 0b000000*   879 	bleq	OSCFS_readFile

                     880 ;266:     }


                     881 ;267:     return result;


                     882 

0000048c ea000002    883 	b	.L1219

                     884 .L1226:

                     885 ;257:     {


                     886 ;258:     case FS_SUB_CFG:


                     887 ;259:         result = CFGFS_readFile(frsm, fileReadBuf, moreFollows);


                     888 

00000490 e1a02005    889 	mov	r2,r5

00000494 e1a01004    890 	mov	r1,r4

00000498 eb000000*   891 	bl	CFGFS_readFile

                     892 ;266:     }


                     893 ;267:     return result;


                     894 

                     895 .L1219:

0000049c e28dd004    896 	add	sp,sp,4

000004a0 e8bd8030    897 	ldmfd	[sp]!,{r4-r5,pc}

                     898 	.endf	fs_fileRead

                     899 	.align	4

                     900 ;frsm	[sp]	local

                     901 

                     902 ;frsmID	none	param

                     903 ;fileReadBuf	r4	param

                     904 ;moreFollows	r5	param


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cjk1.s
                     905 

                     906 	.section ".bss","awb"

                     907 .L1306:

                     908 	.data

                     909 	.text

                     910 

                     911 ;268: }


                     912 	.align	4

                     913 .L1061:

000004a4 00000000*   914 	.data.w	.L1016

                     915 	.type	.L1061,$object

                     916 	.size	.L1061,4

                     917 

                     918 .L1062:

000004a8 00000000*   919 	.data.w	.L1015

                     920 	.type	.L1062,$object

                     921 	.size	.L1062,4

                     922 

                     923 	.align	4

                     924 ;__UNNAMED_1_static_in_fileOpen	.L1016	static

                     925 ;__UNNAMED_2_static_in_fileOpen	.L1015	static

                     926 

                     927 	.data

                     928 	.ghsnote version,6

                     929 	.ghsnote tools,3

                     930 	.ghsnote options,0

                     931 	.text

                     932 	.align	4

                     933 	.section ".rodata","a"

                     934 	.align	4

                     935 	.text

