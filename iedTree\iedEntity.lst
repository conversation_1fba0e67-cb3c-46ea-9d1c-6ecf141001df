                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedEntity.c -o iedTree\gh_bns1.o -list=iedTree/iedEntity.lst C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
Source File: iedEntity.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		iedTree/iedEntity.c -o iedTree/iedEntity.o

                      12 ;Source File:   iedTree/iedEntity.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:48 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "iedEntity.h"


                      23 ;2: 


                      24 ;3: #include "..\MemoryManager.h"


                      25 ;4: #include "..\bufViewBER.h"


                      26 ;5: #include "..\iedmodel.h"


                      27 ;6: #include "..\BaseAsnTypes.h"


                      28 ;7: #include "iedFC.h"


                      29 ;8: #include "iedObjects.h"


                      30 ;9: #include "iedFinalDA.h"


                      31 ;10: #include "iedTimeStamp.h"


                      32 ;11: #include "DataSet.h"


                      33 ;12: #include "..\IsoConnectionForward.h"


                      34 ;13: 


                      35 ;14: #include <string.h>


                      36 ;15: #include <stdlib.h>


                      37 ;16: 


                      38 ;17: #include "debug.h"


                      39 ;18: 


                      40 ;19: static bool createChildrenFromBER(IEDEntity parent,


                      41 ;20:     BufferView* childrenBER);


                      42 ;21: 


                      43 ;22: void *IEDEntity_alloc(size_t size)


                      44 ;23: {


                      45 ;24:     void* p = MM_alloc(size);


                      46 ;25:     if(p != NULL)


                      47 ;26:     {


                      48 ;27:         memset(p, 0, size);


                      49 ;28:     }


                      50 ;29:     return p;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
                      51 ;30: }


                      52 ;31: 


                      53 ;32: // Заглушка типа IEDEntity_OnNewDataSlice


                      54 ;33: static void updateFromDataSliceStub(IEDEntity da)


                      55 

                      56 ;35:     ERROR_REPORT("Stub called:onNewDataSliceStub");


                      57 ;36: }


                      58 

                      59 ;37: 


                      60 ;38: static bool calcReadLen(IEDEntity entity, size_t* pLen )


                      61 ;39: {


                      62 ;40:     size_t len;


                      63 ;41:     bool result;


                      64 ;42:     switch (entity->type)


                      65 ;43:     {


                      66 ;44:     case IED_ENTITY_DA_TERMINAL_ITEM:


                      67 ;45:         result = IEDTermItemDA_calcReadLen(entity, &len);


                      68 ;46:         break;


                      69 ;47:     case IED_ENTITY_DA_TIMESTAMP:


                      70 ;48:         result = IEDTimeStampDA_calcReadLen(entity, &len);


                      71 ;49:         break;


                      72 ;50:     case IED_ENTITY_DA_CONST:


                      73 ;51:         result = IEDConstDA_calcReadLen(entity, &len);


                      74 ;52:         break;


                      75 ;53:     case IED_ENTITY_DA_VAR:


                      76 ;54:         result = IEDVarDA_calcReadLen(entity, &len);


                      77 ;55:         break;


                      78 ;56:     case IED_ENTITY_LN:


                      79 ;57:     case IED_ENTITY_FC:


                      80 ;58:     case IED_ENTITY_DO:


                      81 ;59:     case IED_ENTITY_DA:


                      82 ;60:         result = IEDComplexObj_calcReadLen(entity, &len);


                      83 ;61:         break;


                      84 ;62:     default:


                      85 ;63:         ERROR_REPORT("Invalid object to read");


                      86 ;64:         return false;


                      87 ;65:     }


                      88 ;66:     *pLen = len;


                      89 ;67:     return result;


                      90 ;68: }


                      91 ;69: 


                      92 ;70: static bool encodeRead(IEDEntity entity, BufferView* outBuf)


                      93 ;71: {


                      94 ;72:     switch (entity->type)


                      95 ;73:     {


                      96 ;74:     case IED_ENTITY_DA_TERMINAL_ITEM:


                      97 ;75:         return IEDTermItemDA_encodeRead(entity, outBuf);


                      98 ;76:     case IED_ENTITY_DA_TIMESTAMP:


                      99 ;77:         return IEDTimeStampDA_encodeRead(entity, outBuf);


                     100 ;78:     case IED_ENTITY_DA_CONST:


                     101 ;79:         return IEDConstDA_encodeRead(entity, outBuf);


                     102 ;80:     case IED_ENTITY_DA_VAR:


                     103 ;81:         return IEDVarDA_encodeRead(entity, outBuf);


                     104 ;82:     case IED_ENTITY_LN:


                     105 ;83:     case IED_ENTITY_FC:


                     106 ;84:     case IED_ENTITY_DO:


                     107 ;85:     case IED_ENTITY_DA:


                     108 ;86:         return IEDComplexObj_encodeRead(entity, outBuf);


                     109 ;87:     default:


                     110 ;88:         ERROR_REPORT("Invalid object to read");


                     111 ;89:         return false;



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
                     112 ;90:     }


                     113 ;91: }


                     114 ;92: 


                     115 ;93: static MmsDataAccessError write(IEDEntity entity, IsoConnection* isoConn,


                     116 ;94:                                    BufferView* value)


                     117 ;95: {


                     118 ;96:     switch (entity->type)


                     119 ;97:     {


                     120 ;98:     case IED_ENTITY_DA_TERMINAL_ITEM:


                     121 ;99:         return IEDTermItemDA_write(entity, isoConn, value);


                     122 ;100:     case IED_ENTITY_DA_TIMESTAMP:


                     123 ;101:         return IEDTimeStampDA_write(entity, value);


                     124 ;102:     case IED_ENTITY_DA_CONST:


                     125 ;103:         //Константы специально игнорируем


                     126 ;104:         if(!BufferView_skipAnyObject(value))


                     127 ;105:         {


                     128 ;106:             return DATA_ACCESS_ERROR_UNKNOWN;


                     129 ;107:         }


                     130 ;108:         return DATA_ACCESS_ERROR_SUCCESS;


                     131 ;109:     case IED_ENTITY_DA_VAR:


                     132 ;110:         return IEDVarDA_write(entity, isoConn, value);


                     133 ;111:     case IED_ENTITY_DA:


                     134 ;112:     case IED_ENTITY_DO:


                     135 ;113:         return IEDComplexObj_write(entity, isoConn, value);


                     136 ;114:     default:


                     137 ;115:         ERROR_REPORT("Invalid object to write");


                     138 ;116:         return DATA_ACCESS_ERROR_OBJECT_ACCESS_UNSUPPORTED;


                     139 ;117:     }


                     140 ;118: }


                     141 ;119: 


                     142 ;120: 


                     143 ;121: void IEDEntity_addChild(IEDEntity parent, IEDEntity child)


                     144 ;122: {


                     145 ;123:     child->parent = parent;


                     146 ;124:     child->next = NULL;


                     147 ;125:     if (parent->firstChild == NULL)


                     148 ;126:     {


                     149 ;127:         parent->firstChild = child;


                     150 ;128:     }


                     151 ;129:     else


                     152 ;130:     {


                     153 ;131:         parent->lastChild->next = child;


                     154 ;132:     }


                     155 ;133:     parent->lastChild = child;


                     156 ;134: }


                     157 ;135: 


                     158 ;136: void IEDEntity_init(IEDEntity iedEntity, IEDEntity parent)


                     159 ;137: {


                     160 ;138:     //Для всех объектов ставим заглушку.


                     161 ;139:     //Потом каждый объект заменит её на то что ему надо.


                     162 ;140:     iedEntity->updateFromDataSlice = updateFromDataSliceStub;


                     163 ;141: 


                     164 ;142:     // Эти функции общие для всех элементов


                     165 ;143:     // они внутри используют switch по типу элемента.


                     166 ;144:     // Со временем можно постепенно заменить их на индивидуальные


                     167 ;145:     // для каждого.


                     168 ;146:     iedEntity->encodeRead = encodeRead;


                     169 ;147:     iedEntity->calcReadLen = calcReadLen;


                     170 ;148:     iedEntity->write = write;


                     171 ;149: 


                     172 ;150:     // По умолчанию невалидное значение



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
                     173 ;151:     iedEntity->dataSliceOffset = -1;


                     174 ;152: 


                     175 ;153: 


                     176 ;154:     if(parent != NULL)


                     177 ;155:     {


                     178 ;156:         IEDEntity_addChild(parent, iedEntity);


                     179 ;157:     }


                     180 ;158: }


                     181 ;159: 


                     182 ;160: bool IEDEntity_create(IEDEntity* iedEntity, IEDEntity parent)


                     183 ;161: {


                     184 ;162:     size_t size = sizeof(struct IEDEntityStruct);


                     185 ;163:     *iedEntity = IEDEntity_alloc(size);


                     186 ;164:     if(*iedEntity == NULL)


                     187 ;165:     {


                     188 ;166:         ERROR_REPORT("IEDEntity allocation error");


                     189 ;167:         return false;


                     190 ;168:     }


                     191 ;169:     IEDEntity_init(*iedEntity, parent);


                     192 ;170:     return true;


                     193 ;171: }


                     194 ;172: 


                     195 ;173: bool IEDEntity_postCreate(IEDEntity entity)


                     196 ;174: {


                     197 ;175:     IEDEntity nextChild;


                     198 ;176: 


                     199 ;177:     //Вызываем postCreate детей


                     200 ;178:     nextChild = entity->firstChild;


                     201 ;179:     while(nextChild != NULL)


                     202 ;180:     {


                     203 ;181:         bool result = IEDEntity_postCreate(nextChild);


                     204 ;182:         if(!result)


                     205 ;183:         {


                     206 ;184:             return false;


                     207 ;185:         }


                     208 ;186:         nextChild = nextChild->next;


                     209 ;187:     }


                     210 ;188: 


                     211 ;189:     //Выполняем postCreate для себя


                     212 ;190:     switch(entity->tag) {


                     213 ;191:     case IED_DATA_SET:


                     214 ;192:         return DataSet_postCreate(entity);


                     215 ;193:     }


                     216 ;194:     return true;


                     217 ;195: }


                     218 ;196: 


                     219 ;197: 


                     220 ;198: static bool initSpecific(IEDEntity entity)


                     221 

                     222 ;218: }


                     223 

                     224 ;219: 


                     225 ;220: // Заполняет служебную информацию  любого IEDEntiy из BER


                     226 ;221: // вроде имени и флагов


                     227 ;222: // ber должен указывать на начало содержимого объекта


                     228 ;223: //(то есть сразу после длины).


                     229 ;224: // При успешном завершении ber указывает на буфер сразу


                     230 ;225: // за параметрами.


                     231 ;226: static bool readServiceInfo(IEDEntity entity, BufferView* ber)


                     232 

                     233 ;283: }



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
                     234 

                     235 ;284: 


                     236 ;285: bool IEDEntity_createFromBER(IEDEntity* iedEntity, BufferView* ber,


                     237 ;286:                              IEDEntity parent)


                     238 ;287: {


                     239 ;288:     uint8_t tag;


                     240 ;289:     size_t len, fullLen;


                     241 ;290:     BufferView berObject;


                     242 ;291:     IEDEntity entity;


                     243 ;292:     uint8_t* pObjectBER = ber->p + ber->pos;


                     244 ;293: 


                     245 ;294:     if(!IEDEntity_create(&entity, parent))


                     246 ;295:     {


                     247 ;296:         ERROR_REPORT("IEDEntity_create error");


                     248 ;297:         return false;


                     249 ;298:     }


                     250 ;299: 


                     251 ;300:     *iedEntity = entity;


                     252 ;301: 


                     253 ;302:     //Получить размер объекта


                     254 ;303:     if(!BufferView_decodeTL(ber, &tag, &len, &fullLen))


                     255 ;304:     {


                     256 ;305:         return false;


                     257 ;306:     }


                     258 ;307:     entity->tag = tag;


                     259 ;308: 


                     260 ;309:     BufferView_init(&entity->ber, pObjectBER, fullLen, 0 );


                     261 ;310: 


                     262 ;311:     BufferView_init(&berObject, ber->p + ber->pos, len, 0);


                     263 ;312: 


                     264 ;313:     //Перемещаем позицию буфера за объект.


                     265 ;314:     //Дальше будем действовать через его собственный буфер berObject


                     266 ;315:     if(!BufferView_advance(ber,len))


                     267 ;316:     {


                     268 ;317:         return false;


                     269 ;318:     }


                     270 ;319: 


                     271 ;320:     if(! readServiceInfo(entity, &berObject))


                     272 ;321:     {


                     273 ;322:         return false;


                     274 ;323:     }


                     275 ;324: 


                     276 ;325:     //Дети


                     277 ;326:     if (tag != IED_DA_FINAL && tag != IED_DATA_SET)


                     278 ;327:     {


                     279 ;328:         if(!createChildrenFromBER(entity, &berObject))


                     280 ;329:         {


                     281 ;330:             return false;


                     282 ;331:         }


                     283 ;332:     }


                     284 ;333:     //Инициализация специфических особенностей объекта делается


                     285 ;334:     //после полной инициализации его детей.


                     286 ;335:     //Т.е. тип(или подтип) родителя может зависеть от типа детей


                     287 ;336:     return initSpecific(entity);


                     288 ;337: }


                     289 ;338: 


                     290 ;339: bool createChildrenFromBER(IEDEntity parent,


                     291 

                     292 ;352: }


                     293 

                     294 	.text


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
                     295 	.align	4

                     296 IEDEntity_alloc::

00000000 e92d4030    297 	stmfd	[sp]!,{r4-r5,lr}

00000004 e1a05000    298 	mov	r5,r0

00000008 eb000000*   299 	bl	MM_alloc

0000000c e1b04000    300 	movs	r4,r0

00000010 11a02005    301 	movne	r2,r5

00000014 13a01000    302 	movne	r1,0

00000018 1b000000*   303 	blne	memset

0000001c e1a00004    304 	mov	r0,r4

00000020 e8bd8030    305 	ldmfd	[sp]!,{r4-r5,pc}

                     306 	.endf	IEDEntity_alloc

                     307 	.align	4

                     308 ;p	r4	local

                     309 

                     310 ;size	r5	param

                     311 

                     312 	.section ".bss","awb"

                     313 .L189:

                     314 	.data

                     315 	.text

                     316 

                     317 

                     318 	.align	4

                     319 	.align	4

                     320 calcReadLen:

00000024 e92d4010    321 	stmfd	[sp]!,{r4,lr}

00000028 e24dd004    322 	sub	sp,sp,4

0000002c e5902050    323 	ldr	r2,[r0,80]

00000030 e1a04001    324 	mov	r4,r1

00000034 e2522003    325 	subs	r2,r2,3

00000038 e2522004    326 	subs	r2,r2,4

0000003c 3a00001b    327 	blo	.L210

00000040 0a000010    328 	beq	.L208

00000044 e2522002    329 	subs	r2,r2,2

00000048 3a000004    330 	blo	.L206

0000004c 0a000008    331 	beq	.L207

00000050 e3520001    332 	cmp	r2,1

00000054 13a00000    333 	movne	r0,0

00000058 1a000018    334 	bne	.L202

0000005c ea00000e    335 	b	.L209

                     336 .L206:

00000060 e1a0100d    337 	mov	r1,sp

00000064 eb000000*   338 	bl	IEDTermItemDA_calcReadLen

00000068 e59d1000    339 	ldr	r1,[sp]

0000006c e5841000    340 	str	r1,[r4]

00000070 ea000012    341 	b	.L202

                     342 .L207:

00000074 e1a0100d    343 	mov	r1,sp

00000078 eb000000*   344 	bl	IEDTimeStampDA_calcReadLen

0000007c e59d1000    345 	ldr	r1,[sp]

00000080 e5841000    346 	str	r1,[r4]

00000084 ea00000d    347 	b	.L202

                     348 .L208:

00000088 e1a0100d    349 	mov	r1,sp

0000008c eb000000*   350 	bl	IEDConstDA_calcReadLen

00000090 e59d1000    351 	ldr	r1,[sp]

00000094 e5841000    352 	str	r1,[r4]

00000098 ea000008    353 	b	.L202

                     354 .L209:

0000009c e1a0100d    355 	mov	r1,sp


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
000000a0 eb000000*   356 	bl	IEDVarDA_calcReadLen

000000a4 e59d1000    357 	ldr	r1,[sp]

000000a8 e5841000    358 	str	r1,[r4]

000000ac ea000003    359 	b	.L202

                     360 .L210:

000000b0 e1a0100d    361 	mov	r1,sp

000000b4 eb000000*   362 	bl	IEDComplexObj_calcReadLen

000000b8 e59d1000    363 	ldr	r1,[sp]

000000bc e5841000    364 	str	r1,[r4]

                     365 .L202:

000000c0 e28dd004    366 	add	sp,sp,4

000000c4 e8bd4010    367 	ldmfd	[sp]!,{r4,lr}

000000c8 e12fff1e*   368 	ret	

                     369 	.endf	calcReadLen

                     370 	.align	4

                     371 ;len	[sp]	local

                     372 ;result	r0	local

                     373 

                     374 ;entity	r0	param

                     375 ;pLen	r4	param

                     376 

                     377 	.section ".bss","awb"

                     378 .L276:

                     379 	.data

                     380 	.text

                     381 

                     382 

                     383 	.align	4

                     384 	.align	4

                     385 encodeRead:

000000cc e92d4000    386 	stmfd	[sp]!,{lr}

000000d0 e5902050    387 	ldr	r2,[r0,80]

000000d4 e2522003    388 	subs	r2,r2,3

000000d8 e2522004    389 	subs	r2,r2,4

000000dc 3a00000d    390 	blo	.L314

000000e0 0a00000a    391 	beq	.L310

000000e4 e2522002    392 	subs	r2,r2,2

000000e8 3a000004    393 	blo	.L306

000000ec 0a000005    394 	beq	.L308

000000f0 e3520001    395 	cmp	r2,1

000000f4 13a00000    396 	movne	r0,0

000000f8 0b000000*   397 	bleq	IEDVarDA_encodeRead

000000fc ea000006    398 	b	.L302

                     399 .L306:

00000100 eb000000*   400 	bl	IEDTermItemDA_encodeRead

00000104 ea000004    401 	b	.L302

                     402 .L308:

00000108 eb000000*   403 	bl	IEDTimeStampDA_encodeRead

0000010c ea000002    404 	b	.L302

                     405 .L310:

00000110 eb000000*   406 	bl	IEDConstDA_encodeRead

00000114 ea000000    407 	b	.L302

                     408 .L314:

00000118 eb000000*   409 	bl	IEDComplexObj_encodeRead

                     410 .L302:

0000011c e8bd4000    411 	ldmfd	[sp]!,{lr}

00000120 e12fff1e*   412 	ret	

                     413 	.endf	encodeRead

                     414 	.align	4

                     415 

                     416 ;entity	r0	param


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
                     417 ;outBuf	r1	param

                     418 

                     419 	.section ".bss","awb"

                     420 .L367:

                     421 	.data

                     422 	.text

                     423 

                     424 

                     425 	.align	4

                     426 	.align	4

                     427 write:

00000124 e92d4000    428 	stmfd	[sp]!,{lr}

00000128 e5903050    429 	ldr	r3,[r0,80]

0000012c e2533005    430 	subs	r3,r3,5

00000130 e2533002    431 	subs	r3,r3,2

00000134 3a000012    432 	blo	.L407

00000138 0a00000b    433 	beq	.L400

0000013c e2533002    434 	subs	r3,r3,2

00000140 3a000004    435 	blo	.L396

00000144 0a000005    436 	beq	.L398

00000148 e3530001    437 	cmp	r3,1

0000014c 13a00009    438 	movne	r0,9

00000150 0b000000*   439 	bleq	IEDVarDA_write

00000154 ea00000b    440 	b	.L392

                     441 .L396:

00000158 eb000000*   442 	bl	IEDTermItemDA_write

0000015c ea000009    443 	b	.L392

                     444 .L398:

00000160 e1a01002    445 	mov	r1,r2

00000164 eb000000*   446 	bl	IEDTimeStampDA_write

00000168 ea000006    447 	b	.L392

                     448 .L400:

0000016c e1a00002    449 	mov	r0,r2

00000170 eb000000*   450 	bl	BufferView_skipAnyObject

00000174 e3500000    451 	cmp	r0,0

00000178 13e00000    452 	mvnne	r0,0

0000017c 03a0000c    453 	moveq	r0,12

00000180 ea000000    454 	b	.L392

                     455 .L407:

00000184 eb000000*   456 	bl	IEDComplexObj_write

                     457 .L392:

00000188 e8bd4000    458 	ldmfd	[sp]!,{lr}

0000018c e12fff1e*   459 	ret	

                     460 	.endf	write

                     461 	.align	4

                     462 

                     463 ;entity	r0	param

                     464 ;isoConn	r1	param

                     465 ;value	r2	param

                     466 

                     467 	.section ".bss","awb"

                     468 .L462:

                     469 	.data

                     470 	.text

                     471 

                     472 

                     473 	.align	4

                     474 	.align	4

                     475 IEDEntity_addChild::

00000190 e5810000    476 	str	r0,[r1]

00000194 e3a02000    477 	mov	r2,0


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
00000198 e581200c    478 	str	r2,[r1,12]

0000019c e5902004    479 	ldr	r2,[r0,4]

000001a0 e3520000    480 	cmp	r2,0

000001a4 15902008    481 	ldrne	r2,[r0,8]

000001a8 05801004    482 	streq	r1,[r0,4]

000001ac 1582100c    483 	strne	r1,[r2,12]

000001b0 e5801008    484 	str	r1,[r0,8]

000001b4 e12fff1e*   485 	ret	

                     486 	.endf	IEDEntity_addChild

                     487 	.align	4

                     488 

                     489 ;parent	r0	param

                     490 ;child	r1	param

                     491 

                     492 	.section ".bss","awb"

                     493 .L522:

                     494 	.data

                     495 	.text

                     496 

                     497 

                     498 	.align	4

                     499 	.align	4

                     500 IEDEntity_init::

000001b8 e1b02001    501 	movs	r2,r1

000001bc e59f12a4*   502 	ldr	r1,.L579

000001c0 e5801068    503 	str	r1,[r0,104]

000001c4 e59f12a0*   504 	ldr	r1,.L580

000001c8 e59f32a0*   505 	ldr	r3,.L581

000001cc e5801060    506 	str	r1,[r0,96]

000001d0 e59f129c*   507 	ldr	r1,.L582

000001d4 e580305c    508 	str	r3,[r0,92]

000001d8 e5801064    509 	str	r1,[r0,100]

000001dc e3e01000    510 	mvn	r1,0

000001e0 e580102c    511 	str	r1,[r0,44]

000001e4 11a01000    512 	movne	r1,r0

000001e8 11a00002    513 	movne	r0,r2

000001ec 1affffe7*   514 	bne	IEDEntity_addChild

000001f0 e12fff1e*   515 	ret	

                     516 	.endf	IEDEntity_init

                     517 	.align	4

                     518 

                     519 ;iedEntity	r0	param

                     520 ;parent	r2	param

                     521 

                     522 	.section ".bss","awb"

                     523 .L568:

                     524 	.data

                     525 	.text

                     526 

                     527 

                     528 	.align	4

                     529 	.align	4

                     530 IEDEntity_create::

000001f4 e92d4030    531 	stmfd	[sp]!,{r4-r5,lr}

000001f8 e1a05001    532 	mov	r5,r1

000001fc e1a04000    533 	mov	r4,r0

00000200 e3a0006c    534 	mov	r0,108

00000204 ebffff7d*   535 	bl	IEDEntity_alloc

00000208 e1b02000    536 	movs	r2,r0

0000020c e5842000    537 	str	r2,[r4]

00000210 020200ff    538 	andeq	r0,r2,255


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
00000214 0a000002    539 	beq	.L583

00000218 e1a01005    540 	mov	r1,r5

0000021c ebffffe5*   541 	bl	IEDEntity_init

00000220 e3a00001    542 	mov	r0,1

                     543 .L583:

00000224 e8bd8030    544 	ldmfd	[sp]!,{r4-r5,pc}

                     545 	.endf	IEDEntity_create

                     546 	.align	4

                     547 

                     548 ;iedEntity	r4	param

                     549 ;parent	r5	param

                     550 

                     551 	.section ".bss","awb"

                     552 .L629:

                     553 	.data

                     554 	.text

                     555 

                     556 

                     557 	.align	4

                     558 	.align	4

                     559 IEDEntity_postCreate::

00000228 e92d4030    560 	stmfd	[sp]!,{r4-r5,lr}

0000022c e1a05000    561 	mov	r5,r0

00000230 e5954004    562 	ldr	r4,[r5,4]

00000234 e3540000    563 	cmp	r4,0

00000238 0a000006    564 	beq	.L646

                     565 .L647:

0000023c e1a00004    566 	mov	r0,r4

00000240 ebfffff8*   567 	bl	IEDEntity_postCreate

00000244 e3500000    568 	cmp	r0,0

00000248 0a000008    569 	beq	.L643

0000024c e594400c    570 	ldr	r4,[r4,12]

00000250 e3540000    571 	cmp	r4,0

00000254 1afffff8    572 	bne	.L647

                     573 .L646:

00000258 e5d50020    574 	ldrb	r0,[r5,32]

0000025c e35000e7    575 	cmp	r0,231

00000260 01a00005    576 	moveq	r0,r5

00000264 08bd4030    577 	ldmeqfd	[sp]!,{r4-r5,lr}

00000268 0a000000*   578 	beq	DataSet_postCreate

0000026c e3a00001    579 	mov	r0,1

                     580 .L643:

00000270 e8bd8030    581 	ldmfd	[sp]!,{r4-r5,pc}

                     582 	.endf	IEDEntity_postCreate

                     583 	.align	4

                     584 ;nextChild	r4	local

                     585 ;result	r0	local

                     586 

                     587 ;entity	r5	param

                     588 

                     589 	.section ".bss","awb"

                     590 .L738:

                     591 	.data

                     592 	.text

                     593 

                     594 

                     595 	.align	4

                     596 	.align	4

                     597 IEDEntity_createFromBER::

00000274 e92d4070    598 	stmfd	[sp]!,{r4-r6,lr}

00000278 e24dd024    599 	sub	sp,sp,36


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
0000027c e1a06000    600 	mov	r6,r0

00000280 e1a04001    601 	mov	r4,r1

00000284 e8940021    602 	ldmfd	[r4],{r0,r5}

00000288 e1a01002    603 	mov	r1,r2

0000028c e0855000    604 	add	r5,r5,r0

00000290 e28d000c    605 	add	r0,sp,12

00000294 ebffffd6*   606 	bl	IEDEntity_create

00000298 e3500000    607 	cmp	r0,0

0000029c 0a00006f    608 	beq	.L801

000002a0 e28d3008    609 	add	r3,sp,8

000002a4 e28d2004    610 	add	r2,sp,4

000002a8 e59d000c    611 	ldr	r0,[sp,12]

000002ac e28d1002    612 	add	r1,sp,2

000002b0 e5860000    613 	str	r0,[r6]

000002b4 e1a00004    614 	mov	r0,r4

000002b8 eb000000*   615 	bl	BufferView_decodeTL

000002bc e3500000    616 	cmp	r0,0

000002c0 0a000066    617 	beq	.L801

000002c4 e59d000c    618 	ldr	r0,[sp,12]

000002c8 e5dd1002    619 	ldrb	r1,[sp,2]

000002cc e59d2008    620 	ldr	r2,[sp,8]

000002d0 e5c01020    621 	strb	r1,[r0,32]

000002d4 e1a01005    622 	mov	r1,r5

000002d8 e2800014    623 	add	r0,r0,20

000002dc e3a03000    624 	mov	r3,0

000002e0 eb000000*   625 	bl	BufferView_init

000002e4 e59d2004    626 	ldr	r2,[sp,4]

000002e8 e894000a    627 	ldmfd	[r4],{r1,r3}

000002ec e28d0018    628 	add	r0,sp,24

000002f0 e0831001    629 	add	r1,r3,r1

000002f4 e3a03000    630 	mov	r3,0

000002f8 eb000000*   631 	bl	BufferView_init

000002fc e59d1004    632 	ldr	r1,[sp,4]

00000300 e1a00004    633 	mov	r0,r4

00000304 eb000000*   634 	bl	BufferView_advance

00000308 e3500000    635 	cmp	r0,0

0000030c 0a000053    636 	beq	.L801

00000310 e59d1020    637 	ldr	r1,[sp,32]

00000314 e59d001c    638 	ldr	r0,[sp,28]

00000318 e28d4018    639 	add	r4,sp,24

                     640 ;273:                 {


                     641 

                     642 ;274:                     ERROR_REPORT("BER error");


                     643 ;275:                     return false;


                     644 

0000031c e1500001    645 	cmp	r0,r1

00000320 0a000039    646 	beq	.L775

                     647 .L780:

                     648 ;227: {


                     649 

                     650 ;228:     uint32_t objFlags;


                     651 ;229: 


                     652 ;230:     while(!BufferView_endOfBuf(ber))


                     653 

                     654 ;231:     {


                     655 

                     656 ;232:         uint8_t tag;


                     657 ;233:         if(!BufferView_peekTag(ber, &tag))


                     658 

00000324 e28d1003    659 	add	r1,sp,3

00000328 e1a00004    660 	mov	r0,r4


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
0000032c eb000000*   661 	bl	BufferView_peekTag

00000330 e3500000    662 	cmp	r0,0

00000334 0a000034    663 	beq	.L775

                     664 ;234:         {


                     665 

                     666 ;235:             break;


                     667 

                     668 ;236:         }


                     669 ;237: 


                     670 ;238:         if(!IEDModel_isServiceInfo(tag))


                     671 

00000338 e5dd0003    672 	ldrb	r0,[sp,3]

0000033c eb000000*   673 	bl	IEDModel_isServiceInfo

00000340 e3500000    674 	cmp	r0,0

00000344 0a000030    675 	beq	.L775

                     676 ;239:         {


                     677 

                     678 ;240:             //Служебная информация кончилась


                     679 ;241:             break;


                     680 

                     681 ;242:         }


                     682 ;243: 


                     683 ;244:         switch(tag)


                     684 

00000348 e5dd0003    685 	ldrb	r0,[sp,3]

0000034c e250001a    686 	subs	r0,r0,26

00000350 0a000002    687 	beq	.L783

00000354 e35000a6    688 	cmp	r0,166

00000358 0a000008    689 	beq	.L785

0000035c ea000022    690 	b	.L791

                     691 .L783:

                     692 ;245:         {


                     693 ;246:             case ASN_VISIBLE_STRING:


                     694 ;247:                 //Имя


                     695 ;248:                 if (!BufferView_decodeStringViewTL(ber, ASN_VISIBLE_STRING,


                     696 

00000360 e59d000c    697 	ldr	r0,[sp,12]

00000364 e3a0101a    698 	mov	r1,26

00000368 e2802048    699 	add	r2,r0,72

0000036c e1a00004    700 	mov	r0,r4

00000370 eb000000*   701 	bl	BufferView_decodeStringViewTL

00000374 e3500000    702 	cmp	r0,0

00000378 1a00001f    703 	bne	.L793

0000037c ea000037    704 	b	.L801

                     705 .L785:

                     706 ;249:                     &entity->name))


                     707 ;250:                 {


                     708 

                     709 ;251:                     ERROR_REPORT("Error getting object name");


                     710 ;252:                     return false;


                     711 

                     712 ;253:                 }


                     713 ;254:                 break;


                     714 ;255:             case IED_OBJ_FLAGS:


                     715 ;256:                 if(!BufferView_decodeUInt32TL(ber, IED_OBJ_FLAGS, &objFlags))


                     716 

00000380 e28d2010    717 	add	r2,sp,16

00000384 e1a00004    718 	mov	r0,r4

00000388 e3a010c0    719 	mov	r1,192

0000038c eb000000*   720 	bl	BufferView_decodeUInt32TL

00000390 e3500000    721 	cmp	r0,0


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
00000394 0a000031    722 	beq	.L801

                     723 ;257:                 {


                     724 

                     725 ;258:                     ERROR_REPORT("Error getting object flags");


                     726 ;259:                     return false;


                     727 

                     728 ;260:                 }


                     729 ;261:                 if(objFlags & IED_OBJ_FLAGS_QCHG)


                     730 

00000398 e59d0010    731 	ldr	r0,[sp,16]

0000039c e3100002    732 	tst	r0,2

000003a0 0a000007    733 	beq	.L789

                     734 ;262:                 {


                     735 

                     736 ;263:                     entity->trgOps = TRGOP_QCHG;


                     737 

000003a4 e59d000c    738 	ldr	r0,[sp,12]

000003a8 e3a01008    739 	mov	r1,8

000003ac e5801024    740 	str	r1,[r0,36]

                     741 ;273:                 {


                     742 

                     743 ;274:                     ERROR_REPORT("BER error");


                     744 ;275:                     return false;


                     745 

000003b0 e59d1020    746 	ldr	r1,[sp,32]

000003b4 e59d001c    747 	ldr	r0,[sp,28]

000003b8 e1500001    748 	cmp	r0,r1

000003bc 1affffd8    749 	bne	.L780

000003c0 ea000011    750 	b	.L775

                     751 .L789:

                     752 ;264:                 }


                     753 ;265:                 else if (objFlags & IED_OBJ_FLAGS_DCHG)


                     754 

000003c4 e3100001    755 	tst	r0,1

000003c8 0a00000b    756 	beq	.L793

                     757 ;266:                 {


                     758 

                     759 ;267:                     entity->trgOps = TRGOP_DCHG;


                     760 

000003cc e59d000c    761 	ldr	r0,[sp,12]

000003d0 e3a01010    762 	mov	r1,16

000003d4 e5801024    763 	str	r1,[r0,36]

                     764 ;273:                 {


                     765 

                     766 ;274:                     ERROR_REPORT("BER error");


                     767 ;275:                     return false;


                     768 

000003d8 e59d1020    769 	ldr	r1,[sp,32]

000003dc e59d001c    770 	ldr	r0,[sp,28]

000003e0 e1500001    771 	cmp	r0,r1

000003e4 1affffce    772 	bne	.L780

000003e8 ea000007    773 	b	.L775

                     774 .L791:

                     775 ;268:                 }


                     776 ;269: 


                     777 ;270:                 break;


                     778 ;271:             default:


                     779 ;272:                 if(!BufferView_skipAnyObject(ber))


                     780 

000003ec e1a00004    781 	mov	r0,r4

000003f0 eb000000*   782 	bl	BufferView_skipAnyObject


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
000003f4 e3500000    783 	cmp	r0,0

000003f8 0a000018    784 	beq	.L801

                     785 .L793:

                     786 ;273:                 {


                     787 

                     788 ;274:                     ERROR_REPORT("BER error");


                     789 ;275:                     return false;


                     790 

000003fc e59d1020    791 	ldr	r1,[sp,32]

00000400 e59d001c    792 	ldr	r0,[sp,28]

00000404 e1500001    793 	cmp	r0,r1

00000408 1affffc5    794 	bne	.L780

                     795 .L775:

                     796 ;276:                 }


                     797 ;277:         }


                     798 ;278:     }


                     799 ;279: 


                     800 ;280:     //Собрали всю инфу


                     801 ;281: 


                     802 ;282:     return true;


                     803 

0000040c e5dd0002    804 	ldrb	r0,[sp,2]

00000410 e35000e9    805 	cmp	r0,233

00000414 135000e7    806 	cmpne	r0,231

00000418 0a000016    807 	beq	.L813

0000041c e28d4018    808 	add	r4,sp,24

00000420 e1a05004    809 	mov	r5,r4

00000424 e59d1020    810 	ldr	r1,[sp,32]

00000428 e59d001c    811 	ldr	r0,[sp,28]

0000042c e28d6014    812 	add	r6,sp,20

                     813 ;347:         {


                     814 

                     815 ;348:             return false;


                     816 

00000430 e1500001    817 	cmp	r0,r1

00000434 0a00000f    818 	beq	.L813

                     819 .L805:

00000438 e59d200c    820 	ldr	r2,[sp,12]

0000043c e1a01005    821 	mov	r1,r5

00000440 e1a00006    822 	mov	r0,r6

00000444 ebffff8a*   823 	bl	IEDEntity_createFromBER

                     824 ;340:                                      BufferView* childrenBER)


                     825 ;341: {


                     826 

                     827 ;342: 


                     828 ;343:     while(!BufferView_endOfBuf(childrenBER))


                     829 

                     830 ;344:     {


                     831 

                     832 ;345:         IEDEntity child;


                     833 ;346:         if(!IEDEntity_createFromBER(&child, childrenBER, parent))


                     834 

00000448 e3500000    835 	cmp	r0,0

0000044c 0a000003    836 	beq	.L801

                     837 ;347:         {


                     838 

                     839 ;348:             return false;


                     840 

00000450 e9940003    841 	ldmed	[r4],{r0-r1}

00000454 e1500001    842 	cmp	r0,r1

00000458 1afffff6    843 	bne	.L805


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
0000045c ea000005    844 	b	.L813

                     845 .L801:

                     846 ;349:         }


                     847 ;350:     }


                     848 ;351:     return true;


                     849 

00000460 e3a00000    850 	mov	r0,0

00000464 ea000026    851 	b	.L764

                     852 	.align	4

                     853 .L579:

00000468 00000000*   854 	.data.w	updateFromDataSliceStub

                     855 	.type	.L579,$object

                     856 	.size	.L579,4

                     857 

                     858 .L580:

0000046c 00000000*   859 	.data.w	calcReadLen

                     860 	.type	.L580,$object

                     861 	.size	.L580,4

                     862 

                     863 .L581:

00000470 00000000*   864 	.data.w	encodeRead

                     865 	.type	.L581,$object

                     866 	.size	.L581,4

                     867 

                     868 .L582:

00000474 00000000*   869 	.data.w	write

                     870 	.type	.L582,$object

                     871 	.size	.L582,4

                     872 

                     873 .L813:

                     874 ;199: {


                     875 

                     876 ;200:     switch(entity->tag) {


                     877 

00000478 e59d000c    878 	ldr	r0,[sp,12]

0000047c e5d01020    879 	ldrb	r1,[r0,32]

00000480 e25110e0    880 	subs	r1,r1,224

00000484 e351000c    881 	cmp	r1,12

00000488 8a00001c    882 	bhi	.L821

0000048c e08ff101    883 	add	pc,pc,r1 lsl 2

                     884 .L1301:

                     885 

00000490 e1a00000    886 	nop	

00000494 ea00000f    887 	b	.L816

00000498 ea000018    888 	b	.L821

0000049c ea000009    889 	b	.L814

000004a0 ea000016    890 	b	.L821

000004a4 ea000009    891 	b	.L815

000004a8 ea000014    892 	b	.L821

000004ac ea00000b    893 	b	.L817

000004b0 ea000010    894 	b	.L820

000004b4 ea00000b    895 	b	.L818

000004b8 ea00000c    896 	b	.L819

000004bc ea00000f    897 	b	.L821

000004c0 ea00000e    898 	b	.L821

000004c4 ea000001    899 	b	.L815

                     900 .L814:

                     901 ;201:     case IED_LD:


                     902 ;202:         return IEDLD_init(entity);


                     903 

000004c8 eb000000*   904 	bl	IEDLD_init


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
000004cc ea00000c    905 	b	.L764

                     906 .L815:

                     907 ;203:     case IED_VMD_DATA_SECTION:


                     908 ;204:     case IED_LN:


                     909 ;205:         return IEDComplexObj_init(entity);


                     910 

000004d0 eb000000*   911 	bl	IEDComplexObj_init

000004d4 ea00000a    912 	b	.L764

                     913 .L816:

                     914 ;206:     case IED_FC:


                     915 ;207:         return IEDFC_init(entity);


                     916 

000004d8 eb000000*   917 	bl	IEDFC_init

000004dc ea000008    918 	b	.L764

                     919 .L817:

                     920 ;208:     case IED_DO:


                     921 ;209:         return IEDDO_init(entity);


                     922 

000004e0 eb000000*   923 	bl	IEDDO_init

000004e4 ea000006    924 	b	.L764

                     925 .L818:

                     926 ;210:     case IED_DA:


                     927 ;211:         return IEDDA_init(entity);


                     928 

000004e8 eb000000*   929 	bl	IEDDA_init

000004ec ea000004    930 	b	.L764

                     931 .L819:

                     932 ;212:     case IED_DA_FINAL:


                     933 ;213:         return IEDFinalDA_init(entity);


                     934 

000004f0 eb000000*   935 	bl	IEDFinalDA_init

000004f4 ea000002    936 	b	.L764

                     937 .L820:

                     938 ;214:     case IED_DATA_SET:


                     939 ;215:         return DataSet_init(entity);


                     940 

000004f8 eb000000*   941 	bl	DataSet_init

000004fc ea000000    942 	b	.L764

                     943 .L821:

                     944 ;216:     }


                     945 ;217:     return true;


                     946 

00000500 e3a00001    947 	mov	r0,1

                     948 .L764:

00000504 e28dd024    949 	add	sp,sp,36

00000508 e8bd8070    950 	ldmfd	[sp]!,{r4-r6,pc}

                     951 	.endf	IEDEntity_createFromBER

                     952 	.align	4

                     953 ;tag	[sp,2]	local

                     954 ;len	[sp,4]	local

                     955 ;fullLen	[sp,8]	local

                     956 ;berObject	[sp,24]	local

                     957 ;entity	[sp,12]	local

                     958 ;pObjectBER	r5	local

                     959 ;objFlags	[sp,16]	local

                     960 ;tag	[sp,3]	local

                     961 ;child	[sp,20]	local

                     962 

                     963 ;iedEntity	r6	param

                     964 ;ber	r4	param

                     965 ;parent	r2	param


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
                     966 

                     967 	.section ".bss","awb"

                     968 .L1300:

                     969 	.data

                     970 	.ghsnote jtable,5,.L1301,.L1301,.L1301,14

                     971 	.text

                     972 

                     973 

                     974 ;353: 


                     975 ;354: IEDEntity IEDEntity_getChildByName(IEDEntity parent, StringView* name)


                     976 	.align	4

                     977 	.align	4

                     978 IEDEntity_getChildByName::

0000050c e92d4030    979 	stmfd	[sp]!,{r4-r5,lr}

                     980 ;355: {


                     981 

                     982 ;356:     IEDEntity child = parent->firstChild;


                     983 

00000510 e5904004    984 	ldr	r4,[r0,4]

                     985 ;357:     while(child != NULL)


                     986 

00000514 e1a05001    987 	mov	r5,r1

00000518 e3540000    988 	cmp	r4,0

0000051c 0a000006    989 	beq	.L1368

                     990 .L1369:

                     991 ;358:     {


                     992 

                     993 ;359:         if(0 == StringView_cmp(name, &child->name))


                     994 

00000520 e2841048    995 	add	r1,r4,72

00000524 e1a00005    996 	mov	r0,r5

00000528 eb000000*   997 	bl	StringView_cmp

0000052c e3500000    998 	cmp	r0,0

                     999 ;360:         {


                    1000 

                    1001 ;361:             return child;


                    1002 

                    1003 ;362:         }


                    1004 ;363:         child = child->next;


                    1005 

00000530 1594400c   1006 	ldrne	r4,[r4,12]

00000534 13540000   1007 	cmpne	r4,0

00000538 1afffff8   1008 	bne	.L1369

                    1009 .L1368:

                    1010 ;364:     }


                    1011 ;365:     return NULL;


                    1012 

0000053c e1a00004   1013 	mov	r0,r4

00000540 e8bd8030   1014 	ldmfd	[sp]!,{r4-r5,pc}

                    1015 	.endf	IEDEntity_getChildByName

                    1016 	.align	4

                    1017 ;child	r4	local

                    1018 

                    1019 ;parent	r0	param

                    1020 ;name	r5	param

                    1021 

                    1022 	.section ".bss","awb"

                    1023 .L1434:

                    1024 	.data

                    1025 	.text

                    1026 


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
                    1027 ;366: }


                    1028 

                    1029 ;367: 


                    1030 ;368: IEDEntity IEDEntity_getChildByCStrName(IEDEntity parent, const char* name)


                    1031 	.align	4

                    1032 	.align	4

                    1033 IEDEntity_getChildByCStrName::

00000544 e92d4030   1034 	stmfd	[sp]!,{r4-r5,lr}

                    1035 ;369: {


                    1036 

                    1037 ;370:     IEDEntity child = parent->firstChild;


                    1038 

00000548 e5904004   1039 	ldr	r4,[r0,4]

                    1040 ;371:     while(child != NULL)


                    1041 

0000054c e1a05001   1042 	mov	r5,r1

00000550 e3540000   1043 	cmp	r4,0

00000554 0a000006   1044 	beq	.L1455

                    1045 .L1456:

                    1046 ;372:     {


                    1047 

                    1048 ;373:         if(0 == StringView_cmpCStr(&child->name, name))


                    1049 

00000558 e1a01005   1050 	mov	r1,r5

0000055c e2840048   1051 	add	r0,r4,72

00000560 eb000000*  1052 	bl	StringView_cmpCStr

00000564 e3500000   1053 	cmp	r0,0

                    1054 ;374:         {


                    1055 

                    1056 ;375:             return child;


                    1057 

                    1058 ;376:         }


                    1059 ;377:         child = child->next;


                    1060 

00000568 1594400c   1061 	ldrne	r4,[r4,12]

0000056c 13540000   1062 	cmpne	r4,0

00000570 1afffff8   1063 	bne	.L1456

                    1064 .L1455:

                    1065 ;378:     }


                    1066 ;379:     return NULL;


                    1067 

00000574 e1a00004   1068 	mov	r0,r4

00000578 e8bd8030   1069 	ldmfd	[sp]!,{r4-r5,pc}

                    1070 	.endf	IEDEntity_getChildByCStrName

                    1071 	.align	4

                    1072 ;child	r4	local

                    1073 

                    1074 ;parent	r0	param

                    1075 ;name	r5	param

                    1076 

                    1077 	.section ".bss","awb"

                    1078 .L1530:

                    1079 	.data

                    1080 	.text

                    1081 

                    1082 ;380: }


                    1083 

                    1084 ;381: 


                    1085 ;382: IEDEntity IEDEntity_getChildByFullName(IEDEntity parent,  StringView* name)


                    1086 	.align	4

                    1087 	.align	4


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
                    1088 IEDEntity_getChildByFullName::

0000057c e92d40f0   1089 	stmfd	[sp]!,{r4-r7,lr}

00000580 e24dd018   1090 	sub	sp,sp,24

                    1091 ;383: {


                    1092 

                    1093 ;384:     StringView fullName = *name;


                    1094 

00000584 e5913000   1095 	ldr	r3,[r1]

00000588 e1a04000   1096 	mov	r4,r0

                    1097 ;389: 


                    1098 ;390:     do


                    1099 

0000058c e58d3010   1100 	str	r3,[sp,16]

00000590 e5911004   1101 	ldr	r1,[r1,4]

00000594 e28d6008   1102 	add	r6,sp,8

00000598 e58d1014   1103 	str	r1,[sp,20]

                    1104 ;385:     StringView directChildName;


                    1105 ;386:     StringView remainingName;


                    1106 ;387:     bool lastName;


                    1107 ;388:     IEDEntity entity = parent;


                    1108 

0000059c e1a0700d   1109 	mov	r7,sp

                    1110 .L1552:

                    1111 ;391:     {


                    1112 

                    1113 ;392:         lastName = !StringView_splitChar(&fullName, '$',


                    1114 

000005a0 e1a03007   1115 	mov	r3,r7

000005a4 e1a02006   1116 	mov	r2,r6

000005a8 e28d0010   1117 	add	r0,sp,16

000005ac e3a01024   1118 	mov	r1,36

000005b0 eb000000*  1119 	bl	StringView_splitChar

000005b4 e3500000   1120 	cmp	r0,0

000005b8 03a05001   1121 	moveq	r5,1

000005bc 13a05000   1122 	movne	r5,0

                    1123 ;393:                                            &directChildName, &remainingName);


                    1124 ;394:         if(lastName)


                    1125 

000005c0 e3550000   1126 	cmp	r5,0

                    1127 ;395:         {


                    1128 

                    1129 ;396:             //Разделитель не найден, значит это последнее имя


                    1130 ;397:             directChildName = fullName;


                    1131 

000005c4 159d2010   1132 	ldrne	r2,[sp,16]

000005c8 159d0014   1133 	ldrne	r0,[sp,20]

000005cc 158d2008   1134 	strne	r2,[sp,8]

000005d0 e1a01006   1135 	mov	r1,r6

000005d4 158d000c   1136 	strne	r0,[sp,12]

                    1137 ;398:         }


                    1138 ;399:         entity = IEDEntity_getChildByName(entity, &directChildName);


                    1139 

000005d8 e1a00004   1140 	mov	r0,r4

000005dc ebffffca*  1141 	bl	IEDEntity_getChildByName

000005e0 e1b04000   1142 	movs	r4,r0

                    1143 ;400:         if(entity == NULL)


                    1144 

000005e4 0a000004   1145 	beq	.L1550

                    1146 ;401:         {


                    1147 

                    1148 ;402:             return NULL;



                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
                    1149 

                    1150 ;403:         }


                    1151 ;404:         fullName = remainingName;


                    1152 

000005e8 e89d000c   1153 	ldmfd	[sp],{r2-r3}

000005ec e58d2010   1154 	str	r2,[sp,16]

000005f0 e58d3014   1155 	str	r3,[sp,20]

000005f4 e3550000   1156 	cmp	r5,0

000005f8 0affffe8   1157 	beq	.L1552

                    1158 .L1550:

                    1159 ;405:     } while(!lastName);


                    1160 ;406: 


                    1161 ;407:     return entity;


                    1162 

000005fc e1a00004   1163 	mov	r0,r4

00000600 e28dd018   1164 	add	sp,sp,24

00000604 e8bd80f0   1165 	ldmfd	[sp]!,{r4-r7,pc}

                    1166 	.endf	IEDEntity_getChildByFullName

                    1167 	.align	4

                    1168 ;fullName	[sp,16]	local

                    1169 ;directChildName	[sp,8]	local

                    1170 ;remainingName	[sp]	local

                    1171 ;lastName	r5	local

                    1172 ;entity	r4	local

                    1173 

                    1174 ;parent	r0	param

                    1175 ;name	r1	param

                    1176 

                    1177 	.section ".bss","awb"

                    1178 .L1648:

                    1179 	.data

                    1180 	.text

                    1181 

                    1182 ;408: }


                    1183 

                    1184 ;409: 


                    1185 ;410: IEDEntity IEDEntity_getChildByTag(IEDEntity parent,  uint8_t tag)


                    1186 	.align	4

                    1187 	.align	4

                    1188 IEDEntity_getChildByTag::

                    1189 ;411: {


                    1190 

                    1191 ;412:     IEDEntity child = parent->firstChild;


                    1192 

00000608 e5900004   1193 	ldr	r0,[r0,4]

                    1194 ;413:     while(child != NULL)


                    1195 

0000060c e3500000   1196 	cmp	r0,0

                    1197 ;414:     {


                    1198 

                    1199 ;415:         if(child->tag == tag)


                    1200 

00000610 15d02020   1201 	ldrneb	r2,[r0,32]

00000614 11520001   1202 	cmpne	r2,r1

                    1203 .L1675:

                    1204 ;416:         {


                    1205 

                    1206 ;417:             return child;


                    1207 

                    1208 ;418:         }


                    1209 ;419:         child = child->next;



                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
                    1210 

00000618 1590000c   1211 	ldrne	r0,[r0,12]

0000061c 13500000   1212 	cmpne	r0,0

                    1213 ;414:     {


                    1214 

                    1215 ;415:         if(child->tag == tag)


                    1216 

00000620 15d02020   1217 	ldrneb	r2,[r0,32]

00000624 11520001   1218 	cmpne	r2,r1

00000628 1afffffa   1219 	bne	.L1675

                    1220 .L1673:

                    1221 ;420:     }


                    1222 ;421:     return NULL;


                    1223 

0000062c e12fff1e*  1224 	ret	

                    1225 	.endf	IEDEntity_getChildByTag

                    1226 	.align	4

                    1227 ;child	r0	local

                    1228 

                    1229 ;parent	r0	param

                    1230 ;tag	r1	param

                    1231 

                    1232 	.section ".bss","awb"

                    1233 .L1738:

                    1234 	.data

                    1235 	.text

                    1236 

                    1237 ;422: }


                    1238 

                    1239 ;423: 


                    1240 ;424: MmsDataAccessError IEDEntity_write(IEDEntity entity, IsoConnection* isoConn,


                    1241 	.align	4

                    1242 	.align	4

                    1243 IEDEntity_write::

00000630 e92d4000   1244 	stmfd	[sp]!,{lr}

                    1245 ;425:                                    BufferView* value)


                    1246 ;426: {    


                    1247 

                    1248 ;427:     if(entity->readOnly)


                    1249 

00000634 e5d0c022   1250 	ldrb	r12,[r0,34]

00000638 e35c0000   1251 	cmp	r12,0

                    1252 ;428:     {


                    1253 

                    1254 ;429:         return DATA_ACCESS_ERROR_OBJECT_ACCESS_DENIED;


                    1255 

0000063c 13a00003   1256 	movne	r0,3

                    1257 ;430:     }


                    1258 ;431:     return entity->write(entity, isoConn, value);


                    1259 

00000640 0590c064   1260 	ldreq	r12,[r0,100]

00000644 01a0e00f   1261 	moveq	lr,pc

00000648 012fff1c*  1262 	bxeq	r12

0000064c e8bd8000   1263 	ldmfd	[sp]!,{pc}

                    1264 	.endf	IEDEntity_write

                    1265 	.align	4

                    1266 

                    1267 ;entity	r0	param

                    1268 ;isoConn	r1	param

                    1269 ;value	r2	param

                    1270 


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
                    1271 	.section ".bss","awb"

                    1272 .L1782:

                    1273 	.data

                    1274 	.text

                    1275 

                    1276 ;432: }


                    1277 

                    1278 ;433: 


                    1279 ;434: bool IEDEntity_getFullName(IEDEntity entity, BufferView* nameBuf)


                    1280 	.align	4

                    1281 	.align	4

                    1282 IEDEntity_getFullName::

00000650 e92d4030   1283 	stmfd	[sp]!,{r4-r5,lr}

00000654 e1a04000   1284 	mov	r4,r0

                    1285 ;435: {


                    1286 

                    1287 ;436:     char *delimiter;


                    1288 ;437:     if(entity->parent !=NULL && entity->type != IED_ENTITY_LD)


                    1289 

00000658 e5940000   1290 	ldr	r0,[r4]

0000065c e1a05001   1291 	mov	r5,r1

00000660 e3500000   1292 	cmp	r0,0

00000664 15941050   1293 	ldrne	r1,[r4,80]

00000668 13510001   1294 	cmpne	r1,1

0000066c 0a000010   1295 	beq	.L1797

                    1296 ;438:     {


                    1297 

                    1298 ;439:         if(!IEDEntity_getFullName(entity->parent, nameBuf))


                    1299 

00000670 e1a01005   1300 	mov	r1,r5

00000674 ebfffff5*  1301 	bl	IEDEntity_getFullName

00000678 e3500000   1302 	cmp	r0,0

0000067c 0a00000a   1303 	beq	.L1811

                    1304 ;440:         {


                    1305 

                    1306 ;441:             return false;


                    1307 

                    1308 ;442:         }


                    1309 ;443: 


                    1310 ;444:         if(entity->name.len == 0)


                    1311 

00000680 e5940048   1312 	ldr	r0,[r4,72]

00000684 e3500000   1313 	cmp	r0,0

                    1314 ;445:         {


                    1315 

                    1316 ;446:             return true;


                    1317 

00000688 03a00001   1318 	moveq	r0,1

0000068c 0a00000c   1319 	beq	.L1795

                    1320 ;447:         }


                    1321 ;448: 


                    1322 ;449:         switch(entity->type)


                    1323 

00000690 e5940050   1324 	ldr	r0,[r4,80]

00000694 e3500003   1325 	cmp	r0,3

                    1326 ;453:             break;


                    1327 ;454:         default:


                    1328 ;455:             delimiter = "$";


                    1329 

00000698 128f1000*  1330 	adrne	r1,.L1977

                    1331 ;456:             break;



                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
                    1332 ;457:         }


                    1333 ;458:         if(!BufferView_writeStr(nameBuf, delimiter))


                    1334 

                    1335 ;450:         {


                    1336 ;451:         case IED_ENTITY_LN:


                    1337 ;452:             delimiter = "/";


                    1338 

0000069c 028f1000*  1339 	adreq	r1,.L1978

                    1340 ;456:             break;


                    1341 ;457:         }


                    1342 ;458:         if(!BufferView_writeStr(nameBuf, delimiter))


                    1343 

000006a0 e1a00005   1344 	mov	r0,r5

000006a4 eb000000*  1345 	bl	BufferView_writeStr

000006a8 e3500000   1346 	cmp	r0,0

                    1347 .L1811:

                    1348 ;459:         {


                    1349 

                    1350 ;460:             ERROR_REPORT("Name buffer overflow");


                    1351 ;461:             return false;


                    1352 

000006ac 03a00000   1353 	moveq	r0,0

000006b0 0a000003   1354 	beq	.L1795

                    1355 .L1797:

                    1356 ;462:         }


                    1357 ;463:     }


                    1358 ;464:     return BufferView_writeStringView(nameBuf, &entity->name);


                    1359 

000006b4 e2841048   1360 	add	r1,r4,72

000006b8 e1a00005   1361 	mov	r0,r5

000006bc e8bd4030   1362 	ldmfd	[sp]!,{r4-r5,lr}

000006c0 ea000000*  1363 	b	BufferView_writeStringView

                    1364 .L1795:

000006c4 e8bd8030   1365 	ldmfd	[sp]!,{r4-r5,pc}

                    1366 	.endf	IEDEntity_getFullName

                    1367 	.align	4

                    1368 ;.L1943	.L1947	static

                    1369 ;.L1944	.L1948	static

                    1370 

                    1371 ;entity	r4	param

                    1372 ;nameBuf	r5	param

                    1373 

                    1374 	.section ".bss","awb"

                    1375 .L1942:

                    1376 	.data

                    1377 	.text

                    1378 

                    1379 ;465: }


                    1380 

                    1381 ;466: 


                    1382 ;467: bool IEDEntity_getFullItemId(IEDEntity entity, BufferView* nameBuf)


                    1383 	.align	4

                    1384 	.align	4

                    1385 IEDEntity_getFullItemId::

000006c8 e92d4030   1386 	stmfd	[sp]!,{r4-r5,lr}

000006cc e1a04000   1387 	mov	r4,r0

                    1388 ;468: {


                    1389 

                    1390 ;469:     if(entity->parent !=NULL && entity->type != IED_ENTITY_LN)


                    1391 

000006d0 e5940000   1392 	ldr	r0,[r4]


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
000006d4 e1a05001   1393 	mov	r5,r1

000006d8 e3500000   1394 	cmp	r0,0

000006dc 15941050   1395 	ldrne	r1,[r4,80]

000006e0 13510003   1396 	cmpne	r1,3

000006e4 0a00000d   1397 	beq	.L1981

                    1398 ;470:     {


                    1399 

                    1400 ;471:         if(!IEDEntity_getFullItemId(entity->parent, nameBuf))


                    1401 

000006e8 e1a01005   1402 	mov	r1,r5

000006ec ebfffff5*  1403 	bl	IEDEntity_getFullItemId

000006f0 e3500000   1404 	cmp	r0,0

000006f4 0a000007   1405 	beq	.L1991

                    1406 ;472:         {


                    1407 

                    1408 ;473:             return false;


                    1409 

                    1410 ;474:         }


                    1411 ;475: 


                    1412 ;476:         if(entity->name.len == 0)


                    1413 

000006f8 e5940048   1414 	ldr	r0,[r4,72]

000006fc e3500000   1415 	cmp	r0,0

                    1416 ;477:         {


                    1417 

                    1418 ;478:             return true;


                    1419 

00000700 03a00001   1420 	moveq	r0,1

00000704 0a000009   1421 	beq	.L1979

                    1422 ;479:         }


                    1423 ;480: 


                    1424 ;481:         if(!BufferView_writeStr(nameBuf, "$"))


                    1425 

00000708 e28f1000*  1426 	adr	r1,.L1977

0000070c e1a00005   1427 	mov	r0,r5

00000710 eb000000*  1428 	bl	BufferView_writeStr

00000714 e3500000   1429 	cmp	r0,0

                    1430 .L1991:

                    1431 ;482:         {


                    1432 

                    1433 ;483:             ERROR_REPORT("Name buffer overflow");


                    1434 ;484:             return false;


                    1435 

00000718 03a00000   1436 	moveq	r0,0

0000071c 0a000003   1437 	beq	.L1979

                    1438 .L1981:

                    1439 ;485:         }


                    1440 ;486:     }


                    1441 ;487:     return BufferView_writeStringView(nameBuf, &entity->name);


                    1442 

00000720 e2841048   1443 	add	r1,r4,72

00000724 e1a00005   1444 	mov	r0,r5

00000728 e8bd4030   1445 	ldmfd	[sp]!,{r4-r5,lr}

0000072c ea000000*  1446 	b	BufferView_writeStringView

                    1447 .L1979:

00000730 e8bd8030   1448 	ldmfd	[sp]!,{r4-r5,pc}

                    1449 	.endf	IEDEntity_getFullItemId

                    1450 	.align	4

                    1451 ;.L2095	.L2098	static

                    1452 

                    1453 ;entity	r4	param


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
                    1454 ;nameBuf	r5	param

                    1455 

                    1456 	.section ".bss","awb"

                    1457 .L2094:

                    1458 	.data

                    1459 	.text

                    1460 

                    1461 ;488: }


                    1462 

                    1463 ;489: 


                    1464 ;490: bool IEDEntity_getDomainId(IEDEntity entity, StringView** name)


                    1465 	.align	4

                    1466 	.align	4

                    1467 IEDEntity_getDomainId::

                    1468 ;491: {


                    1469 

                    1470 ;492:     if(entity->type == IED_ENTITY_LD)


                    1471 

00000734 e5902050   1472 	ldr	r2,[r0,80]

00000738 e3520001   1473 	cmp	r2,1

                    1474 ;493:     {


                    1475 

                    1476 ;494:         *name = &entity->name;


                    1477 

0000073c 02800048   1478 	addeq	r0,r0,72

00000740 05810000   1479 	streq	r0,[r1]

                    1480 ;495:         return true;


                    1481 

00000744 03a00001   1482 	moveq	r0,1

00000748 0a000003   1483 	beq	.L2123

0000074c e5902000   1484 	ldr	r2,[r0]

00000750 e1b00002   1485 	movs	r0,r2

                    1486 ;496:     }


                    1487 ;497:     if(entity->parent ==NULL )


                    1488 

                    1489 ;500:     }


                    1490 ;501: 


                    1491 ;502:     return IEDEntity_getDomainId(entity->parent, name);


                    1492 

00000754 1afffff6   1493 	bne	IEDEntity_getDomainId

                    1494 ;498:     {


                    1495 

                    1496 ;499:         return false;


                    1497 

00000758 e20000ff   1498 	and	r0,r0,255

                    1499 .L2123:

0000075c e12fff1e*  1500 	ret	

                    1501 	.endf	IEDEntity_getDomainId

                    1502 	.align	4

                    1503 

                    1504 ;entity	r0	param

                    1505 ;name	r1	param

                    1506 

                    1507 	.section ".bss","awb"

                    1508 .L2202:

                    1509 	.data

                    1510 	.text

                    1511 

                    1512 ;503: }


                    1513 

                    1514 ;504: 



                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
                    1515 ;505: void IEDEntity_attachTimeStamp(IEDEntity entity, TimeStamp* timeStamp)


                    1516 	.align	4

                    1517 	.align	4

                    1518 IEDEntity_attachTimeStamp::

00000760 e92d4030   1519 	stmfd	[sp]!,{r4-r5,lr}

00000764 e1a05001   1520 	mov	r5,r1

                    1521 ;506: {


                    1522 

                    1523 ;507:     IEDEntity child;


                    1524 ;508: 


                    1525 ;509:     if(entity->type == IED_ENTITY_DA_TIMESTAMP)


                    1526 

00000768 e5901050   1527 	ldr	r1,[r0,80]

0000076c e3510009   1528 	cmp	r1,9

                    1529 ;510:     {


                    1530 

                    1531 ;511:         return;


                    1532 

                    1533 ;512:     }


                    1534 ;513:     entity->timeStamp = timeStamp;


                    1535 

00000770 15904004   1536 	ldrne	r4,[r0,4]

                    1537 ;516:     while(child != NULL)


                    1538 

00000774 15805044   1539 	strne	r5,[r0,68]

                    1540 ;514: 


                    1541 ;515:     child = entity->firstChild;


                    1542 

00000778 13540000   1543 	cmpne	r4,0

0000077c 0a000005   1544 	beq	.L2221

                    1545 .L2228:

                    1546 ;517:     {


                    1547 

                    1548 ;518:         IEDEntity_attachTimeStamp(child, timeStamp);


                    1549 

00000780 e1a01005   1550 	mov	r1,r5

00000784 e1a00004   1551 	mov	r0,r4

00000788 ebfffff4*  1552 	bl	IEDEntity_attachTimeStamp

                    1553 ;519:         child = child->next;


                    1554 

0000078c e594400c   1555 	ldr	r4,[r4,12]

00000790 e3540000   1556 	cmp	r4,0

00000794 1afffff9   1557 	bne	.L2228

                    1558 .L2221:

00000798 e8bd8030   1559 	ldmfd	[sp]!,{r4-r5,pc}

                    1560 	.endf	IEDEntity_attachTimeStamp

                    1561 	.align	4

                    1562 ;child	r4	local

                    1563 

                    1564 ;entity	r0	param

                    1565 ;timeStamp	r5	param

                    1566 

                    1567 	.section ".bss","awb"

                    1568 .L2276:

                    1569 	.data

                    1570 	.text

                    1571 

                    1572 ;520:     }


                    1573 ;521: }


                    1574 

                    1575 ;522: 



                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
                    1576 ;523: void IEDEntity_writeFullName(IEDEntity entity, BufferView* bv )


                    1577 	.align	4

                    1578 	.align	4

                    1579 IEDEntity_writeFullName::

0000079c e92d4030   1580 	stmfd	[sp]!,{r4-r5,lr}

000007a0 e1a05000   1581 	mov	r5,r0

                    1582 ;524: {


                    1583 

                    1584 ;525:     if(entity->parent !=NULL)


                    1585 

000007a4 e5950000   1586 	ldr	r0,[r5]

000007a8 e1a04001   1587 	mov	r4,r1

000007ac e3500000   1588 	cmp	r0,0

000007b0 0a000003   1589 	beq	.L2293

                    1590 ;526:     {


                    1591 

                    1592 ;527:         IEDEntity_writeFullName(entity->parent, bv);


                    1593 

000007b4 ebfffff8*  1594 	bl	IEDEntity_writeFullName

                    1595 ;528:         BufferView_writeStr(bv, "/");


                    1596 

000007b8 e28f1000*  1597 	adr	r1,.L1978

000007bc e1a00004   1598 	mov	r0,r4

000007c0 eb000000*  1599 	bl	BufferView_writeStr

                    1600 .L2293:

                    1601 ;529:     }


                    1602 ;530:     BufferView_writeStringView(bv, &entity->name);


                    1603 

000007c4 e2851048   1604 	add	r1,r5,72

000007c8 e1a00004   1605 	mov	r0,r4

000007cc e8bd4030   1606 	ldmfd	[sp]!,{r4-r5,lr}

000007d0 ea000000*  1607 	b	BufferView_writeStringView

                    1608 	.endf	IEDEntity_writeFullName

                    1609 	.align	4

                    1610 ;.L2334	.L2337	static

                    1611 

                    1612 ;entity	r5	param

                    1613 ;bv	r4	param

                    1614 

                    1615 	.section ".bss","awb"

                    1616 .L2333:

                    1617 	.data

                    1618 	.text

                    1619 

                    1620 ;531: }


                    1621 

                    1622 ;532: 


                    1623 ;533: void IEDEntity_printFullName(IEDEntity entity)


                    1624 	.align	4

                    1625 	.align	4

                    1626 IEDEntity_printFullName::

000007d4 e92d4030   1627 	stmfd	[sp]!,{r4-r5,lr}

                    1628 ;534: {


                    1629 

                    1630 ;535:     BufferView nameBuf;


                    1631 ;536:     void* p = malloc(257);


                    1632 

000007d8 e24dd00c   1633 	sub	sp,sp,12

000007dc e1a05000   1634 	mov	r5,r0

000007e0 e3a00f40   1635 	mov	r0,256

000007e4 e2800001   1636 	add	r0,r0,1


                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
000007e8 eb000000*  1637 	bl	malloc

000007ec e1b04000   1638 	movs	r4,r0

                    1639 ;537:     if(p == NULL)


                    1640 

000007f0 0a00000d   1641 	beq	.L2346

                    1642 ;538:     {


                    1643 

                    1644 ;539:         return;


                    1645 

                    1646 ;540:     }


                    1647 ;541:     memset(p, 0, 257);


                    1648 

000007f4 e3a02f40   1649 	mov	r2,256

000007f8 e2822001   1650 	add	r2,r2,1

000007fc e3a01000   1651 	mov	r1,0

00000800 eb000000*  1652 	bl	memset

                    1653 ;542:     BufferView_init(&nameBuf, p, 256, 0);


                    1654 

00000804 e1a01004   1655 	mov	r1,r4

00000808 e1a0000d   1656 	mov	r0,sp

0000080c e3a03000   1657 	mov	r3,0

00000810 e3a02f40   1658 	mov	r2,256

00000814 eb000000*  1659 	bl	BufferView_init

                    1660 ;543:     IEDEntity_writeFullName(entity, &nameBuf);	//TRACE(nameBuf.p);


                    1661 

00000818 e1a0100d   1662 	mov	r1,sp

0000081c e1a00005   1663 	mov	r0,r5

00000820 ebffffdd*  1664 	bl	IEDEntity_writeFullName

                    1665 ;544:     TRACE((char*)nameBuf.p);


                    1666 ;545:     free(p);


                    1667 

00000824 e1a00004   1668 	mov	r0,r4

00000828 eb000000*  1669 	bl	free

                    1670 .L2346:

0000082c e28dd00c   1671 	add	sp,sp,12

00000830 e8bd8030   1672 	ldmfd	[sp]!,{r4-r5,pc}

                    1673 	.endf	IEDEntity_printFullName

                    1674 	.align	4

                    1675 ;nameBuf	[sp]	local

                    1676 ;p	r4	local

                    1677 

                    1678 ;entity	r5	param

                    1679 

                    1680 	.section ".bss","awb"

                    1681 .L2376:

                    1682 	.data

                    1683 	.text

                    1684 

                    1685 ;546: }


                    1686 

                    1687 ;547: 


                    1688 ;548: void IEDEntity_setTimeStamp(IEDEntity entity, uint64_t timeStamp)


                    1689 	.align	4

                    1690 	.align	4

                    1691 IEDEntity_setTimeStamp::

                    1692 ;549: {


                    1693 

                    1694 ;550:     if(entity->timeStamp != NULL)


                    1695 

00000834 e5900044   1696 	ldr	r0,[r0,68]

00000838 e3500000   1697 	cmp	r0,0


                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
                    1698 ;551:     {


                    1699 

                    1700 ;552:         entity->timeStamp->timeStamp = timeStamp;


                    1701 

0000083c 18800006   1702 	stmneea	[r0],{r1-r2}

00000840 e12fff1e*  1703 	ret	

                    1704 	.endf	IEDEntity_setTimeStamp

                    1705 	.align	4

                    1706 .L1977:

                    1707 ;	"$\000"

00000844 0024      1708 	.data.b	36,0

00000846 0000      1709 	.align 4

                    1710 

                    1711 	.type	.L1977,$object

                    1712 	.size	.L1977,4

                    1713 

                    1714 .L1978:

                    1715 ;	"/\000"

00000848 002f      1716 	.data.b	47,0

0000084a 0000      1717 	.align 4

                    1718 

                    1719 	.type	.L1978,$object

                    1720 	.size	.L1978,4

                    1721 

                    1722 	.align	4

                    1723 

                    1724 ;entity	r0	param

                    1725 ;timeStamp	r1	param

                    1726 

                    1727 	.section ".bss","awb"

                    1728 .L2424:

                    1729 	.data

                    1730 	.text

                    1731 

                    1732 ;553:     }


                    1733 ;554: }


                    1734 

                    1735 ;555: 


                    1736 ;556: TrgOps IEDEntity_findChanges(IEDEntity entity, TrgOps trgOps)


                    1737 	.align	4

                    1738 	.align	4

                    1739 IEDEntity_findChanges::

0000084c e92d4030   1740 	stmfd	[sp]!,{r4-r5,lr}

00000850 e1a02000   1741 	mov	r2,r0

                    1742 ;557: {


                    1743 

                    1744 ;558:     IEDEntity child;


                    1745 ;559:     TrgOps change;


                    1746 ;560: 


                    1747 ;561:     change = (TrgOps)(entity->changed & trgOps);


                    1748 

00000854 e5920028   1749 	ldr	r0,[r2,40]

00000858 e1a05001   1750 	mov	r5,r1

0000085c e0150000   1751 	ands	r0,r5,r0

                    1752 ;562: 


                    1753 ;563:     if(change)


                    1754 

                    1755 .L2438:

                    1756 ;564:     {


                    1757 

                    1758 ;565:         return change;



                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
                    1759 

00000860 1a00000b   1760 	bne	.L2435

                    1761 .L2437:

                    1762 ;566:     }


                    1763 ;567: 


                    1764 ;568:     child = entity->firstChild;


                    1765 

00000864 e5924004   1766 	ldr	r4,[r2,4]

                    1767 ;569: 


                    1768 ;570:     while(child != NULL)


                    1769 

00000868 e3540000   1770 	cmp	r4,0

0000086c e1a00004   1771 	mov	r0,r4

00000870 0a000007   1772 	beq	.L2435

                    1773 .L2442:

                    1774 ;571:     {


                    1775 

                    1776 ;572:         change = IEDEntity_findChanges(child, trgOps);


                    1777 

00000874 e1a01005   1778 	mov	r1,r5

00000878 ebfffff3*  1779 	bl	IEDEntity_findChanges

                    1780 ;573:         if(change)


                    1781 

0000087c e3500000   1782 	cmp	r0,0

00000880 1afffff6   1783 	bne	.L2438

                    1784 ;574:         {


                    1785 

                    1786 ;575:             return change;


                    1787 

                    1788 ;576:         }


                    1789 ;577:         child = child->next;


                    1790 

00000884 e594400c   1791 	ldr	r4,[r4,12]

00000888 e3540000   1792 	cmp	r4,0

0000088c e1a00004   1793 	mov	r0,r4

00000890 1afffff7   1794 	bne	.L2442

                    1795 ;578:     }


                    1796 ;579:     return TRGOP_NONE;


                    1797 

                    1798 .L2435:

00000894 e8bd8030   1799 	ldmfd	[sp]!,{r4-r5,pc}

                    1800 	.endf	IEDEntity_findChanges

                    1801 	.align	4

                    1802 ;child	r4	local

                    1803 ;change	r0	local

                    1804 

                    1805 ;entity	r2	param

                    1806 ;trgOps	r5	param

                    1807 

                    1808 	.section ".bss","awb"

                    1809 .L2528:

                    1810 	.data

                    1811 	.text

                    1812 

                    1813 ;580: }


                    1814 

                    1815 ;581: 


                    1816 ;582: void IEDEntity_setReadOnlyRecursive(IEDEntity entity, bool readOnly)


                    1817 	.align	4

                    1818 	.align	4

                    1819 IEDEntity_setReadOnlyRecursive::


                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
00000898 e92d4030   1820 	stmfd	[sp]!,{r4-r5,lr}

                    1821 ;583: {


                    1822 

                    1823 ;584:     IEDEntity child;


                    1824 ;585: 


                    1825 ;586:     entity->readOnly = readOnly;


                    1826 

0000089c e1a05001   1827 	mov	r5,r1

000008a0 e5904004   1828 	ldr	r4,[r0,4]

                    1829 ;588:     while(child != NULL)


                    1830 

000008a4 e5c05022   1831 	strb	r5,[r0,34]

                    1832 ;587:     child = entity->firstChild;


                    1833 

000008a8 e3540000   1834 	cmp	r4,0

000008ac 0a000005   1835 	beq	.L2546

                    1836 .L2550:

                    1837 ;589:     {


                    1838 

                    1839 ;590:         IEDEntity_setReadOnlyRecursive(child, readOnly);


                    1840 

000008b0 e1a01005   1841 	mov	r1,r5

000008b4 e1a00004   1842 	mov	r0,r4

000008b8 ebfffff6*  1843 	bl	IEDEntity_setReadOnlyRecursive

                    1844 ;591:         child = child->next;


                    1845 

000008bc e594400c   1846 	ldr	r4,[r4,12]

000008c0 e3540000   1847 	cmp	r4,0

000008c4 1afffff9   1848 	bne	.L2550

                    1849 .L2546:

000008c8 e8bd8030   1850 	ldmfd	[sp]!,{r4-r5,pc}

                    1851 	.endf	IEDEntity_setReadOnlyRecursive

                    1852 	.align	4

                    1853 ;child	r4	local

                    1854 

                    1855 ;entity	r0	param

                    1856 ;readOnly	r5	param

                    1857 

                    1858 	.section ".bss","awb"

                    1859 .L2582:

                    1860 	.data

                    1861 	.text

                    1862 

                    1863 ;592:     }


                    1864 ;593: }


                    1865 	.align	4

                    1866 	.align	4

                    1867 updateFromDataSliceStub:

                    1868 ;34: {


                    1869 

000008cc e12fff1e*  1870 	ret	

                    1871 	.endf	updateFromDataSliceStub

                    1872 	.align	4

                    1873 

                    1874 ;da	none	param

                    1875 

                    1876 	.section ".bss","awb"

                    1877 .L2606:

                    1878 	.data

                    1879 	.text

                    1880 	.align	4


                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bns1.s
                    1881 

                    1882 	.data

                    1883 	.ghsnote version,6

                    1884 	.ghsnote tools,3

                    1885 	.ghsnote options,0

                    1886 	.text

                    1887 	.align	4

