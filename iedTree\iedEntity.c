#include "iedEntity.h"

#include "..\MemoryManager.h"
#include "..\bufViewBER.h"
#include "..\iedmodel.h"
#include "..\BaseAsnTypes.h"
#include "iedFC.h"
#include "iedObjects.h"
#include "iedFinalDA.h"
#include "iedTimeStamp.h"
#include "DataSet.h"
#include "..\IsoConnectionForward.h"

#include <string.h>
#include <stdlib.h>

#include "debug.h"

static bool createChildrenFromBER(IEDEntity parent,
    BufferView* childrenBER);

void *IEDEntity_alloc(size_t size)
{
    void* p = MM_alloc(size);
    if(p != NULL)
    {
        memset(p, 0, size);
    }
    return p;
}

// Заглушка типа IEDEntity_OnNewDataSlice
static void updateFromDataSliceStub(IEDEntity da)
{
    ERROR_REPORT("Stub called:onNewDataSliceStub");
}

static bool calcReadLen(IEDEntity entity, size_t* pLen )
{
    size_t len;
    bool result;
    switch (entity->type)
    {
    case IED_ENTITY_DA_TERMINAL_ITEM:
        result = IEDTermItemDA_calcReadLen(entity, &len);
        break;
    case IED_ENTITY_DA_TIMESTAMP:
        result = IEDTimeStampDA_calcReadLen(entity, &len);
        break;
    case IED_ENTITY_DA_CONST:
        result = IEDConstDA_calcReadLen(entity, &len);
        break;
    case IED_ENTITY_DA_VAR:
        result = IEDVarDA_calcReadLen(entity, &len);
        break;
    case IED_ENTITY_LN:
    case IED_ENTITY_FC:
    case IED_ENTITY_DO:
    case IED_ENTITY_DA:
        result = IEDComplexObj_calcReadLen(entity, &len);
        break;
    default:
        ERROR_REPORT("Invalid object to read");
        return false;
    }
    *pLen = len;
    return result;
}

static bool encodeRead(IEDEntity entity, BufferView* outBuf)
{
    switch (entity->type)
    {
    case IED_ENTITY_DA_TERMINAL_ITEM:
        return IEDTermItemDA_encodeRead(entity, outBuf);
    case IED_ENTITY_DA_TIMESTAMP:
        return IEDTimeStampDA_encodeRead(entity, outBuf);
    case IED_ENTITY_DA_CONST:
        return IEDConstDA_encodeRead(entity, outBuf);
    case IED_ENTITY_DA_VAR:
        return IEDVarDA_encodeRead(entity, outBuf);
    case IED_ENTITY_LN:
    case IED_ENTITY_FC:
    case IED_ENTITY_DO:
    case IED_ENTITY_DA:
        return IEDComplexObj_encodeRead(entity, outBuf);
    default:
        ERROR_REPORT("Invalid object to read");
        return false;
    }
}

static MmsDataAccessError write(IEDEntity entity, IsoConnection* isoConn,
                                   BufferView* value)
{
    switch (entity->type)
    {
    case IED_ENTITY_DA_TERMINAL_ITEM:
        return IEDTermItemDA_write(entity, isoConn, value);
    case IED_ENTITY_DA_TIMESTAMP:
        return IEDTimeStampDA_write(entity, value);
    case IED_ENTITY_DA_CONST:
        //Константы специально игнорируем
        if(!BufferView_skipAnyObject(value))
        {
            return DATA_ACCESS_ERROR_UNKNOWN;
        }
        return DATA_ACCESS_ERROR_SUCCESS;
    case IED_ENTITY_DA_VAR:
        return IEDVarDA_write(entity, isoConn, value);
    case IED_ENTITY_DA:
    case IED_ENTITY_DO:
        return IEDComplexObj_write(entity, isoConn, value);
    default:
        ERROR_REPORT("Invalid object to write");
        return DATA_ACCESS_ERROR_OBJECT_ACCESS_UNSUPPORTED;
    }
}


void IEDEntity_addChild(IEDEntity parent, IEDEntity child)
{
    child->parent = parent;
    child->next = NULL;
    if (parent->firstChild == NULL)
    {
        parent->firstChild = child;
    }
    else
    {
        parent->lastChild->next = child;
    }
    parent->lastChild = child;
}

void IEDEntity_init(IEDEntity iedEntity, IEDEntity parent)
{
    //Для всех объектов ставим заглушку.
    //Потом каждый объект заменит её на то что ему надо.
    iedEntity->updateFromDataSlice = updateFromDataSliceStub;

    // Эти функции общие для всех элементов
    // они внутри используют switch по типу элемента.
    // Со временем можно постепенно заменить их на индивидуальные
    // для каждого.
    iedEntity->encodeRead = encodeRead;
    iedEntity->calcReadLen = calcReadLen;
    iedEntity->write = write;

    // По умолчанию невалидное значение
    iedEntity->dataSliceOffset = -1;


    if(parent != NULL)
    {
        IEDEntity_addChild(parent, iedEntity);
    }
}

bool IEDEntity_create(IEDEntity* iedEntity, IEDEntity parent)
{
    size_t size = sizeof(struct IEDEntityStruct);
    *iedEntity = IEDEntity_alloc(size);
    if(*iedEntity == NULL)
    {
        ERROR_REPORT("IEDEntity allocation error");
        return false;
    }
    IEDEntity_init(*iedEntity, parent);
    return true;
}

bool IEDEntity_postCreate(IEDEntity entity)
{
    IEDEntity nextChild;

    //Вызываем postCreate детей
    nextChild = entity->firstChild;
    while(nextChild != NULL)
    {
        bool result = IEDEntity_postCreate(nextChild);
        if(!result)
        {
            return false;
        }
        nextChild = nextChild->next;
    }

    //Выполняем postCreate для себя
    switch(entity->tag) {
    case IED_DATA_SET:
        return DataSet_postCreate(entity);
    }
    return true;
}


static bool initSpecific(IEDEntity entity)
{
    switch(entity->tag) {
    case IED_LD:
        return IEDLD_init(entity);
    case IED_VMD_DATA_SECTION:
    case IED_LN:
        return IEDComplexObj_init(entity);
    case IED_FC:
        return IEDFC_init(entity);
    case IED_DO:
        return IEDDO_init(entity);
    case IED_DA:
        return IEDDA_init(entity);
    case IED_DA_FINAL:
        return IEDFinalDA_init(entity);
    case IED_DATA_SET:
        return DataSet_init(entity);
    }
    return true;
}

// Заполняет служебную информацию  любого IEDEntiy из BER
// вроде имени и флагов
// ber должен указывать на начало содержимого объекта
//(то есть сразу после длины).
// При успешном завершении ber указывает на буфер сразу
// за параметрами.
static bool readServiceInfo(IEDEntity entity, BufferView* ber)
{
    uint32_t objFlags;

    while(!BufferView_endOfBuf(ber))
    {
        uint8_t tag;
        if(!BufferView_peekTag(ber, &tag))
        {
            break;
        }

        if(!IEDModel_isServiceInfo(tag))
        {
            //Служебная информация кончилась
            break;
        }

        switch(tag)
        {
            case ASN_VISIBLE_STRING:
                //Имя
                if (!BufferView_decodeStringViewTL(ber, ASN_VISIBLE_STRING,
                    &entity->name))
                {
                    ERROR_REPORT("Error getting object name");
                    return false;
                }
                break;
            case IED_OBJ_FLAGS:
                if(!BufferView_decodeUInt32TL(ber, IED_OBJ_FLAGS, &objFlags))
                {
                    ERROR_REPORT("Error getting object flags");
                    return false;
                }
                if(objFlags & IED_OBJ_FLAGS_QCHG)
                {
                    entity->trgOps = TRGOP_QCHG;
                }
                else if (objFlags & IED_OBJ_FLAGS_DCHG)
                {
                    entity->trgOps = TRGOP_DCHG;
                }

                break;
            default:
                if(!BufferView_skipAnyObject(ber))
                {
                    ERROR_REPORT("BER error");
                    return false;
                }
        }
    }

    //Собрали всю инфу

    return true;
}

bool IEDEntity_createFromBER(IEDEntity* iedEntity, BufferView* ber,
                             IEDEntity parent)
{
    uint8_t tag;
    size_t len, fullLen;
    BufferView berObject;
    IEDEntity entity;
    uint8_t* pObjectBER = ber->p + ber->pos;

    if(!IEDEntity_create(&entity, parent))
    {
        ERROR_REPORT("IEDEntity_create error");
        return false;
    }

    *iedEntity = entity;

    //Получить размер объекта
    if(!BufferView_decodeTL(ber, &tag, &len, &fullLen))
    {
        return false;
    }
    entity->tag = tag;

    BufferView_init(&entity->ber, pObjectBER, fullLen, 0 );

    BufferView_init(&berObject, ber->p + ber->pos, len, 0);

    //Перемещаем позицию буфера за объект.
    //Дальше будем действовать через его собственный буфер berObject
    if(!BufferView_advance(ber,len))
    {
        return false;
    }

    if(! readServiceInfo(entity, &berObject))
    {
        return false;
    }

    //Дети
    if (tag != IED_DA_FINAL && tag != IED_DATA_SET)
    {
        if(!createChildrenFromBER(entity, &berObject))
        {
            return false;
        }
    }
    //Инициализация специфических особенностей объекта делается
    //после полной инициализации его детей.
    //Т.е. тип(или подтип) родителя может зависеть от типа детей
    return initSpecific(entity);
}

bool createChildrenFromBER(IEDEntity parent,
                                     BufferView* childrenBER)
{

    while(!BufferView_endOfBuf(childrenBER))
    {
        IEDEntity child;
        if(!IEDEntity_createFromBER(&child, childrenBER, parent))
        {
            return false;
        }
    }
    return true;
}

IEDEntity IEDEntity_getChildByName(IEDEntity parent, StringView* name)
{
    IEDEntity child = parent->firstChild;
    while(child != NULL)
    {
        if(0 == StringView_cmp(name, &child->name))
        {
            return child;
        }
        child = child->next;
    }
    return NULL;
}

IEDEntity IEDEntity_getChildByCStrName(IEDEntity parent, const char* name)
{
    IEDEntity child = parent->firstChild;
    while(child != NULL)
    {
        if(0 == StringView_cmpCStr(&child->name, name))
        {
            return child;
        }
        child = child->next;
    }
    return NULL;
}

IEDEntity IEDEntity_getChildByFullName(IEDEntity parent,  StringView* name)
{
    StringView fullName = *name;
    StringView directChildName;
    StringView remainingName;
    bool lastName;
    IEDEntity entity = parent;

    do
    {
        lastName = !StringView_splitChar(&fullName, '$',
                                           &directChildName, &remainingName);
        if(lastName)
        {
            //Разделитель не найден, значит это последнее имя
            directChildName = fullName;
        }
        entity = IEDEntity_getChildByName(entity, &directChildName);
        if(entity == NULL)
        {
            return NULL;
        }
        fullName = remainingName;
    } while(!lastName);

    return entity;
}

IEDEntity IEDEntity_getChildByTag(IEDEntity parent,  uint8_t tag)
{
    IEDEntity child = parent->firstChild;
    while(child != NULL)
    {
        if(child->tag == tag)
        {
            return child;
        }
        child = child->next;
    }
    return NULL;
}

MmsDataAccessError IEDEntity_write(IEDEntity entity, IsoConnection* isoConn,
                                   BufferView* value)
{    
    if(entity->readOnly)
    {
        return DATA_ACCESS_ERROR_OBJECT_ACCESS_DENIED;
    }
    return entity->write(entity, isoConn, value);
}

bool IEDEntity_getFullName(IEDEntity entity, BufferView* nameBuf)
{
    char *delimiter;
    if(entity->parent !=NULL && entity->type != IED_ENTITY_LD)
    {
        if(!IEDEntity_getFullName(entity->parent, nameBuf))
        {
            return false;
        }

        if(entity->name.len == 0)
        {
            return true;
        }

        switch(entity->type)
        {
        case IED_ENTITY_LN:
            delimiter = "/";
            break;
        default:
            delimiter = "$";
            break;
        }
        if(!BufferView_writeStr(nameBuf, delimiter))
        {
            ERROR_REPORT("Name buffer overflow");
            return false;
        }
    }
    return BufferView_writeStringView(nameBuf, &entity->name);
}

bool IEDEntity_getFullItemId(IEDEntity entity, BufferView* nameBuf)
{
    if(entity->parent !=NULL && entity->type != IED_ENTITY_LN)
    {
        if(!IEDEntity_getFullItemId(entity->parent, nameBuf))
        {
            return false;
        }

        if(entity->name.len == 0)
        {
            return true;
        }

        if(!BufferView_writeStr(nameBuf, "$"))
        {
            ERROR_REPORT("Name buffer overflow");
            return false;
        }
    }
    return BufferView_writeStringView(nameBuf, &entity->name);
}

bool IEDEntity_getDomainId(IEDEntity entity, StringView** name)
{
    if(entity->type == IED_ENTITY_LD)
    {
        *name = &entity->name;
        return true;
    }
    if(entity->parent ==NULL )
    {
        return false;
    }

    return IEDEntity_getDomainId(entity->parent, name);
}

void IEDEntity_attachTimeStamp(IEDEntity entity, TimeStamp* timeStamp)
{
    IEDEntity child;

    if(entity->type == IED_ENTITY_DA_TIMESTAMP)
    {
        return;
    }
    entity->timeStamp = timeStamp;

    child = entity->firstChild;
    while(child != NULL)
    {
        IEDEntity_attachTimeStamp(child, timeStamp);
        child = child->next;
    }
}

void IEDEntity_writeFullName(IEDEntity entity, BufferView* bv )
{
    if(entity->parent !=NULL)
    {
        IEDEntity_writeFullName(entity->parent, bv);
        BufferView_writeStr(bv, "/");
    }
    BufferView_writeStringView(bv, &entity->name);
}

void IEDEntity_printFullName(IEDEntity entity)
{
    BufferView nameBuf;
    void* p = malloc(257);
    if(p == NULL)
    {
        return;
    }
    memset(p, 0, 257);
    BufferView_init(&nameBuf, p, 256, 0);
    IEDEntity_writeFullName(entity, &nameBuf);	//TRACE(nameBuf.p);
    TRACE((char*)nameBuf.p);
    free(p);
}

void IEDEntity_setTimeStamp(IEDEntity entity, uint64_t timeStamp)
{
    if(entity->timeStamp != NULL)
    {
        entity->timeStamp->timeStamp = timeStamp;
    }
}

TrgOps IEDEntity_findChanges(IEDEntity entity, TrgOps trgOps)
{
    IEDEntity child;
    TrgOps change;

    change = (TrgOps)(entity->changed & trgOps);

    if(change)
    {
        return change;
    }

    child = entity->firstChild;

    while(child != NULL)
    {
        change = IEDEntity_findChanges(child, trgOps);
        if(change)
        {
            return change;
        }
        child = child->next;
    }
    return TRGOP_NONE;
}

void IEDEntity_setReadOnlyRecursive(IEDEntity entity, bool readOnly)
{
    IEDEntity child;

    entity->readOnly = readOnly;
    child = entity->firstChild;
    while(child != NULL)
    {
        IEDEntity_setReadOnlyRecursive(child, readOnly);
        child = child->next;
    }
}
