                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5fs1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=timers_iednexus.c -o gh_5fs1.o -list=timers_iednexus.lst C:\Users\<USER>\AppData\Local\Temp\gh_5fs1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_5fs1.s
Source File: timers_iednexus.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		timers_iednexus.c -o timers_iednexus.o

                      12 ;Source File:   timers_iednexus.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:53 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1:  #include "timers.h"


                      23 ;2: #include "Clib.h"


                      24 ;3: #include "string.h"


                      25 ;4: #include <hw/sama5dx.h>


                      26 ;5: 


                      27 ;6: //Проверка кодировки


                      28 ;7: 


                      29 ;8: //Для аппартного таймера GOOSE


                      30 ;9: #define TC_PERIOD	1000  // период таймера в мкс


                      31 ;10: #define TIMER_ID	(26)  // see at91core\core\timer\src\timer.asm


                      32 ;11: #define TC_TIMER_BASE (volatile unsigned long*)0xF0010080


                      33 ;12: #define AIC_BASE	0xFFFFF000


                      34 ;13: #define PMC_BASE	0xFFFFFC00


                      35 ;14: #define MCK	138000000


                      36 ;15: 


                      37 ;16: static STIMER g_softTimer;


                      38 ;17: static uint32_t tickCounter32 = 0;


                      39 ;18: 


                      40 ;19: // предыдущий обработчик аппаратного таймера (или dummyCallback)


                      41 ;20: void (*oldHWTmrIsrHandlerPtr)(unsigned int id) = NULL;


                      42 ;21: 


                      43 ;22: static void(*goose1msCallbackFunc)(void) = NULL;


                      44 ;23: static void(*integrity1msCallbackFunc)(void) = NULL;


                      45 ;24: static void(*netBusCheck1msCallbackFunc)(void) = NULL;


                      46 ;25: 


                      47 ;26: static void softTimerProc(void)


                      48 ;27: {    


                      49 ;28:     if (integrity1msCallbackFunc)


                      50 ;29:     {



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5fs1.s
                      51 ;30:         integrity1msCallbackFunc();


                      52 ;31:     }


                      53 ;32: 	if (netBusCheck1msCallbackFunc)


                      54 ;33: 	{


                      55 ;34: 		netBusCheck1msCallbackFunc();


                      56 ;35: 	}	


                      57 ;36:     tickCounter32++;


                      58 ;37: }


                      59 ;38: 


                      60 ;39: void Timers_setGoose1msCallBack(void(*func)(void))


                      61 ;40: {


                      62 ;41:     goose1msCallbackFunc = func;


                      63 ;42: }


                      64 ;43: 


                      65 ;44: void Timers_setIntegrity1msCallBack(void(*func)(void))


                      66 ;45: {


                      67 ;46:     integrity1msCallbackFunc = func;


                      68 ;47: }


                      69 ;48: 


                      70 ;49: void Timers_setNetBusChek1msCallback(void(*func)(void))


                      71 ;50: {


                      72 ;51: 	netBusCheck1msCallbackFunc = func;


                      73 ;52: }


                      74 ;53: 


                      75 ;54: static void gooseTimerIsr(unsigned int id)


                      76 ;55: {


                      77 ;56:     volatile unsigned int *TC = (volatile unsigned int*)TC_TIMER_BASE;


                      78 ;57:     unsigned int status =  TC[TC_SR] & TC[TC_IMR];


                      79 ;58:     // если прерывание от нужного таймера


                      80 ;59:     if (status & TC_SR_CPCS)


                      81 ;60:     {


                      82 ;61:         // пользовательский обработчик


                      83 ;62:         if (goose1msCallbackFunc)


                      84 ;63:         {


                      85 ;64:             goose1msCallbackFunc();


                      86 ;65:         }


                      87 ;66:     }


                      88 ;67:     // вызываем следующий обработчик


                      89 ;68:     oldHWTmrIsrHandlerPtr(id);


                      90 ;69: }


                      91 ;70: 


                      92 ;71: static void initGOOSETimer(void)


                      93 

                      94 ;105: }


                      95 

                      96 ;106: 


                      97 ;107: void Timers_init(void)


                      98 ;108: {    


                      99 ;109:     memset(&g_softTimer, 0, sizeof(g_softTimer));


                     100 ;110:     CreateTimer(&g_softTimer);


                     101 ;111:     g_softTimer.TimerProc = softTimerProc;


                     102 ;112:     g_softTimer.AlarmTime = 1;


                     103 ;113:     g_softTimer.Precision = 1;


                     104 ;114:     g_softTimer.Started = 1;


                     105 ;115: 


                     106 ;116:     initGOOSETimer();


                     107 ;117: }


                     108 ;118: 


                     109 ;119: uint32_t Timers_getTickCount(void)


                     110 

                     111 ;122: }



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5fs1.s
                     112 

                     113 	.text

                     114 	.align	4

                     115 softTimerProc:

00000000 e92d4100    116 	stmfd	[sp]!,{r8,lr}

00000004 e59f81c8*   117 	ldr	r8,.L110

00000008 e598c000    118 	ldr	r12,[r8]

0000000c e35c0000    119 	cmp	r12,0

00000010 11a0e00f    120 	movne	lr,pc

00000014 112fff1c*   121 	bxne	r12

00000018 e59f81b8*   122 	ldr	r8,.L111

0000001c e598c000    123 	ldr	r12,[r8]

00000020 e35c0000    124 	cmp	r12,0

00000024 11a0e00f    125 	movne	lr,pc

00000028 112fff1c*   126 	bxne	r12

0000002c e59f11a8*   127 	ldr	r1,.L112

00000030 e5910000    128 	ldr	r0,[r1]

00000034 e2800001    129 	add	r0,r0,1

00000038 e5810000    130 	str	r0,[r1]

0000003c e8bd4100    131 	ldmfd	[sp]!,{r8,lr}

00000040 e12fff1e*   132 	ret	

                     133 	.endf	softTimerProc

                     134 	.align	4

                     135 

                     136 	.section ".bss","awb"

                     137 .L89:

                     138 	.data

                     139 .L90:

00000000 00000000    140 tickCounter32:	.data.b	0,0,0,0

                     141 	.type	tickCounter32,$object

                     142 	.size	tickCounter32,4

                     143 .L91:

00000004 00000000    144 goose1msCallbackFunc:	.data.b	0,0,0,0

                     145 	.type	goose1msCallbackFunc,$object

                     146 	.size	goose1msCallbackFunc,4

                     147 .L92:

00000008 00000000    148 integrity1msCallbackFunc:	.data.b	0,0,0,0

                     149 	.type	integrity1msCallbackFunc,$object

                     150 	.size	integrity1msCallbackFunc,4

                     151 .L93:

0000000c 00000000    152 netBusCheck1msCallbackFunc:	.data.b	0,0,0,0

                     153 	.type	netBusCheck1msCallbackFunc,$object

                     154 	.size	netBusCheck1msCallbackFunc,4

                     155 	.section ".bss","awb"

00000000 00000000    156 g_softTimer:	.space	20

00000004 00000000 
00000008 00000000 
0000000c 00000000 
00000010 00000000 
                     157 	.data

                     158 	.text

                     159 

                     160 

                     161 	.align	4

                     162 	.align	4

                     163 Timers_setGoose1msCallBack::

00000044 e59fc194*   164 	ldr	r12,.L148

00000048 e58c0000    165 	str	r0,[r12]

0000004c e12fff1e*   166 	ret	

                     167 	.endf	Timers_setGoose1msCallBack

                     168 	.align	4


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5fs1.s
                     169 

                     170 ;func	r0	param

                     171 

                     172 	.data

                     173 	.text

                     174 

                     175 

                     176 	.align	4

                     177 	.align	4

                     178 Timers_setIntegrity1msCallBack::

00000050 e59fc17c*   179 	ldr	r12,.L110

00000054 e58c0000    180 	str	r0,[r12]

00000058 e12fff1e*   181 	ret	

                     182 	.endf	Timers_setIntegrity1msCallBack

                     183 	.align	4

                     184 

                     185 ;func	r0	param

                     186 

                     187 	.data

                     188 	.text

                     189 

                     190 

                     191 	.align	4

                     192 	.align	4

                     193 Timers_setNetBusChek1msCallback::

0000005c e59fc174*   194 	ldr	r12,.L111

00000060 e58c0000    195 	str	r0,[r12]

00000064 e12fff1e*   196 	ret	

                     197 	.endf	Timers_setNetBusChek1msCallback

                     198 	.align	4

                     199 

                     200 ;func	r0	param

                     201 

                     202 	.data

                     203 	.text

                     204 

                     205 

                     206 	.align	4

                     207 	.align	4

                     208 gooseTimerIsr:

00000068 e92d4070    209 	stmfd	[sp]!,{r4-r6,lr}

0000006c e59f6170*   210 	ldr	r6,.L274

00000070 e596c000    211 	ldr	r12,[r6]

00000074 e516600c    212 	ldr	r6,[r6,-12]

00000078 e59f5160*   213 	ldr	r5,.L148

0000007c e00cc006    214 	and	r12,r12,r6

00000080 e31c0010    215 	tst	r12,16

00000084 1595c000    216 	ldrne	r12,[r5]

00000088 e1a04000    217 	mov	r4,r0

0000008c 135c0000    218 	cmpne	r12,0

00000090 11a0e00f    219 	movne	lr,pc

00000094 112fff1c*   220 	bxne	r12

00000098 e59f0148*   221 	ldr	r0,.L275

0000009c e590c000    222 	ldr	r12,[r0]

000000a0 e1a00004    223 	mov	r0,r4

000000a4 e1a0e00f    224 	mov	lr,pc

000000a8 e12fff1c*   225 	bx	r12

000000ac e8bd4070    226 	ldmfd	[sp]!,{r4-r6,lr}

000000b0 e12fff1e*   227 	ret	

                     228 	.endf	gooseTimerIsr

                     229 	.align	4


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5fs1.s
                     230 ;status	r12	local

                     231 

                     232 ;id	r4	param

                     233 

                     234 	.data

                     235 	.text

                     236 

                     237 

                     238 	.align	4

                     239 	.align	4

                     240 Timers_init::

000000b4 e92d4070    241 	stmfd	[sp]!,{r4-r6,lr}

000000b8 e59f612c*   242 	ldr	r6,.L308

000000bc e3a02014    243 	mov	r2,20

000000c0 e1a00006    244 	mov	r0,r6

000000c4 e3a01000    245 	mov	r1,0

000000c8 eb000000*   246 	bl	memset

000000cc e1a00006    247 	mov	r0,r6

000000d0 eb000000*   248 	bl	CreateTimer

000000d4 e59f0114*   249 	ldr	r0,.L309

000000d8 e59f4114*   250 	ldr	r4,.L310

000000dc e5860010    251 	str	r0,[r6,16]

000000e0 e3a05001    252 	mov	r5,1

000000e4 e5865008    253 	str	r5,[r6,8]

000000e8 e5c65007    254 	strb	r5,[r6,7]

000000ec e5c65005    255 	strb	r5,[r6,5]

                     256 ;72: {


                     257 

                     258 ;73:     int prev = lockInterrupt();


                     259 

000000f0 eb000000*   260 	bl	lockInterrupt

000000f4 e1a06000    261 	mov	r6,r0

                     262 ;74:     volatile unsigned long *TC = TC_TIMER_BASE;


                     263 

                     264 ;75: 


                     265 ;76:     volatile unsigned int *AIC = (volatile unsigned int*)AIC_BASE;


                     266 

                     267 ;77:     volatile unsigned int *PMC = (volatile unsigned int*)PMC_BASE;


                     268 

                     269 ;78:     // старый обработчик


                     270 ;79:     GetIntCallBack(TIMER_ID,(void (**)())&oldHWTmrIsrHandlerPtr);


                     271 

000000f8 e59f10e8*   272 	ldr	r1,.L275

000000fc e3a0001a    273 	mov	r0,26

00000100 eb000000*   274 	bl	GetIntCallBack

                     275 ;80:     // включаем таймер


                     276 ;81:     PMC[PMC_PCER0]=1 << TIMER_ID;


                     277 

00000104 e3a00640    278 	mov	r0,1<<26

00000108 e3e01fc0    279 	mvn	r1,3<<8

0000010c e22110ef    280 	eor	r1,r1,239

00000110 e5810000    281 	str	r0,[r1]

                     282 ;82:     AIC[AIC_SSR] = TIMER_ID;


                     283 

00000114 e3a0001a    284 	mov	r0,26

00000118 e3e01ef0    285 	mvn	r1,15<<8

0000011c e22110ff    286 	eor	r1,r1,255

00000120 e5810000    287 	str	r0,[r1]

                     288 ;83:     AIC[AIC_SMR] = (0<<5) | 3;


                     289 

00000124 e3a01003    290 	mov	r1,3


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5fs1.s
00000128 e3e02ef0    291 	mvn	r2,15<<8

0000012c e22220fb    292 	eor	r2,r2,251

00000130 e5821000    293 	str	r1,[r2]

                     294 ;84:     AIC[AIC_IECR] = 1;


                     295 

00000134 e3e01ef0    296 	mvn	r1,15<<8

00000138 e22110bf    297 	eor	r1,r1,191

0000013c e5815000    298 	str	r5,[r1]

                     299 ;85: 


                     300 ;86: 


                     301 ;87:     SetIntCallBack(TIMER_ID,gooseTimerIsr);


                     302 

00000140 e59f10b0*   303 	ldr	r1,.L311

00000144 eb000000*   304 	bl	SetIntCallBack

                     305 ;88:     // Disable CLK


                     306 ;89:     TC[TC_CCR]=TC_CCR_CLKDIS;


                     307 

00000148 e3a00002    308 	mov	r0,2

0000014c e5840000    309 	str	r0,[r4]

                     310 ;90: 


                     311 ;91:     // Disable all timer interrupts


                     312 ;92:     TC[TC_IDR] = 0xFF;


                     313 

00000150 e3a000ff    314 	mov	r0,255

00000154 e5840028    315 	str	r0,[r4,40]

                     316 ;93: 


                     317 ;94:     TC[TC_CMR] = TC_CMR_WAVE | TC_CMR_CPCTRG | TC_CMR_TIMER_CLOCK2;  // MCK/8


                     318 

00000158 e3a00cc0    319 	mov	r0,3<<14

0000015c e2800001    320 	add	r0,r0,1

00000160 e5840004    321 	str	r0,[r4,4]

                     322 ;95: 


                     323 ;96: 


                     324 ;97:     TC[TC_SR]; // read status


                     325 

00000164 e5940020    326 	ldr	r0,[r4,32]

                     327 ;98:     TC[TC_IER] = TC_IER_CPCS; // enable interrupt


                     328 

00000168 e3a00010    329 	mov	r0,16

0000016c e5840024    330 	str	r0,[r4,36]

                     331 ;99:     TC[TC_RC] = MCK/8/(1000000/TC_PERIOD);


                     332 

00000170 e3a00c43    333 	mov	r0,67<<8

00000174 e2800062    334 	add	r0,r0,98

00000178 e584001c    335 	str	r0,[r4,28]

                     336 ;100: 


                     337 ;101: 


                     338 ;102:     // Reset timer and enable


                     339 ;103:     TC[TC_CCR]=TC_CCR_SWTRG | TC_CCR_CLKEN;


                     340 

0000017c e3a00005    341 	mov	r0,5

00000180 e5840000    342 	str	r0,[r4]

                     343 ;104:     unlockInterrupt(prev);


                     344 

00000184 e1a00006    345 	mov	r0,r6

00000188 e8bd4070    346 	ldmfd	[sp]!,{r4-r6,lr}

0000018c ea000000*   347 	b	unlockInterrupt

                     348 	.endf	Timers_init

                     349 	.align	4

                     350 ;prev	r6	local

                     351 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5fs1.s
                     352 	.data

                     353 	.text

                     354 

                     355 

                     356 ;123: 


                     357 ;124: bool Timers_isTimeout(uint32_t startTime, uint32_t timeout)


                     358 	.align	4

                     359 	.align	4

                     360 Timers_isTimeout::

                     361 ;125: {


                     362 

                     363 ;126:     uint32_t tickCount = Timers_getTickCount();


                     364 

                     365 ;120: {


                     366 

                     367 ;121:     return tickCounter32;


                     368 

00000190 e59f3044*   369 	ldr	r3,.L112

00000194 e5932000    370 	ldr	r2,[r3]

                     371 ;127:     uint32_t timePassed;


                     372 ;128: 


                     373 ;129:     // сколько прошло времени с момента запуска


                     374 ;130:     if (startTime  <= tickCount)


                     375 

00000198 e1500002    376 	cmp	r0,r2

0000019c e0423000    377 	sub	r3,r2,r0

000001a0 8a000003    378 	bhi	.L318

                     379 ;131:     {


                     380 

                     381 ;132:         timePassed = tickCount - startTime;


                     382 

                     383 ;137:     }


                     384 ;138:     return timePassed > timeout;


                     385 

000001a4 e1530001    386 	cmp	r3,r1

000001a8 83a00001    387 	movhi	r0,1

000001ac 93a00000    388 	movls	r0,0

000001b0 ea000003    389 	b	.L312

                     390 .L318:

                     391 ;133:     }


                     392 ;134:     else // переполнение


                     393 ;135:     {


                     394 

                     395 ;136:         timePassed = UINT32_MAX - startTime + tickCount;


                     396 

000001b4 e2430001    397 	sub	r0,r3,1

                     398 ;137:     }


                     399 ;138:     return timePassed > timeout;


                     400 

000001b8 e1500001    401 	cmp	r0,r1

000001bc 83a00001    402 	movhi	r0,1

000001c0 93a00000    403 	movls	r0,0

                     404 .L312:

000001c4 e12fff1e*   405 	ret	

                     406 	.endf	Timers_isTimeout

                     407 	.align	4

                     408 ;tickCount	r2	local

                     409 ;timePassed	r0	local

                     410 

                     411 ;startTime	r0	param

                     412 ;timeout	r1	param


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5fs1.s
                     413 

                     414 	.section ".bss","awb"

                     415 .L368:

                     416 	.data

                     417 	.text

                     418 

                     419 ;139: }


                     420 	.align	4

                     421 	.align	4

                     422 Timers_getTickCount::

                     423 ;120: {


                     424 

                     425 ;121:     return tickCounter32;


                     426 

000001c8 e59fc00c*   427 	ldr	r12,.L112

000001cc e59c0000    428 	ldr	r0,[r12]

000001d0 e12fff1e*   429 	ret	

                     430 	.endf	Timers_getTickCount

                     431 	.align	4

                     432 

                     433 	.data

                     434 	.text

                     435 	.align	4

                     436 .L110:

000001d4 00000000*   437 	.data.w	.L92

                     438 	.type	.L110,$object

                     439 	.size	.L110,4

                     440 

                     441 .L111:

000001d8 00000000*   442 	.data.w	.L93

                     443 	.type	.L111,$object

                     444 	.size	.L111,4

                     445 

                     446 .L112:

000001dc 00000000*   447 	.data.w	.L90

                     448 	.type	.L112,$object

                     449 	.size	.L112,4

                     450 

                     451 .L148:

000001e0 00000000*   452 	.data.w	.L91

                     453 	.type	.L148,$object

                     454 	.size	.L148,4

                     455 

                     456 .L274:

000001e4 f00100ac    457 	.data.w	0xf00100ac

                     458 	.type	.L274,$object

                     459 	.size	.L274,4

                     460 

                     461 .L275:

000001e8 00000000*   462 	.data.w	oldHWTmrIsrHandlerPtr

                     463 	.type	.L275,$object

                     464 	.size	.L275,4

                     465 

                     466 .L308:

000001ec 00000000*   467 	.data.w	g_softTimer

                     468 	.type	.L308,$object

                     469 	.size	.L308,4

                     470 

                     471 .L309:

000001f0 00000000*   472 	.data.w	softTimerProc

                     473 	.type	.L309,$object


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5fs1.s
                     474 	.size	.L309,4

                     475 

                     476 .L310:

000001f4 f0010080    477 	.data.w	0xf0010080

                     478 	.type	.L310,$object

                     479 	.size	.L310,4

                     480 

                     481 .L311:

000001f8 00000000*   482 	.data.w	gooseTimerIsr

                     483 	.type	.L311,$object

                     484 	.size	.L311,4

                     485 

                     486 	.align	4

                     487 ;g_softTimer	g_softTimer	static

                     488 ;tickCounter32	.L90	static

                     489 ;goose1msCallbackFunc	.L91	static

                     490 ;integrity1msCallbackFunc	.L92	static

                     491 ;netBusCheck1msCallbackFunc	.L93	static

                     492 

                     493 	.data

                     494 .L420:

                     495 	.globl	oldHWTmrIsrHandlerPtr

00000010 00000000    496 oldHWTmrIsrHandlerPtr:	.data.b	0,0,0,0

                     497 	.type	oldHWTmrIsrHandlerPtr,$object

                     498 	.size	oldHWTmrIsrHandlerPtr,4

                     499 	.ghsnote version,6

                     500 	.ghsnote tools,3

                     501 	.ghsnote options,0

                     502 	.text

                     503 	.align	4

                     504 	.data

                     505 	.align	4

                     506 	.section ".bss","awb"

                     507 	.align	4

                     508 	.text

