                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8sk1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_rcb.c -o gh_8sk1.o -list=mms_rcb.lst C:\Users\<USER>\AppData\Local\Temp\gh_8sk1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_8sk1.s
Source File: mms_rcb.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile mms_rcb.c -o

                      11 ;		mms_rcb.o

                      12 ;Source File:   mms_rcb.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:03 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "mms_rcb.h"


                      23 ;2: #include "iedmodel.h"


                      24 ;3: #include "mms_data.h"


                      25 ;4: #include "rcb.h"


                      26 ;5: #include "reports.h"


                      27 ;6: #include "IEDCompile/AccessInfo.h"


                      28 ;7: #include "IEDCompile/InnerAttributeTypes.h"


                      29 ;8: 


                      30 ;9: #include "debug.h"


                      31 ;10: #include "AsnEncoding.h"


                      32 ;11: 


                      33 ;12: #include <stddef.h>


                      34 ;13: #include <stdint.h>


                      35 ;14: 


                      36 ;15: int encodeAccessAttrRCB(uint8_t* outBuf, int bufPos, int accessDataPos, bool determineSize)


                      37 ;16: {


                      38 ;17:     CBAttrAccessInfo* pAccessInfo =


                      39 ;18:             (CBAttrAccessInfo*)getAlignedDescrStruct(accessDataPos);


                      40 ;19:     if(pAccessInfo == NULL)


                      41 ;20:     {


                      42 ;21:         ERROR_REPORT("Unable to get access info struct");


                      43 ;22:         return 0;


                      44 ;23:     }


                      45 ;24: 


                      46 ;25:     switch(pAccessInfo->attrCode)


                      47 ;26:     {


                      48 ;27:         case RptEna:


                      49 ;28: 		case GI:


                      50 ;29:         case Resv:



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8sk1.s
                      51 ;30:             return encodeAccessAttrBoolean( outBuf, bufPos, determineSize);


                      52 ;31: 		case SqNum:


                      53 ;32: 			return encodeAccessAttrUInt(outBuf, bufPos, 16, determineSize);


                      54 ;33: 		case IntgPd:


                      55 ;34: 			return encodeAccessAttrUInt(outBuf, bufPos, 32, determineSize);


                      56 ;35: 		case EntryID:


                      57 ;36: 			return encodeAccessAttrString(outBuf, bufPos,


                      58 ;37: 				IEC61850_BER_OCTET_STRING, 8, determineSize);


                      59 ;38: 		case ConfRev:


                      60 ;39: 			return encodeAccessAttrUInt(outBuf, bufPos, 32, determineSize);


                      61 ;40:         default :


                      62 ;41:             ERROR_REPORT("Invalid RCB DA code");


                      63 ;42:             return 0;


                      64 ;43:     }


                      65 ;44: }


                      66 ;45: 


                      67 ;46: /*


                      68 ;47: static int determineAttrRCBReadSize(CBAttrAccessInfo* pAccessInfo)


                      69 ;48: {


                      70 ;49:     switch(pAccessInfo->attrCode){


                      71 ;50:         case RptEna:


                      72 ;51: 		case GI:


                      73 ;52:             return 3; // boolean


                      74 ;53: 		case SqNum:


                      75 ;54: 			!!


                      76 ;55: 			return encodeUInt32Value(uint8_t* outBuf, int bufPos, uint32_t value,


                      77 ;56: 				TRUE)


                      78 ;57: 		case EntryID:


                      79 ;58: 			!!!


                      80 ;59:         default:


                      81 ;60:             ERROR_REPORT("Invalid RCB DA code");


                      82 ;61:             return 0;


                      83 ;62:     }


                      84 ;63: }


                      85 ;64: */


                      86 ;65: 


                      87 ;66: static int encodeReadRptEna(uint8_t* outBuf, int bufPos, 


                      88 

                      89 ;80: }


                      90 

                      91 ;81: 


                      92 ;82: static int encodeReadResv(uint8_t* outBuf, int bufPos,


                      93 

                      94 ;96: }


                      95 

                      96 ;97: 


                      97 ;98: static int encodeReadGI(uint8_t* outBuf, int bufPos,


                      98 

                      99 ;112: }


                     100 

                     101 ;113: 


                     102 ;114: static int encodeReadSqNum(uint8_t* outBuf, int bufPos,


                     103 

                     104 ;128: }


                     105 

                     106 ;129: 


                     107 ;130: static int encodeReadIntgPd(uint8_t* outBuf, int bufPos,


                     108 

                     109 ;144: }


                     110 

                     111 ;145: 



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8sk1.s
                     112 ;146: static int encodeReadConfRev(uint8_t* outBuf, int bufPos,


                     113 

                     114 ;160: }


                     115 

                     116 ;161: 


                     117 ;162: static int encodeReadEntryID(uint8_t* outBuf, int bufPos,


                     118 

                     119 ;176: }


                     120 

                     121 ;177: 


                     122 ;178: int encodeReadAttrRCB(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)


                     123 ;179: {


                     124 ;180:     CBAttrAccessInfo* pAccessInfo =  descrStruct;


                     125 ;181:     


                     126 ;182:     switch (pAccessInfo->attrCode) {


                     127 ;183:     case RptEna:


                     128 ;184:         return encodeReadRptEna(outBuf, bufPos, pAccessInfo, determineSize);


                     129 ;185:     case Resv:


                     130 ;186:         return encodeReadResv(outBuf, bufPos, pAccessInfo, determineSize);


                     131 ;187: 	case GI:


                     132 ;188: 		return encodeReadGI(outBuf, bufPos, pAccessInfo, determineSize);


                     133 ;189: 	case SqNum:


                     134 ;190: 		return encodeReadSqNum(outBuf, bufPos, pAccessInfo, determineSize);


                     135 ;191: 	case IntgPd:


                     136 ;192: 		return encodeReadIntgPd(outBuf, bufPos, pAccessInfo, determineSize);


                     137 ;193: 	case EntryID:


                     138 ;194: 		return encodeReadEntryID(outBuf, bufPos, pAccessInfo, determineSize);


                     139 ;195: 	case ConfRev:


                     140 ;196: 		return encodeReadConfRev(outBuf, bufPos, pAccessInfo, determineSize);


                     141 ;197:     default:


                     142 ;198:         ERROR_REPORT("Invalid RCB DA code");


                     143 ;199:         return 0;


                     144 ;200:     }


                     145 ;201: }


                     146 ;202: 


                     147 ;203: static void writeRptEna(IsoConnection* isoConn, int rcbIndex, uint8_t* dataToWrite)


                     148 

                     149 ;221: }


                     150 

                     151 ;222: 


                     152 ;223: static void writeResv(IsoConnection* isoConn, int rcbIndex, uint8_t* dataToWrite)


                     153 

                     154 ;241: }


                     155 

                     156 	.text

                     157 	.align	4

                     158 encodeAccessAttrRCB::

00000000 e92d4070    159 	stmfd	[sp]!,{r4-r6,lr}

00000004 e24dd004    160 	sub	sp,sp,4

00000008 e1a05001    161 	mov	r5,r1

0000000c e1a06003    162 	mov	r6,r3

00000010 e1a04000    163 	mov	r4,r0

00000014 e1a00002    164 	mov	r0,r2

00000018 eb000000*   165 	bl	getAlignedDescrStruct

0000001c e3500000    166 	cmp	r0,0

00000020 0a000021    167 	beq	.L185

00000024 e5900004    168 	ldr	r0,[r0,4]

00000028 e2500001    169 	subs	r0,r0,1

0000002c e2500002    170 	subs	r0,r0,2

00000030 3a000005    171 	blo	.L175

00000034 0a000009    172 	beq	.L177


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8sk1.s
00000038 e2500001    173 	subs	r0,r0,1

0000003c 0a00000d    174 	beq	.L181

00000040 e3500003    175 	cmp	r0,3

00000044 3a000012    176 	blo	.L183

00000048 1a000017    177 	bne	.L185

                     178 .L175:

0000004c e1a02006    179 	mov	r2,r6

00000050 e1a01005    180 	mov	r1,r5

00000054 e1a00004    181 	mov	r0,r4

00000058 eb000000*   182 	bl	encodeAccessAttrBoolean

0000005c ea000013    183 	b	.L168

                     184 .L177:

00000060 e1a03006    185 	mov	r3,r6

00000064 e1a01005    186 	mov	r1,r5

00000068 e1a00004    187 	mov	r0,r4

0000006c e3a02010    188 	mov	r2,16

00000070 eb000000*   189 	bl	encodeAccessAttrUInt

00000074 ea00000d    190 	b	.L168

                     191 .L181:

00000078 e58d6000    192 	str	r6,[sp]

0000007c e1a01005    193 	mov	r1,r5

00000080 e1a00004    194 	mov	r0,r4

00000084 e3a03008    195 	mov	r3,8

00000088 e3a02089    196 	mov	r2,137

0000008c eb000000*   197 	bl	encodeAccessAttrString

00000090 ea000006    198 	b	.L168

                     199 .L183:

00000094 e1a03006    200 	mov	r3,r6

00000098 e1a01005    201 	mov	r1,r5

0000009c e1a00004    202 	mov	r0,r4

000000a0 e3a02020    203 	mov	r2,32

000000a4 eb000000*   204 	bl	encodeAccessAttrUInt

000000a8 ea000000    205 	b	.L168

                     206 .L185:

000000ac e3a00000    207 	mov	r0,0

                     208 .L168:

000000b0 e28dd004    209 	add	sp,sp,4

000000b4 e8bd8070    210 	ldmfd	[sp]!,{r4-r6,pc}

                     211 	.endf	encodeAccessAttrRCB

                     212 	.align	4

                     213 ;pAccessInfo	r0	local

                     214 

                     215 ;outBuf	r4	param

                     216 ;bufPos	r5	param

                     217 ;accessDataPos	r2	param

                     218 ;determineSize	r6	param

                     219 

                     220 	.section ".bss","awb"

                     221 .L256:

                     222 	.data

                     223 	.text

                     224 

                     225 

                     226 	.align	4

                     227 	.align	4

                     228 encodeReadAttrRCB::

000000b8 e92d40f0    229 	stmfd	[sp]!,{r4-r7,lr}

000000bc e24dd024    230 	sub	sp,sp,36

000000c0 e1a06000    231 	mov	r6,r0

000000c4 e5920004    232 	ldr	r0,[r2,4]

000000c8 e3500007    233 	cmp	r0,7


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8sk1.s
000000cc 8a000078    234 	bhi	.L335

000000d0 eaffffff    235 	b	.L564

                     236 	.align	4

                     237 .L564:

                     238 

000000d4 e08ff100    239 	add	pc,pc,r0 lsl 2

                     240 .L503:

                     241 

000000d8 e1a00000    242 	nop	

000000dc ea000074    243 	b	.L335

000000e0 ea000005    244 	b	.L279

000000e4 ea000022    245 	b	.L295

000000e8 ea000030    246 	b	.L303

000000ec ea00004d    247 	b	.L319

000000f0 ea000060    248 	b	.L327

000000f4 ea00003c    249 	b	.L311

000000f8 ea00000e    250 	b	.L287

                     251 .L279:

000000fc e1a05003    252 	mov	r5,r3

                     253 ;67: 	CBAttrAccessInfo* descrStruct, bool determineSize)


                     254 ;68: {


                     255 

00000100 e1a04001    256 	mov	r4,r1

00000104 e5920008    257 	ldr	r0,[r2,8]

                     258 ;71: 


                     259 ;72: 	bool value = false;


                     260 

                     261 ;73: 


                     262 ;74: 	if (getRCB(rcbIndex, &pRCB))


                     263 

00000108 e1a0100d    264 	mov	r1,sp

0000010c eb000000*   265 	bl	getRCB

00000110 e3500000    266 	cmp	r0,0

                     267 ;75: 	{


                     268 

                     269 ;76: 		value = pRCB->rptEna;


                     270 

00000114 159d0000    271 	ldrne	r0,[sp]

00000118 e3a07000    272 	mov	r7,0

                     273 ;69: 	RCB* pRCB;


                     274 ;70: 	int rcbIndex = descrStruct->rcbIndex;


                     275 

0000011c 15d07001    276 	ldrneb	r7,[r0,1]

                     277 ;77: 	}


                     278 ;78: 


                     279 ;79: 	return encodeBoolValue(outBuf, bufPos, value, determineSize);    


                     280 

00000120 e1a03005    281 	mov	r3,r5

00000124 e1a02007    282 	mov	r2,r7

00000128 e1a01004    283 	mov	r1,r4

0000012c e1a00006    284 	mov	r0,r6

00000130 eb000000*   285 	bl	encodeBoolValue

00000134 ea00005f    286 	b	.L275

                     287 .L287:

00000138 e1a05003    288 	mov	r5,r3

                     289 ;83:     CBAttrAccessInfo* descrStruct, bool determineSize)


                     290 ;84: {


                     291 

0000013c e1a07001    292 	mov	r7,r1

00000140 e5920008    293 	ldr	r0,[r2,8]

                     294 ;87: 



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8sk1.s
                     295 ;88:     bool value = false;


                     296 

                     297 ;89: 


                     298 ;90:     if (getRCB(rcbIndex, &pRCB))


                     299 

00000144 e28d1004    300 	add	r1,sp,4

00000148 eb000000*   301 	bl	getRCB

0000014c e3500000    302 	cmp	r0,0

                     303 ;91:     {


                     304 

                     305 ;92:         value = pRCB->resv;


                     306 

00000150 159d0004    307 	ldrne	r0,[sp,4]

00000154 e3a04000    308 	mov	r4,0

                     309 ;85:     RCB* pRCB;


                     310 ;86:     int rcbIndex = descrStruct->rcbIndex;


                     311 

00000158 15d04002    312 	ldrneb	r4,[r0,2]

                     313 ;93:     }


                     314 ;94: 


                     315 ;95:     return encodeBoolValue(outBuf, bufPos, value, determineSize);


                     316 

0000015c e1a03005    317 	mov	r3,r5

00000160 e1a02004    318 	mov	r2,r4

00000164 e1a01007    319 	mov	r1,r7

00000168 e1a00006    320 	mov	r0,r6

0000016c eb000000*   321 	bl	encodeBoolValue

00000170 ea000050    322 	b	.L275

                     323 .L295:

00000174 e1a05003    324 	mov	r5,r3

                     325 ;99: 	CBAttrAccessInfo* descrStruct, bool determineSize)


                     326 ;100: {


                     327 

00000178 e1a07001    328 	mov	r7,r1

0000017c e5920008    329 	ldr	r0,[r2,8]

                     330 ;103: 


                     331 ;104: 	bool value = false;


                     332 

                     333 ;105: 


                     334 ;106: 	if (getRCB(rcbIndex, &pRCB))


                     335 

00000180 e28d1008    336 	add	r1,sp,8

00000184 eb000000*   337 	bl	getRCB

00000188 e3500000    338 	cmp	r0,0

                     339 ;107: 	{


                     340 

                     341 ;108: 		value = pRCB->gi;


                     342 

0000018c 159d0008    343 	ldrne	r0,[sp,8]

00000190 e3a04000    344 	mov	r4,0

                     345 ;101: 	RCB* pRCB;


                     346 ;102: 	int rcbIndex = descrStruct->rcbIndex;


                     347 

00000194 15d04022    348 	ldrneb	r4,[r0,34]

                     349 ;109: 	}


                     350 ;110: 


                     351 ;111: 	return encodeBoolValue(outBuf, bufPos, value, determineSize);


                     352 

00000198 e1a03005    353 	mov	r3,r5

0000019c e1a02004    354 	mov	r2,r4

000001a0 e1a01007    355 	mov	r1,r7


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8sk1.s
000001a4 e1a00006    356 	mov	r0,r6

000001a8 eb000000*   357 	bl	encodeBoolValue

000001ac ea000041    358 	b	.L275

                     359 .L303:

000001b0 e1a05003    360 	mov	r5,r3

                     361 ;115: 	CBAttrAccessInfo* descrStruct, bool determineSize)


                     362 ;116: {


                     363 

000001b4 e1a07001    364 	mov	r7,r1

000001b8 e5920008    365 	ldr	r0,[r2,8]

                     366 ;119: 


                     367 ;120: 	uint16_t value = 0;


                     368 

                     369 ;121: 


                     370 ;122: 	if (getRCB(rcbIndex, &pRCB))


                     371 

000001bc e28d100c    372 	add	r1,sp,12

000001c0 eb000000*   373 	bl	getRCB

000001c4 e3500000    374 	cmp	r0,0

                     375 ;123: 	{


                     376 

                     377 ;124: 		value = pRCB->sqNum;


                     378 

000001c8 159d000c    379 	ldrne	r0,[sp,12]

000001cc e3a04000    380 	mov	r4,0

                     381 ;117: 	RCB* pRCB;


                     382 ;118: 	int rcbIndex = descrStruct->rcbIndex;


                     383 

000001d0 11d041b8    384 	ldrneh	r4,[r0,24]

                     385 ;125: 	}


                     386 ;126: 


                     387 ;127: 	return encodeUInt32Value(outBuf, bufPos, value, determineSize);


                     388 

000001d4 e1a03005    389 	mov	r3,r5

000001d8 e1a02004    390 	mov	r2,r4

000001dc e1a01007    391 	mov	r1,r7

000001e0 e1a00006    392 	mov	r0,r6

000001e4 eb000000*   393 	bl	encodeUInt32Value

000001e8 ea000032    394 	b	.L275

                     395 .L311:

000001ec e1a05003    396 	mov	r5,r3

                     397 ;131: 	CBAttrAccessInfo* descrStruct, bool determineSize)


                     398 ;132: {


                     399 

000001f0 e1a07001    400 	mov	r7,r1

000001f4 e5920008    401 	ldr	r0,[r2,8]

                     402 ;135: 


                     403 ;136: 	uint32_t value = 0;


                     404 

                     405 ;137: 


                     406 ;138: 	if (getRCB(rcbIndex, &pRCB))


                     407 

000001f8 e28d1010    408 	add	r1,sp,16

000001fc eb000000*   409 	bl	getRCB

00000200 e3500000    410 	cmp	r0,0

                     411 ;139: 	{


                     412 

                     413 ;140: 		value = pRCB->intgPd;


                     414 

00000204 159d0010    415 	ldrne	r0,[sp,16]

00000208 e3a04000    416 	mov	r4,0


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8sk1.s
                     417 ;133: 	RCB* pRCB;


                     418 ;134: 	int rcbIndex = descrStruct->rcbIndex;


                     419 

0000020c 1590401c    420 	ldrne	r4,[r0,28]

                     421 ;141: 	}


                     422 ;142: 


                     423 ;143: 	return encodeUInt32Value(outBuf, bufPos, value, determineSize);


                     424 

00000210 e1a03005    425 	mov	r3,r5

00000214 e1a02004    426 	mov	r2,r4

00000218 e1a01007    427 	mov	r1,r7

0000021c e1a00006    428 	mov	r0,r6

00000220 eb000000*   429 	bl	encodeUInt32Value

00000224 ea000023    430 	b	.L275

                     431 .L319:

00000228 e1a05003    432 	mov	r5,r3

                     433 ;163: 	CBAttrAccessInfo* descrStruct, bool determineSize)


                     434 ;164: {


                     435 

0000022c e1a07001    436 	mov	r7,r1

00000230 e3a00000    437 	mov	r0,0

00000234 e58d001c    438 	str	r0,[sp,28]

00000238 e58d0020    439 	str	r0,[sp,32]

                     440 ;165: 	RCB* pRCB;


                     441 ;166: 	int rcbIndex = descrStruct->rcbIndex;


                     442 

0000023c e5920008    443 	ldr	r0,[r2,8]

                     444 ;167: 


                     445 ;168: 	uint64_t value = 0;


                     446 

                     447 ;169: 


                     448 ;170: 	if (getRCB(rcbIndex, &pRCB))


                     449 

00000240 e28d1014    450 	add	r1,sp,20

00000244 eb000000*   451 	bl	getRCB

00000248 e1a03005    452 	mov	r3,r5

0000024c e3500000    453 	cmp	r0,0

                     454 ;171: 	{


                     455 

                     456 ;172: 		value = pRCB->entryID;


                     457 

00000250 159d0014    458 	ldrne	r0,[sp,20]

00000254 e28d201c    459 	add	r2,sp,28

00000258 15901024    460 	ldrne	r1,[r0,36]

0000025c 15900028    461 	ldrne	r0,[r0,40]

00000260 158d101c    462 	strne	r1,[sp,28]

00000264 e1a01007    463 	mov	r1,r7

00000268 158d0020    464 	strne	r0,[sp,32]

                     465 ;173: 	}


                     466 ;174: 	


                     467 ;175: 	return encodeOctetString8Value(outBuf, bufPos, &value, determineSize);


                     468 

0000026c e1a00006    469 	mov	r0,r6

00000270 eb000000*   470 	bl	encodeOctetString8Value

00000274 ea00000f    471 	b	.L275

                     472 .L327:

00000278 e1a05003    473 	mov	r5,r3

                     474 ;147: 	CBAttrAccessInfo* descrStruct, bool determineSize)


                     475 ;148: {


                     476 

0000027c e1a07001    477 	mov	r7,r1


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8sk1.s
00000280 e5920008    478 	ldr	r0,[r2,8]

                     479 ;151: 


                     480 ;152: 	uint32_t value = 0;


                     481 

                     482 ;153: 


                     483 ;154: 	if (getRCB(rcbIndex, &pRCB))


                     484 

00000284 e28d1018    485 	add	r1,sp,24

00000288 eb000000*   486 	bl	getRCB

0000028c e3500000    487 	cmp	r0,0

                     488 ;155: 	{


                     489 

                     490 ;156: 		value = pRCB->confRev;


                     491 

00000290 159d0018    492 	ldrne	r0,[sp,24]

00000294 e3a04000    493 	mov	r4,0

                     494 ;149: 	RCB* pRCB;


                     495 ;150: 	int rcbIndex = descrStruct->rcbIndex;


                     496 

00000298 15904014    497 	ldrne	r4,[r0,20]

                     498 ;157: 	}


                     499 ;158: 


                     500 ;159: 	return encodeUInt32Value(outBuf, bufPos, value, determineSize);


                     501 

0000029c e1a03005    502 	mov	r3,r5

000002a0 e1a02004    503 	mov	r2,r4

000002a4 e1a01007    504 	mov	r1,r7

000002a8 e1a00006    505 	mov	r0,r6

000002ac eb000000*   506 	bl	encodeUInt32Value

000002b0 ea000000    507 	b	.L275

                     508 .L335:

000002b4 e3a00000    509 	mov	r0,0

                     510 .L275:

000002b8 e28dd024    511 	add	sp,sp,36

000002bc e8bd80f0    512 	ldmfd	[sp]!,{r4-r7,pc}

                     513 	.endf	encodeReadAttrRCB

                     514 	.align	4

                     515 ;pAccessInfo	r2	local

                     516 ;bufPos	r4	local

                     517 ;determineSize	r5	local

                     518 ;pRCB	[sp]	local

                     519 ;value	r7	local

                     520 ;bufPos	r7	local

                     521 ;determineSize	r5	local

                     522 ;pRCB	[sp,4]	local

                     523 ;value	r4	local

                     524 ;bufPos	r7	local

                     525 ;determineSize	r5	local

                     526 ;pRCB	[sp,8]	local

                     527 ;value	r4	local

                     528 ;bufPos	r7	local

                     529 ;determineSize	r5	local

                     530 ;pRCB	[sp,12]	local

                     531 ;value	r4	local

                     532 ;bufPos	r7	local

                     533 ;determineSize	r5	local

                     534 ;pRCB	[sp,16]	local

                     535 ;value	r4	local

                     536 ;bufPos	r7	local

                     537 ;determineSize	r5	local

                     538 ;pRCB	[sp,20]	local


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8sk1.s
                     539 ;value	[sp,28]	local

                     540 ;bufPos	r7	local

                     541 ;determineSize	r5	local

                     542 ;pRCB	[sp,24]	local

                     543 ;value	r4	local

                     544 

                     545 ;outBuf	r6	param

                     546 ;bufPos	r1	param

                     547 ;descrStruct	r2	param

                     548 ;determineSize	r3	param

                     549 

                     550 	.section ".bss","awb"

                     551 .L502:

                     552 	.data

                     553 	.ghsnote jtable,5,.L503,.L503,.L503,9

                     554 	.text

                     555 

                     556 

                     557 ;242: 


                     558 ;243: void writeGI(IsoConnection* isoConn, int rcbIndex, uint8_t* dataToWrite)


                     559 	.align	4

                     560 	.align	4

                     561 writeGI::

000002c0 e92d4070    562 	stmfd	[sp]!,{r4-r6,lr}

000002c4 e1a05000    563 	mov	r5,r0

000002c8 e1a04002    564 	mov	r4,r2

                     565 ;244: {


                     566 

                     567 ;245:     PReporter pReporter;


                     568 ;246: 


                     569 ;247:     //Сюда надо ещё проверку включен ли GI в TrgOp


                     570 ;248: 


                     571 ;249: 	if (dataToWrite[0] != IEC61850_BER_BOOLEAN || dataToWrite[1] != 1)


                     572 

000002cc e5d40000    573 	ldrb	r0,[r4]

000002d0 e3500083    574 	cmp	r0,131

000002d4 05d40001    575 	ldreqb	r0,[r4,1]

000002d8 03500001    576 	cmpeq	r0,1

000002dc 1a00000f    577 	bne	.L565

                     578 ;250: 	{


                     579 

                     580 ;251: 		return;


                     581 

                     582 ;252: 	}	


                     583 ;253: 


                     584 ;254:     pReporter = getReporterByIndex(rcbIndex);


                     585 

000002e0 e1a00001    586 	mov	r0,r1

000002e4 eb000000*   587 	bl	getReporterByIndex

000002e8 e1b06000    588 	movs	r6,r0

                     589 ;255:     if(pReporter == NULL)


                     590 

000002ec 0a00000b    591 	beq	.L565

                     592 ;256:     {


                     593 

                     594 ;257:         ERROR_REPORT("Writing to unregistered report");


                     595 ;258:         return;


                     596 

                     597 ;259:     }


                     598 ;260:     


                     599 ;261:     if(!isRCBConnected(pReporter)



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8sk1.s
                     600 

000002f0 eb000000*   601 	bl	isRCBConnected

000002f4 e3500000    602 	cmp	r0,0

000002f8 0a000004    603 	beq	.L575

000002fc e1a01005    604 	mov	r1,r5

00000300 e1a00006    605 	mov	r0,r6

00000304 eb000000*   606 	bl	Reporter_isOwnerConnection

00000308 e3500000    607 	cmp	r0,0

0000030c 0a000003    608 	beq	.L565

                     609 .L575:

                     610 ;262:             || Reporter_isOwnerConnection(pReporter, isoConn))


                     611 ;263:     {


                     612 

                     613 ;264:         Reporter_setGI(pReporter, dataToWrite[2]);


                     614 

00000310 e5d41002    615 	ldrb	r1,[r4,2]

00000314 e1a00006    616 	mov	r0,r6

00000318 e8bd4070    617 	ldmfd	[sp]!,{r4-r6,lr}

0000031c ea000000*   618 	b	Reporter_setGI

                     619 .L565:

00000320 e8bd8070    620 	ldmfd	[sp]!,{r4-r6,pc}

                     621 	.endf	writeGI

                     622 	.align	4

                     623 ;pReporter	r6	local

                     624 

                     625 ;isoConn	r5	param

                     626 ;rcbIndex	r1	param

                     627 ;dataToWrite	r4	param

                     628 

                     629 	.section ".bss","awb"

                     630 .L646:

                     631 	.data

                     632 	.text

                     633 

                     634 ;265:     }


                     635 ;266: 


                     636 ;267: }


                     637 

                     638 ;268: 


                     639 ;269: void writeIntgPd(IsoConnection* isoConn, int rcbIndex, uint8_t* dataToWrite)


                     640 	.align	4

                     641 	.align	4

                     642 writeIntgPd::

00000324 e92d4070    643 	stmfd	[sp]!,{r4-r6,lr}

00000328 e1a04000    644 	mov	r4,r0

                     645 ;270: {


                     646 

                     647 ;271: 	PReporter pReporter;


                     648 ;272: 	size_t len;


                     649 ;273: 	uint32_t intgPd;


                     650 ;274: 


                     651 ;275: 	if (dataToWrite[0] != IEC61850_BER_UNSIGNED_INTEGER)


                     652 

0000032c e5d20000    653 	ldrb	r0,[r2]

00000330 e1a05001    654 	mov	r5,r1

00000334 e3500086    655 	cmp	r0,134

00000338 1a000014    656 	bne	.L662

                     657 ;276: 	{


                     658 

                     659 ;277: 		return;


                     660 


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8sk1.s
                     661 ;278: 	}


                     662 ;279: 


                     663 ;280: 	len = dataToWrite[1];	


                     664 

0000033c e5d21001    665 	ldrb	r1,[r2,1]

                     666 ;281: 	intgPd = BerDecoder_decodeUint32(dataToWrite, len, 2);


                     667 

00000340 e1a00002    668 	mov	r0,r2

00000344 e3a02002    669 	mov	r2,2

00000348 eb000000*   670 	bl	BerDecoder_decodeUint32

0000034c e1a06000    671 	mov	r6,r0

                     672 ;282: 


                     673 ;283: 	pReporter = getReporterByIndex(rcbIndex);


                     674 

00000350 e1a00005    675 	mov	r0,r5

00000354 eb000000*   676 	bl	getReporterByIndex

00000358 e1b05000    677 	movs	r5,r0

                     678 ;284: 	if (pReporter == NULL)


                     679 

0000035c 0a00000b    680 	beq	.L662

                     681 ;285: 	{


                     682 

                     683 ;286: 		ERROR_REPORT("Writing to unregistered report");


                     684 ;287: 		return;


                     685 

                     686 ;288: 	}


                     687 ;289:     


                     688 ;290:     if(!isRCBConnected(pReporter)


                     689 

00000360 eb000000*   690 	bl	isRCBConnected

00000364 e3500000    691 	cmp	r0,0

00000368 0a000004    692 	beq	.L671

0000036c e1a01004    693 	mov	r1,r4

00000370 e1a00005    694 	mov	r0,r5

00000374 eb000000*   695 	bl	Reporter_isOwnerConnection

00000378 e3500000    696 	cmp	r0,0

0000037c 0a000003    697 	beq	.L662

                     698 .L671:

                     699 ;291:             || Reporter_isOwnerConnection(pReporter, isoConn))


                     700 ;292:     {


                     701 

                     702 ;293:         Reporter_setIntgPd(pReporter, intgPd);


                     703 

00000380 e1a01006    704 	mov	r1,r6

00000384 e1a00005    705 	mov	r0,r5

00000388 e8bd4070    706 	ldmfd	[sp]!,{r4-r6,lr}

0000038c ea000000*   707 	b	Reporter_setIntgPd

                     708 .L662:

00000390 e8bd8070    709 	ldmfd	[sp]!,{r4-r6,pc}

                     710 	.endf	writeIntgPd

                     711 	.align	4

                     712 ;pReporter	r5	local

                     713 ;intgPd	r6	local

                     714 

                     715 ;isoConn	r4	param

                     716 ;rcbIndex	r5	param

                     717 ;dataToWrite	r2	param

                     718 

                     719 	.section ".bss","awb"

                     720 .L735:

                     721 	.data


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8sk1.s
                     722 	.text

                     723 

                     724 ;294:     }


                     725 ;295: }


                     726 

                     727 ;296: 


                     728 ;297: void writeAttrRCB(struct IsoConnection* isoConn, void* descrStruct, uint8_t* dataToWrite)


                     729 	.align	4

                     730 	.align	4

                     731 writeAttrRCB::

00000394 e92d4030    732 	stmfd	[sp]!,{r4-r5,lr}

                     733 ;298: {


                     734 

                     735 ;299: 	CBAttrAccessInfo* pAccessInfo = descrStruct;


                     736 

                     737 ;300: 


                     738 ;301: 	switch (pAccessInfo->attrCode)


                     739 

00000398 e5913004    740 	ldr	r3,[r1,4]

0000039c e2533001    741 	subs	r3,r3,1

000003a0 0a000006    742 	beq	.L754

000003a4 e2533001    743 	subs	r3,r3,1

000003a8 0a000026    744 	beq	.L770

000003ac e2533004    745 	subs	r3,r3,4

000003b0 0a000027    746 	beq	.L771

000003b4 e3530001    747 	cmp	r3,1

000003b8 0a000011    748 	beq	.L762

000003bc ea000027    749 	b	.L750

                     750 .L754:

                     751 ;302: 	{


                     752 ;303: 	case RptEna:


                     753 ;304: 		writeRptEna(isoConn, pAccessInfo->rcbIndex, dataToWrite);


                     754 

000003c0 e1a04000    755 	mov	r4,r0

000003c4 e5910008    756 	ldr	r0,[r1,8]

                     757 ;204: {	


                     758 

                     759 ;205: 	bool value;


                     760 ;206: 	PReporter pReporter;


                     761 ;207: 


                     762 ;208: 	if (dataToWrite[0] != IEC61850_BER_BOOLEAN || dataToWrite[1] != 1)


                     763 

000003c8 e5d21000    764 	ldrb	r1,[r2]

000003cc e3510083    765 	cmp	r1,131

000003d0 05d21001    766 	ldreqb	r1,[r2,1]

000003d4 03510001    767 	cmpeq	r1,1

000003d8 1a000020    768 	bne	.L750

                     769 ;209: 	{


                     770 

                     771 ;210: 		return;


                     772 

                     773 ;211: 	}


                     774 ;212: 	value = dataToWrite[2] != 0;


                     775 

000003dc e5d21002    776 	ldrb	r1,[r2,2]

000003e0 e1b05001    777 	movs	r5,r1

000003e4 13a05001    778 	movne	r5,1

                     779 ;213: 	


                     780 ;214: 	pReporter = getReporterByIndex(rcbIndex);


                     781 

000003e8 eb000000*   782 	bl	getReporterByIndex


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8sk1.s
                     783 ;215: 	if (pReporter == NULL)


                     784 

000003ec e3500000    785 	cmp	r0,0

                     786 ;216: 	{


                     787 

                     788 ;217: 		ERROR_REPORT("Writing to unregistered report");


                     789 ;218: 		return;


                     790 

                     791 ;219: 	}


                     792 ;220: 	Reporter_setEnable(pReporter, isoConn, value);	


                     793 

000003f0 11a02005    794 	movne	r2,r5

000003f4 11a01004    795 	movne	r1,r4

000003f8 18bd4030    796 	ldmnefd	[sp]!,{r4-r5,lr}

000003fc 1a000000*   797 	bne	Reporter_setEnable

00000400 ea000016    798 	b	.L750

                     799 .L762:

                     800 ;305: 		break;


                     801 ;306:     case Resv:


                     802 ;307:         writeResv(isoConn, pAccessInfo->rcbIndex, dataToWrite);


                     803 

00000404 e1a04000    804 	mov	r4,r0

00000408 e5910008    805 	ldr	r0,[r1,8]

                     806 ;224: {


                     807 

                     808 ;225:     bool value;


                     809 ;226:     PReporter pReporter;


                     810 ;227: 


                     811 ;228:     if (dataToWrite[0] != IEC61850_BER_BOOLEAN || dataToWrite[1] != 1)


                     812 

0000040c e5d21000    813 	ldrb	r1,[r2]

00000410 e3510083    814 	cmp	r1,131

00000414 05d21001    815 	ldreqb	r1,[r2,1]

00000418 03510001    816 	cmpeq	r1,1

0000041c 1a00000f    817 	bne	.L750

                     818 ;229:     {


                     819 

                     820 ;230:         return;


                     821 

                     822 ;231:     }


                     823 ;232:     value = dataToWrite[2] != 0;


                     824 

00000420 e5d21002    825 	ldrb	r1,[r2,2]

00000424 e1b05001    826 	movs	r5,r1

00000428 13a05001    827 	movne	r5,1

                     828 ;233: 


                     829 ;234:     pReporter = getReporterByIndex(rcbIndex);


                     830 

0000042c eb000000*   831 	bl	getReporterByIndex

                     832 ;235:     if (pReporter == NULL)


                     833 

00000430 e3500000    834 	cmp	r0,0

                     835 ;236:     {


                     836 

                     837 ;237:         ERROR_REPORT("Writing to unregistered report");


                     838 ;238:         return;


                     839 

                     840 ;239:     }


                     841 ;240:     Reporter_setResv(pReporter, isoConn, value);


                     842 

00000434 11a02005    843 	movne	r2,r5


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8sk1.s
00000438 11a01004    844 	movne	r1,r4

0000043c 18bd4030    845 	ldmnefd	[sp]!,{r4-r5,lr}

00000440 1a000000*   846 	bne	Reporter_setResv

00000444 ea000005    847 	b	.L750

                     848 .L770:

                     849 ;308:         break;


                     850 ;309: 	case GI:


                     851 ;310: 		writeGI(isoConn, pAccessInfo->rcbIndex, dataToWrite);


                     852 

00000448 e5911008    853 	ldr	r1,[r1,8]

0000044c e8bd4030    854 	ldmfd	[sp]!,{r4-r5,lr}

00000450 eaffff9a*   855 	b	writeGI

                     856 .L771:

                     857 ;311: 		break;


                     858 ;312: 	case IntgPd:


                     859 ;313:         writeIntgPd(isoConn, pAccessInfo->rcbIndex, dataToWrite);


                     860 

00000454 e5911008    861 	ldr	r1,[r1,8]

00000458 e8bd4030    862 	ldmfd	[sp]!,{r4-r5,lr}

0000045c eaffffb0*   863 	b	writeIntgPd

                     864 .L750:

00000460 e8bd8030    865 	ldmfd	[sp]!,{r4-r5,pc}

                     866 	.endf	writeAttrRCB

                     867 	.align	4

                     868 ;pAccessInfo	r1	local

                     869 ;isoConn	r4	local

                     870 ;rcbIndex	r0	local

                     871 ;value	r5	local

                     872 ;pReporter	r1	local

                     873 ;isoConn	r4	local

                     874 ;rcbIndex	r0	local

                     875 ;value	r5	local

                     876 ;pReporter	r1	local

                     877 

                     878 ;isoConn	r0	param

                     879 ;descrStruct	r1	param

                     880 ;dataToWrite	r2	param

                     881 

                     882 	.section ".bss","awb"

                     883 .L878:

                     884 	.data

                     885 	.text

                     886 

                     887 ;314: 		break;


                     888 ;315: 	case EntryID:


                     889 ;316: 	case SqNum:


                     890 ;317: 		break;


                     891 ;318: 	default:


                     892 ;319: 		ERROR_REPORT("Unsupported RCB attribute");


                     893 ;320: 	}


                     894 ;321: 	


                     895 ;322: }


                     896 	.align	4

                     897 

                     898 	.data

                     899 	.ghsnote version,6

                     900 	.ghsnote tools,3

                     901 	.ghsnote options,0

                     902 	.text

                     903 	.align	4

