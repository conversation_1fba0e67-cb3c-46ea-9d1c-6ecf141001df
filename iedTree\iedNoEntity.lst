                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c0c1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedNoEntity.c -o iedTree\gh_c0c1.o -list=iedTree/iedNoEntity.lst C:\Users\<USER>\AppData\Local\Temp\gh_c0c1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_c0c1.s
Source File: iedNoEntity.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		iedTree/iedNoEntity.c -o iedTree/iedNoEntity.o

                      12 ;Source File:   iedTree/iedNoEntity.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:52 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "iedNoEntity.h"


                      23 ;2: 


                      24 ;3: #include "../bufView.h"


                      25 ;4: 


                      26 ;5: #include <string.h>


                      27 ;6: 


                      28 ;7: 


                      29 ;8: static struct IEDEntityStruct noEntity;


                      30 ;9: 


                      31 ;10: static bool calcReadLen(IEDEntity entity, size_t* pLen )


                      32 ;11: {


                      33 ;12:     *pLen = 3;


                      34 ;13:     return true;


                      35 ;14: }


                      36 ;15: 


                      37 ;16: static bool encodeRead(IEDEntity entity, BufferView* outBuf)


                      38 ;17: {


                      39 ;18:     // При чтении создаёт код object-non-existent


                      40 ;19:     return BufferView_writeData(outBuf, "\x80\x01\x0A", 3);


                      41 ;20: }


                      42 ;21: 


                      43 ;22: static MmsDataAccessError write(IEDEntity entity, IsoConnection* isoConn,


                      44 

                      45 ;26: }


                      46 

                      47 ;27: 


                      48 ;28: 


                      49 ;29: void IEDNoEntity_init(void)


                      50 ;30: {



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c0c1.s
                      51 ;31:     memset(&noEntity, 0, sizeof(noEntity));


                      52 ;32:     noEntity.type = IED_ENTITY_NONE;


                      53 ;33:     noEntity.encodeRead = encodeRead;


                      54 ;34:     noEntity.calcReadLen = calcReadLen;


                      55 ;35:     noEntity.write = write;


                      56 ;36: }


                      57 ;37: 


                      58 ;38: IEDEntity IEDNoEntity_get(void)


                      59 

                      60 ;41: }


                      61 

                      62 	.text

                      63 	.align	4

                      64 calcReadLen:

00000000 e3a00003     65 	mov	r0,3

00000004 e5810000     66 	str	r0,[r1]

00000008 e3a00001     67 	mov	r0,1

0000000c e12fff1e*    68 	ret	

                      69 	.endf	calcReadLen

                      70 	.align	4

                      71 

                      72 ;entity	none	param

                      73 ;pLen	r1	param

                      74 

                      75 	.section ".bss","awb"

                      76 .L62:

                      77 	.data

                      78 	.text

                      79 

                      80 

                      81 	.align	4

                      82 	.align	4

                      83 encodeRead:

00000010 e92d4000     84 	stmfd	[sp]!,{lr}

00000014 e1a00001     85 	mov	r0,r1

00000018 e59f1058*    86 	ldr	r1,.L106

0000001c e3a02003     87 	mov	r2,3

00000020 eb000000*    88 	bl	BufferView_writeData

00000024 e20000ff     89 	and	r0,r0,255

00000028 e8bd4000     90 	ldmfd	[sp]!,{lr}

0000002c e12fff1e*    91 	ret	

                      92 	.endf	encodeRead

                      93 	.align	4

                      94 ;.L98	.L101	static

                      95 

                      96 ;entity	none	param

                      97 ;outBuf	r2	param

                      98 

                      99 	.section ".bss","awb"

                     100 .L97:

                     101 	.section ".rodata","a"

                     102 .L101:;	"\200\001\n\000"

00000000 000a0180    103 	.data.b	128,1,10,0

                     104 	.type	.L101,$object

                     105 	.size	.L101,4

                     106 	.data

                     107 	.text

                     108 

                     109 

                     110 	.align	4

                     111 	.align	4


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c0c1.s
                     112 	.align	4

                     113 IEDNoEntity_init::

00000030 e92d4070    114 	stmfd	[sp]!,{r4-r6,lr}

00000034 e59f5040*   115 	ldr	r5,.L133

00000038 e59f4040*   116 	ldr	r4,.L134

0000003c e59f6040*   117 	ldr	r6,.L135

00000040 e1a00004    118 	mov	r0,r4

00000044 e3a0206c    119 	mov	r2,108

00000048 e3a01000    120 	mov	r1,0

0000004c eb000000*   121 	bl	memset

00000050 e3a00000    122 	mov	r0,0

00000054 e5840050    123 	str	r0,[r4,80]

00000058 e59fc028*   124 	ldr	r12,.L136

0000005c e284405c    125 	add	r4,r4,92

00000060 e8841060    126 	stmea	[r4],{r5-r6,r12}

00000064 e8bd8070    127 	ldmfd	[sp]!,{r4-r6,pc}

                     128 	.endf	IEDNoEntity_init

                     129 	.align	4

                     130 

                     131 	.section ".bss","awb"

                     132 .L126:

00000000 00000000    133 noEntity:	.space	108

00000004 00000000 
00000008 00000000 
0000000c 00000000 
00000010 00000000 
00000014 00000000 
00000018 00000000 
0000001c 00000000 
00000020 00000000 
00000024 00000000 
00000028 00000000 
0000002c 00000000 
00000030 00000000 
00000034 00000000 
00000038 00000000 
0000003c 00000000 
00000040 00000000 
00000044 00000000 
00000048 00000000 
0000004c 00000000 
00000050 00000000 
00000054 00000000 
00000058 00000000 
0000005c 00000000 
00000060 00000000 
00000064 00000000 
00000068 00000000 
                     134 	.data

                     135 	.text

                     136 

                     137 	.align	4

                     138 	.align	4

                     139 write:

                     140 ;23:                                BufferView* value)


                     141 ;24: {


                     142 

                     143 ;25:     return DATA_ACCESS_ERROR_OBJECT_NONE_EXISTENT;


                     144 

00000068 e3a0000a    145 	mov	r0,10

0000006c e12fff1e*   146 	ret	


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c0c1.s
                     147 	.endf	write

                     148 	.align	4

                     149 

                     150 ;entity	none	param

                     151 ;isoConn	none	param

                     152 ;value	none	param

                     153 

                     154 	.section ".bss","awb"

                     155 .L158:

                     156 	.data

                     157 	.text

                     158 	.align	4

                     159 	.align	4

                     160 IEDNoEntity_get::

                     161 ;39: {


                     162 

                     163 ;40:     return &noEntity;


                     164 

00000070 e59f0008*   165 	ldr	r0,.L134

00000074 e12fff1e*   166 	ret	

                     167 	.endf	IEDNoEntity_get

                     168 	.align	4

                     169 

                     170 	.data

                     171 	.text

                     172 	.align	4

                     173 .L106:

00000078 00000000*   174 	.data.w	.L101

                     175 	.type	.L106,$object

                     176 	.size	.L106,4

                     177 

                     178 .L133:

0000007c 00000000*   179 	.data.w	encodeRead

                     180 	.type	.L133,$object

                     181 	.size	.L133,4

                     182 

                     183 .L134:

00000080 00000000*   184 	.data.w	noEntity

                     185 	.type	.L134,$object

                     186 	.size	.L134,4

                     187 

                     188 .L135:

00000084 00000000*   189 	.data.w	calcReadLen

                     190 	.type	.L135,$object

                     191 	.size	.L135,4

                     192 

                     193 .L136:

00000088 00000000*   194 	.data.w	write

                     195 	.type	.L136,$object

                     196 	.size	.L136,4

                     197 

                     198 	.align	4

                     199 ;noEntity	noEntity	static

                     200 

                     201 	.data

                     202 	.ghsnote version,6

                     203 	.ghsnote tools,3

                     204 	.ghsnote options,0

                     205 	.text

                     206 	.align	4

                     207 	.section ".bss","awb"


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c0c1.s
                     208 	.align	4

                     209 	.section ".rodata","a"

                     210 	.align	4

                     211 	.text

