                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=OscConverter.c -o fs\gh_8l01.o -list=fs/OscConverter.lst C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
Source File: OscConverter.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		fs/OscConverter.c -o fs/OscConverter.o

                      12 ;Source File:   fs/OscConverter.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:58 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "OscConverter.h"


                      23 ;2: #include "platform_critical_section.h"


                      24 ;3: #include <time.h>


                      25 ;4: #include <string.h>


                      26 ;5: #include <stdlib.h>


                      27 ;6: #include <stdio.h>


                      28 ;7: #include "../timetools.h"


                      29 ;8: 


                      30 ;9: bool  OscConverter_init(void)


                      31 

                      32 ;12: }


                      33 

                      34 ;13: 


                      35 ;14: 


                      36 ;15: bool  OscConverter_processPeriod(OscReadFileContext * readFileContext)


                      37 ;16: {


                      38 ;17: 	char *formatBuffer = readFileContext->formatBuffer;


                      39 ;18: 	OscWriteBuffer *datBuffer = &readFileContext->datBuffer;


                      40 ;19: 	OSCInfoStruct *oscInfo;


                      41 ;20: 	int pointPerFrame;


                      42 ;21: 	int analogCount;


                      43 ;22: 	int boolCount;


                      44 ;23: 	int pointNum;


                      45 ;24: 	int sampleNum;


                      46 ;25: 	int size;


                      47 ;26: 	double tick;


                      48 ;27: 	int analogNum;


                      49 ;28: 	int boolNum;


                      50 ;29: 	float freq;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
                      51 ;30: 


                      52 ;31: 	if (!readFileContext)


                      53 ;32: 	{


                      54 ;33: 		return false;


                      55 ;34: 	}


                      56 ;35: 	oscInfo = readFileContext->oscInfo;


                      57 ;36: 	if (!oscInfo)


                      58 ;37: 	{


                      59 ;38: 		return false;


                      60 ;39: 	}


                      61 ;40: 


                      62 ;41: 	pointPerFrame = OSCInfo_getPointPerFrameCount(oscInfo);


                      63 ;42: 	analogCount = OSCInfo_getAnalogCount(oscInfo);


                      64 ;43: 	boolCount = OSCInfo_getBoolCount(oscInfo);


                      65 ;44: 	sampleNum = (readFileContext->curFrame) * pointPerFrame + 1;


                      66 ;45: 


                      67 ;46: 


                      68 ;47: 


                      69 ;48: 	OscWriteBuffer_reset(datBuffer);


                      70 ;49: 


                      71 ;50: 	for (pointNum = 0; pointNum < pointPerFrame; ++pointNum)


                      72 ;51: 	{


                      73 ;52: 		size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "%d", sampleNum);


                      74 ;53: 		if (!OscWriteBuffer_write(datBuffer, formatBuffer, size)) return false;


                      75 ;54: 


                      76 ;55: 		size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, ",%lld", 


                      77 ;56: 			(uint64_t)( readFileContext->tick + 0.5));


                      78 ;57: 		if (!OscWriteBuffer_write(datBuffer, formatBuffer, size)) return false;


                      79 ;58: 		


                      80 ;59: 		if (!OSCFrame_getTick(oscInfo, &tick))


                      81 ;60: 		{


                      82 ;61: 			return false;


                      83 ;62: 		}


                      84 ;63: 		readFileContext->tick += tick;


                      85 ;64: 


                      86 ;65: 		// если еще не вышли за предысторию, фиксируем тики


                      87 ;66: 		if (readFileContext->curFrame < OSCInfo_getPrehistFrameCount(oscInfo))


                      88 ;67: 		{


                      89 ;68: 			readFileContext->phistoryTick = readFileContext->tick;


                      90 ;69: 		}


                      91 ;70: 


                      92 ;71: 		


                      93 ;72: 


                      94 ;73: 		for (analogNum = 0; analogNum < analogCount; ++analogNum)


                      95 ;74: 		{


                      96 ;75: 			int analogValue = OSCFrame_getAnalogValue(oscInfo, pointNum, analogNum);


                      97 ;76: 			size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, ",%d", analogValue);


                      98 ;77: 			if (!OscWriteBuffer_write(datBuffer, formatBuffer, size)) return false;


                      99 ;78: 		}


                     100 ;79: 


                     101 ;80: 		for (boolNum = 0; boolNum < boolCount; ++boolNum)


                     102 ;81: 		{


                     103 ;82: 			int boolValue = OSCFrame_getBoolValue(oscInfo, pointNum, boolNum);


                     104 ;83: 			size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, ",%d", boolValue);


                     105 ;84: 			if (!OscWriteBuffer_write(datBuffer, formatBuffer, size)) return false;


                     106 ;85: 		}


                     107 ;86: 


                     108 ;87: 


                     109 ;88: 		size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "\r\n");


                     110 ;89: 		if (!OscWriteBuffer_write(datBuffer, formatBuffer, size)) return false;


                     111 ;90: 



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
                     112 ;91: 		// частоту для cfg файла


                     113 ;92: 		if (!OSCFrame_getFreq(oscInfo, &freq))


                     114 ;93: 		{


                     115 ;94: 			return false;


                     116 ;95: 		}


                     117 ;96: 		if (!OscReadFileContext_writeFreq(readFileContext, freq, sampleNum))


                     118 ;97: 		{


                     119 ;98: 			return false;


                     120 ;99: 		}


                     121 ;100: 


                     122 ;101: 		sampleNum++;


                     123 ;102: 	}


                     124 ;103: 


                     125 ;104: 	return true;


                     126 ;105: }


                     127 ;106: 


                     128 ;107: //! вычисление времени начала предыстории


                     129 ;108: static void getPhistTime(OscReadFileContext * readFileContext,


                     130 

                     131 ;126: 	


                     132 ;127: }


                     133 

                     134 ;128: //! время в COMTRADE 


                     135 ;129: static bool writeFormatTimeToCfgBuffer(OscReadFileContext * readFileContext,


                     136 ;130: 	__time32_t t, int ms)


                     137 ;131: {


                     138 ;132: 	char *formatBuffer = readFileContext->formatBuffer;


                     139 ;133: 	OscWriteBuffer *cfgBuffer = &readFileContext->cfgBuffer;


                     140 ;134: 	int size;


                     141 ;135: 	struct tm tmTime;


                     142 ;136: 


                     143 ;137: 	// пишется UTC, поэтому осцилограмма будет отличатся от мониторной, 


                     144 ;138: 	// чтобы было все одинаково нужно вытащить часовой пояс из ЦП


                     145 ;139: 	if (TimeTools_gmtime32(&tmTime, &t) == true)


                     146 ;140: 	{


                     147 ;141: 		// по идее тут нужно прибавить 1900, но comtrade


                     148 ;142: 		// требует год в формате %02d, поэтому вычитаем 100


                     149 ;143: 


                     150 ;144: 		// Update: для нового формата используется год в виде 4-х символов


                     151 ;145: 		// поэтому прибавляем 1900 и меняем формат на %04d


                     152 ;146: 		tmTime.tm_year += 1900;


                     153 ;147: 		tmTime.tm_mon += 1;


                     154 ;148: 


                     155 ;149: 		size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE,


                     156 ;150: 			"%02d/%02d/%04d,%02d:%02d:%02d.%03d\r\n",


                     157 ;151: 			tmTime.tm_mday,


                     158 ;152: 			tmTime.tm_mon,


                     159 ;153: 			tmTime.tm_year,


                     160 ;154: 			tmTime.tm_hour,


                     161 ;155: 			tmTime.tm_min,


                     162 ;156: 			tmTime.tm_sec,


                     163 ;157: 			ms);


                     164 ;158: 	}


                     165 ;159: 	else


                     166 ;160: 	{


                     167 ;161: 		size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE,


                     168 ;162: 			"01/01/1980 00:00:00.%03d\r\n",ms);


                     169 ;163: 	}


                     170 ;164: 


                     171 ;165: 	return OscWriteBuffer_write(cfgBuffer, formatBuffer, size);


                     172 ;166: }



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
                     173 ;167: 


                     174 ;168: //! записывает время предыстории и осцилограммы


                     175 ;169: static bool writeOscTimeToCfg(OscReadFileContext * readFileContext)


                     176 

                     177 ;190: }


                     178 

                     179 ;191: //! описание аналоговых каналов в cfg


                     180 ;192: static bool writeAnalogToCfg(OscReadFileContext * readFileContext)


                     181 

                     182 ;228: }


                     183 

                     184 ;229: 


                     185 ;230: //! описание дискретных каналов в cfg


                     186 ;231: static bool writeBoolToCfg(OscReadFileContext * readFileContext)


                     187 

                     188 ;252: }


                     189 

                     190 ;253: 


                     191 ;254: //! частота и список изменений частоты в cfg


                     192 ;255: static bool writeFrequencyToCfg(OscReadFileContext * readFileContext)


                     193 

                     194 ;286: }


                     195 

                     196 	.text

                     197 	.align	4

                     198 OscConverter_processPeriod::

00000000 e92d4ff0    199 	stmfd	[sp]!,{r4-fp,lr}

00000004 e1a0a000    200 	mov	r10,r0

00000008 e2804050    201 	add	r4,r0,80

0000000c e3a01e40    202 	mov	r1,1<<10

00000010 e2811064    203 	add	r1,r1,100

00000014 e24dd030    204 	sub	sp,sp,48

00000018 e3500000    205 	cmp	r0,0

0000001c 1590502c    206 	ldrne	r5,[r0,44]

00000020 e0807001    207 	add	r7,r0,r1

00000024 13550000    208 	cmpne	r5,0

                     209 .L182:

00000028 03a00000    210 	moveq	r0,0

0000002c 0a00019a    211 	beq	.L176

                     212 .L181:

00000030 e1a00005    213 	mov	r0,r5

00000034 eb000000*   214 	bl	OSCInfo_getPointPerFrameCount

00000038 e58d0020    215 	str	r0,[sp,32]

0000003c e1a06000    216 	mov	r6,r0

00000040 e1a0b000    217 	mov	fp,r0

00000044 e1a00005    218 	mov	r0,r5

00000048 eb000000*   219 	bl	OSCInfo_getAnalogCount

0000004c e58d001c    220 	str	r0,[sp,28]

00000050 e1a00005    221 	mov	r0,r5

00000054 eb000000*   222 	bl	OSCInfo_getBoolCount

00000058 e59a1030    223 	ldr	r1,[r10,48]

0000005c e001019b    224 	mul	r1,fp,r1

00000060 e3a0b000    225 	mov	fp,0

00000064 e58d0018    226 	str	r0,[sp,24]

00000068 e2810001    227 	add	r0,r1,1

0000006c e58d0010    228 	str	r0,[sp,16]

00000070 e1a00007    229 	mov	r0,r7

00000074 eb000000*   230 	bl	OscWriteBuffer_reset

00000078 e15b0006    231 	cmp	fp,r6

0000007c aa000185    232 	bge	.L184

                     233 .L186:


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
00000080 e59d3010    234 	ldr	r3,[sp,16]

00000084 e28f2000*   235 	adr	r2,.L1026

00000088 e1a00004    236 	mov	r0,r4

0000008c e3a01e40    237 	mov	r1,1<<10

00000090 eb000000*   238 	bl	snprintf

00000094 e1a01004    239 	mov	r1,r4

00000098 e1a02000    240 	mov	r2,r0

0000009c e1a00007    241 	mov	r0,r7

000000a0 eb000000*   242 	bl	OscWriteBuffer_write

000000a4 e3500000    243 	cmp	r0,0

000000a8 0affffde    244 	beq	.L182

000000ac e59a0034    245 	ldr	r0,[r10,52]

000000b0 e59a1038    246 	ldr	r1,[r10,56]

000000b4 e3a02000    247 	mov	r2,0

000000b8 e3a036fe    248 	mov	r3,254<<20

000000bc e28335c0    249 	add	r3,r3,3<<28

000000c0 eb000000*   250 	bl	__dadd

000000c4 eb000000*   251 	bl	__dtou64

000000c8 e52d0004    252 	str	r0,[sp,-4]!

000000cc e58d1004    253 	str	r1,[sp,4]

000000d0 e8bd0008    254 	ldmfd	[sp]!,{r3}

000000d4 e28f2000*   255 	adr	r2,.L1027

000000d8 e1a00004    256 	mov	r0,r4

000000dc e3a01e40    257 	mov	r1,1<<10

000000e0 eb000000*   258 	bl	snprintf

000000e4 e1a01004    259 	mov	r1,r4

000000e8 e1a02000    260 	mov	r2,r0

000000ec e1a00007    261 	mov	r0,r7

000000f0 eb000000*   262 	bl	OscWriteBuffer_write

000000f4 e3500000    263 	cmp	r0,0

000000f8 0affffca    264 	beq	.L182

000000fc e28d1028    265 	add	r1,sp,40

00000100 e1a00005    266 	mov	r0,r5

00000104 eb000000*   267 	bl	OSCFrame_getTick

00000108 e3500000    268 	cmp	r0,0

0000010c 0affffc5    269 	beq	.L182

00000110 e59d302c    270 	ldr	r3,[sp,44]

00000114 e59d2028    271 	ldr	r2,[sp,40]

00000118 e59a1038    272 	ldr	r1,[r10,56]

0000011c e59a0034    273 	ldr	r0,[r10,52]

00000120 eb000000*   274 	bl	__dadd

00000124 e58a1038    275 	str	r1,[r10,56]

00000128 e58a0034    276 	str	r0,[r10,52]

0000012c e1a00005    277 	mov	r0,r5

00000130 eb000000*   278 	bl	OSCInfo_getPrehistFrameCount

00000134 e1a0600a    279 	mov	r6,r10

00000138 e5961030    280 	ldr	r1,[r6,48]

0000013c e1510000    281 	cmp	r1,r0

00000140 35961034    282 	ldrlo	r1,[r6,52]

00000144 35b62038    283 	ldrlo	r2,[r6,56]!

00000148 39860006    284 	stmlofa	[r6],{r1-r2}

0000014c e59d001c    285 	ldr	r0,[sp,28]

00000150 e3a08000    286 	mov	r8,0

00000154 e3500000    287 	cmp	r0,0

00000158 b3a00000    288 	movlt	r0,0

0000015c e58d0014    289 	str	r0,[sp,20]

00000160 e1b091a0    290 	movs	r9,r0 lsr 3

00000164 0a00007b    291 	beq	.L319

00000168 e59f6304*   292 	ldr	r6,.L1028

                     293 .L320:

0000016c e1a02008    294 	mov	r2,r8


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
00000170 e1a0100b    295 	mov	r1,fp

00000174 e1a00005    296 	mov	r0,r5

00000178 eb000000*   297 	bl	OSCFrame_getAnalogValue

0000017c e1a02006    298 	mov	r2,r6

00000180 e1a03000    299 	mov	r3,r0

00000184 e1a00004    300 	mov	r0,r4

00000188 e3a01e40    301 	mov	r1,1<<10

0000018c eb000000*   302 	bl	snprintf

00000190 e1a01004    303 	mov	r1,r4

00000194 e1a02000    304 	mov	r2,r0

00000198 e1a00007    305 	mov	r0,r7

0000019c eb000000*   306 	bl	OscWriteBuffer_write

000001a0 e3500000    307 	cmp	r0,0

000001a4 0affff9f    308 	beq	.L182

000001a8 e2882001    309 	add	r2,r8,1

000001ac e1a0100b    310 	mov	r1,fp

000001b0 e1a00005    311 	mov	r0,r5

000001b4 eb000000*   312 	bl	OSCFrame_getAnalogValue

000001b8 e1a02006    313 	mov	r2,r6

000001bc e1a03000    314 	mov	r3,r0

000001c0 e1a00004    315 	mov	r0,r4

000001c4 e3a01e40    316 	mov	r1,1<<10

000001c8 eb000000*   317 	bl	snprintf

000001cc e1a01004    318 	mov	r1,r4

000001d0 e1a02000    319 	mov	r2,r0

000001d4 e1a00007    320 	mov	r0,r7

000001d8 eb000000*   321 	bl	OscWriteBuffer_write

000001dc e3500000    322 	cmp	r0,0

000001e0 0affff90    323 	beq	.L182

000001e4 e2882002    324 	add	r2,r8,2

000001e8 e1a0100b    325 	mov	r1,fp

000001ec e1a00005    326 	mov	r0,r5

000001f0 eb000000*   327 	bl	OSCFrame_getAnalogValue

000001f4 e1a02006    328 	mov	r2,r6

000001f8 e1a03000    329 	mov	r3,r0

000001fc e1a00004    330 	mov	r0,r4

00000200 e3a01e40    331 	mov	r1,1<<10

00000204 eb000000*   332 	bl	snprintf

00000208 e1a01004    333 	mov	r1,r4

0000020c e1a02000    334 	mov	r2,r0

00000210 e1a00007    335 	mov	r0,r7

00000214 eb000000*   336 	bl	OscWriteBuffer_write

00000218 e3500000    337 	cmp	r0,0

0000021c 0affff81    338 	beq	.L182

00000220 e2882003    339 	add	r2,r8,3

00000224 e1a0100b    340 	mov	r1,fp

00000228 e1a00005    341 	mov	r0,r5

0000022c eb000000*   342 	bl	OSCFrame_getAnalogValue

00000230 e1a02006    343 	mov	r2,r6

00000234 e1a03000    344 	mov	r3,r0

00000238 e1a00004    345 	mov	r0,r4

0000023c e3a01e40    346 	mov	r1,1<<10

00000240 eb000000*   347 	bl	snprintf

00000244 e1a01004    348 	mov	r1,r4

00000248 e1a02000    349 	mov	r2,r0

0000024c e1a00007    350 	mov	r0,r7

00000250 eb000000*   351 	bl	OscWriteBuffer_write

00000254 e3500000    352 	cmp	r0,0

00000258 0affff72    353 	beq	.L182

0000025c e2882004    354 	add	r2,r8,4

00000260 e1a0100b    355 	mov	r1,fp


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
00000264 e1a00005    356 	mov	r0,r5

00000268 eb000000*   357 	bl	OSCFrame_getAnalogValue

0000026c e1a02006    358 	mov	r2,r6

00000270 e1a03000    359 	mov	r3,r0

00000274 e1a00004    360 	mov	r0,r4

00000278 e3a01e40    361 	mov	r1,1<<10

0000027c eb000000*   362 	bl	snprintf

00000280 e1a01004    363 	mov	r1,r4

00000284 e1a02000    364 	mov	r2,r0

00000288 e1a00007    365 	mov	r0,r7

0000028c eb000000*   366 	bl	OscWriteBuffer_write

00000290 e3500000    367 	cmp	r0,0

00000294 0affff63    368 	beq	.L182

00000298 e2882005    369 	add	r2,r8,5

0000029c e1a0100b    370 	mov	r1,fp

000002a0 e1a00005    371 	mov	r0,r5

000002a4 eb000000*   372 	bl	OSCFrame_getAnalogValue

000002a8 e1a02006    373 	mov	r2,r6

000002ac e1a03000    374 	mov	r3,r0

000002b0 e1a00004    375 	mov	r0,r4

000002b4 e3a01e40    376 	mov	r1,1<<10

000002b8 eb000000*   377 	bl	snprintf

000002bc e1a01004    378 	mov	r1,r4

000002c0 e1a02000    379 	mov	r2,r0

000002c4 e1a00007    380 	mov	r0,r7

000002c8 eb000000*   381 	bl	OscWriteBuffer_write

000002cc e3500000    382 	cmp	r0,0

000002d0 0affff54    383 	beq	.L182

000002d4 e2882006    384 	add	r2,r8,6

000002d8 e1a0100b    385 	mov	r1,fp

000002dc e1a00005    386 	mov	r0,r5

000002e0 eb000000*   387 	bl	OSCFrame_getAnalogValue

000002e4 e1a02006    388 	mov	r2,r6

000002e8 e1a03000    389 	mov	r3,r0

000002ec e1a00004    390 	mov	r0,r4

000002f0 e3a01e40    391 	mov	r1,1<<10

000002f4 eb000000*   392 	bl	snprintf

000002f8 e1a01004    393 	mov	r1,r4

000002fc e1a02000    394 	mov	r2,r0

00000300 e1a00007    395 	mov	r0,r7

00000304 eb000000*   396 	bl	OscWriteBuffer_write

00000308 e3500000    397 	cmp	r0,0

0000030c 0affff45    398 	beq	.L182

00000310 e2882007    399 	add	r2,r8,7

00000314 e1a0100b    400 	mov	r1,fp

00000318 e1a00005    401 	mov	r0,r5

0000031c eb000000*   402 	bl	OSCFrame_getAnalogValue

00000320 e1a02006    403 	mov	r2,r6

00000324 e1a03000    404 	mov	r3,r0

00000328 e1a00004    405 	mov	r0,r4

0000032c e3a01e40    406 	mov	r1,1<<10

00000330 eb000000*   407 	bl	snprintf

00000334 e1a01004    408 	mov	r1,r4

00000338 e1a02000    409 	mov	r2,r0

0000033c e1a00007    410 	mov	r0,r7

00000340 eb000000*   411 	bl	OscWriteBuffer_write

00000344 e3500000    412 	cmp	r0,0

00000348 0affff36    413 	beq	.L182

0000034c e2888008    414 	add	r8,r8,8

00000350 e2599001    415 	subs	r9,r9,1

00000354 1affff84    416 	bne	.L320


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
                     417 .L319:

00000358 e59d0014    418 	ldr	r0,[sp,20]

0000035c e2109007    419 	ands	r9,r0,7

00000360 0a000012    420 	beq	.L199

00000364 e59f6108*   421 	ldr	r6,.L1028

                     422 .L346:

00000368 e1a02008    423 	mov	r2,r8

0000036c e1a0100b    424 	mov	r1,fp

00000370 e1a00005    425 	mov	r0,r5

00000374 eb000000*   426 	bl	OSCFrame_getAnalogValue

00000378 e1a02006    427 	mov	r2,r6

0000037c e1a03000    428 	mov	r3,r0

00000380 e1a00004    429 	mov	r0,r4

00000384 e3a01e40    430 	mov	r1,1<<10

00000388 eb000000*   431 	bl	snprintf

0000038c e1a01004    432 	mov	r1,r4

00000390 e1a02000    433 	mov	r2,r0

00000394 e1a00007    434 	mov	r0,r7

00000398 eb000000*   435 	bl	OscWriteBuffer_write

0000039c e3500000    436 	cmp	r0,0

000003a0 0affff20    437 	beq	.L182

000003a4 e2888001    438 	add	r8,r8,1

000003a8 e2599001    439 	subs	r9,r9,1

000003ac 1affffed    440 	bne	.L346

                     441 .L199:

000003b0 e59d0018    442 	ldr	r0,[sp,24]

000003b4 e3a08000    443 	mov	r8,0

000003b8 e3500000    444 	cmp	r0,0

000003bc b3a00000    445 	movlt	r0,0

000003c0 e58d0014    446 	str	r0,[sp,20]

000003c4 e1b091a0    447 	movs	r9,r0 lsr 3

000003c8 0a000080    448 	beq	.L351

000003cc e59f60a0*   449 	ldr	r6,.L1028

                     450 .L352:

000003d0 e1a02008    451 	mov	r2,r8

000003d4 e1a0100b    452 	mov	r1,fp

000003d8 e1a00005    453 	mov	r0,r5

000003dc eb000000*   454 	bl	OSCFrame_getBoolValue

000003e0 e1a02006    455 	mov	r2,r6

000003e4 e1a03000    456 	mov	r3,r0

000003e8 e1a00004    457 	mov	r0,r4

000003ec e3a01e40    458 	mov	r1,1<<10

000003f0 eb000000*   459 	bl	snprintf

000003f4 e1a01004    460 	mov	r1,r4

000003f8 e1a02000    461 	mov	r2,r0

000003fc e1a00007    462 	mov	r0,r7

00000400 eb000000*   463 	bl	OscWriteBuffer_write

00000404 e3500000    464 	cmp	r0,0

00000408 0affff06    465 	beq	.L182

0000040c e2882001    466 	add	r2,r8,1

00000410 e1a0100b    467 	mov	r1,fp

00000414 e1a00005    468 	mov	r0,r5

00000418 eb000000*   469 	bl	OSCFrame_getBoolValue

0000041c e1a02006    470 	mov	r2,r6

00000420 e1a03000    471 	mov	r3,r0

00000424 e1a00004    472 	mov	r0,r4

00000428 e3a01e40    473 	mov	r1,1<<10

0000042c eb000000*   474 	bl	snprintf

00000430 e1a01004    475 	mov	r1,r4

00000434 e1a02000    476 	mov	r2,r0

00000438 e1a00007    477 	mov	r0,r7


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
0000043c eb000000*   478 	bl	OscWriteBuffer_write

00000440 e3500000    479 	cmp	r0,0

00000444 0afffef7    480 	beq	.L182

00000448 e2882002    481 	add	r2,r8,2

0000044c e1a0100b    482 	mov	r1,fp

00000450 e1a00005    483 	mov	r0,r5

00000454 eb000000*   484 	bl	OSCFrame_getBoolValue

00000458 e1a02006    485 	mov	r2,r6

0000045c e1a03000    486 	mov	r3,r0

00000460 e1a00004    487 	mov	r0,r4

00000464 ea000003    488 	b	.L1029

                     489 	.align	4

                     490 .L1026:

                     491 ;	"%d\000"

00000468 6425       492 	.data.b	37,100

0000046a 00         493 	.data.b	0

0000046b 00         494 	.align 4

                     495 

                     496 	.type	.L1026,$object

                     497 	.size	.L1026,4

                     498 

                     499 .L1027:

                     500 ;	",%lld\000"

0000046c 6c6c252c    501 	.data.b	44,37,108,108

00000470 0064       502 	.data.b	100,0

00000472 0000       503 	.align 4

                     504 

                     505 	.type	.L1027,$object

                     506 	.size	.L1027,4

                     507 

                     508 .L1028:

00000474 00000000*   509 	.data.w	.L894

                     510 	.type	.L1028,$object

                     511 	.size	.L1028,4

                     512 

                     513 .L1029:

                     514 

00000478 e3a01e40    515 	mov	r1,1<<10

0000047c eb000000*   516 	bl	snprintf

00000480 e1a01004    517 	mov	r1,r4

00000484 e1a02000    518 	mov	r2,r0

00000488 e1a00007    519 	mov	r0,r7

0000048c eb000000*   520 	bl	OscWriteBuffer_write

00000490 e3500000    521 	cmp	r0,0

00000494 0afffee3    522 	beq	.L182

00000498 e2882003    523 	add	r2,r8,3

0000049c e1a0100b    524 	mov	r1,fp

000004a0 e1a00005    525 	mov	r0,r5

000004a4 eb000000*   526 	bl	OSCFrame_getBoolValue

000004a8 e1a02006    527 	mov	r2,r6

000004ac e1a03000    528 	mov	r3,r0

000004b0 e1a00004    529 	mov	r0,r4

000004b4 e3a01e40    530 	mov	r1,1<<10

000004b8 eb000000*   531 	bl	snprintf

000004bc e1a01004    532 	mov	r1,r4

000004c0 e1a02000    533 	mov	r2,r0

000004c4 e1a00007    534 	mov	r0,r7

000004c8 eb000000*   535 	bl	OscWriteBuffer_write

000004cc e3500000    536 	cmp	r0,0

000004d0 0afffed4    537 	beq	.L182

000004d4 e2882004    538 	add	r2,r8,4


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
000004d8 e1a0100b    539 	mov	r1,fp

000004dc e1a00005    540 	mov	r0,r5

000004e0 eb000000*   541 	bl	OSCFrame_getBoolValue

000004e4 e1a02006    542 	mov	r2,r6

000004e8 e1a03000    543 	mov	r3,r0

000004ec e1a00004    544 	mov	r0,r4

000004f0 e3a01e40    545 	mov	r1,1<<10

000004f4 eb000000*   546 	bl	snprintf

000004f8 e1a01004    547 	mov	r1,r4

000004fc e1a02000    548 	mov	r2,r0

00000500 e1a00007    549 	mov	r0,r7

00000504 eb000000*   550 	bl	OscWriteBuffer_write

00000508 e3500000    551 	cmp	r0,0

0000050c 0afffec5    552 	beq	.L182

00000510 e2882005    553 	add	r2,r8,5

00000514 e1a0100b    554 	mov	r1,fp

00000518 e1a00005    555 	mov	r0,r5

0000051c eb000000*   556 	bl	OSCFrame_getBoolValue

00000520 e1a02006    557 	mov	r2,r6

00000524 e1a03000    558 	mov	r3,r0

00000528 e1a00004    559 	mov	r0,r4

0000052c e3a01e40    560 	mov	r1,1<<10

00000530 eb000000*   561 	bl	snprintf

00000534 e1a01004    562 	mov	r1,r4

00000538 e1a02000    563 	mov	r2,r0

0000053c e1a00007    564 	mov	r0,r7

00000540 eb000000*   565 	bl	OscWriteBuffer_write

00000544 e3500000    566 	cmp	r0,0

00000548 0afffeb6    567 	beq	.L182

0000054c e2882006    568 	add	r2,r8,6

00000550 e1a0100b    569 	mov	r1,fp

00000554 e1a00005    570 	mov	r0,r5

00000558 eb000000*   571 	bl	OSCFrame_getBoolValue

0000055c e1a02006    572 	mov	r2,r6

00000560 e1a03000    573 	mov	r3,r0

00000564 e1a00004    574 	mov	r0,r4

00000568 e3a01e40    575 	mov	r1,1<<10

0000056c eb000000*   576 	bl	snprintf

00000570 e1a01004    577 	mov	r1,r4

00000574 e1a02000    578 	mov	r2,r0

00000578 e1a00007    579 	mov	r0,r7

0000057c eb000000*   580 	bl	OscWriteBuffer_write

00000580 e3500000    581 	cmp	r0,0

00000584 0afffea7    582 	beq	.L182

00000588 e2882007    583 	add	r2,r8,7

0000058c e1a0100b    584 	mov	r1,fp

00000590 e1a00005    585 	mov	r0,r5

00000594 eb000000*   586 	bl	OSCFrame_getBoolValue

00000598 e1a02006    587 	mov	r2,r6

0000059c e1a03000    588 	mov	r3,r0

000005a0 e1a00004    589 	mov	r0,r4

000005a4 e3a01e40    590 	mov	r1,1<<10

000005a8 eb000000*   591 	bl	snprintf

000005ac e1a01004    592 	mov	r1,r4

000005b0 e1a02000    593 	mov	r2,r0

000005b4 e1a00007    594 	mov	r0,r7

000005b8 eb000000*   595 	bl	OscWriteBuffer_write

000005bc e3500000    596 	cmp	r0,0

000005c0 0afffe98    597 	beq	.L182

000005c4 e2888008    598 	add	r8,r8,8

000005c8 e2599001    599 	subs	r9,r9,1


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
000005cc 1affff7f    600 	bne	.L352

                     601 .L351:

000005d0 e59d0014    602 	ldr	r0,[sp,20]

000005d4 e2109007    603 	ands	r9,r0,7

000005d8 0a000012    604 	beq	.L206

000005dc e51f6170*   605 	ldr	r6,.L1028

                     606 .L378:

000005e0 e1a02008    607 	mov	r2,r8

000005e4 e1a0100b    608 	mov	r1,fp

000005e8 e1a00005    609 	mov	r0,r5

000005ec eb000000*   610 	bl	OSCFrame_getBoolValue

000005f0 e1a02006    611 	mov	r2,r6

000005f4 e1a03000    612 	mov	r3,r0

000005f8 e1a00004    613 	mov	r0,r4

000005fc e3a01e40    614 	mov	r1,1<<10

00000600 eb000000*   615 	bl	snprintf

00000604 e1a01004    616 	mov	r1,r4

00000608 e1a02000    617 	mov	r2,r0

0000060c e1a00007    618 	mov	r0,r7

00000610 eb000000*   619 	bl	OscWriteBuffer_write

00000614 e3500000    620 	cmp	r0,0

00000618 0afffe82    621 	beq	.L182

0000061c e2888001    622 	add	r8,r8,1

00000620 e2599001    623 	subs	r9,r9,1

00000624 1affffed    624 	bne	.L378

                     625 .L206:

00000628 e28f2000*   626 	adr	r2,.L1030

0000062c e1a00004    627 	mov	r0,r4

00000630 e3a01e40    628 	mov	r1,1<<10

00000634 eb000000*   629 	bl	snprintf

00000638 e1a01004    630 	mov	r1,r4

0000063c e1a02000    631 	mov	r2,r0

00000640 e1a00007    632 	mov	r0,r7

00000644 eb000000*   633 	bl	OscWriteBuffer_write

00000648 e3500000    634 	cmp	r0,0

0000064c 0afffe75    635 	beq	.L182

00000650 e28d1004    636 	add	r1,sp,4

00000654 e1a00005    637 	mov	r0,r5

00000658 eb000000*   638 	bl	OSCFrame_getFreq

0000065c e3500000    639 	cmp	r0,0

00000660 0afffe70    640 	beq	.L182

00000664 e59d2010    641 	ldr	r2,[sp,16]

00000668 e59d1004    642 	ldr	r1,[sp,4]

0000066c e1a0000a    643 	mov	r0,r10

00000670 eb000000*   644 	bl	OscReadFileContext_writeFreq

00000674 e3500000    645 	cmp	r0,0

00000678 0afffe6a    646 	beq	.L182

0000067c e59d0010    647 	ldr	r0,[sp,16]

00000680 e2800001    648 	add	r0,r0,1

00000684 e58d0010    649 	str	r0,[sp,16]

00000688 e59d0020    650 	ldr	r0,[sp,32]

0000068c e28bb001    651 	add	fp,fp,1

00000690 e15b0000    652 	cmp	fp,r0

00000694 bafffe79    653 	blt	.L186

                     654 .L184:

00000698 e3a00001    655 	mov	r0,1

                     656 .L176:

0000069c e28dd030    657 	add	sp,sp,48

000006a0 e8bd8ff0    658 	ldmfd	[sp]!,{r4-fp,pc}

                     659 	.endf	OscConverter_processPeriod

                     660 	.align	4


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
                     661 ;formatBuffer	r4	local

                     662 ;datBuffer	r7	local

                     663 ;oscInfo	r5	local

                     664 ;pointPerFrame	[sp,32]	local

                     665 ;analogCount	[sp,28]	local

                     666 ;boolCount	[sp,24]	local

                     667 ;pointNum	fp	local

                     668 ;sampleNum	[sp,16]	local

                     669 ;tick	[sp,40]	local

                     670 ;analogNum	r8	local

                     671 ;boolNum	r8	local

                     672 ;freq	[sp,4]	local

                     673 ;.L888	.L896	static

                     674 ;.L889	.L895	static

                     675 ;.L890	.L894	static

                     676 ;.L891	.L897	static

                     677 

                     678 ;readFileContext	r10	param

                     679 

                     680 	.section ".bss","awb"

                     681 .L887:

                     682 	.section ".rodata","a"

                     683 .L894:;	",%d\000"

00000000 0064252c    684 	.data.b	44,37,100,0

                     685 	.type	.L894,$object

                     686 	.size	.L894,4

                     687 	.data

                     688 	.text

                     689 

                     690 

                     691 	.align	4

                     692 	.align	4

                     693 writeFormatTimeToCfgBuffer:

000006a4 e92d4072    694 	stmfd	[sp]!,{r1,r4-r6,lr}

000006a8 e1a05002    695 	mov	r5,r2

000006ac e24dd03c    696 	sub	sp,sp,60

000006b0 e58d103c    697 	str	r1,[sp,60]

000006b4 e28d103c    698 	add	r1,sp,60

000006b8 e2804050    699 	add	r4,r0,80

000006bc e2806e45    700 	add	r6,r0,0x0450

000006c0 e28d0018    701 	add	r0,sp,24

000006c4 eb000000*   702 	bl	TimeTools_gmtime32

000006c8 e3a01e40    703 	mov	r1,1<<10

000006cc e3500001    704 	cmp	r0,1

000006d0 1a000014    705 	bne	.L1033

000006d4 e59d202c    706 	ldr	r2,[sp,44]

000006d8 e59d3020    707 	ldr	r3,[sp,32]

000006dc e2820e70    708 	add	r0,r2,7<<8

000006e0 e280206c    709 	add	r2,r0,108

000006e4 e59d0028    710 	ldr	r0,[sp,40]

000006e8 e58d202c    711 	str	r2,[sp,44]

000006ec e2800001    712 	add	r0,r0,1

000006f0 e59dc018    713 	ldr	r12,[sp,24]

000006f4 e1a0e005    714 	mov	lr,r5

000006f8 e59d501c    715 	ldr	r5,[sp,28]

000006fc e58d0028    716 	str	r0,[sp,40]

00000700 e88d502d    717 	stmea	[sp],{r0,r2-r3,r5,r12,lr}

00000704 e59d3024    718 	ldr	r3,[sp,36]

00000708 e28f2000*   719 	adr	r2,.L1087

0000070c e1a00004    720 	mov	r0,r4

00000710 eb000000*   721 	bl	snprintf


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
00000714 e1a01004    722 	mov	r1,r4

00000718 e1a02000    723 	mov	r2,r0

0000071c e1a00006    724 	mov	r0,r6

00000720 eb000000*   725 	bl	OscWriteBuffer_write

00000724 ea000007    726 	b	.L1031

                     727 .L1033:

00000728 e1a03005    728 	mov	r3,r5

0000072c e28f2000*   729 	adr	r2,.L1088

00000730 e1a00004    730 	mov	r0,r4

00000734 eb000000*   731 	bl	snprintf

00000738 e1a01004    732 	mov	r1,r4

0000073c e1a02000    733 	mov	r2,r0

00000740 e1a00006    734 	mov	r0,r6

00000744 eb000000*   735 	bl	OscWriteBuffer_write

                     736 .L1031:

00000748 e28dd03c    737 	add	sp,sp,60

0000074c e8bd4072    738 	ldmfd	[sp]!,{r1,r4-r6,lr}

00000750 e12fff1e*   739 	ret	

                     740 	.endf	writeFormatTimeToCfgBuffer

                     741 	.align	4

                     742 ;formatBuffer	r4	local

                     743 ;cfgBuffer	r6	local

                     744 ;size	r2	local

                     745 ;tmTime	[sp,24]	local

                     746 ;.L1073	.L1077	static

                     747 ;.L1074	.L1078	static

                     748 

                     749 ;readFileContext	r0	param

                     750 ;t	[sp,60]	param

                     751 ;ms	r5	param

                     752 

                     753 	.section ".bss","awb"

                     754 .L1072:

                     755 	.data

                     756 	.text

                     757 

                     758 

                     759 ;287: 


                     760 ;288: 


                     761 ;289: bool  OscConverter_processCfg(OscReadFileContext * readFileContext)


                     762 	.align	4

                     763 	.align	4

                     764 	.align	4

                     765 OscConverter_processCfg::

00000754 e92d4ff0    766 	stmfd	[sp]!,{r4-fp,lr}

                     767 ;290: {


                     768 

                     769 ;291: 	char *formatBuffer = readFileContext->formatBuffer;


                     770 

00000758 e1a09000    771 	mov	r9,r0

0000075c e280a050    772 	add	r10,r0,80

00000760 e24dd078    773 	sub	sp,sp,120

00000764 e58da058    774 	str	r10,[sp,88]

                     775 ;292: 	OscWriteBuffer *cfgBuffer = &readFileContext->cfgBuffer;


                     776 

00000768 e280be45    777 	add	fp,r0,0x0450

0000076c e590502c    778 	ldr	r5,[r0,44]

                     779 ;299: 	if (!oscInfo)


                     780 

00000770 e58db05c    781 	str	fp,[sp,92]

                     782 ;293: 	OSCInfoStruct *oscInfo;



                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
                     783 ;294: 	int size;


                     784 ;295: 	int analogCount;


                     785 ;296: 	int boolCount;


                     786 ;297: 


                     787 ;298: 	oscInfo = readFileContext->oscInfo;


                     788 

00000774 e3550000    789 	cmp	r5,0

00000778 0a000372    790 	beq	.L1163

                     791 ;300: 	{


                     792 

                     793 ;301: 		return false;


                     794 

                     795 ;302: 	}


                     796 ;303: 


                     797 ;304: 	analogCount = OSCInfo_getAnalogCount(oscInfo);


                     798 

0000077c e1a00005    799 	mov	r0,r5

00000780 eb000000*   800 	bl	OSCInfo_getAnalogCount

00000784 e1a04000    801 	mov	r4,r0

                     802 ;305: 	boolCount = OSCInfo_getBoolCount(oscInfo);


                     803 

00000788 e1a00005    804 	mov	r0,r5

0000078c eb000000*   805 	bl	OSCInfo_getBoolCount

00000790 e1a05000    806 	mov	r5,r0

                     807 ;306: 


                     808 ;307: 	// имя блока и время


                     809 ;308: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "MTRA,%s,2013\r\n", OSCDescr_getTerminalName());


                     810 

00000794 eb000000*   811 	bl	OSCDescr_getTerminalName

00000798 e1a03000    812 	mov	r3,r0

0000079c e28f2000*   813 	adr	r2,.L2450

000007a0 e1a0000a    814 	mov	r0,r10

000007a4 e3a01e40    815 	mov	r1,1<<10

000007a8 eb000000*   816 	bl	snprintf

                     817 ;309: 	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;


                     818 

000007ac e1a0100a    819 	mov	r1,r10

000007b0 e1a02000    820 	mov	r2,r0

000007b4 e1a0000b    821 	mov	r0,fp

000007b8 eb000000*   822 	bl	OscWriteBuffer_write

000007bc e3500000    823 	cmp	r0,0

000007c0 0a000360    824 	beq	.L1163

                     825 ;310: 


                     826 ;311: 	// количество аналоговых и дискретных


                     827 ;312: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "%d,%dA,%dD\r\n", analogCount + boolCount, 


                     828 

000007c4 e88d0030    829 	stmea	[sp],{r4-r5}

000007c8 e0853004    830 	add	r3,r5,r4

000007cc e28f2000*   831 	adr	r2,.L2451

000007d0 e59d0058    832 	ldr	r0,[sp,88]

000007d4 e3a01e40    833 	mov	r1,1<<10

000007d8 eb000000*   834 	bl	snprintf

                     835 ;313: 		analogCount,


                     836 ;314: 		boolCount);


                     837 ;315: 	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;


                     838 

000007dc e1a02000    839 	mov	r2,r0

000007e0 e59d005c    840 	ldr	r0,[sp,92]

000007e4 e59d1058    841 	ldr	r1,[sp,88]

000007e8 eb000000*   842 	bl	OscWriteBuffer_write

000007ec e3500000    843 	cmp	r0,0


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
000007f0 0a000354    844 	beq	.L1163

                     845 ;316: 


                     846 ;317: 


                     847 ;318: 	// запись каналов


                     848 ;319: 


                     849 ;320: 	// аналоговые


                     850 ;321: 	if (!writeAnalogToCfg(readFileContext))


                     851 

                     852 ;193: {


                     853 

                     854 ;194: 	OSCInfoStruct *oscInfo = readFileContext->oscInfo;


                     855 

000007f4 e599502c    856 	ldr	r5,[r9,44]

                     857 ;195: 	int analogCount = OSCInfo_getAnalogCount(oscInfo);


                     858 

000007f8 e3a04000    859 	mov	r4,0

000007fc e1a00005    860 	mov	r0,r5

00000800 eb000000*   861 	bl	OSCInfo_getAnalogCount

                     862 ;196: 	OscWriteBuffer *cfgBuffer = &readFileContext->cfgBuffer;


                     863 

00000804 e289b050    864 	add	fp,r9,80

                     865 ;198: 	int analogNum;


                     866 ;199: 	int size;


                     867 ;200: 	// аналоговые


                     868 ;201: 	for (analogNum = 0; analogNum < analogCount; ++analogNum)


                     869 

00000808 e2891e45    870 	add	r1,r9,0x0450

0000080c e58d1050    871 	str	r1,[sp,80]

                     872 ;197: 	char *formatBuffer = readFileContext->formatBuffer;


                     873 

00000810 e3500000    874 	cmp	r0,0

00000814 a1a01000    875 	movge	r1,r0

00000818 b3a01000    876 	movlt	r1,0

0000081c e58d1060    877 	str	r1,[sp,96]

00000820 e1b00121    878 	movs	r0,r1 lsr 2

00000824 e58d0048    879 	str	r0,[sp,72]

00000828 0a000101    880 	beq	.L1472

0000082c e3a00000    881 	mov	r0,0

00000830 e58d0068    882 	str	r0,[sp,104]

00000834 e3a006ff    883 	mov	r0,255<<20

00000838 e28005c0    884 	add	r0,r0,3<<28

0000083c e58d006c    885 	str	r0,[sp,108]

00000840 e59f01b4*   886 	ldr	r0,.L2452

00000844 e59f71b4*   887 	ldr	r7,.L2453

00000848 e58d004c    888 	str	r0,[sp,76]

                     889 .L1473:

0000084c e1a01004    890 	mov	r1,r4

00000850 e1a00005    891 	mov	r0,r5

00000854 eb000000*   892 	bl	OSCInfo_getAnalog

00000858 e1a01004    893 	mov	r1,r4

0000085c e1a06000    894 	mov	r6,r0

00000860 e1a00005    895 	mov	r0,r5

00000864 eb000000*   896 	bl	OSCInfo_getAnalogCft

00000868 eb000000*   897 	bl	__ftod

0000086c e58d1074    898 	str	r1,[sp,116]

00000870 e1a01004    899 	mov	r1,r4

00000874 e58d0070    900 	str	r0,[sp,112]

00000878 e1a00005    901 	mov	r0,r5

0000087c eb000000*   902 	bl	OSCInfo_getAnalogMax

00000880 e1a01004    903 	mov	r1,r4

00000884 e1a0a000    904 	mov	r10,r0


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
00000888 e1a00005    905 	mov	r0,r5

0000088c eb000000*   906 	bl	OSCInfo_getAnalogMin

00000890 e58d0044    907 	str	r0,[sp,68]

00000894 e1a00006    908 	mov	r0,r6

00000898 eb000000*   909 	bl	OSCDescr_analogName

0000089c e1a08000    910 	mov	r8,r0

000008a0 e1a00006    911 	mov	r0,r6

000008a4 eb000000*   912 	bl	OSCDescr_analogUnits

000008a8 e3a0c053    913 	mov	r12,83

000008ac e59d2068    914 	ldr	r2,[sp,104]

000008b0 e59d306c    915 	ldr	r3,[sp,108]

000008b4 e58d202c    916 	str	r2,[sp,44]

000008b8 e59d106c    917 	ldr	r1,[sp,108]

000008bc e28de030    918 	add	lr,sp,48

000008c0 e88e100e    919 	stmea	[lr],{r1-r3,r12}

000008c4 e59d3044    920 	ldr	r3,[sp,68]

000008c8 e3a01000    921 	mov	r1,0

000008cc e1a02001    922 	mov	r2,r1

000008d0 e1a0e001    923 	mov	lr,r1

000008d4 e28dc01c    924 	add	r12,sp,28

000008d8 e88c040e    925 	stmea	[r12],{r1-r3,r10}

000008dc e59d3070    926 	ldr	r3,[sp,112]

000008e0 e59dc074    927 	ldr	r12,[sp,116]

000008e4 e1a01007    928 	mov	r1,r7

000008e8 e1a02000    929 	mov	r2,r0

000008ec e1a00007    930 	mov	r0,r7

000008f0 e98d500f    931 	stmfa	[sp],{r0-r3,r12,lr}

000008f4 e58d8000    932 	str	r8,[sp]

000008f8 e2843001    933 	add	r3,r4,1

000008fc e59d204c    934 	ldr	r2,[sp,76]

00000900 e1a0000b    935 	mov	r0,fp

00000904 e3a01e40    936 	mov	r1,1<<10

00000908 eb000000*   937 	bl	snprintf

0000090c e1a02000    938 	mov	r2,r0

00000910 e59d0050    939 	ldr	r0,[sp,80]

00000914 e1a0100b    940 	mov	r1,fp

00000918 eb000000*   941 	bl	OscWriteBuffer_write

0000091c e3500000    942 	cmp	r0,0

00000920 0a000308    943 	beq	.L1163

00000924 e2841001    944 	add	r1,r4,1

00000928 e1a00005    945 	mov	r0,r5

0000092c eb000000*   946 	bl	OSCInfo_getAnalog

00000930 e2841001    947 	add	r1,r4,1

00000934 e1a06000    948 	mov	r6,r0

00000938 e1a00005    949 	mov	r0,r5

0000093c eb000000*   950 	bl	OSCInfo_getAnalogCft

00000940 eb000000*   951 	bl	__ftod

00000944 e58d1074    952 	str	r1,[sp,116]

00000948 e2841001    953 	add	r1,r4,1

0000094c e58d0070    954 	str	r0,[sp,112]

00000950 e1a00005    955 	mov	r0,r5

00000954 eb000000*   956 	bl	OSCInfo_getAnalogMax

00000958 e2841001    957 	add	r1,r4,1

0000095c e1a0a000    958 	mov	r10,r0

00000960 e1a00005    959 	mov	r0,r5

00000964 eb000000*   960 	bl	OSCInfo_getAnalogMin

00000968 e58d0044    961 	str	r0,[sp,68]

0000096c e1a00006    962 	mov	r0,r6

00000970 eb000000*   963 	bl	OSCDescr_analogName

00000974 e1a08000    964 	mov	r8,r0

00000978 e1a00006    965 	mov	r0,r6


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
0000097c eb000000*   966 	bl	OSCDescr_analogUnits

00000980 e3a0c053    967 	mov	r12,83

00000984 e59d2068    968 	ldr	r2,[sp,104]

00000988 e59d306c    969 	ldr	r3,[sp,108]

0000098c e58d202c    970 	str	r2,[sp,44]

00000990 ea00001b    971 	b	.L2454

                     972 	.align	4

                     973 .L1030:

                     974 ;	"\r\n\000"

00000994 0a0d       975 	.data.b	13,10

00000996 00         976 	.data.b	0

00000997 00         977 	.align 4

                     978 

                     979 	.type	.L1030,$object

                     980 	.size	.L1030,4

                     981 

                     982 .L1087:

                     983 ;	"%02d/%02d/%04d,%02d:%02d:%02d.%03d\r\n\000"

00000998 64323025    984 	.data.b	37,48,50,100

0000099c 3230252f    985 	.data.b	47,37,48,50

000009a0 30252f64    986 	.data.b	100,47,37,48

000009a4 252c6434    987 	.data.b	52,100,44,37

000009a8 3a643230    988 	.data.b	48,50,100,58

000009ac 64323025    989 	.data.b	37,48,50,100

000009b0 3230253a    990 	.data.b	58,37,48,50

000009b4 30252e64    991 	.data.b	100,46,37,48

000009b8 0a0d6433    992 	.data.b	51,100,13,10

000009bc 00         993 	.data.b	0

000009bd 000000     994 	.align 4

                     995 

                     996 	.type	.L1087,$object

                     997 	.size	.L1087,4

                     998 

                     999 .L1088:

                    1000 ;	"01/01/1980 00:00:00.%03d\r\n\000"

000009c0 302f3130   1001 	.data.b	48,49,47,48

000009c4 39312f31   1002 	.data.b	49,47,49,57

000009c8 30203038   1003 	.data.b	56,48,32,48

000009cc 30303a30   1004 	.data.b	48,58,48,48

000009d0 2e30303a   1005 	.data.b	58,48,48,46

000009d4 64333025   1006 	.data.b	37,48,51,100

000009d8 0a0d      1007 	.data.b	13,10

000009da 00        1008 	.data.b	0

000009db 00        1009 	.align 4

                    1010 

                    1011 	.type	.L1088,$object

                    1012 	.size	.L1088,4

                    1013 

                    1014 .L2450:

                    1015 ;	"MTRA,%s,2013\r\n\000"

000009dc 4152544d   1016 	.data.b	77,84,82,65

000009e0 2c73252c   1017 	.data.b	44,37,115,44

000009e4 33313032   1018 	.data.b	50,48,49,51

000009e8 0a0d      1019 	.data.b	13,10

000009ea 00        1020 	.data.b	0

000009eb 00        1021 	.align 4

                    1022 

                    1023 	.type	.L2450,$object

                    1024 	.size	.L2450,4

                    1025 

                    1026 .L2451:


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
                    1027 ;	"%d,%dA,%dD\r\n\000"

000009ec 252c6425   1028 	.data.b	37,100,44,37

000009f0 252c4164   1029 	.data.b	100,65,44,37

000009f4 0a0d4464   1030 	.data.b	100,68,13,10

000009f8 00        1031 	.data.b	0

000009f9 000000    1032 	.align 4

                    1033 

                    1034 	.type	.L2451,$object

                    1035 	.size	.L2451,4

                    1036 

                    1037 .L2452:

000009fc 00000000*  1038 	.data.w	.L2254

                    1039 	.type	.L2452,$object

                    1040 	.size	.L2452,4

                    1041 

                    1042 .L2453:

00000a00 00000000*  1043 	.data.w	.L2252

                    1044 	.type	.L2453,$object

                    1045 	.size	.L2453,4

                    1046 

                    1047 .L2454:

                    1048 

00000a04 e59d106c   1049 	ldr	r1,[sp,108]

00000a08 e28de030   1050 	add	lr,sp,48

00000a0c e88e100e   1051 	stmea	[lr],{r1-r3,r12}

00000a10 e59d3044   1052 	ldr	r3,[sp,68]

00000a14 e3a01000   1053 	mov	r1,0

00000a18 e1a02001   1054 	mov	r2,r1

00000a1c e1a0e001   1055 	mov	lr,r1

00000a20 e28dc01c   1056 	add	r12,sp,28

00000a24 e88c040e   1057 	stmea	[r12],{r1-r3,r10}

00000a28 e59d3070   1058 	ldr	r3,[sp,112]

00000a2c e59dc074   1059 	ldr	r12,[sp,116]

00000a30 e1a01007   1060 	mov	r1,r7

00000a34 e1a02000   1061 	mov	r2,r0

00000a38 e1a00007   1062 	mov	r0,r7

00000a3c e98d500f   1063 	stmfa	[sp],{r0-r3,r12,lr}

00000a40 e58d8000   1064 	str	r8,[sp]

00000a44 e2843002   1065 	add	r3,r4,2

00000a48 e59d204c   1066 	ldr	r2,[sp,76]

00000a4c e1a0000b   1067 	mov	r0,fp

00000a50 e3a01e40   1068 	mov	r1,1<<10

00000a54 eb000000*  1069 	bl	snprintf

00000a58 e1a02000   1070 	mov	r2,r0

00000a5c e59d0050   1071 	ldr	r0,[sp,80]

00000a60 e1a0100b   1072 	mov	r1,fp

00000a64 eb000000*  1073 	bl	OscWriteBuffer_write

00000a68 e3500000   1074 	cmp	r0,0

00000a6c 0a0002b5   1075 	beq	.L1163

00000a70 e2841002   1076 	add	r1,r4,2

00000a74 e1a00005   1077 	mov	r0,r5

00000a78 eb000000*  1078 	bl	OSCInfo_getAnalog

00000a7c e2841002   1079 	add	r1,r4,2

00000a80 e1a06000   1080 	mov	r6,r0

00000a84 e1a00005   1081 	mov	r0,r5

00000a88 eb000000*  1082 	bl	OSCInfo_getAnalogCft

00000a8c eb000000*  1083 	bl	__ftod

00000a90 e58d1074   1084 	str	r1,[sp,116]

00000a94 e2841002   1085 	add	r1,r4,2

00000a98 e58d0070   1086 	str	r0,[sp,112]

00000a9c e1a00005   1087 	mov	r0,r5


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
00000aa0 eb000000*  1088 	bl	OSCInfo_getAnalogMax

00000aa4 e2841002   1089 	add	r1,r4,2

00000aa8 e1a0a000   1090 	mov	r10,r0

00000aac e1a00005   1091 	mov	r0,r5

00000ab0 eb000000*  1092 	bl	OSCInfo_getAnalogMin

00000ab4 e58d0044   1093 	str	r0,[sp,68]

00000ab8 e1a00006   1094 	mov	r0,r6

00000abc eb000000*  1095 	bl	OSCDescr_analogName

00000ac0 e1a08000   1096 	mov	r8,r0

00000ac4 e1a00006   1097 	mov	r0,r6

00000ac8 eb000000*  1098 	bl	OSCDescr_analogUnits

00000acc e3a0c053   1099 	mov	r12,83

00000ad0 e59d2068   1100 	ldr	r2,[sp,104]

00000ad4 e59d306c   1101 	ldr	r3,[sp,108]

00000ad8 e58d202c   1102 	str	r2,[sp,44]

00000adc e59d106c   1103 	ldr	r1,[sp,108]

00000ae0 e28de030   1104 	add	lr,sp,48

00000ae4 e88e100e   1105 	stmea	[lr],{r1-r3,r12}

00000ae8 e59d3044   1106 	ldr	r3,[sp,68]

00000aec e3a01000   1107 	mov	r1,0

00000af0 e1a02001   1108 	mov	r2,r1

00000af4 e1a0e001   1109 	mov	lr,r1

00000af8 e28dc01c   1110 	add	r12,sp,28

00000afc e88c040e   1111 	stmea	[r12],{r1-r3,r10}

00000b00 e59d3070   1112 	ldr	r3,[sp,112]

00000b04 e59dc074   1113 	ldr	r12,[sp,116]

00000b08 e1a01007   1114 	mov	r1,r7

00000b0c e1a02000   1115 	mov	r2,r0

00000b10 e1a00007   1116 	mov	r0,r7

00000b14 e98d500f   1117 	stmfa	[sp],{r0-r3,r12,lr}

00000b18 e58d8000   1118 	str	r8,[sp]

00000b1c e2843003   1119 	add	r3,r4,3

00000b20 e59d204c   1120 	ldr	r2,[sp,76]

00000b24 e1a0000b   1121 	mov	r0,fp

00000b28 e3a01e40   1122 	mov	r1,1<<10

00000b2c eb000000*  1123 	bl	snprintf

00000b30 e1a02000   1124 	mov	r2,r0

00000b34 e59d0050   1125 	ldr	r0,[sp,80]

00000b38 e1a0100b   1126 	mov	r1,fp

00000b3c eb000000*  1127 	bl	OscWriteBuffer_write

00000b40 e3500000   1128 	cmp	r0,0

00000b44 0a00027f   1129 	beq	.L1163

00000b48 e2841003   1130 	add	r1,r4,3

00000b4c e1a00005   1131 	mov	r0,r5

00000b50 eb000000*  1132 	bl	OSCInfo_getAnalog

00000b54 e2841003   1133 	add	r1,r4,3

00000b58 e1a06000   1134 	mov	r6,r0

00000b5c e1a00005   1135 	mov	r0,r5

00000b60 eb000000*  1136 	bl	OSCInfo_getAnalogCft

00000b64 eb000000*  1137 	bl	__ftod

00000b68 e58d1074   1138 	str	r1,[sp,116]

00000b6c e2841003   1139 	add	r1,r4,3

00000b70 e58d0070   1140 	str	r0,[sp,112]

00000b74 e1a00005   1141 	mov	r0,r5

00000b78 eb000000*  1142 	bl	OSCInfo_getAnalogMax

00000b7c e2841003   1143 	add	r1,r4,3

00000b80 e1a0a000   1144 	mov	r10,r0

00000b84 e1a00005   1145 	mov	r0,r5

00000b88 eb000000*  1146 	bl	OSCInfo_getAnalogMin

00000b8c e58d0044   1147 	str	r0,[sp,68]

00000b90 e1a00006   1148 	mov	r0,r6


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
00000b94 eb000000*  1149 	bl	OSCDescr_analogName

00000b98 e1a08000   1150 	mov	r8,r0

00000b9c e1a00006   1151 	mov	r0,r6

00000ba0 eb000000*  1152 	bl	OSCDescr_analogUnits

00000ba4 e3a0c053   1153 	mov	r12,83

00000ba8 e59d2068   1154 	ldr	r2,[sp,104]

00000bac e59d306c   1155 	ldr	r3,[sp,108]

00000bb0 e58d202c   1156 	str	r2,[sp,44]

00000bb4 e59d106c   1157 	ldr	r1,[sp,108]

00000bb8 e28de030   1158 	add	lr,sp,48

00000bbc e88e100e   1159 	stmea	[lr],{r1-r3,r12}

00000bc0 e59dc044   1160 	ldr	r12,[sp,68]

00000bc4 e1a0e00a   1161 	mov	lr,r10

00000bc8 e3a0a000   1162 	mov	r10,0

00000bcc e1a0200a   1163 	mov	r2,r10

00000bd0 e1a0300a   1164 	mov	r3,r10

00000bd4 e28d1018   1165 	add	r1,sp,24

00000bd8 e881540c   1166 	stmea	[r1],{r2-r3,r10,r12,lr}

00000bdc e59dc070   1167 	ldr	r12,[sp,112]

00000be0 e59de074   1168 	ldr	lr,[sp,116]

00000be4 e1a01007   1169 	mov	r1,r7

00000be8 e1a0a000   1170 	mov	r10,r0

00000bec e1a00008   1171 	mov	r0,r8

00000bf0 e88d5483   1172 	stmea	[sp],{r0-r1,r7,r10,r12,lr}

00000bf4 e2843004   1173 	add	r3,r4,4

00000bf8 e59d204c   1174 	ldr	r2,[sp,76]

00000bfc e1a0000b   1175 	mov	r0,fp

00000c00 e3a01e40   1176 	mov	r1,1<<10

00000c04 eb000000*  1177 	bl	snprintf

00000c08 e1a02000   1178 	mov	r2,r0

00000c0c e59d0050   1179 	ldr	r0,[sp,80]

00000c10 e1a0100b   1180 	mov	r1,fp

00000c14 eb000000*  1181 	bl	OscWriteBuffer_write

00000c18 e3500000   1182 	cmp	r0,0

00000c1c 0a000249   1183 	beq	.L1163

00000c20 e59d0048   1184 	ldr	r0,[sp,72]

00000c24 e2844004   1185 	add	r4,r4,4

00000c28 e2500001   1186 	subs	r0,r0,1

00000c2c e58d0048   1187 	str	r0,[sp,72]

00000c30 1affff05   1188 	bne	.L1473

                    1189 .L1472:

00000c34 e59d0060   1190 	ldr	r0,[sp,96]

00000c38 e2100003   1191 	ands	r0,r0,3

00000c3c e58d0048   1192 	str	r0,[sp,72]

00000c40 0a000043   1193 	beq	.L1116

00000c44 e3a00000   1194 	mov	r0,0

00000c48 e58d0068   1195 	str	r0,[sp,104]

00000c4c e3a006ff   1196 	mov	r0,255<<20

00000c50 e28005c0   1197 	add	r0,r0,3<<28

00000c54 e58d006c   1198 	str	r0,[sp,108]

00000c58 e51f0264*  1199 	ldr	r0,.L2452

00000c5c e51f7264*  1200 	ldr	r7,.L2453

00000c60 e58d004c   1201 	str	r0,[sp,76]

                    1202 .L1491:

00000c64 e1a01004   1203 	mov	r1,r4

00000c68 e1a00005   1204 	mov	r0,r5

00000c6c eb000000*  1205 	bl	OSCInfo_getAnalog

00000c70 e1a01004   1206 	mov	r1,r4

00000c74 e1a06000   1207 	mov	r6,r0

00000c78 e1a00005   1208 	mov	r0,r5

00000c7c eb000000*  1209 	bl	OSCInfo_getAnalogCft


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
00000c80 eb000000*  1210 	bl	__ftod

00000c84 e58d1074   1211 	str	r1,[sp,116]

00000c88 e1a01004   1212 	mov	r1,r4

00000c8c e58d0070   1213 	str	r0,[sp,112]

00000c90 e1a00005   1214 	mov	r0,r5

00000c94 eb000000*  1215 	bl	OSCInfo_getAnalogMax

00000c98 e1a01004   1216 	mov	r1,r4

00000c9c e1a0a000   1217 	mov	r10,r0

00000ca0 e1a00005   1218 	mov	r0,r5

00000ca4 eb000000*  1219 	bl	OSCInfo_getAnalogMin

00000ca8 e1a08000   1220 	mov	r8,r0

00000cac e1a00006   1221 	mov	r0,r6

00000cb0 eb000000*  1222 	bl	OSCDescr_analogName

00000cb4 e58d0060   1223 	str	r0,[sp,96]

00000cb8 e1a00006   1224 	mov	r0,r6

00000cbc eb000000*  1225 	bl	OSCDescr_analogUnits

00000cc0 e3a0c053   1226 	mov	r12,83

00000cc4 e59d2068   1227 	ldr	r2,[sp,104]

00000cc8 e59d306c   1228 	ldr	r3,[sp,108]

00000ccc e58d202c   1229 	str	r2,[sp,44]

00000cd0 e59d106c   1230 	ldr	r1,[sp,108]

00000cd4 e28de030   1231 	add	lr,sp,48

00000cd8 e88e100e   1232 	stmea	[lr],{r1-r3,r12}

00000cdc e1a0c00a   1233 	mov	r12,r10

00000ce0 e1a0a008   1234 	mov	r10,r8

00000ce4 e3a08000   1235 	mov	r8,0

00000ce8 e1a02008   1236 	mov	r2,r8

00000cec e59d1070   1237 	ldr	r1,[sp,112]

00000cf0 e1a03008   1238 	mov	r3,r8

00000cf4 e58d1010   1239 	str	r1,[sp,16]

00000cf8 e59d1074   1240 	ldr	r1,[sp,116]

00000cfc e28de014   1241 	add	lr,sp,20

00000d00 e88e150e   1242 	stmea	[lr],{r1-r3,r8,r10,r12}

00000d04 e1a08000   1243 	mov	r8,r0

00000d08 e59d0060   1244 	ldr	r0,[sp,96]

00000d0c e1a01007   1245 	mov	r1,r7

00000d10 e88d0183   1246 	stmea	[sp],{r0-r1,r7-r8}

00000d14 e2843001   1247 	add	r3,r4,1

00000d18 e59d204c   1248 	ldr	r2,[sp,76]

00000d1c e1a0000b   1249 	mov	r0,fp

00000d20 e3a01e40   1250 	mov	r1,1<<10

00000d24 eb000000*  1251 	bl	snprintf

00000d28 e1a02000   1252 	mov	r2,r0

00000d2c e59d0050   1253 	ldr	r0,[sp,80]

00000d30 e1a0100b   1254 	mov	r1,fp

00000d34 eb000000*  1255 	bl	OscWriteBuffer_write

00000d38 e3500000   1256 	cmp	r0,0

00000d3c 0a000201   1257 	beq	.L1163

00000d40 e59d0048   1258 	ldr	r0,[sp,72]

00000d44 e2844001   1259 	add	r4,r4,1

00000d48 e2500001   1260 	subs	r0,r0,1

00000d4c e58d0048   1261 	str	r0,[sp,72]

00000d50 1affffc3   1262 	bne	.L1491

                    1263 .L1116:

                    1264 ;224: 


                    1265 ;225: 	}


                    1266 ;226: 


                    1267 ;227: 	return true;


                    1268 

                    1269 ;322: 	{


                    1270 


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
                    1271 ;323: 		return false;


                    1272 

                    1273 ;324: 	}


                    1274 ;325: 


                    1275 ;326: 	// дискретные


                    1276 ;327: 	if (!writeBoolToCfg(readFileContext))


                    1277 

                    1278 ;232: {


                    1279 

                    1280 ;233: 	OSCInfoStruct *oscInfo = readFileContext->oscInfo;


                    1281 

00000d54 e599602c   1282 	ldr	r6,[r9,44]

                    1283 ;234: 	int boolCount = OSCInfo_getBoolCount(oscInfo);


                    1284 

00000d58 e3a04000   1285 	mov	r4,0

00000d5c e1a00006   1286 	mov	r0,r6

00000d60 eb000000*  1287 	bl	OSCInfo_getBoolCount

                    1288 ;235: 	OscWriteBuffer *cfgBuffer = &readFileContext->cfgBuffer;


                    1289 

00000d64 e289be45   1290 	add	fp,r9,0x0450

                    1291 ;236: 	char *formatBuffer = readFileContext->formatBuffer;


                    1292 

00000d68 e2895050   1293 	add	r5,r9,80

                    1294 ;237: 	int boolNum;


                    1295 ;238: 	int size;


                    1296 ;239: 


                    1297 ;240: 	for (boolNum = 0; boolNum < boolCount; ++boolNum)


                    1298 

00000d6c e3500000   1299 	cmp	r0,0

00000d70 a1a01000   1300 	movge	r1,r0

00000d74 b3a01000   1301 	movlt	r1,0

00000d78 e58d1060   1302 	str	r1,[sp,96]

00000d7c e1b081a1   1303 	movs	r8,r1 lsr 3

00000d80 0a00008b   1304 	beq	.L1431

00000d84 e59f770c*  1305 	ldr	r7,.L2455

                    1306 .L1432:

00000d88 e1a01004   1307 	mov	r1,r4

00000d8c e1a00006   1308 	mov	r0,r6

00000d90 eb000000*  1309 	bl	OSCInfo_getBool

00000d94 eb000000*  1310 	bl	OSCDescr_boolName

00000d98 e3a01001   1311 	mov	r1,1

00000d9c e88d0003   1312 	stmea	[sp],{r0-r1}

00000da0 e2843001   1313 	add	r3,r4,1

00000da4 e1a02007   1314 	mov	r2,r7

00000da8 e1a00005   1315 	mov	r0,r5

00000dac e3a01e40   1316 	mov	r1,1<<10

00000db0 eb000000*  1317 	bl	snprintf

00000db4 e1a01005   1318 	mov	r1,r5

00000db8 e1a02000   1319 	mov	r2,r0

00000dbc e1a0000b   1320 	mov	r0,fp

00000dc0 eb000000*  1321 	bl	OscWriteBuffer_write

00000dc4 e3500000   1322 	cmp	r0,0

00000dc8 0a0001de   1323 	beq	.L1163

00000dcc e2841001   1324 	add	r1,r4,1

00000dd0 e1a00006   1325 	mov	r0,r6

00000dd4 eb000000*  1326 	bl	OSCInfo_getBool

00000dd8 eb000000*  1327 	bl	OSCDescr_boolName

00000ddc e3a01001   1328 	mov	r1,1

00000de0 e88d0003   1329 	stmea	[sp],{r0-r1}

00000de4 e2843002   1330 	add	r3,r4,2

00000de8 e1a02007   1331 	mov	r2,r7


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
00000dec e1a00005   1332 	mov	r0,r5

00000df0 e3a01e40   1333 	mov	r1,1<<10

00000df4 eb000000*  1334 	bl	snprintf

00000df8 e1a01005   1335 	mov	r1,r5

00000dfc e1a02000   1336 	mov	r2,r0

00000e00 e1a0000b   1337 	mov	r0,fp

00000e04 eb000000*  1338 	bl	OscWriteBuffer_write

00000e08 e3500000   1339 	cmp	r0,0

00000e0c 0a0001cd   1340 	beq	.L1163

00000e10 e2841002   1341 	add	r1,r4,2

00000e14 e1a00006   1342 	mov	r0,r6

00000e18 eb000000*  1343 	bl	OSCInfo_getBool

00000e1c eb000000*  1344 	bl	OSCDescr_boolName

00000e20 e3a01001   1345 	mov	r1,1

00000e24 e88d0003   1346 	stmea	[sp],{r0-r1}

00000e28 e2843003   1347 	add	r3,r4,3

00000e2c e1a02007   1348 	mov	r2,r7

00000e30 e1a00005   1349 	mov	r0,r5

00000e34 e3a01e40   1350 	mov	r1,1<<10

00000e38 eb000000*  1351 	bl	snprintf

00000e3c e1a01005   1352 	mov	r1,r5

00000e40 e1a02000   1353 	mov	r2,r0

00000e44 e1a0000b   1354 	mov	r0,fp

00000e48 eb000000*  1355 	bl	OscWriteBuffer_write

00000e4c e3500000   1356 	cmp	r0,0

00000e50 0a0001bc   1357 	beq	.L1163

00000e54 e2841003   1358 	add	r1,r4,3

00000e58 e1a00006   1359 	mov	r0,r6

00000e5c eb000000*  1360 	bl	OSCInfo_getBool

00000e60 eb000000*  1361 	bl	OSCDescr_boolName

00000e64 e3a01001   1362 	mov	r1,1

00000e68 e88d0003   1363 	stmea	[sp],{r0-r1}

00000e6c e2843004   1364 	add	r3,r4,4

00000e70 e1a02007   1365 	mov	r2,r7

00000e74 e1a00005   1366 	mov	r0,r5

00000e78 e3a01e40   1367 	mov	r1,1<<10

00000e7c eb000000*  1368 	bl	snprintf

00000e80 e1a01005   1369 	mov	r1,r5

00000e84 e1a02000   1370 	mov	r2,r0

00000e88 e1a0000b   1371 	mov	r0,fp

00000e8c eb000000*  1372 	bl	OscWriteBuffer_write

00000e90 e3500000   1373 	cmp	r0,0

00000e94 0a0001ab   1374 	beq	.L1163

00000e98 e2841004   1375 	add	r1,r4,4

00000e9c e1a00006   1376 	mov	r0,r6

00000ea0 eb000000*  1377 	bl	OSCInfo_getBool

00000ea4 eb000000*  1378 	bl	OSCDescr_boolName

00000ea8 e3a01001   1379 	mov	r1,1

00000eac e88d0003   1380 	stmea	[sp],{r0-r1}

00000eb0 e2843005   1381 	add	r3,r4,5

00000eb4 e1a02007   1382 	mov	r2,r7

00000eb8 e1a00005   1383 	mov	r0,r5

00000ebc e3a01e40   1384 	mov	r1,1<<10

00000ec0 eb000000*  1385 	bl	snprintf

00000ec4 e1a01005   1386 	mov	r1,r5

00000ec8 e1a02000   1387 	mov	r2,r0

00000ecc e1a0000b   1388 	mov	r0,fp

00000ed0 eb000000*  1389 	bl	OscWriteBuffer_write

00000ed4 e3500000   1390 	cmp	r0,0

00000ed8 0a00019a   1391 	beq	.L1163

00000edc e2841005   1392 	add	r1,r4,5


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
00000ee0 e1a00006   1393 	mov	r0,r6

00000ee4 eb000000*  1394 	bl	OSCInfo_getBool

00000ee8 eb000000*  1395 	bl	OSCDescr_boolName

00000eec e3a01001   1396 	mov	r1,1

00000ef0 e88d0003   1397 	stmea	[sp],{r0-r1}

00000ef4 e2843006   1398 	add	r3,r4,6

00000ef8 e1a02007   1399 	mov	r2,r7

00000efc e1a00005   1400 	mov	r0,r5

00000f00 e3a01e40   1401 	mov	r1,1<<10

00000f04 eb000000*  1402 	bl	snprintf

00000f08 e1a01005   1403 	mov	r1,r5

00000f0c e1a02000   1404 	mov	r2,r0

00000f10 e1a0000b   1405 	mov	r0,fp

00000f14 eb000000*  1406 	bl	OscWriteBuffer_write

00000f18 e3500000   1407 	cmp	r0,0

00000f1c 0a000189   1408 	beq	.L1163

00000f20 e2841006   1409 	add	r1,r4,6

00000f24 e1a00006   1410 	mov	r0,r6

00000f28 eb000000*  1411 	bl	OSCInfo_getBool

00000f2c eb000000*  1412 	bl	OSCDescr_boolName

00000f30 e3a01001   1413 	mov	r1,1

00000f34 e88d0003   1414 	stmea	[sp],{r0-r1}

00000f38 e2843007   1415 	add	r3,r4,7

00000f3c e1a02007   1416 	mov	r2,r7

00000f40 e1a00005   1417 	mov	r0,r5

00000f44 e3a01e40   1418 	mov	r1,1<<10

00000f48 eb000000*  1419 	bl	snprintf

00000f4c e1a01005   1420 	mov	r1,r5

00000f50 e1a02000   1421 	mov	r2,r0

00000f54 e1a0000b   1422 	mov	r0,fp

00000f58 eb000000*  1423 	bl	OscWriteBuffer_write

00000f5c e3500000   1424 	cmp	r0,0

00000f60 0a000178   1425 	beq	.L1163

00000f64 e2841007   1426 	add	r1,r4,7

00000f68 e1a00006   1427 	mov	r0,r6

00000f6c eb000000*  1428 	bl	OSCInfo_getBool

00000f70 eb000000*  1429 	bl	OSCDescr_boolName

00000f74 e3a01001   1430 	mov	r1,1

00000f78 e88d0003   1431 	stmea	[sp],{r0-r1}

00000f7c e2843008   1432 	add	r3,r4,8

00000f80 e1a02007   1433 	mov	r2,r7

00000f84 e1a00005   1434 	mov	r0,r5

00000f88 e3a01e40   1435 	mov	r1,1<<10

00000f8c eb000000*  1436 	bl	snprintf

00000f90 e1a01005   1437 	mov	r1,r5

00000f94 e1a02000   1438 	mov	r2,r0

00000f98 e1a0000b   1439 	mov	r0,fp

00000f9c eb000000*  1440 	bl	OscWriteBuffer_write

00000fa0 e3500000   1441 	cmp	r0,0

00000fa4 0a000167   1442 	beq	.L1163

00000fa8 e2844008   1443 	add	r4,r4,8

00000fac e2588001   1444 	subs	r8,r8,1

00000fb0 1affff74   1445 	bne	.L1432

                    1446 .L1431:

00000fb4 e59d0060   1447 	ldr	r0,[sp,96]

00000fb8 e2108007   1448 	ands	r8,r0,7

00000fbc 0a000014   1449 	beq	.L1128

00000fc0 e59f74d0*  1450 	ldr	r7,.L2455

                    1451 .L1466:

00000fc4 e1a01004   1452 	mov	r1,r4

00000fc8 e1a00006   1453 	mov	r0,r6


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
00000fcc eb000000*  1454 	bl	OSCInfo_getBool

00000fd0 eb000000*  1455 	bl	OSCDescr_boolName

00000fd4 e3a01001   1456 	mov	r1,1

00000fd8 e88d0003   1457 	stmea	[sp],{r0-r1}

00000fdc e2843001   1458 	add	r3,r4,1

00000fe0 e1a02007   1459 	mov	r2,r7

00000fe4 e1a00005   1460 	mov	r0,r5

00000fe8 e3a01e40   1461 	mov	r1,1<<10

00000fec eb000000*  1462 	bl	snprintf

00000ff0 e1a01005   1463 	mov	r1,r5

00000ff4 e1a02000   1464 	mov	r2,r0

00000ff8 e1a0000b   1465 	mov	r0,fp

00000ffc eb000000*  1466 	bl	OscWriteBuffer_write

00001000 e3500000   1467 	cmp	r0,0

00001004 0a00014f   1468 	beq	.L1163

00001008 e2844001   1469 	add	r4,r4,1

0000100c e2588001   1470 	subs	r8,r8,1

00001010 1affffeb   1471 	bne	.L1466

                    1472 .L1128:

                    1473 ;249: 	}


                    1474 ;250: 


                    1475 ;251: 	return true;


                    1476 

                    1477 ;328: 	{


                    1478 

                    1479 ;329: 		return false;


                    1480 

                    1481 ;330: 	}


                    1482 ;331: 	// изменение частоты


                    1483 ;332: 	if (!writeFrequencyToCfg(readFileContext))


                    1484 

                    1485 ;256: {


                    1486 

                    1487 ;257: 	OscWriteBuffer *cfgBuffer = &readFileContext->cfgBuffer;


                    1488 

00001014 e2897e45   1489 	add	r7,r9,0x0450

                    1490 ;258: 	char *formatBuffer = readFileContext->formatBuffer;


                    1491 

00001018 e2895050   1492 	add	r5,r9,80

                    1493 ;259: 	double freq;


                    1494 ;260: 	int freqCount;


                    1495 ;261: 	int freqNum;


                    1496 ;262: 	int size;


                    1497 ;263: 	// частота


                    1498 ;264: 	freq = OSCDescr_getFreq();


                    1499 

0000101c eb000000*  1500 	bl	OSCDescr_getFreq

00001020 eb000000*  1501 	bl	__ftod

                    1502 ;265: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "%d\r\n", (int)freq);


                    1503 

00001024 e59f6470*  1504 	ldr	r6,.L2456

00001028 eb000000*  1505 	bl	__dtoi

0000102c e1a02006   1506 	mov	r2,r6

00001030 e1a03000   1507 	mov	r3,r0

00001034 e1a00005   1508 	mov	r0,r5

00001038 e3a01e40   1509 	mov	r1,1<<10

0000103c eb000000*  1510 	bl	snprintf

                    1511 ;266: 	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;


                    1512 

00001040 e1a01005   1513 	mov	r1,r5

00001044 e1a02000   1514 	mov	r2,r0


                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
00001048 e1a00007   1515 	mov	r0,r7

0000104c eb000000*  1516 	bl	OscWriteBuffer_write

00001050 e3500000   1517 	cmp	r0,0

00001054 0a00013b   1518 	beq	.L1163

                    1519 ;267: 


                    1520 ;268: 	// количество изменений частоты


                    1521 ;269: 	freqCount = OscReadFileContext_getFreqCount(readFileContext);


                    1522 

00001058 e1a00009   1523 	mov	r0,r9

0000105c eb000000*  1524 	bl	OscReadFileContext_getFreqCount

00001060 e1a04000   1525 	mov	r4,r0

                    1526 ;270: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "%d\r\n", freqCount);


                    1527 

00001064 e1a03004   1528 	mov	r3,r4

00001068 e1a02006   1529 	mov	r2,r6

0000106c e1a00005   1530 	mov	r0,r5

00001070 e3a01e40   1531 	mov	r1,1<<10

00001074 eb000000*  1532 	bl	snprintf

                    1533 ;271: 	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;


                    1534 

00001078 e1a01005   1535 	mov	r1,r5

0000107c e1a02000   1536 	mov	r2,r0

00001080 e1a00007   1537 	mov	r0,r7

00001084 eb000000*  1538 	bl	OscWriteBuffer_write

00001088 e3500000   1539 	cmp	r0,0

0000108c 0a00012d   1540 	beq	.L1163

                    1541 ;272: 


                    1542 ;273: 	// изменения частоты


                    1543 ;274: 	for (freqNum = 0; freqNum < freqCount; ++freqNum)


                    1544 

00001090 e3a06000   1545 	mov	r6,0

00001094 e3540000   1546 	cmp	r4,0

00001098 a1a00004   1547 	movge	r0,r4

0000109c b3a00000   1548 	movlt	r0,0

000010a0 e58d0060   1549 	str	r0,[sp,96]

000010a4 e1b081a0   1550 	movs	r8,r0 lsr 3

000010a8 0a0000ab   1551 	beq	.L1390

                    1552 .L1391:

000010ac e1a01006   1553 	mov	r1,r6

000010b0 e1a00009   1554 	mov	r0,r9

000010b4 eb000000*  1555 	bl	OscReadFileContext_getFreqCfg

000010b8 e1b04000   1556 	movs	r4,r0

000010bc 0a000121   1557 	beq	.L1163

000010c0 e5940000   1558 	ldr	r0,[r4]

000010c4 e59fb3d4*  1559 	ldr	fp,.L2457

000010c8 eb000000*  1560 	bl	__ftod

000010cc e5942004   1561 	ldr	r2,[r4,4]

000010d0 e24dd004   1562 	sub	sp,sp,4

000010d4 e88d0007   1563 	stmea	[sp],{r0-r2}

000010d8 e8bd0008   1564 	ldmfd	[sp]!,{r3}

000010dc e1a0200b   1565 	mov	r2,fp

000010e0 e1a00005   1566 	mov	r0,r5

000010e4 e3a01e40   1567 	mov	r1,1<<10

000010e8 eb000000*  1568 	bl	snprintf

000010ec e1a01005   1569 	mov	r1,r5

000010f0 e1a02000   1570 	mov	r2,r0

000010f4 e1a00007   1571 	mov	r0,r7

000010f8 eb000000*  1572 	bl	OscWriteBuffer_write

000010fc e3500000   1573 	cmp	r0,0

00001100 0a000110   1574 	beq	.L1163

00001104 e2861001   1575 	add	r1,r6,1


                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
00001108 e1a00009   1576 	mov	r0,r9

0000110c eb000000*  1577 	bl	OscReadFileContext_getFreqCfg

00001110 e1b04000   1578 	movs	r4,r0

00001114 0a00010b   1579 	beq	.L1163

00001118 e5940000   1580 	ldr	r0,[r4]

0000111c eb000000*  1581 	bl	__ftod

00001120 e5942004   1582 	ldr	r2,[r4,4]

00001124 e24dd004   1583 	sub	sp,sp,4

00001128 e88d0007   1584 	stmea	[sp],{r0-r2}

0000112c e8bd0008   1585 	ldmfd	[sp]!,{r3}

00001130 e1a0200b   1586 	mov	r2,fp

00001134 e1a00005   1587 	mov	r0,r5

00001138 e3a01e40   1588 	mov	r1,1<<10

0000113c eb000000*  1589 	bl	snprintf

00001140 e1a01005   1590 	mov	r1,r5

00001144 e1a02000   1591 	mov	r2,r0

00001148 e1a00007   1592 	mov	r0,r7

0000114c eb000000*  1593 	bl	OscWriteBuffer_write

00001150 e3500000   1594 	cmp	r0,0

00001154 0a0000fb   1595 	beq	.L1163

00001158 e2861002   1596 	add	r1,r6,2

0000115c e1a00009   1597 	mov	r0,r9

00001160 eb000000*  1598 	bl	OscReadFileContext_getFreqCfg

00001164 e1b04000   1599 	movs	r4,r0

00001168 0a0000f6   1600 	beq	.L1163

0000116c e5940000   1601 	ldr	r0,[r4]

00001170 eb000000*  1602 	bl	__ftod

00001174 e5942004   1603 	ldr	r2,[r4,4]

00001178 e24dd004   1604 	sub	sp,sp,4

0000117c e88d0007   1605 	stmea	[sp],{r0-r2}

00001180 e8bd0008   1606 	ldmfd	[sp]!,{r3}

00001184 e1a0200b   1607 	mov	r2,fp

00001188 e1a00005   1608 	mov	r0,r5

0000118c e3a01e40   1609 	mov	r1,1<<10

00001190 eb000000*  1610 	bl	snprintf

00001194 e1a01005   1611 	mov	r1,r5

00001198 e1a02000   1612 	mov	r2,r0

0000119c e1a00007   1613 	mov	r0,r7

000011a0 eb000000*  1614 	bl	OscWriteBuffer_write

000011a4 e3500000   1615 	cmp	r0,0

000011a8 0a0000e6   1616 	beq	.L1163

000011ac e2861003   1617 	add	r1,r6,3

000011b0 e1a00009   1618 	mov	r0,r9

000011b4 eb000000*  1619 	bl	OscReadFileContext_getFreqCfg

000011b8 e1b04000   1620 	movs	r4,r0

000011bc 0a0000e1   1621 	beq	.L1163

000011c0 e5940000   1622 	ldr	r0,[r4]

000011c4 eb000000*  1623 	bl	__ftod

000011c8 e5942004   1624 	ldr	r2,[r4,4]

000011cc e24dd004   1625 	sub	sp,sp,4

000011d0 e88d0007   1626 	stmea	[sp],{r0-r2}

000011d4 e8bd0008   1627 	ldmfd	[sp]!,{r3}

000011d8 e1a0200b   1628 	mov	r2,fp

000011dc e1a00005   1629 	mov	r0,r5

000011e0 e3a01e40   1630 	mov	r1,1<<10

000011e4 eb000000*  1631 	bl	snprintf

000011e8 e1a01005   1632 	mov	r1,r5

000011ec e1a02000   1633 	mov	r2,r0

000011f0 e1a00007   1634 	mov	r0,r7

000011f4 eb000000*  1635 	bl	OscWriteBuffer_write

000011f8 e3500000   1636 	cmp	r0,0


                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
000011fc 0a0000d1   1637 	beq	.L1163

00001200 e2861004   1638 	add	r1,r6,4

00001204 e1a00009   1639 	mov	r0,r9

00001208 eb000000*  1640 	bl	OscReadFileContext_getFreqCfg

0000120c e1b04000   1641 	movs	r4,r0

00001210 0a0000cc   1642 	beq	.L1163

00001214 e5940000   1643 	ldr	r0,[r4]

00001218 eb000000*  1644 	bl	__ftod

0000121c e5942004   1645 	ldr	r2,[r4,4]

00001220 e24dd004   1646 	sub	sp,sp,4

00001224 e88d0007   1647 	stmea	[sp],{r0-r2}

00001228 e8bd0008   1648 	ldmfd	[sp]!,{r3}

0000122c e1a0200b   1649 	mov	r2,fp

00001230 e1a00005   1650 	mov	r0,r5

00001234 e3a01e40   1651 	mov	r1,1<<10

00001238 eb000000*  1652 	bl	snprintf

0000123c e1a01005   1653 	mov	r1,r5

00001240 e1a02000   1654 	mov	r2,r0

00001244 e1a00007   1655 	mov	r0,r7

00001248 eb000000*  1656 	bl	OscWriteBuffer_write

0000124c e3500000   1657 	cmp	r0,0

00001250 0a0000bc   1658 	beq	.L1163

00001254 e2861005   1659 	add	r1,r6,5

00001258 e1a00009   1660 	mov	r0,r9

0000125c eb000000*  1661 	bl	OscReadFileContext_getFreqCfg

00001260 e1b04000   1662 	movs	r4,r0

00001264 0a0000b7   1663 	beq	.L1163

00001268 e5940000   1664 	ldr	r0,[r4]

0000126c eb000000*  1665 	bl	__ftod

00001270 e5942004   1666 	ldr	r2,[r4,4]

00001274 e24dd004   1667 	sub	sp,sp,4

00001278 e88d0007   1668 	stmea	[sp],{r0-r2}

0000127c e8bd0008   1669 	ldmfd	[sp]!,{r3}

00001280 e1a0200b   1670 	mov	r2,fp

00001284 e1a00005   1671 	mov	r0,r5

00001288 e3a01e40   1672 	mov	r1,1<<10

0000128c eb000000*  1673 	bl	snprintf

00001290 e1a01005   1674 	mov	r1,r5

00001294 e1a02000   1675 	mov	r2,r0

00001298 e1a00007   1676 	mov	r0,r7

0000129c eb000000*  1677 	bl	OscWriteBuffer_write

000012a0 e3500000   1678 	cmp	r0,0

000012a4 0a0000a7   1679 	beq	.L1163

000012a8 e2861006   1680 	add	r1,r6,6

000012ac e1a00009   1681 	mov	r0,r9

000012b0 eb000000*  1682 	bl	OscReadFileContext_getFreqCfg

000012b4 e1b04000   1683 	movs	r4,r0

000012b8 0a0000a2   1684 	beq	.L1163

000012bc e5940000   1685 	ldr	r0,[r4]

000012c0 eb000000*  1686 	bl	__ftod

000012c4 e5942004   1687 	ldr	r2,[r4,4]

000012c8 e24dd004   1688 	sub	sp,sp,4

000012cc e88d0007   1689 	stmea	[sp],{r0-r2}

000012d0 e8bd0008   1690 	ldmfd	[sp]!,{r3}

000012d4 e1a0200b   1691 	mov	r2,fp

000012d8 e1a00005   1692 	mov	r0,r5

000012dc e3a01e40   1693 	mov	r1,1<<10

000012e0 eb000000*  1694 	bl	snprintf

000012e4 e1a01005   1695 	mov	r1,r5

000012e8 e1a02000   1696 	mov	r2,r0

000012ec e1a00007   1697 	mov	r0,r7


                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
000012f0 eb000000*  1698 	bl	OscWriteBuffer_write

000012f4 e3500000   1699 	cmp	r0,0

000012f8 0a000092   1700 	beq	.L1163

000012fc e2861007   1701 	add	r1,r6,7

00001300 e1a00009   1702 	mov	r0,r9

00001304 eb000000*  1703 	bl	OscReadFileContext_getFreqCfg

00001308 e1b04000   1704 	movs	r4,r0

0000130c 0a00008d   1705 	beq	.L1163

00001310 e5940000   1706 	ldr	r0,[r4]

00001314 eb000000*  1707 	bl	__ftod

00001318 e5942004   1708 	ldr	r2,[r4,4]

0000131c e24dd004   1709 	sub	sp,sp,4

00001320 e88d0007   1710 	stmea	[sp],{r0-r2}

00001324 e8bd0008   1711 	ldmfd	[sp]!,{r3}

00001328 e1a0200b   1712 	mov	r2,fp

0000132c e1a00005   1713 	mov	r0,r5

00001330 e3a01e40   1714 	mov	r1,1<<10

00001334 eb000000*  1715 	bl	snprintf

00001338 e1a01005   1716 	mov	r1,r5

0000133c e1a02000   1717 	mov	r2,r0

00001340 e1a00007   1718 	mov	r0,r7

00001344 eb000000*  1719 	bl	OscWriteBuffer_write

00001348 e3500000   1720 	cmp	r0,0

0000134c 0a00007d   1721 	beq	.L1163

00001350 e2866008   1722 	add	r6,r6,8

00001354 e2588001   1723 	subs	r8,r8,1

00001358 1affff53   1724 	bne	.L1391

                    1725 .L1390:

0000135c e59d0060   1726 	ldr	r0,[sp,96]

00001360 e2108007   1727 	ands	r8,r0,7

00001364 0a000017   1728 	beq	.L1146

                    1729 .L1425:

00001368 e1a01006   1730 	mov	r1,r6

0000136c e1a00009   1731 	mov	r0,r9

00001370 eb000000*  1732 	bl	OscReadFileContext_getFreqCfg

00001374 e1b04000   1733 	movs	r4,r0

00001378 0a000072   1734 	beq	.L1163

0000137c e5940000   1735 	ldr	r0,[r4]

00001380 eb000000*  1736 	bl	__ftod

00001384 e5942004   1737 	ldr	r2,[r4,4]

00001388 e24dd004   1738 	sub	sp,sp,4

0000138c e88d0007   1739 	stmea	[sp],{r0-r2}

00001390 e8bd0008   1740 	ldmfd	[sp]!,{r3}

00001394 e59f2104*  1741 	ldr	r2,.L2457

00001398 e1a00005   1742 	mov	r0,r5

0000139c e3a01e40   1743 	mov	r1,1<<10

000013a0 eb000000*  1744 	bl	snprintf

000013a4 e1a01005   1745 	mov	r1,r5

000013a8 e1a02000   1746 	mov	r2,r0

000013ac e1a00007   1747 	mov	r0,r7

000013b0 eb000000*  1748 	bl	OscWriteBuffer_write

000013b4 e3500000   1749 	cmp	r0,0

000013b8 0a000062   1750 	beq	.L1163

000013bc e2866001   1751 	add	r6,r6,1

000013c0 e2588001   1752 	subs	r8,r8,1

000013c4 1affffe7   1753 	bne	.L1425

                    1754 .L1146:

                    1755 ;284: 	}


                    1756 ;285: 	return true;


                    1757 

                    1758 ;333: 	{



                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
                    1759 

                    1760 ;334: 		return false;


                    1761 

                    1762 ;335: 	}


                    1763 ;336: 	// время начала предыстории и время осцилограммы


                    1764 ;337: 	if (!writeOscTimeToCfg(readFileContext))


                    1765 

                    1766 ;170: {


                    1767 

                    1768 ;171: 	OSCInfoStruct *oscInfo = readFileContext->oscInfo;


                    1769 

000013c8 e599402c   1770 	ldr	r4,[r9,44]

                    1771 ;172: 	


                    1772 ;173: 	__time32_t oscTime = OSCInfo_getUTCDate(oscInfo);


                    1773 

000013cc e1a00004   1774 	mov	r0,r4

000013d0 eb000000*  1775 	bl	OSCInfo_getUTCDate

000013d4 e58d0060   1776 	str	r0,[sp,96]

000013d8 e1a08000   1777 	mov	r8,r0

                    1778 ;174: 	int oscTimeMS = OSCInfo_getDateMS(oscInfo);


                    1779 

000013dc e1a00004   1780 	mov	r0,r4

000013e0 eb000000*  1781 	bl	OSCInfo_getDateMS

000013e4 e599402c   1782 	ldr	r4,[r9,44]

                    1783 ;112: 	__time32_t oscTime = OSCInfo_getUTCDate(oscInfo);


                    1784 

000013e8 e1a0b000   1785 	mov	fp,r0

                    1786 ;175: 	int phistTimeMS;


                    1787 ;176: 	__time32_t phistTime;


                    1788 ;177: 


                    1789 ;178: 	getPhistTime(readFileContext, &phistTime, &phistTimeMS);


                    1790 

                    1791 ;109: 	__time32_t *t, int *ms)


                    1792 ;110: {


                    1793 

                    1794 ;111: 	OSCInfoStruct *oscInfo = readFileContext->oscInfo;


                    1795 

000013ec e1a00004   1796 	mov	r0,r4

000013f0 eb000000*  1797 	bl	OSCInfo_getUTCDate

000013f4 e1a05000   1798 	mov	r5,r0

                    1799 ;113: 	int oscTimeMS = OSCInfo_getDateMS(oscInfo);


                    1800 

000013f8 e1a00004   1801 	mov	r0,r4

000013fc eb000000*  1802 	bl	OSCInfo_getDateMS

                    1803 ;114: 	int64_t tmp;


                    1804 ;115: 


                    1805 ;116: 	// время осцилограммы в миллисекундах


                    1806 ;117: 	tmp = oscTime;


                    1807 

00001400 e3a0affa   1808 	mov	r10,0x03e8

00001404 e08c359a   1809 	umull	r3,r12,r10,r5

00001408 e1a02fc5   1810 	mov	r2,r5 asr 31

                    1811 ;118: 	tmp *= 1000;


                    1812 

0000140c e0424102   1813 	sub	r4,r2,r2 lsl 2

00001410 e0844382   1814 	add	r4,r4,r2 lsl 7

00001414 e08c3184   1815 	add	r3,r12,r4 lsl 3

00001418 e0451105   1816 	sub	r1,r5,r5 lsl 2

0000141c e0811385   1817 	add	r1,r1,r5 lsl 7

                    1818 ;119: 	tmp += oscTimeMS;


                    1819 


                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
00001420 e0904181   1820 	adds	r4,r0,r1 lsl 3

00001424 e0a35fc0   1821 	adc	r5,r3,r0 asr 31

                    1822 ;120: 


                    1823 ;121: 	// время предыстории в миллисекундах


                    1824 ;122: 	tmp -= OscReadFileContext_getPhistotyTimeMS(readFileContext);


                    1825 

00001428 e1a00009   1826 	mov	r0,r9

0000142c eb000000*  1827 	bl	OscReadFileContext_getPhistotyTimeMS

00001430 e0546000   1828 	subs	r6,r4,r0

00001434 e0c57fc0   1829 	sbc	r7,r5,r0 asr 31

                    1830 ;123: 	// в секундах


                    1831 ;124: 	*t = (__time32_t)(tmp / 1000);


                    1832 

00001438 e1a00006   1833 	mov	r0,r6

0000143c e1a01007   1834 	mov	r1,r7

00001440 e1a0200a   1835 	mov	r2,r10

00001444 e3a03000   1836 	mov	r3,0

00001448 eb000000*  1837 	bl	__gh_div64

0000144c e1a04000   1838 	mov	r4,r0

                    1839 ;125: 	*ms = tmp % 1000;


                    1840 

00001450 e1a00006   1841 	mov	r0,r6

00001454 e1a01007   1842 	mov	r1,r7

00001458 e1a0200a   1843 	mov	r2,r10

0000145c e3a03000   1844 	mov	r3,0

00001460 eb000000*  1845 	bl	__gh_rem64

                    1846 ;179: 	if (!writeFormatTimeToCfgBuffer(readFileContext, phistTime, phistTimeMS))


                    1847 

00001464 e1a01004   1848 	mov	r1,r4

00001468 e1a02000   1849 	mov	r2,r0

0000146c e1a00009   1850 	mov	r0,r9

00001470 ebfffc8b*  1851 	bl	writeFormatTimeToCfgBuffer

00001474 e3500000   1852 	cmp	r0,0

00001478 0a000032   1853 	beq	.L1163

                    1854 ;180: 	{


                    1855 

                    1856 ;181: 		return false;


                    1857 

                    1858 ;182: 	}


                    1859 ;183: 	if (!writeFormatTimeToCfgBuffer(readFileContext, oscTime, oscTimeMS))


                    1860 

0000147c e1a0200b   1861 	mov	r2,fp

00001480 e1a01008   1862 	mov	r1,r8

00001484 e1a00009   1863 	mov	r0,r9

00001488 ebfffc85*  1864 	bl	writeFormatTimeToCfgBuffer

0000148c e3500000   1865 	cmp	r0,0

00001490 0a00002c   1866 	beq	.L1163

                    1867 ;184: 	{


                    1868 

                    1869 ;185: 		return false;


                    1870 

                    1871 ;186: 	}


                    1872 ;187: 


                    1873 ;188: 


                    1874 ;189: 	return true;


                    1875 

                    1876 ;338: 	{


                    1877 

                    1878 ;339: 		return false;


                    1879 

                    1880 ;340: 	}



                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
                    1881 ;341: 


                    1882 ;342: 	// ASCII согласно формату


                    1883 ;343: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "ASCII\r\n");


                    1884 

00001494 ea000002   1885 	b	.L2458

                    1886 	.align	4

                    1887 .L2455:

00001498 00000000*  1888 	.data.w	.L2255

                    1889 	.type	.L2455,$object

                    1890 	.size	.L2455,4

                    1891 

                    1892 .L2456:

0000149c 00000000*  1893 	.data.w	.L2253

                    1894 	.type	.L2456,$object

                    1895 	.size	.L2456,4

                    1896 

                    1897 .L2457:

000014a0 00000000*  1898 	.data.w	.L2251

                    1899 	.type	.L2457,$object

                    1900 	.size	.L2457,4

                    1901 

                    1902 .L2458:

                    1903 

000014a4 e28f2000*  1904 	adr	r2,.L2459

000014a8 e59d0058   1905 	ldr	r0,[sp,88]

000014ac e3a01e40   1906 	mov	r1,1<<10

000014b0 eb000000*  1907 	bl	snprintf

                    1908 ;344: 	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;


                    1909 

000014b4 e1a02000   1910 	mov	r2,r0

000014b8 e59d005c   1911 	ldr	r0,[sp,92]

000014bc e59d1058   1912 	ldr	r1,[sp,88]

000014c0 eb000000*  1913 	bl	OscWriteBuffer_write

000014c4 e3500000   1914 	cmp	r0,0

000014c8 0a00001e   1915 	beq	.L1163

                    1916 ;345: 


                    1917 ;346: 	// Следующие значения вписываются напрямую


                    1918 ;347: 	// в дальнейшем следует переделать для большего соответствия формату


                    1919 ;348: 


                    1920 ;349: 	// коэффициент умножения временной метки


                    1921 ;350: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "1\r\n");


                    1922 

000014cc e28f2000*  1923 	adr	r2,.L2460

000014d0 e59d0058   1924 	ldr	r0,[sp,88]

000014d4 e3a01e40   1925 	mov	r1,1<<10

000014d8 eb000000*  1926 	bl	snprintf

                    1927 ;351: 	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;


                    1928 

000014dc e1a02000   1929 	mov	r2,r0

000014e0 e59d005c   1930 	ldr	r0,[sp,92]

000014e4 e59d1058   1931 	ldr	r1,[sp,88]

000014e8 eb000000*  1932 	bl	OscWriteBuffer_write

000014ec e3500000   1933 	cmp	r0,0

000014f0 0a000014   1934 	beq	.L1163

                    1935 ;352: 


                    1936 ;353: 	// Разница между локальным временем и UTC


                    1937 ;354: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "0,0\r\n");


                    1938 

000014f4 e59f40f0*  1939 	ldr	r4,.L2461

000014f8 e59d0058   1940 	ldr	r0,[sp,88]

000014fc e1a02004   1941 	mov	r2,r4


                                                                      Page 33
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
00001500 e3a01e40   1942 	mov	r1,1<<10

00001504 eb000000*  1943 	bl	snprintf

                    1944 ;355: 	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;


                    1945 

00001508 e1a02000   1946 	mov	r2,r0

0000150c e59d1058   1947 	ldr	r1,[sp,88]

00001510 e59d005c   1948 	ldr	r0,[sp,92]

00001514 eb000000*  1949 	bl	OscWriteBuffer_write

00001518 e3500000   1950 	cmp	r0,0

0000151c 0a000009   1951 	beq	.L1163

                    1952 ;356: 	


                    1953 ;357: 	// Качество времени


                    1954 ;358: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "0,0\r\n");


                    1955 

00001520 e1a02004   1956 	mov	r2,r4

00001524 e59d0058   1957 	ldr	r0,[sp,88]

00001528 e3a01e40   1958 	mov	r1,1<<10

0000152c eb000000*  1959 	bl	snprintf

                    1960 ;359: 	if (!OscWriteBuffer_write(cfgBuffer, formatBuffer, size)) return false;


                    1961 

00001530 e1a02000   1962 	mov	r2,r0

00001534 e59d005c   1963 	ldr	r0,[sp,92]

00001538 e59d1058   1964 	ldr	r1,[sp,88]

0000153c eb000000*  1965 	bl	OscWriteBuffer_write

00001540 e3500000   1966 	cmp	r0,0

                    1967 ;360: 


                    1968 ;361: 	return true;


                    1969 

00001544 13a00001   1970 	movne	r0,1

                    1971 .L1163:

00001548 03a00000   1972 	moveq	r0,0

                    1973 .L1089:

0000154c e28dd078   1974 	add	sp,sp,120

00001550 e8bd8ff0   1975 	ldmfd	[sp]!,{r4-fp,pc}

                    1976 	.endf	OscConverter_processCfg

                    1977 	.align	4

                    1978 ;formatBuffer	[sp,88]	local

                    1979 ;cfgBuffer	[sp,92]	local

                    1980 ;oscInfo	r5	local

                    1981 ;analogCount	r4	local

                    1982 ;boolCount	r5	local

                    1983 ;.L2246	.L2261	static

                    1984 ;.L2247	.L2262	static

                    1985 ;oscInfo	r5	local

                    1986 ;cfgBuffer	[sp,80]	local

                    1987 ;formatBuffer	fp	local

                    1988 ;analogNum	r4	local

                    1989 ;pAnalog	r6	local

                    1990 ;max	[sp,64]	local

                    1991 ;min	[sp,68]	local

                    1992 ;oscInfo	r6	local

                    1993 ;cfgBuffer	fp	local

                    1994 ;formatBuffer	r5	local

                    1995 ;boolNum	r4	local

                    1996 ;cfgBuffer	r7	local

                    1997 ;formatBuffer	r5	local

                    1998 ;freqCount	r4	local

                    1999 ;freqNum	r6	local

                    2000 ;freqCfg	r4	local

                    2001 ;oscInfo	r4	local

                    2002 ;oscTime	[sp,96]	local


                                                                      Page 34
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
                    2003 ;oscTimeMS	fp	local

                    2004 ;phistTime	r4	local

                    2005 ;oscInfo	r4	local

                    2006 ;oscTime	r5	local

                    2007 ;tmp	r1	local

                    2008 ;.L2248	.L2258	static

                    2009 ;.L2249	.L2259	static

                    2010 ;.L2250	.L2260	static

                    2011 ;cft	[sp,112]	local

                    2012 

                    2013 ;readFileContext	r9	param

                    2014 

                    2015 	.section ".bss","awb"

                    2016 .L2245:

                    2017 	.section ".rodata","a"

                    2018 .L2251:

                    2019 __UNNAMED_2_static_in_writeFrequencyToCfg:;	"%f,%d\r\n\000"

00000004 252c6625   2020 	.data.b	37,102,44,37

00000008 000a0d64   2021 	.data.b	100,13,10,0

                    2022 	.type	__UNNAMED_2_static_in_writeFrequencyToCfg,$object

                    2023 	.size	__UNNAMED_2_static_in_writeFrequencyToCfg,8

                    2024 .L2252:

                    2025 __UNNAMED_2_static_in_writeAnalogToCfg:;	"\000"

0000000c 00        2026 	.data.b	0

                    2027 	.type	__UNNAMED_2_static_in_writeAnalogToCfg,$object

                    2028 	.size	__UNNAMED_2_static_in_writeAnalogToCfg,1

0000000d 000000    2029 	.space	3

                    2030 .L2253:

                    2031 __UNNAMED_1_static_in_writeFrequencyToCfg:;	"%d\r\n\000"

00000010 0a0d6425   2032 	.data.b	37,100,13,10

00000014 00        2033 	.data.b	0

00000015 000000    2034 	.space	3

                    2035 	.type	__UNNAMED_1_static_in_writeFrequencyToCfg,$object

                    2036 	.size	__UNNAMED_1_static_in_writeFrequencyToCfg,8

                    2037 .L2254:

                    2038 __UNNAMED_1_static_in_writeAnalogToCfg:;	"%d,%s,%s,%s,%s,%e,%e,%d,%d,%d,%e,%e,%c\r\n\000"

00000018 252c6425   2039 	.data.b	37,100,44,37

0000001c 73252c73   2040 	.data.b	115,44,37,115

00000020 2c73252c   2041 	.data.b	44,37,115,44

00000024 252c7325   2042 	.data.b	37,115,44,37

00000028 65252c65   2043 	.data.b	101,44,37,101

0000002c 2c64252c   2044 	.data.b	44,37,100,44

00000030 252c6425   2045 	.data.b	37,100,44,37

00000034 65252c64   2046 	.data.b	100,44,37,101

00000038 2c65252c   2047 	.data.b	44,37,101,44

0000003c 0a0d6325   2048 	.data.b	37,99,13,10

00000040 00        2049 	.data.b	0

00000041 000000    2050 	.space	3

                    2051 	.type	__UNNAMED_1_static_in_writeAnalogToCfg,$object

                    2052 	.size	__UNNAMED_1_static_in_writeAnalogToCfg,44

                    2053 .L2255:

                    2054 __UNNAMED_1_static_in_writeBoolToCfg:;	"%d,%s,%d\r\n\000"

00000044 252c6425   2055 	.data.b	37,100,44,37

00000048 64252c73   2056 	.data.b	115,44,37,100

0000004c 0a0d      2057 	.data.b	13,10

0000004e 00        2058 	.data.b	0

0000004f 00        2059 	.space	1

                    2060 	.type	__UNNAMED_1_static_in_writeBoolToCfg,$object

                    2061 	.size	__UNNAMED_1_static_in_writeBoolToCfg,12

                    2062 .L2260:;	"0,0\r\n\000"

00000050 0d302c30   2063 	.data.b	48,44,48,13


                                                                      Page 35
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
00000054 000a      2064 	.data.b	10,0

00000056 0000      2065 	.space	2

                    2066 	.type	.L2260,$object

                    2067 	.size	.L2260,8

                    2068 	.data

                    2069 	.text

                    2070 

                    2071 ;362: }


                    2072 

                    2073 ;363: 


                    2074 ;364: bool OscConverter_processHdr(OscReadFileContext * readFileContext)


                    2075 	.align	4

                    2076 	.align	4

                    2077 OscConverter_processHdr::

00001554 e92d40f0   2078 	stmfd	[sp]!,{r4-r7,lr}

                    2079 ;365: {


                    2080 

                    2081 ;366: 	// сюдя можно что-нибудь дописать


                    2082 ;367: 	char *comment = "";


                    2083 

                    2084 ;368: 	char *text = "";


                    2085 

                    2086 ;369: 


                    2087 ;370: 	char *formatBuffer = readFileContext->formatBuffer;


                    2088 

00001558 e2804050   2089 	add	r4,r0,80

                    2090 ;371: 	OscWriteBuffer *hdrBuffer = &readFileContext->hdrBuffer;


                    2091 

0000155c e2801e40   2092 	add	r1,r0,1<<10

00001560 e590002c   2093 	ldr	r0,[r0,44]

                    2094 ;375: 	if (!oscInfo)


                    2095 

00001564 e2815078   2096 	add	r5,r1,120

                    2097 ;372: 	OSCInfoStruct *oscInfo;


                    2098 ;373: 	int size;


                    2099 ;374: 	oscInfo = readFileContext->oscInfo;


                    2100 

00001568 e3500000   2101 	cmp	r0,0

0000156c 0a000017   2102 	beq	.L2471

                    2103 ;376: 	{


                    2104 

                    2105 ;377: 		return false;


                    2106 

                    2107 ;378: 	}


                    2108 ;379: 


                    2109 ;380: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "%s\r\n",text);


                    2110 

00001570 e59f6078*  2111 	ldr	r6,.L2558

00001574 e59f7078*  2112 	ldr	r7,.L2559

00001578 e1a03006   2113 	mov	r3,r6

0000157c e1a02007   2114 	mov	r2,r7

00001580 e1a00004   2115 	mov	r0,r4

00001584 e3a01e40   2116 	mov	r1,1<<10

00001588 eb000000*  2117 	bl	snprintf

                    2118 ;381: 	if (!OscWriteBuffer_write(hdrBuffer, formatBuffer, size)) return false;


                    2119 

0000158c e1a01004   2120 	mov	r1,r4

00001590 e1a02000   2121 	mov	r2,r0

00001594 e1a00005   2122 	mov	r0,r5

00001598 eb000000*  2123 	bl	OscWriteBuffer_write

0000159c e3500000   2124 	cmp	r0,0


                                                                      Page 36
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
000015a0 0a00000a   2125 	beq	.L2471

                    2126 ;382: 


                    2127 ;383: 	size = snprintf(formatBuffer, FORMAT_BUFFER_SIZE, "%s\r\n",comment);


                    2128 

000015a4 e1a03006   2129 	mov	r3,r6

000015a8 e1a02007   2130 	mov	r2,r7

000015ac e1a00004   2131 	mov	r0,r4

000015b0 e3a01e40   2132 	mov	r1,1<<10

000015b4 eb000000*  2133 	bl	snprintf

                    2134 ;384: 	if (!OscWriteBuffer_write(hdrBuffer, formatBuffer, size)) return false;


                    2135 

000015b8 e1a01004   2136 	mov	r1,r4

000015bc e1a02000   2137 	mov	r2,r0

000015c0 e1a00005   2138 	mov	r0,r5

000015c4 eb000000*  2139 	bl	OscWriteBuffer_write

000015c8 e3500000   2140 	cmp	r0,0

                    2141 ;385: 


                    2142 ;386: 	return true;


                    2143 

000015cc 13a00001   2144 	movne	r0,1

                    2145 .L2471:

000015d0 03a00000   2146 	moveq	r0,0

                    2147 .L2462:

000015d4 e8bd80f0   2148 	ldmfd	[sp]!,{r4-r7,pc}

                    2149 	.endf	OscConverter_processHdr

                    2150 	.align	4

                    2151 ;.L2535	.L2539	static

                    2152 ;formatBuffer	r4	local

                    2153 ;hdrBuffer	r5	local

                    2154 ;oscInfo	r0	local

                    2155 ;.L2536	.L2540	static

                    2156 

                    2157 ;readFileContext	r0	param

                    2158 

                    2159 	.section ".bss","awb"

                    2160 .L2534:

                    2161 	.section ".rodata","a"

                    2162 .L2539:;	"\000"

00000058 00        2163 	.data.b	0

                    2164 	.type	.L2539,$object

                    2165 	.size	.L2539,1

00000059 000000    2166 	.space	3

                    2167 .L2540:;	"%s\r\n\000"

0000005c 0a0d7325   2168 	.data.b	37,115,13,10

00000060 00        2169 	.data.b	0

00000061 000000    2170 	.space	3

                    2171 	.type	.L2540,$object

                    2172 	.size	.L2540,8

                    2173 	.data

                    2174 	.text

                    2175 

                    2176 ;387: }


                    2177 	.align	4

                    2178 	.align	4

                    2179 OscConverter_init::

                    2180 ;10: {	


                    2181 

                    2182 ;11: 	return true;


                    2183 

000015d8 e3a00001   2184 	mov	r0,1

000015dc e12fff1e*  2185 	ret	


                                                                      Page 37
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8l01.s
                    2186 	.endf	OscConverter_init

                    2187 	.align	4

                    2188 

                    2189 	.section ".bss","awb"

                    2190 .L2574:

                    2191 	.data

                    2192 	.text

                    2193 	.align	4

                    2194 .L2459:

                    2195 ;	"ASCII\r\n\000"

000015e0 49435341   2196 	.data.b	65,83,67,73

000015e4 000a0d49   2197 	.data.b	73,13,10,0

                    2198 	.align 4

                    2199 

                    2200 	.type	.L2459,$object

                    2201 	.size	.L2459,4

                    2202 

                    2203 .L2460:

                    2204 ;	"1\r\n\000"

000015e8 000a0d31   2205 	.data.b	49,13,10,0

                    2206 	.align 4

                    2207 

                    2208 	.type	.L2460,$object

                    2209 	.size	.L2460,4

                    2210 

                    2211 .L2461:

000015ec 00000000*  2212 	.data.w	.L2260

                    2213 	.type	.L2461,$object

                    2214 	.size	.L2461,4

                    2215 

                    2216 .L2558:

000015f0 00000000*  2217 	.data.w	.L2539

                    2218 	.type	.L2558,$object

                    2219 	.size	.L2558,4

                    2220 

                    2221 .L2559:

000015f4 00000000*  2222 	.data.w	.L2540

                    2223 	.type	.L2559,$object

                    2224 	.size	.L2559,4

                    2225 

                    2226 	.align	4

                    2227 ;__UNNAMED_1_static_in_writeAnalogToCfg	.L2254	static

                    2228 ;__UNNAMED_2_static_in_writeAnalogToCfg	.L2252	static

                    2229 ;__UNNAMED_1_static_in_writeBoolToCfg	.L2255	static

                    2230 ;__UNNAMED_1_static_in_writeFrequencyToCfg	.L2253	static

                    2231 ;__UNNAMED_2_static_in_writeFrequencyToCfg	.L2251	static

                    2232 

                    2233 	.data

                    2234 	.need	__gh_long_long_printf

                    2235 	.need	__gh_float_printf

                    2236 	.ghsnote version,6

                    2237 	.ghsnote tools,3

                    2238 	.ghsnote options,0

                    2239 	.text

                    2240 	.align	4

                    2241 	.section ".rodata","a"

                    2242 	.align	4

                    2243 	.text

