                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_data.c -o gh_bdg1.o -list=mms_data.lst C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
Source File: mms_data.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile mms_data.c -o

                      11 ;		mms_data.o

                      12 ;Source File:   mms_data.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:05 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "mms_data.h"


                      23 ;2: 


                      24 ;3: #include "pwin_access.h"


                      25 ;4: #include "DataSlice.h"


                      26 ;5: #include "AsnEncoding.h"


                      27 ;6: #include "debug.h"


                      28 ;7: #include "iedmodel.h"


                      29 ;8: 


                      30 ;9: #include "IEDCompile/InnerAttributeTypes.h"


                      31 ;10: #include <types.h>


                      32 ;11: #include <string.h>


                      33 ;12: #include <stdint.h>


                      34 ;13: 


                      35 ;14: //Получение битов quality через сохранённые смещения в DataSlice


                      36 ;15: #define SET_QUALITY_BIT_FAST(offsName, bitNum) \


                      37 ;16:     if((accessInfo->offsName != -1)  \


                      38 ;17:         && DataSlice_getBoolFast(dataSliceWnd, accessInfo->offsName)) \


                      39 ;18:     { quality |= (1 << bitNum);}


                      40 ;19: 


                      41 ;20: uint16_t qualityFromBitsFast(void* dataSliceWnd, QualityAccsessInfo* accessInfo)


                      42 ;21: {


                      43 ;22:     uint16_t quality = 0;


                      44 ;23: 


                      45 ;24: 	SET_QUALITY_BIT_FAST(goodInvalidOffset, 7);


                      46 ;25: 	SET_QUALITY_BIT_FAST(reservedQuestionableOffset, 6);    


                      47 ;26:     SET_QUALITY_BIT_FAST(overflowOffset, 5);


                      48 ;27:     SET_QUALITY_BIT_FAST(outOfRangeOffset, 4);


                      49 ;28:     SET_QUALITY_BIT_FAST(badReferenceOffset, 3);


                      50 ;29:     SET_QUALITY_BIT_FAST(oscillatoryOffset, 2);



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                      51 ;30:     SET_QUALITY_BIT_FAST(failureOffset, 1);


                      52 ;31:     SET_QUALITY_BIT_FAST(oldDataOffset, 0);


                      53 ;32:     SET_QUALITY_BIT_FAST(inconsistentOffset,15);


                      54 ;33:     SET_QUALITY_BIT_FAST(inaccurateOffset, 14);


                      55 ;34:     SET_QUALITY_BIT_FAST(processSubstitutedOffset, 13);


                      56 ;35:     SET_QUALITY_BIT_FAST(testOffset, 12);


                      57 ;36:     SET_QUALITY_BIT_FAST(operatorBlockedOffset, 11);


                      58 ;37:     return quality;


                      59 ;38: }


                      60 ;39: 


                      61 ;40: float readFloatValue(FloatAccsessInfo* accessInfo)


                      62 ;41: {


                      63 ;42: 	return dataSliceGetFloatValue(accessInfo->valueOffset)  * accessInfo->multiplier;


                      64 ;43: }


                      65 ;44: 


                      66 ;45: 


                      67 ;46: //! проверяет на NAN


                      68 ;47: static __inline int isfnan(float value)


                      69 

                      70 ;51: }


                      71 

                      72 	.text

                      73 	.align	4

                      74 qualityFromBitsFast::

00000000 e92d4070     75 	stmfd	[sp]!,{r4-r6,lr}

00000004 e1a05000     76 	mov	r5,r0

00000008 e1a06001     77 	mov	r6,r1

0000000c e5961004     78 	ldr	r1,[r6,4]

00000010 e3a04000     79 	mov	r4,0

00000014 e3710001     80 	cmn	r1,1

00000018 0a000004     81 	beq	.L23

0000001c e1a01801     82 	mov	r1,r1 lsl 16

00000020 e1a01821     83 	mov	r1,r1 lsr 16

00000024 eb000000*    84 	bl	DataSlice_getBoolFast

00000028 e3500000     85 	cmp	r0,0

0000002c 13a04080     86 	movne	r4,128

                      87 .L23:

00000030 e5961008     88 	ldr	r1,[r6,8]

00000034 e3710001     89 	cmn	r1,1

00000038 0a000005     90 	beq	.L26

0000003c e1a01801     91 	mov	r1,r1 lsl 16

00000040 e1a01821     92 	mov	r1,r1 lsr 16

00000044 e1a00005     93 	mov	r0,r5

00000048 eb000000*    94 	bl	DataSlice_getBoolFast

0000004c e3500000     95 	cmp	r0,0

00000050 13844040     96 	orrne	r4,r4,64

                      97 .L26:

00000054 e596100c     98 	ldr	r1,[r6,12]

00000058 e3710001     99 	cmn	r1,1

0000005c 0a000005    100 	beq	.L29

00000060 e1a01801    101 	mov	r1,r1 lsl 16

00000064 e1a01821    102 	mov	r1,r1 lsr 16

00000068 e1a00005    103 	mov	r0,r5

0000006c eb000000*   104 	bl	DataSlice_getBoolFast

00000070 e3500000    105 	cmp	r0,0

00000074 13844020    106 	orrne	r4,r4,32

                     107 .L29:

00000078 e5961010    108 	ldr	r1,[r6,16]

0000007c e3710001    109 	cmn	r1,1

00000080 0a000005    110 	beq	.L32

00000084 e1a01801    111 	mov	r1,r1 lsl 16


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
00000088 e1a01821    112 	mov	r1,r1 lsr 16

0000008c e1a00005    113 	mov	r0,r5

00000090 eb000000*   114 	bl	DataSlice_getBoolFast

00000094 e3500000    115 	cmp	r0,0

00000098 13844010    116 	orrne	r4,r4,16

                     117 .L32:

0000009c e5961014    118 	ldr	r1,[r6,20]

000000a0 e3710001    119 	cmn	r1,1

000000a4 0a000005    120 	beq	.L35

000000a8 e1a01801    121 	mov	r1,r1 lsl 16

000000ac e1a01821    122 	mov	r1,r1 lsr 16

000000b0 e1a00005    123 	mov	r0,r5

000000b4 eb000000*   124 	bl	DataSlice_getBoolFast

000000b8 e3500000    125 	cmp	r0,0

000000bc 13844008    126 	orrne	r4,r4,8

                     127 .L35:

000000c0 e5961018    128 	ldr	r1,[r6,24]

000000c4 e3710001    129 	cmn	r1,1

000000c8 0a000005    130 	beq	.L38

000000cc e1a01801    131 	mov	r1,r1 lsl 16

000000d0 e1a01821    132 	mov	r1,r1 lsr 16

000000d4 e1a00005    133 	mov	r0,r5

000000d8 eb000000*   134 	bl	DataSlice_getBoolFast

000000dc e3500000    135 	cmp	r0,0

000000e0 13844004    136 	orrne	r4,r4,4

                     137 .L38:

000000e4 e596101c    138 	ldr	r1,[r6,28]

000000e8 e3710001    139 	cmn	r1,1

000000ec 0a000005    140 	beq	.L41

000000f0 e1a01801    141 	mov	r1,r1 lsl 16

000000f4 e1a01821    142 	mov	r1,r1 lsr 16

000000f8 e1a00005    143 	mov	r0,r5

000000fc eb000000*   144 	bl	DataSlice_getBoolFast

00000100 e3500000    145 	cmp	r0,0

00000104 13844002    146 	orrne	r4,r4,2

                     147 .L41:

00000108 e5961020    148 	ldr	r1,[r6,32]

0000010c e3710001    149 	cmn	r1,1

00000110 0a000005    150 	beq	.L44

00000114 e1a01801    151 	mov	r1,r1 lsl 16

00000118 e1a01821    152 	mov	r1,r1 lsr 16

0000011c e1a00005    153 	mov	r0,r5

00000120 eb000000*   154 	bl	DataSlice_getBoolFast

00000124 e3500000    155 	cmp	r0,0

00000128 13844001    156 	orrne	r4,r4,1

                     157 .L44:

0000012c e5961024    158 	ldr	r1,[r6,36]

00000130 e3710001    159 	cmn	r1,1

00000134 0a000005    160 	beq	.L47

00000138 e1a01801    161 	mov	r1,r1 lsl 16

0000013c e1a01821    162 	mov	r1,r1 lsr 16

00000140 e1a00005    163 	mov	r0,r5

00000144 eb000000*   164 	bl	DataSlice_getBoolFast

00000148 e3500000    165 	cmp	r0,0

0000014c 13844c80    166 	orrne	r4,r4,1<<15

                     167 .L47:

00000150 e5961028    168 	ldr	r1,[r6,40]

00000154 e3710001    169 	cmn	r1,1

00000158 0a000005    170 	beq	.L50

0000015c e1a01801    171 	mov	r1,r1 lsl 16

00000160 e1a01821    172 	mov	r1,r1 lsr 16


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
00000164 e1a00005    173 	mov	r0,r5

00000168 eb000000*   174 	bl	DataSlice_getBoolFast

0000016c e3500000    175 	cmp	r0,0

00000170 13844c40    176 	orrne	r4,r4,1<<14

                     177 .L50:

00000174 e596102c    178 	ldr	r1,[r6,44]

00000178 e3710001    179 	cmn	r1,1

0000017c 0a000005    180 	beq	.L53

00000180 e1a01801    181 	mov	r1,r1 lsl 16

00000184 e1a01821    182 	mov	r1,r1 lsr 16

00000188 e1a00005    183 	mov	r0,r5

0000018c eb000000*   184 	bl	DataSlice_getBoolFast

00000190 e3500000    185 	cmp	r0,0

00000194 13844d80    186 	orrne	r4,r4,1<<13

                     187 .L53:

00000198 e5961030    188 	ldr	r1,[r6,48]

0000019c e3710001    189 	cmn	r1,1

000001a0 0a000005    190 	beq	.L56

000001a4 e1a01801    191 	mov	r1,r1 lsl 16

000001a8 e1a01821    192 	mov	r1,r1 lsr 16

000001ac e1a00005    193 	mov	r0,r5

000001b0 eb000000*   194 	bl	DataSlice_getBoolFast

000001b4 e3500000    195 	cmp	r0,0

000001b8 13844d40    196 	orrne	r4,r4,1<<12

                     197 .L56:

000001bc e5961034    198 	ldr	r1,[r6,52]

000001c0 e3710001    199 	cmn	r1,1

000001c4 0a000005    200 	beq	.L59

000001c8 e1a01801    201 	mov	r1,r1 lsl 16

000001cc e1a01821    202 	mov	r1,r1 lsr 16

000001d0 e1a00005    203 	mov	r0,r5

000001d4 eb000000*   204 	bl	DataSlice_getBoolFast

000001d8 e3500000    205 	cmp	r0,0

000001dc 13844e80    206 	orrne	r4,r4,1<<11

                     207 .L59:

000001e0 e1a00004    208 	mov	r0,r4

000001e4 e8bd8070    209 	ldmfd	[sp]!,{r4-r6,pc}

                     210 	.endf	qualityFromBitsFast

                     211 	.align	4

                     212 ;quality	r4	local

                     213 

                     214 ;dataSliceWnd	r5	param

                     215 ;accessInfo	r6	param

                     216 

                     217 	.section ".bss","awb"

                     218 .L370:

                     219 	.data

                     220 	.text

                     221 

                     222 

                     223 	.align	4

                     224 	.align	4

                     225 readFloatValue::

000001e8 e92d4010    226 	stmfd	[sp]!,{r4,lr}

000001ec e1a04000    227 	mov	r4,r0

000001f0 e1d400b4    228 	ldrh	r0,[r4,4]

000001f4 eb000000*   229 	bl	dataSliceGetFloatValue

000001f8 eb000000*   230 	bl	__itof

000001fc e5941008    231 	ldr	r1,[r4,8]

00000200 e8bd4010    232 	ldmfd	[sp]!,{r4,lr}

00000204 ea000000*   233 	b	__fmul


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                     234 	.endf	readFloatValue

                     235 	.align	4

                     236 

                     237 ;accessInfo	r4	param

                     238 

                     239 	.section ".bss","awb"

                     240 .L510:

                     241 	.data

                     242 	.text

                     243 

                     244 

                     245 ;52: 


                     246 ;53: float readRealValue(FloatAccsessInfo* accessInfo)


                     247 	.align	4

                     248 	.align	4

                     249 readRealValue::

00000208 e92d4010    250 	stmfd	[sp]!,{r4,lr}

0000020c e24dd004    251 	sub	sp,sp,4

00000210 e1a04000    252 	mov	r4,r0

                     253 ;54: {


                     254 

                     255 ;55:     float result = dataSliceGetRealValue(accessInfo->valueOffset);


                     256 

00000214 e1d400b4    257 	ldrh	r0,[r4,4]

00000218 eb000000*   258 	bl	dataSliceGetRealValue

0000021c e1a01000    259 	mov	r1,r0

                     260 ;56: 


                     261 ;57:     if(isfnan(result))


                     262 

00000220 e58d1000    263 	str	r1,[sp]

                     264 ;48: {


                     265 

                     266 ;49:   unsigned int inan = *(unsigned int*)(void*)&value;


                     267 

                     268 ;50:   return (inan & 0x7F800000) == 0x7F800000;


                     269 

00000224 e3a025fe    270 	mov	r2,254<<22

00000228 e2822440    271 	add	r2,r2,1<<30

0000022c e0013002    272 	and	r3,r1,r2

00000230 e1530002    273 	cmp	r3,r2

                     274 ;58:     {


                     275 

                     276 ;59:         return result;


                     277 

                     278 ;60:     }


                     279 ;61: 


                     280 ;62:     return result  * accessInfo->multiplier;


                     281 

00000234 15941008    282 	ldrne	r1,[r4,8]

00000238 1b000000*   283 	blne	__fmul

0000023c e28dd004    284 	add	sp,sp,4

00000240 e8bd8010    285 	ldmfd	[sp]!,{r4,pc}

                     286 	.endf	readRealValue

                     287 	.align	4

                     288 ;result	r1	local

                     289 ;value	[sp]	local

                     290 

                     291 ;accessInfo	r4	param

                     292 

                     293 	.section ".bss","awb"

                     294 .L555:


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                     295 	.data

                     296 	.text

                     297 

                     298 ;63: }


                     299 

                     300 ;64: 


                     301 ;65: int encodeReadFloat(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)


                     302 	.align	4

                     303 	.align	4

                     304 encodeReadFloat::

00000244 e92d4030    305 	stmfd	[sp]!,{r4-r5,lr}

                     306 ;66: {


                     307 

                     308 ;67:     float value;    


                     309 ;68: 


                     310 ;69:     if(determineSize)


                     311 

00000248 e24dd008    312 	sub	sp,sp,8

0000024c e3530000    313 	cmp	r3,0

                     314 ;70:     {


                     315 

                     316 ;71:         int floatSize = 5;


                     317 

                     318 ;72:         return floatSize + 2; // 2 for tag and length


                     319 

00000250 13a00007    320 	movne	r0,7

00000254 1a000009    321 	bne	.L568

00000258 e1a05001    322 	mov	r5,r1

                     323 ;73:     }


                     324 ;74:    


                     325 ;75:     value = readFloatValue(descrStruct);


                     326 

0000025c e1a04000    327 	mov	r4,r0

00000260 e1a00002    328 	mov	r0,r2

00000264 ebffffdf*   329 	bl	readFloatValue

                     330 ;76: 


                     331 ;77:     return BerEncoder_EncodeFloatWithTL(0x87, value, 32, 8, outBuf, bufPos);


                     332 

00000268 e88d0030    333 	stmea	[sp],{r4-r5}

0000026c e3a03008    334 	mov	r3,8

00000270 e3a02020    335 	mov	r2,32

00000274 e1a01000    336 	mov	r1,r0

00000278 e3a00087    337 	mov	r0,135

0000027c eb000000*   338 	bl	BerEncoder_EncodeFloatWithTL

                     339 .L568:

00000280 e28dd008    340 	add	sp,sp,8

00000284 e8bd8030    341 	ldmfd	[sp]!,{r4-r5,pc}

                     342 	.endf	encodeReadFloat

                     343 	.align	4

                     344 

                     345 ;outBuf	r4	param

                     346 ;bufPos	r5	param

                     347 ;descrStruct	r2	param

                     348 ;determineSize	r3	param

                     349 

                     350 	.section ".bss","awb"

                     351 .L598:

                     352 	.data

                     353 	.text

                     354 

                     355 ;78: }



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                     356 

                     357 ;79: 


                     358 ;80: int encodeReadReal(uint8_t* outBuf, int bufPos, void* descrStruct,


                     359 	.align	4

                     360 	.align	4

                     361 encodeReadReal::

00000288 e92d4030    362 	stmfd	[sp]!,{r4-r5,lr}

                     363 ;81:                    bool determineSize)


                     364 ;82: {


                     365 

                     366 ;83:     float value;


                     367 ;84: 


                     368 ;85:     if(determineSize)


                     369 

0000028c e24dd008    370 	sub	sp,sp,8

00000290 e3530000    371 	cmp	r3,0

                     372 ;86:     {


                     373 

                     374 ;87:         int floatSize = 5;


                     375 

                     376 ;88:         return floatSize + 2; // 2 for tag and length


                     377 

00000294 13a00007    378 	movne	r0,7

00000298 1a000009    379 	bne	.L612

0000029c e1a05001    380 	mov	r5,r1

                     381 ;89:     }


                     382 ;90: 


                     383 ;91:     value = readRealValue(descrStruct);


                     384 

000002a0 e1a04000    385 	mov	r4,r0

000002a4 e1a00002    386 	mov	r0,r2

000002a8 ebffffd6*   387 	bl	readRealValue

                     388 ;92: 


                     389 ;93:     return BerEncoder_EncodeFloatWithTL(0x87, value, 32, 8, outBuf, bufPos);


                     390 

000002ac e88d0030    391 	stmea	[sp],{r4-r5}

000002b0 e3a03008    392 	mov	r3,8

000002b4 e3a02020    393 	mov	r2,32

000002b8 e1a01000    394 	mov	r1,r0

000002bc e3a00087    395 	mov	r0,135

000002c0 eb000000*   396 	bl	BerEncoder_EncodeFloatWithTL

                     397 .L612:

000002c4 e28dd008    398 	add	sp,sp,8

000002c8 e8bd8030    399 	ldmfd	[sp]!,{r4-r5,pc}

                     400 	.endf	encodeReadReal

                     401 	.align	4

                     402 

                     403 ;outBuf	r4	param

                     404 ;bufPos	r5	param

                     405 ;descrStruct	r2	param

                     406 ;determineSize	r3	param

                     407 

                     408 	.section ".bss","awb"

                     409 .L646:

                     410 	.data

                     411 	.text

                     412 

                     413 ;94: }


                     414 

                     415 ;95: 


                     416 ;96: int encodeReadFloatSett(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                     417 	.align	4

                     418 	.align	4

                     419 encodeReadFloatSett::

000002cc e92d4070    420 	stmfd	[sp]!,{r4-r6,lr}

                     421 ;97: {


                     422 

                     423 ;98:     float value;


                     424 ;99:     FloatAccsessInfo* accessInfo = descrStruct;


                     425 

                     426 ;100: 


                     427 ;101:     if(determineSize)


                     428 

000002d0 e24dd008    429 	sub	sp,sp,8

000002d4 e3530000    430 	cmp	r3,0

                     431 ;102:     {


                     432 

                     433 ;103:         int floatSize = 5;


                     434 

                     435 ;104:         return floatSize + 2; // 2 for tag and length


                     436 

000002d8 13a00007    437 	movne	r0,7

000002dc 1a00000d    438 	bne	.L660

000002e0 e1a05000    439 	mov	r5,r0

000002e4 e1a04002    440 	mov	r4,r2

                     441 ;105:     }


                     442 ;106: 


                     443 ;107:     value = getFloatSett(accessInfo->valueOffset) * accessInfo->multiplier;


                     444 

000002e8 e1d400b4    445 	ldrh	r0,[r4,4]

000002ec e1a06001    446 	mov	r6,r1

000002f0 eb000000*   447 	bl	getFloatSett

000002f4 eb000000*   448 	bl	__itof

000002f8 e5941008    449 	ldr	r1,[r4,8]

000002fc eb000000*   450 	bl	__fmul

                     451 ;108: 


                     452 ;109:     return BerEncoder_EncodeFloatWithTL(0x87, value, 32, 8, outBuf, bufPos);


                     453 

00000300 e88d0060    454 	stmea	[sp],{r5-r6}

00000304 e3a03008    455 	mov	r3,8

00000308 e3a02020    456 	mov	r2,32

0000030c e1a01000    457 	mov	r1,r0

00000310 e3a00087    458 	mov	r0,135

00000314 eb000000*   459 	bl	BerEncoder_EncodeFloatWithTL

                     460 .L660:

00000318 e28dd008    461 	add	sp,sp,8

0000031c e8bd8070    462 	ldmfd	[sp]!,{r4-r6,pc}

                     463 	.endf	encodeReadFloatSett

                     464 	.align	4

                     465 ;accessInfo	r4	local

                     466 

                     467 ;outBuf	r5	param

                     468 ;bufPos	r6	param

                     469 ;descrStruct	r2	param

                     470 ;determineSize	r3	param

                     471 

                     472 	.section ".bss","awb"

                     473 .L694:

                     474 	.data

                     475 	.text

                     476 

                     477 ;110: }



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                     478 

                     479 ;111: 


                     480 ;112: int encodeReadRealSett(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)


                     481 	.align	4

                     482 	.align	4

                     483 encodeReadRealSett::

00000320 e92d4070    484 	stmfd	[sp]!,{r4-r6,lr}

                     485 ;113: {


                     486 

                     487 ;114:     float value;


                     488 ;115:     FloatAccsessInfo* accessInfo = descrStruct;


                     489 

                     490 ;116: 


                     491 ;117:     if(determineSize)


                     492 

00000324 e24dd008    493 	sub	sp,sp,8

00000328 e3530000    494 	cmp	r3,0

                     495 ;118:     {


                     496 

                     497 ;119:         int floatSize = 5;


                     498 

                     499 ;120:         return floatSize + 2; // 2 for tag and length


                     500 

0000032c 13a00007    501 	movne	r0,7

00000330 1a00000c    502 	bne	.L708

00000334 e1a05000    503 	mov	r5,r0

00000338 e1a04002    504 	mov	r4,r2

                     505 ;121:     }


                     506 ;122: 


                     507 ;123:     value = getRealSett(accessInfo->valueOffset) * accessInfo->multiplier;


                     508 

0000033c e1d400b4    509 	ldrh	r0,[r4,4]

00000340 e1a06001    510 	mov	r6,r1

00000344 eb000000*   511 	bl	getRealSett

00000348 e5941008    512 	ldr	r1,[r4,8]

0000034c eb000000*   513 	bl	__fmul

                     514 ;124: 


                     515 ;125:     return BerEncoder_EncodeFloatWithTL(0x87, value, 32, 8, outBuf, bufPos);


                     516 

00000350 e88d0060    517 	stmea	[sp],{r5-r6}

00000354 e3a03008    518 	mov	r3,8

00000358 e3a02020    519 	mov	r2,32

0000035c e1a01000    520 	mov	r1,r0

00000360 e3a00087    521 	mov	r0,135

00000364 eb000000*   522 	bl	BerEncoder_EncodeFloatWithTL

                     523 .L708:

00000368 e28dd008    524 	add	sp,sp,8

0000036c e8bd8070    525 	ldmfd	[sp]!,{r4-r6,pc}

                     526 	.endf	encodeReadRealSett

                     527 	.align	4

                     528 ;accessInfo	r4	local

                     529 

                     530 ;outBuf	r5	param

                     531 ;bufPos	r6	param

                     532 ;descrStruct	r2	param

                     533 ;determineSize	r3	param

                     534 

                     535 	.section ".bss","awb"

                     536 .L742:

                     537 	.data

                     538 	.text


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                     539 

                     540 ;126: }


                     541 

                     542 ;127: 


                     543 ;128: int MMSData_encodeTimeStamp(uint8_t tag, uint64_t timeStamp, uint8_t* outBuf,


                     544 	.align	4

                     545 	.align	4

                     546 MMSData_encodeTimeStamp::

00000370 e92d4010    547 	stmfd	[sp]!,{r4,lr}

00000374 e24dd014    548 	sub	sp,sp,20

00000378 e98d0006    549 	stmfa	[sp],{r1-r2}

0000037c e59f47c4*   550 	ldr	r4,.L833

00000380 e5942000    551 	ldr	r2,[r4]

00000384 e59dc01c    552 	ldr	r12,[sp,28]

00000388 e58d200c    553 	str	r2,[sp,12]

0000038c e5944004    554 	ldr	r4,[r4,4]

00000390 e58d4010    555 	str	r4,[sp,16]

00000394 e5dd400b    556 	ldrb	r4,[sp,11]

00000398 e5cd400c    557 	strb	r4,[sp,12]

0000039c e5dd400a    558 	ldrb	r4,[sp,10]

000003a0 e5cd400d    559 	strb	r4,[sp,13]

000003a4 e5dd4009    560 	ldrb	r4,[sp,9]

000003a8 e5cd400e    561 	strb	r4,[sp,14]

000003ac e5dd4008    562 	ldrb	r4,[sp,8]

000003b0 e5cd400f    563 	strb	r4,[sp,15]

000003b4 e5dd4007    564 	ldrb	r4,[sp,7]

000003b8 e5cd4010    565 	strb	r4,[sp,16]

000003bc e5dd4006    566 	ldrb	r4,[sp,6]

000003c0 e5cd4011    567 	strb	r4,[sp,17]

000003c4 e5dd4005    568 	ldrb	r4,[sp,5]

000003c8 e28d100c    569 	add	r1,sp,12

000003cc e5cd4012    570 	strb	r4,[sp,18]

000003d0 e5dd4004    571 	ldrb	r4,[sp,4]

000003d4 e3a02008    572 	mov	r2,8

000003d8 e5cd4013    573 	strb	r4,[sp,19]

                     574 ;129: 	int bufPos)


                     575 ;130: {


                     576 

                     577 ;131: 	int i;


                     578 ;132: 	uint8_t timeStampBuf[8] = { 0 };	


                     579 

                     580 ;133: 	uint8_t* pTime = (uint8_t*)&timeStamp;


                     581 

                     582 ;134: 	pTime += 7; //На старший байт времени


                     583 

                     584 ;135: 


                     585 ;136: 	// в dataslice  Timequality передается вместе со временев в формате iec61850, 


                     586 ;137: 	// поэтому копируется целиком


                     587 ;138: 	for (i = 0; i < 8; ++i)


                     588 

                     589 ;142: 	}


                     590 ;143: 	return BerEncoder_encodeOctetString(tag, timeStampBuf, 8, outBuf, bufPos);


                     591 

000003dc e58dc000    592 	str	r12,[sp]

000003e0 eb000000*   593 	bl	BerEncoder_encodeOctetString

000003e4 e28dd014    594 	add	sp,sp,20

000003e8 e8bd8010    595 	ldmfd	[sp]!,{r4,pc}

                     596 	.endf	MMSData_encodeTimeStamp

                     597 	.align	4

                     598 ;timeStampBuf	[sp,12]	local

                     599 ;.L825	.L828	static


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                     600 

                     601 ;tag	none	param

                     602 ;timeStamp	[sp,4]	param

                     603 ;outBuf	none	param

                     604 ;bufPos	r12	param

                     605 

                     606 	.section ".bss","awb"

                     607 .L824:

                     608 	.section ".rodata","a"

00000000 00         609 .L828:	.space	1

00000001 00000000    610 	.space	7

00000005 000000 
                     611 	.type	.L828,$object

                     612 	.size	.L828,8

                     613 	.data

                     614 	.text

                     615 

                     616 ;144: }


                     617 

                     618 ;145: 


                     619 ;146: int encodeReadTimeStamp(uint8_t* outBuf, int bufPos, bool determineSize)


                     620 	.align	4

                     621 	.align	4

                     622 encodeReadTimeStamp::

000003ec e92d4030    623 	stmfd	[sp]!,{r4-r5,lr}

                     624 ;147: {


                     625 

                     626 ;148: 	/*


                     627 ;149: 	int i;


                     628 ;150:     uint8_t timeStampBuf[8] = {0};


                     629 ;151:     unsigned long long timeStamp = dataSliceGetTimeStamp();


                     630 ;152: 	uint8_t* pTime = (uint8_t*)&timeStamp;


                     631 ;153: 	pTime += 7; //На старший байт времени


                     632 ;154: 


                     633 ;155: 	//Разворачиваем для big endian. Последний байт оставляем для TimeQuality


                     634 ;156: 	for (i = 0; i < 7; ++i)


                     635 ;157: 	{


                     636 ;158: 		timeStampBuf[i] = *pTime;


                     637 ;159: 		pTime--;


                     638 ;160: 	}


                     639 ;161: 	*/


                     640 ;162: 	uint64_t timeStamp;


                     641 ;163: 


                     642 ;164:     if(determineSize)


                     643 

000003f0 e24dd004    644 	sub	sp,sp,4

000003f4 e3520000    645 	cmp	r2,0

                     646 ;165:     {


                     647 

                     648 ;166:         return 10;


                     649 

000003f8 13a0000a    650 	movne	r0,10

000003fc 1a000008    651 	bne	.L834

00000400 e1a04000    652 	mov	r4,r0

00000404 e1a05001    653 	mov	r5,r1

                     654 ;167:     }


                     655 ;168: 	timeStamp = dataSliceGetTimeStamp();


                     656 

00000408 eb000000*   657 	bl	dataSliceGetTimeStamp

                     658 ;169: 


                     659 ;170:     //return BerEncoder_encodeOctetString(0x91, timeStampBuf, 8, outBuf, bufPos);



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                     660 ;171: 	return MMSData_encodeTimeStamp(0x91, timeStamp, outBuf, bufPos);


                     661 

0000040c e58d5000    662 	str	r5,[sp]

00000410 e1a03004    663 	mov	r3,r4

00000414 e1a02001    664 	mov	r2,r1

00000418 e1a01000    665 	mov	r1,r0

0000041c e3a00091    666 	mov	r0,145

00000420 ebffffd2*   667 	bl	MMSData_encodeTimeStamp

                     668 .L834:

00000424 e28dd004    669 	add	sp,sp,4

00000428 e8bd8030    670 	ldmfd	[sp]!,{r4-r5,pc}

                     671 	.endf	encodeReadTimeStamp

                     672 	.align	4

                     673 

                     674 ;outBuf	r4	param

                     675 ;bufPos	r5	param

                     676 ;determineSize	r2	param

                     677 

                     678 	.section ".bss","awb"

                     679 .L870:

                     680 	.data

                     681 	.text

                     682 

                     683 ;172: }


                     684 

                     685 ;173: 


                     686 ;174: //По значению dataValue возвращает соответствующее ему значение типа enumerator


                     687 ;175: int getEnumValue(int dataValue, EnumTableRecord* table, size_t tableSize)


                     688 	.align	4

                     689 	.align	4

                     690 getEnumValue::

0000042c e92d0070    691 	stmfd	[sp]!,{r4-r6}

                     692 ;176: {


                     693 

                     694 ;177: 	size_t i;


                     695 ;178: 	for (i = 0; i < tableSize; ++i)


                     696 

00000430 e3a04000    697 	mov	r4,0

00000434 e3520000    698 	cmp	r2,0

00000438 a1a05002    699 	movge	r5,r2

0000043c b3a05000    700 	movlt	r5,0

00000440 e1b0c1a5    701 	movs	r12,r5 lsr 3

00000444 0a00001d    702 	beq	.L914

00000448 e2812038    703 	add	r2,r1,56

                     704 .L915:

0000044c e5126038    705 	ldr	r6,[r2,-56]

00000450 e2423038    706 	sub	r3,r2,56

00000454 e1500006    707 	cmp	r0,r6

00000458 15126030    708 	ldrne	r6,[r2,-48]

0000045c 12423030    709 	subne	r3,r2,48

00000460 11500006    710 	cmpne	r0,r6

00000464 15126028    711 	ldrne	r6,[r2,-40]

00000468 12423028    712 	subne	r3,r2,40

0000046c 11500006    713 	cmpne	r0,r6

00000470 15126020    714 	ldrne	r6,[r2,-32]

00000474 12423020    715 	subne	r3,r2,32

00000478 11500006    716 	cmpne	r0,r6

0000047c 15126018    717 	ldrne	r6,[r2,-24]

00000480 12423018    718 	subne	r3,r2,24

00000484 11500006    719 	cmpne	r0,r6

00000488 15126010    720 	ldrne	r6,[r2,-16]


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
0000048c 12423010    721 	subne	r3,r2,16

00000490 11500006    722 	cmpne	r0,r6

00000494 15126008    723 	ldrne	r6,[r2,-8]

00000498 12423008    724 	subne	r3,r2,8

0000049c 11500006    725 	cmpne	r0,r6

000004a0 11a03002    726 	movne	r3,r2

000004a4 15936000    727 	ldrne	r6,[r3]

000004a8 11500006    728 	cmpne	r0,r6

000004ac 0a000009    729 	beq	.L950

000004b0 e2822040    730 	add	r2,r2,64

000004b4 e2844008    731 	add	r4,r4,8

000004b8 e25cc001    732 	subs	r12,r12,1

000004bc 1affffe2    733 	bne	.L915

                     734 .L914:

000004c0 e215c007    735 	ands	r12,r5,7

000004c4 0a000009    736 	beq	.L884

000004c8 e0811184    737 	add	r1,r1,r4 lsl 3

                     738 .L949:

000004cc e1a03001    739 	mov	r3,r1

000004d0 e5932000    740 	ldr	r2,[r3]

000004d4 e1500002    741 	cmp	r0,r2

                     742 .L950:

000004d8 05930004    743 	ldreq	r0,[r3,4]

000004dc 0a000003    744 	beq	.L884

                     745 .L952:

000004e0 e2811008    746 	add	r1,r1,8

000004e4 e2844001    747 	add	r4,r4,1

000004e8 e25cc001    748 	subs	r12,r12,1

000004ec 1afffff6    749 	bne	.L949

                     750 ;184: 		}


                     751 ;185: 	}


                     752 ;186: 	//Если в таблице нет такого значения, возвращаем его напрямую


                     753 ;187: 	return dataValue;


                     754 

                     755 .L884:

000004f0 e8bd0070    756 	ldmfd	[sp]!,{r4-r6}

000004f4 e12fff1e*   757 	ret	

                     758 	.endf	getEnumValue

                     759 	.align	4

                     760 ;i	r4	local

                     761 ;pair	r3	local

                     762 

                     763 ;dataValue	r0	param

                     764 ;table	r1	param

                     765 ;tableSize	r2	param

                     766 

                     767 	.section ".bss","awb"

                     768 .L1185:

                     769 	.data

                     770 	.text

                     771 

                     772 ;188: }


                     773 

                     774 ;189: 


                     775 ;190: int getEnumDataValue(int enumValue, EnumTableRecord* table, size_t tableSize)


                     776 	.align	4

                     777 	.align	4

                     778 getEnumDataValue::

000004f8 e92d0070    779 	stmfd	[sp]!,{r4-r6}

                     780 ;191: {


                     781 


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                     782 ;192: 	size_t i;


                     783 ;193: 	for (i = 0; i < tableSize; ++i)


                     784 

000004fc e3a04000    785 	mov	r4,0

00000500 e3520000    786 	cmp	r2,0

00000504 a1a05002    787 	movge	r5,r2

00000508 b3a05000    788 	movlt	r5,0

0000050c e1b0c1a5    789 	movs	r12,r5 lsr 3

00000510 0a00001d    790 	beq	.L1266

00000514 e2812038    791 	add	r2,r1,56

                     792 .L1267:

00000518 e5126034    793 	ldr	r6,[r2,-52]

0000051c e2423038    794 	sub	r3,r2,56

00000520 e1500006    795 	cmp	r0,r6

00000524 1512602c    796 	ldrne	r6,[r2,-44]

00000528 12423030    797 	subne	r3,r2,48

0000052c 11500006    798 	cmpne	r0,r6

00000530 15126024    799 	ldrne	r6,[r2,-36]

00000534 12423028    800 	subne	r3,r2,40

00000538 11500006    801 	cmpne	r0,r6

0000053c 1512601c    802 	ldrne	r6,[r2,-28]

00000540 12423020    803 	subne	r3,r2,32

00000544 11500006    804 	cmpne	r0,r6

00000548 15126014    805 	ldrne	r6,[r2,-20]

0000054c 12423018    806 	subne	r3,r2,24

00000550 11500006    807 	cmpne	r0,r6

00000554 1512600c    808 	ldrne	r6,[r2,-12]

00000558 12423010    809 	subne	r3,r2,16

0000055c 11500006    810 	cmpne	r0,r6

00000560 15126004    811 	ldrne	r6,[r2,-4]

00000564 12423008    812 	subne	r3,r2,8

00000568 11500006    813 	cmpne	r0,r6

0000056c 11a03002    814 	movne	r3,r2

00000570 15936004    815 	ldrne	r6,[r3,4]

00000574 11500006    816 	cmpne	r0,r6

00000578 0a000009    817 	beq	.L1302

0000057c e2822040    818 	add	r2,r2,64

00000580 e2844008    819 	add	r4,r4,8

00000584 e25cc001    820 	subs	r12,r12,1

00000588 1affffe2    821 	bne	.L1267

                     822 .L1266:

0000058c e215c007    823 	ands	r12,r5,7

00000590 0a000009    824 	beq	.L1226

00000594 e0811184    825 	add	r1,r1,r4 lsl 3

                     826 .L1301:

00000598 e1a03001    827 	mov	r3,r1

0000059c e5932004    828 	ldr	r2,[r3,4]

000005a0 e1500002    829 	cmp	r0,r2

                     830 .L1302:

000005a4 05930000    831 	ldreq	r0,[r3]

000005a8 0a000003    832 	beq	.L1226

                     833 .L1304:

000005ac e2811008    834 	add	r1,r1,8

000005b0 e2844001    835 	add	r4,r4,1

000005b4 e25cc001    836 	subs	r12,r12,1

000005b8 1afffff6    837 	bne	.L1301

                     838 ;199: 		}


                     839 ;200: 	}


                     840 ;201: 	//Если в таблице нет такого значения, возвращаем его напрямую


                     841 ;202: 	return enumValue;


                     842 


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                     843 .L1226:

000005bc e8bd0070    844 	ldmfd	[sp]!,{r4-r6}

000005c0 e12fff1e*   845 	ret	

                     846 	.endf	getEnumDataValue

                     847 	.align	4

                     848 ;i	r4	local

                     849 ;pair	r3	local

                     850 

                     851 ;enumValue	r0	param

                     852 ;table	r1	param

                     853 ;tableSize	r2	param

                     854 

                     855 	.section ".bss","awb"

                     856 .L1537:

                     857 	.data

                     858 	.text

                     859 

                     860 ;203: }


                     861 

                     862 ;204: 


                     863 ;205: int readIntSettValue(IntBoolAccessInfo* accessInfo)


                     864 	.align	4

                     865 	.align	4

                     866 readIntSettValue::

000005c4 e92d4010    867 	stmfd	[sp]!,{r4,lr}

000005c8 e1a04000    868 	mov	r4,r0

                     869 ;206: {


                     870 

                     871 ;207: 	int dataValue = getIntSett(accessInfo->valueOffset);


                     872 

000005cc e5940004    873 	ldr	r0,[r4,4]

000005d0 eb000000*   874 	bl	getIntSett

                     875 ;208: 	if (accessInfo->enumTableSize == 0)


                     876 

000005d4 e5942008    877 	ldr	r2,[r4,8]

000005d8 e3520000    878 	cmp	r2,0

                     879 ;211: 	}


                     880 ;212: 	return getEnumValue(dataValue, accessInfo->enumTable,


                     881 

000005dc 1284100c    882 	addne	r1,r4,12

000005e0 18bd4010    883 	ldmnefd	[sp]!,{r4,lr}

000005e4 1affff90*   884 	bne	getEnumValue

                     885 ;209: 	{


                     886 

                     887 ;210: 		return dataValue;


                     888 

000005e8 e8bd8010    889 	ldmfd	[sp]!,{r4,pc}

                     890 	.endf	readIntSettValue

                     891 	.align	4

                     892 ;dataValue	r1	local

                     893 

                     894 ;accessInfo	r4	param

                     895 

                     896 	.section ".bss","awb"

                     897 .L1606:

                     898 	.data

                     899 	.text

                     900 

                     901 ;213: 		accessInfo->enumTableSize);


                     902 ;214: }


                     903 


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                     904 ;215: 


                     905 ;216: int readIntValue(IntBoolAccessInfo* accessInfo)


                     906 	.align	4

                     907 	.align	4

                     908 readIntValue::

000005ec e92d4010    909 	stmfd	[sp]!,{r4,lr}

000005f0 e1a04000    910 	mov	r4,r0

                     911 ;217: {


                     912 

                     913 ;218: 	int dataValue = dataSliceGetIntValue(accessInfo->valueOffset);


                     914 

000005f4 e1d400b4    915 	ldrh	r0,[r4,4]

000005f8 eb000000*   916 	bl	dataSliceGetIntValue

                     917 ;219: 	if (accessInfo->enumTableSize == 0)


                     918 

000005fc e5942008    919 	ldr	r2,[r4,8]

00000600 e3520000    920 	cmp	r2,0

                     921 ;222: 	}


                     922 ;223: 	return getEnumValue(dataValue, accessInfo->enumTable, 


                     923 

00000604 1284100c    924 	addne	r1,r4,12

00000608 18bd4010    925 	ldmnefd	[sp]!,{r4,lr}

0000060c 1affff86*   926 	bne	getEnumValue

                     927 ;220: 	{


                     928 

                     929 ;221: 		return dataValue;


                     930 

00000610 e8bd8010    931 	ldmfd	[sp]!,{r4,pc}

                     932 	.endf	readIntValue

                     933 	.align	4

                     934 ;dataValue	r1	local

                     935 

                     936 ;accessInfo	r4	param

                     937 

                     938 	.section ".bss","awb"

                     939 .L1654:

                     940 	.data

                     941 	.text

                     942 

                     943 ;224: 		accessInfo->enumTableSize);


                     944 ;225: }


                     945 

                     946 ;226: 


                     947 ;227: int encodeUInt32Value(uint8_t* outBuf, int bufPos, uint32_t value, 


                     948 	.align	4

                     949 	.align	4

                     950 encodeUInt32Value::

00000614 e92d4000    951 	stmfd	[sp]!,{lr}

                     952 ;228: 	bool determineSize)


                     953 ;229: {


                     954 

                     955 ;230: 	if (determineSize)


                     956 

00000618 e1a0c002    957 	mov	r12,r2

0000061c e3530000    958 	cmp	r3,0

00000620 0a000003    959 	beq	.L1670

                     960 ;231: 	{


                     961 

                     962 ;232: 		int intSize = BerEncoder_UInt32determineEncodedSize(value);


                     963 

00000624 e1a0000c    964 	mov	r0,r12


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
00000628 eb000000*   965 	bl	BerEncoder_UInt32determineEncodedSize

                     966 ;233: 		return intSize + 2; // 2 for tag and length


                     967 

0000062c e2800002    968 	add	r0,r0,2

00000630 ea000005    969 	b	.L1668

                     970 .L1670:

                     971 ;234: 	}


                     972 ;235: 


                     973 ;236: 	return BerEncoder_encodeUInt32WithTL(IEC61850_BER_UNSIGNED_INTEGER,


                     974 

00000634 e1a03001    975 	mov	r3,r1

00000638 e1a0100c    976 	mov	r1,r12

0000063c e1a02000    977 	mov	r2,r0

00000640 e3a00086    978 	mov	r0,134

00000644 e8bd4000    979 	ldmfd	[sp]!,{lr}

00000648 ea000000*   980 	b	BerEncoder_encodeUInt32WithTL

                     981 .L1668:

0000064c e8bd8000    982 	ldmfd	[sp]!,{pc}

                     983 	.endf	encodeUInt32Value

                     984 	.align	4

                     985 

                     986 ;outBuf	r0	param

                     987 ;bufPos	r1	param

                     988 ;value	r12	param

                     989 ;determineSize	r3	param

                     990 

                     991 	.section ".bss","awb"

                     992 .L1702:

                     993 	.data

                     994 	.text

                     995 

                     996 ;237: 		value, outBuf, bufPos);


                     997 ;238: }


                     998 

                     999 ;239: 


                    1000 ;240: int encodeBoolValue(uint8_t* outBuf, int bufPos, bool value,


                    1001 	.align	4

                    1002 	.align	4

                    1003 encodeBoolValue::

                    1004 ;241: 	bool determineSize)


                    1005 ;242: {


                    1006 

                    1007 ;243: 	if (determineSize)


                    1008 

00000650 e3530000   1009 	cmp	r3,0

                    1010 ;244: 	{


                    1011 

                    1012 ;245: 		return 3;


                    1013 

00000654 13a00003   1014 	movne	r0,3

00000658 1a000005   1015 	bne	.L1713

0000065c e1a03001   1016 	mov	r3,r1

00000660 e1a0c002   1017 	mov	r12,r2

                    1018 ;246: 	}


                    1019 ;247: 


                    1020 ;248: 	return BerEncoder_encodeBoolean(IEC61850_BER_BOOLEAN, value, outBuf,


                    1021 

00000664 e1a0100c   1022 	mov	r1,r12

00000668 e1a02000   1023 	mov	r2,r0

0000066c e3a00083   1024 	mov	r0,131

00000670 ea000000*  1025 	b	BerEncoder_encodeBoolean


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                    1026 .L1713:

00000674 e12fff1e*  1027 	ret	

                    1028 	.endf	encodeBoolValue

                    1029 	.align	4

                    1030 

                    1031 ;outBuf	r0	param

                    1032 ;bufPos	r1	param

                    1033 ;value	r12	param

                    1034 ;determineSize	r3	param

                    1035 

                    1036 	.section ".bss","awb"

                    1037 .L1750:

                    1038 	.data

                    1039 	.text

                    1040 

                    1041 ;249: 		bufPos);


                    1042 ;250: }


                    1043 

                    1044 ;251: 


                    1045 ;252: int readCodedEnum(CodedEnumAccessInfo* accessInfo)


                    1046 	.align	4

                    1047 	.align	4

                    1048 readCodedEnum::

00000678 e92d44f0   1049 	stmfd	[sp]!,{r4-r7,r10,lr}

0000067c e1a07000   1050 	mov	r7,r0

                    1051 ;253: {


                    1052 

00000680 e2876008   1053 	add	r6,r7,8

00000684 e3e0a000   1054 	mvn	r10,0

00000688 e3a04000   1055 	mov	r4,0

                    1056 ;254: 	uint8_t value = 0;


                    1057 

                    1058 ;255: 	int valIdx;


                    1059 ;256: 	for (valIdx = 0; valIdx < accessInfo->bitCount; ++valIdx)


                    1060 

0000068c e5971004   1061 	ldr	r1,[r7,4]

00000690 e1a05004   1062 	mov	r5,r4

00000694 e1550001   1063 	cmp	r5,r1

00000698 aa00000c   1064 	bge	.L1766

                    1065 .L1768:

                    1066 ;257: 	{


                    1067 

                    1068 ;258: 		int offset = accessInfo->valueOffsets[valIdx];


                    1069 

0000069c e1a04084   1070 	mov	r4,r4 lsl 1

000006a0 e4960004   1071 	ldr	r0,[r6],4

                    1072 ;259: 		value <<= 1;


                    1073 

000006a4 e20440ff   1074 	and	r4,r4,255

                    1075 ;260: 


                    1076 ;261: 		if (offset != -1)


                    1077 

000006a8 e150000a   1078 	cmp	r0,r10

000006ac 0a000004   1079 	beq	.L1767

                    1080 ;262: 		{


                    1081 

                    1082 ;263: 			value |= dataSliceGetBoolValue(offset);


                    1083 

000006b0 e1a00800   1084 	mov	r0,r0 lsl 16

000006b4 e1a00820   1085 	mov	r0,r0 lsr 16

000006b8 eb000000*  1086 	bl	dataSliceGetBoolValue


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
000006bc e1844000   1087 	orr	r4,r4,r0

000006c0 e5971004   1088 	ldr	r1,[r7,4]

                    1089 .L1767:

000006c4 e2855001   1090 	add	r5,r5,1

000006c8 e1550001   1091 	cmp	r5,r1

000006cc bafffff2   1092 	blt	.L1768

                    1093 .L1766:

                    1094 ;264: 


                    1095 ;265: 		}


                    1096 ;266: 	}


                    1097 ;267: 	value <<= (8 - accessInfo->bitCount);


                    1098 

000006d0 e2610008   1099 	rsb	r0,r1,8

000006d4 e1a01014   1100 	mov	r1,r4 lsl r0

000006d8 e20100ff   1101 	and	r0,r1,255

                    1102 ;268: 	return value;


                    1103 

000006dc e8bd84f0   1104 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    1105 	.endf	readCodedEnum

                    1106 	.align	4

                    1107 ;value	r4	local

                    1108 ;valIdx	r5	local

                    1109 ;offset	r0	local

                    1110 

                    1111 ;accessInfo	r7	param

                    1112 

                    1113 	.section ".bss","awb"

                    1114 .L1828:

                    1115 	.data

                    1116 	.text

                    1117 

                    1118 ;269: }


                    1119 

                    1120 ;270: 


                    1121 ;271: int encodeReadCodedEnum(uint8_t* outBuf, int bufPos, void* descrStruct,


                    1122 	.align	4

                    1123 	.align	4

                    1124 encodeReadCodedEnum::

000006e0 e92d4070   1125 	stmfd	[sp]!,{r4-r6,lr}

                    1126 ;272: 	bool determineSize)


                    1127 ;273: {	


                    1128 

                    1129 ;274: 	//Поддерживается не более 8 бит		


                    1130 ;275: 	uint8_t value;


                    1131 ;276: 	CodedEnumAccessInfo* accessInfo = descrStruct;


                    1132 

                    1133 ;277: 


                    1134 ;278: 	if (determineSize)


                    1135 

000006e4 e24dd008   1136 	sub	sp,sp,8

000006e8 e3530000   1137 	cmp	r3,0

                    1138 ;279: 	{


                    1139 

                    1140 ;280: 		return 4;


                    1141 

000006ec 13a00004   1142 	movne	r0,4

000006f0 1a00000b   1143 	bne	.L1844

000006f4 e1a06001   1144 	mov	r6,r1

000006f8 e1a05000   1145 	mov	r5,r0

000006fc e1a04002   1146 	mov	r4,r2

                    1147 ;281: 	}



                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                    1148 ;282: 


                    1149 ;283: 	if (accessInfo->bitCount > 8)


                    1150 

                    1151 ;284: 	{


                    1152 

                    1153 ;285: 		ERROR_REPORT("More than 8 bits is not supported");


                    1154 ;286: 	}	


                    1155 ;287: 


                    1156 ;288: 	if (accessInfo->bitCount < 1)


                    1157 

                    1158 ;289: 	{


                    1159 

                    1160 ;290: 		ERROR_REPORT("Invalid coded enum: bitCount < 1");


                    1161 ;291: 	}


                    1162 ;292: 	


                    1163 ;293: 	value = readCodedEnum(accessInfo);


                    1164 

00000700 e1a00004   1165 	mov	r0,r4

00000704 ebffffdb*  1166 	bl	readCodedEnum

00000708 e1a03005   1167 	mov	r3,r5

0000070c e28d2007   1168 	add	r2,sp,7

00000710 e5cd0007   1169 	strb	r0,[sp,7]

                    1170 ;294: 


                    1171 ;295: 	return BerEncoder_encodeBitString(ASN_TYPEDESCRIPTION_BIT_STRING,


                    1172 

00000714 e58d6000   1173 	str	r6,[sp]

00000718 e5941004   1174 	ldr	r1,[r4,4]

0000071c e3a00084   1175 	mov	r0,132

00000720 eb000000*  1176 	bl	BerEncoder_encodeBitString

                    1177 .L1844:

00000724 e28dd008   1178 	add	sp,sp,8

00000728 e8bd8070   1179 	ldmfd	[sp]!,{r4-r6,pc}

                    1180 	.endf	encodeReadCodedEnum

                    1181 	.align	4

                    1182 ;value	[sp,7]	local

                    1183 ;accessInfo	r4	local

                    1184 

                    1185 ;outBuf	r5	param

                    1186 ;bufPos	r6	param

                    1187 ;descrStruct	r2	param

                    1188 ;determineSize	r3	param

                    1189 

                    1190 	.section ".bss","awb"

                    1191 .L1887:

                    1192 	.data

                    1193 	.text

                    1194 

                    1195 ;296: 		accessInfo->bitCount, &value, outBuf, bufPos);	


                    1196 ;297: }


                    1197 

                    1198 ;298: 


                    1199 ;299: int encodeOctetString8Value(uint8_t* outBuf, int bufPos, void* pValue,


                    1200 	.align	4

                    1201 	.align	4

                    1202 encodeOctetString8Value::

0000072c e92d4000   1203 	stmfd	[sp]!,{lr}

                    1204 ;300: 	bool determineSize)


                    1205 ;301: {


                    1206 

                    1207 ;302: 	if (determineSize)


                    1208 


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
00000730 e24dd004   1209 	sub	sp,sp,4

00000734 e3530000   1210 	cmp	r3,0

                    1211 ;303: 	{


                    1212 

                    1213 ;304: 		return 10;


                    1214 

00000738 13a0000a   1215 	movne	r0,10

0000073c 1a000005   1216 	bne	.L1901

                    1217 ;305: 	}


                    1218 ;306: 


                    1219 ;307: 	return BerEncoder_encodeOctetString(IEC61850_BER_OCTET_STRING, pValue, 8,


                    1220 

00000740 e58d1000   1221 	str	r1,[sp]

00000744 e1a01002   1222 	mov	r1,r2

00000748 e3a02008   1223 	mov	r2,8

0000074c e1a03000   1224 	mov	r3,r0

00000750 e3a00089   1225 	mov	r0,137

00000754 eb000000*  1226 	bl	BerEncoder_encodeOctetString

                    1227 .L1901:

00000758 e28dd004   1228 	add	sp,sp,4

0000075c e8bd8000   1229 	ldmfd	[sp]!,{pc}

                    1230 	.endf	encodeOctetString8Value

                    1231 	.align	4

                    1232 

                    1233 ;outBuf	r0	param

                    1234 ;bufPos	r1	param

                    1235 ;pValue	r2	param

                    1236 ;determineSize	r3	param

                    1237 

                    1238 	.section ".bss","awb"

                    1239 .L1942:

                    1240 	.data

                    1241 	.text

                    1242 

                    1243 ;308: 		outBuf, bufPos);


                    1244 ;309: }


                    1245 

                    1246 ;310: 


                    1247 ;311: int encodeReadInt32(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)


                    1248 	.align	4

                    1249 	.align	4

                    1250 encodeReadInt32::

00000760 e92d4070   1251 	stmfd	[sp]!,{r4-r6,lr}

                    1252 ;312: {


                    1253 

                    1254 ;313: 	int value;


                    1255 ;314: 	IntBoolAccessInfo* accessInfo = descrStruct;


                    1256 

                    1257 ;315: 


                    1258 ;316:     value = readIntValue(accessInfo);


                    1259 

00000764 e1a05001   1260 	mov	r5,r1

00000768 e1a06003   1261 	mov	r6,r3

0000076c e1a04000   1262 	mov	r4,r0

00000770 e1a00002   1263 	mov	r0,r2

00000774 ebffff9c*  1264 	bl	readIntValue

                    1265 ;317: 


                    1266 ;318:     if(determineSize)


                    1267 

00000778 e3560000   1268 	cmp	r6,0

0000077c 0a000002   1269 	beq	.L1958


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                    1270 ;319:     {


                    1271 

                    1272 ;320:         int intSize = BerEncoder_Int32DetermineEncodedSize(value);


                    1273 

00000780 eb000000*  1274 	bl	BerEncoder_Int32DetermineEncodedSize

                    1275 ;321:         return intSize + 2; // 2 for tag and length


                    1276 

00000784 e2800002   1277 	add	r0,r0,2

00000788 ea000005   1278 	b	.L1956

                    1279 .L1958:

0000078c e1a03005   1280 	mov	r3,r5

00000790 e1a02004   1281 	mov	r2,r4

00000794 e1a01000   1282 	mov	r1,r0

                    1283 ;322:     }


                    1284 ;323:     


                    1285 ;324:     return BerEncoder_EncodeInt32WithTL(0x85, value, outBuf, bufPos);


                    1286 

00000798 e3a00085   1287 	mov	r0,133

0000079c e8bd4070   1288 	ldmfd	[sp]!,{r4-r6,lr}

000007a0 ea000000*  1289 	b	BerEncoder_EncodeInt32WithTL

                    1290 .L1956:

000007a4 e8bd8070   1291 	ldmfd	[sp]!,{r4-r6,pc}

                    1292 	.endf	encodeReadInt32

                    1293 	.align	4

                    1294 ;value	r1	local

                    1295 

                    1296 ;outBuf	r4	param

                    1297 ;bufPos	r5	param

                    1298 ;descrStruct	r2	param

                    1299 ;determineSize	r6	param

                    1300 

                    1301 	.section ".bss","awb"

                    1302 .L1990:

                    1303 	.data

                    1304 	.text

                    1305 

                    1306 ;325: }


                    1307 

                    1308 ;326: 


                    1309 ;327: int encodeReadInt32U(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)


                    1310 	.align	4

                    1311 	.align	4

                    1312 encodeReadInt32U::

000007a8 e92d4070   1313 	stmfd	[sp]!,{r4-r6,lr}

                    1314 ;328: {


                    1315 

                    1316 ;329: 	uint32_t value;


                    1317 ;330: 	


                    1318 ;331: 	IntBoolAccessInfo* accessInfo = descrStruct;


                    1319 

                    1320 ;332: 


                    1321 ;333: 	value = readIntValue(accessInfo);


                    1322 

000007ac e1a05001   1323 	mov	r5,r1

000007b0 e1a06003   1324 	mov	r6,r3

000007b4 e1a04000   1325 	mov	r4,r0

000007b8 e1a00002   1326 	mov	r0,r2

000007bc ebffff8a*  1327 	bl	readIntValue

                    1328 ;334: 


                    1329 ;335: 	return encodeUInt32Value(outBuf, bufPos, value, determineSize);


                    1330 


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
000007c0 e1a03006   1331 	mov	r3,r6

000007c4 e1a01005   1332 	mov	r1,r5

000007c8 e1a02000   1333 	mov	r2,r0

000007cc e1a00004   1334 	mov	r0,r4

000007d0 e8bd4070   1335 	ldmfd	[sp]!,{r4-r6,lr}

000007d4 eaffff8e*  1336 	b	encodeUInt32Value

                    1337 	.endf	encodeReadInt32U

                    1338 	.align	4

                    1339 

                    1340 ;outBuf	r4	param

                    1341 ;bufPos	r5	param

                    1342 ;descrStruct	r2	param

                    1343 ;determineSize	r6	param

                    1344 

                    1345 	.section ".bss","awb"

                    1346 .L2030:

                    1347 	.data

                    1348 	.text

                    1349 

                    1350 ;336: }


                    1351 

                    1352 ;337: 


                    1353 ;338: int encodeReadInt32Sett(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)


                    1354 	.align	4

                    1355 	.align	4

                    1356 encodeReadInt32Sett::

000007d8 e92d4070   1357 	stmfd	[sp]!,{r4-r6,lr}

                    1358 ;339: {


                    1359 

                    1360 ;340: 	int value;


                    1361 ;341: 


                    1362 ;342: 	IntBoolAccessInfo* accessInfo = descrStruct;


                    1363 

                    1364 ;343: 


                    1365 ;344: 	value = readIntSettValue(accessInfo);


                    1366 

000007dc e1a05001   1367 	mov	r5,r1

000007e0 e1a06003   1368 	mov	r6,r3

000007e4 e1a04000   1369 	mov	r4,r0

000007e8 e1a00002   1370 	mov	r0,r2

000007ec ebffff74*  1371 	bl	readIntSettValue

                    1372 ;345: 


                    1373 ;346: 	if (determineSize)


                    1374 

000007f0 e3560000   1375 	cmp	r6,0

000007f4 0a000002   1376 	beq	.L2039

                    1377 ;347: 	{


                    1378 

                    1379 ;348: 		int intSize = BerEncoder_Int32DetermineEncodedSize(value);


                    1380 

000007f8 eb000000*  1381 	bl	BerEncoder_Int32DetermineEncodedSize

                    1382 ;349: 		return intSize + 2; // 2 for tag and length


                    1383 

000007fc e2800002   1384 	add	r0,r0,2

00000800 ea000005   1385 	b	.L2037

                    1386 .L2039:

00000804 e1a03005   1387 	mov	r3,r5

00000808 e1a02004   1388 	mov	r2,r4

0000080c e1a01000   1389 	mov	r1,r0

                    1390 ;350: 	}


                    1391 ;351: 



                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                    1392 ;352: 	return BerEncoder_EncodeInt32WithTL(0x85, value, outBuf, bufPos);


                    1393 

00000810 e3a00085   1394 	mov	r0,133

00000814 e8bd4070   1395 	ldmfd	[sp]!,{r4-r6,lr}

00000818 ea000000*  1396 	b	BerEncoder_EncodeInt32WithTL

                    1397 .L2037:

0000081c e8bd8070   1398 	ldmfd	[sp]!,{r4-r6,pc}

                    1399 	.endf	encodeReadInt32Sett

                    1400 	.align	4

                    1401 ;value	r1	local

                    1402 

                    1403 ;outBuf	r4	param

                    1404 ;bufPos	r5	param

                    1405 ;descrStruct	r2	param

                    1406 ;determineSize	r6	param

                    1407 

                    1408 	.section ".bss","awb"

                    1409 .L2070:

                    1410 	.data

                    1411 	.text

                    1412 

                    1413 ;353: }


                    1414 

                    1415 ;354: 


                    1416 ;355: int encodeReadInt32USett(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)


                    1417 	.align	4

                    1418 	.align	4

                    1419 encodeReadInt32USett::

00000820 e92d4070   1420 	stmfd	[sp]!,{r4-r6,lr}

                    1421 ;356: {


                    1422 

                    1423 ;357:     uint32_t value;


                    1424 ;358: 


                    1425 ;359:     IntBoolAccessInfo* accessInfo = descrStruct;


                    1426 

                    1427 ;360: 


                    1428 ;361:     value = readIntSettValue(accessInfo);


                    1429 

00000824 e1a05001   1430 	mov	r5,r1

00000828 e1a06003   1431 	mov	r6,r3

0000082c e1a04000   1432 	mov	r4,r0

00000830 e1a00002   1433 	mov	r0,r2

00000834 ebffff62*  1434 	bl	readIntSettValue

                    1435 ;362: 


                    1436 ;363: 	return encodeUInt32Value(outBuf, bufPos, value, determineSize);


                    1437 

00000838 e1a03006   1438 	mov	r3,r6

0000083c e1a01005   1439 	mov	r1,r5

00000840 e1a02000   1440 	mov	r2,r0

00000844 e1a00004   1441 	mov	r0,r4

00000848 e8bd4070   1442 	ldmfd	[sp]!,{r4-r6,lr}

0000084c eaffff70*  1443 	b	encodeUInt32Value

                    1444 	.endf	encodeReadInt32USett

                    1445 	.align	4

                    1446 

                    1447 ;outBuf	r4	param

                    1448 ;bufPos	r5	param

                    1449 ;descrStruct	r2	param

                    1450 ;determineSize	r6	param

                    1451 

                    1452 	.section ".bss","awb"


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                    1453 .L2110:

                    1454 	.data

                    1455 	.text

                    1456 

                    1457 ;364: }


                    1458 

                    1459 ;365: 


                    1460 ;366: 


                    1461 ;367: int readBoolValue(IntBoolAccessInfo* accessInfo)


                    1462 	.align	4

                    1463 	.align	4

                    1464 readBoolValue::

                    1465 ;368: {


                    1466 

                    1467 ;369: 	return dataSliceGetBoolValue(accessInfo->valueOffset);


                    1468 

00000850 e1d000b4   1469 	ldrh	r0,[r0,4]

00000854 ea000000*  1470 	b	dataSliceGetBoolValue

                    1471 	.endf	readBoolValue

                    1472 	.align	4

                    1473 

                    1474 ;accessInfo	r0	param

                    1475 

                    1476 	.section ".bss","awb"

                    1477 .L2142:

                    1478 	.data

                    1479 	.text

                    1480 

                    1481 ;370: }


                    1482 

                    1483 ;371: 


                    1484 ;372: int encodeReadBoolean(uint8_t* outBuf, int bufPos, void* descrStruct, 


                    1485 	.align	4

                    1486 	.align	4

                    1487 encodeReadBoolean::

00000858 e92d4070   1488 	stmfd	[sp]!,{r4-r6,lr}

                    1489 ;373: 	bool determineSize)


                    1490 ;374: {


                    1491 

                    1492 ;375: 	int value = FALSE;


                    1493 

                    1494 ;376: 


                    1495 ;377: 	IntBoolAccessInfo* accessInfo = descrStruct;


                    1496 

                    1497 ;378: 


                    1498 ;379: 	value = readBoolValue(accessInfo);


                    1499 

0000085c e1a05001   1500 	mov	r5,r1

00000860 e1a06003   1501 	mov	r6,r3

00000864 e1a04000   1502 	mov	r4,r0

00000868 e1a00002   1503 	mov	r0,r2

0000086c ebfffff7*  1504 	bl	readBoolValue

                    1505 ;380: 


                    1506 ;381: 	return encodeBoolValue(outBuf, bufPos, value, determineSize);		


                    1507 

00000870 e1a03006   1508 	mov	r3,r6

00000874 e1a01005   1509 	mov	r1,r5

00000878 e20020ff   1510 	and	r2,r0,255

0000087c e1a00004   1511 	mov	r0,r4

00000880 e8bd4070   1512 	ldmfd	[sp]!,{r4-r6,lr}

00000884 eaffff71*  1513 	b	encodeBoolValue


                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                    1514 	.endf	encodeReadBoolean

                    1515 	.align	4

                    1516 

                    1517 ;outBuf	r4	param

                    1518 ;bufPos	r5	param

                    1519 ;descrStruct	r2	param

                    1520 ;determineSize	r6	param

                    1521 

                    1522 	.section ".bss","awb"

                    1523 .L2174:

                    1524 	.data

                    1525 	.text

                    1526 

                    1527 ;382: }


                    1528 

                    1529 ;383: 


                    1530 ;384: int encodeAccessAttrQuality(uint8_t* outBuf, int bufPos, bool determineSize)


                    1531 	.align	4

                    1532 	.align	4

                    1533 encodeAccessAttrQuality::

                    1534 ;385: {


                    1535 

                    1536 ;386:     //<Unknown len="1" tag="0x84" value="0xf3" valueStr="'\xf3'"/>


                    1537 ;387:     if(determineSize)


                    1538 

00000888 e3520000   1539 	cmp	r2,0

                    1540 ;390:     }


                    1541 ;391: 


                    1542 ;392:     return BerEncoder_EncodeInt32WithTL(ASN_TYPEDESCRIPTION_BIT_STRING,


                    1543 

0000088c 01a03001   1544 	moveq	r3,r1

00000890 03e0100c   1545 	mvneq	r1,12

00000894 01a02000   1546 	moveq	r2,r0

00000898 03a00084   1547 	moveq	r0,132

0000089c 0a000000*  1548 	beq	BerEncoder_EncodeInt32WithTL

                    1549 ;388:     {


                    1550 

                    1551 ;389:         return 3;


                    1552 

000008a0 e3a00003   1553 	mov	r0,3

000008a4 e12fff1e*  1554 	ret	

                    1555 	.endf	encodeAccessAttrQuality

                    1556 	.align	4

                    1557 

                    1558 ;outBuf	r0	param

                    1559 ;bufPos	r1	param

                    1560 ;determineSize	r2	param

                    1561 

                    1562 	.section ".bss","awb"

                    1563 .L2214:

                    1564 	.data

                    1565 	.text

                    1566 

                    1567 ;393:                                               -13, outBuf, bufPos);


                    1568 ;394: 


                    1569 ;395: }


                    1570 

                    1571 ;396: 


                    1572 ;397: int encodeAccessAttrBitString(int bitCount, uint8_t* outBuf, int bufPos, 


                    1573 	.align	4

                    1574 	.align	4


                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                    1575 encodeAccessAttrBitString::

                    1576 ;398: 	bool determineSize)


                    1577 ;399: {	


                    1578 

                    1579 ;400: 	//<Unknown len="1" tag="0x84" value="0xf3" valueStr="'\xf3'"/>


                    1580 ;401: 	if (determineSize)


                    1581 

000008a8 e3530000   1582 	cmp	r3,0

                    1583 ;404: 	}


                    1584 ;405: 


                    1585 ;406: 	return BerEncoder_EncodeInt32WithTL(ASN_TYPEDESCRIPTION_BIT_STRING,


                    1586 

000008ac 01a03002   1587 	moveq	r3,r2

000008b0 01a02001   1588 	moveq	r2,r1

000008b4 02601000   1589 	rsbeq	r1,r0,0

000008b8 03a00084   1590 	moveq	r0,132

000008bc 0a000000*  1591 	beq	BerEncoder_EncodeInt32WithTL

                    1592 ;402: 	{


                    1593 

                    1594 ;403: 		return 3;


                    1595 

000008c0 e3a00003   1596 	mov	r0,3

000008c4 e12fff1e*  1597 	ret	

                    1598 	.endf	encodeAccessAttrBitString

                    1599 	.align	4

                    1600 

                    1601 ;bitCount	r0	param

                    1602 ;outBuf	r1	param

                    1603 ;bufPos	r2	param

                    1604 ;determineSize	r3	param

                    1605 

                    1606 	.section ".bss","awb"

                    1607 .L2262:

                    1608 	.data

                    1609 	.text

                    1610 

                    1611 ;407: 		-bitCount, outBuf, bufPos);


                    1612 ;408: 


                    1613 ;409: }


                    1614 

                    1615 ;410: 


                    1616 ;411: int encodeAccessAttrBitStringConst(int constPos, uint8_t* outBuf, int bufPos,


                    1617 	.align	4

                    1618 	.align	4

                    1619 	.align	4

                    1620 encodeAccessAttrBitStringConst::

                    1621 ;412: 	bool determineSize)


                    1622 ;413: {


                    1623 

                    1624 ;414: 	int bitCount;


                    1625 ;415: 	int padding;


                    1626 ;416: 	int len;


                    1627 ;417: 	if (determineSize)


                    1628 

000008c8 e3530000   1629 	cmp	r3,0

                    1630 ;418: 	{


                    1631 

                    1632 ;419: 		return 3;


                    1633 

000008cc 13a00003   1634 	movne	r0,3

000008d0 1a00000d   1635 	bne	.L2276


                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                    1636 ;420: 	}


                    1637 ;421: 	len = iedModel[constPos + 1];


                    1638 

000008d4 e59fc270*  1639 	ldr	r12,.L2368

000008d8 e59c3000   1640 	ldr	r3,[r12]

000008dc e0833000   1641 	add	r3,r3,r0

000008e0 e5d3c001   1642 	ldrb	r12,[r3,1]

                    1643 ;422: 	RET_IF_NOT(len >= 2 && len < 127, "Invalid bitstring constant length");


                    1644 

000008e4 e24c0002   1645 	sub	r0,r12,2

000008e8 e350007d   1646 	cmp	r0,125

000008ec 2a000005   1647 	bhs	.L2286

                    1648 ;423: 	padding = iedModel[constPos + 2];


                    1649 

000008f0 e5d30002   1650 	ldrb	r0,[r3,2]

                    1651 ;424: 	RET_IF_NOT(padding < 8, "Invalid bitstring constant length");


                    1652 

000008f4 e3500008   1653 	cmp	r0,8

                    1654 ;425: 	bitCount = (len - 1) * 8 - padding;


                    1655 

000008f8 b060018c   1656 	rsblt	r0,r0,r12 lsl 3

000008fc b2400008   1657 	sublt	r0,r0,8

                    1658 ;426: 	return encodeAccessAttrBitString(bitCount, outBuf, bufPos, FALSE);


                    1659 

00000900 b3a03000   1660 	movlt	r3,0

00000904 baffffe7*  1661 	blt	encodeAccessAttrBitString

                    1662 .L2286:

00000908 e3a00000   1663 	mov	r0,0

                    1664 .L2276:

0000090c e12fff1e*  1665 	ret	

                    1666 	.endf	encodeAccessAttrBitStringConst

                    1667 	.align	4

                    1668 ;padding	r0	local

                    1669 ;len	r12	local

                    1670 

                    1671 ;constPos	r0	param

                    1672 ;outBuf	r1	param

                    1673 ;bufPos	r2	param

                    1674 ;determineSize	r3	param

                    1675 

                    1676 	.section ".bss","awb"

                    1677 .L2346:

                    1678 	.data

                    1679 	.text

                    1680 

                    1681 ;427: }


                    1682 

                    1683 ;428: 


                    1684 ;429: int encodeAccessAttrTimeStamp(uint8_t* outBuf, int bufPos, bool determineSize)


                    1685 	.align	4

                    1686 	.align	4

                    1687 encodeAccessAttrTimeStamp::

                    1688 ;430: {


                    1689 

                    1690 ;431:     //<Unknown len="0" tag="0x91" value="" valueStr="''"/>


                    1691 ;432:     if(determineSize)


                    1692 

00000910 e3520000   1693 	cmp	r2,0

                    1694 ;433:     {


                    1695 

                    1696 ;434:         return 2;



                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                    1697 

00000914 13a00002   1698 	movne	r0,2

00000918 1a000005   1699 	bne	.L2369

                    1700 ;435:     }


                    1701 ;436: 


                    1702 ;437:     outBuf[bufPos++] = 0x91;


                    1703 

0000091c e3a02091   1704 	mov	r2,145

00000920 e7c02001   1705 	strb	r2,[r0,r1]

00000924 e2811001   1706 	add	r1,r1,1

                    1707 ;438:     outBuf[bufPos++] = 0;//Длина


                    1708 

00000928 e3a02000   1709 	mov	r2,0

0000092c e7c02001   1710 	strb	r2,[r0,r1]

00000930 e2810001   1711 	add	r0,r1,1

                    1712 ;439:     return bufPos;


                    1713 

                    1714 .L2369:

00000934 e12fff1e*  1715 	ret	

                    1716 	.endf	encodeAccessAttrTimeStamp

                    1717 	.align	4

                    1718 

                    1719 ;outBuf	r0	param

                    1720 ;bufPos	r1	param

                    1721 ;determineSize	r2	param

                    1722 

                    1723 	.section ".bss","awb"

                    1724 .L2406:

                    1725 	.data

                    1726 	.text

                    1727 

                    1728 ;440: }


                    1729 

                    1730 ;441: 


                    1731 ;442: int encodeAccessAttrFloat(uint8_t* outBuf, int bufPos, bool determineSize)


                    1732 	.align	4

                    1733 	.align	4

                    1734 encodeAccessAttrFloat::

00000938 e92d4010   1735 	stmfd	[sp]!,{r4,lr}

                    1736 ;443: {


                    1737 

                    1738 ;444:     if(determineSize)


                    1739 

0000093c e3520000   1740 	cmp	r2,0

                    1741 ;445:     {


                    1742 

                    1743 ;446:         return 8;


                    1744 

00000940 13a00008   1745 	movne	r0,8

00000944 1a000010   1746 	bne	.L2420

00000948 e1a04000   1747 	mov	r4,r0

                    1748 ;447:     }    


                    1749 ;448: 


                    1750 ;449:     // Описание типа FLOAT32


                    1751 ;450:     bufPos = BerEncoder_encodeTL(ASN_TYPEDESCRIPTION_FLOAT, 6,


                    1752 

0000094c e1a02004   1753 	mov	r2,r4

00000950 e1a03001   1754 	mov	r3,r1

00000954 e3a01006   1755 	mov	r1,6

00000958 e3a000a7   1756 	mov	r0,167

0000095c eb000000*  1757 	bl	BerEncoder_encodeTL


                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                    1758 ;451:         outBuf, bufPos);


                    1759 ;452: 


                    1760 ;453:     //Ширина формата


                    1761 ;454:     bufPos = BerEncoder_encodeUInt32WithTL(ASN_INTEGER, 32,


                    1762 

00000960 e1a02004   1763 	mov	r2,r4

00000964 e3a01020   1764 	mov	r1,32

00000968 e1a03000   1765 	mov	r3,r0

0000096c e3a00002   1766 	mov	r0,2

00000970 eb000000*  1767 	bl	BerEncoder_encodeUInt32WithTL

                    1768 ;455:         outBuf, bufPos);


                    1769 ;456: 


                    1770 ;457:     //Ширина экспоненты


                    1771 ;458:     bufPos = BerEncoder_encodeUInt32WithTL(ASN_INTEGER, 8,


                    1772 

00000974 e1a02004   1773 	mov	r2,r4

00000978 e3a01008   1774 	mov	r1,8

0000097c e1a03000   1775 	mov	r3,r0

00000980 e3a00002   1776 	mov	r0,2

00000984 e8bd4010   1777 	ldmfd	[sp]!,{r4,lr}

00000988 ea000000*  1778 	b	BerEncoder_encodeUInt32WithTL

                    1779 .L2420:

0000098c e8bd8010   1780 	ldmfd	[sp]!,{r4,pc}

                    1781 	.endf	encodeAccessAttrFloat

                    1782 	.align	4

                    1783 

                    1784 ;outBuf	r4	param

                    1785 ;bufPos	r1	param

                    1786 ;determineSize	r2	param

                    1787 

                    1788 	.section ".bss","awb"

                    1789 .L2454:

                    1790 	.data

                    1791 	.text

                    1792 

                    1793 ;461: }


                    1794 

                    1795 ;462: 


                    1796 ;463: int encodeAccessAttrString(uint8_t* outBuf, int bufPos, uint8_t tag, int size, 


                    1797 	.align	4

                    1798 	.align	4

                    1799 encodeAccessAttrString::

00000990 e92d4010   1800 	stmfd	[sp]!,{r4,lr}

                    1801 ;464: 	bool determineSize)


                    1802 ;465: {


                    1803 

                    1804 ;466: 	if (determineSize)


                    1805 

00000994 e1a04001   1806 	mov	r4,r1

00000998 e1a0c002   1807 	mov	r12,r2

0000099c e5dd2008   1808 	ldrb	r2,[sp,8]

000009a0 e2631000   1809 	rsb	r1,r3,0

000009a4 e3520000   1810 	cmp	r2,0

                    1811 ;469: 		//return 4;


                    1812 ;470: 	}


                    1813 ;471: 


                    1814 ;472: 	// Описание типа 


                    1815 ;473: 	bufPos = BerEncoder_EncodeInt32WithTL(tag, -size, outBuf, bufPos);


                    1816 

000009a8 01a03004   1817 	moveq	r3,r4

000009ac 01a02000   1818 	moveq	r2,r0


                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
000009b0 01a0000c   1819 	moveq	r0,r12

000009b4 08bd4010   1820 	ldmeqfd	[sp]!,{r4,lr}

000009b8 0a000000*  1821 	beq	BerEncoder_EncodeInt32WithTL

                    1822 ;467: 	{		


                    1823 

                    1824 ;468: 		return BerEncoder_Int32DetermineEncodedSize(-size) + 2;


                    1825 

000009bc e1a00001   1826 	mov	r0,r1

000009c0 eb000000*  1827 	bl	BerEncoder_Int32DetermineEncodedSize

000009c4 e2800002   1828 	add	r0,r0,2

000009c8 e8bd8010   1829 	ldmfd	[sp]!,{r4,pc}

                    1830 	.endf	encodeAccessAttrString

                    1831 	.align	4

                    1832 

                    1833 ;outBuf	r0	param

                    1834 ;bufPos	r4	param

                    1835 ;tag	r12	param

                    1836 ;size	r3	param

                    1837 ;determineSize	r5	param

                    1838 

                    1839 	.section ".bss","awb"

                    1840 .L2502:

                    1841 	.data

                    1842 	.text

                    1843 

                    1844 ;476: }


                    1845 

                    1846 ;477: 


                    1847 ;478: int encodeAccessAttrInt(uint8_t* outBuf, int bufPos, uint8_t bitCount, 


                    1848 	.align	4

                    1849 	.align	4

                    1850 encodeAccessAttrInt::

000009cc e92d4000   1851 	stmfd	[sp]!,{lr}

                    1852 ;479: 	bool determineSize)


                    1853 ;480: {


                    1854 

                    1855 ;481:     //<Unknown len="1" tag="0x85" value="0x20" valueStr="' '"/>


                    1856 ;482:     uint8_t description = bitCount;


                    1857 

000009d0 e24dd008   1858 	sub	sp,sp,8

000009d4 e5cd2007   1859 	strb	r2,[sp,7]

                    1860 ;483: 


                    1861 ;484:     if(determineSize)


                    1862 

000009d8 e3530000   1863 	cmp	r3,0

                    1864 ;485:     {


                    1865 

                    1866 ;486:         return 3;


                    1867 

000009dc 13a00003   1868 	movne	r0,3

000009e0 1a000005   1869 	bne	.L2516

                    1870 ;487:     }


                    1871 ;488: 


                    1872 ;489:     // Описание типа INT


                    1873 ;490:     return BerEncoder_encodeOctetString(IEC61850_BER_INTEGER, &description, 1,


                    1874 

000009e4 e58d1000   1875 	str	r1,[sp]

000009e8 e28d1007   1876 	add	r1,sp,7

000009ec e3a02001   1877 	mov	r2,1

000009f0 e1a03000   1878 	mov	r3,r0

000009f4 e3a00085   1879 	mov	r0,133


                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
000009f8 eb000000*  1880 	bl	BerEncoder_encodeOctetString

                    1881 .L2516:

000009fc e28dd008   1882 	add	sp,sp,8

00000a00 e8bd8000   1883 	ldmfd	[sp]!,{pc}

                    1884 	.endf	encodeAccessAttrInt

                    1885 	.align	4

                    1886 ;description	[sp,7]	local

                    1887 

                    1888 ;outBuf	r0	param

                    1889 ;bufPos	r1	param

                    1890 ;bitCount	r2	param

                    1891 ;determineSize	r3	param

                    1892 

                    1893 	.section ".bss","awb"

                    1894 .L2550:

                    1895 	.data

                    1896 	.text

                    1897 

                    1898 ;491: 		outBuf, bufPos);


                    1899 ;492: }


                    1900 

                    1901 ;493: 


                    1902 ;494: int encodeAccessAttrUInt(uint8_t* outBuf, int bufPos, uint8_t bitCount, 


                    1903 	.align	4

                    1904 	.align	4

                    1905 encodeAccessAttrUInt::

00000a04 e92d4000   1906 	stmfd	[sp]!,{lr}

                    1907 ;495: 	bool determineSize)


                    1908 ;496: {	


                    1909 

                    1910 ;497: 	uint8_t description = bitCount;	


                    1911 

00000a08 e24dd008   1912 	sub	sp,sp,8

00000a0c e5cd2007   1913 	strb	r2,[sp,7]

                    1914 ;498: 


                    1915 ;499: 	if (determineSize)


                    1916 

00000a10 e3530000   1917 	cmp	r3,0

                    1918 ;500: 	{


                    1919 

                    1920 ;501: 		return 3;


                    1921 

00000a14 13a00003   1922 	movne	r0,3

00000a18 1a000005   1923 	bne	.L2564

                    1924 ;502: 	}


                    1925 ;503: 


                    1926 ;504: 	// Описание типа INT


                    1927 ;505: 	return BerEncoder_encodeOctetString(IEC61850_BER_UNSIGNED_INTEGER, 


                    1928 

00000a1c e58d1000   1929 	str	r1,[sp]

00000a20 e28d1007   1930 	add	r1,sp,7

00000a24 e3a02001   1931 	mov	r2,1

00000a28 e1a03000   1932 	mov	r3,r0

00000a2c e3a00086   1933 	mov	r0,134

00000a30 eb000000*  1934 	bl	BerEncoder_encodeOctetString

                    1935 .L2564:

00000a34 e28dd008   1936 	add	sp,sp,8

00000a38 e8bd8000   1937 	ldmfd	[sp]!,{pc}

                    1938 	.endf	encodeAccessAttrUInt

                    1939 	.align	4

                    1940 ;description	[sp,7]	local


                                                                      Page 33
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                    1941 

                    1942 ;outBuf	r0	param

                    1943 ;bufPos	r1	param

                    1944 ;bitCount	r2	param

                    1945 ;determineSize	r3	param

                    1946 

                    1947 	.section ".bss","awb"

                    1948 .L2598:

                    1949 	.data

                    1950 	.text

                    1951 

                    1952 ;506: 		&description, 1, outBuf, bufPos);


                    1953 ;507: }


                    1954 

                    1955 ;508: 


                    1956 ;509: int encodeAccessAttrInt128(uint8_t* outBuf, int bufPos, bool determineSize)


                    1957 	.align	4

                    1958 	.align	4

                    1959 encodeAccessAttrInt128::

00000a3c e92d4000   1960 	stmfd	[sp]!,{lr}

                    1961 ;510: {	


                    1962 

00000a40 e59f3108*  1963 	ldr	r3,.L2662

00000a44 e24dd008   1964 	sub	sp,sp,8

00000a48 e5d3c000   1965 	ldrb	r12,[r3]

00000a4c e3520000   1966 	cmp	r2,0

                    1967 ;514: 	{


                    1968 

                    1969 ;515: 		return 4;


                    1970 

00000a50 e5cdc006   1971 	strb	r12,[sp,6]

00000a54 e5d33001   1972 	ldrb	r3,[r3,1]

00000a58 13a00004   1973 	movne	r0,4

00000a5c e5cd3007   1974 	strb	r3,[sp,7]

                    1975 ;511: 	uint8_t description[2] = { 0x00, 0x20 };


                    1976 

                    1977 ;512: 


                    1978 ;513: 	if (determineSize)


                    1979 

00000a60 1a000005   1980 	bne	.L2612

                    1981 ;516: 	}


                    1982 ;517: 


                    1983 ;518: 	// Описание типа INT


                    1984 ;519: 	return BerEncoder_encodeOctetString(IEC61850_BER_INTEGER, description, 2,


                    1985 

00000a64 e58d1000   1986 	str	r1,[sp]

00000a68 e28d1006   1987 	add	r1,sp,6

00000a6c e3a02002   1988 	mov	r2,2

00000a70 e1a03000   1989 	mov	r3,r0

00000a74 e3a00085   1990 	mov	r0,133

00000a78 eb000000*  1991 	bl	BerEncoder_encodeOctetString

                    1992 .L2612:

00000a7c e28dd008   1993 	add	sp,sp,8

00000a80 e8bd8000   1994 	ldmfd	[sp]!,{pc}

                    1995 	.endf	encodeAccessAttrInt128

                    1996 	.align	4

                    1997 ;description	[sp,6]	local

                    1998 ;.L2647	.L2650	static

                    1999 

                    2000 ;outBuf	r0	param

                    2001 ;bufPos	r1	param


                                                                      Page 34
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                    2002 ;determineSize	r2	param

                    2003 

                    2004 	.section ".bss","awb"

                    2005 .L2646:

                    2006 	.section ".rodata","a"

00000008 00        2007 .L2650:	.space	1

00000009 20        2008 	.data.b	32

                    2009 	.type	.L2650,$object

                    2010 	.size	.L2650,2

                    2011 	.data

                    2012 	.text

                    2013 

                    2014 ;520: 		outBuf, bufPos);


                    2015 ;521: }


                    2016 

                    2017 ;522: 


                    2018 ;523: int encodeAccessAttrBoolean(uint8_t* outBuf, int bufPos, bool determineSize)


                    2019 	.align	4

                    2020 	.align	4

                    2021 encodeAccessAttrBoolean::

                    2022 ;524: {


                    2023 

                    2024 ;525:     //<Unknown len="0" tag="0x83" value="" valueStr="''"/>


                    2025 ;526:     if(determineSize)


                    2026 

00000a84 e3520000   2027 	cmp	r2,0

                    2028 ;529:     }


                    2029 ;530: 


                    2030 ;531:     // Описание типа BOOLEAN (только тэг и длина. Данных никаких нет)


                    2031 ;532:     return BerEncoder_encodeTL(IEC61850_BER_BOOLEAN, 0, outBuf, bufPos);


                    2032 

00000a88 01a03001   2033 	moveq	r3,r1

00000a8c 03a01000   2034 	moveq	r1,0

00000a90 01a02000   2035 	moveq	r2,r0

00000a94 03a00083   2036 	moveq	r0,131

00000a98 0a000000*  2037 	beq	BerEncoder_encodeTL

                    2038 ;527:     {


                    2039 

                    2040 ;528:         return 2;


                    2041 

00000a9c e3a00002   2042 	mov	r0,2

00000aa0 e12fff1e*  2043 	ret	

                    2044 	.endf	encodeAccessAttrBoolean

                    2045 	.align	4

                    2046 

                    2047 ;outBuf	r0	param

                    2048 ;bufPos	r1	param

                    2049 ;determineSize	r2	param

                    2050 

                    2051 	.section ".bss","awb"

                    2052 .L2694:

                    2053 	.data

                    2054 	.text

                    2055 

                    2056 ;533: }


                    2057 

                    2058 ;534: 


                    2059 ;535: int encodeAccessAttrCodedEnum(uint8_t* outBuf, int bufPos, int accessDataPos,


                    2060 	.align	4

                    2061 	.align	4

                    2062 encodeAccessAttrCodedEnum::


                                                                      Page 35
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
00000aa4 e92d4030   2063 	stmfd	[sp]!,{r4-r5,lr}

                    2064 ;536: 	bool determineSize)


                    2065 ;537: {	


                    2066 

                    2067 ;538: 	CodedEnumAccessInfo* pAccessInfo;


                    2068 ;539: 	if (determineSize)


                    2069 

00000aa8 e3530000   2070 	cmp	r3,0

                    2071 ;540: 	{	


                    2072 

                    2073 ;541: 		return 3;


                    2074 

00000aac 13a00003   2075 	movne	r0,3

00000ab0 1a00000c   2076 	bne	.L2708

00000ab4 e1a05001   2077 	mov	r5,r1

                    2078 ;542: 	}


                    2079 ;543: 	pAccessInfo = (CodedEnumAccessInfo*)getAlignedDescrStruct(accessDataPos);


                    2080 

00000ab8 e1a04000   2081 	mov	r4,r0

00000abc e1a00002   2082 	mov	r0,r2

00000ac0 eb000000*  2083 	bl	getAlignedDescrStruct

                    2084 ;544: 	if (pAccessInfo == NULL)


                    2085 

00000ac4 e3500000   2086 	cmp	r0,0

                    2087 ;545: 	{


                    2088 

                    2089 ;546: 		ERROR_REPORT("Unable to get access to the info struct");


                    2090 ;547: 		return 0;


                    2091 

00000ac8 0a000006   2092 	beq	.L2708

                    2093 ;548: 	}


                    2094 ;549: 


                    2095 ;550: 	return BerEncoder_EncodeInt32WithTL(ASN_TYPEDESCRIPTION_BIT_STRING,


                    2096 

00000acc e1a03005   2097 	mov	r3,r5

00000ad0 e5900004   2098 	ldr	r0,[r0,4]

00000ad4 e1a02004   2099 	mov	r2,r4

00000ad8 e2601000   2100 	rsb	r1,r0,0

00000adc e3a00084   2101 	mov	r0,132

00000ae0 e8bd4030   2102 	ldmfd	[sp]!,{r4-r5,lr}

00000ae4 ea000000*  2103 	b	BerEncoder_EncodeInt32WithTL

                    2104 .L2708:

00000ae8 e8bd8030   2105 	ldmfd	[sp]!,{r4-r5,pc}

                    2106 	.endf	encodeAccessAttrCodedEnum

                    2107 	.align	4

                    2108 ;pAccessInfo	r0	local

                    2109 

                    2110 ;outBuf	r4	param

                    2111 ;bufPos	r5	param

                    2112 ;accessDataPos	r2	param

                    2113 ;determineSize	r3	param

                    2114 

                    2115 	.section ".bss","awb"

                    2116 .L2771:

                    2117 	.data

                    2118 	.text

                    2119 

                    2120 ;551: 		-pAccessInfo->bitCount, outBuf, bufPos);


                    2121 ;552: }


                    2122 

                    2123 ;553: 



                                                                      Page 36
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                    2124 ;554: int encodeAccessAttrEntryTime(uint8_t* outBuf, int bufPos, bool determineSize)


                    2125 	.align	4

                    2126 	.align	4

                    2127 encodeAccessAttrEntryTime::

                    2128 ;555: {


                    2129 

                    2130 ;556:     if (determineSize)


                    2131 

00000aec e3520000   2132 	cmp	r2,0

                    2133 ;559:     }


                    2134 ;560: 


                    2135 ;561:     // Описание типа


                    2136 ;562:     bufPos = BerEncoder_EncodeInt32WithTL(IEC61850_BER_BINARY_TIME,


                    2137 

00000af0 01a03001   2138 	moveq	r3,r1

00000af4 03a01001   2139 	moveq	r1,1

00000af8 01a02000   2140 	moveq	r2,r0

00000afc 03a0008c   2141 	moveq	r0,140

00000b00 0a000000*  2142 	beq	BerEncoder_EncodeInt32WithTL

                    2143 ;557:     {


                    2144 

                    2145 ;558:         return 3;


                    2146 

00000b04 e3a00003   2147 	mov	r0,3

00000b08 e12fff1e*  2148 	ret	

                    2149 	.endf	encodeAccessAttrEntryTime

                    2150 	.align	4

                    2151 

                    2152 ;outBuf	r0	param

                    2153 ;bufPos	r1	param

                    2154 ;determineSize	r2	param

                    2155 

                    2156 	.section ".bss","awb"

                    2157 .L2822:

                    2158 	.data

                    2159 	.text

                    2160 

                    2161 ;566: }


                    2162 

                    2163 ;567: 


                    2164 ;568: 


                    2165 ;569: int encodeAccessAttrConst(uint8_t* outBuf, int bufPos, int constPos, bool determineSize)


                    2166 	.align	4

                    2167 	.align	4

                    2168 encodeAccessAttrConst::

00000b0c e92d4010   2169 	stmfd	[sp]!,{r4,lr}

                    2170 ;570: {


                    2171 

                    2172 ;571: 	uint8_t constTag = iedModel[constPos];


                    2173 

00000b10 e24dd004   2174 	sub	sp,sp,4

00000b14 e59fc030*  2175 	ldr	r12,.L2368

00000b18 e1a04002   2176 	mov	r4,r2

00000b1c e59c2000   2177 	ldr	r2,[r12]

00000b20 e7d2c004   2178 	ldrb	r12,[r2,r4]

                    2179 ;572: 	if ((constTag & BER_TAG_CLASS_MASK) != BER_CONTEXT_SPECIFIC)


                    2180 

00000b24 e20c20c0   2181 	and	r2,r12,192

00000b28 e3520080   2182 	cmp	r2,128

                    2183 ;573: 	{


                    2184 


                                                                      Page 37
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
                    2185 ;574: 		ERROR_REPORT("Invalid const tag %02X", constTag);


                    2186 ;575: 		return 0;


                    2187 

00000b2c 13a00000   2188 	movne	r0,0

00000b30 1a000063   2189 	bne	.L2836

00000b34 e59f2018*  2190 	ldr	r2,.L3068

                    2191 ;576: 	}


                    2192 ;577: 	constTag &= ~BER_TAG_CLASS_MASK;


                    2193 

00000b38 e20cc03f   2194 	and	r12,r12,63

                    2195 ;578: 


                    2196 ;579:     switch (constTag) {		


                    2197 

00000b3c e35c001c   2198 	cmp	r12,28

00000b40 8a00005b   2199 	bhi	.L2877

00000b44 ea000003   2200 	b	.L3069

                    2201 	.align	4

                    2202 .L833:

00000b48 00000000*  2203 	.data.w	.L828

                    2204 	.type	.L833,$object

                    2205 	.size	.L833,4

                    2206 

                    2207 .L2368:

00000b4c 00000000*  2208 	.data.w	iedModel

                    2209 	.type	.L2368,$object

                    2210 	.size	.L2368,4

                    2211 

                    2212 .L2662:

00000b50 00000000*  2213 	.data.w	.L2650

                    2214 	.type	.L2662,$object

                    2215 	.size	.L2662,4

                    2216 

                    2217 .L3068:

00000b54 00000000*  2218 	.data.w	encodeAccessAttrInt

                    2219 	.type	.L3068,$object

                    2220 	.size	.L3068,4

                    2221 

                    2222 .L3069:

                    2223 

00000b58 e08ff10c   2224 	add	pc,pc,r12 lsl 2

                    2225 .L3009:

                    2226 

00000b5c e1a00000   2227 	nop	

00000b60 ea00001b   2228 	b	.L2843

00000b64 ea000052   2229 	b	.L2877

00000b68 ea000051   2230 	b	.L2877

00000b6c ea000027   2231 	b	.L2855

00000b70 ea00001a   2232 	b	.L2847

00000b74 ea00004e   2233 	b	.L2877

00000b78 ea00001b   2234 	b	.L2849

00000b7c ea00001d   2235 	b	.L2851

00000b80 ea00004b   2236 	b	.L2877

00000b84 ea00001e   2237 	b	.L2853

00000b88 ea000049   2238 	b	.L2877

00000b8c ea000048   2239 	b	.L2877

00000b90 ea00001e   2240 	b	.L2855

00000b94 ea000025   2241 	b	.L2859

00000b98 ea00001f   2242 	b	.L2857

00000b9c ea000044   2243 	b	.L2877

00000ba0 ea000043   2244 	b	.L2877

00000ba4 ea000026   2245 	b	.L2863


                                                                      Page 38
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
00000ba8 ea00002a   2246 	b	.L2865

00000bac ea00002e   2247 	b	.L2867

00000bb0 ea00003f   2248 	b	.L2877

00000bb4 ea000031   2249 	b	.L2871

00000bb8 ea00003d   2250 	b	.L2877

00000bbc ea000034   2251 	b	.L2873

00000bc0 ea00003b   2252 	b	.L2877

00000bc4 ea00003a   2253 	b	.L2877

00000bc8 ea000031   2254 	b	.L2873

00000bcc ea000038   2255 	b	.L2877

00000bd0 ea000034   2256 	b	.L2875

                    2257 .L2843:

                    2258 ;580:     case IEC61850_BOOLEAN:


                    2259 ;581:         return encodeAccessAttrBoolean(outBuf, bufPos, determineSize);


                    2260 

00000bd4 e1a02003   2261 	mov	r2,r3

00000bd8 ebffffa9*  2262 	bl	encodeAccessAttrBoolean

00000bdc ea000038   2263 	b	.L2836

                    2264 .L2847:

                    2265 ;582: 	case IEC61850_INT32:


                    2266 ;583: 		return encodeAccessAttrInt(outBuf, bufPos, 32, determineSize);


                    2267 

                    2268 ;584: 	case IEC61850_INT64:


                    2269 ;585: 		return encodeAccessAttrInt(outBuf, bufPos, 64, determineSize);


                    2270 

00000be0 e3a02040   2271 	mov	r2,64

00000be4 ebffff78*  2272 	bl	encodeAccessAttrInt

00000be8 ea000035   2273 	b	.L2836

                    2274 .L2849:

                    2275 ;586: 	case IEC61850_INT8U:


                    2276 ;587: 		return encodeAccessAttrUInt(outBuf, bufPos, 8, determineSize);


                    2277 

00000bec e3a02008   2278 	mov	r2,8

00000bf0 ebffff83*  2279 	bl	encodeAccessAttrUInt

00000bf4 ea000032   2280 	b	.L2836

                    2281 .L2851:

                    2282 ;588: 	case IEC61850_INT16U:


                    2283 ;589: 		return encodeAccessAttrUInt(outBuf, bufPos, 16, determineSize);


                    2284 

00000bf8 e3a02010   2285 	mov	r2,16

00000bfc ebffff80*  2286 	bl	encodeAccessAttrUInt

00000c00 ea00002f   2287 	b	.L2836

                    2288 .L2853:

                    2289 ;590: 	case IEC61850_INT32U:


                    2290 ;591: 		return encodeAccessAttrUInt(outBuf, bufPos, 32, determineSize);


                    2291 

00000c04 e3a02020   2292 	mov	r2,32

00000c08 ebffff7d*  2293 	bl	encodeAccessAttrUInt

00000c0c ea00002c   2294 	b	.L2836

                    2295 .L2855:

                    2296 ;592: 	case IEC61850_ENUMERATED:


                    2297 ;593: 		return encodeAccessAttrInt(outBuf, bufPos, 32, determineSize);


                    2298 

00000c10 e3a02020   2299 	mov	r2,32

00000c14 ebffff6c*  2300 	bl	encodeAccessAttrInt

00000c18 ea000029   2301 	b	.L2836

                    2302 .L2857:

                    2303 ;594: 	case IEC61850_OCTET_STRING_6:


                    2304 ;595: 		return encodeAccessAttrString(outBuf, bufPos,


                    2305 

00000c1c e58d3000   2306 	str	r3,[sp]


                                                                      Page 39
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
00000c20 e3a03006   2307 	mov	r3,6

00000c24 e3a02089   2308 	mov	r2,137

00000c28 ebffff58*  2309 	bl	encodeAccessAttrString

00000c2c ea000024   2310 	b	.L2836

                    2311 .L2859:

                    2312 ;596: 			IEC61850_BER_OCTET_STRING, 6, determineSize);


                    2313 ;597: 	case IEC61850_OCTET_STRING_64:


                    2314 ;598: 		return encodeAccessAttrString(outBuf, bufPos,


                    2315 

00000c30 e58d3000   2316 	str	r3,[sp]

00000c34 e3a03040   2317 	mov	r3,64

00000c38 e3a02089   2318 	mov	r2,137

00000c3c ebffff53*  2319 	bl	encodeAccessAttrString

00000c40 ea00001f   2320 	b	.L2836

                    2321 .L2863:

                    2322 ;599: 			IEC61850_BER_OCTET_STRING, 64, determineSize);


                    2323 ;600:     case IEC61850_VISIBLE_STRING_32:


                    2324 ;601:         return encodeAccessAttrString(outBuf, bufPos,


                    2325 

                    2326 ;602:             IEC61850_BER_VISIBLE_STRING, 255, determineSize);


                    2327 ;603:     case IEC61850_VISIBLE_STRING_64:


                    2328 ;604:         return encodeAccessAttrString(outBuf, bufPos,


                    2329 

00000c44 e58d3000   2330 	str	r3,[sp]

00000c48 e3a03040   2331 	mov	r3,64

00000c4c e3a0208a   2332 	mov	r2,138

00000c50 ebffff4e*  2333 	bl	encodeAccessAttrString

00000c54 ea00001a   2334 	b	.L2836

                    2335 .L2865:

                    2336 ;605:             IEC61850_BER_VISIBLE_STRING, 64, determineSize);


                    2337 ;606:     case IEC61850_VISIBLE_STRING_65:


                    2338 ;607:         return encodeAccessAttrString(outBuf, bufPos,


                    2339 

00000c58 e58d3000   2340 	str	r3,[sp]

00000c5c e3a03041   2341 	mov	r3,65

00000c60 e3a0208a   2342 	mov	r2,138

00000c64 ebffff49*  2343 	bl	encodeAccessAttrString

00000c68 ea000015   2344 	b	.L2836

                    2345 .L2867:

                    2346 ;608:             IEC61850_BER_VISIBLE_STRING, 65, determineSize);


                    2347 ;609:     case IEC61850_VISIBLE_STRING_129:


                    2348 ;610:         return encodeAccessAttrString(outBuf, bufPos,


                    2349 

00000c6c e58d3000   2350 	str	r3,[sp]

00000c70 e3a03081   2351 	mov	r3,129

00000c74 e3a0208a   2352 	mov	r2,138

00000c78 ebffff44*  2353 	bl	encodeAccessAttrString

00000c7c ea000010   2354 	b	.L2836

                    2355 .L2871:

                    2356 ;611:             IEC61850_BER_VISIBLE_STRING, 129, determineSize);


                    2357 ;612:     case IEC61850_VISIBLE_STRING_255:


                    2358 ;613: 		return encodeAccessAttrString(outBuf, bufPos, 


                    2359 

                    2360 ;614: 			IEC61850_BER_VISIBLE_STRING, 255, determineSize);


                    2361 ;615: 	case IEC61850_UNICODE_STRING_255:


                    2362 ;616: 		return encodeAccessAttrString(outBuf, bufPos,


                    2363 

00000c80 e58d3000   2364 	str	r3,[sp]

00000c84 e3a030ff   2365 	mov	r3,255

00000c88 e3a02090   2366 	mov	r2,144

00000c8c ebffff3f*  2367 	bl	encodeAccessAttrString


                                                                      Page 40
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
00000c90 ea00000b   2368 	b	.L2836

                    2369 .L2873:

                    2370 ;617: 			IEC61850_BER_MMS_STRING, 255, determineSize);


                    2371 ;618:     case IEC61850_GENERIC_BITSTRING:


                    2372 ;619: 	case IEC61850_QUALITY:


                    2373 ;620: 		return encodeAccessAttrBitStringConst(constPos, outBuf, bufPos, determineSize);    


                    2374 

00000c94 e1a02001   2375 	mov	r2,r1

00000c98 e1a01000   2376 	mov	r1,r0

00000c9c e1a00004   2377 	mov	r0,r4

00000ca0 ebffff08*  2378 	bl	encodeAccessAttrBitStringConst

00000ca4 ea000006   2379 	b	.L2836

                    2380 .L2875:

                    2381 ;621:     case IEC61850_ENTRY_TIME:


                    2382 ;622:         return encodeAccessAttrEntryTime(outBuf, bufPos, determineSize);


                    2383 

00000ca8 e1a02003   2384 	mov	r2,r3

00000cac ebffff8e*  2385 	bl	encodeAccessAttrEntryTime

00000cb0 ea000003   2386 	b	.L2836

                    2387 .L2877:

                    2388 ;623:     default:


                    2389 ;624: 		ERROR_REPORT("Invalid const tag %02X", constTag);


                    2390 ;625: 		return encodeAccessAttrString(outBuf, bufPos,


                    2391 

00000cb4 e58d3000   2392 	str	r3,[sp]

00000cb8 e3a030ff   2393 	mov	r3,255

00000cbc e3a0208a   2394 	mov	r2,138

00000cc0 ebffff32*  2395 	bl	encodeAccessAttrString

                    2396 .L2836:

00000cc4 e28dd004   2397 	add	sp,sp,4

00000cc8 e8bd8010   2398 	ldmfd	[sp]!,{r4,pc}

                    2399 	.endf	encodeAccessAttrConst

                    2400 	.align	4

                    2401 ;constTag	r12	local

                    2402 

                    2403 ;outBuf	r0	param

                    2404 ;bufPos	r1	param

                    2405 ;constPos	r4	param

                    2406 ;determineSize	r3	param

                    2407 

                    2408 	.section ".bss","awb"

                    2409 .L3008:

                    2410 	.data

                    2411 	.ghsnote jtable,5,.L3009,.L3009,.L3009,30

                    2412 	.text

                    2413 

                    2414 ;626: 			IEC61850_BER_VISIBLE_STRING, 255, determineSize);        


                    2415 ;627:     }


                    2416 ;628: }


                    2417 	.align	4

                    2418 ;iedModel	iedModel	import

                    2419 

                    2420 	.data

                    2421 	.ghsnote version,6

                    2422 	.ghsnote tools,3

                    2423 	.ghsnote options,0

                    2424 	.text

                    2425 	.align	4

                    2426 	.section ".rodata","a"

0000000a 0000      2427 	.align	4

                    2428 	.text


                                                                      Page 41
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bdg1.s
