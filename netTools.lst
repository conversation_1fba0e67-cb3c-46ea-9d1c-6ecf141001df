                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5941.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=netTools.c -o gh_5941.o -list=netTools.lst C:\Users\<USER>\AppData\Local\Temp\gh_5941.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_5941.s
Source File: netTools.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile netTools.c -o

                      11 ;		netTools.o

                      12 ;Source File:   netTools.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:55 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "netTools.h"


                      23 ;2: 


                      24 ;3: #include <nw.h>


                      25 ;4: #include <string.h>


                      26 ;5: #include <Clib.h> // Idle


                      27 ;6: #include <ethbuslib.h>


                      28 ;7: #include <process.h>


                      29 ;8: #include <system/System.h>


                      30 ;9: #include "debug.h"


                      31 ;10: #include <stdbool.h>


                      32 ;11: 


                      33 ;12: // в зависимости от режима работы инициализируются указатели


                      34 ;13: static bool (*_getIfFuncPtr)(unsigned char ifNum, void **pNetIf);


                      35 ;14: static bool (*_getMacFuncPtr)(void* netIf, uint8_t mac[6]);


                      36 ;15: static bool (*_sendFuncPtr)(void* netIf, void* data, size_t byteCount);


                      37 ;16: 


                      38 ;17: // для межпроцессорной шины


                      39 ;18: static EthBusInterface *_ethbus;


                      40 ;19: static int _gooseReceiverSlotNum;


                      41 ;20: static int _gooseReceiverDescr = -1;


                      42 ;21: 


                      43 ;22: //Контроллер присоединения или что-то другое


                      44 ;23: static bool _isBC;


                      45 ;24: 


                      46 ;25: // =========== интерфейс межпроцессорной шины =================================


                      47 ;26: static bool ethBusGetIf(unsigned char ifNum, void **pNetIf)


                      48 	.text

                      49 	.align	4

                      50 ethBusGetIf:


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5941.s
00000000 e92d4010     51 	stmfd	[sp]!,{r4,lr}

                      52 ;27: {


                      53 

                      54 ;28: 	


                      55 ;29: 	// интерфейс уже проинициализирован


                      56 ;30: 	if (_gooseReceiverDescr >= 0)


                      57 

00000004 e59f40b8*    58 	ldr	r4,.L64

00000008 e5940000     59 	ldr	r0,[r4]

0000000c e3500000     60 	cmp	r0,0

                      61 ;43: 	}


                      62 ;44: 	


                      63 ;45: 	return TRUE;


                      64 

00000010 a3a00001     65 	movge	r0,1

00000014 aa00000b     66 	bge	.L2

                      67 ;31: 	{


                      68 

                      69 ;32: 		return TRUE;


                      70 

                      71 ;33: 	}


                      72 ;34: 	


                      73 ;35: 	// TODO: здесь нужно получить номер слота модуля связи на шине процесса


                      74 ;36: 	// пока отправляется по широковещательному


                      75 ;37: 	_gooseReceiverSlotNum = BA_GOOSE_REPEATER;


                      76 

00000018 e59fc0a8*    77 	ldr	r12,.L65

0000001c e3a00085     78 	mov	r0,133

00000020 e58c0000     79 	str	r0,[r12]

                      80 ;38: 	


                      81 ;39: 	_gooseReceiverDescr = _ethbus->open(PROTO_GOOSE_REPEATER);


                      82 

00000024 e59fc0a0*    83 	ldr	r12,.L66

00000028 e59c0000     84 	ldr	r0,[r12]

0000002c e590c028     85 	ldr	r12,[r0,40]

00000030 e3a0000d     86 	mov	r0,13

00000034 e1a0e00f     87 	mov	lr,pc

00000038 e12fff1c*    88 	bx	r12

0000003c e5840000     89 	str	r0,[r4]

                      90 ;40: 	if (_gooseReceiverDescr < 0)


                      91 

                      92 

                      93 

                      94 

00000040 e1a00fa0     95 	mov	r0,r0 lsr 31

00000044 e2200001     96 	eor	r0,r0,1

                      97 .L2:

00000048 e8bd4010     98 	ldmfd	[sp]!,{r4,lr}

0000004c e12fff1e*    99 	ret	

                     100 	.endf	ethBusGetIf

                     101 	.align	4

                     102 

                     103 ;ifNum	none	param

                     104 ;pNetIf	none	param

                     105 

                     106 	.data

                     107 .L49:

                     108 	.section ".bss","awb"

00000000 00000000    109 _getIfFuncPtr:	.space	4

00000004 00000000    110 _getMacFuncPtr:	.space	4

00000008 00000000    111 _sendFuncPtr:	.space	4


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5941.s
0000000c 00000000    112 _ethbus:	.space	4

00000010 00000000    113 _gooseReceiverSlotNum:	.space	4

                     114 	.data

                     115 .L50:

00000000 ffffffff    116 _gooseReceiverDescr:	.data.b	255,255,255,255

                     117 	.type	_gooseReceiverDescr,$object

                     118 	.size	_gooseReceiverDescr,4

                     119 	.section ".bss","awb"

00000014 00         120 _isBC:	.space	1

                     121 	.data

                     122 	.text

                     123 

                     124 ;46: }


                     125 

                     126 ;47: static bool ethBusGetMac(void* netIf, uint8_t mac[6])


                     127 	.align	4

                     128 	.align	4

                     129 ethBusGetMac:

00000050 e92d4000    130 	stmfd	[sp]!,{lr}

                     131 ;48: {


                     132 

                     133 ;49: 	memset(mac,0,6);


                     134 

00000054 e3a02006    135 	mov	r2,6

00000058 e1a00001    136 	mov	r0,r1

0000005c e3a01000    137 	mov	r1,0

00000060 eb000000*   138 	bl	memset

                     139 ;50: 	return TRUE;	


                     140 

00000064 e3a00001    141 	mov	r0,1

00000068 e8bd4000    142 	ldmfd	[sp]!,{lr}

0000006c e12fff1e*   143 	ret	

                     144 	.endf	ethBusGetMac

                     145 	.align	4

                     146 

                     147 ;netIf	none	param

                     148 ;mac	r1	param

                     149 

                     150 	.section ".bss","awb"

                     151 .L94:

                     152 	.data

                     153 	.text

                     154 

                     155 ;51: }


                     156 

                     157 ;52: 


                     158 ;53: static bool ethBusSend(void* netIf, void* data, size_t byteCount)


                     159 	.align	4

                     160 	.align	4

                     161 ethBusSend:

00000070 e92d4010    162 	stmfd	[sp]!,{r4,lr}

                     163 ;54: {


                     164 

                     165 ;55: 	_ethbus->sendTo(_gooseReceiverDescr, _gooseReceiverSlotNum,data,byteCount);


                     166 

00000074 e59fc050*   167 	ldr	r12,.L66

00000078 e89c4001    168 	ldmfd	[r12],{r0,lr}

0000007c e1a03002    169 	mov	r3,r2

00000080 e1a02001    170 	mov	r2,r1

00000084 e59fc044*   171 	ldr	r12,.L132

00000088 e5904030    172 	ldr	r4,[r0,48]


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5941.s
0000008c e59c0000    173 	ldr	r0,[r12]

00000090 e1a0100e    174 	mov	r1,lr

00000094 e1a0e00f    175 	mov	lr,pc

00000098 e12fff14*   176 	bx	r4

                     177 ;56: 	return TRUE;


                     178 

0000009c e3a00001    179 	mov	r0,1

000000a0 e8bd4010    180 	ldmfd	[sp]!,{r4,lr}

000000a4 e12fff1e*   181 	ret	

                     182 	.endf	ethBusSend

                     183 	.align	4

                     184 

                     185 ;netIf	none	param

                     186 ;data	r1	param

                     187 ;byteCount	r2	param

                     188 

                     189 	.data

                     190 	.text

                     191 

                     192 ;57: }


                     193 

                     194 ;58: 


                     195 ;59: // ============================================================================


                     196 ;60: 


                     197 ;61: 


                     198 ;62: 


                     199 ;63: 


                     200 ;64: // =========== сетевой интерфейс ==============================================


                     201 ;65: static bool networkGetIf(unsigned char ifNum, void **pNetIf)


                     202 	.align	4

                     203 	.align	4

                     204 networkGetIf:

000000a8 e92d4030    205 	stmfd	[sp]!,{r4-r5,lr}

000000ac e1a04000    206 	mov	r4,r0

000000b0 e1a05001    207 	mov	r5,r1

                     208 ;66: {


                     209 

                     210 ;67: 	NetifT*  netIf;


                     211 ;68:     // сетевые интерфейсы добавляются "пачкой" в критической секции,


                     212 ;69:     // поэтому ждем пока добавится интерфейс по-умолчанию и соответственно


                     213 ;70:     // добавятся остальные


                     214 ;71:     while(nwGetDefaultNetif() == NULL) Idle();


                     215 

000000b4 eb000000*   216 	bl	nwGetDefaultNetif

000000b8 e3500000    217 	cmp	r0,0

000000bc 1a000008    218 	bne	.L136

                     219 .L137:

000000c0 ea000003    220 	b	.L222

                     221 	.align	4

                     222 .L64:

000000c4 00000000*   223 	.data.w	.L49

                     224 	.type	.L64,$object

                     225 	.size	.L64,4

                     226 

                     227 .L65:

000000c8 00000000*   228 	.data.w	_gooseReceiverSlotNum

                     229 	.type	.L65,$object

                     230 	.size	.L65,4

                     231 

                     232 .L66:

000000cc 00000000*   233 	.data.w	_ethbus


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5941.s
                     234 	.type	.L66,$object

                     235 	.size	.L66,4

                     236 

                     237 .L132:

000000d0 00000000*   238 	.data.w	.L50

                     239 	.type	.L132,$object

                     240 	.size	.L132,4

                     241 

                     242 .L222:

                     243 

000000d4 e6000010    244 	.word	0xE6000010

                     245 

000000d8 eb000000*   246 	bl	nwGetDefaultNetif

000000dc e3500000    247 	cmp	r0,0

000000e0 0afffff6    248 	beq	.L137

                     249 .L136:

                     250 ;72: 


                     251 ;73:     // здесь уже все возможные интерфейсы должны быть добавлены


                     252 ;74:     netIf = nwGetNetif(ifNum);


                     253 

000000e4 e1a00004    254 	mov	r0,r4

000000e8 eb000000*   255 	bl	nwGetNetif

                     256 ;75:     if(netIf == NULL)


                     257 

000000ec e3500000    258 	cmp	r0,0

                     259 ;76:     {


                     260 

                     261 ;77:         return FALSE;


                     262 

000000f0 020000ff    263 	andeq	r0,r0,255

                     264 ;78:     }


                     265 ;79:     *pNetIf = netIf;


                     266 

000000f4 15850000    267 	strne	r0,[r5]

                     268 ;80:     return TRUE;


                     269 

000000f8 13a00001    270 	movne	r0,1

000000fc e8bd4030    271 	ldmfd	[sp]!,{r4-r5,lr}

00000100 e12fff1e*   272 	ret	

                     273 	.endf	networkGetIf

                     274 	.align	4

                     275 ;netIf	r0	local

                     276 

                     277 ;ifNum	r4	param

                     278 ;pNetIf	r5	param

                     279 

                     280 	.section ".bss","awb"

                     281 .L202:

                     282 	.data

                     283 	.text

                     284 

                     285 ;81: }


                     286 

                     287 ;82: 


                     288 ;83: static bool networkGetMac(void* netIf, uint8_t mac[6])


                     289 	.align	4

                     290 	.align	4

                     291 networkGetMac:

00000104 e92d4010    292 	stmfd	[sp]!,{r4,lr}

00000108 e1a04001    293 	mov	r4,r1

                     294 ;84: {



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5941.s
                     295 

                     296 ;85: 	//unsigned char *nwGetMac(NetifT *netif);


                     297 ;86:     uint8_t* result  = nwGetMac(netIf);


                     298 

0000010c eb000000*   299 	bl	nwGetMac

                     300 ;87:     memcpy(mac, result, 6);


                     301 

00000110 e1a01000    302 	mov	r1,r0

00000114 e1a00004    303 	mov	r0,r4

00000118 e3a02006    304 	mov	r2,6

0000011c eb000000*   305 	bl	memcpy

                     306 ;88:     return TRUE;	


                     307 

00000120 e3a00001    308 	mov	r0,1

00000124 e8bd4010    309 	ldmfd	[sp]!,{r4,lr}

00000128 e12fff1e*   310 	ret	

                     311 	.endf	networkGetMac

                     312 	.align	4

                     313 

                     314 ;netIf	none	param

                     315 ;mac	r4	param

                     316 

                     317 	.section ".bss","awb"

                     318 .L254:

                     319 	.data

                     320 	.text

                     321 

                     322 ;89: }


                     323 

                     324 ;90: 


                     325 ;91: static bool networkSend(void* netIf, void* data, size_t byteCount)


                     326 	.align	4

                     327 	.align	4

                     328 networkSend:

0000012c e92d4000    329 	stmfd	[sp]!,{lr}

                     330 ;92: {


                     331 

                     332 ;93: 	 return 0 == nwEthSend(netIf, data, byteCount);


                     333 

00000130 eb000000*   334 	bl	nwEthSend

00000134 e3500000    335 	cmp	r0,0

00000138 03a00001    336 	moveq	r0,1

0000013c 13a00000    337 	movne	r0,0

00000140 e8bd4000    338 	ldmfd	[sp]!,{lr}

00000144 e12fff1e*   339 	ret	

                     340 	.endf	networkSend

                     341 	.align	4

                     342 

                     343 ;netIf	none	param

                     344 ;data	none	param

                     345 ;byteCount	none	param

                     346 

                     347 	.section ".bss","awb"

                     348 .L289:

                     349 	.data

                     350 	.text

                     351 

                     352 ;94: }


                     353 

                     354 ;95: // ============================================================================


                     355 ;96: 



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5941.s
                     356 ;97: //Комментарий для проверки кодировки


                     357 ;98: bool NetTools_init(void)


                     358 	.align	4

                     359 	.align	4

                     360 NetTools_init::

00000148 e92d4030    361 	stmfd	[sp]!,{r4-r5,lr}

                     362 ;99: {


                     363 

                     364 ;100: 	_HANDLE hModule = _GetModuleHandle("SYSTEM");


                     365 

0000014c e28f0000*   366 	adr	r0,.L418

00000150 e59f4110*   367 	ldr	r4,.L419

00000154 eb000000*   368 	bl	_GetModuleHandle

                     369 ;101: 	_FindRomModule findRomModule; 


                     370 ;102: 	if (!hModule)


                     371 

00000158 e3500000    372 	cmp	r0,0

0000015c 0a000002    373 	beq	.L302

                     374 ;103: 	{


                     375 

                     376 ;104: 		TRACE("NetTools_init: SYSTEM not found");


                     377 ;105: 		return FALSE;


                     378 

                     379 ;106: 	}


                     380 ;107: 	


                     381 ;108: 	findRomModule = (_FindRomModule)_GetProcAddress(hModule,(char*)SYS_FRM);


                     382 

00000160 e3a0118a    383 	mov	r1,0x80000022

00000164 eb000000*   384 	bl	_GetProcAddress

00000168 e1b05000    385 	movs	r5,r0

                     386 ;109: 	if (!findRomModule)


                     387 

                     388 .L302:

                     389 ;110: 	{


                     390 

                     391 ;111: 		TRACE("NetTools_init: findRomModule not found");


                     392 ;112: 		return FALSE;


                     393 

0000016c 03a00000    394 	moveq	r0,0

00000170 0a000017    395 	beq	.L296

                     396 .L304:

00000174 eb000000*   397 	bl	ethBusGetInterface

00000178 e584000c    398 	str	r0,[r4,12]

0000017c e3500000    399 	cmp	r0,0

00000180 13a00001    400 	movne	r0,1

00000184 e5c40014    401 	strb	r0,[r4,20]

                     402 ;113: 	}


                     403 ;114: 	


                     404 ;115: 	// ethbus инициализируется всегда


                     405 ;116: 	_ethbus = 	ethBusGetInterface();


                     406 

                     407 ;117: 	_isBC = _ethbus != NULL;


                     408 

                     409 ;118: 	if (!_ethbus)


                     410 

                     411 ;119: 	{


                     412 

                     413 ;120: 		TRACE("NetTools_init: ethbus not found");


                     414 ;121: 		


                     415 ;122: 	}


                     416 ;123: 	// в зависимости от того, в каком модуле связи находится MMServer,



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5941.s
                     417 ;124: 	// по-разному инициализируются интерфейсы посылки


                     418 ;125: 	


                     419 ;126: 	// модуль находится в "дополнительной плате" (только для КП)


                     420 ;127: 	if (_isBC && findRomModule(NULL,'GFCN'))


                     421 

00000188 e3500000    422 	cmp	r0,0

0000018c 0a00000b    423 	beq	.L306

00000190 e59f10d4*   424 	ldr	r1,.L420

00000194 e3a00000    425 	mov	r0,0

00000198 e1a0e00f    426 	mov	lr,pc

0000019c e12fff15*   427 	bx	r5

000001a0 e3500000    428 	cmp	r0,0

000001a4 0a000005    429 	beq	.L306

000001a8 e59f20c0*   430 	ldr	r2,.L421

                     431 ;128: 	{


                     432 

                     433 ;129: 		_getIfFuncPtr = ethBusGetIf;


                     434 

                     435 ;130: 		_getMacFuncPtr = ethBusGetMac;


                     436 

                     437 ;131: 		_sendFuncPtr = ethBusSend;


                     438 

000001ac e59f10c0*   439 	ldr	r1,.L422

000001b0 e59f00c0*   440 	ldr	r0,.L423

000001b4 e8840007    441 	stmea	[r4],{r0-r2}

                     442 ;138: 	}


                     443 ;139: 	


                     444 ;140: 	return TRUE;


                     445 

000001b8 e3a00001    446 	mov	r0,1

000001bc ea000004    447 	b	.L296

                     448 .L306:

                     449 ;132: 	}


                     450 ;133: 	else // модуль находится в плате с шиной процесса или УСО


                     451 ;134: 	{


                     452 

                     453 ;135: 		_getIfFuncPtr = networkGetIf;


                     454 

000001c0 e59f20b4*   455 	ldr	r2,.L424

000001c4 e59f10b4*   456 	ldr	r1,.L425

                     457 ;137: 		_sendFuncPtr = networkSend;


                     458 

000001c8 e59f00b4*   459 	ldr	r0,.L426

                     460 ;136: 		_getMacFuncPtr = networkGetMac;


                     461 

000001cc e8840007    462 	stmea	[r4],{r0-r2}

                     463 ;138: 	}


                     464 ;139: 	


                     465 ;140: 	return TRUE;


                     466 

000001d0 e3a00001    467 	mov	r0,1

                     468 .L296:

000001d4 e8bd8030    469 	ldmfd	[sp]!,{r4-r5,pc}

                     470 	.endf	NetTools_init

                     471 	.align	4

                     472 ;hModule	r1	local

                     473 ;findRomModule	r5	local

                     474 ;.L398	.L401	static

                     475 

                     476 	.data

                     477 	.text


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5941.s
                     478 

                     479 ;141: }


                     480 

                     481 ;142: 


                     482 ;143: bool NetTools_getIf(uint8_t ifNum, void** pNetIf)


                     483 	.align	4

                     484 	.align	4

                     485 NetTools_getIf::

000001d8 e92d4100    486 	stmfd	[sp]!,{r8,lr}

                     487 ;144: {	


                     488 

                     489 ;145:    return _getIfFuncPtr(ifNum, pNetIf);


                     490 

000001dc e59f8084*   491 	ldr	r8,.L419

000001e0 e598c000    492 	ldr	r12,[r8]

000001e4 e1a0e00f    493 	mov	lr,pc

000001e8 e12fff1c*   494 	bx	r12

000001ec e8bd8100    495 	ldmfd	[sp]!,{r8,pc}

                     496 	.endf	NetTools_getIf

                     497 	.align	4

                     498 

                     499 ;ifNum	none	param

                     500 ;pNetIf	none	param

                     501 

                     502 	.data

                     503 	.text

                     504 

                     505 ;146: }


                     506 

                     507 ;147: 


                     508 ;148: bool NetTools_getMac(void* netIf, uint8_t mac[6])


                     509 	.align	4

                     510 	.align	4

                     511 NetTools_getMac::

000001f0 e92d4100    512 	stmfd	[sp]!,{r8,lr}

                     513 ;149: {


                     514 

                     515 ;150:     return _getMacFuncPtr(netIf,mac);


                     516 

000001f4 e59f808c*   517 	ldr	r8,.L487

000001f8 e598c000    518 	ldr	r12,[r8]

000001fc e1a0e00f    519 	mov	lr,pc

00000200 e12fff1c*   520 	bx	r12

00000204 e8bd8100    521 	ldmfd	[sp]!,{r8,pc}

                     522 	.endf	NetTools_getMac

                     523 	.align	4

                     524 

                     525 ;netIf	none	param

                     526 ;mac	none	param

                     527 

                     528 	.data

                     529 	.text

                     530 

                     531 ;151: }


                     532 

                     533 ;152: 


                     534 ;153: bool NetTools_send(void* netIf, void* data, size_t byteCount)


                     535 	.align	4

                     536 	.align	4

                     537 NetTools_send::

00000208 e92d4100    538 	stmfd	[sp]!,{r8,lr}


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5941.s
                     539 ;154: {


                     540 

                     541 ;155:    return _sendFuncPtr(netIf,data,byteCount);


                     542 

0000020c e59f8078*   543 	ldr	r8,.L519

00000210 e598c000    544 	ldr	r12,[r8]

00000214 e1a0e00f    545 	mov	lr,pc

00000218 e12fff1c*   546 	bx	r12

0000021c e8bd8100    547 	ldmfd	[sp]!,{r8,pc}

                     548 	.endf	NetTools_send

                     549 	.align	4

                     550 

                     551 ;netIf	none	param

                     552 ;data	none	param

                     553 ;byteCount	none	param

                     554 

                     555 	.data

                     556 	.text

                     557 

                     558 ;156: }


                     559 

                     560 ;157: 


                     561 ;158: 


                     562 ;159: bool NetTools_busOK(void)


                     563 	.align	4

                     564 	.align	4

                     565 NetTools_busOK::

00000220 e92d4000    566 	stmfd	[sp]!,{lr}

                     567 ;160: {


                     568 

                     569 ;161: 	EthBusModuleState *moduleState;


                     570 ;162: 


                     571 ;163: 	if(!_isBC)


                     572 

00000224 e51fc160*   573 	ldr	r12,.L66

00000228 e5dc0008    574 	ldrb	r0,[r12,8]

0000022c e3500000    575 	cmp	r0,0

                     576 ;164: 	{


                     577 

                     578 ;165: 		return true;


                     579 

00000230 03a00001    580 	moveq	r0,1

00000234 0a000008    581 	beq	.L520

00000238 e59cc000    582 	ldr	r12,[r12]

0000023c e1b0000c    583 	movs	r0,r12

                     584 ;166: 	}


                     585 ;167: 


                     586 ;168: 	// шина еще не инициализирована


                     587 ;169: 	if (!_ethbus)


                     588 

                     589 ;170: 	{


                     590 

                     591 ;171: 		return false;


                     592 

00000240 020000ff    593 	andeq	r0,r0,255

00000244 0a000004    594 	beq	.L520

                     595 ;172: 	}


                     596 ;173: 


                     597 ;174: 	// не может быть NULL


                     598 ;175: 	moduleState = _ethbus->getModuleState(-1);


                     599 


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5941.s
00000248 e59cc014    600 	ldr	r12,[r12,20]

0000024c e3e00000    601 	mvn	r0,0

00000250 e1a0e00f    602 	mov	lr,pc

00000254 e12fff1c*   603 	bx	r12

                     604 ;176: 	return moduleState->readyFlag;


                     605 

00000258 e5d00005    606 	ldrb	r0,[r0,5]

                     607 .L520:

0000025c e8bd8000    608 	ldmfd	[sp]!,{pc}

                     609 	.endf	NetTools_busOK

                     610 	.align	4

                     611 

                     612 	.data

                     613 	.text

                     614 

                     615 ;177: }


                     616 	.align	4

                     617 .L418:

                     618 ;	"SYSTEM\000"

00000260 54535953    619 	.data.b	83,89,83,84

00000264 4d45       620 	.data.b	69,77

00000266 00         621 	.data.b	0

00000267 00         622 	.align 4

                     623 

                     624 	.type	.L418,$object

                     625 	.size	.L418,4

                     626 

                     627 .L419:

00000268 00000000*   628 	.data.w	_getIfFuncPtr

                     629 	.type	.L419,$object

                     630 	.size	.L419,4

                     631 

                     632 .L420:

0000026c 4746434e    633 	.data.w	0x4746434e

                     634 	.type	.L420,$object

                     635 	.size	.L420,4

                     636 

                     637 .L421:

00000270 00000000*   638 	.data.w	ethBusSend

                     639 	.type	.L421,$object

                     640 	.size	.L421,4

                     641 

                     642 .L422:

00000274 00000000*   643 	.data.w	ethBusGetMac

                     644 	.type	.L422,$object

                     645 	.size	.L422,4

                     646 

                     647 .L423:

00000278 00000000*   648 	.data.w	ethBusGetIf

                     649 	.type	.L423,$object

                     650 	.size	.L423,4

                     651 

                     652 .L424:

0000027c 00000000*   653 	.data.w	networkSend

                     654 	.type	.L424,$object

                     655 	.size	.L424,4

                     656 

                     657 .L425:

00000280 00000000*   658 	.data.w	networkGetMac

                     659 	.type	.L425,$object

                     660 	.size	.L425,4


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5941.s
                     661 

                     662 .L426:

00000284 00000000*   663 	.data.w	networkGetIf

                     664 	.type	.L426,$object

                     665 	.size	.L426,4

                     666 

                     667 .L487:

00000288 00000000*   668 	.data.w	_getMacFuncPtr

                     669 	.type	.L487,$object

                     670 	.size	.L487,4

                     671 

                     672 .L519:

0000028c 00000000*   673 	.data.w	_sendFuncPtr

                     674 	.type	.L519,$object

                     675 	.size	.L519,4

                     676 

                     677 	.align	4

                     678 ;_getIfFuncPtr	_getIfFuncPtr	static

                     679 ;_getMacFuncPtr	_getMacFuncPtr	static

                     680 ;_sendFuncPtr	_sendFuncPtr	static

                     681 ;_ethbus	_ethbus	static

                     682 ;_gooseReceiverSlotNum	_gooseReceiverSlotNum	static

                     683 ;_gooseReceiverDescr	.L50	static

                     684 ;_isBC	_isBC	static

                     685 

                     686 	.data

                     687 	.ghsnote version,6

                     688 	.ghsnote tools,1

                     689 	.ghsnote options,0

                     690 	.text

                     691 	.align	4

                     692 	.data

                     693 	.align	4

                     694 	.section ".bss","awb"

00000015 000000     695 	.align	4

                     696 	.text

