                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_acc1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=BERCoder.c -o gh_acc1.o -list=BERCoder.lst C:\Users\<USER>\AppData\Local\Temp\gh_acc1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_acc1.s
Source File: BERCoder.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile BERCoder.c -o

                      11 ;		BERCoder.o

                      12 ;Source File:   BERCoder.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:53 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "BERCoder.h"


                      23 ;2: 


                      24 ;3: 


                      25 ;4: size_t BERCoder_calcIntEncodedLen(const void* pValue, size_t intSize)


                      26 	.text

                      27 	.align	4

                      28 BERCoder_calcIntEncodedLen::

00000000 e92d0070     29 	stmfd	[sp]!,{r4-r6}

                      30 ;5: {


                      31 

                      32 ;6:     // Функция работает в big endian. То есть начинаем с последнего


                      33 ;7:     // байта, он же - старший


                      34 ;8:     const uint8_t* pValueBytes = ((const uint8_t*)pValue) + (intSize - 1);


                      35 

00000004 e2512001     36 	subs	r2,r1,1

                      37 ;9:     size_t i;


                      38 ;10:     uint8_t byte = *pValueBytes;


                      39 

00000008 e7f05002     40 	ldrb	r5,[r0,r2]!

                      41 ;11: 


                      42 ;12:     // Пропускаем лидирующие знаковые байты. Цикл по старшим 7 байтам


                      43 ;13:     for (i = 0; i < (intSize - 1); i++)


                      44 

0000000c e3a03000     45 	mov	r3,0

00000010 51a04002     46 	movpl	r4,r2

00000014 43a04000     47 	movmi	r4,0

00000018 e1b0c1a4     48 	movs	r12,r4 lsr 3

0000001c 0a000059     49 	beq	.L47

                      50 .L48:


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_acc1.s
00000020 e5502001     51 	ldrb	r2,[r0,-1]

00000024 e35500ff     52 	cmp	r5,255

00000028 e2026080     53 	and	r6,r2,128

0000002c 1a000001     54 	bne	.L50

00000030 e3560000     55 	cmp	r6,0

00000034 1a000001     56 	bne	.L53

                      57 .L50:

00000038 e1965005     58 	orrs	r5,r6,r5

0000003c 1a000060     59 	bne	.L2

                      60 .L53:

00000040 e2833001     61 	add	r3,r3,1

00000044 e1a05002     62 	mov	r5,r2

00000048 e5502002     63 	ldrb	r2,[r0,-2]

0000004c e2400001     64 	sub	r0,r0,1

00000050 e2026080     65 	and	r6,r2,128

00000054 e35500ff     66 	cmp	r5,255

00000058 1a000001     67 	bne	.L55

0000005c e3560000     68 	cmp	r6,0

00000060 1a000001     69 	bne	.L58

                      70 .L55:

00000064 e1965005     71 	orrs	r5,r6,r5

00000068 1a000055     72 	bne	.L2

                      73 .L58:

0000006c e2833001     74 	add	r3,r3,1

00000070 e1a05002     75 	mov	r5,r2

00000074 e5502002     76 	ldrb	r2,[r0,-2]

00000078 e2400001     77 	sub	r0,r0,1

0000007c e2026080     78 	and	r6,r2,128

00000080 e35500ff     79 	cmp	r5,255

00000084 1a000001     80 	bne	.L60

00000088 e3560000     81 	cmp	r6,0

0000008c 1a000001     82 	bne	.L63

                      83 .L60:

00000090 e1965005     84 	orrs	r5,r6,r5

00000094 1a00004a     85 	bne	.L2

                      86 .L63:

00000098 e2833001     87 	add	r3,r3,1

0000009c e1a05002     88 	mov	r5,r2

000000a0 e5502002     89 	ldrb	r2,[r0,-2]

000000a4 e2400001     90 	sub	r0,r0,1

000000a8 e2026080     91 	and	r6,r2,128

000000ac e35500ff     92 	cmp	r5,255

000000b0 1a000001     93 	bne	.L65

000000b4 e3560000     94 	cmp	r6,0

000000b8 1a000001     95 	bne	.L68

                      96 .L65:

000000bc e1965005     97 	orrs	r5,r6,r5

000000c0 1a00003f     98 	bne	.L2

                      99 .L68:

000000c4 e2833001    100 	add	r3,r3,1

000000c8 e1a05002    101 	mov	r5,r2

000000cc e5502002    102 	ldrb	r2,[r0,-2]

000000d0 e2400001    103 	sub	r0,r0,1

000000d4 e2026080    104 	and	r6,r2,128

000000d8 e35500ff    105 	cmp	r5,255

000000dc 1a000001    106 	bne	.L70

000000e0 e3560000    107 	cmp	r6,0

000000e4 1a000001    108 	bne	.L73

                     109 .L70:

000000e8 e1965005    110 	orrs	r5,r6,r5

000000ec 1a000034    111 	bne	.L2


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_acc1.s
                     112 .L73:

000000f0 e2833001    113 	add	r3,r3,1

000000f4 e1a05002    114 	mov	r5,r2

000000f8 e5502002    115 	ldrb	r2,[r0,-2]

000000fc e2400001    116 	sub	r0,r0,1

00000100 e2026080    117 	and	r6,r2,128

00000104 e35500ff    118 	cmp	r5,255

00000108 1a000001    119 	bne	.L75

0000010c e3560000    120 	cmp	r6,0

00000110 1a000001    121 	bne	.L78

                     122 .L75:

00000114 e1965005    123 	orrs	r5,r6,r5

00000118 1a000029    124 	bne	.L2

                     125 .L78:

0000011c e2833001    126 	add	r3,r3,1

00000120 e1a05002    127 	mov	r5,r2

00000124 e5502002    128 	ldrb	r2,[r0,-2]

00000128 e2400001    129 	sub	r0,r0,1

0000012c e2026080    130 	and	r6,r2,128

00000130 e35500ff    131 	cmp	r5,255

00000134 1a000001    132 	bne	.L80

00000138 e3560000    133 	cmp	r6,0

0000013c 1a000001    134 	bne	.L83

                     135 .L80:

00000140 e1965005    136 	orrs	r5,r6,r5

00000144 1a00001e    137 	bne	.L2

                     138 .L83:

00000148 e2833001    139 	add	r3,r3,1

0000014c e1a05002    140 	mov	r5,r2

00000150 e5502002    141 	ldrb	r2,[r0,-2]

00000154 e2400001    142 	sub	r0,r0,1

00000158 e2026080    143 	and	r6,r2,128

0000015c e35500ff    144 	cmp	r5,255

00000160 1a000001    145 	bne	.L85

00000164 e3560000    146 	cmp	r6,0

00000168 1a000001    147 	bne	.L87

                     148 .L85:

0000016c e1965005    149 	orrs	r5,r6,r5

00000170 1a000013    150 	bne	.L2

                     151 .L87:

00000174 e1a05002    152 	mov	r5,r2

00000178 e2400001    153 	sub	r0,r0,1

0000017c e2833001    154 	add	r3,r3,1

00000180 e25cc001    155 	subs	r12,r12,1

00000184 1affffa5    156 	bne	.L48

                     157 .L47:

00000188 e214c007    158 	ands	r12,r4,7

0000018c 0a00000c    159 	beq	.L2

                     160 .L90:

00000190 e5502001    161 	ldrb	r2,[r0,-1]

00000194 e35500ff    162 	cmp	r5,255

00000198 e2026080    163 	and	r6,r2,128

0000019c 1a000001    164 	bne	.L92

000001a0 e3560000    165 	cmp	r6,0

000001a4 1a000001    166 	bne	.L94

                     167 .L92:

000001a8 e1964005    168 	orrs	r4,r6,r5

000001ac 1a000004    169 	bne	.L2

                     170 .L94:

000001b0 e1a05002    171 	mov	r5,r2

000001b4 e2400001    172 	sub	r0,r0,1


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_acc1.s
000001b8 e2833001    173 	add	r3,r3,1

000001bc e25cc001    174 	subs	r12,r12,1

000001c0 1afffff2    175 	bne	.L90

                     176 ;26:     }


                     177 ;27: 


                     178 ;28:     // Return the minimum number of bytes needed to represent the value


                     179 ;29:     return intSize - i;


                     180 

                     181 .L2:

000001c4 e0410003    182 	sub	r0,r1,r3

000001c8 e8bd0070    183 	ldmfd	[sp]!,{r4-r6}

000001cc e12fff1e*   184 	ret	

                     185 	.endf	BERCoder_calcIntEncodedLen

                     186 	.align	4

                     187 ;pValueBytes	r0	local

                     188 ;i	r3	local

                     189 ;byte	r5	local

                     190 ;nextByte	r2	local

                     191 ;nextBit	r6	local

                     192 

                     193 ;pValue	r0	param

                     194 ;intSize	r1	param

                     195 

                     196 	.section ".bss","awb"

                     197 .L519:

                     198 	.data

                     199 	.text

                     200 

                     201 ;30: }


                     202 

                     203 ;31: 


                     204 ;32: void BERCoder_reverseCopy(const void* src, uint8_t* dst, size_t len)


                     205 	.align	4

                     206 	.align	4

                     207 BERCoder_reverseCopy::

                     208 ;33: {    


                     209 

                     210 ;34:     size_t i;


                     211 ;35:     const uint8_t* pSrc = src;


                     212 

000001d0 e0823000    213 	add	r3,r2,r0

000001d4 e2430001    214 	sub	r0,r3,1

                     215 ;36:     pSrc += len - 1;


                     216 

                     217 ;37:     


                     218 ;38:     for (i = 0; i < len; i++)


                     219 

000001d8 e3520000    220 	cmp	r2,0

000001dc a1a0c002    221 	movge	r12,r2

000001e0 b3a0c000    222 	movlt	r12,0

000001e4 e1b021ac    223 	movs	r2,r12 lsr 3

000001e8 0a000011    224 	beq	.L657

                     225 .L673:

000001ec e4503008    226 	ldrb	r3,[r0],-8

000001f0 e4c13001    227 	strb	r3,[r1],1

000001f4 e5d03007    228 	ldrb	r3,[r0,7]

000001f8 e4c13001    229 	strb	r3,[r1],1

000001fc e5d03006    230 	ldrb	r3,[r0,6]

00000200 e4c13001    231 	strb	r3,[r1],1

00000204 e5d03005    232 	ldrb	r3,[r0,5]

00000208 e4c13001    233 	strb	r3,[r1],1


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_acc1.s
0000020c e5d03004    234 	ldrb	r3,[r0,4]

00000210 e4c13001    235 	strb	r3,[r1],1

00000214 e5d03003    236 	ldrb	r3,[r0,3]

00000218 e4c13001    237 	strb	r3,[r1],1

0000021c e5d03002    238 	ldrb	r3,[r0,2]

00000220 e4c13001    239 	strb	r3,[r1],1

00000224 e5d03001    240 	ldrb	r3,[r0,1]

00000228 e2522001    241 	subs	r2,r2,1

0000022c e4c13001    242 	strb	r3,[r1],1

00000230 1affffed    243 	bne	.L673

                     244 .L657:

00000234 e21c2007    245 	ands	r2,r12,7

                     246 .L677:

00000238 14503001    247 	ldrneb	r3,[r0],-1

0000023c 14c13001    248 	strneb	r3,[r1],1

00000240 12522001    249 	subnes	r2,r2,1

00000244 1afffffb    250 	bne	.L677

                     251 .L625:

00000248 e12fff1e*   252 	ret	

                     253 	.endf	BERCoder_reverseCopy

                     254 	.align	4

                     255 ;pSrc	r0	local

                     256 

                     257 ;src	r0	param

                     258 ;dst	r1	param

                     259 ;len	r2	param

                     260 

                     261 	.section ".bss","awb"

                     262 .L830:

                     263 	.data

                     264 	.text

                     265 

                     266 ;41:     }


                     267 ;42: }


                     268 	.align	4

                     269 

                     270 	.data

                     271 	.ghsnote version,6

                     272 	.ghsnote tools,3

                     273 	.ghsnote options,0

                     274 	.text

                     275 	.align	4

