                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9jo1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=platformTools.c -o gh_9jo1.o -list=platformTools.lst C:\Users\<USER>\AppData\Local\Temp\gh_9jo1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_9jo1.s
Source File: platformTools.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		platformTools.c -o platformTools.o

                      12 ;Source File:   platformTools.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:53 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "platformTools.h"


                      23 ;2: #include "Clib.h"


                      24 ;3: 


                      25 ;4: int PTools_lockInterrupt(void)


                      26 	.text

                      27 	.align	4

                      28 PTools_lockInterrupt::

                      29 ;5: {


                      30 

                      31 ;6:     return lockInterrupt();


                      32 

00000000 ea000000*    33 	b	lockInterrupt

                      34 	.endf	PTools_lockInterrupt

                      35 	.align	4

                      36 

                      37 	.section ".bss","awb"

                      38 .L30:

                      39 	.data

                      40 	.text

                      41 

                      42 ;7: }


                      43 

                      44 ;8: 


                      45 ;9: void PTools_unlockInterrupt(int previousInterruptState)


                      46 	.align	4

                      47 	.align	4

                      48 PTools_unlockInterrupt::

                      49 ;10: {


                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9jo1.s
                      51 ;11:     unlockInterrupt(previousInterruptState);


                      52 

00000004 ea000000*    53 	b	unlockInterrupt

                      54 	.endf	PTools_unlockInterrupt

                      55 	.align	4

                      56 

                      57 ;previousInterruptState	none	param

                      58 

                      59 	.section ".bss","awb"

                      60 .L62:

                      61 	.data

                      62 	.text

                      63 

                      64 ;12: }


                      65 	.align	4

                      66 

                      67 	.data

                      68 	.ghsnote version,6

                      69 	.ghsnote tools,3

                      70 	.ghsnote options,0

                      71 	.text

                      72 	.align	4

