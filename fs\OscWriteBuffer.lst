                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=OscWriteBuffer.c -o fs\gh_6u41.o -list=fs/OscWriteBuffer.lst C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
Source File: OscWriteBuffer.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		fs/OscWriteBuffer.c -o fs/OscWriteBuffer.o

                      12 ;Source File:   fs/OscWriteBuffer.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:59 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include <stdlib.h>


                      23 ;2: #include <string.h>


                      24 ;3: #include <types.h>


                      25 ;4: #include "../local_types.h"


                      26 ;5: #include "OscWriteBuffer.h"


                      27 ;6: #include "OscFiles.h"


                      28 ;7: 


                      29 ;8: bool OscWriteBuffer_create(OscWriteBuffer *wb, size_t size)


                      30 ;9: {


                      31 ;10: 	void *p;


                      32 ;11: 


                      33 ;12: 	if (wb->p) // p должно быть NULL для корректного удаления


                      34 ;13: 	{


                      35 ;14: 		return false;


                      36 ;15: 	}


                      37 ;16: 


                      38 ;17: 	p = OscFiles_malloc(size);


                      39 ;18: 	if (!p)


                      40 ;19: 	{


                      41 ;20: 		return false;


                      42 ;21: 	}


                      43 ;22: 


                      44 ;23: 	wb->p = p;


                      45 ;24: 	wb->data = p;


                      46 ;25: 	wb->size = size;


                      47 ;26: 	wb->dataSize = 0;


                      48 ;27: 	wb->dataPos = 0;


                      49 ;28: 	return true;


                      50 ;29: }



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
                      51 ;30: 


                      52 ;31: bool OscWriteBuffer_attach(OscWriteBuffer *wb, unsigned char *p, size_t size)


                      53 ;32: {


                      54 ;33: 	wb->p = NULL;


                      55 ;34: 	wb->data = p;


                      56 ;35: 	wb->size = size;


                      57 ;36: 	wb->dataSize = 0;


                      58 ;37: 	wb->dataPos = 0;


                      59 ;38: 	return true;


                      60 ;39: }


                      61 ;40: 


                      62 ;41: void OscWriteBuffer_reset(OscWriteBuffer *wb)


                      63 ;42: {


                      64 ;43: 	wb->dataPos = 0;


                      65 ;44: 	wb->dataSize = 0;


                      66 ;45: }


                      67 ;46: 


                      68 ;47: static size_t getFreeSize(OscWriteBuffer *wb)


                      69 

                      70 ;50: }


                      71 

                      72 	.text

                      73 	.align	4

                      74 OscWriteBuffer_create::

00000000 e92d4030     75 	stmfd	[sp]!,{r4-r5,lr}

00000004 e1a04000     76 	mov	r4,r0

00000008 e5940004     77 	ldr	r0,[r4,4]

0000000c e1a05001     78 	mov	r5,r1

00000010 e3500000     79 	cmp	r0,0

00000014 1a000003     80 	bne	.L27

00000018 e1a00005     81 	mov	r0,r5

0000001c eb000000*    82 	bl	OscFiles_malloc

00000020 e3500000     83 	cmp	r0,0

00000024 1a000001     84 	bne	.L26

                      85 .L27:

00000028 e3a00000     86 	mov	r0,0

0000002c ea000005     87 	b	.L21

                      88 .L26:

00000030 e1a02000     89 	mov	r2,r0

00000034 e1a0c005     90 	mov	r12,r5

00000038 e3a05000     91 	mov	r5,0

0000003c e1a03005     92 	mov	r3,r5

00000040 e884102d     93 	stmea	[r4],{r0,r2-r3,r5,r12}

00000044 e3a00001     94 	mov	r0,1

                      95 .L21:

00000048 e8bd8030     96 	ldmfd	[sp]!,{r4-r5,pc}

                      97 	.endf	OscWriteBuffer_create

                      98 	.align	4

                      99 ;p	r0	local

                     100 

                     101 ;wb	r4	param

                     102 ;size	r5	param

                     103 

                     104 	.section ".bss","awb"

                     105 .L76:

                     106 	.data

                     107 	.text

                     108 

                     109 

                     110 	.align	4

                     111 	.align	4


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
                     112 OscWriteBuffer_attach::

0000004c e3a03000    113 	mov	r3,0

00000050 e1a0c003    114 	mov	r12,r3

00000054 e8a0100a    115 	stmea	[r0]!,{r1,r3,r12}

00000058 e1a01003    116 	mov	r1,r3

0000005c e8800006    117 	stmea	[r0],{r1-r2}

00000060 e3a00001    118 	mov	r0,1

00000064 e12fff1e*   119 	ret	

                     120 	.endf	OscWriteBuffer_attach

                     121 	.align	4

                     122 

                     123 ;wb	r0	param

                     124 ;p	r1	param

                     125 ;size	r2	param

                     126 

                     127 	.section ".bss","awb"

                     128 .L110:

                     129 	.data

                     130 	.text

                     131 

                     132 

                     133 	.align	4

                     134 	.align	4

                     135 OscWriteBuffer_reset::

00000068 e3a01000    136 	mov	r1,0

0000006c e5801008    137 	str	r1,[r0,8]

00000070 e580100c    138 	str	r1,[r0,12]

00000074 e12fff1e*   139 	ret	

                     140 	.endf	OscWriteBuffer_reset

                     141 	.align	4

                     142 

                     143 ;wb	r0	param

                     144 

                     145 	.section ".bss","awb"

                     146 .L142:

                     147 	.data

                     148 	.text

                     149 

                     150 

                     151 ;51: bool OscWriteBuffer_write(OscWriteBuffer *wb, void *data, size_t size)


                     152 	.align	4

                     153 	.align	4

                     154 OscWriteBuffer_write::

00000078 e92d40f0    155 	stmfd	[sp]!,{r4-r7,lr}

0000007c e2804008    156 	add	r4,r0,8

00000080 e1a07001    157 	mov	r7,r1

00000084 e1a05002    158 	mov	r5,r2

                     159 ;52: {


                     160 

                     161 ;53: 	size_t freeSize = getFreeSize(wb);


                     162 

                     163 ;48: {


                     164 

                     165 ;49: 	return wb->size - wb->dataSize - wb->dataPos;


                     166 

00000088 e8940049    167 	ldmfd	[r4],{r0,r3,r6}

0000008c e2444008    168 	sub	r4,r4,8

00000090 e1a01006    169 	mov	r1,r6

00000094 e1a0c000    170 	mov	r12,r0

00000098 e0410003    171 	sub	r0,r1,r3

0000009c e040000c    172 	sub	r0,r0,r12


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
                     173 ;54: 	// не хватает места


                     174 ;55: 	if (freeSize < size)


                     175 

000000a0 e1500005    176 	cmp	r0,r5

000000a4 25940000    177 	ldrhs	r0,[r4]

000000a8 2a00000d    178 	bhs	.L155

                     179 ;56: 	{


                     180 

                     181 ;57: 		void *p = wb->p;


                     182 

000000ac e5940004    183 	ldr	r0,[r4,4]

                     184 ;58: 		int newSize = wb->size * 2; // увеличивается в 2 раза


                     185 

000000b0 e1a06081    186 	mov	r6,r1 lsl 1

                     187 ;59: 		if (p == NULL) // статический буфер, нельзя перевыделить


                     188 

000000b4 e3500000    189 	cmp	r0,0

000000b8 0a000002    190 	beq	.L161

                     191 ;60: 		{


                     192 

                     193 ;61: 			return false;


                     194 

                     195 ;62: 		}


                     196 ;63: 	


                     197 ;64: 		p = OscFiles_realloc(p, newSize);


                     198 

000000bc e1a01006    199 	mov	r1,r6

000000c0 eb000000*   200 	bl	OscFiles_realloc

                     201 ;65: 		if (!p)


                     202 

000000c4 e3500000    203 	cmp	r0,0

                     204 .L161:

                     205 ;66: 		{


                     206 

                     207 ;67: 			return false;


                     208 

000000c8 03a00000    209 	moveq	r0,0

000000cc 0a00000d    210 	beq	.L149

                     211 .L160:

                     212 ;68: 		}


                     213 ;69: 


                     214 ;70: 		wb->p = p;


                     215 

000000d0 e1a01000    216 	mov	r1,r0

000000d4 e8840003    217 	stmea	[r4],{r0-r1}

                     218 ;71: 		wb->data = wb->p;


                     219 

                     220 ;72: 		wb->size = newSize;


                     221 

000000d8 e5846010    222 	str	r6,[r4,16]

000000dc e594300c    223 	ldr	r3,[r4,12]

000000e0 e594c008    224 	ldr	r12,[r4,8]

                     225 .L155:

                     226 ;73: 	}


                     227 ;74: 


                     228 ;75: 


                     229 ;76: 	memcpy(wb->data + wb->dataPos + wb->dataSize, data, size);


                     230 

000000e4 e1a02005    231 	mov	r2,r5

000000e8 e1a01007    232 	mov	r1,r7

000000ec e08c0000    233 	add	r0,r12,r0


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
000000f0 e0830000    234 	add	r0,r3,r0

000000f4 eb000000*   235 	bl	memcpy

                     236 ;77: 	wb->dataSize += size;


                     237 

000000f8 e594000c    238 	ldr	r0,[r4,12]

000000fc e0800005    239 	add	r0,r0,r5

00000100 e584000c    240 	str	r0,[r4,12]

                     241 ;78: 	return true;


                     242 

00000104 e3a00001    243 	mov	r0,1

                     244 .L149:

00000108 e8bd80f0    245 	ldmfd	[sp]!,{r4-r7,pc}

                     246 	.endf	OscWriteBuffer_write

                     247 	.align	4

                     248 ;p	r0	local

                     249 ;newSize	r6	local

                     250 

                     251 ;wb	r4	param

                     252 ;data	r7	param

                     253 ;size	r5	param

                     254 

                     255 	.section ".bss","awb"

                     256 .L235:

                     257 	.data

                     258 	.text

                     259 

                     260 ;79: }


                     261 

                     262 ;80: 


                     263 ;81: bool OscWriteBuffer_toWriteBuffer(OscWriteBuffer *dst, OscWriteBuffer *src)


                     264 	.align	4

                     265 	.align	4

                     266 OscWriteBuffer_toWriteBuffer::

0000010c e92d4070    267 	stmfd	[sp]!,{r4-r6,lr}

00000110 e1a06000    268 	mov	r6,r0

00000114 e1a05001    269 	mov	r5,r1

                     270 ;82: {


                     271 

                     272 ;83: 	unsigned char *data = OscWriteBuffer_data(src);


                     273 

00000118 e1a00005    274 	mov	r0,r5

0000011c eb00001a*   275 	bl	OscWriteBuffer_data

00000120 e1a04000    276 	mov	r4,r0

                     277 ;84: 	size_t size = OscWriteBuffer_dataLen(src);


                     278 

00000124 e1a00005    279 	mov	r0,r5

00000128 eb00001b*   280 	bl	OscWriteBuffer_dataLen

                     281 ;85: 	bool result =  OscWriteBuffer_write(dst,data, size);


                     282 

0000012c e1a01004    283 	mov	r1,r4

00000130 e1a02000    284 	mov	r2,r0

00000134 e1a00006    285 	mov	r0,r6

00000138 ebffffce*   286 	bl	OscWriteBuffer_write

0000013c e1b04000    287 	movs	r4,r0

                     288 ;86: 	if (result)


                     289 

                     290 ;87: 	{


                     291 

                     292 ;88: 		OscWriteBuffer_reset(src);


                     293 

00000140 11a00005    294 	movne	r0,r5


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
00000144 1bffffc7*   295 	blne	OscWriteBuffer_reset

                     296 ;89: 	}


                     297 ;90: 


                     298 ;91: 	return result;


                     299 

00000148 e1a00004    300 	mov	r0,r4

0000014c e8bd8070    301 	ldmfd	[sp]!,{r4-r6,pc}

                     302 	.endf	OscWriteBuffer_toWriteBuffer

                     303 	.align	4

                     304 ;data	r4	local

                     305 ;result	r4	local

                     306 

                     307 ;dst	r6	param

                     308 ;src	r5	param

                     309 

                     310 	.section ".bss","awb"

                     311 .L306:

                     312 	.data

                     313 	.text

                     314 

                     315 ;92: }


                     316 

                     317 ;93: 


                     318 ;94: bool OscWriteBuffer_resize(OscWriteBuffer *wb, int len)


                     319 	.align	4

                     320 	.align	4

                     321 OscWriteBuffer_resize::

                     322 ;95: {


                     323 

                     324 ;96: 	size_t newLen = wb->dataSize + len;


                     325 

00000150 e590200c    326 	ldr	r2,[r0,12]

00000154 e0811002    327 	add	r1,r1,r2

                     328 ;97: 	if (newLen + wb->dataPos > wb->size)


                     329 

00000158 e5902008    330 	ldr	r2,[r0,8]

0000015c e5903010    331 	ldr	r3,[r0,16]

00000160 e0812002    332 	add	r2,r1,r2

00000164 e1520003    333 	cmp	r2,r3

                     334 ;98: 	{


                     335 

                     336 ;99: 		return false;


                     337 

00000168 83a00000    338 	movhi	r0,0

                     339 ;100: 	}


                     340 ;101: 	wb->dataSize += len;


                     341 

0000016c 9580100c    342 	strls	r1,[r0,12]

                     343 ;102: 	return true;


                     344 

00000170 93a00001    345 	movls	r0,1

00000174 e12fff1e*   346 	ret	

                     347 	.endf	OscWriteBuffer_resize

                     348 	.align	4

                     349 

                     350 ;wb	r0	param

                     351 ;len	r1	param

                     352 

                     353 	.section ".bss","awb"

                     354 .L358:

                     355 	.data


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
                     356 	.text

                     357 

                     358 ;103: }


                     359 

                     360 ;104: 


                     361 ;105: bool OscWriteBuffer_empty(OscWriteBuffer *wb) 


                     362 	.align	4

                     363 	.align	4

                     364 OscWriteBuffer_empty::

                     365 ;106: {


                     366 

                     367 ;107: 	return wb->dataSize == 0;


                     368 

00000178 e590000c    369 	ldr	r0,[r0,12]

0000017c e3500000    370 	cmp	r0,0

00000180 03a00001    371 	moveq	r0,1

00000184 13a00000    372 	movne	r0,0

00000188 e12fff1e*   373 	ret	

                     374 	.endf	OscWriteBuffer_empty

                     375 	.align	4

                     376 

                     377 ;wb	r0	param

                     378 

                     379 	.section ".bss","awb"

                     380 .L401:

                     381 	.data

                     382 	.text

                     383 

                     384 ;108: }


                     385 

                     386 ;109: 


                     387 ;110: void * OscWriteBuffer_data(OscWriteBuffer *wb)


                     388 	.align	4

                     389 	.align	4

                     390 OscWriteBuffer_data::

                     391 ;111: {


                     392 

                     393 ;112: 	return wb->data + wb->dataPos;


                     394 

0000018c e5901008    395 	ldr	r1,[r0,8]

00000190 e5902000    396 	ldr	r2,[r0]

00000194 e0810002    397 	add	r0,r1,r2

00000198 e12fff1e*   398 	ret	

                     399 	.endf	OscWriteBuffer_data

                     400 	.align	4

                     401 

                     402 ;wb	r0	param

                     403 

                     404 	.section ".bss","awb"

                     405 .L430:

                     406 	.data

                     407 	.text

                     408 

                     409 ;113: }


                     410 

                     411 ;114: 


                     412 ;115: size_t OscWriteBuffer_dataLen(OscWriteBuffer *wb)


                     413 	.align	4

                     414 	.align	4

                     415 OscWriteBuffer_dataLen::

                     416 ;116: {



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
                     417 

                     418 ;117: 	return wb->dataSize;


                     419 

0000019c e590000c    420 	ldr	r0,[r0,12]

000001a0 e12fff1e*   421 	ret	

                     422 	.endf	OscWriteBuffer_dataLen

                     423 	.align	4

                     424 

                     425 ;wb	r0	param

                     426 

                     427 	.section ".bss","awb"

                     428 .L462:

                     429 	.data

                     430 	.text

                     431 

                     432 ;118: }


                     433 

                     434 ;119: 


                     435 ;120: size_t OscWriteBuffer_size(OscWriteBuffer *wb)


                     436 	.align	4

                     437 	.align	4

                     438 OscWriteBuffer_size::

                     439 ;121: {


                     440 

                     441 ;122: 	return wb->size - wb->dataSize;


                     442 

000001a4 e5901010    443 	ldr	r1,[r0,16]

000001a8 e590000c    444 	ldr	r0,[r0,12]

000001ac e0410000    445 	sub	r0,r1,r0

000001b0 e12fff1e*   446 	ret	

                     447 	.endf	OscWriteBuffer_size

                     448 	.align	4

                     449 

                     450 ;wb	r0	param

                     451 

                     452 	.section ".bss","awb"

                     453 .L494:

                     454 	.data

                     455 	.text

                     456 

                     457 ;123: }


                     458 

                     459 ;124: 


                     460 ;125: void OscWriteBuffer_destroy(OscWriteBuffer *wb)


                     461 	.align	4

                     462 	.align	4

                     463 OscWriteBuffer_destroy::

                     464 ;126: {


                     465 

                     466 ;127: 	// буфер создан через выделение памяти


                     467 ;128: 	if (wb->p)


                     468 

000001b4 e5900004    469 	ldr	r0,[r0,4]

000001b8 e3500000    470 	cmp	r0,0

                     471 ;129: 	{


                     472 

                     473 ;130: 		OscFiles_free(wb->p);


                     474 

000001bc 1a000000*   475 	bne	OscFiles_free

000001c0 e12fff1e*   476 	ret	

                     477 	.endf	OscWriteBuffer_destroy


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
                     478 	.align	4

                     479 

                     480 ;wb	r0	param

                     481 

                     482 	.section ".bss","awb"

                     483 .L536:

                     484 	.data

                     485 	.text

                     486 

                     487 ;132: 	}


                     488 ;133: 	// буфер создан OscWriteBuffer_attach


                     489 ;134: }


                     490 

                     491 ;135: 


                     492 ;136: size_t OscWriteBuffer_toBufferView(BufferView *bv, OscWriteBuffer *wb)


                     493 	.align	4

                     494 	.align	4

                     495 OscWriteBuffer_toBufferView::

000001c4 e92d4070    496 	stmfd	[sp]!,{r4-r6,lr}

000001c8 e1a04000    497 	mov	r4,r0

000001cc e1a05001    498 	mov	r5,r1

                     499 ;137: {


                     500 

                     501 ;138: 	int writenSize;


                     502 ;139: 


                     503 ;140: 	if (OscWriteBuffer_empty(wb))


                     504 

000001d0 e1a00005    505 	mov	r0,r5

000001d4 ebffffe7*   506 	bl	OscWriteBuffer_empty

000001d8 e3500000    507 	cmp	r0,0

                     508 ;141: 	{


                     509 

                     510 ;142: 		return 0;


                     511 

000001dc 13a00000    512 	movne	r0,0

000001e0 1a000012    513 	bne	.L547

                     514 ;143: 	}


                     515 ;144: 


                     516 ;145: 	writenSize = BufferView_writeData(bv, OscWriteBuffer_data(wb),


                     517 

000001e4 e1a00005    518 	mov	r0,r5

000001e8 ebffffe7*   519 	bl	OscWriteBuffer_data

000001ec e1a06000    520 	mov	r6,r0

000001f0 e1a00005    521 	mov	r0,r5

000001f4 ebffffe8*   522 	bl	OscWriteBuffer_dataLen

000001f8 e1a01006    523 	mov	r1,r6

000001fc e1a02000    524 	mov	r2,r0

00000200 e1a00004    525 	mov	r0,r4

00000204 eb000000*   526 	bl	BufferView_writeData

00000208 e5951008    527 	ldr	r1,[r5,8]

0000020c e1a04000    528 	mov	r4,r0

                     529 ;146: 		OscWriteBuffer_dataLen(wb));


                     530 ;147: 	wb->dataPos += writenSize;


                     531 

00000210 e0811004    532 	add	r1,r1,r4

00000214 e5851008    533 	str	r1,[r5,8]

                     534 ;148: 	wb->dataSize -= writenSize;


                     535 

00000218 e595100c    536 	ldr	r1,[r5,12]

0000021c e0511004    537 	subs	r1,r1,r4

00000220 e585100c    538 	str	r1,[r5,12]


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
                     539 ;149: 


                     540 ;150: 	// все записали


                     541 ;151: 	if (wb->dataSize == 0)


                     542 

                     543 ;152: 	{


                     544 

                     545 ;153: 		OscWriteBuffer_reset(wb);


                     546 

00000224 01a00005    547 	moveq	r0,r5

00000228 0bffff8e*   548 	bleq	OscWriteBuffer_reset

                     549 ;154: 	}


                     550 ;155: 	return writenSize;


                     551 

0000022c e1a00004    552 	mov	r0,r4

                     553 .L547:

00000230 e8bd8070    554 	ldmfd	[sp]!,{r4-r6,pc}

                     555 	.endf	OscWriteBuffer_toBufferView

                     556 	.align	4

                     557 ;writenSize	r4	local

                     558 

                     559 ;bv	r4	param

                     560 ;wb	r5	param

                     561 

                     562 	.section ".bss","awb"

                     563 .L599:

                     564 	.data

                     565 	.text

                     566 

                     567 ;156: }


                     568 	.align	4

                     569 

                     570 	.data

                     571 	.ghsnote version,6

                     572 	.ghsnote tools,3

                     573 	.ghsnote options,0

                     574 	.text

                     575 	.align	4

