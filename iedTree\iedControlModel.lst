                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedControlModel.c -o iedTree\gh_crc1.o -list=iedTree/iedControlModel.lst C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
Source File: iedControlModel.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		iedTree/iedControlModel.c -o iedTree/iedControlModel.o

                      12 ;Source File:   iedTree/iedControlModel.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:49 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "iedControlModel.h"


                      23 ;2: 


                      24 ;3: 


                      25 ;4: 


                      26 ;5: #include "../iedmodel.h"


                      27 ;6: #include "../timers.h"


                      28 ;7: #include "iedFinalDA.h"


                      29 ;8: #include "iedObjects.h"


                      30 ;9: #include "../mms_data.h"


                      31 ;10: #include "../pwin_access.h"


                      32 ;11: #include "../control.h"


                      33 ;12: 


                      34 ;13: #include "../AsnEncoding.h"


                      35 ;14: 


                      36 ;15: #include "IEDCompile/AccessInfo.h"


                      37 ;16: #include "IEDCompile/InnerAttributeTypes.h"


                      38 ;17: 


                      39 ;18: #include <Clib.h>


                      40 ;19: 


                      41 ;20: #include <string.h>


                      42 ;21: 


                      43 ;22: //Сколько ждать готовности после передачи сигнала объекта управления


                      44 ;23: //в алгоритмы.


                      45 ;24: //Таймаут сделан для избежания зависания протокола при ошибке в алгоритмах.


                      46 ;25: #define CTRL_READY_TIMEOUT 5000


                      47 ;26: 


                      48 ;27: typedef struct {


                      49 ;28:     size_t len;


                      50 ;29:     char orIdent[64];



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
                      51 ;30: } IEDOrIdent;


                      52 ;31: 


                      53 ;32: typedef struct {


                      54 ;33:     int orCat;


                      55 ;34: } IEDOrCat;


                      56 ;35: 


                      57 ;36: typedef enum {


                      58 ;37:     CONTROL_SBOW,


                      59 ;38:     CONTROL_OPER,


                      60 ;39:     CONTROL_CANCEL


                      61 ;40: } ControlType;


                      62 ;41: 


                      63 ;42: typedef struct {


                      64 ;43:     IEDOrIdent *orIdent;


                      65 ;44:     IEDOrCat *orCat;


                      66 ;45:     ControlType type;


                      67 ;46:     //true если операция в процессе выполнения и ожидает


                      68 ;47:     //сигнала terminate


                      69 ;48:     bool waitTerminate;


                      70 ;49:     //Соединение, в которое отправлять отчёт


                      71 ;50:     IsoConnection* isoConn;


                      72 ;51:     IntBoolAccessInfo* code;


                      73 ;52:     IntBoolAccessInfo* ready;


                      74 ;53:     IntBoolAccessInfo* ctlValOne;


                      75 ;54:     IntBoolAccessInfo* ctlValZero;


                      76 ;55:     IntBoolAccessInfo* terminate;


                      77 ;56: } IEDControlDA;


                      78 ;57: 


                      79 ;58: typedef struct {


                      80 ;59:     //Пока просто какой-то мусор


                      81 ;60:     int reserved;


                      82 ;61: } IEDControlDO;


                      83 ;62: 


                      84 ;63: 


                      85 ;64: bool IEDControlDA_isControlDA(IEDEntity entity)


                      86 ;65: {


                      87 ;66:     return entity->type == IED_ENTITY_DA


                      88 ;67:             && entity->subType == DA_SUBTYPE_CONTROL;


                      89 ;68: }


                      90 ;69: 


                      91 ;70: bool IEDControlDA_isReady(IEDEntity controlDA)


                      92 ;71: {


                      93 ;72:     IEDControlDA *extInfo;


                      94 ;73:     if(!IEDControlDA_isControlDA(controlDA))


                      95 ;74:     {


                      96 ;75:         ERROR_REPORT("Invalid control DA");


                      97 ;76:         return false;


                      98 ;77:     }


                      99 ;78:     extInfo = controlDA->extInfo;


                     100 ;79:     return readBoolValue(extInfo->ready);


                     101 ;80: }


                     102 ;81: 


                     103 ;82: bool IEDControlDA_waitReady(IEDEntity controlDA)


                     104 ;83: {


                     105 ;84:     uint32_t startTime;


                     106 ;85:     IEDControlDA *extInfo;


                     107 ;86:     if(!IEDControlDA_isControlDA(controlDA))


                     108 ;87:     {


                     109 ;88:         ERROR_REPORT("Invalid control DA");


                     110 ;89:         return false;


                     111 ;90:     }



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
                     112 ;91:     extInfo = controlDA->extInfo;


                     113 ;92: 


                     114 ;93:     startTime = Timers_getTickCount();


                     115 ;94:     while(!readBoolValue(extInfo->ready))


                     116 ;95:     {


                     117 ;96:         if(Timers_isTimeout(startTime, CTRL_READY_TIMEOUT))


                     118 ;97:         {


                     119 ;98:             ERROR_REPORT("Timeout in IEDControlDA_waitReady");


                     120 ;99:             return false;


                     121 ;100:         }


                     122 ;101:         Idle();


                     123 ;102:     };


                     124 ;103:     return true;


                     125 ;104: }


                     126 ;105: 


                     127 ;106: void IEDControlDA_checkTerminate(IEDEntity controlDA)


                     128 ;107: {


                     129 ;108:     IEDControlDA* extInfo;


                     130 ;109:     uint8_t terminateCode;


                     131 ;110: 


                     132 ;111:     if(!IEDControlDA_isControlDA(controlDA))


                     133 ;112:     {


                     134 ;113:         return;


                     135 ;114:     }


                     136 ;115:     extInfo = controlDA->extInfo;


                     137 ;116:     if(extInfo->type != CONTROL_OPER)


                     138 ;117:     {


                     139 ;118:         return;


                     140 ;119:     }


                     141 ;120: 


                     142 ;121:     if(!extInfo->waitTerminate)


                     143 ;122:     {


                     144 ;123:         return;


                     145 ;124:     }


                     146 ;125: 


                     147 ;126:     if(!readBoolValue(extInfo->terminate))


                     148 ;127:     {


                     149 ;128:         return;


                     150 ;129:     }


                     151 ;130: 


                     152 ;131:     extInfo->waitTerminate = false;


                     153 ;132:     if(extInfo->isoConn == NULL)


                     154 ;133:     {


                     155 ;134:         return;


                     156 ;135:     }


                     157 ;136:     terminateCode = readIntValue(extInfo->code);


                     158 ;137: 


                     159 ;138:     if(terminateCode == 0)


                     160 ;139:     {


                     161 ;140:         Control_sendPositiveCmdTermReport(extInfo->isoConn, controlDA);


                     162 ;141:     }


                     163 ;142:     else


                     164 ;143:     {


                     165 ;144:         Control_sendNegativeCmdTermReport(extInfo->isoConn, controlDA,


                     166 ;145:                                           terminateCode);


                     167 ;146:     }


                     168 ;147:     extInfo->isoConn = NULL;


                     169 ;148: }


                     170 ;149: 


                     171 ;150: void IEDControlDA_disconnect(IEDEntity controlDA)


                     172 ;151: {



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
                     173 ;152:     IEDControlDA* extInfo;


                     174 ;153: 


                     175 ;154:     if(!IEDControlDA_isControlDA(controlDA))


                     176 ;155:     {


                     177 ;156:         return;


                     178 ;157:     }


                     179 ;158:     extInfo = controlDA->extInfo;


                     180 ;159:     if(extInfo->type != CONTROL_OPER)


                     181 ;160:     {


                     182 ;161:         return;


                     183 ;162:     }


                     184 ;163: 


                     185 ;164:     if(!extInfo->waitTerminate || extInfo->isoConn == NULL)


                     186 ;165:     {


                     187 ;166:         return;


                     188 ;167:     }


                     189 ;168: 


                     190 ;169:     extInfo->waitTerminate = false;


                     191 ;170:     extInfo->isoConn = NULL;


                     192 ;171: }


                     193 ;172: 


                     194 ;173: bool IEDControlDA_getOrIdent(IEDEntity entity, StringView *orIdent)


                     195 ;174: {


                     196 ;175:     IEDControlDA *extInfo;


                     197 ;176:     if(!IEDControlDA_isControlDA(entity))


                     198 ;177:     {


                     199 ;178:         return false;


                     200 ;179:     }


                     201 ;180: 


                     202 ;181:     extInfo = entity->extInfo;


                     203 ;182:     if(extInfo->orIdent == NULL)


                     204 ;183:     {


                     205 ;184:         return false;


                     206 ;185:     }


                     207 ;186: 


                     208 ;187:     StringView_init(orIdent, extInfo->orIdent->orIdent, extInfo->orIdent->len);


                     209 ;188:     return true;


                     210 ;189: }


                     211 ;190: 


                     212 ;191: bool IEDControlDA_getOrCat(IEDEntity entity, int32_t *orCat)


                     213 ;192: {


                     214 ;193:     IEDControlDA *extInfo;


                     215 ;194:     if(!IEDControlDA_isControlDA(entity))


                     216 ;195:     {


                     217 ;196:         return false;


                     218 ;197:     }


                     219 ;198: 


                     220 ;199:     extInfo = entity->extInfo;


                     221 ;200:     if(extInfo->orCat == NULL)


                     222 ;201:     {


                     223 ;202:         return false;


                     224 ;203:     }


                     225 ;204: 


                     226 ;205:     *orCat = extInfo->orCat->orCat;


                     227 ;206: 


                     228 ;207:     return true;


                     229 ;208: }


                     230 ;209: 


                     231 ;210: bool IEDOrCat_calcReadLen(IEDEntity entity, size_t *pLen)


                     232 ;211: {


                     233 ;212:     IEDOrCat *extInfo;



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
                     234 ;213: 


                     235 ;214:     if(entity->subType != DA_SUBTYPE_ORCAT)


                     236 ;215:     {


                     237 ;216:         return false;


                     238 ;217:     }


                     239 ;218: 


                     240 ;219:     extInfo = entity->extInfo;


                     241 ;220: 


                     242 ;221:     *pLen = 2 + BerEncoder_Int32DetermineEncodedSize(extInfo->orCat);


                     243 ;222: 


                     244 ;223:     return true;


                     245 ;224: }


                     246 ;225: 


                     247 ;226: MmsDataAccessError IEDOrCat_write(IEDEntity entity,


                     248 ;227:                                            IsoConnection* isoConn, BufferView* value)


                     249 ;228: {


                     250 ;229:     int32_t decodedVal;


                     251 ;230:     IEDOrCat *extInfo;


                     252 ;231: 


                     253 ;232:     if(entity->type != IED_ENTITY_DA_VAR


                     254 ;233:             || entity->subType != DA_SUBTYPE_ORCAT)


                     255 ;234:     {


                     256 ;235:         return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;


                     257 ;236:     }


                     258 ;237: 


                     259 ;238:     if(!BufferView_decodeInt32TL(value, IEC61850_BER_INTEGER, &decodedVal))


                     260 ;239:     {


                     261 ;240:         return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                     262 ;241:     }


                     263 ;242: 


                     264 ;243:     extInfo = entity->extInfo;


                     265 ;244: 


                     266 ;245:     extInfo->orCat = decodedVal;


                     267 ;246: 


                     268 ;247:     return DATA_ACCESS_ERROR_SUCCESS;


                     269 ;248: }


                     270 ;249: 


                     271 ;250: 


                     272 ;251: bool IEDOrIdent_init(IEDEntity entity)


                     273 ;252: {


                     274 ;253:     IEDOrIdent* extInfo;


                     275 ;254:     entity->type = IED_ENTITY_DA_VAR;


                     276 ;255:     entity->subType = DA_SUBTYPE_ORIDENT;


                     277 ;256:     extInfo = IEDEntity_alloc(sizeof(IEDOrIdent));


                     278 ;257:     if(extInfo == NULL)


                     279 ;258:     {


                     280 ;259:         return false;


                     281 ;260:     }


                     282 ;261:     entity->extInfo = extInfo;


                     283 ;262:     return true;


                     284 ;263: }


                     285 ;264: 


                     286 ;265: bool IEDOrCat_init(IEDEntity entity)


                     287 ;266: {


                     288 ;267:     IEDOrCat* extInfo;


                     289 ;268:     entity->type = IED_ENTITY_DA_VAR;


                     290 ;269:     entity->subType = DA_SUBTYPE_ORCAT;


                     291 ;270:     extInfo = IEDEntity_alloc(sizeof(IEDOrCat));


                     292 ;271:     if(extInfo == NULL)


                     293 ;272:     {


                     294 ;273:         return false;



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
                     295 ;274:     }


                     296 ;275: 


                     297 ;276:     entity->extInfo = extInfo;


                     298 ;277:     return true;


                     299 ;278: }


                     300 ;279: 


                     301 ;280: bool IEDCtlNum_init(IEDEntity entity,BufferView* ber)


                     302 ;281: {	


                     303 ;282: 	//!!! Всё это временно пока нет ясности с ctlNum


                     304 ;283: 


                     305 ;284: 	entity->type = IED_ENTITY_DA_CONST;	


                     306 ;285: 	//Сохраняем позицию константы в информационной модели


                     307 ;286: 	entity->extInfo = (void*)(ber->p + ber->pos - iedModel);


                     308 ;287: 	return true;


                     309 ;288: }


                     310 ;289: 


                     311 ;290: bool IEDOrCat_encodeRead(IEDEntity entity, BufferView *outBuf)


                     312 ;291: {


                     313 ;292:     IEDOrCat *extInfo;


                     314 ;293: 


                     315 ;294:     if(entity->subType != DA_SUBTYPE_ORCAT)


                     316 ;295:     {


                     317 ;296:         return false;


                     318 ;297:     }


                     319 ;298: 


                     320 ;299:     extInfo = entity->extInfo;


                     321 ;300: 


                     322 ;301:     return BufferView_encodeInt32(outBuf, IEC61850_BER_INTEGER,


                     323 ;302:                                   extInfo->orCat);


                     324 ;303: }


                     325 ;304: 


                     326 ;305: bool IEDOrIdent_calcReadLen(IEDEntity entity, size_t *pLen)


                     327 ;306: {


                     328 ;307:     IEDOrIdent *extInfo;


                     329 ;308: 


                     330 ;309:     if(entity->subType != DA_SUBTYPE_ORIDENT)


                     331 ;310:     {


                     332 ;311:         return false;


                     333 ;312:     }


                     334 ;313:     extInfo = entity->extInfo;


                     335 ;314: 


                     336 ;315:     *pLen = extInfo->len + 2;


                     337 ;316: 


                     338 ;317:     return true;


                     339 ;318: }


                     340 ;319: 


                     341 ;320: bool IEDOrIdent_encodeRead(IEDEntity entity, BufferView *outBuf)


                     342 ;321: {


                     343 ;322:     IEDOrIdent *extInfo;


                     344 ;323: 


                     345 ;324:     if(entity->subType != DA_SUBTYPE_ORIDENT)


                     346 ;325:     {


                     347 ;326:         return false;


                     348 ;327:     }


                     349 ;328: 


                     350 ;329:     extInfo = entity->extInfo;


                     351 ;330: 


                     352 ;331:     return BufferView_encodeOctetString(outBuf, IEC61850_BER_OCTET_STRING,


                     353 ;332:                                      extInfo->orIdent, extInfo->len);


                     354 ;333: }


                     355 ;334: 



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
                     356 ;335: MmsDataAccessError IEDOrIdent_write(IEDEntity entity,


                     357 ;336:                                            IsoConnection* isoConn, BufferView* value)


                     358 ;337: {


                     359 ;338:     StringView decodedVal;


                     360 ;339:     IEDOrIdent *extInfo;


                     361 ;340: 


                     362 ;341:     if(entity->type != IED_ENTITY_DA_VAR


                     363 ;342:             || entity->subType != DA_SUBTYPE_ORIDENT)


                     364 ;343:     {


                     365 ;344:         return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;


                     366 ;345:     }


                     367 ;346: 


                     368 ;347: 


                     369 ;348:     if(!BufferView_decodeStringViewTL(value, IEC61850_BER_OCTET_STRING,


                     370 ;349:                                   &decodedVal))


                     371 ;350:     {


                     372 ;351:         return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                     373 ;352:     }


                     374 ;353: 


                     375 ;354:     if(decodedVal.len > 64)


                     376 ;355:     {


                     377 ;356:         return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                     378 ;357:     }


                     379 ;358: 


                     380 ;359:     extInfo = entity->extInfo;


                     381 ;360: 


                     382 ;361:     extInfo->len = decodedVal.len;


                     383 ;362:     if(extInfo->len != 0)


                     384 ;363:     {


                     385 ;364:         memcpy(extInfo->orIdent, decodedVal.p, extInfo->len);


                     386 ;365:     }


                     387 ;366: 


                     388 ;367:     return DATA_ACCESS_ERROR_SUCCESS;


                     389 ;368: }


                     390 ;369: 


                     391 ;370: bool IEDControlDA_init(IEDEntity entity, BufferView *ctrlInfoBER)


                     392 ;371: {


                     393 ;372:     uint8_t tag;


                     394 ;373:     size_t len;


                     395 ;374:     IEDEntity origin;


                     396 ;375:     IEDEntity orCat;


                     397 ;376:     IEDEntity orIdent;


                     398 ;377: 


                     399 ;378:     IEDControlDA* extInfo = IEDEntity_alloc(sizeof(IEDControlDA));


                     400 ;379:     if(extInfo == NULL)


                     401 ;380:     {


                     402 ;381:         return false;


                     403 ;382:     }


                     404 ;383:     entity->extInfo = extInfo;


                     405 ;384: 


                     406 ;385:     //Подтип


                     407 ;386:     entity->subType = DA_SUBTYPE_CONTROL;


                     408 ;387: 


                     409 ;388:     //По имени определить тип


                     410 ;389:     if(StringView_cmpCStr(&entity->name, "SBOw") == 0)


                     411 ;390:     {


                     412 ;391:         extInfo->type = CONTROL_SBOW;


                     413 ;392:     }


                     414 ;393:     else if(StringView_cmpCStr(&entity->name, "Oper") == 0)


                     415 ;394:     {


                     416 ;395:         extInfo->type = CONTROL_OPER;



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
                     417 ;396:     }


                     418 ;397:     else if(StringView_cmpCStr(&entity->name, "Cancel") == 0)


                     419 ;398:     {


                     420 ;399:         extInfo->type = CONTROL_CANCEL;


                     421 ;400:     }


                     422 ;401:     else


                     423 ;402:     {


                     424 ;403:         ERROR_REPORT("Invalid DA name");


                     425 ;404:         return false;


                     426 ;405:     }


                     427 ;406: 


                     428 ;407:     if(!BufferView_decodeTL(ctrlInfoBER, &tag, &len, NULL))


                     429 ;408:     {


                     430 ;409:         ERROR_REPORT("Invalid Ctrl info");


                     431 ;410:         return false;


                     432 ;411:     }


                     433 ;412: 


                     434 ;413:     if(tag != IED_CONTROL_INFO)


                     435 ;414:     {


                     436 ;415:         //Нет дополнительной информации


                     437 ;416:         return true;


                     438 ;417:     }


                     439 ;418:     //Подобъекты дополнительной информации


                     440 ;419:     BufferView_init(ctrlInfoBER, ctrlInfoBER->p + ctrlInfoBER->pos, len, 0);


                     441 ;420: 


                     442 ;421:     //Доступ к коду ошибки


                     443 ;422:     if(!IEDModel_getTermItemDescrStruct(ctrlInfoBER, (void**)&extInfo->code))


                     444 ;423:     {


                     445 ;424:         return false;


                     446 ;425:     }


                     447 ;426: 


                     448 ;427:     //Доступ к сигналу готовности


                     449 ;428:     if(!IEDModel_getTermItemDescrStruct(ctrlInfoBER, (void**)&extInfo->ready))


                     450 ;429:     {


                     451 ;430:         return false;


                     452 ;431:     }


                     453 ;432: 


                     454 ;433:     // Доступ к "1"


                     455 ;434:     if(!IEDModel_getTermItemDescrStruct(ctrlInfoBER, (void**)&extInfo->ctlValOne))


                     456 ;435:     {


                     457 ;436:         return false;


                     458 ;437:     }


                     459 ;438: 


                     460 ;439: 


                     461 ;440: 


                     462 ;441:     // Доступ к "0"


                     463 ;442:     if(!IEDModel_getTermItemDescrStruct(ctrlInfoBER,


                     464 ;443:                                        (void**)&extInfo->ctlValZero))


                     465 ;444:     {


                     466 ;445:         return false;


                     467 ;446:     }


                     468 ;447: 


                     469 ;448:     if(extInfo->type == CONTROL_OPER)


                     470 ;449:     {


                     471 ;450:         // Доступ к готовности Terminate


                     472 ;451:         if(!IEDModel_getTermItemDescrStruct(ctrlInfoBER,


                     473 ;452:                                            (void**)&extInfo->terminate))


                     474 ;453:         {


                     475 ;454:             return false;


                     476 ;455:         }


                     477 ;456:         if(!Control_registerCtrlObj(entity))



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
                     478 ;457:         {


                     479 ;458:             return false;


                     480 ;459:         }


                     481 ;460:     }


                     482 ;461: 


                     483 ;462:     origin = IEDEntity_getChildByCStrName(entity, "origin");


                     484 ;463:     if(origin == NULL)


                     485 ;464:     {


                     486 ;465:         return false;


                     487 ;466:     }


                     488 ;467: 


                     489 ;468:     orIdent = IEDEntity_getChildByCStrName(origin, "orIdent");


                     490 ;469:     if(orIdent != NULL)


                     491 ;470:     {


                     492 ;471:         extInfo->orIdent = orIdent->extInfo;


                     493 ;472:     }


                     494 ;473: 


                     495 ;474:     orCat = IEDEntity_getChildByCStrName(origin, "orCat");


                     496 ;475:     if(orCat != NULL)


                     497 ;476:     {


                     498 ;477:         extInfo->orCat = orCat->extInfo;


                     499 ;478:     }


                     500 ;479: 


                     501 ;480:     return true;


                     502 ;481: }


                     503 ;482: 


                     504 ;483: bool IEDControlDO_init(IEDEntity entity)


                     505 ;484: {


                     506 ;485:     IEDControlDO *extInfo;


                     507 ;486:     entity->type = IED_ENTITY_DO;


                     508 ;487:     extInfo = IEDEntity_alloc(sizeof(IEDControlDO));


                     509 ;488:     entity->subType = DO_SUBTYPE_CONTROL;


                     510 ;489:     if(extInfo == NULL)


                     511 ;490:     {


                     512 ;491:         return false;


                     513 ;492:     }


                     514 ;493:     return true;


                     515 ;494: }


                     516 ;495: 


                     517 ;496: static bool writeCtlValue(IEDControlDA* ctrlInfo, const BufferView* value)


                     518 

                     519 ;513: }


                     520 

                     521 	.text

                     522 	.align	4

                     523 IEDControlDA_isControlDA::

00000000 e1a01000    524 	mov	r1,r0

00000004 e5912050    525 	ldr	r2,[r1,80]

00000008 e3a00000    526 	mov	r0,0

0000000c e3520006    527 	cmp	r2,6

00000010 1a000002    528 	bne	.L30

00000014 e5910054    529 	ldr	r0,[r1,84]

00000018 e3500001    530 	cmp	r0,1

0000001c 13a00000    531 	movne	r0,0

                     532 .L30:

00000020 e20000ff    533 	and	r0,r0,255

00000024 e12fff1e*   534 	ret	

                     535 	.endf	IEDControlDA_isControlDA

                     536 	.align	4

                     537 

                     538 ;entity	r1	param


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
                     539 

                     540 	.section ".bss","awb"

                     541 .L82:

                     542 	.data

                     543 	.text

                     544 

                     545 

                     546 	.align	4

                     547 	.align	4

                     548 IEDControlDA_isReady::

00000028 e92d4010    549 	stmfd	[sp]!,{r4,lr}

0000002c e1a04000    550 	mov	r4,r0

00000030 ebfffff2*   551 	bl	IEDControlDA_isControlDA

00000034 e3500000    552 	cmp	r0,0

00000038 0a000003    553 	beq	.L93

0000003c e5941058    554 	ldr	r1,[r4,88]

00000040 e5910018    555 	ldr	r0,[r1,24]

00000044 eb000000*   556 	bl	readBoolValue

00000048 e20000ff    557 	and	r0,r0,255

                     558 .L93:

0000004c e8bd8010    559 	ldmfd	[sp]!,{r4,pc}

                     560 	.endf	IEDControlDA_isReady

                     561 	.align	4

                     562 ;extInfo	r1	local

                     563 

                     564 ;controlDA	r4	param

                     565 

                     566 	.section ".bss","awb"

                     567 .L154:

                     568 	.data

                     569 	.text

                     570 

                     571 

                     572 	.align	4

                     573 	.align	4

                     574 IEDControlDA_waitReady::

00000050 e92d4070    575 	stmfd	[sp]!,{r4-r6,lr}

00000054 e1a04000    576 	mov	r4,r0

00000058 ebffffe8*   577 	bl	IEDControlDA_isControlDA

0000005c e3500000    578 	cmp	r0,0

00000060 1a000001    579 	bne	.L170

                     580 .L171:

00000064 e3a00000    581 	mov	r0,0

00000068 ea000013    582 	b	.L168

                     583 .L170:

0000006c e3a06d4c    584 	mov	r6,19<<8

00000070 e5945058    585 	ldr	r5,[r4,88]

00000074 eb000000*   586 	bl	Timers_getTickCount

00000078 e1a04000    587 	mov	r4,r0

0000007c e5950018    588 	ldr	r0,[r5,24]

00000080 e2866088    589 	add	r6,r6,136

00000084 eb000000*   590 	bl	readBoolValue

00000088 e3500000    591 	cmp	r0,0

0000008c 1a000009    592 	bne	.L174

                     593 .L175:

00000090 e1a01006    594 	mov	r1,r6

00000094 e1a00004    595 	mov	r0,r4

00000098 eb000000*   596 	bl	Timers_isTimeout

0000009c e3500000    597 	cmp	r0,0

000000a0 1affffef    598 	bne	.L171

000000a4 e6000010    599 	.word	0xE6000010


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
                     600 

000000a8 e5950018    601 	ldr	r0,[r5,24]

000000ac eb000000*   602 	bl	readBoolValue

000000b0 e3500000    603 	cmp	r0,0

000000b4 0afffff5    604 	beq	.L175

                     605 .L174:

000000b8 e3a00001    606 	mov	r0,1

                     607 .L168:

000000bc e8bd8070    608 	ldmfd	[sp]!,{r4-r6,pc}

                     609 	.endf	IEDControlDA_waitReady

                     610 	.align	4

                     611 ;startTime	r4	local

                     612 ;extInfo	r5	local

                     613 

                     614 ;controlDA	r4	param

                     615 

                     616 	.section ".bss","awb"

                     617 .L254:

                     618 	.data

                     619 	.text

                     620 

                     621 

                     622 	.align	4

                     623 	.align	4

                     624 IEDControlDA_checkTerminate::

000000c0 e92d4030    625 	stmfd	[sp]!,{r4-r5,lr}

000000c4 e1a05000    626 	mov	r5,r0

000000c8 ebffffcc*   627 	bl	IEDControlDA_isControlDA

000000cc e3500000    628 	cmp	r0,0

000000d0 0a00001b    629 	beq	.L272

000000d4 e5954058    630 	ldr	r4,[r5,88]

000000d8 e5940008    631 	ldr	r0,[r4,8]

000000dc e3500001    632 	cmp	r0,1

000000e0 1a000017    633 	bne	.L272

000000e4 e5d4000c    634 	ldrb	r0,[r4,12]

000000e8 e3500000    635 	cmp	r0,0

000000ec 0a000014    636 	beq	.L272

000000f0 e5940024    637 	ldr	r0,[r4,36]

000000f4 eb000000*   638 	bl	readBoolValue

000000f8 e3500000    639 	cmp	r0,0

000000fc 13a00000    640 	movne	r0,0

00000100 15c4000c    641 	strneb	r0,[r4,12]

00000104 15940010    642 	ldrne	r0,[r4,16]

00000108 13500000    643 	cmpne	r0,0

0000010c 0a00000c    644 	beq	.L272

00000110 e5940014    645 	ldr	r0,[r4,20]

00000114 eb000000*   646 	bl	readIntValue

00000118 e1a01005    647 	mov	r1,r5

0000011c e21020ff    648 	ands	r2,r0,255

00000120 e5940010    649 	ldr	r0,[r4,16]

00000124 1a000003    650 	bne	.L289

00000128 eb000000*   651 	bl	Control_sendPositiveCmdTermReport

0000012c e3a00000    652 	mov	r0,0

00000130 e5840010    653 	str	r0,[r4,16]

00000134 ea000002    654 	b	.L272

                     655 .L289:

00000138 eb000000*   656 	bl	Control_sendNegativeCmdTermReport

0000013c e3a00000    657 	mov	r0,0

00000140 e5840010    658 	str	r0,[r4,16]

                     659 .L272:

00000144 e8bd8030    660 	ldmfd	[sp]!,{r4-r5,pc}


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
                     661 	.endf	IEDControlDA_checkTerminate

                     662 	.align	4

                     663 ;extInfo	r4	local

                     664 ;terminateCode	r2	local

                     665 

                     666 ;controlDA	r5	param

                     667 

                     668 	.section ".bss","awb"

                     669 .L371:

                     670 	.data

                     671 	.text

                     672 

                     673 

                     674 	.align	4

                     675 	.align	4

                     676 IEDControlDA_disconnect::

00000148 e92d4010    677 	stmfd	[sp]!,{r4,lr}

0000014c e1a04000    678 	mov	r4,r0

00000150 ebffffaa*   679 	bl	IEDControlDA_isControlDA

00000154 e3500000    680 	cmp	r0,0

00000158 0a00000a    681 	beq	.L391

0000015c e5941058    682 	ldr	r1,[r4,88]

00000160 e5910008    683 	ldr	r0,[r1,8]

00000164 e3500001    684 	cmp	r0,1

00000168 1a000006    685 	bne	.L391

0000016c e5d1000c    686 	ldrb	r0,[r1,12]

00000170 e3500000    687 	cmp	r0,0

00000174 15910010    688 	ldrne	r0,[r1,16]

00000178 13500000    689 	cmpne	r0,0

0000017c 13a02000    690 	movne	r2,0

00000180 15c1200c    691 	strneb	r2,[r1,12]

00000184 15812010    692 	strne	r2,[r1,16]

                     693 .L391:

00000188 e8bd8010    694 	ldmfd	[sp]!,{r4,pc}

                     695 	.endf	IEDControlDA_disconnect

                     696 	.align	4

                     697 ;extInfo	r1	local

                     698 

                     699 ;controlDA	r4	param

                     700 

                     701 	.section ".bss","awb"

                     702 .L461:

                     703 	.data

                     704 	.text

                     705 

                     706 

                     707 	.align	4

                     708 	.align	4

                     709 IEDControlDA_getOrIdent::

0000018c e92d4030    710 	stmfd	[sp]!,{r4-r5,lr}

00000190 e1a05001    711 	mov	r5,r1

00000194 e1a04000    712 	mov	r4,r0

00000198 ebffff98*   713 	bl	IEDControlDA_isControlDA

0000019c e3500000    714 	cmp	r0,0

000001a0 15942058    715 	ldrne	r2,[r4,88]

000001a4 15923000    716 	ldrne	r3,[r2]

000001a8 13530000    717 	cmpne	r3,0

000001ac 03a00000    718 	moveq	r0,0

000001b0 0a000004    719 	beq	.L479

000001b4 e4932004    720 	ldr	r2,[r3],4

000001b8 e1a01003    721 	mov	r1,r3


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
000001bc e1a00005    722 	mov	r0,r5

000001c0 eb000000*   723 	bl	StringView_init

000001c4 e3a00001    724 	mov	r0,1

                     725 .L479:

000001c8 e8bd8030    726 	ldmfd	[sp]!,{r4-r5,pc}

                     727 	.endf	IEDControlDA_getOrIdent

                     728 	.align	4

                     729 ;extInfo	r2	local

                     730 

                     731 ;entity	r4	param

                     732 ;orIdent	r5	param

                     733 

                     734 	.section ".bss","awb"

                     735 .L540:

                     736 	.data

                     737 	.text

                     738 

                     739 

                     740 	.align	4

                     741 	.align	4

                     742 IEDControlDA_getOrCat::

000001cc e92d4030    743 	stmfd	[sp]!,{r4-r5,lr}

000001d0 e1a05001    744 	mov	r5,r1

000001d4 e1a04000    745 	mov	r4,r0

000001d8 ebffff88*   746 	bl	IEDControlDA_isControlDA

000001dc e3500000    747 	cmp	r0,0

000001e0 15942058    748 	ldrne	r2,[r4,88]

000001e4 15922004    749 	ldrne	r2,[r2,4]

000001e8 13520000    750 	cmpne	r2,0

000001ec 15920000    751 	ldrne	r0,[r2]

000001f0 15850000    752 	strne	r0,[r5]

000001f4 13a00001    753 	movne	r0,1

000001f8 03a00000    754 	moveq	r0,0

000001fc e8bd8030    755 	ldmfd	[sp]!,{r4-r5,pc}

                     756 	.endf	IEDControlDA_getOrCat

                     757 	.align	4

                     758 ;extInfo	r2	local

                     759 

                     760 ;entity	r4	param

                     761 ;orCat	r5	param

                     762 

                     763 	.section ".bss","awb"

                     764 .L620:

                     765 	.data

                     766 	.text

                     767 

                     768 

                     769 	.align	4

                     770 	.align	4

                     771 IEDOrCat_calcReadLen::

00000200 e92d4010    772 	stmfd	[sp]!,{r4,lr}

00000204 e1a04001    773 	mov	r4,r1

00000208 e5901054    774 	ldr	r1,[r0,84]

0000020c e3510003    775 	cmp	r1,3

00000210 13a00000    776 	movne	r0,0

00000214 1a000005    777 	bne	.L634

00000218 e5900058    778 	ldr	r0,[r0,88]

0000021c e5900000    779 	ldr	r0,[r0]

00000220 eb000000*   780 	bl	BerEncoder_Int32DetermineEncodedSize

00000224 e2800002    781 	add	r0,r0,2

00000228 e5840000    782 	str	r0,[r4]


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
0000022c e3a00001    783 	mov	r0,1

                     784 .L634:

00000230 e8bd8010    785 	ldmfd	[sp]!,{r4,pc}

                     786 	.endf	IEDOrCat_calcReadLen

                     787 	.align	4

                     788 ;extInfo	r0	local

                     789 

                     790 ;entity	r0	param

                     791 ;pLen	r4	param

                     792 

                     793 	.section ".bss","awb"

                     794 .L662:

                     795 	.data

                     796 	.text

                     797 

                     798 

                     799 	.align	4

                     800 	.align	4

                     801 IEDOrCat_write::

00000234 e92d4010    802 	stmfd	[sp]!,{r4,lr}

00000238 e24dd004    803 	sub	sp,sp,4

0000023c e1a04000    804 	mov	r4,r0

00000240 e5940050    805 	ldr	r0,[r4,80]

00000244 e350000a    806 	cmp	r0,10

00000248 05940054    807 	ldreq	r0,[r4,84]

0000024c e1a03002    808 	mov	r3,r2

00000250 03500003    809 	cmpeq	r0,3

00000254 13a00007    810 	movne	r0,7

00000258 1a000009    811 	bne	.L676

0000025c e1a0200d    812 	mov	r2,sp

00000260 e1a00003    813 	mov	r0,r3

00000264 e3a01085    814 	mov	r1,133

00000268 eb000000*   815 	bl	BufferView_decodeInt32TL

0000026c e3500000    816 	cmp	r0,0

00000270 159d1000    817 	ldrne	r1,[sp]

00000274 15940058    818 	ldrne	r0,[r4,88]

00000278 15801000    819 	strne	r1,[r0]

0000027c 13e00000    820 	mvnne	r0,0

00000280 03a0000b    821 	moveq	r0,11

                     822 .L676:

00000284 e28dd004    823 	add	sp,sp,4

00000288 e8bd8010    824 	ldmfd	[sp]!,{r4,pc}

                     825 	.endf	IEDOrCat_write

                     826 	.align	4

                     827 ;decodedVal	[sp]	local

                     828 ;extInfo	r0	local

                     829 

                     830 ;entity	r4	param

                     831 ;isoConn	none	param

                     832 ;value	r3	param

                     833 

                     834 	.section ".bss","awb"

                     835 .L734:

                     836 	.data

                     837 	.text

                     838 

                     839 

                     840 	.align	4

                     841 	.align	4

                     842 IEDOrIdent_init::

0000028c e92d4010    843 	stmfd	[sp]!,{r4,lr}


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
00000290 e1a04000    844 	mov	r4,r0

00000294 e3a0000a    845 	mov	r0,10

00000298 e5840050    846 	str	r0,[r4,80]

0000029c e3a00002    847 	mov	r0,2

000002a0 e5840054    848 	str	r0,[r4,84]

000002a4 e3a00044    849 	mov	r0,68

000002a8 eb000000*   850 	bl	IEDEntity_alloc

000002ac e3500000    851 	cmp	r0,0

000002b0 020000ff    852 	andeq	r0,r0,255

000002b4 15840058    853 	strne	r0,[r4,88]

000002b8 13a00001    854 	movne	r0,1

000002bc e8bd8010    855 	ldmfd	[sp]!,{r4,pc}

                     856 	.endf	IEDOrIdent_init

                     857 	.align	4

                     858 ;extInfo	r0	local

                     859 

                     860 ;entity	r4	param

                     861 

                     862 	.section ".bss","awb"

                     863 .L805:

                     864 	.data

                     865 	.text

                     866 

                     867 

                     868 	.align	4

                     869 	.align	4

                     870 IEDOrCat_init::

000002c0 e92d4010    871 	stmfd	[sp]!,{r4,lr}

000002c4 e1a04000    872 	mov	r4,r0

000002c8 e3a0000a    873 	mov	r0,10

000002cc e5840050    874 	str	r0,[r4,80]

000002d0 e3a00003    875 	mov	r0,3

000002d4 e5840054    876 	str	r0,[r4,84]

000002d8 e3a00004    877 	mov	r0,4

000002dc eb000000*   878 	bl	IEDEntity_alloc

000002e0 e3500000    879 	cmp	r0,0

000002e4 020000ff    880 	andeq	r0,r0,255

000002e8 15840058    881 	strne	r0,[r4,88]

000002ec 13a00001    882 	movne	r0,1

000002f0 e8bd8010    883 	ldmfd	[sp]!,{r4,pc}

                     884 	.endf	IEDOrCat_init

                     885 	.align	4

                     886 ;extInfo	r0	local

                     887 

                     888 ;entity	r4	param

                     889 

                     890 	.section ".bss","awb"

                     891 .L869:

                     892 	.data

                     893 	.text

                     894 

                     895 

                     896 	.align	4

                     897 	.align	4

                     898 	.align	4

                     899 IEDCtlNum_init::

000002f4 e3a02007    900 	mov	r2,7

000002f8 e5802050    901 	str	r2,[r0,80]

000002fc e8910006    902 	ldmfd	[r1],{r1-r2}

00000300 e59f32c8*   903 	ldr	r3,.L917

00000304 e0822001    904 	add	r2,r2,r1


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
00000308 e5931000    905 	ldr	r1,[r3]

0000030c e0421001    906 	sub	r1,r2,r1

00000310 e5801058    907 	str	r1,[r0,88]

00000314 e3a00001    908 	mov	r0,1

00000318 e12fff1e*   909 	ret	

                     910 	.endf	IEDCtlNum_init

                     911 	.align	4

                     912 

                     913 ;entity	r0	param

                     914 ;ber	r1	param

                     915 

                     916 	.section ".bss","awb"

                     917 .L910:

                     918 	.data

                     919 	.text

                     920 

                     921 

                     922 	.align	4

                     923 	.align	4

                     924 IEDOrCat_encodeRead::

0000031c e5902054    925 	ldr	r2,[r0,84]

00000320 e3520003    926 	cmp	r2,3

00000324 05900058    927 	ldreq	r0,[r0,88]

00000328 05902000    928 	ldreq	r2,[r0]

0000032c 01a00001    929 	moveq	r0,r1

00000330 03a01085    930 	moveq	r1,133

00000334 0a000000*   931 	beq	BufferView_encodeInt32

00000338 e3a00000    932 	mov	r0,0

0000033c e12fff1e*   933 	ret	

                     934 	.endf	IEDOrCat_encodeRead

                     935 	.align	4

                     936 ;extInfo	r0	local

                     937 

                     938 ;entity	r0	param

                     939 ;outBuf	r1	param

                     940 

                     941 	.section ".bss","awb"

                     942 .L955:

                     943 	.data

                     944 	.text

                     945 

                     946 

                     947 	.align	4

                     948 	.align	4

                     949 IEDOrIdent_calcReadLen::

00000340 e5902054    950 	ldr	r2,[r0,84]

00000344 e3520002    951 	cmp	r2,2

00000348 13a00000    952 	movne	r0,0

0000034c 05900058    953 	ldreq	r0,[r0,88]

00000350 05900000    954 	ldreq	r0,[r0]

00000354 02800002    955 	addeq	r0,r0,2

00000358 05810000    956 	streq	r0,[r1]

0000035c 03a00001    957 	moveq	r0,1

00000360 e12fff1e*   958 	ret	

                     959 	.endf	IEDOrIdent_calcReadLen

                     960 	.align	4

                     961 ;extInfo	r0	local

                     962 

                     963 ;entity	r0	param

                     964 ;pLen	r1	param

                     965 


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
                     966 	.section ".bss","awb"

                     967 .L998:

                     968 	.data

                     969 	.text

                     970 

                     971 

                     972 	.align	4

                     973 	.align	4

                     974 IEDOrIdent_encodeRead::

00000364 e5902054    975 	ldr	r2,[r0,84]

00000368 e3520002    976 	cmp	r2,2

0000036c 05902058    977 	ldreq	r2,[r0,88]

00000370 01a00001    978 	moveq	r0,r1

00000374 04923004    979 	ldreq	r3,[r2],4

00000378 03a01089    980 	moveq	r1,137

0000037c 0a000000*   981 	beq	BufferView_encodeOctetString

00000380 e3a00000    982 	mov	r0,0

00000384 e12fff1e*   983 	ret	

                     984 	.endf	IEDOrIdent_encodeRead

                     985 	.align	4

                     986 ;extInfo	r0	local

                     987 

                     988 ;entity	r0	param

                     989 ;outBuf	r1	param

                     990 

                     991 	.section ".bss","awb"

                     992 .L1056:

                     993 	.data

                     994 	.text

                     995 

                     996 

                     997 	.align	4

                     998 	.align	4

                     999 IEDOrIdent_write::

00000388 e92d4010   1000 	stmfd	[sp]!,{r4,lr}

0000038c e24dd008   1001 	sub	sp,sp,8

00000390 e1a04000   1002 	mov	r4,r0

00000394 e5940050   1003 	ldr	r0,[r4,80]

00000398 e350000a   1004 	cmp	r0,10

0000039c 05940054   1005 	ldreq	r0,[r4,84]

000003a0 e1a03002   1006 	mov	r3,r2

000003a4 03500002   1007 	cmpeq	r0,2

000003a8 13a00007   1008 	movne	r0,7

000003ac 1a000011   1009 	bne	.L1070

000003b0 e1a0200d   1010 	mov	r2,sp

000003b4 e1a00003   1011 	mov	r0,r3

000003b8 e3a01089   1012 	mov	r1,137

000003bc eb000000*  1013 	bl	BufferView_decodeStringViewTL

000003c0 e3500000   1014 	cmp	r0,0

000003c4 0a000002   1015 	beq	.L1080

000003c8 e59d2000   1016 	ldr	r2,[sp]

000003cc e3520040   1017 	cmp	r2,64

000003d0 9a000001   1018 	bls	.L1079

                    1019 .L1080:

000003d4 e3a0000b   1020 	mov	r0,11

000003d8 ea000006   1021 	b	.L1070

                    1022 .L1079:

000003dc e5940058   1023 	ldr	r0,[r4,88]

000003e0 e3520000   1024 	cmp	r2,0

000003e4 e5802000   1025 	str	r2,[r0]

000003e8 159d1004   1026 	ldrne	r1,[sp,4]


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
000003ec 12800004   1027 	addne	r0,r0,4

000003f0 1b000000*  1028 	blne	memcpy

000003f4 e3e00000   1029 	mvn	r0,0

                    1030 .L1070:

000003f8 e28dd008   1031 	add	sp,sp,8

000003fc e8bd8010   1032 	ldmfd	[sp]!,{r4,pc}

                    1033 	.endf	IEDOrIdent_write

                    1034 	.align	4

                    1035 ;decodedVal	[sp]	local

                    1036 ;extInfo	r0	local

                    1037 

                    1038 ;entity	r4	param

                    1039 ;isoConn	none	param

                    1040 ;value	r3	param

                    1041 

                    1042 	.section ".bss","awb"

                    1043 .L1179:

                    1044 	.data

                    1045 	.text

                    1046 

                    1047 

                    1048 	.align	4

                    1049 	.align	4

                    1050 IEDControlDA_init::

00000400 e92d4070   1051 	stmfd	[sp]!,{r4-r6,lr}

00000404 e24dd008   1052 	sub	sp,sp,8

00000408 e1a05001   1053 	mov	r5,r1

0000040c e1a06000   1054 	mov	r6,r0

00000410 e3a00028   1055 	mov	r0,40

00000414 eb000000*  1056 	bl	IEDEntity_alloc

00000418 e1b04000   1057 	movs	r4,r0

0000041c 0a00005b   1058 	beq	.L1249

00000420 e5864058   1059 	str	r4,[r6,88]

00000424 e3a00001   1060 	mov	r0,1

00000428 e5860054   1061 	str	r0,[r6,84]

0000042c e28f1000*  1062 	adr	r1,.L1622

00000430 e2860048   1063 	add	r0,r6,72

00000434 eb000000*  1064 	bl	StringView_cmpCStr

00000438 e3500000   1065 	cmp	r0,0

0000043c 1a000008   1066 	bne	.L1212

00000440 e28d2004   1067 	add	r2,sp,4

00000444 e28d1003   1068 	add	r1,sp,3

00000448 e5840008   1069 	str	r0,[r4,8]

0000044c e1a00005   1070 	mov	r0,r5

00000450 e3a03000   1071 	mov	r3,0

00000454 eb000000*  1072 	bl	BufferView_decodeTL

00000458 e3500000   1073 	cmp	r0,0

0000045c 0a00004b   1074 	beq	.L1249

00000460 ea00001c   1075 	b	.L1222

                    1076 .L1212:

00000464 e28f1000*  1077 	adr	r1,.L1623

00000468 e2860048   1078 	add	r0,r6,72

0000046c eb000000*  1079 	bl	StringView_cmpCStr

00000470 e3500000   1080 	cmp	r0,0

00000474 1a000009   1081 	bne	.L1215

00000478 e28d2004   1082 	add	r2,sp,4

0000047c e28d1003   1083 	add	r1,sp,3

00000480 e3a00001   1084 	mov	r0,1

00000484 e5840008   1085 	str	r0,[r4,8]

00000488 e1a00005   1086 	mov	r0,r5

0000048c e3a03000   1087 	mov	r3,0


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
00000490 eb000000*  1088 	bl	BufferView_decodeTL

00000494 e3500000   1089 	cmp	r0,0

00000498 0a00003c   1090 	beq	.L1249

0000049c ea00000d   1091 	b	.L1222

                    1092 .L1215:

000004a0 e28f1000*  1093 	adr	r1,.L1624

000004a4 e2860048   1094 	add	r0,r6,72

000004a8 eb000000*  1095 	bl	StringView_cmpCStr

000004ac e3500000   1096 	cmp	r0,0

000004b0 1a000036   1097 	bne	.L1249

000004b4 e28d2004   1098 	add	r2,sp,4

000004b8 e28d1003   1099 	add	r1,sp,3

000004bc e3a00002   1100 	mov	r0,2

000004c0 e5840008   1101 	str	r0,[r4,8]

000004c4 e1a00005   1102 	mov	r0,r5

000004c8 e3a03000   1103 	mov	r3,0

000004cc eb000000*  1104 	bl	BufferView_decodeTL

000004d0 e3500000   1105 	cmp	r0,0

000004d4 0a00002d   1106 	beq	.L1249

                    1107 .L1222:

000004d8 e5dd0003   1108 	ldrb	r0,[sp,3]

000004dc e35000f1   1109 	cmp	r0,241

000004e0 1a000037   1110 	bne	.L1253

000004e4 e59d2004   1111 	ldr	r2,[sp,4]

000004e8 e895000a   1112 	ldmfd	[r5],{r1,r3}

000004ec e1a00005   1113 	mov	r0,r5

000004f0 e0831001   1114 	add	r1,r3,r1

000004f4 e3a03000   1115 	mov	r3,0

000004f8 eb000000*  1116 	bl	BufferView_init

000004fc e2841014   1117 	add	r1,r4,20

00000500 e1a00005   1118 	mov	r0,r5

00000504 eb000000*  1119 	bl	IEDModel_getTermItemDescrStruct

00000508 e3500000   1120 	cmp	r0,0

0000050c 0a00001f   1121 	beq	.L1249

00000510 e2841018   1122 	add	r1,r4,24

00000514 e1a00005   1123 	mov	r0,r5

00000518 eb000000*  1124 	bl	IEDModel_getTermItemDescrStruct

0000051c e3500000   1125 	cmp	r0,0

00000520 0a00001a   1126 	beq	.L1249

00000524 e284101c   1127 	add	r1,r4,28

00000528 e1a00005   1128 	mov	r0,r5

0000052c eb000000*  1129 	bl	IEDModel_getTermItemDescrStruct

00000530 e3500000   1130 	cmp	r0,0

00000534 0a000015   1131 	beq	.L1249

00000538 e2841020   1132 	add	r1,r4,32

0000053c e1a00005   1133 	mov	r0,r5

00000540 eb000000*  1134 	bl	IEDModel_getTermItemDescrStruct

00000544 e3500000   1135 	cmp	r0,0

00000548 0a000010   1136 	beq	.L1249

0000054c e5940008   1137 	ldr	r0,[r4,8]

00000550 e3500001   1138 	cmp	r0,1

00000554 1a000008   1139 	bne	.L1240

00000558 e2841024   1140 	add	r1,r4,36

0000055c e1a00005   1141 	mov	r0,r5

00000560 eb000000*  1142 	bl	IEDModel_getTermItemDescrStruct

00000564 e3500000   1143 	cmp	r0,0

00000568 0a000008   1144 	beq	.L1249

0000056c e1a00006   1145 	mov	r0,r6

00000570 eb000000*  1146 	bl	Control_registerCtrlObj

00000574 e3500000   1147 	cmp	r0,0

00000578 0a000004   1148 	beq	.L1249


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
                    1149 .L1240:

0000057c e28f1000*  1150 	adr	r1,.L1625

00000580 e1a00006   1151 	mov	r0,r6

00000584 eb000000*  1152 	bl	IEDEntity_getChildByCStrName

00000588 e1b05000   1153 	movs	r5,r0

0000058c 1a000001   1154 	bne	.L1248

                    1155 .L1249:

00000590 e3a00000   1156 	mov	r0,0

00000594 ea00000b   1157 	b	.L1207

                    1158 .L1248:

00000598 e28f1000*  1159 	adr	r1,.L1626

0000059c eb000000*  1160 	bl	IEDEntity_getChildByCStrName

000005a0 e3500000   1161 	cmp	r0,0

000005a4 15900058   1162 	ldrne	r0,[r0,88]

000005a8 e28f1000*  1163 	adr	r1,.L1627

000005ac 15840000   1164 	strne	r0,[r4]

000005b0 e1a00005   1165 	mov	r0,r5

000005b4 eb000000*  1166 	bl	IEDEntity_getChildByCStrName

000005b8 e3500000   1167 	cmp	r0,0

000005bc 15900058   1168 	ldrne	r0,[r0,88]

000005c0 15840004   1169 	strne	r0,[r4,4]

                    1170 .L1253:

000005c4 e3a00001   1171 	mov	r0,1

                    1172 .L1207:

000005c8 e28dd008   1173 	add	sp,sp,8

000005cc e8bd8070   1174 	ldmfd	[sp]!,{r4-r6,pc}

                    1175 	.endf	IEDControlDA_init

                    1176 	.align	4

                    1177 .L917:

000005d0 00000000*  1178 	.data.w	iedModel

                    1179 	.type	.L917,$object

                    1180 	.size	.L917,4

                    1181 

                    1182 .L1622:

                    1183 ;	"SBOw\000"

000005d4 774f4253   1184 	.data.b	83,66,79,119

000005d8 00        1185 	.data.b	0

000005d9 000000    1186 	.align 4

                    1187 

                    1188 	.type	.L1622,$object

                    1189 	.size	.L1622,4

                    1190 

                    1191 .L1623:

                    1192 ;	"Oper\000"

000005dc 7265704f   1193 	.data.b	79,112,101,114

000005e0 00        1194 	.data.b	0

000005e1 000000    1195 	.align 4

                    1196 

                    1197 	.type	.L1623,$object

                    1198 	.size	.L1623,4

                    1199 

                    1200 .L1624:

                    1201 ;	"Cancel\000"

000005e4 636e6143   1202 	.data.b	67,97,110,99

000005e8 6c65      1203 	.data.b	101,108

000005ea 00        1204 	.data.b	0

000005eb 00        1205 	.align 4

                    1206 

                    1207 	.type	.L1624,$object

                    1208 	.size	.L1624,4

                    1209 


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
                    1210 .L1625:

                    1211 ;	"origin\000"

000005ec 6769726f   1212 	.data.b	111,114,105,103

000005f0 6e69      1213 	.data.b	105,110

000005f2 00        1214 	.data.b	0

000005f3 00        1215 	.align 4

                    1216 

                    1217 	.type	.L1625,$object

                    1218 	.size	.L1625,4

                    1219 

                    1220 .L1626:

                    1221 ;	"orIdent\000"

000005f4 6449726f   1222 	.data.b	111,114,73,100

000005f8 00746e65   1223 	.data.b	101,110,116,0

                    1224 	.align 4

                    1225 

                    1226 	.type	.L1626,$object

                    1227 	.size	.L1626,4

                    1228 

                    1229 .L1627:

                    1230 ;	"orCat\000"

000005fc 6143726f   1231 	.data.b	111,114,67,97

00000600 0074      1232 	.data.b	116,0

00000602 0000      1233 	.align 4

                    1234 

                    1235 	.type	.L1627,$object

                    1236 	.size	.L1627,4

                    1237 

                    1238 	.align	4

                    1239 ;tag	[sp,3]	local

                    1240 ;len	[sp,4]	local

                    1241 ;origin	r5	local

                    1242 ;orCat	r0	local

                    1243 ;orIdent	r0	local

                    1244 ;extInfo	r4	local

                    1245 ;.L1542	.L1552	static

                    1246 ;.L1543	.L1553	static

                    1247 ;.L1544	.L1554	static

                    1248 ;.L1545	.L1555	static

                    1249 ;.L1546	.L1550	static

                    1250 ;.L1547	.L1551	static

                    1251 

                    1252 ;entity	r6	param

                    1253 ;ctrlInfoBER	r5	param

                    1254 

                    1255 	.section ".bss","awb"

                    1256 .L1541:

                    1257 	.data

                    1258 	.text

                    1259 

                    1260 

                    1261 	.align	4

                    1262 	.align	4

                    1263 IEDControlDO_init::

00000604 e92d4010   1264 	stmfd	[sp]!,{r4,lr}

00000608 e1a04000   1265 	mov	r4,r0

0000060c e3a00005   1266 	mov	r0,5

00000610 e5840050   1267 	str	r0,[r4,80]

00000614 e3a00004   1268 	mov	r0,4

00000618 eb000000*  1269 	bl	IEDEntity_alloc

0000061c e3a01001   1270 	mov	r1,1


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
00000620 e5841054   1271 	str	r1,[r4,84]

00000624 e3500000   1272 	cmp	r0,0

00000628 13a00001   1273 	movne	r0,1

0000062c e8bd8010   1274 	ldmfd	[sp]!,{r4,pc}

                    1275 	.endf	IEDControlDO_init

                    1276 	.align	4

                    1277 

                    1278 ;entity	r4	param

                    1279 

                    1280 	.section ".bss","awb"

                    1281 .L1669:

                    1282 	.data

                    1283 	.text

                    1284 

                    1285 

                    1286 ;514: 


                    1287 ;515: MmsDataAccessError IEDControlDA_write(IEDEntity entity,


                    1288 	.align	4

                    1289 	.align	4

                    1290 IEDControlDA_write::

00000630 e92d4070   1291 	stmfd	[sp]!,{r4-r6,lr}

                    1292 ;516:                                          IsoConnection* isoConn, BufferView* value)


                    1293 ;517: {


                    1294 

                    1295 ;518:     uint8_t addCause;


                    1296 ;519:     IEDControlDA* extInfo = entity->extInfo;


                    1297 

00000634 e1a06001   1298 	mov	r6,r1

00000638 e1a05000   1299 	mov	r5,r0

0000063c e5954058   1300 	ldr	r4,[r5,88]

                    1301 ;520: 


                    1302 ;521:     //Пишем value 1 или 0


                    1303 ;522:     if(!writeCtlValue(extInfo, value))


                    1304 

                    1305 ;497: {


                    1306 

                    1307 ;498:     uint8_t* pValue = value->p + value->pos;


                    1308 

00000640 e892000a   1309 	ldmfd	[r2],{r1,r3}

00000644 e1a00003   1310 	mov	r0,r3

                    1311 ;499:     if (pValue[0] != IEC61850_BER_BOOLEAN || pValue[1] != 1)


                    1312 

00000648 e7f02001   1313 	ldrb	r2,[r0,r1]!

0000064c e3520083   1314 	cmp	r2,131

00000650 05d01001   1315 	ldreqb	r1,[r0,1]

00000654 03510001   1316 	cmpeq	r1,1

                    1317 ;511:     }


                    1318 ;512:     return true;


                    1319 

                    1320 ;523:     {


                    1321 

                    1322 ;524:         return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                    1323 

00000658 13a0000b   1324 	movne	r0,11

0000065c 1a00001b   1325 	bne	.L1676

                    1326 ;500:     {


                    1327 

                    1328 ;501:         return false;


                    1329 

                    1330 ;502:     }


                    1331 ;503: 



                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
                    1332 ;504:     if(pValue[2])


                    1333 

00000660 e5d00002   1334 	ldrb	r0,[r0,2]

00000664 e3500000   1335 	cmp	r0,0

                    1336 ;507:     }


                    1337 ;508:     else


                    1338 ;509:     {


                    1339 

                    1340 ;510:         writeTele(ctrlInfo->ctlValZero->valueOffset);


                    1341 

00000668 05940020   1342 	ldreq	r0,[r4,32]

                    1343 ;505:     {


                    1344 

                    1345 ;506:         writeTele(ctrlInfo->ctlValOne->valueOffset);


                    1346 

0000066c 1594001c   1347 	ldrne	r0,[r4,28]

00000670 e5900004   1348 	ldr	r0,[r0,4]

00000674 eb000000*  1349 	bl	writeTele

                    1350 ;525:     }


                    1351 ;526: 


                    1352 ;527:     //Читать ready пока не созреет


                    1353 ;528:     if(!IEDControlDA_waitReady(entity))


                    1354 

00000678 e1a00005   1355 	mov	r0,r5

0000067c ebfffe73*  1356 	bl	IEDControlDA_waitReady

00000680 e3500000   1357 	cmp	r0,0

00000684 0a000009   1358 	beq	.L1697

                    1359 ;529:     {


                    1360 

                    1361 ;530:         return DATA_ACCESS_ERROR_UNKNOWN;


                    1362 

                    1363 ;531:     }


                    1364 ;532: 


                    1365 ;533:     //Считать код


                    1366 ;534:     addCause = readIntValue(extInfo->code);


                    1367 

00000688 e5940014   1368 	ldr	r0,[r4,20]

0000068c eb000000*  1369 	bl	readIntValue

00000690 e21020ff   1370 	ands	r2,r0,255

                    1371 ;535: 


                    1372 ;536:     if(addCause != 0)


                    1373 

00000694 0a000007   1374 	beq	.L1694

                    1375 ;537:     {


                    1376 

                    1377 ;538:         if(!Control_sendServiceErrorReport(isoConn, entity, addCause))


                    1378 

00000698 e1a01005   1379 	mov	r1,r5

0000069c e1a00006   1380 	mov	r0,r6

000006a0 eb000000*  1381 	bl	Control_sendServiceErrorReport

000006a4 e3500000   1382 	cmp	r0,0

                    1383 ;541:         }


                    1384 ;542:         else


                    1385 ;543:         {


                    1386 

                    1387 ;544:             return DATA_ACCESS_ERROR_OBJECT_ACCESS_DENIED;


                    1388 

000006a8 13a00003   1389 	movne	r0,3

000006ac 1a000007   1390 	bne	.L1676

                    1391 .L1697:

                    1392 ;539:         {



                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_crc1.s
                    1393 

                    1394 ;540:             return DATA_ACCESS_ERROR_UNKNOWN;


                    1395 

000006b0 e3a0000c   1396 	mov	r0,12

000006b4 ea000005   1397 	b	.L1676

                    1398 .L1694:

                    1399 ;545:         }


                    1400 ;546:     }


                    1401 ;547:     if(extInfo->type == CONTROL_OPER)


                    1402 

000006b8 e5940008   1403 	ldr	r0,[r4,8]

000006bc e3500001   1404 	cmp	r0,1

                    1405 ;548:     {


                    1406 

                    1407 ;549:         //Включаем контроль выполнения операции для посылки


                    1408 ;550:         // CommandTermination по окончании


                    1409 ;551:         extInfo->isoConn = isoConn;


                    1410 

000006c0 05846010   1411 	streq	r6,[r4,16]

                    1412 ;552:         extInfo->waitTerminate = true;


                    1413 

000006c4 03a00001   1414 	moveq	r0,1

000006c8 05c4000c   1415 	streqb	r0,[r4,12]

                    1416 ;553:     }


                    1417 ;554:     return DATA_ACCESS_ERROR_SUCCESS;


                    1418 

000006cc e3e00000   1419 	mvn	r0,0

                    1420 .L1676:

000006d0 e8bd8070   1421 	ldmfd	[sp]!,{r4-r6,pc}

                    1422 	.endf	IEDControlDA_write

                    1423 	.align	4

                    1424 ;addCause	r2	local

                    1425 ;extInfo	r4	local

                    1426 ;pValue	r0	local

                    1427 

                    1428 ;entity	r5	param

                    1429 ;isoConn	r6	param

                    1430 ;value	r2	param

                    1431 

                    1432 	.section ".bss","awb"

                    1433 .L1872:

                    1434 	.data

                    1435 	.text

                    1436 

                    1437 ;555: }


                    1438 	.align	4

                    1439 ;iedModel	iedModel	import

                    1440 

                    1441 	.data

                    1442 	.ghsnote version,6

                    1443 	.ghsnote tools,1

                    1444 	.ghsnote options,0

                    1445 	.text

                    1446 	.align	4

