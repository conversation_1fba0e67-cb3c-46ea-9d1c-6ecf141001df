                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ar01.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=acse.c -o gh_ar01.o -list=acse.lst C:\Users\<USER>\AppData\Local\Temp\gh_ar01.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_ar01.s
Source File: acse.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile acse.c -o

                      11 ;		acse.o

                      12 ;Source File:   acse.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:02 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "acse.h"


                      23 ;2: #include "AsnEncoding.h"  


                      24 ;3: 


                      25 ;4: #include <string.h>


                      26 ;5: 


                      27 ;6: #pragma alignvar (4)


                      28 ;7: unsigned char appContextNameMms[] = { 0x28, 0xca, 0x22, 0x02, 0x03 };


                      29 ;8: 


                      30 ;9: void AcseConnection_init(AcseConnection* acse)


                      31 	.text

                      32 	.align	4

                      33 AcseConnection_init::

                      34 ;10: {


                      35 

                      36 ;11:     //TODO acse->nextReference Что это?


                      37 ;12:     acse->nextReference = 3;


                      38 

00000000 e3a01003     39 	mov	r1,3

00000004 e5801000     40 	str	r1,[r0]

00000008 e12fff1e*    41 	ret	

                      42 	.endf	AcseConnection_init

                      43 	.align	4

                      44 

                      45 ;acse	r0	param

                      46 

                      47 	.section ".bss","awb"

                      48 .L30:

                      49 	.data

                      50 	.text


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ar01.s
                      51 

                      52 ;13: }


                      53 

                      54 ;14: 


                      55 ;15: int AcseConnection_createAssociateResponseMessage(AcseConnection* acse,


                      56 	.align	4

                      57 	.align	4

                      58 	.align	4

                      59 AcseConnection_createAssociateResponseMessage::

0000000c e92d4df4     60 	stmfd	[sp]!,{r2,r4-r8,r10-fp,lr}

00000010 e1a05003     61 	mov	r5,r3

                      62 ;16:         unsigned char* buf, unsigned char* userData, int userDataLen,unsigned char acseResult)


                      63 ;17: {


                      64 

                      65 ;18:     int contentLength;


                      66 ;19:     int bufPos = 0;


                      67 

                      68 ;20: 


                      69 ;21:     int appContextLength = 9;


                      70 

                      71 ;22:     int resultLength = 5;


                      72 

                      73 ;23:     int resultDiagnosticLength = 5;


                      74 

                      75 ;24: 


                      76 ;25:     int fixedContentLength = appContextLength + resultLength + resultDiagnosticLength;


                      77 

                      78 ;26: 


                      79 ;27:     int variableContentLength = 0;


                      80 

                      81 ;28: 


                      82 ;29:     int assocDataLength;


                      83 ;30:     int userInfoLength;


                      84 ;31:     int nextRefLength;


                      85 ;32: 


                      86 ;33:     /* single ASN1 type tag */


                      87 ;34:     variableContentLength += userDataLen;


                      88 

00000014 e285a001     89 	add	r10,r5,1

                      90 ;35:     variableContentLength += 1;


                      91 

                      92 ;36:     variableContentLength += BerEncoder_determineLengthSize(userDataLen);


                      93 

00000018 e1a04001     94 	mov	r4,r1

0000001c e24dd004     95 	sub	sp,sp,4

00000020 e58d2004     96 	str	r2,[sp,4]

00000024 e1a0b000     97 	mov	fp,r0

00000028 e1a00005     98 	mov	r0,r5

0000002c eb000000*    99 	bl	BerEncoder_determineLengthSize

00000030 e08aa000    100 	add	r10,r10,r0

                     101 ;37: 


                     102 ;38:     /* indirect reference */


                     103 ;39:     nextRefLength = BerEncoder_UInt32determineEncodedSize(acse->nextReference);


                     104 

00000034 e59b0000    105 	ldr	r0,[fp]

00000038 eb000000*   106 	bl	BerEncoder_UInt32determineEncodedSize

0000003c e1a08000    107 	mov	r8,r0

                     108 ;40:     variableContentLength += nextRefLength;


                     109 

00000040 e08aa000    110 	add	r10,r10,r0

                     111 ;41:     variableContentLength += 2;



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ar01.s
                     112 

00000044 e28a6002    113 	add	r6,r10,2

                     114 ;42: 


                     115 ;43:     /* association data */


                     116 ;44:     assocDataLength = variableContentLength;


                     117 

                     118 ;45:     variableContentLength += BerEncoder_determineLengthSize(assocDataLength);


                     119 

00000048 e1a00006    120 	mov	r0,r6

0000004c eb000000*   121 	bl	BerEncoder_determineLengthSize

00000050 e086a000    122 	add	r10,r6,r0

                     123 ;46:     variableContentLength += 1;


                     124 

00000054 e28a7001    125 	add	r7,r10,1

                     126 ;47: 


                     127 ;48:     /* user information */


                     128 ;49:     userInfoLength = variableContentLength;


                     129 

                     130 ;50:     variableContentLength += BerEncoder_determineLengthSize(userInfoLength);


                     131 

00000058 e1a00007    132 	mov	r0,r7

0000005c eb000000*   133 	bl	BerEncoder_determineLengthSize

00000060 e087a000    134 	add	r10,r7,r0

                     135 ;51:     variableContentLength += 1;


                     136 

00000064 e28a1016    137 	add	r1,r10,22

                     138 ;52: 


                     139 ;53:     variableContentLength += 2;


                     140 

                     141 ;54: 


                     142 ;55:     contentLength = fixedContentLength + variableContentLength;


                     143 

                     144 ;56: 


                     145 ;57:     bufPos = BerEncoder_encodeTL(AARE_PACKET, contentLength, buf, bufPos);


                     146 

00000068 e1a02004    147 	mov	r2,r4

0000006c e3a03000    148 	mov	r3,0

00000070 e3a00061    149 	mov	r0,97

00000074 eb000000*   150 	bl	BerEncoder_encodeTL

                     151 ;58: 


                     152 ;59:     /* application context name */


                     153 ;60:     bufPos = BerEncoder_encodeTL(APPLICATION_CONTEXT_NAME, 7, buf, bufPos);


                     154 

00000078 e1a02004    155 	mov	r2,r4

0000007c e3a01007    156 	mov	r1,7

00000080 e1a03000    157 	mov	r3,r0

00000084 e3a000a1    158 	mov	r0,161

00000088 eb000000*   159 	bl	BerEncoder_encodeTL

                     160 ;61:     bufPos = BerEncoder_encodeTL(ASN_OBJECT_IDENTIFIER, 5, buf, bufPos);


                     161 

0000008c e1a02004    162 	mov	r2,r4

00000090 e3a01005    163 	mov	r1,5

00000094 e1a03000    164 	mov	r3,r0

00000098 e3a00006    165 	mov	r0,6

0000009c eb000000*   166 	bl	BerEncoder_encodeTL

000000a0 e1a0a000    167 	mov	r10,r0

                     168 ;62:     memcpy(buf + bufPos, appContextNameMms, 5);


                     169 

000000a4 e59f10fc*   170 	ldr	r1,.L75

000000a8 e08a0004    171 	add	r0,r10,r4

000000ac e3a02005    172 	mov	r2,5


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ar01.s
000000b0 eb000000*   173 	bl	memcpy

                     174 ;63:     bufPos += 5;


                     175 

000000b4 e28a3005    176 	add	r3,r10,5

                     177 ;64: 


                     178 ;65:     /* result */


                     179 ;66:     bufPos = BerEncoder_encodeTL(AARE_PACKET_RESULT, 3, buf, bufPos);


                     180 

000000b8 e1a02004    181 	mov	r2,r4

000000bc e3a01003    182 	mov	r1,3

000000c0 e3a000a2    183 	mov	r0,162

000000c4 eb000000*   184 	bl	BerEncoder_encodeTL

                     185 ;67:     bufPos = BerEncoder_encodeTL(ASN_INTEGER, 1, buf, bufPos);


                     186 

000000c8 e1a02004    187 	mov	r2,r4

000000cc e3a01001    188 	mov	r1,1

000000d0 e1a03000    189 	mov	r3,r0

000000d4 e3a00002    190 	mov	r0,2

000000d8 eb000000*   191 	bl	BerEncoder_encodeTL

                     192 ;68:     buf[bufPos++] = acseResult;


                     193 

000000dc e1a02004    194 	mov	r2,r4

000000e0 e3a01005    195 	mov	r1,5

000000e4 e5dda028    196 	ldrb	r10,[sp,40]

000000e8 e2803001    197 	add	r3,r0,1

000000ec e7c4a000    198 	strb	r10,[r4,r0]

                     199 ;69: 


                     200 ;70:     /* result source diagnostics */


                     201 ;71:     bufPos = BerEncoder_encodeTL(RESULT_SOURCE_DIAGNOSTICS, 5, buf, bufPos);


                     202 

000000f0 e3a000a3    203 	mov	r0,163

000000f4 eb000000*   204 	bl	BerEncoder_encodeTL

                     205 ;72:     bufPos = BerEncoder_encodeTL(0xa1, 3, buf, bufPos);


                     206 

000000f8 e1a02004    207 	mov	r2,r4

000000fc e3a01003    208 	mov	r1,3

00000100 e1a03000    209 	mov	r3,r0

00000104 e3a000a1    210 	mov	r0,161

00000108 eb000000*   211 	bl	BerEncoder_encodeTL

                     212 ;73:     bufPos = BerEncoder_encodeTL(ASN_INTEGER, 1, buf, bufPos);


                     213 

0000010c e1a02004    214 	mov	r2,r4

00000110 e3a01001    215 	mov	r1,1

00000114 e1a03000    216 	mov	r3,r0

00000118 e3a00002    217 	mov	r0,2

0000011c eb000000*   218 	bl	BerEncoder_encodeTL

                     219 ;74:     buf[bufPos++] = 0;


                     220 

00000120 e1a02004    221 	mov	r2,r4

00000124 e3a01000    222 	mov	r1,0

00000128 e7c41000    223 	strb	r1,[r4,r0]

                     224 ;75: 


                     225 ;76:     /* user information */


                     226 ;77:     bufPos = BerEncoder_encodeTL(USER_INFORMATION, userInfoLength, buf, bufPos);


                     227 

0000012c e1a01007    228 	mov	r1,r7

00000130 e2803001    229 	add	r3,r0,1

00000134 e3a000be    230 	mov	r0,190

00000138 eb000000*   231 	bl	BerEncoder_encodeTL

                     232 ;78: 


                     233 ;79:     /* association data */



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ar01.s
                     234 ;80:     bufPos = BerEncoder_encodeTL(ACSE_USER_INFORMATION, assocDataLength, buf,


                     235 

0000013c e1a02004    236 	mov	r2,r4

00000140 e1a01006    237 	mov	r1,r6

00000144 e1a03000    238 	mov	r3,r0

00000148 e3a00028    239 	mov	r0,40

0000014c eb000000*   240 	bl	BerEncoder_encodeTL

                     241 ;81:                                  bufPos);


                     242 ;82: 


                     243 ;83:     /* indirect-reference */


                     244 ;84:     bufPos = BerEncoder_encodeTL(ASN_INTEGER, nextRefLength, buf, bufPos);


                     245 

00000150 e1a02004    246 	mov	r2,r4

00000154 e1a01008    247 	mov	r1,r8

00000158 e1a03000    248 	mov	r3,r0

0000015c e3a00002    249 	mov	r0,2

00000160 eb000000*   250 	bl	BerEncoder_encodeTL

                     251 ;85:     bufPos = BerEncoder_encodeUInt32(acse->nextReference, buf, bufPos);


                     252 

00000164 e1a02000    253 	mov	r2,r0

00000168 e59b0000    254 	ldr	r0,[fp]

0000016c e1a01004    255 	mov	r1,r4

00000170 eb000000*   256 	bl	BerEncoder_encodeUInt32

                     257 ;86: 


                     258 ;87:     /* single ASN1 type */


                     259 ;88:     bufPos = BerEncoder_encodeTL(USER_INFORMATION_ENCODING, userDataLen,


                     260 

00000174 e1a02004    261 	mov	r2,r4

00000178 e1a01005    262 	mov	r1,r5

0000017c e1a03000    263 	mov	r3,r0

00000180 e3a000a0    264 	mov	r0,160

00000184 eb000000*   265 	bl	BerEncoder_encodeTL

00000188 e1a02005    266 	mov	r2,r5

0000018c e1a0a000    267 	mov	r10,r0

                     268 ;89:                                  buf, bufPos);


                     269 ;90: 


                     270 ;91:     memcpy(buf + bufPos,userData,userDataLen);


                     271 

00000190 e59d1004    272 	ldr	r1,[sp,4]

00000194 e08a0004    273 	add	r0,r10,r4

00000198 eb000000*   274 	bl	memcpy

                     275 ;92: 


                     276 ;93:     return bufPos + userDataLen;


                     277 

0000019c e08a0005    278 	add	r0,r10,r5

000001a0 e28dd004    279 	add	sp,sp,4

000001a4 e8bd8df4    280 	ldmfd	[sp]!,{r2,r4-r8,r10-fp,pc}

                     281 	.endf	AcseConnection_createAssociateResponseMessage

                     282 	.align	4

                     283 ;bufPos	r10	local

                     284 ;variableContentLength	r10	local

                     285 ;assocDataLength	r6	local

                     286 ;userInfoLength	r7	local

                     287 ;nextRefLength	r8	local

                     288 

                     289 ;acse	fp	param

                     290 ;buf	r4	param

                     291 ;userData	[sp,4]	param

                     292 ;userDataLen	r5	param

                     293 ;acseResult	[sp,40]	param

                     294 


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_ar01.s
                     295 	.section ".bss","awb"

                     296 .L68:

                     297 	.data

                     298 	.text

                     299 

                     300 ;94: }


                     301 	.align	4

                     302 .L75:

000001a8 00000000*   303 	.data.w	appContextNameMms

                     304 	.type	.L75,$object

                     305 	.size	.L75,4

                     306 

                     307 	.align	4

                     308 

                     309 	.data

                     310 .L84:

                     311 	.globl	appContextNameMms

00000000 0222ca28    312 appContextNameMms:	.data.b	40,202,34,2

00000004 03         313 	.data.b	3

00000005 000000     314 	.space	3

                     315 	.type	appContextNameMms,$object

                     316 	.size	appContextNameMms,8

                     317 	.ghsnote version,6

                     318 	.ghsnote tools,3

                     319 	.ghsnote options,0

                     320 	.text

                     321 	.align	4

                     322 	.data

                     323 	.align	4

                     324 	.text

