                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a341.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=ConfigFiles.c -o fs\gh_a341.o -list=fs/ConfigFiles.lst C:\Users\<USER>\AppData\Local\Temp\gh_a341.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_a341.s
Source File: ConfigFiles.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		fs/ConfigFiles.c -o fs/ConfigFiles.o

                      12 ;Source File:   fs/ConfigFiles.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:55 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "ConfigFiles.h"


                      23 ;2: #include "../pwin_access.h"


                      24 ;3: #include <debug.h>


                      25 ;4: #include <string.h>


                      26 ;5: #include <stdlib.h>


                      27 ;6: 


                      28 ;7: //Комментарий для проверки кодировки


                      29 ;8: 


                      30 ;9: 


                      31 ;10: // информация о конфигурационном файле


                      32 ;11: typedef struct 


                      33 ;12: {


                      34 ;13: 	//! сигнатура (заполняется пользователем)


                      35 ;14: 	unsigned long signature;


                      36 ;15: 	//! имя файла, отображаемое в файловой системе (заполняется пользователем)


                      37 ;16: 	char fileName[8];


                      38 ;17: 	// указатель, где файл хранится в памяти или NULL если файл не найден


                      39 ;18: 	void *pHeader;


                      40 ;19: 	// указатель на данные


                      41 ;20: 	unsigned char *pData;


                      42 ;21: 	//! атрибуты


                      43 ;22: 	size_t fileSize;


                      44 ;23: 	__time32_t time;	


                      45 ;24: }CfgFileInfo;


                      46 ;25: 


                      47 ;26: 


                      48 ;27: //! количество конфигурационных файлов (автоматически)


                      49 ;28: #define CFG_FILES_COUNT (sizeof(cfgFiles)/sizeof(cfgFiles[0]))


                      50 ;29: 



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a341.s
                      51 ;30: //! icd файл


                      52 ;31: CfgFileInfo icdFile = {'ICDF',"icd.zip" ,NULL}; 


                      53 ;32: //! cid файл


                      54 ;33: CfgFileInfo  cidFile = {'CIDF', "cid.zip" ,NULL}; 


                      55 ;34: //! все файлы 


                      56 ;35: CfgFileInfo *cfgFiles[] = { &icdFile, &cidFile };


                      57 ;36: 


                      58 ;37: 


                      59 ;38: static FNameErrCode getCfgFileInfo(FSFindData* fileInfo,


                      60 ;39: 	BufferView* bufToWrite)


                      61 ;40: {


                      62 ;41: 	CfgFileInfo *info = NULL;


                      63 ;42: 	if (fileInfo->fileIndex >= CFG_FILES_COUNT)


                      64 ;43: 	{


                      65 ;44: 		return FNAME_NOT_FOUND;


                      66 ;45: 	}


                      67 ;46: 


                      68 ;47: 	info = cfgFiles[fileInfo->fileIndex];


                      69 ;48: 	if (!info || !info->pData)


                      70 ;49: 	{


                      71 ;50: 		return FNAME_NOT_FOUND;


                      72 ;51: 	}


                      73 ;52: 


                      74 ;53: 	fileInfo->attr.fileSize = info->fileSize;


                      75 ;54: 	fileInfo->attr.time = info->time;


                      76 ;55: 	fileInfo->attr.ms = 0;


                      77 ;56: 


                      78 ;57: 	if (!BufferView_writeStr(bufToWrite, info->fileName))


                      79 ;58: 	{


                      80 ;59: 		return FNAME_BUF_ERROR;


                      81 ;60: 	}


                      82 ;61: 	return FNAME_OK;


                      83 ;62: }


                      84 ;63: 


                      85 ;64: //! загружает файл по сигнатуре, всегда успешно


                      86 ;65: static void loadCfgFile(uint32_t signature, CfgFileInfo *info)


                      87 

                      88 ;69: 		&info->fileSize,


                      89 ;70: 		&info->time);


                      90 ;71: }


                      91 

                      92 ;72: 


                      93 ;73: bool CFGFS_init(void)


                      94 ;74: {


                      95 ;75: 	int i;


                      96 ;76: 	// чтение файлов в память, без проверки на результат 


                      97 ;77: 	for (i = 0; i < CFG_FILES_COUNT; ++i)


                      98 ;78: 	{


                      99 ;79: 		loadCfgFile(cfgFiles[i]->signature, cfgFiles[i]);


                     100 ;80: 	}


                     101 ;81: 	return TRUE;


                     102 ;82: }


                     103 ;83: 


                     104 ;84: FNameErrCode CFGFS_findFirst(StringView* startFileName, FSFindData* findData,


                     105 ;85: 	BufferView* fnameBuf)


                     106 ;86: {


                     107 ;87: 	FNameErrCode result;


                     108 ;88: 	


                     109 ;89: 	findData->fileIndex = 0;


                     110 ;90: 


                     111 ;91: 	while (findData->fileIndex < CFG_FILES_COUNT)



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a341.s
                     112 ;92: 	{


                     113 ;93: 		result = getCfgFileInfo(findData, fnameBuf);


                     114 ;94: 		findData->fileIndex++;


                     115 ;95: 		// файл найден


                     116 ;96: 		if (result == FNAME_OK)


                     117 ;97: 		{


                     118 ;98: 			break;


                     119 ;99: 		}


                     120 ;100: 		


                     121 ;101: 		// файл не найден - ищем дальше


                     122 ;102: 	}


                     123 ;103: 


                     124 ;104: 	return result;


                     125 ;105: }


                     126 ;106: 


                     127 ;107: FNameErrCode CFGFS_findNext(FSFindData* findData, BufferView* fnameBuf)


                     128 ;108: {


                     129 ;109: 	FNameErrCode result;	


                     130 ;110: 	result = getCfgFileInfo(findData, fnameBuf);	


                     131 ;111: 	findData->fileIndex++;


                     132 ;112: 	return result;


                     133 ;113: }


                     134 ;114: 


                     135 ;115: void CFGFS_findClose(FSFindData* findData)


                     136 

                     137 ;117: 	


                     138 ;118: }


                     139 

                     140 ;119: bool CFGFS_openFile(StringView* fileName, FRSM* frsm, FSFileAttr* attr)


                     141 ;120: {	


                     142 ;121: 	CfgFileInfo *info = NULL;


                     143 ;122: 	int i;


                     144 ;123: 


                     145 ;124: 	for (i = 0; i < CFG_FILES_COUNT; ++i)


                     146 ;125: 	{


                     147 ;126: 		if (StringView_cmpCStr(fileName, cfgFiles[i]->fileName) == 0)


                     148 ;127: 		{


                     149 ;128: 			info = cfgFiles[i];


                     150 ;129: 		}


                     151 ;130: 	}


                     152 ;131:     


                     153 ;132: 	// по идее такого быть не должно, т.к. если файл не найден - он не должен запрашиваться


                     154 ;133: 	if (!info || !info->pData)


                     155 ;134: 	{


                     156 ;135: 		return FALSE;


                     157 ;136: 	}


                     158 ;137: 


                     159 ;138: 	frsm->start = info->pData;


                     160 ;139: 	frsm->size = info->fileSize;


                     161 ;140: 	frsm->pos = 0;


                     162 ;141: 


                     163 ;142: 	attr->fileSize = info->fileSize;


                     164 ;143: 	attr->time = info->time;


                     165 ;144: 	attr->ms = 0;


                     166 ;145: 


                     167 ;146: 	return TRUE;


                     168 ;147: }


                     169 ;148: 


                     170 ;149: bool CFGFS_closeFile(FRSM* frsm)


                     171 

                     172 ;152: }



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a341.s
                     173 

                     174 	.text

                     175 	.align	4

                     176 getCfgFileInfo:

00000000 e92d4000    177 	stmfd	[sp]!,{lr}

00000004 e1a02001    178 	mov	r2,r1

00000008 e5901004    179 	ldr	r1,[r0,4]

0000000c e3510002    180 	cmp	r1,2

00000010 2a000005    181 	bhs	.L59

00000014 e59f31ec*   182 	ldr	r3,.L176

00000018 e7931101    183 	ldr	r1,[r3,r1 lsl 2]

0000001c e3510000    184 	cmp	r1,0

00000020 15913010    185 	ldrne	r3,[r1,16]

00000024 13530000    186 	cmpne	r3,0

00000028 1a000001    187 	bne	.L58

                     188 .L59:

0000002c e3a00001    189 	mov	r0,1

00000030 ea00000a    190 	b	.L53

                     191 .L58:

00000034 e5913014    192 	ldr	r3,[r1,20]

00000038 e5a03014    193 	str	r3,[r0,20]!

0000003c e5913018    194 	ldr	r3,[r1,24]

00000040 e3a0c000    195 	mov	r12,0

00000044 e9801008    196 	stmfa	[r0],{r3,r12}

00000048 e2811004    197 	add	r1,r1,4

0000004c e1a00002    198 	mov	r0,r2

00000050 eb000000*   199 	bl	BufferView_writeStr

00000054 e3500000    200 	cmp	r0,0

00000058 13a00000    201 	movne	r0,0

0000005c 03a00002    202 	moveq	r0,2

                     203 .L53:

00000060 e8bd4000    204 	ldmfd	[sp]!,{lr}

00000064 e12fff1e*   205 	ret	

                     206 	.endf	getCfgFileInfo

                     207 	.align	4

                     208 ;info	r1	local

                     209 

                     210 ;fileInfo	r0	param

                     211 ;bufToWrite	r2	param

                     212 

                     213 	.data

                     214 .L153:

                     215 	.text

                     216 

                     217 

                     218 	.align	4

                     219 	.align	4

                     220 CFGFS_init::

00000068 e92d4030    221 	stmfd	[sp]!,{r4-r5,lr}

0000006c e59f5194*   222 	ldr	r5,.L176

00000070 e3a04000    223 	mov	r4,0

00000074 e5951000    224 	ldr	r1,[r5]

00000078 e1a02004    225 	mov	r2,r4

0000007c e491000c    226 	ldr	r0,[r1],12

00000080 e8810014    227 	stmea	[r1],{r2,r4}

00000084 e2813008    228 	add	r3,r1,8

00000088 e281200c    229 	add	r2,r1,12

0000008c e52d2004    230 	str	r2,[sp,-4]!

00000090 e2812004    231 	add	r2,r1,4

00000094 eb000000*   232 	bl	loadRomModule

00000098 e5951004    233 	ldr	r1,[r5,4]


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a341.s
0000009c e1a05004    234 	mov	r5,r4

000000a0 e491000c    235 	ldr	r0,[r1],12

000000a4 e8810030    236 	stmea	[r1],{r4-r5}

000000a8 e2813008    237 	add	r3,r1,8

000000ac e281200c    238 	add	r2,r1,12

000000b0 e58d2000    239 	str	r2,[sp]

000000b4 e2812004    240 	add	r2,r1,4

000000b8 eb000000*   241 	bl	loadRomModule

000000bc e3a00001    242 	mov	r0,1

000000c0 e28dd004    243 	add	sp,sp,4

000000c4 e8bd8030    244 	ldmfd	[sp]!,{r4-r5,pc}

                     245 	.endf	CFGFS_init

                     246 	.align	4

                     247 

                     248 	.section ".bss","awb"

                     249 .L245:

                     250 	.data

                     251 	.text

                     252 

                     253 

                     254 	.align	4

                     255 	.align	4

                     256 CFGFS_findFirst::

000000c8 e92d4030    257 	stmfd	[sp]!,{r4-r5,lr}

000000cc e1a05002    258 	mov	r5,r2

000000d0 e1a04001    259 	mov	r4,r1

000000d4 e3a03000    260 	mov	r3,0

000000d8 e5843004    261 	str	r3,[r4,4]

                     262 .L256:

000000dc e1a01005    263 	mov	r1,r5

000000e0 e1a00004    264 	mov	r0,r4

000000e4 ebffffc5*   265 	bl	getCfgFileInfo

000000e8 e5943004    266 	ldr	r3,[r4,4]

000000ec e3500000    267 	cmp	r0,0

000000f0 e2833001    268 	add	r3,r3,1

000000f4 e5843004    269 	str	r3,[r4,4]

000000f8 13530002    270 	cmpne	r3,2

000000fc 3afffff6    271 	blo	.L256

00000100 e8bd8030    272 	ldmfd	[sp]!,{r4-r5,pc}

                     273 	.endf	CFGFS_findFirst

                     274 	.align	4

                     275 ;result	r0	local

                     276 

                     277 ;startFileName	none	param

                     278 ;findData	r4	param

                     279 ;fnameBuf	r5	param

                     280 

                     281 	.section ".bss","awb"

                     282 .L320:

                     283 	.data

                     284 	.text

                     285 

                     286 

                     287 	.align	4

                     288 	.align	4

                     289 CFGFS_findNext::

00000104 e92d4010    290 	stmfd	[sp]!,{r4,lr}

00000108 e1a04000    291 	mov	r4,r0

0000010c ebffffbb*   292 	bl	getCfgFileInfo

00000110 e5941004    293 	ldr	r1,[r4,4]

00000114 e2811001    294 	add	r1,r1,1


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a341.s
00000118 e5841004    295 	str	r1,[r4,4]

0000011c e8bd8010    296 	ldmfd	[sp]!,{r4,pc}

                     297 	.endf	CFGFS_findNext

                     298 	.align	4

                     299 

                     300 ;findData	r4	param

                     301 ;fnameBuf	none	param

                     302 

                     303 	.section ".bss","awb"

                     304 .L366:

                     305 	.data

                     306 	.text

                     307 

                     308 

                     309 	.align	4

                     310 	.align	4

                     311 CFGFS_openFile::

00000120 e92d44f0    312 	stmfd	[sp]!,{r4-r7,r10,lr}

00000124 e3a04000    313 	mov	r4,0

00000128 e59f50d8*   314 	ldr	r5,.L176

0000012c e1a06001    315 	mov	r6,r1

00000130 e5951000    316 	ldr	r1,[r5]

00000134 e1a07002    317 	mov	r7,r2

00000138 e2811004    318 	add	r1,r1,4

0000013c e1a0a000    319 	mov	r10,r0

00000140 eb000000*   320 	bl	StringView_cmpCStr

00000144 e3500000    321 	cmp	r0,0

00000148 e5950004    322 	ldr	r0,[r5,4]

0000014c 05954000    323 	ldreq	r4,[r5]

00000150 e2801004    324 	add	r1,r0,4

00000154 e1a0000a    325 	mov	r0,r10

00000158 eb000000*   326 	bl	StringView_cmpCStr

0000015c e3500000    327 	cmp	r0,0

00000160 05954004    328 	ldreq	r4,[r5,4]

00000164 e3540000    329 	cmp	r4,0

00000168 15940010    330 	ldrne	r0,[r4,16]

0000016c 13500000    331 	cmpne	r0,0

00000170 03a00000    332 	moveq	r0,0

00000174 0a000007    333 	beq	.L373

00000178 e5a6000c    334 	str	r0,[r6,12]!

0000017c e5942014    335 	ldr	r2,[r4,20]

00000180 e3a01000    336 	mov	r1,0

00000184 e9860006    337 	stmfa	[r6],{r1-r2}

00000188 e5940018    338 	ldr	r0,[r4,24]

0000018c e5872000    339 	str	r2,[r7]

00000190 e9870003    340 	stmfa	[r7],{r0-r1}

00000194 e3a00001    341 	mov	r0,1

                     342 .L373:

00000198 e8bd84f0    343 	ldmfd	[sp]!,{r4-r7,r10,pc}

                     344 	.endf	CFGFS_openFile

                     345 	.align	4

                     346 ;info	r4	local

                     347 

                     348 ;fileName	r10	param

                     349 ;frsm	r6	param

                     350 ;attr	r7	param

                     351 

                     352 	.section ".bss","awb"

                     353 .L522:

                     354 	.data

                     355 	.text


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a341.s
                     356 

                     357 

                     358 ;153: 


                     359 ;154: bool CFGFS_readFile(FRSM* frsm, BufferView* readBuf, bool* moreFollows)


                     360 	.align	4

                     361 	.align	4

                     362 CFGFS_readFile::

0000019c e92d4030    363 	stmfd	[sp]!,{r4-r5,lr}

                     364 ;155: {


                     365 

                     366 ;156: 	size_t readCount = frsm->size - frsm->pos;


                     367 

000001a0 e1a03001    368 	mov	r3,r1

000001a4 e1a04000    369 	mov	r4,r0

000001a8 e5941014    370 	ldr	r1,[r4,20]

000001ac e5940010    371 	ldr	r0,[r4,16]

000001b0 e1a05002    372 	mov	r5,r2

000001b4 e0512000    373 	subs	r2,r1,r0

                     374 ;157: 


                     375 ;158: 	if (readCount == 0)


                     376 

                     377 ;159: 	{


                     378 

                     379 ;160: 		return FALSE;


                     380 

000001b8 020200ff    381 	andeq	r0,r2,255

000001bc 0a00000d    382 	beq	.L545

                     383 ;161: 	}


                     384 ;162: 


                     385 ;163: 	readCount = BufferView_writeData(readBuf,


                     386 

000001c0 e5b4100c    387 	ldr	r1,[r4,12]!

000001c4 e0801001    388 	add	r1,r0,r1

000001c8 e1a00003    389 	mov	r0,r3

000001cc eb000000*   390 	bl	BufferView_writeData

                     391 ;164: 		frsm->start + frsm->pos, readCount);


                     392 ;165: 	*moreFollows = (readCount < frsm->size - frsm->pos);


                     393 

000001d0 e994000c    394 	ldmed	[r4],{r2-r3}

000001d4 e0431002    395 	sub	r1,r3,r2

000001d8 e1510000    396 	cmp	r1,r0

000001dc 83a01001    397 	movhi	r1,1

000001e0 93a01000    398 	movls	r1,0

000001e4 e5c51000    399 	strb	r1,[r5]

                     400 ;166: 	frsm->pos += readCount;


                     401 

000001e8 e5941004    402 	ldr	r1,[r4,4]

000001ec e0811000    403 	add	r1,r1,r0

000001f0 e5841004    404 	str	r1,[r4,4]

                     405 ;167: 	return TRUE;


                     406 

000001f4 e3a00001    407 	mov	r0,1

                     408 .L545:

000001f8 e8bd8030    409 	ldmfd	[sp]!,{r4-r5,pc}

                     410 	.endf	CFGFS_readFile

                     411 	.align	4

                     412 ;readCount	r2	local

                     413 

                     414 ;frsm	r4	param

                     415 ;readBuf	r3	param

                     416 ;moreFollows	r5	param


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a341.s
                     417 

                     418 	.section ".bss","awb"

                     419 .L597:

                     420 	.data

                     421 	.text

                     422 

                     423 ;168: }


                     424 	.align	4

                     425 	.align	4

                     426 CFGFS_findClose::

                     427 ;116: {


                     428 

000001fc e12fff1e*   429 	ret	

                     430 	.endf	CFGFS_findClose

                     431 	.align	4

                     432 

                     433 ;findData	none	param

                     434 

                     435 	.section ".bss","awb"

                     436 .L638:

                     437 	.data

                     438 	.text

                     439 	.align	4

                     440 	.align	4

                     441 CFGFS_closeFile::

                     442 ;150: {


                     443 

                     444 ;151: 	return TRUE;


                     445 

00000200 e3a00001    446 	mov	r0,1

00000204 e12fff1e*   447 	ret	

                     448 	.endf	CFGFS_closeFile

                     449 	.align	4

                     450 

                     451 ;frsm	none	param

                     452 

                     453 	.section ".bss","awb"

                     454 .L670:

                     455 	.data

                     456 	.text

                     457 	.align	4

                     458 .L176:

00000208 00000000*   459 	.data.w	cfgFiles

                     460 	.type	.L176,$object

                     461 	.size	.L176,4

                     462 

                     463 	.align	4

                     464 

                     465 	.data

                     466 .L692:

                     467 	.globl	icdFile

00000000 49434446    468 icdFile:	.data.b	70,68,67,73

                     469 ;	"icd.zip\000"

00000004 2e646369    470 	.data.b	105,99,100,46

00000008 0070697a    471 	.data.b	122,105,112,0

0000000c 00000000    472 	.space	4

00000010 00000000    473 	.space	4

00000014 00000000    474 	.space	4

00000018 00000000    475 	.space	4

                     476 	.type	icdFile,$object

                     477 	.size	icdFile,28


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a341.s
                     478 .L693:

                     479 	.globl	cidFile

0000001c 43494446    480 cidFile:	.data.b	70,68,73,67

                     481 ;	"cid.zip\000"

00000020 2e646963    482 	.data.b	99,105,100,46

00000024 0070697a    483 	.data.b	122,105,112,0

00000028 00000000    484 	.space	4

0000002c 00000000    485 	.space	4

00000030 00000000    486 	.space	4

00000034 00000000    487 	.space	4

                     488 	.type	cidFile,$object

                     489 	.size	cidFile,28

                     490 .L694:

                     491 	.globl	cfgFiles

00000038 00000000*   492 cfgFiles:	.data.w	.L692

0000003c 00000000*   493 	.data.w	.L693

                     494 	.type	cfgFiles,$object

                     495 	.size	cfgFiles,8

                     496 	.ghsnote version,6

                     497 	.ghsnote tools,3

                     498 	.ghsnote options,0

                     499 	.text

                     500 	.align	4

                     501 	.data

                     502 	.align	4

                     503 	.text

