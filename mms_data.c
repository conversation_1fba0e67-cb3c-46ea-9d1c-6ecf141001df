#include "mms_data.h"

#include "pwin_access.h"
#include "DataSlice.h"
#include "AsnEncoding.h"
#include "debug.h"
#include "iedmodel.h"

#include "IEDCompile/InnerAttributeTypes.h"
#include <types.h>
#include <string.h>
#include <stdint.h>

//Получение битов quality через сохранённые смещения в DataSlice
#define SET_QUALITY_BIT_FAST(offsName, bitNum) \
    if((accessInfo->offsName != -1)  \
        && DataSlice_getBoolFast(dataSliceWnd, accessInfo->offsName)) \
    { quality |= (1 << bitNum);}

uint16_t qualityFromBitsFast(void* dataSliceWnd, QualityAccsessInfo* accessInfo)
{
    uint16_t quality = 0;

	SET_QUALITY_BIT_FAST(goodInvalidOffset, 7);
	SET_QUALITY_BIT_FAST(reservedQuestionableOffset, 6);    
    SET_QUALITY_BIT_FAST(overflowOffset, 5);
    SET_QUALITY_BIT_FAST(outOfRangeOffset, 4);
    SET_QUALITY_BIT_FAST(badReferenceOffset, 3);
    SET_QUALITY_BIT_FAST(oscillatoryOffset, 2);
    SET_QUALITY_BIT_FAST(failureOffset, 1);
    SET_QUALITY_BIT_FAST(oldDataOffset, 0);
    SET_QUALITY_BIT_FAST(inconsistentOffset,15);
    SET_QUALITY_BIT_FAST(inaccurateOffset, 14);
    SET_QUALITY_BIT_FAST(processSubstitutedOffset, 13);
    SET_QUALITY_BIT_FAST(testOffset, 12);
    SET_QUALITY_BIT_FAST(operatorBlockedOffset, 11);
    return quality;
}

float readFloatValue(FloatAccsessInfo* accessInfo)
{
	return dataSliceGetFloatValue(accessInfo->valueOffset)  * accessInfo->multiplier;
}


//! проверяет на NAN
static __inline int isfnan(float value)
{
  unsigned int inan = *(unsigned int*)(void*)&value;
  return (inan & 0x7F800000) == 0x7F800000;
}

float readRealValue(FloatAccsessInfo* accessInfo)
{
    float result = dataSliceGetRealValue(accessInfo->valueOffset);

    if(isfnan(result))
    {
        return result;
    }

    return result  * accessInfo->multiplier;
}

int encodeReadFloat(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)
{
    float value;    

    if(determineSize)
    {
        int floatSize = 5;
        return floatSize + 2; // 2 for tag and length
    }
   
    value = readFloatValue(descrStruct);

    return BerEncoder_EncodeFloatWithTL(0x87, value, 32, 8, outBuf, bufPos);
}

int encodeReadReal(uint8_t* outBuf, int bufPos, void* descrStruct,
                   bool determineSize)
{
    float value;

    if(determineSize)
    {
        int floatSize = 5;
        return floatSize + 2; // 2 for tag and length
    }

    value = readRealValue(descrStruct);

    return BerEncoder_EncodeFloatWithTL(0x87, value, 32, 8, outBuf, bufPos);
}

int encodeReadFloatSett(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)
{
    float value;
    FloatAccsessInfo* accessInfo = descrStruct;

    if(determineSize)
    {
        int floatSize = 5;
        return floatSize + 2; // 2 for tag and length
    }

    value = getFloatSett(accessInfo->valueOffset) * accessInfo->multiplier;

    return BerEncoder_EncodeFloatWithTL(0x87, value, 32, 8, outBuf, bufPos);
}

int encodeReadRealSett(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)
{
    float value;
    FloatAccsessInfo* accessInfo = descrStruct;

    if(determineSize)
    {
        int floatSize = 5;
        return floatSize + 2; // 2 for tag and length
    }

    value = getRealSett(accessInfo->valueOffset) * accessInfo->multiplier;

    return BerEncoder_EncodeFloatWithTL(0x87, value, 32, 8, outBuf, bufPos);
}

int MMSData_encodeTimeStamp(uint8_t tag, uint64_t timeStamp, uint8_t* outBuf,
	int bufPos)
{
	int i;
	uint8_t timeStampBuf[8] = { 0 };	
	uint8_t* pTime = (uint8_t*)&timeStamp;
	pTime += 7; //На старший байт времени

	// в dataslice  Timequality передается вместе со временев в формате iec61850, 
	// поэтому копируется целиком
	for (i = 0; i < 8; ++i)
	{
		timeStampBuf[i] = *pTime;
		pTime--;
	}
	return BerEncoder_encodeOctetString(tag, timeStampBuf, 8, outBuf, bufPos);
}

int encodeReadTimeStamp(uint8_t* outBuf, int bufPos, bool determineSize)
{
	/*
	int i;
    uint8_t timeStampBuf[8] = {0};
    unsigned long long timeStamp = dataSliceGetTimeStamp();
	uint8_t* pTime = (uint8_t*)&timeStamp;
	pTime += 7; //На старший байт времени

	//Разворачиваем для big endian. Последний байт оставляем для TimeQuality
	for (i = 0; i < 7; ++i)
	{
		timeStampBuf[i] = *pTime;
		pTime--;
	}
	*/
	uint64_t timeStamp;

    if(determineSize)
    {
        return 10;
    }
	timeStamp = dataSliceGetTimeStamp();

    //return BerEncoder_encodeOctetString(0x91, timeStampBuf, 8, outBuf, bufPos);
	return MMSData_encodeTimeStamp(0x91, timeStamp, outBuf, bufPos);
}

//По значению dataValue возвращает соответствующее ему значение типа enumerator
int getEnumValue(int dataValue, EnumTableRecord* table, size_t tableSize)
{
	size_t i;
	for (i = 0; i < tableSize; ++i)
	{
		EnumTableRecord* pair = table + i;
		if (dataValue == pair->localValue)
		{
			return pair->mmsValue;
		}
	}
	//Если в таблице нет такого значения, возвращаем его напрямую
	return dataValue;
}

int getEnumDataValue(int enumValue, EnumTableRecord* table, size_t tableSize)
{
	size_t i;
	for (i = 0; i < tableSize; ++i)
	{
		EnumTableRecord* pair = table + i;
		if (enumValue == pair->mmsValue)
		{
			return pair->localValue;
		}
	}
	//Если в таблице нет такого значения, возвращаем его напрямую
	return enumValue;
}

int readIntSettValue(IntBoolAccessInfo* accessInfo)
{
	int dataValue = getIntSett(accessInfo->valueOffset);
	if (accessInfo->enumTableSize == 0)
	{
		return dataValue;
	}
	return getEnumValue(dataValue, accessInfo->enumTable,
		accessInfo->enumTableSize);
}

int readIntValue(IntBoolAccessInfo* accessInfo)
{
	int dataValue = dataSliceGetIntValue(accessInfo->valueOffset);
	if (accessInfo->enumTableSize == 0)
	{
		return dataValue;
	}
	return getEnumValue(dataValue, accessInfo->enumTable, 
		accessInfo->enumTableSize);
}

int encodeUInt32Value(uint8_t* outBuf, int bufPos, uint32_t value, 
	bool determineSize)
{
	if (determineSize)
	{
		int intSize = BerEncoder_UInt32determineEncodedSize(value);
		return intSize + 2; // 2 for tag and length
	}

	return BerEncoder_encodeUInt32WithTL(IEC61850_BER_UNSIGNED_INTEGER,
		value, outBuf, bufPos);
}

int encodeBoolValue(uint8_t* outBuf, int bufPos, bool value,
	bool determineSize)
{
	if (determineSize)
	{
		return 3;
	}

	return BerEncoder_encodeBoolean(IEC61850_BER_BOOLEAN, value, outBuf,
		bufPos);
}

int readCodedEnum(CodedEnumAccessInfo* accessInfo)
{
	uint8_t value = 0;
	int valIdx;
	for (valIdx = 0; valIdx < accessInfo->bitCount; ++valIdx)
	{
		int offset = accessInfo->valueOffsets[valIdx];
		value <<= 1;

		if (offset != -1)
		{
			value |= dataSliceGetBoolValue(offset);

		}
	}
	value <<= (8 - accessInfo->bitCount);
	return value;
}

int encodeReadCodedEnum(uint8_t* outBuf, int bufPos, void* descrStruct,
	bool determineSize)
{	
	//Поддерживается не более 8 бит		
	uint8_t value;
	CodedEnumAccessInfo* accessInfo = descrStruct;

	if (determineSize)
	{
		return 4;
	}

	if (accessInfo->bitCount > 8)
	{
		ERROR_REPORT("More than 8 bits is not supported");
	}	

	if (accessInfo->bitCount < 1)
	{
		ERROR_REPORT("Invalid coded enum: bitCount < 1");
	}
	
	value = readCodedEnum(accessInfo);

	return BerEncoder_encodeBitString(ASN_TYPEDESCRIPTION_BIT_STRING,
		accessInfo->bitCount, &value, outBuf, bufPos);	
}

int encodeOctetString8Value(uint8_t* outBuf, int bufPos, void* pValue,
	bool determineSize)
{
	if (determineSize)
	{
		return 10;
	}

	return BerEncoder_encodeOctetString(IEC61850_BER_OCTET_STRING, pValue, 8,
		outBuf, bufPos);
}

int encodeReadInt32(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)
{
	int value;
	IntBoolAccessInfo* accessInfo = descrStruct;

    value = readIntValue(accessInfo);

    if(determineSize)
    {
        int intSize = BerEncoder_Int32DetermineEncodedSize(value);
        return intSize + 2; // 2 for tag and length
    }
    
    return BerEncoder_EncodeInt32WithTL(0x85, value, outBuf, bufPos);
}

int encodeReadInt32U(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)
{
	uint32_t value;
	
	IntBoolAccessInfo* accessInfo = descrStruct;

	value = readIntValue(accessInfo);

	return encodeUInt32Value(outBuf, bufPos, value, determineSize);
}

int encodeReadInt32Sett(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)
{
	int value;

	IntBoolAccessInfo* accessInfo = descrStruct;

	value = readIntSettValue(accessInfo);

	if (determineSize)
	{
		int intSize = BerEncoder_Int32DetermineEncodedSize(value);
		return intSize + 2; // 2 for tag and length
	}

	return BerEncoder_EncodeInt32WithTL(0x85, value, outBuf, bufPos);
}

int encodeReadInt32USett(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)
{
    uint32_t value;

    IntBoolAccessInfo* accessInfo = descrStruct;

    value = readIntSettValue(accessInfo);

	return encodeUInt32Value(outBuf, bufPos, value, determineSize);
}


int readBoolValue(IntBoolAccessInfo* accessInfo)
{
	return dataSliceGetBoolValue(accessInfo->valueOffset);
}

int encodeReadBoolean(uint8_t* outBuf, int bufPos, void* descrStruct, 
	bool determineSize)
{
	int value = FALSE;

	IntBoolAccessInfo* accessInfo = descrStruct;

	value = readBoolValue(accessInfo);

	return encodeBoolValue(outBuf, bufPos, value, determineSize);		
}

int encodeAccessAttrQuality(uint8_t* outBuf, int bufPos, bool determineSize)
{
    //<Unknown len="1" tag="0x84" value="0xf3" valueStr="'\xf3'"/>
    if(determineSize)
    {
        return 3;
    }

    return BerEncoder_EncodeInt32WithTL(ASN_TYPEDESCRIPTION_BIT_STRING,
                                              -13, outBuf, bufPos);

}

int encodeAccessAttrBitString(int bitCount, uint8_t* outBuf, int bufPos, 
	bool determineSize)
{	
	//<Unknown len="1" tag="0x84" value="0xf3" valueStr="'\xf3'"/>
	if (determineSize)
	{
		return 3;
	}

	return BerEncoder_EncodeInt32WithTL(ASN_TYPEDESCRIPTION_BIT_STRING,
		-bitCount, outBuf, bufPos);

}

int encodeAccessAttrBitStringConst(int constPos, uint8_t* outBuf, int bufPos,
	bool determineSize)
{
	int bitCount;
	int padding;
	int len;
	if (determineSize)
	{
		return 3;
	}
	len = iedModel[constPos + 1];
	RET_IF_NOT(len >= 2 && len < 127, "Invalid bitstring constant length");
	padding = iedModel[constPos + 2];
	RET_IF_NOT(padding < 8, "Invalid bitstring constant length");
	bitCount = (len - 1) * 8 - padding;
	return encodeAccessAttrBitString(bitCount, outBuf, bufPos, FALSE);
}

int encodeAccessAttrTimeStamp(uint8_t* outBuf, int bufPos, bool determineSize)
{
    //<Unknown len="0" tag="0x91" value="" valueStr="''"/>
    if(determineSize)
    {
        return 2;
    }

    outBuf[bufPos++] = 0x91;
    outBuf[bufPos++] = 0;//Длина
    return bufPos;
}

int encodeAccessAttrFloat(uint8_t* outBuf, int bufPos, bool determineSize)
{
    if(determineSize)
    {
        return 8;
    }    

    // Описание типа FLOAT32
    bufPos = BerEncoder_encodeTL(ASN_TYPEDESCRIPTION_FLOAT, 6,
        outBuf, bufPos);

    //Ширина формата
    bufPos = BerEncoder_encodeUInt32WithTL(ASN_INTEGER, 32,
        outBuf, bufPos);

    //Ширина экспоненты
    bufPos = BerEncoder_encodeUInt32WithTL(ASN_INTEGER, 8,
        outBuf, bufPos);
    return bufPos;
}

int encodeAccessAttrString(uint8_t* outBuf, int bufPos, uint8_t tag, int size, 
	bool determineSize)
{
	if (determineSize)
	{		
		return BerEncoder_Int32DetermineEncodedSize(-size) + 2;
		//return 4;
	}

	// Описание типа 
	bufPos = BerEncoder_EncodeInt32WithTL(tag, -size, outBuf, bufPos);

	return bufPos;
}

int encodeAccessAttrInt(uint8_t* outBuf, int bufPos, uint8_t bitCount, 
	bool determineSize)
{
    //<Unknown len="1" tag="0x85" value="0x20" valueStr="' '"/>
    uint8_t description = bitCount;

    if(determineSize)
    {
        return 3;
    }

    // Описание типа INT
    return BerEncoder_encodeOctetString(IEC61850_BER_INTEGER, &description, 1,
		outBuf, bufPos);
}

int encodeAccessAttrUInt(uint8_t* outBuf, int bufPos, uint8_t bitCount, 
	bool determineSize)
{	
	uint8_t description = bitCount;	

	if (determineSize)
	{
		return 3;
	}

	// Описание типа INT
	return BerEncoder_encodeOctetString(IEC61850_BER_UNSIGNED_INTEGER, 
		&description, 1, outBuf, bufPos);
}

int encodeAccessAttrInt128(uint8_t* outBuf, int bufPos, bool determineSize)
{	
	uint8_t description[2] = { 0x00, 0x20 };

	if (determineSize)
	{
		return 4;
	}

	// Описание типа INT
	return BerEncoder_encodeOctetString(IEC61850_BER_INTEGER, description, 2,
		outBuf, bufPos);
}

int encodeAccessAttrBoolean(uint8_t* outBuf, int bufPos, bool determineSize)
{
    //<Unknown len="0" tag="0x83" value="" valueStr="''"/>
    if(determineSize)
    {
        return 2;
    }

    // Описание типа BOOLEAN (только тэг и длина. Данных никаких нет)
    return BerEncoder_encodeTL(IEC61850_BER_BOOLEAN, 0, outBuf, bufPos);
}

int encodeAccessAttrCodedEnum(uint8_t* outBuf, int bufPos, int accessDataPos,
	bool determineSize)
{	
	CodedEnumAccessInfo* pAccessInfo;
	if (determineSize)
	{	
		return 3;
	}
	pAccessInfo = (CodedEnumAccessInfo*)getAlignedDescrStruct(accessDataPos);
	if (pAccessInfo == NULL)
	{
		ERROR_REPORT("Unable to get access to the info struct");
		return 0;
	}

	return BerEncoder_EncodeInt32WithTL(ASN_TYPEDESCRIPTION_BIT_STRING,
		-pAccessInfo->bitCount, outBuf, bufPos);
}

int encodeAccessAttrEntryTime(uint8_t* outBuf, int bufPos, bool determineSize)
{
    if (determineSize)
    {
        return 3;
    }

    // Описание типа
    bufPos = BerEncoder_EncodeInt32WithTL(IEC61850_BER_BINARY_TIME,
                                          1, outBuf, bufPos);

    return bufPos;
}


int encodeAccessAttrConst(uint8_t* outBuf, int bufPos, int constPos, bool determineSize)
{
	uint8_t constTag = iedModel[constPos];
	if ((constTag & BER_TAG_CLASS_MASK) != BER_CONTEXT_SPECIFIC)
	{
		ERROR_REPORT("Invalid const tag %02X", constTag);
		return 0;
	}
	constTag &= ~BER_TAG_CLASS_MASK;

    switch (constTag) {		
    case IEC61850_BOOLEAN:
        return encodeAccessAttrBoolean(outBuf, bufPos, determineSize);
	case IEC61850_INT32:
		return encodeAccessAttrInt(outBuf, bufPos, 32, determineSize);
	case IEC61850_INT64:
		return encodeAccessAttrInt(outBuf, bufPos, 64, determineSize);
	case IEC61850_INT8U:
		return encodeAccessAttrUInt(outBuf, bufPos, 8, determineSize);
	case IEC61850_INT16U:
		return encodeAccessAttrUInt(outBuf, bufPos, 16, determineSize);
	case IEC61850_INT32U:
		return encodeAccessAttrUInt(outBuf, bufPos, 32, determineSize);
	case IEC61850_ENUMERATED:
		return encodeAccessAttrInt(outBuf, bufPos, 32, determineSize);
	case IEC61850_OCTET_STRING_6:
		return encodeAccessAttrString(outBuf, bufPos,
			IEC61850_BER_OCTET_STRING, 6, determineSize);
	case IEC61850_OCTET_STRING_64:
		return encodeAccessAttrString(outBuf, bufPos,
			IEC61850_BER_OCTET_STRING, 64, determineSize);
    case IEC61850_VISIBLE_STRING_32:
        return encodeAccessAttrString(outBuf, bufPos,
            IEC61850_BER_VISIBLE_STRING, 255, determineSize);
    case IEC61850_VISIBLE_STRING_64:
        return encodeAccessAttrString(outBuf, bufPos,
            IEC61850_BER_VISIBLE_STRING, 64, determineSize);
    case IEC61850_VISIBLE_STRING_65:
        return encodeAccessAttrString(outBuf, bufPos,
            IEC61850_BER_VISIBLE_STRING, 65, determineSize);
    case IEC61850_VISIBLE_STRING_129:
        return encodeAccessAttrString(outBuf, bufPos,
            IEC61850_BER_VISIBLE_STRING, 129, determineSize);
    case IEC61850_VISIBLE_STRING_255:
		return encodeAccessAttrString(outBuf, bufPos, 
			IEC61850_BER_VISIBLE_STRING, 255, determineSize);
	case IEC61850_UNICODE_STRING_255:
		return encodeAccessAttrString(outBuf, bufPos,
			IEC61850_BER_MMS_STRING, 255, determineSize);
    case IEC61850_GENERIC_BITSTRING:
	case IEC61850_QUALITY:
		return encodeAccessAttrBitStringConst(constPos, outBuf, bufPos, determineSize);    
    case IEC61850_ENTRY_TIME:
        return encodeAccessAttrEntryTime(outBuf, bufPos, determineSize);
    default:
		ERROR_REPORT("Invalid const tag %02X", constTag);
		return encodeAccessAttrString(outBuf, bufPos,
			IEC61850_BER_VISIBLE_STRING, 255, determineSize);        
    }
}
