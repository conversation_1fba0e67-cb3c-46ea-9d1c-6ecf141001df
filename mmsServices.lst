                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ps1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mmsservices.c -o gh_8ps1.o -list=mmsServices.lst C:\Users\<USER>\AppData\Local\Temp\gh_8ps1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_8ps1.s
Source File: mmsservices.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile mmsservices.c

                      11 ;		-o mmsServices.o

                      12 ;Source File:   mmsservices.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:06 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "mmsservices.h"


                      23 ;2: 


                      24 ;3: #include "AsnEncoding.h"


                      25 ;4: #include "mms_error.h"


                      26 ;5: #include "mms_read.h"


                      27 ;6: #include "mms_write.h"


                      28 ;7: #include "mms_get_name_list.h"


                      29 ;8: #include "mms_get_variable_access_attributes.h"


                      30 ;9: #include "mms_get_data_set_access_attr.h"


                      31 ;10: #include "mms_fs.h"


                      32 ;11: #include "mms.h"


                      33 ;12: #include "bufViewBER.h"


                      34 ;13: #include <debug.h>


                      35 ;14: #include <types.h>


                      36 ;15: #include <stddef.h>


                      37 ;16: #include <string.h>


                      38 ;17: 


                      39 ;18: static char* g_vendor = "MTRA";


                      40 ;19: static char* g_model = "device";


                      41 ;20: static char* g_revision = "1";


                      42 ;21: 


                      43 ;22: int mmsServer_handleIdentifyRequest(uint32_t invokeId, uint8_t* outBuf)


                      44 	.text

                      45 	.align	4

                      46 mmsServer_handleIdentifyRequest::

00000000 e92d4ff0     47 	stmfd	[sp]!,{r4-fp,lr}

                      48 ;23: {


                      49 

                      50 ;24:     int bufPos = 0;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ps1.s
                      51 

                      52 ;25: 


                      53 ;26:     uint32_t invokeIdLength = BerEncoder_UInt32determineEncodedSize(invokeId);


                      54 

00000004 e1a04001     55 	mov	r4,r1

00000008 e59f5394*    56 	ldr	r5,.L43

0000000c e24dd008     57 	sub	sp,sp,8

00000010 e58d0004     58 	str	r0,[sp,4]

00000014 eb000000*    59 	bl	BerEncoder_UInt32determineEncodedSize

00000018 e1a06000     60 	mov	r6,r0

                      61 ;27:     uint32_t vendorNameLength = strlen(g_vendor);


                      62 

0000001c e5950000     63 	ldr	r0,[r5]

00000020 eb000000*    64 	bl	strlen

00000024 e1a07000     65 	mov	r7,r0

                      66 ;28:     uint32_t modelNameLength = strlen(g_model);


                      67 

00000028 e5950004     68 	ldr	r0,[r5,4]

0000002c eb000000*    69 	bl	strlen

00000030 e1a0a000     70 	mov	r10,r0

                      71 ;29:     uint32_t revisionLength = strlen(g_revision);


                      72 

00000034 e5950008     73 	ldr	r0,[r5,8]

00000038 eb000000*    74 	bl	strlen

0000003c e1a0b000     75 	mov	fp,r0

                      76 ;30: 


                      77 ;31:     uint32_t identityLength = 3 +  BerEncoder_determineLengthSize(vendorNameLength)


                      78 

00000040 e1a00007     79 	mov	r0,r7

00000044 eb000000*    80 	bl	BerEncoder_determineLengthSize

00000048 e1a08000     81 	mov	r8,r0

0000004c e1a0000b     82 	mov	r0,fp

00000050 eb000000*    83 	bl	BerEncoder_determineLengthSize

00000054 e0809008     84 	add	r9,r0,r8

00000058 e1a0000a     85 	mov	r0,r10

0000005c eb000000*    86 	bl	BerEncoder_determineLengthSize

00000060 e0801009     87 	add	r1,r0,r9

00000064 e0811007     88 	add	r1,r1,r7

00000068 e081100a     89 	add	r1,r1,r10

0000006c e081100b     90 	add	r1,r1,fp

00000070 e2817003     91 	add	r7,r1,3

                      92 ;32:             + BerEncoder_determineLengthSize(modelNameLength) + BerEncoder_determineLengthSize(revisionLength)


                      93 ;33:             + vendorNameLength + modelNameLength + revisionLength;


                      94 ;34: 


                      95 ;35:     uint32_t identifyResponseLength = invokeIdLength + 2 + 1 + BerEncoder_determineLengthSize(identityLength)


                      96 

00000074 e1a00007     97 	mov	r0,r7

00000078 eb000000*    98 	bl	BerEncoder_determineLengthSize

0000007c e0861000     99 	add	r1,r6,r0

00000080 e0871001    100 	add	r1,r7,r1

00000084 e2811003    101 	add	r1,r1,3

                     102 ;36:             + identityLength;


                     103 ;37: 


                     104 ;38:     /* Identify response pdu */


                     105 ;39:     bufPos = BerEncoder_encodeTL(0xa1, identifyResponseLength, outBuf, bufPos);


                     106 

00000088 e1a02004    107 	mov	r2,r4

0000008c e3a03000    108 	mov	r3,0

00000090 e3a000a1    109 	mov	r0,161

00000094 eb000000*   110 	bl	BerEncoder_encodeTL

                     111 ;40: 



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ps1.s
                     112 ;41:     /* invokeId */


                     113 ;42:     bufPos = BerEncoder_encodeTL(0x02, invokeIdLength, outBuf, bufPos);


                     114 

00000098 e1a02004    115 	mov	r2,r4

0000009c e1a01006    116 	mov	r1,r6

000000a0 e1a03000    117 	mov	r3,r0

000000a4 e3a00002    118 	mov	r0,2

000000a8 eb000000*   119 	bl	BerEncoder_encodeTL

                     120 ;43:     bufPos = BerEncoder_encodeUInt32(invokeId, outBuf, bufPos);


                     121 

000000ac e1a02000    122 	mov	r2,r0

000000b0 e59d0004    123 	ldr	r0,[sp,4]

000000b4 e1a01004    124 	mov	r1,r4

000000b8 eb000000*   125 	bl	BerEncoder_encodeUInt32

                     126 ;44: 


                     127 ;45:     bufPos = BerEncoder_encodeTL(0xa2, identityLength, outBuf, bufPos);


                     128 

000000bc e1a02004    129 	mov	r2,r4

000000c0 e1a01007    130 	mov	r1,r7

000000c4 e1a03000    131 	mov	r3,r0

000000c8 e3a000a2    132 	mov	r0,162

000000cc eb000000*   133 	bl	BerEncoder_encodeTL

                     134 ;46:     bufPos = BerEncoder_encodeStringWithTL(0x80, g_vendor, outBuf, bufPos);


                     135 

000000d0 e1a02004    136 	mov	r2,r4

000000d4 e1a03000    137 	mov	r3,r0

000000d8 e5951000    138 	ldr	r1,[r5]

000000dc e3a00080    139 	mov	r0,128

000000e0 eb000000*   140 	bl	BerEncoder_encodeStringWithTL

                     141 ;47:     bufPos = BerEncoder_encodeStringWithTL(0x81, g_model, outBuf, bufPos);


                     142 

000000e4 e1a02004    143 	mov	r2,r4

000000e8 e1a03000    144 	mov	r3,r0

000000ec e5951004    145 	ldr	r1,[r5,4]

000000f0 e3a00081    146 	mov	r0,129

000000f4 eb000000*   147 	bl	BerEncoder_encodeStringWithTL

                     148 ;48:     bufPos = BerEncoder_encodeStringWithTL(0x82, g_revision, outBuf, bufPos);


                     149 

000000f8 e1a02004    150 	mov	r2,r4

000000fc e1a03000    151 	mov	r3,r0

00000100 e5951008    152 	ldr	r1,[r5,8]

00000104 e3a00082    153 	mov	r0,130

00000108 eb000000*   154 	bl	BerEncoder_encodeStringWithTL

                     155 ;49: 


                     156 ;50:     return bufPos;


                     157 

0000010c e28dd008    158 	add	sp,sp,8

00000110 e8bd8ff0    159 	ldmfd	[sp]!,{r4-fp,pc}

                     160 	.endf	mmsServer_handleIdentifyRequest

                     161 	.align	4

                     162 ;invokeIdLength	r6	local

                     163 ;vendorNameLength	r7	local

                     164 ;modelNameLength	r10	local

                     165 ;revisionLength	fp	local

                     166 ;identityLength	r7	local

                     167 

                     168 ;invokeId	[sp,4]	param

                     169 ;outBuf	r4	param

                     170 

                     171 	.data

                     172 .L30:


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ps1.s
                     173 .L31:

00000000 00000000*   174 g_vendor:	.data.w	.L35

                     175 	.type	g_vendor,$object

                     176 	.size	g_vendor,4

                     177 .L32:

00000004 00000000*   178 g_model:	.data.w	.L36

                     179 	.type	g_model,$object

                     180 	.size	g_model,4

                     181 .L33:

00000008 00000000*   182 g_revision:	.data.w	.L34

                     183 	.type	g_revision,$object

                     184 	.size	g_revision,4

                     185 	.section ".rodata","a"

                     186 .L34:;	"1\000"

00000000 0031       187 	.data.b	49,0

00000002 0000       188 	.space	2

                     189 .L35:;	"MTRA\000"

00000004 4152544d    190 	.data.b	77,84,82,65

00000008 00         191 	.data.b	0

00000009 000000     192 	.space	3

                     193 .L36:;	"device\000"

0000000c 69766564    194 	.data.b	100,101,118,105

00000010 6563       195 	.data.b	99,101

00000012 00         196 	.data.b	0

00000013 00         197 	.space	1

                     198 	.data

                     199 	.text

                     200 

                     201 ;51: }


                     202 

                     203 ;52: 


                     204 ;53: 


                     205 ;54: 


                     206 ;55: 


                     207 ;56: int handleConfirmedRequestPdu(IsoConnection* isoConn,


                     208 	.align	4

                     209 	.align	4

                     210 handleConfirmedRequestPdu::

00000114 e92d4ff0    211 	stmfd	[sp]!,{r4-fp,lr}

                     212 ;57:                                unsigned char* inBuf, int inBufPos, int inBufLen,


                     213 ;58:                                unsigned char* outBuf, int maxBufSize)


                     214 ;59: 


                     215 ;60: {	


                     216 

00000118 e1a09000    217 	mov	r9,r0

0000011c e24dd044    218 	sub	sp,sp,68

00000120 e59d6068    219 	ldr	r6,[sp,104]

00000124 e3a08000    220 	mov	r8,0

00000128 e58d800c    221 	str	r8,[sp,12]

                     222 ;61: 	BufferView inBufView;


                     223 ;62: 	BufferView outBufView;


                     224 ;63: 	MmsConnection* mmsConn = &isoConn->mmsConn;


                     225 

0000012c e1a0a001    226 	mov	r10,r1

00000130 e3a01f8f    227 	mov	r1,0x023c

00000134 e2611a49    228 	rsb	r1,r1,73<<12

00000138 e0807001    229 	add	r7,r0,r1

                     230 ;64: 


                     231 ;65:     int fullResponseSize = 0;


                     232 

                     233 ;66:     // Размер ответа на каждый запрос



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ps1.s
                     234 ;67:     int requestResponseSize;


                     235 ;68:     unsigned int invokeId = 0;


                     236 

                     237 ;69:     


                     238 ;70: 


                     239 ;71:     while(inBufPos < inBufLen)


                     240 

0000013c e1a05002    241 	mov	r5,r2

00000140 e1a0b003    242 	mov	fp,r3

00000144 e155000b    243 	cmp	r5,fp

00000148 aa0000cf    244 	bge	.L51

                     245 .L48:

                     246 ;72:     {


                     247 

                     248 ;73: 		uint32_t tag;


                     249 ;74: 		BufferView pktBufview;


                     250 ;75: 		//Размер данных тэга


                     251 ;76: 		size_t length;


                     252 ;77: 		


                     253 ;78: 		//Временная переменная для некоторых сервисов


                     254 ;79: 		bool serviceOK;


                     255 ;80: 


                     256 ;81: 		//Эта переменная проверяется только если requestResponseSize == 0.


                     257 ;82: 		//Тэги, которые просто содержат информацию, но не вызывают


                     258 ;83: 		//формирования ответа, должны устанавливать её в true.


                     259 ;84: 		//В противном случае requestResponseSize == 0 будет считаться


                     260 ;85: 		//необработанной ошибкой.


                     261 ;86: 		//Эту муть можно будет переделать когда сервисы будут явно возвращать


                     262 ;87: 		//наличие/отсутствие ошибки


                     263 ;88: 		bool tagOK = false;


                     264 

                     265 ;89: 


                     266 ;90: 		BufferView_init(&pktBufview, inBuf, inBufLen, inBufPos);


                     267 

0000014c e1a03005    268 	mov	r3,r5

00000150 e1a0200b    269 	mov	r2,fp

00000154 e1a0100a    270 	mov	r1,r10

00000158 e28d0020    271 	add	r0,sp,32

0000015c eb000000*   272 	bl	BufferView_init

                     273 ;91: 		if (!BufferView_decodeExtTL(&pktBufview, &tag, &length, NULL))


                     274 

00000160 e28d2014    275 	add	r2,sp,20

00000164 e28d1010    276 	add	r1,sp,16

00000168 e28d0020    277 	add	r0,sp,32

0000016c e3a03000    278 	mov	r3,0

00000170 eb000000*   279 	bl	BufferView_decodeExtTL

00000174 e3500000    280 	cmp	r0,0

00000178 0a0000c3    281 	beq	.L51

                     282 ;92: 		{


                     283 

                     284 ;93: 			//Не понятно, что делать с такой ошибкой


                     285 ;94: 			ERROR_REPORT("Unable to parse request");


                     286 ;95: 			break;


                     287 

                     288 ;96: 		}


                     289 ;97: 		inBufPos = pktBufview.pos;


                     290 

0000017c e59d5024    291 	ldr	r5,[sp,36]

                     292 ;98: 


                     293 ;99: 		// Пока некоторые операции используют непосредственно буфера,


                     294 ;100: 		// а некоторые - BufferView, готовим BufferView для каждой операции.



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ps1.s
                     295 ;101: 		// В будущем надо сделать инициализацию BufferView в начале функции,


                     296 ;102: 		// Для входящих данных и для исходящих.


                     297 ;103: 		BufferView_init(&inBufView, inBuf + inBufPos, length, 0);


                     298 

00000180 e59d2014    299 	ldr	r2,[sp,20]

00000184 e085100a    300 	add	r1,r5,r10

00000188 e28d0038    301 	add	r0,sp,56

0000018c e3a03000    302 	mov	r3,0

00000190 eb000000*   303 	bl	BufferView_init

                     304 ;104: 		BufferView_init(&outBufView, outBuf, maxBufSize - fullResponseSize, 0);


                     305 

00000194 e1a01006    306 	mov	r1,r6

00000198 e59d006c    307 	ldr	r0,[sp,108]

0000019c e1a04008    308 	mov	r4,r8

000001a0 e0402004    309 	sub	r2,r0,r4

000001a4 e28d002c    310 	add	r0,sp,44

000001a8 e3a03000    311 	mov	r3,0

000001ac eb000000*   312 	bl	BufferView_init

                     313 ;105: 		


                     314 ;106: 		switch (tag)


                     315 

000001b0 e59d1010    316 	ldr	r1,[sp,16]

000001b4 e25110a6    317 	subs	r1,r1,166

000001b8 2a000009    318 	bhs	.L349

000001bc e2911002    319 	adds	r1,r1,2

000001c0 8a00002f    320 	bhi	.L57

000001c4 0a000021    321 	beq	.L56

000001c8 e2911003    322 	adds	r1,r1,3

000001cc 0a000036    323 	beq	.L58

000001d0 e291101f    324 	adds	r1,r1,31

000001d4 0a000057    325 	beq	.L61

000001d8 e2911080    326 	adds	r1,r1,128

000001dc 1a000099    327 	bne	.L78

000001e0 ea000010    328 	b	.L55

                     329 .L349:

                     330 

000001e4 e2511000    331 	subs	r1,r1,0

000001e8 0a00003e    332 	beq	.L59

000001ec e2511006    333 	subs	r1,r1,6

000001f0 0a000046    334 	beq	.L60

000001f4 e3a04c9e    335 	mov	r4,158<<8

000001f8 e284409d    336 	add	r4,r4,157

000001fc e0511004    337 	subs	r1,r1,r4

00000200 0a000074    338 	beq	.L70

00000204 e2511001    339 	subs	r1,r1,1

00000208 0a00007a    340 	beq	.L74

0000020c e3a04d7c    341 	mov	r4,31<<8

00000210 e28440fe    342 	add	r4,r4,254

00000214 e0511004    343 	subs	r1,r1,r4

00000218 0a000059    344 	beq	.L66

0000021c e3510005    345 	cmp	r1,5

00000220 0a00004f    346 	beq	.L62

00000224 ea000087    347 	b	.L78

                     348 .L55:

                     349 ;107: 		{			


                     350 ;108: 		case ASN_INTEGER: //Invoke ID


                     351 ;109: 			invokeId = BerDecoder_decodeUint32(inBuf, length, inBufPos);							


                     352 

00000228 e1a02005    353 	mov	r2,r5

0000022c e59d1014    354 	ldr	r1,[sp,20]

00000230 e1a0000a    355 	mov	r0,r10


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ps1.s
00000234 eb000000*   356 	bl	BerDecoder_decodeUint32

00000238 e59d1014    357 	ldr	r1,[sp,20]

0000023c e58d000c    358 	str	r0,[sp,12]

                     359 ;110: 			requestResponseSize = 0;


                     360 

                     361 ;111: 			tagOK = true;


                     362 

                     363 ;218: 				outBuf, MMS_ERROR_ACCESS_OTHER);


                     364 ;219: 		}


                     365 ;220:         


                     366 ;221:         inBufPos += length;


                     367 

00000240 e0855001    368 	add	r5,r5,r1

                     369 ;222:         outBuf += requestResponseSize;        


                     370 

                     371 ;223:         fullResponseSize += requestResponseSize;		


                     372 

00000244 e155000b    373 	cmp	r5,fp

00000248 baffffbf    374 	blt	.L48

0000024c ea00008e    375 	b	.L51

                     376 .L56:

                     377 ;112: 			break;


                     378 ;113: 


                     379 ;114: 		case MMS_SERVICE_READ_CODE: 			


                     380 ;115:             requestResponseSize = mms_handleReadRequest(mmsConn,


                     381 

00000250 e59d006c    382 	ldr	r0,[sp,108]

00000254 e59d100c    383 	ldr	r1,[sp,12]

00000258 e0403008    384 	sub	r3,r0,r8

0000025c e1a02006    385 	mov	r2,r6

00000260 e88d000e    386 	stmea	[sp],{r1-r3}

00000264 e1a0300b    387 	mov	r3,fp

00000268 e1a02005    388 	mov	r2,r5

0000026c e1a0100a    389 	mov	r1,r10

00000270 e1a00007    390 	mov	r0,r7

00000274 eb000000*   391 	bl	mms_handleReadRequest

00000278 e1b04000    392 	movs	r4,r0

                     393 ;211: 				MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, outBuf);				


                     394 ;212: 			break;


                     395 ;213: 		}


                     396 ;214: 		if (requestResponseSize == 0 && !tagOK)


                     397 

0000027c 1a00007c    398 	bne	.L79

00000280 ea000076    399 	b	.L80

                     400 .L57:

                     401 ;116:                 inBuf, inBufPos, inBufLen, invokeId, outBuf, maxBufSize - fullResponseSize);


                     402 ;117: 


                     403 ;118: 			break;


                     404 ;119: 


                     405 ;120: 		case MMS_SERVICE_WRITE_CODE:			


                     406 ;121: 			TRACE("MMS write request received");


                     407 ;122: 			requestResponseSize = mms_handleWriteRequest(isoConn, inBuf, inBufPos,


                     408 

00000284 e59d100c    409 	ldr	r1,[sp,12]

00000288 e88d0042    410 	stmea	[sp],{r1,r6}

0000028c e1a0300b    411 	mov	r3,fp

00000290 e1a02005    412 	mov	r2,r5

00000294 e1a0100a    413 	mov	r1,r10

00000298 e1a00009    414 	mov	r0,r9

0000029c eb000000*   415 	bl	mms_handleWriteRequest

000002a0 e1b04000    416 	movs	r4,r0


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ps1.s
                     417 ;211: 				MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, outBuf);				


                     418 ;212: 			break;


                     419 ;213: 		}


                     420 ;214: 		if (requestResponseSize == 0 && !tagOK)


                     421 

000002a4 1a000072    422 	bne	.L79

000002a8 ea00006c    423 	b	.L80

                     424 .L58:

                     425 ;123: 				inBufLen, invokeId, outBuf);


                     426 ;124: 			break;


                     427 ;125: 


                     428 ;126: 		case MMS_SERVICE_GET_NAME_LIST_CODE:			


                     429 ;127: 			//TRACE("MMS_SERVICE_GET_NAME_LIST_CODE");


                     430 ;128: 			requestResponseSize = mms_handleGetNameListRequest(mmsConn, inBuf, inBufPos,


                     431 

000002ac e59d100c    432 	ldr	r1,[sp,12]

000002b0 e88d0042    433 	stmea	[sp],{r1,r6}

000002b4 e1a0300b    434 	mov	r3,fp

000002b8 e1a02005    435 	mov	r2,r5

000002bc e1a0100a    436 	mov	r1,r10

000002c0 e1a00007    437 	mov	r0,r7

000002c4 eb000000*   438 	bl	mms_handleGetNameListRequest

000002c8 e1a04000    439 	mov	r4,r0

                     440 ;129: 				inBufLen, invokeId, outBuf);


                     441 ;130: 			debugSendUshort("requestResponseSize", requestResponseSize);


                     442 

000002cc e1a01804    443 	mov	r1,r4 lsl 16

000002d0 e28f0000*   444 	adr	r0,.L399

000002d4 e1a01821    445 	mov	r1,r1 lsr 16

000002d8 eb000000*   446 	bl	debugSendUshort

                     447 ;211: 				MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, outBuf);				


                     448 ;212: 			break;


                     449 ;213: 		}


                     450 ;214: 		if (requestResponseSize == 0 && !tagOK)


                     451 

000002dc e3540000    452 	cmp	r4,0

000002e0 1a000063    453 	bne	.L79

000002e4 ea00005d    454 	b	.L80

                     455 .L59:

                     456 ;131: 			break;


                     457 ;132: 


                     458 ;133: 		case MMS_SERVICE_GET_VARIABLE_ACCESS_ATRIBUTES_CODE:


                     459 ;134: 			//TRACE("MMS_SERVICE_GET_VARIABLE_ACCESS_ATRIBUTES_CODE");


                     460 ;135: 


                     461 ;136: 			requestResponseSize = mms_handleGetVariableAccessAttr(mmsConn, inBuf, inBufPos,


                     462 

000002e8 e59d100c    463 	ldr	r1,[sp,12]

000002ec e88d0042    464 	stmea	[sp],{r1,r6}

000002f0 e1a0300b    465 	mov	r3,fp

000002f4 e1a02005    466 	mov	r2,r5

000002f8 e1a0100a    467 	mov	r1,r10

000002fc e1a00007    468 	mov	r0,r7

00000300 eb000000*   469 	bl	mms_handleGetVariableAccessAttr

00000304 e1b04000    470 	movs	r4,r0

                     471 ;211: 				MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, outBuf);				


                     472 ;212: 			break;


                     473 ;213: 		}


                     474 ;214: 		if (requestResponseSize == 0 && !tagOK)


                     475 

00000308 1a000059    476 	bne	.L79

0000030c ea000053    477 	b	.L80


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ps1.s
                     478 .L60:

                     479 ;137: 				inBufLen, invokeId, outBuf);


                     480 ;138: 			break;


                     481 ;139: 


                     482 ;140: 		case MMS_SERVICE_GET_DATA_SET_ATTRIBUTES_CODE:


                     483 ;141: 			//TRACE("MMS_SERVICE_GET_DATA_SET_ATTRIBUTES_CODE");


                     484 ;142: 			requestResponseSize = mms_handleGetDataSetAccessAttr(mmsConn, inBuf, inBufPos,


                     485 

00000310 e59d100c    486 	ldr	r1,[sp,12]

00000314 e88d0042    487 	stmea	[sp],{r1,r6}

00000318 e1a0300b    488 	mov	r3,fp

0000031c e1a02005    489 	mov	r2,r5

00000320 e1a0100a    490 	mov	r1,r10

00000324 e1a00007    491 	mov	r0,r7

00000328 eb000000*   492 	bl	mms_handleGetDataSetAccessAttr

0000032c e1b04000    493 	movs	r4,r0

                     494 ;211: 				MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, outBuf);				


                     495 ;212: 			break;


                     496 ;213: 		}


                     497 ;214: 		if (requestResponseSize == 0 && !tagOK)


                     498 

00000330 1a00004f    499 	bne	.L79

00000334 ea000049    500 	b	.L80

                     501 .L61:

                     502 ;143: 				inBufLen, invokeId, outBuf);


                     503 ;144: 			break;


                     504 ;145: 


                     505 ;146: 		case MMS_SERVICE_IDENTIFY_CODE:


                     506 ;147: 			TRACE("Identify service");


                     507 ;148: 			requestResponseSize = mmsServer_handleIdentifyRequest(invokeId, outBuf);


                     508 

00000338 e59d000c    509 	ldr	r0,[sp,12]

0000033c e1a01006    510 	mov	r1,r6

00000340 ebffff2e*   511 	bl	mmsServer_handleIdentifyRequest

00000344 e1a04000    512 	mov	r4,r0

                     513 ;149: 			debugSendUshort("Identify service returned:", requestResponseSize);


                     514 

00000348 e1a01804    515 	mov	r1,r4 lsl 16

0000034c e28f0000*   516 	adr	r0,.L400

00000350 e1a01821    517 	mov	r1,r1 lsr 16

00000354 eb000000*   518 	bl	debugSendUshort

                     519 ;211: 				MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, outBuf);				


                     520 ;212: 			break;


                     521 ;213: 		}


                     522 ;214: 		if (requestResponseSize == 0 && !tagOK)


                     523 

00000358 e3540000    524 	cmp	r4,0

0000035c 1a000044    525 	bne	.L79

00000360 ea00003e    526 	b	.L80

                     527 .L62:

                     528 ;150: 			break;


                     529 ;151: 


                     530 ;152: 		case MMS_SERVICE_FILE_DIRECTORY_REQUEST:			


                     531 ;153: 			serviceOK = mms_handleFileDirRequest(mmsConn,


                     532 

00000364 e28d302c    533 	add	r3,sp,44

00000368 e59d200c    534 	ldr	r2,[sp,12]

0000036c e28d1038    535 	add	r1,sp,56

00000370 e1a00007    536 	mov	r0,r7

00000374 eb000000*   537 	bl	mms_handleFileDirRequest

                     538 ;154: 				&inBufView, invokeId, &outBufView);



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ps1.s
                     539 ;155: 			if (serviceOK)


                     540 

                     541 ;183: 				&inBufView, invokeId, &outBufView);


                     542 ;184: 			if (serviceOK)


                     543 

00000378 e3500000    544 	cmp	r0,0

0000037c 0a000028    545 	beq	.L75

00000380 ea000023    546 	b	.L76

                     547 .L66:

                     548 ;156: 			{


                     549 

                     550 ;157: 				requestResponseSize = outBufView.pos;


                     551 

                     552 ;158: 			}


                     553 ;159: 			else


                     554 ;160: 			{


                     555 

                     556 ;161: 				ERROR_REPORT("handleFileDirRequest error");


                     557 ;162: 				requestResponseSize = 0;


                     558 

                     559 ;163: 			}


                     560 ;164: 


                     561 ;165: 			break;


                     562 ;166: 


                     563 ;167: 		case MMS_SERVICE_FILE_OPEN_REQUEST:			


                     564 ;168: 			serviceOK = mms_handleFileOpenRequest(mmsConn,


                     565 

00000384 e28d302c    566 	add	r3,sp,44

00000388 e59d200c    567 	ldr	r2,[sp,12]

0000038c e28d1038    568 	add	r1,sp,56

00000390 e1a00007    569 	mov	r0,r7

00000394 eb000000*   570 	bl	mms_handleFileOpenRequest

                     571 ;169: 				&inBufView, invokeId, &outBufView);


                     572 ;170: 			if (serviceOK)


                     573 

                     574 ;183: 				&inBufView, invokeId, &outBufView);


                     575 ;184: 			if (serviceOK)


                     576 

00000398 e3500000    577 	cmp	r0,0

0000039c 0a000020    578 	beq	.L75

000003a0 ea00001b    579 	b	.L76

                     580 	.align	4

                     581 .L43:

000003a4 00000000*   582 	.data.w	.L30

                     583 	.type	.L43,$object

                     584 	.size	.L43,4

                     585 

                     586 .L399:

                     587 ;	"requestResponseSize\000"

000003a8 75716572    588 	.data.b	114,101,113,117

000003ac 52747365    589 	.data.b	101,115,116,82

000003b0 6f707365    590 	.data.b	101,115,112,111

000003b4 5365736e    591 	.data.b	110,115,101,83

000003b8 00657a69    592 	.data.b	105,122,101,0

                     593 	.align 4

                     594 

                     595 	.type	.L399,$object

                     596 	.size	.L399,4

                     597 

                     598 .L400:

                     599 ;	"Identify service returned:\000"


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ps1.s
000003bc 6e656449    600 	.data.b	73,100,101,110

000003c0 79666974    601 	.data.b	116,105,102,121

000003c4 72657320    602 	.data.b	32,115,101,114

000003c8 65636976    603 	.data.b	118,105,99,101

000003cc 74657220    604 	.data.b	32,114,101,116

000003d0 656e7275    605 	.data.b	117,114,110,101

000003d4 3a64       606 	.data.b	100,58

000003d6 00         607 	.data.b	0

000003d7 00         608 	.align 4

                     609 

                     610 	.type	.L400,$object

                     611 	.size	.L400,4

                     612 

                     613 .L70:

                     614 ;171: 			{


                     615 

                     616 ;172: 				requestResponseSize = outBufView.pos;


                     617 

                     618 ;173: 			}


                     619 ;174: 			else


                     620 ;175: 			{


                     621 

                     622 ;176: 				ERROR_REPORT("handleFileOpenRequest error");


                     623 ;177: 				requestResponseSize = 0;


                     624 

                     625 ;178: 			}


                     626 ;179: 			break;


                     627 ;180: 


                     628 ;181: 		case MMS_SERVICE_FILE_READ_REQUEST:							


                     629 ;182: 			serviceOK = mms_handleFileReadRequest(mmsConn,


                     630 

000003d8 e28d302c    631 	add	r3,sp,44

000003dc e59d200c    632 	ldr	r2,[sp,12]

000003e0 e28d1038    633 	add	r1,sp,56

000003e4 e1a00007    634 	mov	r0,r7

000003e8 eb000000*   635 	bl	mms_handleFileReadRequest

                     636 ;183: 				&inBufView, invokeId, &outBufView);


                     637 ;184: 			if (serviceOK)


                     638 

000003ec e3500000    639 	cmp	r0,0

000003f0 0a00000b    640 	beq	.L75

000003f4 ea000006    641 	b	.L76

                     642 .L74:

                     643 ;185: 			{


                     644 

                     645 ;186: 				requestResponseSize = outBufView.pos;


                     646 

                     647 ;187: 			}


                     648 ;188: 			else


                     649 ;189: 			{


                     650 

                     651 ;190: 				ERROR_REPORT("handleFileReadRequest error");


                     652 ;191: 				requestResponseSize = 0;


                     653 

                     654 ;192: 			}


                     655 ;193: 			break;


                     656 ;194: 		case MMS_SERVICE_FILE_CLOSE_REQUEST:							


                     657 ;195: 			serviceOK = mms_handleFileCloseRequest(mmsConn,


                     658 

000003f8 e28d302c    659 	add	r3,sp,44

000003fc e59d200c    660 	ldr	r2,[sp,12]


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ps1.s
00000400 e28d1038    661 	add	r1,sp,56

00000404 e1a00007    662 	mov	r0,r7

00000408 eb000000*   663 	bl	mms_handleFileCloseRequest

                     664 ;196: 				&inBufView, invokeId, &outBufView);


                     665 ;197: 			if (serviceOK)


                     666 

                     667 ;183: 				&inBufView, invokeId, &outBufView);


                     668 ;184: 			if (serviceOK)


                     669 

0000040c e3500000    670 	cmp	r0,0

00000410 0a000003    671 	beq	.L75

                     672 .L76:

                     673 ;198: 			{


                     674 

                     675 ;199: 				requestResponseSize = outBufView.pos;


                     676 

00000414 e59d4030    677 	ldr	r4,[sp,48]

                     678 ;211: 				MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, outBuf);				


                     679 ;212: 			break;


                     680 ;213: 		}


                     681 ;214: 		if (requestResponseSize == 0 && !tagOK)


                     682 

00000418 e3540000    683 	cmp	r4,0

0000041c 1a000014    684 	bne	.L79

00000420 ea00000e    685 	b	.L80

                     686 .L75:

                     687 ;200: 			}


                     688 ;201: 			else


                     689 ;202: 			{


                     690 

                     691 ;203: 				ERROR_REPORT("handleFileReadRequest error");


                     692 ;204: 				requestResponseSize = 0;


                     693 

                     694 ;215: 		{


                     695 

                     696 ;216: 			ERROR_REPORT("Unknown MMS service error");


                     697 ;217: 			requestResponseSize = CreateMmsConfirmedErrorPdu(invokeId,


                     698 

00000424 e1a01006    699 	mov	r1,r6

00000428 e59d000c    700 	ldr	r0,[sp,12]

0000042c e3a02050    701 	mov	r2,80

00000430 eb000000*   702 	bl	CreateMmsConfirmedErrorPdu

                     703 ;218: 				outBuf, MMS_ERROR_ACCESS_OTHER);


                     704 ;219: 		}


                     705 ;220:         


                     706 ;221:         inBufPos += length;


                     707 

00000434 e59d1014    708 	ldr	r1,[sp,20]

00000438 e0866000    709 	add	r6,r6,r0

                     710 ;223:         fullResponseSize += requestResponseSize;		


                     711 

0000043c e0855001    712 	add	r5,r5,r1

                     713 ;222:         outBuf += requestResponseSize;        


                     714 

00000440 e0888000    715 	add	r8,r8,r0

00000444 ea00000e    716 	b	.L46

                     717 .L78:

                     718 ;205: 			}


                     719 ;206: 			break;


                     720 ;207: 


                     721 ;208: 		default:



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ps1.s
                     722 ;209: 			ERROR_REPORT("Unsupperted MMS tag %04X", tag);


                     723 ;210: 			requestResponseSize = mms_createMmsRejectPdu(&invokeId,


                     724 

00000448 e1a02006    725 	mov	r2,r6

0000044c e28d000c    726 	add	r0,sp,12

00000450 e3a01067    727 	mov	r1,103

00000454 eb000000*   728 	bl	mms_createMmsRejectPdu

00000458 e1b04000    729 	movs	r4,r0

                     730 ;211: 				MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE, outBuf);				


                     731 ;212: 			break;


                     732 ;213: 		}


                     733 ;214: 		if (requestResponseSize == 0 && !tagOK)


                     734 

0000045c 1a000004    735 	bne	.L79

                     736 .L80:

                     737 ;215: 		{


                     738 

                     739 ;216: 			ERROR_REPORT("Unknown MMS service error");


                     740 ;217: 			requestResponseSize = CreateMmsConfirmedErrorPdu(invokeId,


                     741 

00000460 e1a01006    742 	mov	r1,r6

00000464 e59d000c    743 	ldr	r0,[sp,12]

00000468 e3a02050    744 	mov	r2,80

0000046c eb000000*   745 	bl	CreateMmsConfirmedErrorPdu

00000470 e1a04000    746 	mov	r4,r0

                     747 .L79:

                     748 ;218: 				outBuf, MMS_ERROR_ACCESS_OTHER);


                     749 ;219: 		}


                     750 ;220:         


                     751 ;221:         inBufPos += length;


                     752 

00000474 e59d1014    753 	ldr	r1,[sp,20]

00000478 e0866004    754 	add	r6,r6,r4

                     755 ;223:         fullResponseSize += requestResponseSize;		


                     756 

0000047c e0855001    757 	add	r5,r5,r1

                     758 ;222:         outBuf += requestResponseSize;        


                     759 

00000480 e0888004    760 	add	r8,r8,r4

                     761 .L46:

00000484 e155000b    762 	cmp	r5,fp

00000488 baffff2f    763 	blt	.L48

                     764 .L51:

                     765 ;224:     }


                     766 ;225: 


                     767 ;226: 	//TRACE("fullResponseSize = %d", fullResponseSize);


                     768 ;227: 	VERIFY(fullResponseSize <= maxBufSize)


                     769 ;228: 	


                     770 ;229:     return fullResponseSize;


                     771 

0000048c e1a00008    772 	mov	r0,r8

00000490 e28dd044    773 	add	sp,sp,68

00000494 e8bd8ff0    774 	ldmfd	[sp]!,{r4-fp,pc}

                     775 	.endf	handleConfirmedRequestPdu

                     776 	.align	4

                     777 ;inBufView	[sp,56]	local

                     778 ;outBufView	[sp,44]	local

                     779 ;mmsConn	r7	local

                     780 ;fullResponseSize	r8	local

                     781 ;requestResponseSize	r4	local

                     782 ;invokeId	[sp,12]	local


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ps1.s
                     783 ;tag	[sp,16]	local

                     784 ;pktBufview	[sp,32]	local

                     785 ;length	[sp,20]	local

                     786 ;serviceOK	r0	local

                     787 ;.L347	.L352	static

                     788 ;.L348	.L353	static

                     789 

                     790 ;isoConn	r9	param

                     791 ;inBuf	r10	param

                     792 ;inBufPos	r5	param

                     793 ;inBufLen	fp	param

                     794 ;outBuf	r6	param

                     795 ;maxBufSize	[sp,108]	param

                     796 

                     797 	.section ".bss","awb"

                     798 .L346:

                     799 	.data

                     800 	.text

                     801 

                     802 ;230: }


                     803 	.align	4

                     804 ;g_vendor	.L31	static

                     805 ;g_model	.L32	static

                     806 ;g_revision	.L33	static

                     807 ;.L401	.L35	static

                     808 ;.L402	.L36	static

                     809 ;.L403	.L34	static

                     810 

                     811 	.data

                     812 	.ghsnote version,6

                     813 	.ghsnote tools,3

                     814 	.ghsnote options,0

                     815 	.text

                     816 	.align	4

                     817 	.data

                     818 	.align	4

                     819 	.section ".rodata","a"

                     820 	.align	4

                     821 	.text

