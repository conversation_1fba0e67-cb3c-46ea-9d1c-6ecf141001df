build:
        @echo Generating <<build.cmd.tmp
-map
-e _init00
-Y UL,..\..\..\..\AT91CORE\bin
-L ..\..\..\..\AT91CORE\bin
-a
-Q n
..\..\..\..\AT91CORE\bin\c0_bss.obj
..\..\..\..\AT91CORE\bin\gmalloc.lib
..\..\..\..\AT91CORE\bin\ARMLIBV4.lib
..\..\..\..\AT91CORE\bin\CLib.lib
$(LWIP_LIBS)
$(SYSLOG_LIB)
$(ETHBUS_LIB)
..\..\..\..\AT91CORE\GHSLIB\lib\libansi.a
..\..\..\..\AT91CORE\GHSLIB\lib\libarch.a
..\..\..\..\AT91CORE\GHSLIB\lib\libind.a
..\..\..\..\AT91CORE\GHSLIB\lib\indarchk.o
..\..\..\..\AT91CORE\GHSLIB\lib\indarchj.o
..\..\..\..\AT91CORE\GHSLIB\lib\ccvsprnt.o
..\..\..\..\AT91CORE\GHSLIB\lib\ccllout.o
..\..\..\..\AT91CORE\GHSLIB\lib\ccefgout.o


MEMORY
{        
        SRAM1 : ORIGIN = 0x70528644 , LENGTH = 5000K
}

SECTIONS
{
	.text      ALIGN ( 4 )  : > SRAM1
	.interfunc ALIGN ( 4 )  : > .
	.rodata    ALIGN ( 4 )  : > .
	.fixaddr   ALIGN ( 4 )  : > .
	.fixtype   ALIGN ( 4 )  : > .	
	.data      ALIGN ( 4 )  : > .
	.bss       ALIGN ( 4 )  : > .
	.ghsinfo   ALIGN ( 4 )  : > .
	.stack	   ALIGN ( 4 ) MIN_SIZE ( __STACK_SIZE ) : > .
}
<<KEEP
