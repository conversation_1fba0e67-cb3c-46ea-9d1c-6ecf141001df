                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8dg1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=out_queue.c -o gh_8dg1.o -list=out_queue.lst C:\Users\<USER>\AppData\Local\Temp\gh_8dg1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_8dg1.s
Source File: out_queue.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile out_queue.c -o

                      11 ;		out_queue.o

                      12 ;Source File:   out_queue.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:07 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "out_queue.h"


                      23 ;2: 


                      24 ;3: #include <platform_critical_section.h>


                      25 ;4: #include <stddef.h>


                      26 ;5: 


                      27 ;6: void OutQueue_init(OutQueue* self)


                      28 	.text

                      29 	.align	4

                      30 OutQueue_init::

00000000 e92d4010     31 	stmfd	[sp]!,{r4,lr}

                      32 ;7: {


                      33 

                      34 ;8:     CriticalSection_Init(&self->cs);


                      35 

00000004 e1a04000     36 	mov	r4,r0

00000008 eb000000*    37 	bl	CriticalSection_Init

                      38 ;9: 	self->head = -1;


                      39 

0000000c e3e00000     40 	mvn	r0,0

00000010 e5840004     41 	str	r0,[r4,4]

00000014 e8bd8010     42 	ldmfd	[sp]!,{r4,pc}

                      43 	.endf	OutQueue_init

                      44 	.align	4

                      45 

                      46 ;self	r4	param

                      47 

                      48 	.section ".bss","awb"

                      49 .L30:

                      50 	.data


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8dg1.s
                      51 	.text

                      52 

                      53 ;10: }


                      54 

                      55 ;11: 


                      56 ;12: void OutQueue_done(OutQueue* self)


                      57 	.align	4

                      58 	.align	4

                      59 OutQueue_done::

                      60 ;13: {


                      61 

                      62 ;14: 	CriticalSection_Done(&self->cs);	


                      63 

00000018 ea000000*    64 	b	CriticalSection_Done

                      65 	.endf	OutQueue_done

                      66 	.align	4

                      67 

                      68 ;self	none	param

                      69 

                      70 	.section ".bss","awb"

                      71 .L62:

                      72 	.data

                      73 	.text

                      74 

                      75 ;15: }


                      76 

                      77 ;16: 


                      78 ;17: bool OutQueue_isEmpty(OutQueue* self)


                      79 	.align	4

                      80 	.align	4

                      81 OutQueue_isEmpty::

                      82 ;18: {


                      83 

                      84 ;19: 	return self->head == -1;


                      85 

0000001c e5900004     86 	ldr	r0,[r0,4]

00000020 e3700001     87 	cmn	r0,1

00000024 03a00001     88 	moveq	r0,1

00000028 13a00000     89 	movne	r0,0

0000002c e12fff1e*    90 	ret	

                      91 	.endf	OutQueue_isEmpty

                      92 	.align	4

                      93 

                      94 ;self	r0	param

                      95 

                      96 	.section ".bss","awb"

                      97 .L97:

                      98 	.data

                      99 	.text

                     100 

                     101 ;20: };


                     102 

                     103 ;21: 


                     104 ;22: bool OutQueue_insert(OutQueue* self, void* item)


                     105 	.align	4

                     106 	.align	4

                     107 OutQueue_insert::

00000030 e92d4030    108 	stmfd	[sp]!,{r4-r5,lr}

                     109 ;23: {


                     110 

                     111 ;24: 	bool result = FALSE;



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8dg1.s
                     112 

                     113 ;25:     CriticalSection_Lock(&self->cs);


                     114 

00000034 e1a04001    115 	mov	r4,r1

00000038 e1a05000    116 	mov	r5,r0

0000003c eb000000*   117 	bl	CriticalSection_Lock

                     118 ;26: 	if (self->head == -1)


                     119 

00000040 e5950004    120 	ldr	r0,[r5,4]

00000044 e3700001    121 	cmn	r0,1

00000048 1a000006    122 	bne	.L106

                     123 ;27: 	{


                     124 

                     125 ;28: 		//Empty queue


                     126 ;29: 		self->tail = 0;


                     127 

0000004c e3a0c000    128 	mov	r12,0

                     129 ;30: 		self->queue[0] = item;


                     130 

00000050 e1a0e004    131 	mov	lr,r4

                     132 ;31: 		self->head = 1;


                     133 

00000054 e3a04001    134 	mov	r4,1

00000058 e9855010    135 	stmfa	[r5],{r4,r12,lr}

                     136 ;32: 		result = TRUE;


                     137 

                     138 ;47: 	}


                     139 ;48:     CriticalSection_Unlock(&self->cs);


                     140 

0000005c e1a00005    141 	mov	r0,r5

00000060 eb000000*   142 	bl	CriticalSection_Unlock

                     143 ;49:     return result;


                     144 

00000064 ea000012    145 	b	.L104

                     146 .L106:

                     147 ;33: 	}


                     148 ;34: 	else if (self->head == self->tail)


                     149 

00000068 e5951008    150 	ldr	r1,[r5,8]

0000006c e1500001    151 	cmp	r0,r1

00000070 1a000003    152 	bne	.L109

                     153 ;35: 	{


                     154 

                     155 ;36: 		//Overflow


                     156 ;37: 		result = FALSE;


                     157 

00000074 e3a04000    158 	mov	r4,0

                     159 ;47: 	}


                     160 ;48:     CriticalSection_Unlock(&self->cs);


                     161 

00000078 e1a00005    162 	mov	r0,r5

0000007c eb000000*   163 	bl	CriticalSection_Unlock

                     164 ;49:     return result;


                     165 

00000080 ea00000b    166 	b	.L104

                     167 .L109:

                     168 ;38: 	}


                     169 ;39: 	else


                     170 ;40: 	{


                     171 

                     172 ;41: 		self->queue[self->head++] = item;



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8dg1.s
                     173 

00000084 e5950004    174 	ldr	r0,[r5,4]

00000088 e2801001    175 	add	r1,r0,1

0000008c e5851004    176 	str	r1,[r5,4]

00000090 e0850100    177 	add	r0,r5,r0 lsl 2

00000094 e580400c    178 	str	r4,[r0,12]

                     179 ;42: 		if (self->head == OUT_QUEUE_SIZE)


                     180 

00000098 e5950004    181 	ldr	r0,[r5,4]

0000009c e3a04001    182 	mov	r4,1

                     183 ;47: 	}


                     184 ;48:     CriticalSection_Unlock(&self->cs);


                     185 

000000a0 e3500008    186 	cmp	r0,8

                     187 ;43: 		{


                     188 

                     189 ;44: 			self->head = 0;


                     190 

000000a4 03a00000    191 	moveq	r0,0

000000a8 05850004    192 	streq	r0,[r5,4]

                     193 ;45: 		}


                     194 ;46: 		result = TRUE;


                     195 

000000ac e1a00005    196 	mov	r0,r5

000000b0 eb000000*   197 	bl	CriticalSection_Unlock

                     198 ;49:     return result;


                     199 

                     200 .L104:

000000b4 e1a00004    201 	mov	r0,r4

000000b8 e8bd8030    202 	ldmfd	[sp]!,{r4-r5,pc}

                     203 	.endf	OutQueue_insert

                     204 	.align	4

                     205 ;result	r4	local

                     206 

                     207 ;self	r5	param

                     208 ;item	r4	param

                     209 

                     210 	.section ".bss","awb"

                     211 .L202:

                     212 	.data

                     213 	.text

                     214 

                     215 ;50: }


                     216 

                     217 ;51: 


                     218 ;52: void* OutQueue_get(OutQueue* self)


                     219 	.align	4

                     220 	.align	4

                     221 OutQueue_get::

000000bc e92d4030    222 	stmfd	[sp]!,{r4-r5,lr}

                     223 ;53: {


                     224 

                     225 ;54: 	void* result = NULL;


                     226 

                     227 ;55:     CriticalSection_Lock(&self->cs);


                     228 

000000c0 e1a05000    229 	mov	r5,r0

000000c4 eb000000*   230 	bl	CriticalSection_Lock

                     231 ;56: 	if (self->head == -1)


                     232 

000000c8 e5951004    233 	ldr	r1,[r5,4]


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8dg1.s
000000cc e3710001    234 	cmn	r1,1

000000d0 1a000003    235 	bne	.L229

                     236 ;57: 	{


                     237 

                     238 ;58: 		//Empty


                     239 ;59: 		result = NULL;


                     240 

000000d4 e3a04000    241 	mov	r4,0

                     242 ;71: 		}


                     243 ;72: 	}


                     244 ;73:     CriticalSection_Unlock(&self->cs);


                     245 

000000d8 e1a00005    246 	mov	r0,r5

000000dc eb000000*   247 	bl	CriticalSection_Unlock

                     248 ;74:     return result;


                     249 

000000e0 ea00000c    250 	b	.L227

                     251 .L229:

                     252 ;60: 	}


                     253 ;61: 	else


                     254 ;62: 	{


                     255 

                     256 ;63: 		result = self->queue[self->tail++];


                     257 

000000e4 e5952008    258 	ldr	r2,[r5,8]

000000e8 e2820001    259 	add	r0,r2,1

000000ec e0852102    260 	add	r2,r5,r2 lsl 2

000000f0 e5850008    261 	str	r0,[r5,8]

000000f4 e592400c    262 	ldr	r4,[r2,12]

                     263 ;64: 		if (self->tail == OUT_QUEUE_SIZE)


                     264 

000000f8 e3500008    265 	cmp	r0,8

                     266 ;65: 		{


                     267 

                     268 ;66: 			self->tail = 0;


                     269 

000000fc 03a00000    270 	moveq	r0,0

00000100 05850008    271 	streq	r0,[r5,8]

                     272 ;67: 		}


                     273 ;68: 		if (self->tail == self->head)


                     274 

00000104 e1500001    275 	cmp	r0,r1

                     276 ;69: 		{


                     277 

                     278 ;70: 			self->head = -1;


                     279 

00000108 03e00000    280 	mvneq	r0,0

0000010c 05850004    281 	streq	r0,[r5,4]

                     282 ;71: 		}


                     283 ;72: 	}


                     284 ;73:     CriticalSection_Unlock(&self->cs);


                     285 

00000110 e1a00005    286 	mov	r0,r5

00000114 eb000000*   287 	bl	CriticalSection_Unlock

                     288 ;74:     return result;


                     289 

                     290 .L227:

00000118 e1a00004    291 	mov	r0,r4

0000011c e8bd8030    292 	ldmfd	[sp]!,{r4-r5,pc}

                     293 	.endf	OutQueue_get

                     294 	.align	4


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8dg1.s
                     295 ;result	r4	local

                     296 

                     297 ;self	r5	param

                     298 

                     299 	.section ".bss","awb"

                     300 .L300:

                     301 	.data

                     302 	.text

                     303 

                     304 ;75: }


                     305 	.align	4

                     306 

                     307 	.data

                     308 	.ghsnote version,6

                     309 	.ghsnote tools,3

                     310 	.ghsnote options,0

                     311 	.text

                     312 	.align	4

