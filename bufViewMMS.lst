                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7b81.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=bufViewMMS.c -o gh_7b81.o -list=bufViewMMS.lst C:\Users\<USER>\AppData\Local\Temp\gh_7b81.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_7b81.s
Source File: bufViewMMS.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile bufViewMMS.c

                      11 ;		-o bufViewMMS.o

                      12 ;Source File:   bufViewMMS.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:57 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "bufViewMMS.h"


                      23 ;2: 


                      24 ;3: #include "AsnEncoding.h"


                      25 ;4: 


                      26 ;5: 


                      27 ;6: 


                      28 ;7: bool BufView_decodeObjectName(BufferView *bv, StringView *domainId,


                      29 	.text

                      30 	.align	4

                      31 BufView_decodeObjectName::

00000000 e92d4010     32 	stmfd	[sp]!,{r4,lr}

                      33 ;8:                               StringView *itemId)


                      34 ;9: {


                      35 

                      36 ;10:     int nameLen = BerDecoder_DecodeObjectName(bv->p, bv->pos, bv->len,


                      37 

00000004 e24dd00c     38 	sub	sp,sp,12

00000008 e1a04000     39 	mov	r4,r0

0000000c e2813004     40 	add	r3,r1,4

00000010 e1a0c001     41 	mov	r12,r1

00000014 e88d100c     42 	stmea	[sp],{r2-r3,r12}

00000018 e2823004     43 	add	r3,r2,4

0000001c e8940007     44 	ldmfd	[r4],{r0-r2}

00000020 eb000000*    45 	bl	BerDecoder_DecodeObjectName

                      46 ;11:         (uint8_t**)&itemId->p, (int*)&itemId->len,


                      47 ;12:         (uint8_t**)&domainId->p, (int*)&domainId->len);


                      48 ;13: 


                      49 ;14:     if(nameLen < 0)


                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7b81.s
00000024 e3500000     51 	cmp	r0,0

                      52 ;15:     {


                      53 

                      54 ;16:         return false;


                      55 

00000028 b3a00000     56 	movlt	r0,0

                      57 ;17:     }


                      58 ;18: 


                      59 ;19:     bv->pos = nameLen;


                      60 

0000002c a5840004     61 	strge	r0,[r4,4]

                      62 ;20:     return true;


                      63 

00000030 a3a00001     64 	movge	r0,1

00000034 e28dd00c     65 	add	sp,sp,12

00000038 e8bd8010     66 	ldmfd	[sp]!,{r4,pc}

                      67 	.endf	BufView_decodeObjectName

                      68 	.align	4

                      69 ;nameLen	r0	local

                      70 

                      71 ;bv	r4	param

                      72 ;domainId	r1	param

                      73 ;itemId	r2	param

                      74 

                      75 	.section ".bss","awb"

                      76 .L43:

                      77 	.data

                      78 	.text

                      79 

                      80 ;21: }


                      81 	.align	4

                      82 

                      83 	.data

                      84 	.ghsnote version,6

                      85 	.ghsnote tools,3

                      86 	.ghsnote options,0

                      87 	.text

                      88 	.align	4

