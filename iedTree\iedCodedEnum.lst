                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1qo1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedCodedEnum.c -o iedTree\gh_1qo1.o -list=iedTree/iedCodedEnum.lst C:\Users\<USER>\AppData\Local\Temp\gh_1qo1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_1qo1.s
Source File: iedCodedEnum.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		iedTree/iedCodedEnum.c -o iedTree/iedCodedEnum.o

                      12 ;Source File:   iedTree/iedCodedEnum.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:50 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "iedCodedEnum.h"


                      23 ;2: 


                      24 ;3: 


                      25 ;4: #include "debug.h"


                      26 ;5: #include "../DataSlice.h"


                      27 ;6: #include "iedFinalDA.h"


                      28 ;7: #include "iedTree.h"


                      29 ;8: #include "../AsnEncoding.h"


                      30 ;9: 


                      31 ;10: #define CODEDENUM_ENCODED_SIZE 4


                      32 ;11: 


                      33 ;12: static void updateFromDataSlice(IEDEntity entity)


                      34 	.text

                      35 	.align	4

                      36 updateFromDataSlice:

00000000 e92d48f3     37 	stmfd	[sp]!,{r0-r1,r4-r7,fp,lr}

                      38 ;13: {	


                      39 

00000004 e59d0000     40 	ldr	r0,[sp]

00000008 e3a04000     41 	mov	r4,0

                      42 ;14: 	TerminalItem* termItem = entity->extInfo;	


                      43 

0000000c e5901058     44 	ldr	r1,[r0,88]

                      45 ;15: 	size_t bitCount = termItem->ce.bitCount;


                      46 

00000010 e1a07004     47 	mov	r7,r4

00000014 e5912004     48 	ldr	r2,[r1,4]

                      49 ;16: 	int* dsOffsets = termItem->ce.dsOffsets;


                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1qo1.s
00000018 e281b008     51 	add	fp,r1,8

                      52 ;17: 	uint8_t value = 0;


                      53 

                      54 ;18: 


                      55 ;19: 	size_t i;


                      56 ;20: 	for(i = 0; i < bitCount; ++i)


                      57 

0000001c e3520000     58 	cmp	r2,0

00000020 a1a00002     59 	movge	r0,r2

00000024 b3a00000     60 	movlt	r0,0

00000028 e58d0004     61 	str	r0,[sp,4]

0000002c e1b061a0     62 	movs	r6,r0 lsr 3

00000030 0a00001e     63 	beq	.L34

00000034 e281500c     64 	add	r5,r1,12

                      65 .L50:

00000038 e1a01107     66 	mov	r1,r7 lsl 2

0000003c e19100bb     67 	ldrh	r0,[r1,fp]

00000040 eb000000*    68 	bl	DataSlice_getBoolFastCurrDS

00000044 e1804084     69 	orr	r4,r0,r4 lsl 1

00000048 e0d502b0     70 	ldrh	r0,[r5],32

0000004c eb000000*    71 	bl	DataSlice_getBoolFastCurrDS

00000050 e1804084     72 	orr	r4,r0,r4 lsl 1

00000054 e15501bc     73 	ldrh	r0,[r5,-28]

00000058 eb000000*    74 	bl	DataSlice_getBoolFastCurrDS

0000005c e1804084     75 	orr	r4,r0,r4 lsl 1

00000060 e15501b8     76 	ldrh	r0,[r5,-24]

00000064 eb000000*    77 	bl	DataSlice_getBoolFastCurrDS

00000068 e1804084     78 	orr	r4,r0,r4 lsl 1

0000006c e15501b4     79 	ldrh	r0,[r5,-20]

00000070 eb000000*    80 	bl	DataSlice_getBoolFastCurrDS

00000074 e1804084     81 	orr	r4,r0,r4 lsl 1

00000078 e15501b0     82 	ldrh	r0,[r5,-16]

0000007c eb000000*    83 	bl	DataSlice_getBoolFastCurrDS

00000080 e1804084     84 	orr	r4,r0,r4 lsl 1

00000084 e15500bc     85 	ldrh	r0,[r5,-12]

00000088 e2877008     86 	add	r7,r7,8

0000008c eb000000*    87 	bl	DataSlice_getBoolFastCurrDS

00000090 e1804084     88 	orr	r4,r0,r4 lsl 1

00000094 e1a04084     89 	mov	r4,r4 lsl 1

00000098 e15500b8     90 	ldrh	r0,[r5,-8]

0000009c e20440ff     91 	and	r4,r4,255

000000a0 eb000000*    92 	bl	DataSlice_getBoolFastCurrDS

000000a4 e1844000     93 	orr	r4,r4,r0

000000a8 e2566001     94 	subs	r6,r6,1

000000ac 1affffe1     95 	bne	.L50

                      96 .L34:

000000b0 e59d0004     97 	ldr	r0,[sp,4]

000000b4 e2106007     98 	ands	r6,r0,7

000000b8 0a000008     99 	beq	.L4

                     100 .L54:

000000bc e1a04084    101 	mov	r4,r4 lsl 1

000000c0 e1a01107    102 	mov	r1,r7 lsl 2

000000c4 e19100bb    103 	ldrh	r0,[r1,fp]

000000c8 e20440ff    104 	and	r4,r4,255

000000cc eb000000*   105 	bl	DataSlice_getBoolFastCurrDS

000000d0 e1844000    106 	orr	r4,r4,r0

000000d4 e2877001    107 	add	r7,r7,1

000000d8 e2566001    108 	subs	r6,r6,1

000000dc 1afffff6    109 	bne	.L54

                     110 .L4:

                     111 ;24: 	}



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1qo1.s
                     112 ;25: 


                     113 ;26: 	if(entity->codedEnumValue == value)


                     114 

000000e0 e59d0000    115 	ldr	r0,[sp]

000000e4 e5d01030    116 	ldrb	r1,[r0,48]

000000e8 e1510004    117 	cmp	r1,r4

                     118 ;27: 	{


                     119 

                     120 ;28: 		entity->changed = TRGOP_NONE;


                     121 

000000ec 03a01000    122 	moveq	r1,0

000000f0 05801028    123 	streq	r1,[r0,40]

000000f4 0a000007    124 	beq	.L2

                     125 ;29: 	}


                     126 ;30: 	else


                     127 ;31: 	{


                     128 

                     129 ;32: 		entity->changed = entity->trgOps;


                     130 

000000f8 e5901024    131 	ldr	r1,[r0,36]

000000fc e5c04030    132 	strb	r4,[r0,48]

                     133 ;34: 		IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());


                     134 

00000100 e5801028    135 	str	r1,[r0,40]

                     136 ;33: 		entity->codedEnumValue = value;


                     137 

00000104 eb000000*   138 	bl	dataSliceGetTimeStamp

00000108 e1a02001    139 	mov	r2,r1

0000010c e1a01000    140 	mov	r1,r0

00000110 e59d0000    141 	ldr	r0,[sp]

00000114 eb000000*   142 	bl	IEDEntity_setTimeStamp

                     143 .L2:

00000118 e8bd48f3    144 	ldmfd	[sp]!,{r0-r1,r4-r7,fp,lr}

0000011c e12fff1e*   145 	ret	

                     146 	.endf	updateFromDataSlice

                     147 	.align	4

                     148 ;termItem	r1	local

                     149 ;bitCount	r2	local

                     150 ;dsOffsets	fp	local

                     151 ;value	r4	local

                     152 ;i	r7	local

                     153 

                     154 ;entity	[sp]	param

                     155 

                     156 	.section ".bss","awb"

                     157 .L247:

                     158 	.data

                     159 	.text

                     160 

                     161 ;35: 	}


                     162 ;36: }


                     163 

                     164 ;37: 


                     165 ;38: static bool calcReadLen(IEDEntity entity, size_t* pLen )


                     166 	.align	4

                     167 	.align	4

                     168 calcReadLen:

                     169 ;39: {	


                     170 

                     171 ;40: 	*pLen = CODEDENUM_ENCODED_SIZE;


                     172 


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1qo1.s
00000120 e3a00004    173 	mov	r0,4

00000124 e5810000    174 	str	r0,[r1]

                     175 ;41: 	return true;


                     176 

00000128 e3a00001    177 	mov	r0,1

0000012c e12fff1e*   178 	ret	

                     179 	.endf	calcReadLen

                     180 	.align	4

                     181 

                     182 ;entity	none	param

                     183 ;pLen	r1	param

                     184 

                     185 	.section ".bss","awb"

                     186 .L302:

                     187 	.data

                     188 	.text

                     189 

                     190 ;42: }


                     191 

                     192 ;43: 


                     193 ;44: static bool encodeRead(IEDEntity entity, BufferView* outBuf)


                     194 	.align	4

                     195 	.align	4

                     196 encodeRead:

00000130 e92d4070    197 	stmfd	[sp]!,{r4-r6,lr}

00000134 e24dd008    198 	sub	sp,sp,8

00000138 e1a05000    199 	mov	r5,r0

                     200 ;45: {


                     201 

                     202 ;46: 	TerminalItem* termItem = entity->extInfo;


                     203 

0000013c e5950058    204 	ldr	r0,[r5,88]

                     205 ;47: 	size_t bitCount = termItem->ce.bitCount;


                     206 

00000140 e28d2004    207 	add	r2,sp,4

00000144 e5906004    208 	ldr	r6,[r0,4]

00000148 e1a04001    209 	mov	r4,r1

0000014c e1a00004    210 	mov	r0,r4

00000150 e3a01004    211 	mov	r1,4

00000154 eb000000*   212 	bl	BufferView_alloc

                     213 ;48: 


                     214 ;49: 	uint8_t* encodeBuf;


                     215 ;50: 


                     216 ;51: 	if(!BufferView_alloc(outBuf,CODEDENUM_ENCODED_SIZE, &encodeBuf))


                     217 

00000158 e3500000    218 	cmp	r0,0

                     219 ;52: 	{


                     220 

                     221 ;53: 		ERROR_REPORT("Unable to allocate buffer");


                     222 ;54: 		return false;


                     223 

0000015c 0a00000a    224 	beq	.L309

                     225 ;55: 	}


                     226 ;56: 


                     227 ;57: 	// Возвращаемое значение не нужно,


                     228 ;58: 	// потому что функция не возвращает ошибки, а размер известен заранее


                     229 ;59: 	BerEncoder_encodeUcharBitString(ASN_TYPEDESCRIPTION_BIT_STRING,


                     230 

00000160 e3a00000    231 	mov	r0,0

00000164 e58d0000    232 	str	r0,[sp]

00000168 e59d3004    233 	ldr	r3,[sp,4]


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1qo1.s
0000016c e5d52030    234 	ldrb	r2,[r5,48]

00000170 e1a01006    235 	mov	r1,r6

00000174 e3a00084    236 	mov	r0,132

00000178 eb000000*   237 	bl	BerEncoder_encodeUcharBitString

                     238 ;60: 									bitCount,  entity->codedEnumValue, encodeBuf, 0);


                     239 ;61: 


                     240 ;62: 	outBuf->pos += CODEDENUM_ENCODED_SIZE;


                     241 

0000017c e5940004    242 	ldr	r0,[r4,4]

00000180 e2800004    243 	add	r0,r0,4

00000184 e5840004    244 	str	r0,[r4,4]

                     245 ;63: 	return true;


                     246 

00000188 e3a00001    247 	mov	r0,1

                     248 .L309:

0000018c e28dd008    249 	add	sp,sp,8

00000190 e8bd4070    250 	ldmfd	[sp]!,{r4-r6,lr}

00000194 e12fff1e*   251 	ret	

                     252 	.endf	encodeRead

                     253 	.align	4

                     254 ;termItem	r0	local

                     255 ;bitCount	r6	local

                     256 ;encodeBuf	[sp,4]	local

                     257 

                     258 ;entity	r5	param

                     259 ;outBuf	r4	param

                     260 

                     261 	.section ".bss","awb"

                     262 .L362:

                     263 	.data

                     264 	.text

                     265 

                     266 ;64: }


                     267 

                     268 ;65: 


                     269 ;66: void IEDCodedEnum_init(IEDEntity entity)


                     270 	.align	4

                     271 	.align	4

                     272 IEDCodedEnum_init::

00000198 e92d4cf7    273 	stmfd	[sp]!,{r0-r2,r4-r7,r10-fp,lr}

0000019c e59d0000    274 	ldr	r0,[sp]

000001a0 e5901058    275 	ldr	r1,[r0,88]

000001a4 e3a07000    276 	mov	r7,0

000001a8 e5913000    277 	ldr	r3,[r1]

000001ac e281a008    278 	add	r10,r1,8

                     279 ;87: 


                     280 ;88: 


                     281 ;89: 	for (i = 0; i < bitCount; ++i)


                     282 

000001b0 e5932004    283 	ldr	r2,[r3,4]

000001b4 e58d3008    284 	str	r3,[sp,8]

000001b8 e3520008    285 	cmp	r2,8

000001bc 83a02008    286 	movhi	r2,8

                     287 ;67: {	


                     288 

                     289 ;68: 	TerminalItem* extInfo;	


                     290 ;69: 	//accessInfo из бинарника модели


                     291 ;70: 	CodedEnumAccessInfo* accessInfo;


                     292 ;71: 	size_t bitCount;


                     293 ;72: 	int* dsOffsets;


                     294 ;73: 	size_t i;



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1qo1.s
                     295 ;74: 


                     296 ;75: 	extInfo = entity->extInfo;


                     297 

                     298 ;76: 	accessInfo = extInfo->accessInfo;


                     299 

                     300 ;77: 


                     301 ;78: 	bitCount = accessInfo->bitCount;


                     302 

                     303 ;79: 	if(bitCount > 8)


                     304 

                     305 

                     306 

                     307 ;83: 	}


                     308 ;84: 	extInfo->ce.bitCount = bitCount;


                     309 

000001c0 e5812004    310 	str	r2,[r1,4]

                     311 ;85: 


                     312 ;86: 	dsOffsets = extInfo->ce.dsOffsets;


                     313 

000001c4 e3520000    314 	cmp	r2,0

000001c8 a1a00002    315 	movge	r0,r2

000001cc b3a00000    316 	movlt	r0,0

000001d0 e58d0004    317 	str	r0,[sp,4]

000001d4 e1b061a0    318 	movs	r6,r0 lsr 3

000001d8 0a00001c    319 	beq	.L402

000001dc e2834008    320 	add	r4,r3,8

000001e0 e281500c    321 	add	r5,r1,12

                     322 .L418:

000001e4 e4940004    323 	ldr	r0,[r4],4

000001e8 eb000000*   324 	bl	DataSlice_getBoolOffset

000001ec e78a0107    325 	str	r0,[r10,r7 lsl 2]

000001f0 e4940004    326 	ldr	r0,[r4],4

000001f4 eb000000*   327 	bl	DataSlice_getBoolOffset

000001f8 e4850020    328 	str	r0,[r5],32

000001fc e4940004    329 	ldr	r0,[r4],4

00000200 eb000000*   330 	bl	DataSlice_getBoolOffset

00000204 e505001c    331 	str	r0,[r5,-28]

00000208 e4940004    332 	ldr	r0,[r4],4

0000020c eb000000*   333 	bl	DataSlice_getBoolOffset

00000210 e5050018    334 	str	r0,[r5,-24]

00000214 e4940004    335 	ldr	r0,[r4],4

00000218 eb000000*   336 	bl	DataSlice_getBoolOffset

0000021c e5050014    337 	str	r0,[r5,-20]

00000220 e4940004    338 	ldr	r0,[r4],4

00000224 eb000000*   339 	bl	DataSlice_getBoolOffset

00000228 e5050010    340 	str	r0,[r5,-16]

0000022c e4940004    341 	ldr	r0,[r4],4

00000230 eb000000*   342 	bl	DataSlice_getBoolOffset

00000234 e505000c    343 	str	r0,[r5,-12]

00000238 e4940004    344 	ldr	r0,[r4],4

0000023c e2877008    345 	add	r7,r7,8

00000240 eb000000*   346 	bl	DataSlice_getBoolOffset

00000244 e5050008    347 	str	r0,[r5,-8]

00000248 e2566001    348 	subs	r6,r6,1

0000024c 1affffe4    349 	bne	.L418

                     350 .L402:

00000250 e59d0004    351 	ldr	r0,[sp,4]

00000254 e2106007    352 	ands	r6,r0,7

00000258 0a000008    353 	beq	.L380

0000025c e59d0008    354 	ldr	r0,[sp,8]

00000260 e2801008    355 	add	r1,r0,8


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1qo1.s
00000264 e0814107    356 	add	r4,r1,r7 lsl 2

                     357 .L422:

00000268 e4940004    358 	ldr	r0,[r4],4

0000026c eb000000*   359 	bl	DataSlice_getBoolOffset

00000270 e78a0107    360 	str	r0,[r10,r7 lsl 2]

00000274 e2877001    361 	add	r7,r7,1

00000278 e2566001    362 	subs	r6,r6,1

0000027c 1afffff9    363 	bne	.L422

                     364 .L380:

00000280 e59d0000    365 	ldr	r0,[sp]

00000284 e59f1018*   366 	ldr	r1,.L635

00000288 e59f2018*   367 	ldr	r2,.L636

                     368 ;92: 	}


                     369 ;93: 


                     370 ;94: 	entity->updateFromDataSlice = updateFromDataSlice;


                     371 

0000028c e5801068    372 	str	r1,[r0,104]

                     373 ;95: 	entity->calcReadLen = calcReadLen;


                     374 

00000290 e59f1014*   375 	ldr	r1,.L637

00000294 e5802060    376 	str	r2,[r0,96]

                     377 ;96: 	entity->encodeRead = encodeRead;


                     378 

00000298 e580105c    379 	str	r1,[r0,92]

                     380 ;97: 


                     381 ;98: 	IEDTree_addToCmpList(entity);


                     382 

0000029c eb000000*   383 	bl	IEDTree_addToCmpList

000002a0 e8bd8cf7    384 	ldmfd	[sp]!,{r0-r2,r4-r7,r10-fp,pc}

                     385 	.endf	IEDCodedEnum_init

                     386 	.align	4

                     387 ;extInfo	r1	local

                     388 ;accessInfo	[sp,8]	local

                     389 ;bitCount	r2	local

                     390 ;dsOffsets	r10	local

                     391 ;i	r7	local

                     392 

                     393 ;entity	[sp]	param

                     394 

                     395 	.section ".bss","awb"

                     396 .L610:

                     397 	.data

                     398 	.text

                     399 

                     400 ;99: }


                     401 	.align	4

                     402 .L635:

000002a4 00000000*   403 	.data.w	updateFromDataSlice

                     404 	.type	.L635,$object

                     405 	.size	.L635,4

                     406 

                     407 .L636:

000002a8 00000000*   408 	.data.w	calcReadLen

                     409 	.type	.L636,$object

                     410 	.size	.L636,4

                     411 

                     412 .L637:

000002ac 00000000*   413 	.data.w	encodeRead

                     414 	.type	.L637,$object

                     415 	.size	.L637,4

                     416 


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1qo1.s
                     417 	.align	4

                     418 

                     419 	.data

                     420 	.ghsnote version,6

                     421 	.ghsnote tools,3

                     422 	.ghsnote options,0

                     423 	.text

                     424 	.align	4

