                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5101.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_write.c -o gh_5101.o -list=mms_write.lst C:\Users\<USER>\AppData\Local\Temp\gh_5101.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_5101.s
Source File: mms_write.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile mms_write.c -o

                      11 ;		mms_write.o

                      12 ;Source File:   mms_write.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:04 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "mms_write.h"


                      23 ;2: #include "debug.h"


                      24 ;3: #include "AsnEncoding.h"


                      25 ;4: #include "mms.h"


                      26 ;5: #include "iedmodel.h"


                      27 ;6: #include "mms_rcb.h"


                      28 ;7: #include "mms_gocb.h"


                      29 ;8: #include "mms_data.h"


                      30 ;9: #include "pwin_access.h"


                      31 ;10: #include "control.h"


                      32 ;11: #include "iedTree/iedTree.h"


                      33 ;12: #include "IEDCompile/InnerAttributeTypes.h"


                      34 ;13: #include "IEDCompile/AccessInfo.h"


                      35 ;14: #include <types.h>


                      36 ;15: #include <stddef.h>


                      37 ;16: 


                      38 ;17: static size_t calcAccessErrEncodedSize(MmsDataAccessError* errList,


                      39 

                      40 ;34: }


                      41 

                      42 ;35: 


                      43 ;36: static size_t encodeAccessErrors(MmsDataAccessError* errList,


                      44 

                      45 ;55: }


                      46 

                      47 ;56: 


                      48 ;57: MmsDataAccessError writeFloatSett(void* descrStruct, uint8_t* dataToWrite)


                      49 ;58: {


                      50 ;59: 	FloatAccsessInfo* pSettingInfo = descrStruct;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5101.s
                      51 ;60: 	float value;


                      52 ;61: 	int intVal;


                      53 ;62: 


                      54 ;63: 	if(!pSettingInfo->flags & ACCESS_FLAG_WRITABLE)


                      55 ;64: 	{


                      56 ;65: 		return DATA_ACCESS_ERROR_OBJECT_ACCESS_DENIED;


                      57 ;66: 	}


                      58 ;67: 	


                      59 ;68: 	if (dataToWrite[0] != IEC61850_BER_FLOAT)


                      60 ;69: 	{


                      61 ;70: 		return DATA_ACCESS_ERROR_OBJECT_ACCESS_UNSUPPORTED;


                      62 ;71: 	}


                      63 ;72: 	


                      64 ;73: 	value = BerDecoder_decodeFloat(dataToWrite, 2);


                      65 ;74:     intVal = (int)(value / pSettingInfo->multiplier);


                      66 ;75:     if(!pwaWriteFloatSett(pSettingInfo->valueOffset, intVal))


                      67 ;76: 	{


                      68 ;77: 		return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;


                      69 ;78: 	}


                      70 ;79: 	return DATA_ACCESS_ERROR_SUCCESS;


                      71 ;80: }


                      72 ;81: 


                      73 ;82: MmsDataAccessError writeRealSett(void* descrStruct, uint8_t* dataToWrite)


                      74 ;83: {	


                      75 ;84:     FloatAccsessInfo* pSettingInfo = descrStruct;


                      76 ;85:     float value;


                      77 ;86: 


                      78 ;87: 	if(!pSettingInfo->flags & ACCESS_FLAG_WRITABLE)


                      79 ;88: 	{


                      80 ;89: 		return DATA_ACCESS_ERROR_OBJECT_ACCESS_DENIED;


                      81 ;90: 	}


                      82 ;91: 


                      83 ;92:     if (dataToWrite[0] != IEC61850_BER_FLOAT)


                      84 ;93:     {


                      85 ;94:         return DATA_ACCESS_ERROR_OBJECT_ACCESS_UNSUPPORTED;


                      86 ;95:     }


                      87 ;96:     value = BerDecoder_decodeFloat(dataToWrite, 2);


                      88 ;97:     value /= pSettingInfo->multiplier;


                      89 ;98: 	if(!pwaWriteRealSett(pSettingInfo->valueOffset, value))


                      90 ;99: 	{


                      91 ;100: 		return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;


                      92 ;101: 	}


                      93 ;102: 	return DATA_ACCESS_ERROR_SUCCESS;    


                      94 ;103: }


                      95 ;104: 


                      96 ;105: MmsDataAccessError writeIntSett(void* descrStruct, uint8_t* dataToWrite)


                      97 ;106: {	


                      98 ;107: 	IntBoolAccessInfo* pAccessInfo = descrStruct;


                      99 ;108: 	uint32_t value;


                     100 ;109: 	int len;


                     101 ;110: 


                     102 ;111: 	if(!pAccessInfo->flags & ACCESS_FLAG_WRITABLE)


                     103 ;112: 	{


                     104 ;113: 		return DATA_ACCESS_ERROR_OBJECT_ACCESS_DENIED;


                     105 ;114: 	}


                     106 ;115: 


                     107 ;116: 	if (dataToWrite[0] != IEC61850_BER_INTEGER


                     108 ;117: 		&& dataToWrite[0] != IEC61850_BER_UNSIGNED_INTEGER)


                     109 ;118: 	{


                     110 ;119: 		return DATA_ACCESS_ERROR_OBJECT_ACCESS_UNSUPPORTED;


                     111 ;120: 	}



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5101.s
                     112 ;121: 	len = dataToWrite[1];


                     113 ;122: 	if (len > 8)


                     114 ;123: 	{


                     115 ;124: 		return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                     116 ;125: 	}


                     117 ;126: 	value = BerDecoder_decodeUint32(dataToWrite, len, 2);


                     118 ;127: 


                     119 ;128: 


                     120 ;129: 	if (pAccessInfo->enumTableSize != 0)


                     121 ;130: 	{


                     122 ;131: 		value = getEnumDataValue(value, pAccessInfo->enumTable,


                     123 ;132: 			pAccessInfo->enumTableSize);


                     124 ;133: 	}


                     125 ;134: 


                     126 ;135:     if(!pwaWriteIntSett(pAccessInfo->valueOffset,value))


                     127 ;136:     {


                     128 ;137:         return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;


                     129 ;138:     }


                     130 ;139:     return DATA_ACCESS_ERROR_SUCCESS;


                     131 ;140: }


                     132 ;141: 


                     133 ;142: void writeBoolean(void* descrStruct, uint8_t* dataToWrite)


                     134 ;143: {	


                     135 ;144: 	IntBoolAccessInfo* pAccessInfo = descrStruct;


                     136 ;145: 	int value;


                     137 ;146: 


                     138 ;147: 	if(!pAccessInfo->flags & ACCESS_FLAG_WRITABLE)


                     139 ;148: 	{


                     140 ;149: 		return;


                     141 ;150: 	}


                     142 ;151: 


                     143 ;152: 


                     144 ;153: 	if (dataToWrite[0] != IEC61850_BER_BOOLEAN || dataToWrite[1] != 1)


                     145 ;154: 	{


                     146 ;155: 		return;


                     147 ;156: 	}


                     148 ;157: 	value = dataToWrite[2];	


                     149 ;158:     if(value)


                     150 ;159:     {


                     151 ;160:         writeTele(pAccessInfo->valueOffset);


                     152 ;161:     }


                     153 ;162: }


                     154 ;163: 


                     155 ;164: void writeCodedEnum(void* descrStruct, uint8_t* dataToWrite)


                     156 ;165: {	


                     157 ;166:     CodedEnumAccessInfo* pAccessInfo = descrStruct;


                     158 ;167:     int valIdx;


                     159 ;168:     int value;


                     160 ;169: 


                     161 ;170: 	if(!pAccessInfo->flags & ACCESS_FLAG_WRITABLE)


                     162 ;171: 	{


                     163 ;172: 		return;


                     164 ;173: 	}


                     165 ;174: 


                     166 ;175:     if (pAccessInfo->bitCount > 8)


                     167 ;176:     {


                     168 ;177:         ERROR_REPORT("More than 8 bits is not supported");


                     169 ;178:         return;


                     170 ;179:     }


                     171 ;180: 


                     172 ;181:     value = BerDecoder_DecodeBitStringTLToInt(dataToWrite, 0);



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5101.s
                     173 ;182: 	if (value == -1)


                     174 ;183: 	{


                     175 ;184: 		ERROR_REPORT("Error decoding Coded Enum");


                     176 ;185: 		return;


                     177 ;186: 	}


                     178 ;187: 


                     179 ;188:     


                     180 ;189:     for (valIdx = pAccessInfo->bitCount-1;  valIdx >= 0  ; --valIdx)


                     181 ;190:     {


                     182 ;191:         int offset = pAccessInfo->valueOffsets[valIdx];


                     183 ;192:         


                     184 ;193:         if (offset != -1 && (value & 1))


                     185 ;194:         {


                     186 ;195: 			writeTele(offset);


                     187 ;196:         }


                     188 ;197: 		value >>= 1;


                     189 ;198:     }    


                     190 ;199: }


                     191 ;200: 


                     192 ;201: 


                     193 ;202: static int handleWriteRequest(unsigned int invokeId, uint8_t* outBuf,


                     194 

                     195 ;227: }


                     196 

                     197 	.text

                     198 	.align	4

                     199 writeFloatSett::

00000000 e92d4010    200 	stmfd	[sp]!,{r4,lr}

00000004 e1a04000    201 	mov	r4,r0

00000008 e5940000    202 	ldr	r0,[r4]

0000000c e3500000    203 	cmp	r0,0

00000010 03a00001    204 	moveq	r0,1

00000014 13a00000    205 	movne	r0,0

00000018 e3100001    206 	tst	r0,1

0000001c 13a00003    207 	movne	r0,3

00000020 1a00000f    208 	bne	.L113

00000024 e5d10000    209 	ldrb	r0,[r1]

00000028 e3500087    210 	cmp	r0,135

0000002c 13a00009    211 	movne	r0,9

00000030 1a00000b    212 	bne	.L113

00000034 e1a00001    213 	mov	r0,r1

00000038 e3a01002    214 	mov	r1,2

0000003c eb000000*   215 	bl	BerDecoder_decodeFloat

00000040 e5941008    216 	ldr	r1,[r4,8]

00000044 eb000000*   217 	bl	__fdiv

00000048 eb000000*   218 	bl	__ftoi

0000004c e1a01000    219 	mov	r1,r0

00000050 e1d400b4    220 	ldrh	r0,[r4,4]

00000054 eb000000*   221 	bl	pwaWriteFloatSett

00000058 e3500000    222 	cmp	r0,0

0000005c 13e00000    223 	mvnne	r0,0

00000060 03a00007    224 	moveq	r0,7

                     225 .L113:

00000064 e8bd8010    226 	ldmfd	[sp]!,{r4,pc}

                     227 	.endf	writeFloatSett

                     228 	.align	4

                     229 ;pSettingInfo	r4	local

                     230 

                     231 ;descrStruct	r0	param

                     232 ;dataToWrite	r1	param

                     233 


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5101.s
                     234 	.section ".bss","awb"

                     235 .L166:

                     236 	.data

                     237 	.text

                     238 

                     239 

                     240 	.align	4

                     241 	.align	4

                     242 writeRealSett::

00000068 e92d4010    243 	stmfd	[sp]!,{r4,lr}

0000006c e1a04000    244 	mov	r4,r0

00000070 e5940000    245 	ldr	r0,[r4]

00000074 e3500000    246 	cmp	r0,0

00000078 03a00001    247 	moveq	r0,1

0000007c 13a00000    248 	movne	r0,0

00000080 e3100001    249 	tst	r0,1

00000084 13a00003    250 	movne	r0,3

00000088 1a00000e    251 	bne	.L189

0000008c e5d10000    252 	ldrb	r0,[r1]

00000090 e3500087    253 	cmp	r0,135

00000094 13a00009    254 	movne	r0,9

00000098 1a00000a    255 	bne	.L189

0000009c e1a00001    256 	mov	r0,r1

000000a0 e3a01002    257 	mov	r1,2

000000a4 eb000000*   258 	bl	BerDecoder_decodeFloat

000000a8 e5941008    259 	ldr	r1,[r4,8]

000000ac eb000000*   260 	bl	__fdiv

000000b0 e1a01000    261 	mov	r1,r0

000000b4 e1d400b4    262 	ldrh	r0,[r4,4]

000000b8 eb000000*   263 	bl	pwaWriteRealSett

000000bc e3500000    264 	cmp	r0,0

000000c0 13e00000    265 	mvnne	r0,0

000000c4 03a00007    266 	moveq	r0,7

                     267 .L189:

000000c8 e8bd8010    268 	ldmfd	[sp]!,{r4,pc}

                     269 	.endf	writeRealSett

                     270 	.align	4

                     271 ;pSettingInfo	r4	local

                     272 

                     273 ;descrStruct	r0	param

                     274 ;dataToWrite	r1	param

                     275 

                     276 	.section ".bss","awb"

                     277 .L246:

                     278 	.data

                     279 	.text

                     280 

                     281 

                     282 	.align	4

                     283 	.align	4

                     284 writeIntSett::

000000cc e92d4010    285 	stmfd	[sp]!,{r4,lr}

000000d0 e1a04000    286 	mov	r4,r0

000000d4 e5940000    287 	ldr	r0,[r4]

000000d8 e3500000    288 	cmp	r0,0

000000dc 03a00001    289 	moveq	r0,1

000000e0 13a00000    290 	movne	r0,0

000000e4 e3100001    291 	tst	r0,1

000000e8 13a00003    292 	movne	r0,3

000000ec 1a000018    293 	bne	.L269

000000f0 e1a02001    294 	mov	r2,r1


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5101.s
000000f4 e5d20000    295 	ldrb	r0,[r2]

000000f8 e3500085    296 	cmp	r0,133

000000fc 13500086    297 	cmpne	r0,134

00000100 13a00009    298 	movne	r0,9

00000104 1a000012    299 	bne	.L269

00000108 e5d21001    300 	ldrb	r1,[r2,1]

0000010c e3510008    301 	cmp	r1,8

00000110 c3a0000b    302 	movgt	r0,11

00000114 ca00000e    303 	bgt	.L269

00000118 e1a00002    304 	mov	r0,r2

0000011c e3a02002    305 	mov	r2,2

00000120 eb000000*   306 	bl	BerDecoder_decodeUint32

00000124 e5942008    307 	ldr	r2,[r4,8]

00000128 e1a01000    308 	mov	r1,r0

0000012c e3520000    309 	cmp	r2,0

00000130 0a000002    310 	beq	.L281

00000134 e284100c    311 	add	r1,r4,12

00000138 eb000000*   312 	bl	getEnumDataValue

0000013c e1a01000    313 	mov	r1,r0

                     314 .L281:

00000140 e1d400b4    315 	ldrh	r0,[r4,4]

00000144 eb000000*   316 	bl	pwaWriteIntSett

00000148 e3500000    317 	cmp	r0,0

0000014c 13e00000    318 	mvnne	r0,0

00000150 03a00007    319 	moveq	r0,7

                     320 .L269:

00000154 e8bd8010    321 	ldmfd	[sp]!,{r4,pc}

                     322 	.endf	writeIntSett

                     323 	.align	4

                     324 ;pAccessInfo	r4	local

                     325 ;value	r1	local

                     326 ;len	r1	local

                     327 

                     328 ;descrStruct	r0	param

                     329 ;dataToWrite	r2	param

                     330 

                     331 	.section ".bss","awb"

                     332 .L366:

                     333 	.data

                     334 	.text

                     335 

                     336 

                     337 	.align	4

                     338 	.align	4

                     339 writeBoolean::

00000158 e5902000    340 	ldr	r2,[r0]

0000015c e3520000    341 	cmp	r2,0

00000160 03a02001    342 	moveq	r2,1

00000164 13a02000    343 	movne	r2,0

00000168 e3120001    344 	tst	r2,1

0000016c 05d12000    345 	ldreqb	r2,[r1]

00000170 03520083    346 	cmpeq	r2,131

00000174 05d12001    347 	ldreqb	r2,[r1,1]

00000178 03520001    348 	cmpeq	r2,1

0000017c 1a000003    349 	bne	.L401

00000180 e5d11002    350 	ldrb	r1,[r1,2]

00000184 e3510000    351 	cmp	r1,0

00000188 15900004    352 	ldrne	r0,[r0,4]

0000018c 1a000000*   353 	bne	writeTele

                     354 .L401:

00000190 e12fff1e*   355 	ret	


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5101.s
                     356 	.endf	writeBoolean

                     357 	.align	4

                     358 ;pAccessInfo	r0	local

                     359 ;value	r1	local

                     360 

                     361 ;descrStruct	r0	param

                     362 ;dataToWrite	r1	param

                     363 

                     364 	.section ".bss","awb"

                     365 .L461:

                     366 	.data

                     367 	.text

                     368 

                     369 

                     370 	.align	4

                     371 	.align	4

                     372 writeCodedEnum::

00000194 e92d4df0    373 	stmfd	[sp]!,{r4-r8,r10-fp,lr}

00000198 e24dd004    374 	sub	sp,sp,4

0000019c e1a04000    375 	mov	r4,r0

000001a0 e5940000    376 	ldr	r0,[r4]

000001a4 e3500000    377 	cmp	r0,0

000001a8 03a00001    378 	moveq	r0,1

000001ac 13a00000    379 	movne	r0,0

000001b0 e3100001    380 	tst	r0,1

000001b4 1a00004a    381 	bne	.L478

000001b8 e5940004    382 	ldr	r0,[r4,4]

000001bc e3500008    383 	cmp	r0,8

000001c0 ca000047    384 	bgt	.L478

000001c4 e1a00001    385 	mov	r0,r1

000001c8 e3a01000    386 	mov	r1,0

000001cc eb000000*   387 	bl	BerDecoder_DecodeBitStringTLToInt

000001d0 e1a05000    388 	mov	r5,r0

000001d4 e3750001    389 	cmn	r5,1

000001d8 0a000041    390 	beq	.L478

000001dc e5940004    391 	ldr	r0,[r4,4]

000001e0 e3500000    392 	cmp	r0,0

000001e4 e240b001    393 	sub	fp,r0,1

000001e8 a1a08000    394 	movge	r8,r0

000001ec b3a08000    395 	movlt	r8,0

000001f0 e1b0a1a8    396 	movs	r10,r8 lsr 3

000001f4 0a00002e    397 	beq	.L520

000001f8 e2841008    398 	add	r1,r4,8

000001fc e2400008    399 	sub	r0,r0,8

00000200 e0816100    400 	add	r6,r1,r0 lsl 2

00000204 e3e07000    401 	mvn	r7,0

00000208 e04bb18a    402 	sub	fp,fp,r10 lsl 3

                     403 .L521:

0000020c e596001c    404 	ldr	r0,[r6,28]

00000210 e1500007    405 	cmp	r0,r7

00000214 13150001    406 	tstne	r5,1

00000218 1b000000*   407 	blne	writeTele

0000021c e5960018    408 	ldr	r0,[r6,24]

00000220 e1a050c5    409 	mov	r5,r5 asr 1

00000224 e1500007    410 	cmp	r0,r7

00000228 13150001    411 	tstne	r5,1

0000022c 1b000000*   412 	blne	writeTele

00000230 e5960014    413 	ldr	r0,[r6,20]

00000234 e1a050c5    414 	mov	r5,r5 asr 1

00000238 e1500007    415 	cmp	r0,r7

0000023c 13150001    416 	tstne	r5,1


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5101.s
00000240 1b000000*   417 	blne	writeTele

00000244 e5960010    418 	ldr	r0,[r6,16]

00000248 e1a050c5    419 	mov	r5,r5 asr 1

0000024c e1500007    420 	cmp	r0,r7

00000250 13150001    421 	tstne	r5,1

00000254 1b000000*   422 	blne	writeTele

00000258 e596000c    423 	ldr	r0,[r6,12]

0000025c e1a050c5    424 	mov	r5,r5 asr 1

00000260 e1500007    425 	cmp	r0,r7

00000264 13150001    426 	tstne	r5,1

00000268 1b000000*   427 	blne	writeTele

0000026c e5960008    428 	ldr	r0,[r6,8]

00000270 e1a050c5    429 	mov	r5,r5 asr 1

00000274 e1500007    430 	cmp	r0,r7

00000278 13150001    431 	tstne	r5,1

0000027c 1b000000*   432 	blne	writeTele

00000280 e5960004    433 	ldr	r0,[r6,4]

00000284 e1a050c5    434 	mov	r5,r5 asr 1

00000288 e1500007    435 	cmp	r0,r7

0000028c 13150001    436 	tstne	r5,1

00000290 1b000000*   437 	blne	writeTele

00000294 e4160020    438 	ldr	r0,[r6],-32

00000298 e1a050c5    439 	mov	r5,r5 asr 1

0000029c e1500007    440 	cmp	r0,r7

000002a0 13150001    441 	tstne	r5,1

000002a4 e1a050c5    442 	mov	r5,r5 asr 1

000002a8 1b000000*   443 	blne	writeTele

000002ac e25aa001    444 	subs	r10,r10,1

000002b0 1affffd5    445 	bne	.L521

                     446 .L520:

000002b4 e218a007    447 	ands	r10,r8,7

000002b8 0a000009    448 	beq	.L478

000002bc e2840008    449 	add	r0,r4,8

000002c0 e080410b    450 	add	r4,r0,fp lsl 2

000002c4 e3e06000    451 	mvn	r6,0

                     452 .L563:

000002c8 e4140004    453 	ldr	r0,[r4],-4

000002cc e1500006    454 	cmp	r0,r6

000002d0 13150001    455 	tstne	r5,1

000002d4 e1a050c5    456 	mov	r5,r5 asr 1

000002d8 1b000000*   457 	blne	writeTele

000002dc e25aa001    458 	subs	r10,r10,1

000002e0 1afffff8    459 	bne	.L563

                     460 .L478:

000002e4 e28dd004    461 	add	sp,sp,4

000002e8 e8bd8df0    462 	ldmfd	[sp]!,{r4-r8,r10-fp,pc}

                     463 	.endf	writeCodedEnum

                     464 	.align	4

                     465 ;pAccessInfo	r4	local

                     466 ;valIdx	fp	local

                     467 ;value	r5	local

                     468 ;offset	r0	local

                     469 

                     470 ;descrStruct	r0	param

                     471 ;dataToWrite	r1	param

                     472 

                     473 	.section ".bss","awb"

                     474 .L991:

                     475 	.data

                     476 	.text

                     477 


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5101.s
                     478 

                     479 ;228: 


                     480 ;229: int mms_handleWriteRequest(IsoConnection* isoConn,


                     481 	.align	4

                     482 	.align	4

                     483 mms_handleWriteRequest::

000002ec e92d4ff0    484 	stmfd	[sp]!,{r4-fp,lr}

                     485 ;230:         unsigned char* inBuf, int bufPos, int maxBufPos, unsigned int invokeId,


                     486 ;231:         unsigned char* response)


                     487 ;232: {


                     488 

000002f0 e24dd03c    489 	sub	sp,sp,60

000002f4 e58d001c    490 	str	r0,[sp,28]

000002f8 e1a07003    491 	mov	r7,r3

000002fc e3a06080    492 	mov	r6,128

00000300 e3a0b000    493 	mov	fp,0

                     494 ;233:     StringView domainId;


                     495 ;234:     StringView itemId;


                     496 ;235:     uint8_t tag;


                     497 ;236:     int length;


                     498 ;237:     size_t valListLen;


                     499 ;238: 	int specLength;


                     500 ;239: 	int specEnd;


                     501 ;240: 	int specBufPos;	


                     502 ;241:     int valCount = 0;


                     503 

                     504 ;242:     BufferView valBuf;


                     505 ;243:     MmsConnection* mmsConn = &isoConn->mmsConn;


                     506 

00000304 e1a05001    507 	mov	r5,r1

00000308 e3a01f8f    508 	mov	r1,0x023c

0000030c e2611a49    509 	rsb	r1,r1,73<<12

00000310 e080a001    510 	add	r10,r0,r1

                     511 ;244: 


                     512 ;245: 	//==========Variable specification=========


                     513 ;246:     tag = inBuf[bufPos++]; //0xA0


                     514 

00000314 e7d51002    515 	ldrb	r1,[r5,r2]

00000318 e2824001    516 	add	r4,r2,1

0000031c e5cd1007    517 	strb	r1,[sp,7]

                     518 ;247:     if(tag != 0xA0)


                     519 

00000320 e35100a0    520 	cmp	r1,160

00000324 1a00001f    521 	bne	.L1085

                     522 ;248:     {


                     523 

                     524 ;249:         ERROR_REPORT("Unexpected tag %02X  instead of var spec", tag);


                     525 ;250:         return 0;


                     526 

                     527 ;251:     }


                     528 ;252:     bufPos = BerDecoder_decodeLength(inBuf, &specLength, bufPos, maxBufPos);


                     529 

00000328 e1a02004    530 	mov	r2,r4

0000032c e28d1010    531 	add	r1,sp,16

00000330 e1a00005    532 	mov	r0,r5

00000334 eb000000*   533 	bl	BerDecoder_decodeLength

00000338 e2504000    534 	subs	r4,r0,0

                     535 ;253:     if(bufPos < 1)


                     536 

0000033c da000019    537 	ble	.L1085

                     538 ;254:     {



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5101.s
                     539 

                     540 ;255:         ERROR_REPORT("Invalid length");


                     541 ;256:         return 0;


                     542 

                     543 ;257:     }


                     544 ;258: 	specEnd = bufPos + specLength;    


                     545 

00000340 e59d0010    546 	ldr	r0,[sp,16]

00000344 e1a02007    547 	mov	r2,r7

00000348 e0803004    548 	add	r3,r0,r4

0000034c e1a09003    549 	mov	r9,r3

                     550 ;259: 	specBufPos = bufPos;


                     551 

                     552 ;260: 


                     553 ;261:     BufferView_init(&valBuf, inBuf, maxBufPos, specEnd);


                     554 

00000350 e1a01005    555 	mov	r1,r5

00000354 e28d0020    556 	add	r0,sp,32

00000358 eb000000*   557 	bl	BufferView_init

                     558 ;262: 


                     559 ;263: 	//==========Value list=================


                     560 ;264: 


                     561 ;265:     if(!BufferView_decodeTL(&valBuf, &tag, &valListLen, NULL ))


                     562 

0000035c e28d200c    563 	add	r2,sp,12

00000360 e28d1007    564 	add	r1,sp,7

00000364 e28d0020    565 	add	r0,sp,32

00000368 e3a03000    566 	mov	r3,0

0000036c eb000000*   567 	bl	BufferView_decodeTL

00000370 e3500000    568 	cmp	r0,0

00000374 0a00000b    569 	beq	.L1085

                     570 ;266:     {


                     571 

                     572 ;267:         ERROR_REPORT("Invalid write data");


                     573 ;268:         return 0;


                     574 

                     575 ;269:     }


                     576 ;270: 


                     577 ;271: 	if (tag != 0xA0)


                     578 

00000378 e5dd1007    579 	ldrb	r1,[sp,7]

0000037c e35100a0    580 	cmp	r1,160

00000380 1a000008    581 	bne	.L1085

                     582 ;272: 	{


                     583 

                     584 ;273: 		ERROR_REPORT("Unexpected tag %d", tag);


                     585 ;274: 		return 0;


                     586 

                     587 ;275: 	}


                     588 ;276: 


                     589 ;277:     if (valListLen < 1)


                     590 

00000384 e59d100c    591 	ldr	r1,[sp,12]

00000388 e3510000    592 	cmp	r1,0

0000038c 0a000005    593 	beq	.L1085

00000390 e28a1c61    594 	add	r1,r10,97<<8

00000394 e281a0d8    595 	add	r10,r1,216

00000398 e1a0800a    596 	mov	r8,r10

0000039c e1540009    597 	cmp	r4,r9

000003a0 aa00002b    598 	bge	.L1088

000003a4 ea000001    599 	b	.L1089


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5101.s
                     600 .L1085:

                     601 ;278: 	{


                     602 

                     603 ;279:         ERROR_REPORT("Invalid length %d", valListLen);


                     604 ;280: 		return 0;


                     605 

000003a8 e3a00000    606 	mov	r0,0

000003ac ea0000eb    607 	b	.L1070

                     608 .L1089:

                     609 ;281: 	}


                     610 ;282: 


                     611 ;283: 	//specBufPos движется по именам объектов	


                     612 ;284: 	while(specBufPos < specEnd)


                     613 

                     614 ;285: 	{


                     615 

                     616 ;286: 		tag = inBuf[specBufPos++]; //0x30 Sequence


                     617 

000003b0 e7d51004    618 	ldrb	r1,[r5,r4]

000003b4 e2844001    619 	add	r4,r4,1

000003b8 e5cd1007    620 	strb	r1,[sp,7]

                     621 ;287: 		if (tag != 0x30)


                     622 

000003bc e3510030    623 	cmp	r1,48

000003c0 1afffff8    624 	bne	.L1085

                     625 ;288: 		{


                     626 

                     627 ;289: 			ERROR_REPORT("Unexpected tag %02X instead of sequence", tag);


                     628 ;290: 			return 0;


                     629 

                     630 ;291: 		}


                     631 ;292: 		specBufPos = BerDecoder_decodeLength(inBuf, &length, specBufPos, maxBufPos);


                     632 

000003c4 e1a03007    633 	mov	r3,r7

000003c8 e1a02004    634 	mov	r2,r4

000003cc e28d1008    635 	add	r1,sp,8

000003d0 e1a00005    636 	mov	r0,r5

000003d4 eb000000*   637 	bl	BerDecoder_decodeLength

000003d8 e2504000    638 	subs	r4,r0,0

                     639 ;293: 		if (specBufPos < 1)


                     640 

000003dc dafffff1    641 	ble	.L1085

                     642 ;294: 		{


                     643 

                     644 ;295: 			ERROR_REPORT("Invalid length %d", length);


                     645 ;296: 			return 0;


                     646 

                     647 ;297: 		}


                     648 ;298: 


                     649 ;299: 		tag = inBuf[specBufPos++]; //0xA0 Object name


                     650 

000003e0 e7d51004    651 	ldrb	r1,[r5,r4]

000003e4 e2844001    652 	add	r4,r4,1

000003e8 e5cd1007    653 	strb	r1,[sp,7]

                     654 ;300: 		if (tag != 0xA0)


                     655 

000003ec e35100a0    656 	cmp	r1,160

000003f0 1affffec    657 	bne	.L1085

                     658 ;301: 		{


                     659 

                     660 ;302: 			ERROR_REPORT("Unexpected tag %02X  instead of object name", tag);



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5101.s
                     661 ;303: 			return 0;


                     662 

                     663 ;304: 		}


                     664 ;305: 		specBufPos = BerDecoder_decodeLength(inBuf, &length, specBufPos, maxBufPos);


                     665 

000003f4 e1a03007    666 	mov	r3,r7

000003f8 e1a02004    667 	mov	r2,r4

000003fc e28d1008    668 	add	r1,sp,8

00000400 e1a00005    669 	mov	r0,r5

00000404 eb000000*   670 	bl	BerDecoder_decodeLength

00000408 e2504000    671 	subs	r4,r0,0

                     672 ;306: 		if (specBufPos < 1)


                     673 

0000040c daffffe5    674 	ble	.L1085

                     675 ;307: 		{


                     676 

                     677 ;308: 			ERROR_REPORT("Invalid length %d", length);


                     678 ;309: 			return 0;


                     679 

                     680 ;310: 		}


                     681 ;311: 


                     682 ;312: 		//Decode object name


                     683 ;313: 		specBufPos = BerDecoder_DecodeObjectNameToStringView(inBuf, specBufPos,


                     684 

00000410 e28d3034    685 	add	r3,sp,52

00000414 e1a02007    686 	mov	r2,r7

00000418 e1a01004    687 	mov	r1,r4

0000041c e28d002c    688 	add	r0,sp,44

00000420 e58d0000    689 	str	r0,[sp]

00000424 e1a00005    690 	mov	r0,r5

00000428 eb000000*   691 	bl	BerDecoder_DecodeObjectNameToStringView

0000042c e28d3020    692 	add	r3,sp,32

00000430 e59d201c    693 	ldr	r2,[sp,28]

00000434 e28d102c    694 	add	r1,sp,44

00000438 e1a04000    695 	mov	r4,r0

                     696 ;314: 			maxBufPos, &domainId, &itemId);


                     697 ;315: 


                     698 ;316:         //Write object


                     699 ;317:         mmsConn->wrResults[valCount] =                


                     700 

0000043c e28d0034    701 	add	r0,sp,52

00000440 eb000000*   702 	bl	IEDTree_write

00000444 e48a0004    703 	str	r0,[r10],4

                     704 ;318:                 IEDTree_write(&domainId, &itemId, isoConn, &valBuf );


                     705 ;319: 		valCount++;


                     706 

00000448 e28bb001    707 	add	fp,fp,1

0000044c e1540009    708 	cmp	r4,r9

00000450 baffffd6    709 	blt	.L1089

                     710 .L1088:

                     711 ;320: 	}    


                     712 ;321: 	


                     713 ;322:     return handleWriteRequest(invokeId, response, mmsConn->wrResults, valCount);


                     714 

00000454 e59d7060    715 	ldr	r7,[sp,96]

                     716 ;203:                        MmsDataAccessError* errList, size_t valCount)


                     717 ;204: {


                     718 

                     719 ;205: 	int bufPos = 0;


                     720 

                     721 ;206: 	//==============determine BER encoded message sizes==============



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5101.s
                     722 ;207:     int responseContentSize = calcAccessErrEncodedSize(errList, valCount);


                     723 

                     724 ;18:                                 size_t itemCount)


                     725 ;19: {


                     726 

00000458 e3a04000    727 	mov	r4,0

                     728 ;20:     size_t i;


                     729 ;21:     size_t size = 0;


                     730 

                     731 ;22:     for (i = 0; i < itemCount; i++)


                     732 

0000045c e1a03004    733 	mov	r3,r4

00000460 e35b0000    734 	cmp	fp,0

00000464 a1a0c00b    735 	movge	r12,fp

00000468 b3a0c000    736 	movlt	r12,0

0000046c e1b021ac    737 	movs	r2,r12 lsr 3

00000470 0a000024    738 	beq	.L1258

00000474 e1a01008    739 	mov	r1,r8

00000478 e1a03182    740 	mov	r3,r2 lsl 3

                     741 .L1259:

0000047c e5910000    742 	ldr	r0,[r1]

00000480 e3500000    743 	cmp	r0,0

00000484 b2844002    744 	addlt	r4,r4,2

00000488 e5910004    745 	ldr	r0,[r1,4]

0000048c a2844003    746 	addge	r4,r4,3

00000490 e3500000    747 	cmp	r0,0

00000494 b2844002    748 	addlt	r4,r4,2

00000498 e5910008    749 	ldr	r0,[r1,8]

0000049c a2844003    750 	addge	r4,r4,3

000004a0 e3500000    751 	cmp	r0,0

000004a4 b2844002    752 	addlt	r4,r4,2

000004a8 e591000c    753 	ldr	r0,[r1,12]

000004ac a2844003    754 	addge	r4,r4,3

000004b0 e3500000    755 	cmp	r0,0

000004b4 b2844002    756 	addlt	r4,r4,2

000004b8 e5910010    757 	ldr	r0,[r1,16]

000004bc a2844003    758 	addge	r4,r4,3

000004c0 e3500000    759 	cmp	r0,0

000004c4 b2844002    760 	addlt	r4,r4,2

000004c8 e5910014    761 	ldr	r0,[r1,20]

000004cc a2844003    762 	addge	r4,r4,3

000004d0 e3500000    763 	cmp	r0,0

000004d4 b2844002    764 	addlt	r4,r4,2

000004d8 e5910018    765 	ldr	r0,[r1,24]

000004dc a2844003    766 	addge	r4,r4,3

000004e0 e3500000    767 	cmp	r0,0

000004e4 e591001c    768 	ldr	r0,[r1,28]

000004e8 e2811020    769 	add	r1,r1,32

000004ec b2844002    770 	addlt	r4,r4,2

000004f0 a2844003    771 	addge	r4,r4,3

000004f4 e3500000    772 	cmp	r0,0

000004f8 b2844002    773 	addlt	r4,r4,2

000004fc a2844003    774 	addge	r4,r4,3

00000500 e2522001    775 	subs	r2,r2,1

00000504 1affffdc    776 	bne	.L1259

                     777 .L1258:

00000508 e21c2007    778 	ands	r2,r12,7

0000050c 0a000006    779 	beq	.L1110

00000510 e0881103    780 	add	r1,r8,r3 lsl 2

                     781 .L1301:

00000514 e4913004    782 	ldr	r3,[r1],4


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5101.s
00000518 e3530000    783 	cmp	r3,0

0000051c b2844002    784 	addlt	r4,r4,2

00000520 a2844003    785 	addge	r4,r4,3

00000524 e2522001    786 	subs	r2,r2,1

00000528 1afffff9    787 	bne	.L1301

                     788 .L1110:

                     789 ;31:         }


                     790 ;32:     }


                     791 ;33:     return size;


                     792 

                     793 ;208: 	int responseContentSizeTL = responseContentSize + 2;


                     794 

0000052c e284a002    795 	add	r10,r4,2

                     796 ;209: 	int invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;


                     797 

00000530 e1a00007    798 	mov	r0,r7

00000534 eb000000*   799 	bl	BerEncoder_UInt32determineEncodedSize

00000538 e2805002    800 	add	r5,r0,2

                     801 ;210: 	int responseSize = responseContentSizeTL + invokeIdSize;


                     802 

0000053c e085100a    803 	add	r1,r5,r10

                     804 ;211: 


                     805 ;212: 	//================ encode message ============================


                     806 ;213: 	bufPos = 0;


                     807 

                     808 ;214: 


                     809 ;215: 	// confirmed response PDU


                     810 ;216: 	bufPos = BerEncoder_encodeTL(0xa1, responseSize, outBuf, bufPos);


                     811 

00000540 e59d2064    812 	ldr	r2,[sp,100]

00000544 e3a03000    813 	mov	r3,0

00000548 e3a000a1    814 	mov	r0,161

0000054c eb000000*   815 	bl	BerEncoder_encodeTL

                     816 ;217: 	// invoke id


                     817 ;218: 	bufPos = BerEncoder_encodeTL(ASN_INTEGER, invokeIdSize - 2, outBuf, bufPos);


                     818 

00000550 e59d2064    819 	ldr	r2,[sp,100]

00000554 e2451002    820 	sub	r1,r5,2

00000558 e1a03000    821 	mov	r3,r0

0000055c e3a00002    822 	mov	r0,2

00000560 eb000000*   823 	bl	BerEncoder_encodeTL

                     824 ;219: 	bufPos = BerEncoder_encodeUInt32(invokeId, outBuf, bufPos);


                     825 

00000564 e1a02000    826 	mov	r2,r0

00000568 e59d1064    827 	ldr	r1,[sp,100]

0000056c e1a00007    828 	mov	r0,r7

00000570 eb000000*   829 	bl	BerEncoder_encodeUInt32

                     830 ;220: 


                     831 ;221: 	// confirmed-service-response write


                     832 ;222: 	bufPos = BerEncoder_encodeTL(0xa5, responseContentSize, outBuf, bufPos);


                     833 

00000574 e59d2064    834 	ldr	r2,[sp,100]

00000578 e1a01004    835 	mov	r1,r4

0000057c e1a03000    836 	mov	r3,r0

00000580 e3a000a5    837 	mov	r0,165

00000584 eb000000*   838 	bl	BerEncoder_encodeTL

                     839 ;223: 


                     840 ;224:     // encode access results


                     841 ;225:     bufPos = encodeAccessErrors(errList, valCount, outBuf, bufPos);


                     842 

00000588 e1a01008    843 	mov	r1,r8


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5101.s
0000058c e59d2064    844 	ldr	r2,[sp,100]

                     845 ;37:                    size_t itemCount, uint8_t* buf, size_t bufPos)


                     846 ;38: {


                     847 

                     848 ;39:     size_t i;


                     849 ;40:     for (i = 0; i < itemCount; i++)


                     850 

00000590 e3a0c000    851 	mov	r12,0

00000594 e35b0000    852 	cmp	fp,0

00000598 b3a0b000    853 	movlt	fp,0

0000059c e1b0512b    854 	movs	r5,fp lsr 2

000005a0 0a000053    855 	beq	.L1308

000005a4 e2814004    856 	add	r4,r1,4

000005a8 e3a07081    857 	mov	r7,129

000005ac e3a0a001    858 	mov	r10,1

                     859 .L1309:

000005b0 e791310c    860 	ldr	r3,[r1,r12 lsl 2]

000005b4 e3530000    861 	cmp	r3,0

000005b8 aa000008    862 	bge	.L1311

000005bc e3a03000    863 	mov	r3,0

000005c0 e7c27000    864 	strb	r7,[r2,r0]

000005c4 e2800001    865 	add	r0,r0,1

000005c8 e7c23000    866 	strb	r3,[r2,r0]

000005cc e5943000    867 	ldr	r3,[r4]

000005d0 e2800001    868 	add	r0,r0,1

000005d4 e3530000    869 	cmp	r3,0

000005d8 aa000013    870 	bge	.L1316

000005dc ea000009    871 	b	.L1315

                     872 .L1311:

000005e0 e7c26000    873 	strb	r6,[r2,r0]

000005e4 e2800001    874 	add	r0,r0,1

000005e8 e7c2a000    875 	strb	r10,[r2,r0]

000005ec e791310c    876 	ldr	r3,[r1,r12 lsl 2]

000005f0 e2800001    877 	add	r0,r0,1

000005f4 e7c23000    878 	strb	r3,[r2,r0]

000005f8 e5943000    879 	ldr	r3,[r4]

000005fc e2800001    880 	add	r0,r0,1

00000600 e3530000    881 	cmp	r3,0

00000604 aa000008    882 	bge	.L1316

                     883 .L1315:

00000608 e3a03000    884 	mov	r3,0

0000060c e7c27000    885 	strb	r7,[r2,r0]

00000610 e2800001    886 	add	r0,r0,1

00000614 e7c23000    887 	strb	r3,[r2,r0]

00000618 e5943004    888 	ldr	r3,[r4,4]

0000061c e2800001    889 	add	r0,r0,1

00000620 e3530000    890 	cmp	r3,0

00000624 aa000013    891 	bge	.L1321

00000628 ea000009    892 	b	.L1320

                     893 .L1316:

0000062c e7c26000    894 	strb	r6,[r2,r0]

00000630 e2800001    895 	add	r0,r0,1

00000634 e7c2a000    896 	strb	r10,[r2,r0]

00000638 e5943000    897 	ldr	r3,[r4]

0000063c e2800001    898 	add	r0,r0,1

00000640 e7c23000    899 	strb	r3,[r2,r0]

00000644 e5943004    900 	ldr	r3,[r4,4]

00000648 e2800001    901 	add	r0,r0,1

0000064c e3530000    902 	cmp	r3,0

00000650 aa000008    903 	bge	.L1321

                     904 .L1320:


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5101.s
00000654 e3a03000    905 	mov	r3,0

00000658 e7c27000    906 	strb	r7,[r2,r0]

0000065c e2800001    907 	add	r0,r0,1

00000660 e7c23000    908 	strb	r3,[r2,r0]

00000664 e5943008    909 	ldr	r3,[r4,8]

00000668 e2800001    910 	add	r0,r0,1

0000066c e3530000    911 	cmp	r3,0

00000670 aa000014    912 	bge	.L1326

00000674 ea000009    913 	b	.L1325

                     914 .L1321:

00000678 e7c26000    915 	strb	r6,[r2,r0]

0000067c e2800001    916 	add	r0,r0,1

00000680 e7c2a000    917 	strb	r10,[r2,r0]

00000684 e5943004    918 	ldr	r3,[r4,4]

00000688 e2800001    919 	add	r0,r0,1

0000068c e7c23000    920 	strb	r3,[r2,r0]

00000690 e5943008    921 	ldr	r3,[r4,8]

00000694 e2800001    922 	add	r0,r0,1

00000698 e3530000    923 	cmp	r3,0

0000069c aa000009    924 	bge	.L1326

                     925 .L1325:

000006a0 e3a03000    926 	mov	r3,0

000006a4 e7c27000    927 	strb	r7,[r2,r0]

000006a8 e2800001    928 	add	r0,r0,1

000006ac e7c23000    929 	strb	r3,[r2,r0]

000006b0 e2800001    930 	add	r0,r0,1

000006b4 e2844010    931 	add	r4,r4,16

000006b8 e28cc004    932 	add	r12,r12,4

000006bc e2555001    933 	subs	r5,r5,1

000006c0 1affffba    934 	bne	.L1309

000006c4 ea00000a    935 	b	.L1308

                     936 .L1326:

000006c8 e7c26000    937 	strb	r6,[r2,r0]

000006cc e2800001    938 	add	r0,r0,1

000006d0 e7c2a000    939 	strb	r10,[r2,r0]

000006d4 e5943008    940 	ldr	r3,[r4,8]

000006d8 e2800001    941 	add	r0,r0,1

000006dc e7c23000    942 	strb	r3,[r2,r0]

000006e0 e2800001    943 	add	r0,r0,1

000006e4 e2844010    944 	add	r4,r4,16

000006e8 e28cc004    945 	add	r12,r12,4

000006ec e2555001    946 	subs	r5,r5,1

000006f0 1affffae    947 	bne	.L1309

                     948 .L1308:

000006f4 e21b5003    949 	ands	r5,fp,3

000006f8 0a000018    950 	beq	.L1070

000006fc e3a04081    951 	mov	r4,129

00000700 e3a06001    952 	mov	r6,1

00000704 e3a07080    953 	mov	r7,128

                     954 .L1331:

00000708 e791310c    955 	ldr	r3,[r1,r12 lsl 2]

0000070c e3530000    956 	cmp	r3,0

00000710 aa000008    957 	bge	.L1333

00000714 e3a03000    958 	mov	r3,0

00000718 e7c24000    959 	strb	r4,[r2,r0]

0000071c e2800001    960 	add	r0,r0,1

00000720 e7c23000    961 	strb	r3,[r2,r0]

00000724 e2800001    962 	add	r0,r0,1

00000728 e28cc001    963 	add	r12,r12,1

0000072c e2555001    964 	subs	r5,r5,1

00000730 1afffff4    965 	bne	.L1331


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5101.s
00000734 ea000009    966 	b	.L1070

                     967 .L1333:

00000738 e7c27000    968 	strb	r7,[r2,r0]

0000073c e2800001    969 	add	r0,r0,1

00000740 e7c26000    970 	strb	r6,[r2,r0]

00000744 e791310c    971 	ldr	r3,[r1,r12 lsl 2]

00000748 e2800001    972 	add	r0,r0,1

0000074c e7c23000    973 	strb	r3,[r2,r0]

00000750 e2800001    974 	add	r0,r0,1

00000754 e28cc001    975 	add	r12,r12,1

00000758 e2555001    976 	subs	r5,r5,1

0000075c 1affffe9    977 	bne	.L1331

                     978 ;52:         }


                     979 ;53:     }


                     980 ;54:     return bufPos;


                     981 

                     982 ;226: 	return bufPos;


                     983 

                     984 .L1070:

00000760 e28dd03c    985 	add	sp,sp,60

00000764 e8bd8ff0    986 	ldmfd	[sp]!,{r4-fp,pc}

                     987 	.endf	mms_handleWriteRequest

                     988 	.align	4

                     989 ;domainId	[sp,52]	local

                     990 ;itemId	[sp,44]	local

                     991 ;tag	[sp,7]	local

                     992 ;length	[sp,8]	local

                     993 ;valListLen	[sp,12]	local

                     994 ;specLength	[sp,16]	local

                     995 ;specEnd	r9	local

                     996 ;specBufPos	r4	local

                     997 ;valCount	fp	local

                     998 ;valBuf	[sp,32]	local

                     999 ;mmsConn	r10	local

                    1000 ;invokeId	r7	local

                    1001 ;valCount	fp	local

                    1002 ;responseContentSizeTL	r10	local

                    1003 ;invokeIdSize	r5	local

                    1004 ;i	r3	local

                    1005 ;size	r4	local

                    1006 ;errList	r1	local

                    1007 ;buf	r2	local

                    1008 ;bufPos	r0	local

                    1009 ;i	r12	local

                    1010 

                    1011 ;isoConn	[sp,28]	param

                    1012 ;inBuf	r5	param

                    1013 ;bufPos	r4	param

                    1014 ;maxBufPos	r7	param

                    1015 ;invokeId	[sp,96]	param

                    1016 ;response	[sp,100]	param

                    1017 

                    1018 	.section ".bss","awb"

                    1019 .L2001:

                    1020 	.data

                    1021 	.text

                    1022 

                    1023 ;323: }


                    1024 	.align	4

                    1025 

                    1026 	.data


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5101.s
                    1027 	.ghsnote version,6

                    1028 	.ghsnote tools,3

                    1029 	.ghsnote options,0

                    1030 	.text

                    1031 	.align	4

