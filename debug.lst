                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c3k1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=debug.c -o gh_c3k1.o -list=debug.lst C:\Users\<USER>\AppData\Local\Temp\gh_c3k1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_c3k1.s
Source File: debug.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile debug.c

                      11 ;Source File:   debug.c

                      12 ;Directory:     

                      13 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      14 ;Compile Date:  Mon Jul 28 12:31:04 2025

                      15 ;Host OS:       Win32

                      16 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      17 ;Release:       MULTI v4.2.3

                      18 ;Revision Date: Wed Mar 29 05:25:47 2006

                      19 ;Release Date:  Fri Mar 31 10:02:14 2006

                      20 

                      21 ;1: #include <debug.h>


                      22 ;2: 


                      23 ;3: 


                      24 ;4: #ifdef SYSLOG


                      25 ;5: 


                      26 ;6: #include <syslog.h>


                      27 ;7: #include "Clib.h"


                      28 ;8: #include <string.h>


                      29 ;9: #include <stdarg.h>


                      30 ;10: 


                      31 ;11: int g_syslogHandler;


                      32 ;12: volatile STIMER g_pauseTimer;


                      33 ;13: 


                      34 ;14: static void pause(void)


                      35 ;15: {


                      36 ;16:     memset((STIMER*)&g_pauseTimer, 0, sizeof(g_pauseTimer));


                      37 ;17:     CreateTimer((STIMER*)&g_pauseTimer);


                      38 ;18:     g_pauseTimer.AlarmTime = 5000;


                      39 ;19:     g_pauseTimer.Precision = 1;


                      40 ;20:     g_pauseTimer.Started = 1;


                      41 ;21:     while ( !g_pauseTimer.Alarm) {


                      42 ;22:         Idle();


                      43 ;23:     }


                      44 ;24: }


                      45 ;25: 


                      46 ;26: void printf_wrapper(char* fmtMsg, ...)


                      47 ;27: {


                      48 ;28:     va_list args;


                      49 ;29:     va_start(args, fmtMsg);


                      50 ;30: 



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c3k1.s
                      51 ;31:     vsyslog(g_syslogHandler,LOG_NOTICE, fmtMsg, args);


                      52 ;32: 


                      53 ;33:     va_end(args);


                      54 ;34: }


                      55 ;35: 


                      56 ;36: void debugStart()


                      57 ;37: {    


                      58 ;38:     pause();


                      59 ;39:     syslog_init();


                      60 ;40:     g_syslogHandler = openlog ("MMS",0,LOG_LOCAL1);


                      61 ;41: }


                      62 ;42: 


                      63 ;43: #else


                      64 ;44: 


                      65 ;45: void debugStart()


                      66 

                      67 ;47: }


                      68 

                      69 ;48: 


                      70 ;49: #endif


                      71 ;50: 


                      72 ;51: 


                      73 ;52: void debugSendUshort(char* text, unsigned short value)


                      74 

                      75 ;54: }


                      76 

                      77 ;55: 


                      78 ;56: void debugSendStrL(char* text, unsigned char* str, int strLen)


                      79 

                      80 ;58: }


                      81 

                      82 ;59: 


                      83 ;60: void debugSendDump(char* text, unsigned char* data, int byteCount)


                      84 

                      85 ;62: }


                      86 

                      87 ;63: 


                      88 ;64: void debugSendText(char* text)


                      89 

                      90 ;66: }


                      91 	.text

                      92 	.align	4

                      93 debugStart::

                      94 ;46: {


                      95 

00000000 e12fff1e*    96 	ret	

                      97 	.endf	debugStart

                      98 	.align	4

                      99 

                     100 	.section ".bss","awb"

                     101 .L110:

                     102 	.data

                     103 	.text

                     104 	.align	4

                     105 	.align	4

                     106 debugSendUshort::

                     107 ;53: {


                     108 

00000004 e12fff1e*   109 	ret	

                     110 	.endf	debugSendUshort

                     111 	.align	4


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c3k1.s
                     112 

                     113 ;text	none	param

                     114 ;value	none	param

                     115 

                     116 	.section ".bss","awb"

                     117 .L142:

                     118 	.data

                     119 	.text

                     120 	.align	4

                     121 	.align	4

                     122 debugSendStrL::

                     123 ;57: {


                     124 

00000008 e12fff1e*   125 	ret	

                     126 	.endf	debugSendStrL

                     127 	.align	4

                     128 

                     129 ;text	none	param

                     130 ;str	none	param

                     131 ;strLen	none	param

                     132 

                     133 	.section ".bss","awb"

                     134 .L174:

                     135 	.data

                     136 	.text

                     137 	.align	4

                     138 	.align	4

                     139 debugSendDump::

                     140 ;61: {


                     141 

0000000c e12fff1e*   142 	ret	

                     143 	.endf	debugSendDump

                     144 	.align	4

                     145 

                     146 ;text	none	param

                     147 ;data	none	param

                     148 ;byteCount	none	param

                     149 

                     150 	.section ".bss","awb"

                     151 .L206:

                     152 	.data

                     153 	.text

                     154 	.align	4

                     155 	.align	4

                     156 debugSendText::

                     157 ;65: {


                     158 

00000010 e12fff1e*   159 	ret	

                     160 	.endf	debugSendText

                     161 	.align	4

                     162 

                     163 ;text	none	param

                     164 

                     165 	.section ".bss","awb"

                     166 .L238:

                     167 	.data

                     168 	.text

                     169 	.align	4

                     170 

                     171 	.data

                     172 	.ghsnote version,6


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c3k1.s
                     173 	.ghsnote tools,3

                     174 	.ghsnote options,0

                     175 	.text

                     176 	.align	4

