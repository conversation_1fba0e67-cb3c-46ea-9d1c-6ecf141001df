                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bt41.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedFloat.c -o iedTree\gh_bt41.o -list=iedTree/iedFloat.lst C:\Users\<USER>\AppData\Local\Temp\gh_bt41.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_bt41.s
Source File: iedFloat.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		iedTree/iedFloat.c -o iedTree/iedFloat.o

                      12 ;Source File:   iedTree/iedFloat.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:50 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "iedFloat.h"


                      23 ;2: 


                      24 ;3: #include "debug.h"


                      25 ;4: #include "iedFinalDA.h"


                      26 ;5: #include "iedEntity.h"


                      27 ;6: #include "iedTree.h"


                      28 ;7: #include "../DataSlice.h"


                      29 ;8: #include "../AsnEncoding.h"


                      30 ;9: #include "../BERCoder.h"


                      31 ;10: 


                      32 ;11: #include "fnan.h"


                      33 ;12: 


                      34 ;13: #define FLOAT_ENCODED_SIZE 7


                      35 ;14: 


                      36 ;15: static bool calcReadLen(IEDEntity entity, size_t* pLen )


                      37 	.text

                      38 	.align	4

                      39 calcReadLen:

                      40 ;16: {


                      41 

                      42 ;17:     *pLen = FLOAT_ENCODED_SIZE;


                      43 

00000000 e3a00007     44 	mov	r0,7

00000004 e5810000     45 	str	r0,[r1]

                      46 ;18:     return true;


                      47 

00000008 e3a00001     48 	mov	r0,1

0000000c e12fff1e*    49 	ret	

                      50 	.endf	calcReadLen


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bt41.s
                      51 	.align	4

                      52 

                      53 ;entity	none	param

                      54 ;pLen	r1	param

                      55 

                      56 	.section ".bss","awb"

                      57 .L62:

                      58 	.data

                      59 	.text

                      60 

                      61 ;19: }


                      62 

                      63 ;20: 


                      64 ;21: //Общая инициализация для FLOAT и REAL


                      65 ;22: static void commonInit(IEDEntity entity)


                      66 	.align	4

                      67 	.align	4

                      68 commonInit:

00000010 e92d4030     69 	stmfd	[sp]!,{r4-r5,lr}

00000014 e1a04000     70 	mov	r4,r0

00000018 e5940058     71 	ldr	r0,[r4,88]

                      72 ;25:     FloatAccsessInfo* accessInfo = extInfo->accessInfo;


                      73 

0000001c e5901000     74 	ldr	r1,[r0]

                      75 ;26: 


                      76 ;27:     extInfo->f.multiplier = accessInfo->multiplier;


                      77 

00000020 e5912008     78 	ldr	r2,[r1,8]

00000024 e5802004     79 	str	r2,[r0,4]

                      80 ;28: 


                      81 ;29:     //Если будет ошибка, то запишется -1;


                      82 ;30:     entity->dataSliceOffset = DataSlice_getAnalogOffset(accessInfo->valueOffset);


                      83 

00000028 e5910004     84 	ldr	r0,[r1,4]

0000002c e59f5398*    85 	ldr	r5,.L104

                      86 ;23: {


                      87 

                      88 ;24:     TerminalItem* extInfo = entity->extInfo;


                      89 

00000030 eb000000*    90 	bl	DataSlice_getAnalogOffset

00000034 e5845060     91 	str	r5,[r4,96]

                      92 ;33: 


                      93 ;34:     IEDTree_addToCmpList(entity);


                      94 

00000038 e584002c     95 	str	r0,[r4,44]

                      96 ;31: 


                      97 ;32:     entity->calcReadLen = calcReadLen;


                      98 

0000003c e1a00004     99 	mov	r0,r4

00000040 eb000000*   100 	bl	IEDTree_addToCmpList

00000044 e8bd4030    101 	ldmfd	[sp]!,{r4-r5,lr}

00000048 e12fff1e*   102 	ret	

                     103 	.endf	commonInit

                     104 	.align	4

                     105 ;extInfo	r0	local

                     106 ;accessInfo	r1	local

                     107 

                     108 ;entity	r4	param

                     109 

                     110 	.section ".bss","awb"

                     111 .L97:


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bt41.s
                     112 	.data

                     113 	.text

                     114 

                     115 ;35: }


                     116 

                     117 ;36: 


                     118 ;37: //===================REAL======================


                     119 ;38: 


                     120 ;39: static void realUpdateFromDataSlice(IEDEntity entity)


                     121 	.align	4

                     122 	.align	4

                     123 realUpdateFromDataSlice:

0000004c e92d4030    124 	stmfd	[sp]!,{r4-r5,lr}

00000050 e1a04000    125 	mov	r4,r0

                     126 ;40: {


                     127 

                     128 ;41:     int offset  = entity->dataSliceOffset;


                     129 

00000054 e594002c    130 	ldr	r0,[r4,44]

                     131 ;42:     float value;


                     132 ;43: 


                     133 ;44:     if(offset == -1)


                     134 

00000058 e3700001    135 	cmn	r0,1

0000005c 0a000014    136 	beq	.L105

                     137 ;45:     {


                     138 

                     139 ;46:         return;


                     140 

                     141 ;47:     }


                     142 ;48: 


                     143 ;49:     value = DataSlice_getRealFastCurrDS(offset);


                     144 

00000060 e1a00800    145 	mov	r0,r0 lsl 16

00000064 e1a00820    146 	mov	r0,r0 lsr 16

00000068 eb000000*   147 	bl	DataSlice_getRealFastCurrDS

0000006c e1a05000    148 	mov	r5,r0

                     149 ;50: 


                     150 ;51:     if(entity->realValue == value)


                     151 

00000070 e5940030    152 	ldr	r0,[r4,48]

00000074 e1a01005    153 	mov	r1,r5

00000078 eb000000*   154 	bl	__fcmp

0000007c e3500000    155 	cmp	r0,0

                     156 ;52:     {


                     157 

                     158 ;53:         entity->changed = TRGOP_NONE;


                     159 

00000080 03a00000    160 	moveq	r0,0

00000084 05840028    161 	streq	r0,[r4,40]

00000088 0a000009    162 	beq	.L105

                     163 ;54:     }


                     164 ;55:     else


                     165 ;56:     {


                     166 

                     167 ;57: 		//Кэш сбрасываем для RealAsInt64


                     168 ;58:         entity->cached = false;


                     169 

0000008c e3a00000    170 	mov	r0,0

00000090 e5c40034    171 	strb	r0,[r4,52]

                     172 ;59: 



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bt41.s
                     173 ;60:         entity->changed = entity->trgOps;


                     174 

00000094 e5940024    175 	ldr	r0,[r4,36]

00000098 e5845030    176 	str	r5,[r4,48]

                     177 ;62:         IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());


                     178 

0000009c e5840028    179 	str	r0,[r4,40]

                     180 ;61:         entity->realValue = value;


                     181 

000000a0 eb000000*   182 	bl	dataSliceGetTimeStamp

000000a4 e1a02001    183 	mov	r2,r1

000000a8 e1a01000    184 	mov	r1,r0

000000ac e1a00004    185 	mov	r0,r4

000000b0 eb000000*   186 	bl	IEDEntity_setTimeStamp

                     187 .L105:

000000b4 e8bd4030    188 	ldmfd	[sp]!,{r4-r5,lr}

000000b8 e12fff1e*   189 	ret	

                     190 	.endf	realUpdateFromDataSlice

                     191 	.align	4

                     192 ;offset	r0	local

                     193 ;value	r5	local

                     194 

                     195 ;entity	r4	param

                     196 

                     197 	.section ".bss","awb"

                     198 .L168:

                     199 	.data

                     200 	.text

                     201 

                     202 ;63:     }


                     203 ;64: }


                     204 

                     205 ;65: 


                     206 ;66: static bool realEncodeRead(IEDEntity entity, BufferView* outBuf)


                     207 	.align	4

                     208 	.align	4

                     209 realEncodeRead:

000000bc e92d4030    210 	stmfd	[sp]!,{r4-r5,lr}

000000c0 e24dd010    211 	sub	sp,sp,16

                     212 ;67: {


                     213 

                     214 ;68:     uint8_t* encodeBuf;


                     215 ;69:     TerminalItem* extInfo = entity->extInfo;


                     216 

000000c4 e5904030    217 	ldr	r4,[r0,48]

                     218 ;71: 


                     219 ;72:     if(!isfnan(value))


                     220 

000000c8 e1a05001    221 	mov	r5,r1

000000cc e58d400c    222 	str	r4,[sp,12]

000000d0 e5901058    223 	ldr	r1,[r0,88]

                     224 ;70:     float value = entity->realValue;


                     225 

000000d4 e3a005fe    226 	mov	r0,254<<22

000000d8 e2800440    227 	add	r0,r0,1<<30

000000dc e0042000    228 	and	r2,r4,r0

000000e0 e1520000    229 	cmp	r2,r0

000000e4 0a000003    230 	beq	.L187

                     231 ;73:     {


                     232 

                     233 ;74:         value *= extInfo->f.multiplier;



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bt41.s
                     234 

000000e8 e5911004    235 	ldr	r1,[r1,4]

000000ec e1a00004    236 	mov	r0,r4

000000f0 eb000000*   237 	bl	__fmul

000000f4 e1a04000    238 	mov	r4,r0

                     239 .L187:

000000f8 e28d2008    240 	add	r2,sp,8

000000fc e1a00005    241 	mov	r0,r5

00000100 e3a01007    242 	mov	r1,7

00000104 eb000000*   243 	bl	BufferView_alloc

                     244 ;75:     }


                     245 ;76: 


                     246 ;77:     if(!BufferView_alloc(outBuf,FLOAT_ENCODED_SIZE, &encodeBuf))


                     247 

00000108 e3500000    248 	cmp	r0,0

                     249 ;78:     {


                     250 

                     251 ;79:         ERROR_REPORT("Unable to allocate buffer");


                     252 ;80:         return false;


                     253 

0000010c 0a00000b    254 	beq	.L185

                     255 ;81:     }


                     256 ;82: 


                     257 ;83:     // Возвращаемое значение не нужно,


                     258 ;84:     // потому что функция не возвращает ошибки, а размер известен заранее


                     259 ;85:     BerEncoder_EncodeFloatWithTL(0x87, value, 32, 8, encodeBuf, 0);


                     260 

00000110 e59d0008    261 	ldr	r0,[sp,8]

00000114 e3a01000    262 	mov	r1,0

00000118 e88d0003    263 	stmea	[sp],{r0-r1}

0000011c e1a01004    264 	mov	r1,r4

00000120 e3a03008    265 	mov	r3,8

00000124 e3a02020    266 	mov	r2,32

00000128 e3a00087    267 	mov	r0,135

0000012c eb000000*   268 	bl	BerEncoder_EncodeFloatWithTL

                     269 ;86:     outBuf->pos += FLOAT_ENCODED_SIZE;


                     270 

00000130 e5950004    271 	ldr	r0,[r5,4]

00000134 e2800007    272 	add	r0,r0,7

00000138 e5850004    273 	str	r0,[r5,4]

                     274 ;87:     return true;


                     275 

0000013c e3a00001    276 	mov	r0,1

                     277 .L185:

00000140 e28dd010    278 	add	sp,sp,16

00000144 e8bd4030    279 	ldmfd	[sp]!,{r4-r5,lr}

00000148 e12fff1e*   280 	ret	

                     281 	.endf	realEncodeRead

                     282 	.align	4

                     283 ;encodeBuf	[sp,8]	local

                     284 ;extInfo	r1	local

                     285 ;value	r4	local

                     286 ;value	[sp,12]	local

                     287 

                     288 ;entity	r0	param

                     289 ;outBuf	r5	param

                     290 

                     291 	.section ".bss","awb"

                     292 .L282:

                     293 	.data

                     294 	.text


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bt41.s
                     295 

                     296 ;88: }


                     297 

                     298 ;89: 


                     299 ;90: void IEDReal_init(IEDEntity entity)


                     300 	.align	4

                     301 	.align	4

                     302 IEDReal_init::

0000014c e92d4070    303 	stmfd	[sp]!,{r4-r6,lr}

00000150 e59f5278*   304 	ldr	r5,.L325

00000154 e59f6278*   305 	ldr	r6,.L326

                     306 ;91: {


                     307 

                     308 ;92:     commonInit(entity);


                     309 

00000158 e1a04000    310 	mov	r4,r0

0000015c ebffffab*   311 	bl	commonInit

                     312 ;93:     entity->encodeRead = realEncodeRead;


                     313 

00000160 e584505c    314 	str	r5,[r4,92]

                     315 ;94:     entity->updateFromDataSlice = realUpdateFromDataSlice;


                     316 

00000164 e5846068    317 	str	r6,[r4,104]

00000168 e8bd8070    318 	ldmfd	[sp]!,{r4-r6,pc}

                     319 	.endf	IEDReal_init

                     320 	.align	4

                     321 

                     322 ;entity	r4	param

                     323 

                     324 	.section ".bss","awb"

                     325 .L318:

                     326 	.data

                     327 	.text

                     328 

                     329 ;95: }


                     330 

                     331 ;96: 


                     332 ;97: //===================REAL AS INT64======================


                     333 ;98: static void realAsInt64FillCache(IEDEntity entity)


                     334 	.align	4

                     335 	.align	4

                     336 realAsInt64FillCache:

0000016c e92d4010    337 	stmfd	[sp]!,{r4,lr}

                     338 ;99: {


                     339 

                     340 ;100:     TerminalItem* extInfo = entity->extInfo;


                     341 

00000170 e24dd00c    342 	sub	sp,sp,12

00000174 e1a04000    343 	mov	r4,r0

00000178 e5940030    344 	ldr	r0,[r4,48]

0000017c e5942058    345 	ldr	r2,[r4,88]

                     346 ;101:     int64_t intValue;


                     347 ;102:     if(isfnan(entity->realValue))


                     348 

00000180 e58d0000    349 	str	r0,[sp]

00000184 e3a015fe    350 	mov	r1,254<<22

00000188 e2811440    351 	add	r1,r1,1<<30

0000018c e0003001    352 	and	r3,r0,r1

00000190 e1530001    353 	cmp	r3,r1

                     354 ;103:     {


                     355 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bt41.s
                     356 ;104:         intValue = -1;


                     357 

00000194 03e00000    358 	mvneq	r0,0

00000198 01a01000    359 	moveq	r1,r0

0000019c 0a000002    360 	beq	.L335

                     361 ;105:     }


                     362 ;106:     else


                     363 ;107:     {


                     364 

                     365 ;108:         float realValue = entity->realValue * extInfo->f.multiplier;


                     366 

000001a0 e5921004    367 	ldr	r1,[r2,4]

000001a4 eb000000*   368 	bl	__fmul

                     369 ;109:         intValue = (uint64_t)realValue;


                     370 

000001a8 eb000000*   371 	bl	__ftou64

                     372 .L335:

                     373 ;110:     }


                     374 ;111:     entity->cache.int64Value = intValue;


                     375 

000001ac e98d0003    376 	stmfa	[sp],{r0-r1}

000001b0 e584003c    377 	str	r0,[r4,60]

000001b4 e28d0004    378 	add	r0,sp,4

000001b8 e5841040    379 	str	r1,[r4,64]

                     380 ;112:     entity->cachedBERLen =


                     381 

000001bc e3a01008    382 	mov	r1,8

000001c0 eb000000*   383 	bl	BERCoder_calcIntEncodedLen

000001c4 e5840038    384 	str	r0,[r4,56]

                     385 ;113:         BERCoder_calcIntEncodedLen(&intValue, sizeof(intValue));


                     386 ;114:     entity->cached = true;


                     387 

000001c8 e3a00001    388 	mov	r0,1

000001cc e5c40034    389 	strb	r0,[r4,52]

000001d0 e28dd00c    390 	add	sp,sp,12

000001d4 e8bd4010    391 	ldmfd	[sp]!,{r4,lr}

000001d8 e12fff1e*   392 	ret	

                     393 	.endf	realAsInt64FillCache

                     394 	.align	4

                     395 ;extInfo	r2	local

                     396 ;intValue	[sp,4]	local

                     397 ;value	[sp]	local

                     398 

                     399 ;entity	r4	param

                     400 

                     401 	.section ".bss","awb"

                     402 .L380:

                     403 	.data

                     404 	.text

                     405 

                     406 ;115: }


                     407 

                     408 ;116: 


                     409 ;117: static bool realAsInt64CalcReadLen(IEDEntity entity, size_t* pLen )


                     410 	.align	4

                     411 	.align	4

                     412 realAsInt64CalcReadLen:

000001dc e92d4030    413 	stmfd	[sp]!,{r4-r5,lr}

000001e0 e1a04000    414 	mov	r4,r0

                     415 ;118: {


                     416 


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bt41.s
                     417 ;119:     if(!entity->cached)


                     418 

000001e4 e5d40034    419 	ldrb	r0,[r4,52]

000001e8 e1a05001    420 	mov	r5,r1

000001ec e3500000    421 	cmp	r0,0

                     422 ;120:     {


                     423 

                     424 ;121:         realAsInt64FillCache(entity);


                     425 

000001f0 01a00004    426 	moveq	r0,r4

000001f4 0bffffdc*   427 	bleq	realAsInt64FillCache

                     428 ;122:     }


                     429 ;123:     *pLen = entity->cachedBERLen + 2;


                     430 

000001f8 e5940038    431 	ldr	r0,[r4,56]

000001fc e2800002    432 	add	r0,r0,2

00000200 e5850000    433 	str	r0,[r5]

                     434 ;124:     return true;


                     435 

00000204 e3a00001    436 	mov	r0,1

00000208 e8bd4030    437 	ldmfd	[sp]!,{r4-r5,lr}

0000020c e12fff1e*   438 	ret	

                     439 	.endf	realAsInt64CalcReadLen

                     440 	.align	4

                     441 

                     442 ;entity	r4	param

                     443 ;pLen	r5	param

                     444 

                     445 	.section ".bss","awb"

                     446 .L445:

                     447 	.data

                     448 	.text

                     449 

                     450 ;125: }


                     451 

                     452 ;126: 


                     453 ;127: static bool realAsInt64EncodeRead(IEDEntity entity, BufferView* outBuf)


                     454 	.align	4

                     455 	.align	4

                     456 realAsInt64EncodeRead:

00000210 e92d4030    457 	stmfd	[sp]!,{r4-r5,lr}

00000214 e1a04000    458 	mov	r4,r0

                     459 ;128: {


                     460 

                     461 ;129:     if(!entity->cached)


                     462 

00000218 e5d40034    463 	ldrb	r0,[r4,52]

0000021c e1a05001    464 	mov	r5,r1

00000220 e3500000    465 	cmp	r0,0

                     466 ;130:     {


                     467 

                     468 ;131:         realAsInt64FillCache(entity);


                     469 

00000224 01a00004    470 	moveq	r0,r4

00000228 0bffffcf*   471 	bleq	realAsInt64FillCache

                     472 ;132:     }


                     473 ;133: 


                     474 ;134:     if(!BufferView_encodeTL(outBuf, IEC61850_BER_INTEGER, entity->cachedBERLen))


                     475 

0000022c e5942038    476 	ldr	r2,[r4,56]

00000230 e1a00005    477 	mov	r0,r5


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bt41.s
00000234 e3a01085    478 	mov	r1,133

00000238 eb000000*   479 	bl	BufferView_encodeTL

0000023c e3500000    480 	cmp	r0,0

00000240 0a000005    481 	beq	.L466

                     482 ;135:     {


                     483 

                     484 ;136:         ERROR_REPORT("Uable to write TL");


                     485 ;137:         return false;


                     486 

                     487 ;138:     }


                     488 ;139: 


                     489 ;140:     if(!BufferView_reverseWrite(outBuf, &entity->cache.int64Value,


                     490 

00000244 e5942038    491 	ldr	r2,[r4,56]

00000248 e284103c    492 	add	r1,r4,60

0000024c e1a00005    493 	mov	r0,r5

00000250 eb000000*   494 	bl	BufferView_reverseWrite

00000254 e3500000    495 	cmp	r0,0

                     496 ;145:     }


                     497 ;146: 


                     498 ;147:     return true;


                     499 

00000258 13a00001    500 	movne	r0,1

                     501 .L466:

                     502 ;141:         entity->cachedBERLen))


                     503 ;142:     {


                     504 

                     505 ;143:         ERROR_REPORT("Uable to write int64 value");


                     506 ;144:         return false;


                     507 

0000025c 03a00000    508 	moveq	r0,0

                     509 .L458:

00000260 e8bd4030    510 	ldmfd	[sp]!,{r4-r5,lr}

00000264 e12fff1e*   511 	ret	

                     512 	.endf	realAsInt64EncodeRead

                     513 	.align	4

                     514 

                     515 ;entity	r4	param

                     516 ;outBuf	r5	param

                     517 

                     518 	.section ".bss","awb"

                     519 .L546:

                     520 	.data

                     521 	.text

                     522 

                     523 ;148: }


                     524 

                     525 ;149: 


                     526 ;150: void IEDRealAsInt64_init(IEDEntity entity)


                     527 	.align	4

                     528 	.align	4

                     529 IEDRealAsInt64_init::

00000268 e92d4070    530 	stmfd	[sp]!,{r4-r6,lr}

0000026c e59f5164*   531 	ldr	r5,.L597

                     532 ;151: {


                     533 

                     534 ;152:     commonInit(entity);


                     535 

00000270 e280405c    536 	add	r4,r0,92

00000274 ebffff65*   537 	bl	commonInit

                     538 ;153:     entity->calcReadLen = realAsInt64CalcReadLen;



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bt41.s
                     539 

                     540 ;154:     entity->encodeRead = realAsInt64EncodeRead;


                     541 

00000278 e59f015c*   542 	ldr	r0,.L598

0000027c e8840021    543 	stmea	[r4],{r0,r5}

                     544 ;155:     entity->updateFromDataSlice = realUpdateFromDataSlice;


                     545 

00000280 e59f014c*   546 	ldr	r0,.L326

00000284 e584000c    547 	str	r0,[r4,12]

00000288 e8bd8070    548 	ldmfd	[sp]!,{r4-r6,pc}

                     549 	.endf	IEDRealAsInt64_init

                     550 	.align	4

                     551 

                     552 ;entity	r4	param

                     553 

                     554 	.section ".bss","awb"

                     555 .L590:

                     556 	.data

                     557 	.text

                     558 

                     559 ;156: }


                     560 

                     561 ;157: 


                     562 ;158: //===================FLOAT======================


                     563 ;159: static void floatUpdateFromDataSlice(IEDEntity entity)


                     564 	.align	4

                     565 	.align	4

                     566 floatUpdateFromDataSlice:

0000028c e92d4010    567 	stmfd	[sp]!,{r4,lr}

00000290 e1a04000    568 	mov	r4,r0

                     569 ;160: {


                     570 

                     571 ;161:     int offset  = entity->dataSliceOffset;


                     572 

00000294 e594002c    573 	ldr	r0,[r4,44]

                     574 ;162:     int value;


                     575 ;163: 


                     576 ;164:     if(offset == -1)


                     577 

00000298 e3700001    578 	cmn	r0,1

0000029c 0a00000f    579 	beq	.L599

                     580 ;165:     {


                     581 

                     582 ;166:         return;


                     583 

                     584 ;167:     }


                     585 ;168: 


                     586 ;169:     value = DataSlice_getFixedFastCurrDS(offset);


                     587 

000002a0 e1a00800    588 	mov	r0,r0 lsl 16

000002a4 e1a00820    589 	mov	r0,r0 lsr 16

000002a8 eb000000*   590 	bl	DataSlice_getFixedFastCurrDS

                     591 ;170: 


                     592 ;171:     if(entity->fixedValue == value)


                     593 

000002ac e5941030    594 	ldr	r1,[r4,48]

000002b0 e1510000    595 	cmp	r1,r0

                     596 ;172:     {


                     597 

                     598 ;173:         entity->changed = TRGOP_NONE;


                     599 


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bt41.s
000002b4 03a00000    600 	moveq	r0,0

000002b8 05840028    601 	streq	r0,[r4,40]

000002bc 0a000007    602 	beq	.L599

                     603 ;174:     }


                     604 ;175:     else


                     605 ;176:     {


                     606 

                     607 ;177:         entity->changed = entity->trgOps;


                     608 

000002c0 e5941024    609 	ldr	r1,[r4,36]

000002c4 e5840030    610 	str	r0,[r4,48]

                     611 ;179:         IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());


                     612 

000002c8 e5841028    613 	str	r1,[r4,40]

                     614 ;178:         entity->fixedValue = value;


                     615 

000002cc eb000000*   616 	bl	dataSliceGetTimeStamp

000002d0 e1a02001    617 	mov	r2,r1

000002d4 e1a01000    618 	mov	r1,r0

000002d8 e1a00004    619 	mov	r0,r4

000002dc eb000000*   620 	bl	IEDEntity_setTimeStamp

                     621 .L599:

000002e0 e8bd4010    622 	ldmfd	[sp]!,{r4,lr}

000002e4 e12fff1e*   623 	ret	

                     624 	.endf	floatUpdateFromDataSlice

                     625 	.align	4

                     626 ;offset	r0	local

                     627 ;value	r0	local

                     628 

                     629 ;entity	r4	param

                     630 

                     631 	.section ".bss","awb"

                     632 .L648:

                     633 	.data

                     634 	.text

                     635 

                     636 ;180:     }


                     637 ;181: }


                     638 

                     639 ;182: 


                     640 ;183: static bool floatEncodeRead(IEDEntity entity, BufferView* outBuf)


                     641 	.align	4

                     642 	.align	4

                     643 floatEncodeRead:

000002e8 e92d40f0    644 	stmfd	[sp]!,{r4-r7,lr}

000002ec e24dd010    645 	sub	sp,sp,16

                     646 ;184: {


                     647 

                     648 ;185:     uint8_t* encodeBuf;


                     649 ;186:     TerminalItem* extInfo = entity->extInfo;


                     650 

                     651 ;187:     int fixedValue = entity->fixedValue;


                     652 

000002f0 e5907030    653 	ldr	r7,[r0,48]

                     654 ;188:     float floatValue;


                     655 ;189: 


                     656 ;190:     if(fixedValue != 0x7FFFFFFF)


                     657 

000002f4 e1a06001    658 	mov	r6,r1

000002f8 e3770360    659 	cmn	r7,0x80000001

000002fc 0a000012    660 	beq	.L672


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bt41.s
00000300 e5901058    661 	ldr	r1,[r0,88]

                     662 ;191:     {


                     663 

                     664 ;192: 		double doubleValue = fixedValue;        


                     665 

00000304 e5910004    666 	ldr	r0,[r1,4]

00000308 eb000000*   667 	bl	__ftod

0000030c e1a05001    668 	mov	r5,r1

00000310 e1a04000    669 	mov	r4,r0

00000314 e1a00007    670 	mov	r0,r7

00000318 eb000000*   671 	bl	__itod

0000031c e1a02004    672 	mov	r2,r4

00000320 e1a03005    673 	mov	r3,r5

00000324 eb000000*   674 	bl	__dmul

                     675 ;193:         doubleValue *= extInfo->f.multiplier;


                     676 

                     677 ;194: 		floatValue = (float)doubleValue;


                     678 

00000328 eb000000*   679 	bl	__dtof

0000032c e28d2008    680 	add	r2,sp,8

00000330 e1a04000    681 	mov	r4,r0

00000334 e1a00006    682 	mov	r0,r6

00000338 e3a01007    683 	mov	r1,7

0000033c eb000000*   684 	bl	BufferView_alloc

                     685 ;199:     }


                     686 ;200: 


                     687 ;201:     if(!BufferView_alloc(outBuf,FLOAT_ENCODED_SIZE, &encodeBuf))


                     688 

00000340 e3500000    689 	cmp	r0,0

00000344 0a000008    690 	beq	.L675

00000348 ea000008    691 	b	.L674

                     692 .L672:

                     693 ;195:     }


                     694 ;196:     else


                     695 ;197:     {


                     696 

                     697 ;198:         floatValue = fnan(0);


                     698 

0000034c e3a045ff    699 	mov	r4,255<<22

00000350 e2844440    700 	add	r4,r4,1<<30

00000354 e58d400c    701 	str	r4,[sp,12]

00000358 e28d2008    702 	add	r2,sp,8

0000035c e1a00006    703 	mov	r0,r6

00000360 e3a01007    704 	mov	r1,7

00000364 eb000000*   705 	bl	BufferView_alloc

                     706 ;199:     }


                     707 ;200: 


                     708 ;201:     if(!BufferView_alloc(outBuf,FLOAT_ENCODED_SIZE, &encodeBuf))


                     709 

00000368 e3500000    710 	cmp	r0,0

                     711 .L675:

                     712 ;202:     {


                     713 

                     714 ;203:         ERROR_REPORT("Unable to allocate buffer");


                     715 ;204:         return false;


                     716 

0000036c 0a00000b    717 	beq	.L665

                     718 .L674:

                     719 ;205:     }


                     720 ;206: 


                     721 ;207:     // Возвращаемое значение не нужно,



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bt41.s
                     722 ;208:     // потому что функция не возвращает ошибки, а размер известен заранее


                     723 ;209:     BerEncoder_EncodeFloatWithTL(0x87, floatValue, 32, 8, encodeBuf, 0);


                     724 

00000370 e59d0008    725 	ldr	r0,[sp,8]

00000374 e3a01000    726 	mov	r1,0

00000378 e88d0003    727 	stmea	[sp],{r0-r1}

0000037c e1a01004    728 	mov	r1,r4

00000380 e3a03008    729 	mov	r3,8

00000384 e3a02020    730 	mov	r2,32

00000388 e3a00087    731 	mov	r0,135

0000038c eb000000*   732 	bl	BerEncoder_EncodeFloatWithTL

                     733 ;210:     outBuf->pos += FLOAT_ENCODED_SIZE;


                     734 

00000390 e5960004    735 	ldr	r0,[r6,4]

00000394 e2800007    736 	add	r0,r0,7

00000398 e5860004    737 	str	r0,[r6,4]

                     738 ;211:     return true;


                     739 

0000039c e3a00001    740 	mov	r0,1

                     741 .L665:

000003a0 e28dd010    742 	add	sp,sp,16

000003a4 e8bd40f0    743 	ldmfd	[sp]!,{r4-r7,lr}

000003a8 e12fff1e*   744 	ret	

                     745 	.endf	floatEncodeRead

                     746 	.align	4

                     747 ;encodeBuf	[sp,8]	local

                     748 ;extInfo	r1	local

                     749 ;fixedValue	r7	local

                     750 ;floatValue	r4	local

                     751 ;inan	[sp,12]	local

                     752 

                     753 ;entity	r0	param

                     754 ;outBuf	r6	param

                     755 

                     756 	.section ".bss","awb"

                     757 .L762:

                     758 	.data

                     759 	.text

                     760 

                     761 ;212: }


                     762 

                     763 ;213: 


                     764 ;214: void IEDFloat_init(IEDEntity entity)


                     765 	.align	4

                     766 	.align	4

                     767 IEDFloat_init::

000003ac e92d4070    768 	stmfd	[sp]!,{r4-r6,lr}

000003b0 e59f5028*   769 	ldr	r5,.L805

000003b4 e59f6028*   770 	ldr	r6,.L806

                     771 ;215: {


                     772 

                     773 ;216:     commonInit(entity);


                     774 

000003b8 e1a04000    775 	mov	r4,r0

000003bc ebffff13*   776 	bl	commonInit

                     777 ;217:     entity->encodeRead = floatEncodeRead;


                     778 

000003c0 e584505c    779 	str	r5,[r4,92]

                     780 ;218:     entity->updateFromDataSlice = floatUpdateFromDataSlice;


                     781 

000003c4 e5846068    782 	str	r6,[r4,104]


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bt41.s
000003c8 e8bd8070    783 	ldmfd	[sp]!,{r4-r6,pc}

                     784 	.endf	IEDFloat_init

                     785 	.align	4

                     786 

                     787 ;entity	r4	param

                     788 

                     789 	.section ".bss","awb"

                     790 .L798:

                     791 	.data

                     792 	.text

                     793 

                     794 ;219: }


                     795 	.align	4

                     796 .L104:

000003cc 00000000*   797 	.data.w	calcReadLen

                     798 	.type	.L104,$object

                     799 	.size	.L104,4

                     800 

                     801 .L325:

000003d0 00000000*   802 	.data.w	realEncodeRead

                     803 	.type	.L325,$object

                     804 	.size	.L325,4

                     805 

                     806 .L326:

000003d4 00000000*   807 	.data.w	realUpdateFromDataSlice

                     808 	.type	.L326,$object

                     809 	.size	.L326,4

                     810 

                     811 .L597:

000003d8 00000000*   812 	.data.w	realAsInt64CalcReadLen

                     813 	.type	.L597,$object

                     814 	.size	.L597,4

                     815 

                     816 .L598:

000003dc 00000000*   817 	.data.w	realAsInt64EncodeRead

                     818 	.type	.L598,$object

                     819 	.size	.L598,4

                     820 

                     821 .L805:

000003e0 00000000*   822 	.data.w	floatEncodeRead

                     823 	.type	.L805,$object

                     824 	.size	.L805,4

                     825 

                     826 .L806:

000003e4 00000000*   827 	.data.w	floatUpdateFromDataSlice

                     828 	.type	.L806,$object

                     829 	.size	.L806,4

                     830 

                     831 	.align	4

                     832 

                     833 	.data

                     834 	.ghsnote version,6

                     835 	.ghsnote tools,3

                     836 	.ghsnote options,0

                     837 	.text

                     838 	.align	4

