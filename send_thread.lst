                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_80g1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=send_thread.c -o gh_80g1.o -list=send_thread.lst C:\Users\<USER>\AppData\Local\Temp\gh_80g1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_80g1.s
Source File: send_thread.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile send_thread.c

                      11 ;		-o send_thread.o

                      12 ;Source File:   send_thread.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:07 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "send_thread.h"


                      23 ;2: #include "out_buffers.h"


                      24 ;3: #include "Cotp.h"


                      25 ;4: #include <Clib.h>


                      26 ;5: #include <stddef.h>


                      27 ;6: 


                      28 ;7: void sendThread(IsoConnection* isoConn)


                      29 	.text

                      30 	.align	4

                      31 sendThread::

00000000 e92d4030     32 	stmfd	[sp]!,{r4-r5,lr}

                      33 ;8: {


                      34 

                      35 ;9:     isoConn->sendThreadIsRunning = true;


                      36 

00000004 e3a01bf2     37 	mov	r1,242<<10

00000008 e2811099     38 	add	r1,r1,153

0000000c e1a04000     39 	mov	r4,r0

00000010 e3a00001     40 	mov	r0,1

00000014 e7c10004     41 	strb	r0,[r1,r4]

                      42 ;10:     while(isoConn->connected)


                      43 

00000018 e3a01bf2     44 	mov	r1,242<<10

0000001c e2811098     45 	add	r1,r1,152

00000020 e7d10004     46 	ldrb	r0,[r1,r4]

00000024 e3500000     47 	cmp	r0,0

00000028 0a000017     48 	beq	.L5

                      49 .L6:

                      50 ;11:     {



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_80g1.s
                      51 

                      52 ;12:         SessionOutBuffer* buffer = OutQueue_get(&isoConn->outQueue);


                      53 

0000002c e2840bf2     54 	add	r0,r4,242<<10

00000030 e280006c     55 	add	r0,r0,108

00000034 eb000000*    56 	bl	OutQueue_get

00000038 e1b05000     57 	movs	r5,r0

                      58 ;13:         if(buffer == NULL)


                      59 

0000003c 1a000006     60 	bne	.L7

                      61 ;14:         {


                      62 

                      63 ;15:             Idle();


                      64 

00000040 e6000010     65 	.word	0xE6000010

                      66 

00000044 e3a01bf2     67 	mov	r1,242<<10

00000048 e2811098     68 	add	r1,r1,152

0000004c e7d10004     69 	ldrb	r0,[r1,r4]

00000050 e3500000     70 	cmp	r0,0

00000054 1afffff4     71 	bne	.L6

00000058 ea00000b     72 	b	.L5

                      73 .L7:

                      74 ;16:         }


                      75 ;17:         else


                      76 ;18:         {


                      77 

                      78 ;19:             //Посылаем


                      79 ;20:             cotpSendData(&isoConn->cotpConn, buffer->cotpOutBuf, buffer->byteCount);


                      80 

0000005c e5952004     81 	ldr	r2,[r5,4]

00000060 e2851008     82 	add	r1,r5,8

00000064 e2840bf2     83 	add	r0,r4,242<<10

00000068 e280009c     84 	add	r0,r0,156

0000006c eb000000*    85 	bl	cotpSendData

                      86 ;21: 			freeSessionOutBuffer(buffer);


                      87 

00000070 e1a00005     88 	mov	r0,r5

00000074 eb000000*    89 	bl	freeSessionOutBuffer

00000078 e3a01bf2     90 	mov	r1,242<<10

0000007c e2811098     91 	add	r1,r1,152

00000080 e7d10004     92 	ldrb	r0,[r1,r4]

00000084 e3500000     93 	cmp	r0,0

00000088 1affffe7     94 	bne	.L6

                      95 .L5:

                      96 ;22:         }


                      97 ;23:     }


                      98 ;24:     isoConn->sendThreadIsRunning = false;


                      99 

0000008c e3a01bf2    100 	mov	r1,242<<10

00000090 e2811099    101 	add	r1,r1,153

00000094 e7c10004    102 	strb	r0,[r1,r4]

00000098 e8bd8030    103 	ldmfd	[sp]!,{r4-r5,pc}

                     104 	.endf	sendThread

                     105 	.align	4

                     106 ;buffer	r5	local

                     107 

                     108 ;isoConn	r4	param

                     109 

                     110 	.section ".bss","awb"

                     111 .L82:


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_80g1.s
                     112 	.data

                     113 	.text

                     114 

                     115 ;25: }


                     116 	.align	4

                     117 

                     118 	.data

                     119 	.ghsnote version,6

                     120 	.ghsnote tools,1

                     121 	.ghsnote options,0

                     122 	.text

                     123 	.align	4

