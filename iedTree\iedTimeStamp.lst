                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4m01.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedTimeStamp.c -o iedTree\gh_4m01.o -list=iedTree/iedTimeStamp.lst C:\Users\<USER>\AppData\Local\Temp\gh_4m01.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_4m01.s
Source File: iedTimeStamp.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		iedTree/iedTimeStamp.c -o iedTree/iedTimeStamp.o

                      12 ;Source File:   iedTree/iedTimeStamp.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:51 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "iedTimeStamp.h"


                      23 ;2: 


                      24 ;3: #include "../DataSlice.h"


                      25 ;4: #include "../mms_data.h"


                      26 ;5: #include "../BaseAsnTypes.h"


                      27 ;6: 


                      28 ;7: #include <debug.h>


                      29 ;8: 


                      30 ;9: #include <stdint.h>


                      31 ;10: 


                      32 ;11: bool IEDTimeStampDA_calcReadLen(IEDEntity entity, size_t *pLen)


                      33 	.text

                      34 	.align	4

                      35 IEDTimeStampDA_calcReadLen::

                      36 ;12: {


                      37 

                      38 ;13:     *pLen = 10;


                      39 

00000000 e3a0000a     40 	mov	r0,10

00000004 e5810000     41 	str	r0,[r1]

                      42 ;14:     return true;


                      43 

00000008 e3a00001     44 	mov	r0,1

0000000c e12fff1e*    45 	ret	

                      46 	.endf	IEDTimeStampDA_calcReadLen

                      47 	.align	4

                      48 

                      49 ;entity	none	param

                      50 ;pLen	r1	param


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4m01.s
                      51 

                      52 	.section ".bss","awb"

                      53 .L30:

                      54 	.data

                      55 	.text

                      56 

                      57 ;15: }


                      58 

                      59 ;16: 


                      60 ;17: bool IEDTimeStampDA_encodeRead(IEDEntity entity, BufferView *outBufView)


                      61 	.align	4

                      62 	.align	4

                      63 IEDTimeStampDA_encodeRead::

00000010 e92d4010     64 	stmfd	[sp]!,{r4,lr}

00000014 e24dd004     65 	sub	sp,sp,4

                      66 ;18: {


                      67 

                      68 ;19:     int dataLen;


                      69 ;20:     uint8_t *writeBuf;


                      70 ;21:     uint64_t timeStamp;


                      71 ;22: 


                      72 ;23:     TimeStamp* pTimeStamp = entity->extInfo;


                      73 

00000018 e5900058     74 	ldr	r0,[r0,88]

                      75 ;24:     timeStamp = pTimeStamp->timeStamp;


                      76 

0000001c e1a04001     77 	mov	r4,r1

00000020 e8900006     78 	ldmfd	[r0],{r1-r2}

                      79 ;25: 


                      80 ;26:     //Если значения времени в самом элементе нет, берём напрямую из DataSlice


                      81 ;27:     if(timeStamp == 0)


                      82 

00000024 e1910002     83 	orrs	r0,r1,r2

00000028 1a000002     84 	bne	.L39

                      85 ;28:     {


                      86 

                      87 ;29:         timeStamp= dataSliceGetTimeStamp();


                      88 

0000002c eb000000*    89 	bl	dataSliceGetTimeStamp

00000030 e1a02001     90 	mov	r2,r1

00000034 e1a01000     91 	mov	r1,r0

                      92 .L39:

                      93 ;30:     }


                      94 ;31: 


                      95 ;32:     writeBuf = outBufView->p + outBufView->pos;


                      96 

00000038 e8941008     97 	ldmfd	[r4],{r3,r12}

0000003c e3a00000     98 	mov	r0,0

00000040 e08c3003     99 	add	r3,r12,r3

                     100 ;33: 


                     101 ;34:     dataLen = MMSData_encodeTimeStamp(


                     102 

00000044 e58d0000    103 	str	r0,[sp]

00000048 e3a00091    104 	mov	r0,145

0000004c eb000000*   105 	bl	MMSData_encodeTimeStamp

00000050 e2501000    106 	subs	r1,r0,0

                     107 ;35:         IEC61850_BER_TIMESTAMP, timeStamp,writeBuf,0);


                     108 ;36:     if(dataLen <=0)


                     109 

00000054 da000004    110 	ble	.L45

                     111 ;37:     {



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4m01.s
                     112 

                     113 ;38:         ERROR_REPORT("Invalid read length");


                     114 ;39:         return false;


                     115 

                     116 ;40:     }


                     117 ;41: 


                     118 ;42:     if(!BufferView_advance(outBufView, dataLen))


                     119 

00000058 e1a00004    120 	mov	r0,r4

0000005c eb000000*   121 	bl	BufferView_advance

00000060 e3500000    122 	cmp	r0,0

                     123 ;46:     }


                     124 ;47:     return true;


                     125 

00000064 13a00001    126 	movne	r0,1

00000068 1a000000    127 	bne	.L37

                     128 .L45:

                     129 ;43:     {


                     130 

                     131 ;44:         ERROR_REPORT("Buffer overflow");


                     132 ;45:         return false;


                     133 

0000006c e3a00000    134 	mov	r0,0

                     135 .L37:

00000070 e28dd004    136 	add	sp,sp,4

00000074 e8bd8010    137 	ldmfd	[sp]!,{r4,pc}

                     138 	.endf	IEDTimeStampDA_encodeRead

                     139 	.align	4

                     140 ;dataLen	r1	local

                     141 ;writeBuf	r0	local

                     142 ;timeStamp	r1	local

                     143 ;pTimeStamp	r0	local

                     144 

                     145 ;entity	r0	param

                     146 ;outBufView	r4	param

                     147 

                     148 	.section ".bss","awb"

                     149 .L106:

                     150 	.data

                     151 	.text

                     152 

                     153 ;48: }


                     154 

                     155 ;49: 


                     156 ;50: MmsDataAccessError IEDTimeStampDA_write(IEDEntity entity, BufferView *inBufView)


                     157 	.align	4

                     158 	.align	4

                     159 IEDTimeStampDA_write::

00000078 e92d48f0    160 	stmfd	[sp]!,{r4-r7,fp,lr}

                     161 ;51: {


                     162 

0000007c e24dd010    163 	sub	sp,sp,16

00000080 e28d500f    164 	add	r5,sp,15

                     165 ;57:     // Указатель на входные данные


                     166 ;58:     uint8_t *pInData;


                     167 ;59:     TimeStamp* pTimeStamp = entity->extInfo;


                     168 

00000084 e28d2004    169 	add	r2,sp,4

00000088 e1a06001    170 	mov	r6,r1

0000008c e3a01000    171 	mov	r1,0

00000090 e58d100c    172 	str	r1,[sp,12]


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4m01.s
                     173 ;52:     uint8_t tag;


                     174 ;53:     size_t len;


                     175 ;54:     uint64_t timeStamp = 0;


                     176 

                     177 ;55:     // Указатель на старший байт


                     178 ;56:     uint8_t *pValue = ((uint8_t*)&timeStamp) + 7;


                     179 

00000094 e58d1008    180 	str	r1,[sp,8]

00000098 e28d1003    181 	add	r1,sp,3

0000009c e590b058    182 	ldr	fp,[r0,88]

                     183 ;60:     size_t i;


                     184 ;61: 


                     185 ;62:     if(!BufferView_decodeTL(inBufView, &tag, &len, NULL))


                     186 

000000a0 e1a00006    187 	mov	r0,r6

000000a4 e3a03000    188 	mov	r3,0

000000a8 eb000000*   189 	bl	BufferView_decodeTL

000000ac e3500000    190 	cmp	r0,0

000000b0 0a00000b    191 	beq	.L139

                     192 ;63:     {


                     193 

                     194 ;64:         return DATA_ACCESS_ERROR_UNKNOWN;


                     195 

                     196 ;65:     }


                     197 ;66:     if(tag != IEC61850_BER_TIMESTAMP || len != 8)


                     198 

000000b4 e5dd0003    199 	ldrb	r0,[sp,3]

000000b8 e3500091    200 	cmp	r0,145

000000bc 059d0004    201 	ldreq	r0,[sp,4]

000000c0 03500008    202 	cmpeq	r0,8

                     203 ;67:     {


                     204 

                     205 ;68:         return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;


                     206 

000000c4 13a00007    207 	movne	r0,7

000000c8 1a00001b    208 	bne	.L129

                     209 ;69:     }


                     210 ;70: 


                     211 ;71:     pInData = inBufView->p + inBufView->pos;


                     212 

000000cc e8960011    213 	ldmfd	[r6],{r0,r4}

000000d0 e1a07000    214 	mov	r7,r0

                     215 ;72:     if(!BufferView_advance(inBufView, len))


                     216 

000000d4 e1a00006    217 	mov	r0,r6

000000d8 e3a01008    218 	mov	r1,8

000000dc eb000000*   219 	bl	BufferView_advance

000000e0 e3500000    220 	cmp	r0,0

                     221 .L139:

                     222 ;73:     {


                     223 

                     224 ;74:         return DATA_ACCESS_ERROR_UNKNOWN;


                     225 

000000e4 03a0000c    226 	moveq	r0,12

000000e8 0a000013    227 	beq	.L129

                     228 .L141:

000000ec e7f40007    229 	ldrb	r0,[r4,r7]!

000000f0 e5c50000    230 	strb	r0,[r5]

000000f4 e5d40001    231 	ldrb	r0,[r4,1]

000000f8 e5450001    232 	strb	r0,[r5,-1]

000000fc e5d40002    233 	ldrb	r0,[r4,2]


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_4m01.s
00000100 e5450002    234 	strb	r0,[r5,-2]

00000104 e5d40003    235 	ldrb	r0,[r4,3]

00000108 e5450003    236 	strb	r0,[r5,-3]

0000010c e5d40004    237 	ldrb	r0,[r4,4]

00000110 e5450004    238 	strb	r0,[r5,-4]

00000114 e5d40005    239 	ldrb	r0,[r4,5]

00000118 e5450005    240 	strb	r0,[r5,-5]

0000011c e5d40006    241 	ldrb	r0,[r4,6]

00000120 e5450006    242 	strb	r0,[r5,-6]

00000124 e5d40007    243 	ldrb	r0,[r4,7]

00000128 e5450007    244 	strb	r0,[r5,-7]

                     245 ;75:     }


                     246 ;76: 


                     247 ;77:     // Разворачиваем для little endian


                     248 ;78:     for(i = 0; i < 8; i++)


                     249 

                     250 ;81:     }


                     251 ;82:     pTimeStamp->timeStamp = timeStamp;


                     252 

0000012c e59d100c    253 	ldr	r1,[sp,12]

00000130 e59d0008    254 	ldr	r0,[sp,8]

00000134 e88b0003    255 	stmea	[fp],{r0-r1}

                     256 ;83:     return DATA_ACCESS_ERROR_SUCCESS;


                     257 

00000138 e3e00000    258 	mvn	r0,0

                     259 .L129:

0000013c e28dd010    260 	add	sp,sp,16

00000140 e8bd88f0    261 	ldmfd	[sp]!,{r4-r7,fp,pc}

                     262 	.endf	IEDTimeStampDA_write

                     263 	.align	4

                     264 ;tag	[sp,3]	local

                     265 ;len	[sp,4]	local

                     266 ;timeStamp	[sp,8]	local

                     267 ;pValue	r5	local

                     268 ;pInData	r4	local

                     269 ;pTimeStamp	fp	local

                     270 

                     271 ;entity	r0	param

                     272 ;inBufView	r6	param

                     273 

                     274 	.section ".bss","awb"

                     275 .L286:

                     276 	.data

                     277 	.text

                     278 

                     279 ;84: }


                     280 	.align	4

                     281 

                     282 	.data

                     283 	.ghsnote version,6

                     284 	.ghsnote tools,3

                     285 	.ghsnote options,0

                     286 	.text

                     287 	.align	4

