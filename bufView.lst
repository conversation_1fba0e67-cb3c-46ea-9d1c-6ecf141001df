                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8dk1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=bufView.c -o gh_8dk1.o -list=bufView.lst C:\Users\<USER>\AppData\Local\Temp\gh_8dk1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_8dk1.s
Source File: bufView.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile bufView.c -o

                      11 ;		bufView.o

                      12 ;Source File:   bufView.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:56 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "bufView.h"


                      23 ;2: #include "stringView.h"


                      24 ;3: #include <string.h>


                      25 ;4: #include <stddef.h>


                      26 ;5: 


                      27 ;6: void BufferView_init(BufferView* bv, uint8_t* buf, size_t len, size_t pos)


                      28 	.text

                      29 	.align	4

                      30 BufferView_init::

                      31 ;7: {


                      32 

                      33 ;8:     bv->p = buf;


                      34 

00000000 e1a0c002     35 	mov	r12,r2

00000004 e880100a     36 	stmea	[r0],{r1,r3,r12}

                      37 ;9:     bv->len = len;


                      38 

                      39 ;10:     bv->pos = pos;


                      40 

00000008 e12fff1e*    41 	ret	

                      42 	.endf	BufferView_init

                      43 	.align	4

                      44 

                      45 ;bv	r0	param

                      46 ;buf	r1	param

                      47 ;len	r2	param

                      48 ;pos	r3	param

                      49 

                      50 	.section ".bss","awb"


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8dk1.s
                      51 .L30:

                      52 	.data

                      53 	.text

                      54 

                      55 ;11: }


                      56 

                      57 ;12: 


                      58 ;13: bool BufferView_alloc(BufferView* bv, size_t byteCount, uint8_t** pFreeSpace)


                      59 	.align	4

                      60 	.align	4

                      61 BufferView_alloc::

                      62 ;14: {


                      63 

                      64 ;15:     size_t freeSpace = bv->len - bv->pos;


                      65 

0000000c e9901008     66 	ldmed	[r0],{r3,r12}

00000010 e04cc003     67 	sub	r12,r12,r3

                      68 ;16:     if (freeSpace < byteCount)


                      69 

00000014 e15c0001     70 	cmp	r12,r1

                      71 ;17:     {


                      72 

                      73 ;18:         return false;


                      74 

00000018 33a00000     75 	movlo	r0,0

                      76 ;19:     }


                      77 ;20:     *pFreeSpace = bv->p + bv->pos;


                      78 

0000001c 25900000     79 	ldrhs	r0,[r0]

00000020 20830000     80 	addhs	r0,r3,r0

00000024 25820000     81 	strhs	r0,[r2]

                      82 ;21:     return true;


                      83 

00000028 23a00001     84 	movhs	r0,1

0000002c e12fff1e*    85 	ret	

                      86 	.endf	BufferView_alloc

                      87 	.align	4

                      88 ;freeSpace	r12	local

                      89 

                      90 ;bv	r0	param

                      91 ;byteCount	r1	param

                      92 ;pFreeSpace	r2	param

                      93 

                      94 	.section ".bss","awb"

                      95 .L70:

                      96 	.data

                      97 	.text

                      98 

                      99 ;22: }


                     100 

                     101 ;23: 


                     102 ;24: 


                     103 ;25: bool BufferView_writeStringView(BufferView* bv, const StringView* strView)


                     104 	.align	4

                     105 	.align	4

                     106 BufferView_writeStringView::

00000030 e92d4030    107 	stmfd	[sp]!,{r4-r5,lr}

00000034 e1a05001    108 	mov	r5,r1

                     109 ;26: {


                     110 

                     111 ;27:     if (strView->len == 0)



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8dk1.s
                     112 

00000038 e5952000    113 	ldr	r2,[r5]

0000003c e3520000    114 	cmp	r2,0

                     115 ;28:     {


                     116 

                     117 ;29:         return true;


                     118 

00000040 03a00001    119 	moveq	r0,1

00000044 0a00000e    120 	beq	.L83

00000048 e1a04000    121 	mov	r4,r0

                     122 ;30:     }


                     123 ;31:     if (strView->len > bv->len - bv->pos)


                     124 

0000004c e9940003    125 	ldmed	[r4],{r0-r1}

00000050 e0411000    126 	sub	r1,r1,r0

00000054 e1520001    127 	cmp	r2,r1

                     128 ;32:     {


                     129 

                     130 ;33:         return false;


                     131 

00000058 83a00000    132 	movhi	r0,0

0000005c 8a000008    133 	bhi	.L83

                     134 ;34:     }


                     135 ;35:     memcpy(bv->p + bv->pos, strView->p, strView->len);


                     136 

00000060 e5943000    137 	ldr	r3,[r4]

00000064 e5951004    138 	ldr	r1,[r5,4]

00000068 e0800003    139 	add	r0,r0,r3

0000006c eb000000*   140 	bl	memcpy

                     141 ;36:     bv->pos += strView->len;


                     142 

00000070 e5951000    143 	ldr	r1,[r5]

00000074 e5940004    144 	ldr	r0,[r4,4]

00000078 e0800001    145 	add	r0,r0,r1

0000007c e5840004    146 	str	r0,[r4,4]

                     147 ;37:     return true;


                     148 

00000080 e3a00001    149 	mov	r0,1

                     150 .L83:

00000084 e8bd8030    151 	ldmfd	[sp]!,{r4-r5,pc}

                     152 	.endf	BufferView_writeStringView

                     153 	.align	4

                     154 

                     155 ;bv	r4	param

                     156 ;strView	r5	param

                     157 

                     158 	.section ".bss","awb"

                     159 .L126:

                     160 	.data

                     161 	.text

                     162 

                     163 ;38: }


                     164 

                     165 ;39: 


                     166 ;40: bool BufferView_writeStr(BufferView* bv, const char* str)


                     167 	.align	4

                     168 	.align	4

                     169 BufferView_writeStr::

00000088 e92d4010    170 	stmfd	[sp]!,{r4,lr}

0000008c e24dd008    171 	sub	sp,sp,8

00000090 e1a04000    172 	mov	r4,r0


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8dk1.s
                     173 ;41: {


                     174 

                     175 ;42:     StringView strView;


                     176 ;43:     StringView_fromCStr(&strView, (char*)str);


                     177 

00000094 e1a0000d    178 	mov	r0,sp

00000098 eb000000*   179 	bl	StringView_fromCStr

                     180 ;44:     return BufferView_writeStringView(bv, &strView);


                     181 

0000009c e1a0100d    182 	mov	r1,sp

000000a0 e1a00004    183 	mov	r0,r4

000000a4 ebffffe1*   184 	bl	BufferView_writeStringView

000000a8 e28dd008    185 	add	sp,sp,8

000000ac e8bd8010    186 	ldmfd	[sp]!,{r4,pc}

                     187 	.endf	BufferView_writeStr

                     188 	.align	4

                     189 ;strView	[sp]	local

                     190 

                     191 ;bv	r4	param

                     192 ;str	none	param

                     193 

                     194 	.section ".bss","awb"

                     195 .L177:

                     196 	.data

                     197 	.text

                     198 

                     199 ;45: }


                     200 

                     201 ;46: 


                     202 ;47: bool BufferView_writeUshortBE(BufferView* bv, uint16_t data)


                     203 	.align	4

                     204 	.align	4

                     205 BufferView_writeUshortBE::

000000b0 e92d0100    206 	stmfd	[sp]!,{r8}

                     207 ;48: {


                     208 

                     209 ;49:     if (bv->pos + sizeof(uint16_t) > bv->len)


                     210 

000000b4 e990000c    211 	ldmed	[r0],{r2-r3}

000000b8 e1a08002    212 	mov	r8,r2

000000bc e2882002    213 	add	r2,r8,2

000000c0 e1520003    214 	cmp	r2,r3

                     215 ;50:     {


                     216 

                     217 ;51:         return false;


                     218 

000000c4 83a00000    219 	movhi	r0,0

000000c8 8a000009    220 	bhi	.L184

                     221 ;52:     }


                     222 ;53:     bv->p[bv->pos++] = data >> 8;


                     223 

000000cc e5903000    224 	ldr	r3,[r0]

000000d0 e288c001    225 	add	r12,r8,1

000000d4 e580c004    226 	str	r12,[r0,4]

000000d8 e1a0c441    227 	mov	r12,r1 asr 8

000000dc e7c3c008    228 	strb	r12,[r3,r8]

                     229 ;54:     bv->p[bv->pos++] = data & 0xFF;


                     230 

000000e0 e8900108    231 	ldmfd	[r0],{r3,r8}

000000e4 e288c001    232 	add	r12,r8,1

000000e8 e580c004    233 	str	r12,[r0,4]


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8dk1.s
000000ec e7c31008    234 	strb	r1,[r3,r8]

                     235 ;55:     return true;


                     236 

000000f0 e3a00001    237 	mov	r0,1

                     238 .L184:

000000f4 e8bd0100    239 	ldmfd	[sp]!,{r8}

000000f8 e12fff1e*   240 	ret	

                     241 	.endf	BufferView_writeUshortBE

                     242 	.align	4

                     243 

                     244 ;bv	r0	param

                     245 ;data	r1	param

                     246 

                     247 	.section ".bss","awb"

                     248 .L214:

                     249 	.data

                     250 	.text

                     251 

                     252 ;56: }


                     253 

                     254 ;57: 


                     255 ;58: size_t BufferView_writeData(BufferView* bv, void* data, size_t byteCount)


                     256 	.align	4

                     257 	.align	4

                     258 BufferView_writeData::

000000fc e92d4030    259 	stmfd	[sp]!,{r4-r5,lr}

00000100 e1a05000    260 	mov	r5,r0

                     261 ;59: {


                     262 

                     263 ;60:     size_t freeSpace = bv->len - bv->pos;


                     264 

00000104 e9950018    265 	ldmed	[r5],{r3-r4}

00000108 e0544003    266 	subs	r4,r4,r3

                     267 ;61:     if (freeSpace == 0)


                     268 

                     269 ;62:     {


                     270 

                     271 ;63:         return 0;


                     272 

0000010c 0a000008    273 	beq	.L228

00000110 e1540002    274 	cmp	r4,r2

00000114 81a04002    275 	movhi	r4,r2

                     276 ;64:     }


                     277 ;65:     if (freeSpace < byteCount)


                     278 

                     279 

                     280 

                     281 ;68:     }


                     282 ;69:     memcpy(bv->p + bv->pos, data, byteCount);


                     283 

00000118 e595c000    284 	ldr	r12,[r5]

0000011c e1a02004    285 	mov	r2,r4

00000120 e083000c    286 	add	r0,r3,r12

00000124 eb000000*   287 	bl	memcpy

                     288 ;70:     bv->pos += byteCount;


                     289 

00000128 e5951004    290 	ldr	r1,[r5,4]

0000012c e0811004    291 	add	r1,r1,r4

00000130 e5851004    292 	str	r1,[r5,4]

                     293 ;71:     return byteCount;


                     294 


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8dk1.s
                     295 .L228:

00000134 e1a00004    296 	mov	r0,r4

00000138 e8bd8030    297 	ldmfd	[sp]!,{r4-r5,pc}

                     298 	.endf	BufferView_writeData

                     299 	.align	4

                     300 ;freeSpace	r4	local

                     301 

                     302 ;bv	r5	param

                     303 ;data	r1	param

                     304 ;byteCount	r2	param

                     305 

                     306 	.section ".bss","awb"

                     307 .L289:

                     308 	.data

                     309 	.text

                     310 

                     311 ;72: }


                     312 

                     313 ;73: 


                     314 ;74: bool BufferView_readStringView(BufferView* bv, size_t strLen,


                     315 	.align	4

                     316 	.align	4

                     317 BufferView_readStringView::

0000013c e92d4030    318 	stmfd	[sp]!,{r4-r5,lr}

00000140 e1a04000    319 	mov	r4,r0

00000144 e1a05001    320 	mov	r5,r1

00000148 e1a03002    321 	mov	r3,r2

                     322 ;75:     StringView* result)


                     323 ;76: {


                     324 

                     325 ;77:     if (bv->pos + strLen > bv->len)


                     326 

0000014c e9940005    327 	ldmed	[r4],{r0,r2}

00000150 e0801005    328 	add	r1,r0,r5

00000154 e1510002    329 	cmp	r1,r2

                     330 ;78:     {


                     331 

                     332 ;79:         return false;


                     333 

00000158 83a00000    334 	movhi	r0,0

0000015c 8a000008    335 	bhi	.L303

                     336 ;80:     }


                     337 ;81:     StringView_init(result, (const char*)bv->p + bv->pos, strLen);


                     338 

00000160 e5941000    339 	ldr	r1,[r4]

00000164 e1a02005    340 	mov	r2,r5

00000168 e0801001    341 	add	r1,r0,r1

0000016c e1a00003    342 	mov	r0,r3

00000170 eb000000*   343 	bl	StringView_init

                     344 ;82:     bv->pos += strLen;


                     345 

00000174 e5940004    346 	ldr	r0,[r4,4]

00000178 e0800005    347 	add	r0,r0,r5

0000017c e5840004    348 	str	r0,[r4,4]

                     349 ;83:     return true;


                     350 

00000180 e3a00001    351 	mov	r0,1

                     352 .L303:

00000184 e8bd8030    353 	ldmfd	[sp]!,{r4-r5,pc}

                     354 	.endf	BufferView_readStringView

                     355 	.align	4


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8dk1.s
                     356 

                     357 ;bv	r4	param

                     358 ;strLen	r5	param

                     359 ;result	r3	param

                     360 

                     361 	.section ".bss","awb"

                     362 .L342:

                     363 	.data

                     364 	.text

                     365 

                     366 ;84: }


                     367 

                     368 ;85: 


                     369 ;86: bool BufferView_advance(BufferView* bv, size_t len)


                     370 	.align	4

                     371 	.align	4

                     372 BufferView_advance::

                     373 ;87: {


                     374 

                     375 ;88:     if (bv->pos + len > bv->len)


                     376 

00000188 e990000c    377 	ldmed	[r0],{r2-r3}

0000018c e0811002    378 	add	r1,r1,r2

00000190 e1510003    379 	cmp	r1,r3

                     380 ;89:     {


                     381 

                     382 ;90:         return false;


                     383 

00000194 83a00000    384 	movhi	r0,0

                     385 ;91:     }


                     386 ;92:     bv->pos += len;


                     387 

00000198 95801004    388 	strls	r1,[r0,4]

                     389 ;93:     return true;


                     390 

0000019c 93a00001    391 	movls	r0,1

000001a0 e12fff1e*   392 	ret	

                     393 	.endf	BufferView_advance

                     394 	.align	4

                     395 

                     396 ;bv	r0	param

                     397 ;len	r1	param

                     398 

                     399 	.section ".bss","awb"

                     400 .L390:

                     401 	.data

                     402 	.text

                     403 

                     404 ;94: }


                     405 	.align	4

                     406 

                     407 	.data

                     408 	.ghsnote version,6

                     409 	.ghsnote tools,3

                     410 	.ghsnote options,0

                     411 	.text

                     412 	.align	4

