                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=DataSet.c -o iedTree\gh_6u41.o -list=iedTree/DataSet.lst C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
Source File: DataSet.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		iedTree/DataSet.c -o iedTree/DataSet.o

                      12 ;Source File:   iedTree/DataSet.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:52 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "DataSet.h"


                      23 ;2: 


                      24 ;3: #include "../stringView.h"


                      25 ;4: #include "iedEntity.h"


                      26 ;5: #include "iedTree.h"


                      27 ;6: #include "../bufViewBER.h"


                      28 ;7: #include "../AsnEncoding.h"


                      29 ;8: 


                      30 ;9: #include <debug.h>


                      31 ;10: 


                      32 ;11: 


                      33 ;12: bool DataSet_init(IEDEntity entity)


                      34 	.text

                      35 	.align	4

                      36 DataSet_init::

00000000 e92d4070     37 	stmfd	[sp]!,{r4-r6,lr}

                      38 ;13: {


                      39 

                      40 ;14:     uint8_t tag;


                      41 ;15:     BufferView dataSetBER;


                      42 ;16:     DataSet* dataSet;


                      43 ;17:     DataSetItem** pNextItem;


                      44 ;18:     entity->type = IED_ENTITY_DATASET;


                      45 

00000004 e24dd020     46 	sub	sp,sp,32

00000008 e1a04000     47 	mov	r4,r0

0000000c e3a0000b     48 	mov	r0,11

00000010 e5840050     49 	str	r0,[r4,80]

                      50 ;19:     dataSet = IEDEntity_alloc(sizeof(DataSet));



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
                      51 

00000014 e3a00008     52 	mov	r0,8

00000018 eb000000*    53 	bl	IEDEntity_alloc

0000001c e1b05000     54 	movs	r5,r0

                      55 ;20:     if(dataSet == NULL)


                      56 

00000020 0a000017     57 	beq	.L33

                      58 ;21:     {


                      59 

                      60 ;22:         return false;


                      61 

                      62 ;23:     }


                      63 ;24:     entity->extInfo = dataSet;


                      64 

00000024 e5845058     65 	str	r5,[r4,88]

                      66 ;25: 


                      67 ;26:     dataSetBER = entity->ber;


                      68 

00000028 e28d3014     69 	add	r3,sp,20

0000002c e2840014     70 	add	r0,r4,20

00000030 e8900007     71 	ldmfd	[r0],{r0-r2}

00000034 e8830007     72 	stmea	[r3],{r0-r2}

                      73 ;27: 


                      74 ;28:     //Пропускаем тэг и длину


                      75 ;29:     if(!BufferView_decodeTL(&dataSetBER, NULL, NULL, NULL))


                      76 

00000038 e1a00003     77 	mov	r0,r3

0000003c e3a03000     78 	mov	r3,0

00000040 e1a02003     79 	mov	r2,r3

00000044 e1a01003     80 	mov	r1,r3

00000048 eb000000*    81 	bl	BufferView_decodeTL

0000004c e3500000     82 	cmp	r0,0

00000050 0a00000b     83 	beq	.L33

                      84 ;30:     {


                      85 

                      86 ;31:         ERROR_REPORT("DataSet init error");


                      87 ;32:         return false;


                      88 

                      89 ;33:     }


                      90 ;34: 


                      91 ;35:     //Пропускаем имя


                      92 ;36:     if(!BufferView_skipObject(&dataSetBER, ASN_VISIBLE_STRING, true))


                      93 

00000054 e28d0014     94 	add	r0,sp,20

00000058 e3a02001     95 	mov	r2,1

0000005c e3a0101a     96 	mov	r1,26

00000060 eb000000*    97 	bl	BufferView_skipObject

00000064 e3500000     98 	cmp	r0,0

00000068 0a000005     99 	beq	.L33

                     100 ;37:     {


                     101 

                     102 ;38:         ERROR_REPORT("DataSet init error");


                     103 ;39:         return false;


                     104 

                     105 ;40:     }


                     106 ;41: 


                     107 ;42:     //Пропускаем описание


                     108 ;43:     if(!BufferView_skipObject(&dataSetBER, ASN_OCTET_STRING, true))


                     109 

0000006c e28d0014    110 	add	r0,sp,20

00000070 e3a02001    111 	mov	r2,1


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
00000074 e3a01004    112 	mov	r1,4

00000078 eb000000*   113 	bl	BufferView_skipObject

0000007c e3500000    114 	cmp	r0,0

00000080 1a000001    115 	bne	.L32

                     116 .L33:

                     117 ;44:     {


                     118 

                     119 ;45:         ERROR_REPORT("DataSet init error");


                     120 ;46:         return false;


                     121 

00000084 e3a00000    122 	mov	r0,0

00000088 ea000037    123 	b	.L21

                     124 .L32:

                     125 ;47:     }


                     126 ;48: 


                     127 ;49:     pNextItem = &dataSet->firstItem;


                     128 

0000008c e1a06005    129 	mov	r6,r5

                     130 ;50: 


                     131 ;51:     //Получаем список элементов


                     132 ;52:     while(!BufferView_endOfBuf(&dataSetBER))


                     133 

00000090 e59d201c    134 	ldr	r2,[sp,28]

00000094 e59d1018    135 	ldr	r1,[sp,24]

00000098 e28d4014    136 	add	r4,sp,20

0000009c e1510002    137 	cmp	r1,r2

000000a0 0a000030    138 	beq	.L36

                     139 .L37:

                     140 ;53:     {


                     141 

                     142 ;54:         StringView ldName;


                     143 ;55:         StringView objName;        


                     144 ;56:         DataSetItem* dataSetItem;


                     145 ;57: 


                     146 ;58:         //Читаем TL


                     147 ;59:         if(!BufferView_decodeTL(&dataSetBER, &tag, NULL, NULL)


                     148 

000000a4 e28d1003    149 	add	r1,sp,3

000000a8 e1a00004    150 	mov	r0,r4

000000ac e3a03000    151 	mov	r3,0

000000b0 e1a02003    152 	mov	r2,r3

000000b4 eb000000*   153 	bl	BufferView_decodeTL

000000b8 e3500000    154 	cmp	r0,0

000000bc 0afffff0    155 	beq	.L33

000000c0 e5dd1003    156 	ldrb	r1,[sp,3]

000000c4 e3510030    157 	cmp	r1,48

000000c8 1affffed    158 	bne	.L33

                     159 ;60:                 || tag != ASN_SEQUENCE)


                     160 ;61:         {


                     161 

                     162 ;62:             return false;


                     163 

                     164 ;63:         }


                     165 ;64:         //Читаем имя переменной


                     166 ;65:         if(!BufferView_decodeStringViewTL(&dataSetBER, ASN_VISIBLE_STRING,


                     167 

000000cc e28d200c    168 	add	r2,sp,12

000000d0 e1a00004    169 	mov	r0,r4

000000d4 e3a0101a    170 	mov	r1,26

000000d8 eb000000*   171 	bl	BufferView_decodeStringViewTL

000000dc e3500000    172 	cmp	r0,0


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
000000e0 0affffe7    173 	beq	.L33

000000e4 e28d2004    174 	add	r2,sp,4

000000e8 e1a00004    175 	mov	r0,r4

000000ec e3a0101a    176 	mov	r1,26

000000f0 eb000000*   177 	bl	BufferView_decodeStringViewTL

000000f4 e3500000    178 	cmp	r0,0

000000f8 0affffe1    179 	beq	.L33

                     180 ;66:                                           &ldName)


                     181 ;67:              || !BufferView_decodeStringViewTL(&dataSetBER, ASN_VISIBLE_STRING,


                     182 ;68:                                                &objName))


                     183 ;69:         {


                     184 

                     185 ;70:             return false;


                     186 

                     187 ;71:         }


                     188 ;72:         //Пропускаем позицию


                     189 ;73:         if(!BufferView_skipObject(&dataSetBER, ASN_INTEGER, true))


                     190 

000000fc e1a00004    191 	mov	r0,r4

00000100 e3a02001    192 	mov	r2,1

00000104 e3a01002    193 	mov	r1,2

00000108 eb000000*   194 	bl	BufferView_skipObject

0000010c e3500000    195 	cmp	r0,0

00000110 0affffdb    196 	beq	.L33

                     197 ;74:         {


                     198 

                     199 ;75:             return false;


                     200 

                     201 ;76:         }


                     202 ;77: 


                     203 ;78:         dataSetItem = IEDEntity_alloc(sizeof(DataSetItem));


                     204 

00000114 e3a00018    205 	mov	r0,24

00000118 eb000000*   206 	bl	IEDEntity_alloc

                     207 ;79:         if(dataSetItem == NULL)


                     208 

0000011c e3500000    209 	cmp	r0,0

00000120 0affffd7    210 	beq	.L33

                     211 ;80:         {


                     212 

                     213 ;81:             ERROR_REPORT("DataSet item alloc error");


                     214 ;82:             return false;


                     215 

                     216 ;83:         }


                     217 ;84: 


                     218 ;85:         dataSetItem->domainID = ldName;


                     219 

00000124 e59d300c    220 	ldr	r3,[sp,12]

00000128 e5803004    221 	str	r3,[r0,4]

0000012c e59d1010    222 	ldr	r1,[sp,16]

00000130 e5801008    223 	str	r1,[r0,8]

                     224 ;86:         dataSetItem->itemID = objName;        		


                     225 

00000134 e59d3004    226 	ldr	r3,[sp,4]

00000138 e580300c    227 	str	r3,[r0,12]

0000013c e59d1008    228 	ldr	r1,[sp,8]

00000140 e5801010    229 	str	r1,[r0,16]

                     230 ;87: 


                     231 ;88:         *pNextItem = dataSetItem;


                     232 

00000144 e5951004    233 	ldr	r1,[r5,4]


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
00000148 e5860000    234 	str	r0,[r6]

                     235 ;89:         pNextItem = &dataSetItem->next;


                     236 

0000014c e2811001    237 	add	r1,r1,1

00000150 e5851004    238 	str	r1,[r5,4]

00000154 e59d201c    239 	ldr	r2,[sp,28]

00000158 e59d1018    240 	ldr	r1,[sp,24]

0000015c e1a06000    241 	mov	r6,r0

                     242 ;90: 


                     243 ;91: 		dataSet->itemCount++;


                     244 

00000160 e1510002    245 	cmp	r1,r2

00000164 1affffce    246 	bne	.L37

                     247 .L36:

                     248 ;92:     }


                     249 ;93:     return true;


                     250 

00000168 e3a00001    251 	mov	r0,1

                     252 .L21:

0000016c e28dd020    253 	add	sp,sp,32

00000170 e8bd8070    254 	ldmfd	[sp]!,{r4-r6,pc}

                     255 	.endf	DataSet_init

                     256 	.align	4

                     257 ;tag	[sp,3]	local

                     258 ;dataSetBER	[sp,20]	local

                     259 ;dataSet	r5	local

                     260 ;pNextItem	r6	local

                     261 ;ldName	[sp,12]	local

                     262 ;objName	[sp,4]	local

                     263 ;dataSetItem	r0	local

                     264 

                     265 ;entity	r4	param

                     266 

                     267 	.section ".bss","awb"

                     268 .L202:

                     269 	.data

                     270 	.text

                     271 

                     272 ;94: }


                     273 

                     274 ;95: 


                     275 ;96: bool DataSet_postCreate(IEDEntity entity)


                     276 	.align	4

                     277 	.align	4

                     278 DataSet_postCreate::

00000174 e92d4010    279 	stmfd	[sp]!,{r4,lr}

                     280 ;97: {


                     281 

                     282 ;98:     DataSetItem* dataSetItem;


                     283 ;99:     DataSet* dataSet = entity->extInfo;


                     284 

00000178 e5900058    285 	ldr	r0,[r0,88]

                     286 ;100: 


                     287 ;101:     if(dataSet == NULL)


                     288 

0000017c e3500000    289 	cmp	r0,0

                     290 .L239:

                     291 ;102:     {


                     292 

                     293 ;103:         return false;


                     294 


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
00000180 03a00000    295 	moveq	r0,0

00000184 0a00000c    296 	beq	.L236

                     297 .L238:

                     298 ;104:     }


                     299 ;105: 


                     300 ;106:     dataSetItem = dataSet->firstItem;


                     301 

00000188 e5904000    302 	ldr	r4,[r0]

                     303 ;107: 


                     304 ;108:     while(dataSetItem != NULL)


                     305 

0000018c e3540000    306 	cmp	r4,0

00000190 0a000008    307 	beq	.L242

                     308 .L243:

                     309 ;109:     {


                     310 

                     311 ;110:         IEDEntity obj;


                     312 ;111:         obj = IEDTree_findDataByFullName(


                     313 

00000194 e284100c    314 	add	r1,r4,12

00000198 e2840004    315 	add	r0,r4,4

0000019c eb000000*   316 	bl	IEDTree_findDataByFullName

                     317 ;112:                     &dataSetItem->domainID, &dataSetItem->itemID);


                     318 ;113:         if(obj == NULL)


                     319 

000001a0 e3500000    320 	cmp	r0,0

000001a4 0afffff5    321 	beq	.L239

                     322 ;114:         {


                     323 

                     324 ;115:             ERROR_REPORT("DataSet item is not found");


                     325 ;116:             return false;


                     326 

                     327 ;117:         }


                     328 ;118:         dataSetItem->obj = obj;


                     329 

000001a8 e5840014    330 	str	r0,[r4,20]

                     331 ;119: 


                     332 ;120:         dataSetItem = dataSetItem->next;


                     333 

000001ac e5944000    334 	ldr	r4,[r4]

000001b0 e3540000    335 	cmp	r4,0

000001b4 1afffff6    336 	bne	.L243

                     337 .L242:

                     338 ;121:     }


                     339 ;122:     return true;


                     340 

000001b8 e3a00001    341 	mov	r0,1

                     342 .L236:

000001bc e8bd8010    343 	ldmfd	[sp]!,{r4,pc}

                     344 	.endf	DataSet_postCreate

                     345 	.align	4

                     346 ;dataSetItem	r4	local

                     347 ;dataSet	r0	local

                     348 ;obj	r0	local

                     349 

                     350 ;entity	r0	param

                     351 

                     352 	.section ".bss","awb"

                     353 .L318:

                     354 	.data

                     355 	.text


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
                     356 

                     357 ;123: }


                     358 

                     359 ;124: 


                     360 ;125: 


                     361 ;126: DataSet *DataSet_getDataSetObj(IEDEntity entity)


                     362 	.align	4

                     363 	.align	4

                     364 DataSet_getDataSetObj::

                     365 ;127: {


                     366 

                     367 ;128: 	if(entity->type != IED_ENTITY_DATASET)


                     368 

000001c0 e5901050    369 	ldr	r1,[r0,80]

000001c4 e351000b    370 	cmp	r1,11

                     371 ;129: 	{


                     372 

                     373 ;130: 		ERROR_REPORT("Not DataSet");


                     374 ;131: 		return NULL;


                     375 

000001c8 13a00000    376 	movne	r0,0

                     377 ;132: 	}


                     378 ;133: 	return entity->extInfo;


                     379 

000001cc 05900058    380 	ldreq	r0,[r0,88]

000001d0 e12fff1e*   381 	ret	

                     382 	.endf	DataSet_getDataSetObj

                     383 	.align	4

                     384 

                     385 ;entity	r0	param

                     386 

                     387 	.section ".bss","awb"

                     388 .L374:

                     389 	.data

                     390 	.text

                     391 

                     392 ;134: }


                     393 

                     394 ;135: 


                     395 ;136: 


                     396 ;137: DataSetItem *DataSet_getFirstItem(IEDEntity entity)


                     397 	.align	4

                     398 	.align	4

                     399 DataSet_getFirstItem::

                     400 ;138: {


                     401 

                     402 ;139: 	DataSet* dataSet;


                     403 ;140: 


                     404 ;141: 	if(entity->type != IED_ENTITY_DATASET)


                     405 

000001d4 e5901050    406 	ldr	r1,[r0,80]

000001d8 e351000b    407 	cmp	r1,11

                     408 ;142: 	{


                     409 

                     410 ;143: 		ERROR_REPORT("Not DataSet");


                     411 ;144: 		return NULL;


                     412 

000001dc 13a00000    413 	movne	r0,0

                     414 ;145: 	}


                     415 ;146: 


                     416 ;147: 	dataSet =  entity->extInfo;



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
                     417 

000001e0 05900058    418 	ldreq	r0,[r0,88]

                     419 ;148: 


                     420 ;149: 	return dataSet->firstItem;


                     421 

000001e4 05900000    422 	ldreq	r0,[r0]

000001e8 e12fff1e*   423 	ret	

                     424 	.endf	DataSet_getFirstItem

                     425 	.align	4

                     426 ;dataSet	r0	local

                     427 

                     428 ;entity	r0	param

                     429 

                     430 	.section ".bss","awb"

                     431 .L422:

                     432 	.data

                     433 	.text

                     434 

                     435 ;150: }


                     436 

                     437 ;151: 


                     438 ;152: 


                     439 ;153: bool DataSet_calcReadLen(IEDEntity dsEntity, size_t *pLen)


                     440 	.align	4

                     441 	.align	4

                     442 DataSet_calcReadLen::

000001ec e92d4070    443 	stmfd	[sp]!,{r4-r6,lr}

000001f0 e24dd004    444 	sub	sp,sp,4

000001f4 e1a05001    445 	mov	r5,r1

                     446 ;154: {


                     447 

                     448 ;155: 	DataSet* dataSet;


                     449 ;156: 	DataSetItem* dataSetItem;


                     450 ;157: 


                     451 ;158: 	if(dsEntity->type != IED_ENTITY_DATASET)


                     452 

000001f8 e5901050    453 	ldr	r1,[r0,80]

000001fc e351000b    454 	cmp	r1,11

00000200 0a000001    455 	beq	.L437

                     456 .L438:

                     457 ;159: 	{


                     458 

                     459 ;160: 		ERROR_REPORT("Not DataSet");


                     460 ;161: 		return false;


                     461 

00000204 e3a00000    462 	mov	r0,0

00000208 ea000015    463 	b	.L435

                     464 .L437:

                     465 ;162: 	}


                     466 ;163: 


                     467 ;164: 	dataSet =  dsEntity->extInfo;


                     468 

0000020c e5900058    469 	ldr	r0,[r0,88]

                     470 ;165: 	dataSetItem = dataSet->firstItem;


                     471 

00000210 e1a0600d    472 	mov	r6,sp

00000214 e5904000    473 	ldr	r4,[r0]

                     474 ;166: 


                     475 ;167: 	*pLen = 0;


                     476 

00000218 e3a00000    477 	mov	r0,0


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
0000021c e5850000    478 	str	r0,[r5]

                     479 ;168: 	while(dataSetItem != NULL)


                     480 

00000220 e3540000    481 	cmp	r4,0

00000224 0a00000d    482 	beq	.L441

                     483 .L442:

                     484 ;169: 	{


                     485 

                     486 ;170: 		size_t entityReadLen;


                     487 ;171: 		IEDEntity entity = dataSetItem->obj;


                     488 

00000228 e5940014    489 	ldr	r0,[r4,20]

                     490 ;172: 


                     491 ;173: 		if(!entity->calcReadLen(entity, &entityReadLen))


                     492 

0000022c e590c060    493 	ldr	r12,[r0,96]

00000230 e1a01006    494 	mov	r1,r6

00000234 e1a0e00f    495 	mov	lr,pc

00000238 e12fff1c*   496 	bx	r12

0000023c e3500000    497 	cmp	r0,0

00000240 0affffef    498 	beq	.L438

                     499 ;174: 		{


                     500 

                     501 ;175: 			ERROR_REPORT("Unable to calc DataSet itrm len");


                     502 ;176: 			return false;


                     503 

                     504 ;177: 		}


                     505 ;178: 


                     506 ;179: 		*pLen += entityReadLen;


                     507 

00000244 e59d1000    508 	ldr	r1,[sp]

00000248 e5950000    509 	ldr	r0,[r5]

0000024c e5944000    510 	ldr	r4,[r4]

00000250 e0800001    511 	add	r0,r0,r1

00000254 e5850000    512 	str	r0,[r5]

                     513 ;180: 		dataSetItem = dataSetItem->next;


                     514 

00000258 e3540000    515 	cmp	r4,0

0000025c 1afffff1    516 	bne	.L442

                     517 .L441:

                     518 ;181: 	}


                     519 ;182: 


                     520 ;183: 	return true;


                     521 

00000260 e3a00001    522 	mov	r0,1

                     523 .L435:

00000264 e28dd004    524 	add	sp,sp,4

00000268 e8bd8070    525 	ldmfd	[sp]!,{r4-r6,pc}

                     526 	.endf	DataSet_calcReadLen

                     527 	.align	4

                     528 ;dataSet	r0	local

                     529 ;dataSetItem	r4	local

                     530 ;entityReadLen	[sp]	local

                     531 

                     532 ;dsEntity	r0	param

                     533 ;pLen	r5	param

                     534 

                     535 	.section ".bss","awb"

                     536 .L510:

                     537 	.data

                     538 	.text


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
                     539 

                     540 ;184: }


                     541 

                     542 ;185: 


                     543 ;186: bool DataSet_encodeRead(IEDEntity dsEntity, BufferView *outBuf)


                     544 	.align	4

                     545 	.align	4

                     546 DataSet_encodeRead::

0000026c e92d4030    547 	stmfd	[sp]!,{r4-r5,lr}

00000270 e1a04001    548 	mov	r4,r1

                     549 ;187: {


                     550 

                     551 ;188: 	DataSet* dataSet;


                     552 ;189: 	DataSetItem* dataSetItem;


                     553 ;190: 


                     554 ;191: 	if(dsEntity->type != IED_ENTITY_DATASET)


                     555 

00000274 e5901050    556 	ldr	r1,[r0,80]

00000278 e351000b    557 	cmp	r1,11

0000027c 0a000001    558 	beq	.L530

                     559 .L531:

                     560 ;192: 	{


                     561 

                     562 ;193: 		ERROR_REPORT("Not DataSet");


                     563 ;194: 		return false;


                     564 

00000280 e3a00000    565 	mov	r0,0

00000284 ea00000e    566 	b	.L528

                     567 .L530:

                     568 ;195: 	}


                     569 ;196: 


                     570 ;197: 	dataSet =  dsEntity->extInfo;


                     571 

00000288 e5900058    572 	ldr	r0,[r0,88]

                     573 ;198: 	dataSetItem = dataSet->firstItem;


                     574 

0000028c e5905000    575 	ldr	r5,[r0]

                     576 ;199: 


                     577 ;200: 	while(dataSetItem != NULL)


                     578 

00000290 e3550000    579 	cmp	r5,0

00000294 0a000009    580 	beq	.L534

                     581 .L535:

                     582 ;201: 	{


                     583 

                     584 ;202: 		IEDEntity entity = dataSetItem->obj;


                     585 

00000298 e5950014    586 	ldr	r0,[r5,20]

                     587 ;203: 		if(!entity->encodeRead(entity,outBuf))


                     588 

0000029c e590c05c    589 	ldr	r12,[r0,92]

000002a0 e1a01004    590 	mov	r1,r4

000002a4 e1a0e00f    591 	mov	lr,pc

000002a8 e12fff1c*   592 	bx	r12

000002ac e3500000    593 	cmp	r0,0

000002b0 0afffff2    594 	beq	.L531

                     595 ;204: 		{


                     596 

                     597 ;205: 			ERROR_REPORT("Unable to read DataSet item");


                     598 ;206: 			return false;


                     599 


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6u41.s
                     600 ;207: 		}


                     601 ;208: 		dataSetItem = dataSetItem->next;


                     602 

000002b4 e5955000    603 	ldr	r5,[r5]

000002b8 e3550000    604 	cmp	r5,0

000002bc 1afffff5    605 	bne	.L535

                     606 .L534:

                     607 ;209: 	}


                     608 ;210: 	return true;


                     609 

000002c0 e3a00001    610 	mov	r0,1

                     611 .L528:

000002c4 e8bd8030    612 	ldmfd	[sp]!,{r4-r5,pc}

                     613 	.endf	DataSet_encodeRead

                     614 	.align	4

                     615 ;dataSet	r0	local

                     616 ;dataSetItem	r5	local

                     617 

                     618 ;dsEntity	r0	param

                     619 ;outBuf	r4	param

                     620 

                     621 	.section ".bss","awb"

                     622 .L606:

                     623 	.data

                     624 	.text

                     625 

                     626 ;211: }


                     627 	.align	4

                     628 

                     629 	.data

                     630 	.ghsnote version,6

                     631 	.ghsnote tools,3

                     632 	.ghsnote options,0

                     633 	.text

                     634 	.align	4

