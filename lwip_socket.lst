                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b701.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=lwip_socket.c -o gh_b701.o -list=lwip_socket.lst C:\Users\<USER>\AppData\Local\Temp\gh_b701.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_b701.s
Source File: lwip_socket.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile lwip_socket.c

                      11 ;Source File:   lwip_socket.c

                      12 ;Directory:     

                      13 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      14 ;Compile Date:  Mon Jul 28 12:30:57 2025

                      15 ;Host OS:       Win32

                      16 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      17 ;Release:       MULTI v4.2.3

                      18 ;Revision Date: Wed Mar 29 05:25:47 2006

                      19 ;Release Date:  Fri Mar 31 10:02:14 2006

                      20 

                      21 ;1: #include "platform_socket.h"


                      22 ;2: #include <platform_socket_def.h>


                      23 ;3: #include "MmsConst.h"


                      24 ;4: #include "debug.h"


                      25 ;5: #include <lwiplib.h>


                      26 ;6: #include <lwip/sockets.h>


                      27 ;7: 


                      28 ;8: #define MMS_PORT 102


                      29 ;9: 


                      30 ;10: SOCKET listenSocket;


                      31 ;11: 


                      32 ;12: static struct sockaddr_in  listenAddr = { 0 };


                      33 ;13: 


                      34 ;14: bool socketInit(void)


                      35 	.text

                      36 	.align	4

                      37 socketInit::

00000000 e92d4000     38 	stmfd	[sp]!,{lr}

                      39 ;15: {


                      40 

                      41 ;16:     return lwiplib_init() == 0;


                      42 

00000004 eb000000*    43 	bl	lwiplib_init

00000008 e3500000     44 	cmp	r0,0

0000000c 03a00001     45 	moveq	r0,1

00000010 13a00000     46 	movne	r0,0

00000014 e8bd8000     47 	ldmfd	[sp]!,{pc}

                      48 	.endf	socketInit

                      49 	.align	4

                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b701.s
                      51 	.section ".bss","awb"

                      52 .L33:

                      53 	.data

                      54 	.text

                      55 

                      56 ;17: }


                      57 

                      58 ;18: 


                      59 ;19: 


                      60 ;20: 


                      61 ;21: bool acceptConnection(SOCKET* pConnSocket, struct sockaddr*addr, int *addrlen)


                      62 	.align	4

                      63 	.align	4

                      64 acceptConnection::

00000018 e92d4010     65 	stmfd	[sp]!,{r4,lr}

                      66 ;22: {


                      67 

0000001c e24dd014     68 	sub	sp,sp,20

00000020 e1a04000     69 	mov	r4,r0

00000024 e3a00001     70 	mov	r0,1

00000028 e3a0300a     71 	mov	r3,10

0000002c e1a0c003     72 	mov	r12,r3

00000030 e98d1009     73 	stmfa	[sp],{r0,r3,r12}

00000034 e59f318c*    74 	ldr	r3,.L129

00000038 e58d0010     75 	str	r0,[sp,16]

                      76 ;23:     int keepalive = 1;


                      77 

                      78 ;24:     int keepcnt = 10;


                      79 

                      80 ;25:     int keepidle = 10;


                      81 

                      82 ;26:     int keepintvl = 1;


                      83 

                      84 ;27: 


                      85 ;28:     *pConnSocket = accept (listenSocket, addr, (socklen_t*)addrlen);


                      86 

0000003c e5930000     87 	ldr	r0,[r3]

00000040 eb000000*    88 	bl	lwip_accept

00000044 e28d3004     89 	add	r3,sp,4

00000048 e5840000     90 	str	r0,[r4]

                      91 ;29: 


                      92 ;30:     TRACE("Setting keepalive options");


                      93 ;31: 


                      94 ;32:     //Michael: Я не был пьян, goto написал по приколу и чтобы угодить Денису.


                      95 ;33:     if(0 != setsockopt(*pConnSocket, SOL_SOCKET, SO_KEEPALIVE, &keepalive , sizeof(int)))


                      96 

0000004c e3a01004     97 	mov	r1,4

00000050 e58d1000     98 	str	r1,[sp]

00000054 e3a01ef0     99 	mov	r1,15<<8

00000058 e28110ff    100 	add	r1,r1,255

0000005c e3a02008    101 	mov	r2,8

00000060 eb000000*   102 	bl	lwip_setsockopt

00000064 e3500000    103 	cmp	r0,0

00000068 1a000017    104 	bne	.L44

                     105 ;34:     {


                     106 

                     107 ;35:         ERROR_REPORT("Unable to set SO_KEEPALIVE");


                     108 ;36:         goto setOptError;


                     109 

                     110 ;37:     }


                     111 ;38:     if(0 != setsockopt(*pConnSocket, IPPROTO_TCP, TCP_KEEPCNT, &keepcnt, sizeof(int)))



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b701.s
                     112 

0000006c e28d3008    113 	add	r3,sp,8

00000070 e3a00004    114 	mov	r0,4

00000074 e58d0000    115 	str	r0,[sp]

00000078 e5940000    116 	ldr	r0,[r4]

0000007c e3a02005    117 	mov	r2,5

00000080 e3a01006    118 	mov	r1,6

00000084 eb000000*   119 	bl	lwip_setsockopt

00000088 e3500000    120 	cmp	r0,0

0000008c 1a00000e    121 	bne	.L44

                     122 ;39:     {


                     123 

                     124 ;40:         ERROR_REPORT("Unable to set TCP_KEEPCNT");


                     125 ;41:         goto setOptError;


                     126 

                     127 ;42:     }


                     128 ;43:     if(0 != setsockopt(*pConnSocket, IPPROTO_TCP, TCP_KEEPIDLE, &keepidle, sizeof(int)))


                     129 

00000090 e28d300c    130 	add	r3,sp,12

00000094 e3a00004    131 	mov	r0,4

00000098 e58d0000    132 	str	r0,[sp]

0000009c e5940000    133 	ldr	r0,[r4]

000000a0 e3a02003    134 	mov	r2,3

000000a4 e3a01006    135 	mov	r1,6

000000a8 eb000000*   136 	bl	lwip_setsockopt

000000ac e3500000    137 	cmp	r0,0

000000b0 1a000005    138 	bne	.L44

                     139 ;44:     {


                     140 

                     141 ;45:         ERROR_REPORT("Unable to set TCP_KEEPIDLE");


                     142 ;46:         goto setOptError;


                     143 

                     144 ;47:     }


                     145 ;48:     if(0 != setsockopt(*pConnSocket, IPPROTO_TCP, TCP_KEEPINTVL, &keepintvl, sizeof(int)))


                     146 

000000b4 e28d3010    147 	add	r3,sp,16

000000b8 e3a02004    148 	mov	r2,4

000000bc e58d2000    149 	str	r2,[sp]

000000c0 e5940000    150 	ldr	r0,[r4]

000000c4 e3a01006    151 	mov	r1,6

000000c8 eb000000*   152 	bl	lwip_setsockopt

                     153 .L44:

                     154 ;49:     {


                     155 

                     156 ;50:         ERROR_REPORT("Unable to set TCP_KEEPINTVL");


                     157 ;51:         goto setOptError;


                     158 

                     159 ;52:     }


                     160 ;53: setOptError:


                     161 

                     162 ;54: 


                     163 ;55:     return *pConnSocket >= 0;


                     164 

000000cc e5940000    165 	ldr	r0,[r4]

000000d0 e1a00fa0    166 	mov	r0,r0 lsr 31

000000d4 e2200001    167 	eor	r0,r0,1

000000d8 e28dd014    168 	add	sp,sp,20

000000dc e8bd8010    169 	ldmfd	[sp]!,{r4,pc}

                     170 	.endf	acceptConnection

                     171 	.align	4

                     172 ;keepalive	[sp,4]	local


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b701.s
                     173 ;keepcnt	[sp,8]	local

                     174 ;keepidle	[sp,12]	local

                     175 ;keepintvl	[sp,16]	local

                     176 

                     177 ;pConnSocket	r4	param

                     178 ;addr	none	param

                     179 ;addrlen	none	param

                     180 

                     181 	.section ".bss","awb"

                     182 .L114:

                     183 	.data

                     184 	.text

                     185 

                     186 ;56: }


                     187 

                     188 ;57: 


                     189 ;58: int readSocket(SERVER_SOCKET socket, void* buf, int byteCount)


                     190 	.align	4

                     191 	.align	4

                     192 readSocket::

000000e0 e92d4070    193 	stmfd	[sp]!,{r4-r6,lr}

                     194 ;59: {


                     195 

                     196 ;60: 	unsigned char* byteBuf = buf;


                     197 

000000e4 e1a06000    198 	mov	r6,r0

000000e8 e1b05002    199 	movs	r5,r2

000000ec e1a04001    200 	mov	r4,r1

                     201 ;61: 	while (byteCount)


                     202 

000000f0 0a00000a    203 	beq	.L133

                     204 .L134:

                     205 ;62: 	{


                     206 

                     207 ;63: 		int recvCount;


                     208 ;64:         recvCount = recv((SOCKET)socket, byteBuf, byteCount, 0);


                     209 

000000f4 e1a02005    210 	mov	r2,r5

000000f8 e1a01004    211 	mov	r1,r4

000000fc e1a00006    212 	mov	r0,r6

00000100 e3a03000    213 	mov	r3,0

00000104 eb000000*   214 	bl	lwip_recv

                     215 ;65:         if (recvCount <= 0)


                     216 

00000108 e3500000    217 	cmp	r0,0

                     218 ;66: 		{			


                     219 

                     220 ;67: 			return 0;


                     221 

0000010c d3a00000    222 	movle	r0,0

00000110 da000003    223 	ble	.L130

                     224 ;68: 		}


                     225 ;69: 		byteCount -= recvCount;


                     226 

00000114 e0555000    227 	subs	r5,r5,r0

                     228 ;70: 		byteBuf += recvCount;		


                     229 

00000118 e0844000    230 	add	r4,r4,r0

0000011c 1afffff4    231 	bne	.L134

                     232 .L133:

                     233 ;71: 	}



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b701.s
                     234 ;72:     return 1;


                     235 

00000120 e3a00001    236 	mov	r0,1

                     237 .L130:

00000124 e8bd8070    238 	ldmfd	[sp]!,{r4-r6,pc}

                     239 	.endf	readSocket

                     240 	.align	4

                     241 ;byteBuf	r4	local

                     242 ;recvCount	r0	local

                     243 

                     244 ;socket	r6	param

                     245 ;buf	r1	param

                     246 ;byteCount	r5	param

                     247 

                     248 	.section ".bss","awb"

                     249 .L178:

                     250 	.data

                     251 	.text

                     252 

                     253 ;73: }


                     254 

                     255 ;74: 


                     256 ;75: int writeSocket(SERVER_SOCKET socket, void* buf, int byteCount)


                     257 	.align	4

                     258 	.align	4

                     259 writeSocket::

00000128 e92d4000    260 	stmfd	[sp]!,{lr}

                     261 ;76: {


                     262 

                     263 ;77: 	int bytesSent;


                     264 ;78: 	bytesSent = send((SOCKET)socket, buf, byteCount, 0);


                     265 

0000012c e3a03000    266 	mov	r3,0

00000130 eb000000*   267 	bl	lwip_send

                     268 ;79: 


                     269 ;80:     if (bytesSent < 0)


                     270 

                     271 

                     272 

                     273 

00000134 e3500000    274 	cmp	r0,0

00000138 b3e00000    275 	mvnlt	r0,0

0000013c e8bd8000    276 	ldmfd	[sp]!,{pc}

                     277 	.endf	writeSocket

                     278 	.align	4

                     279 

                     280 ;socket	none	param

                     281 ;buf	none	param

                     282 ;byteCount	none	param

                     283 

                     284 	.section ".bss","awb"

                     285 .L228:

                     286 	.data

                     287 	.text

                     288 

                     289 ;86: }


                     290 

                     291 ;87: 


                     292 ;88: 


                     293 ;89: int startListening()


                     294 	.align	4


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b701.s
                     295 	.align	4

                     296 	.align	4

                     297 startListening::

00000140 e92d4030    298 	stmfd	[sp]!,{r4-r5,lr}

00000144 e59f507c*   299 	ldr	r5,.L129

                     300 ;90: {    


                     301 

                     302 ;91: 	int sockResult;


                     303 ;92: 


                     304 ;93: 	listenSocket = socket(AF_INET, SOCK_STREAM, 0);


                     305 

00000148 e3a02000    306 	mov	r2,0

0000014c e3a01001    307 	mov	r1,1

00000150 e3a00002    308 	mov	r0,2

00000154 eb000000*   309 	bl	lwip_socket

00000158 e5850000    310 	str	r0,[r5]

                     311 ;94: 


                     312 ;95:     if (listenSocket < 0)


                     313 

0000015c e3500000    314 	cmp	r0,0

                     315 ;96: 	{


                     316 

                     317 ;97: 		ERROR_REPORT("'socket' function has returned error");


                     318 ;98: 		return 0;


                     319 

00000160 b3a00000    320 	movlt	r0,0

00000164 ba000016    321 	blt	.L235

                     322 ;99: 	}


                     323 ;100: 


                     324 ;101: 	listenAddr.sin_family = AF_INET;


                     325 

00000168 e59f405c*   326 	ldr	r4,.L343

0000016c e3a00002    327 	mov	r0,2

00000170 e5c40001    328 	strb	r0,[r4,1]

                     329 ;102: 	listenAddr.sin_port = htons(MMS_PORT);


                     330 

00000174 e3a00066    331 	mov	r0,102

00000178 eb000000*   332 	bl	lwip_htons

0000017c e1a01004    333 	mov	r1,r4

00000180 e1c400b2    334 	strh	r0,[r4,2]

                     335 ;103: 	listenAddr.sin_addr.s_addr = INADDR_ANY;


                     336 

00000184 e3a00000    337 	mov	r0,0

00000188 e5840004    338 	str	r0,[r4,4]

                     339 ;104: 


                     340 ;105: 	sockResult = bind(listenSocket, (struct sockaddr*)&listenAddr, sizeof(listenAddr));


                     341 

0000018c e5950000    342 	ldr	r0,[r5]

00000190 e3a02010    343 	mov	r2,16

00000194 eb000000*   344 	bl	lwip_bind

                     345 ;106:     if (sockResult < 0)


                     346 

00000198 e3500000    347 	cmp	r0,0

0000019c ba000005    348 	blt	.L244

                     349 ;107: 	{


                     350 

                     351 ;108: 		ERROR_REPORT("Bind error");


                     352 ;109: 		closesocket(listenSocket);


                     353 

                     354 ;110: 		return 0;


                     355 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b701.s
                     356 ;111: 	}


                     357 ;112: 	


                     358 ;113: 	sockResult = listen(listenSocket, MAX_CONN_COUNT);


                     359 

000001a0 e5950000    360 	ldr	r0,[r5]

000001a4 e3a01004    361 	mov	r1,4

000001a8 eb000000*   362 	bl	lwip_listen

                     363 ;114:     if (sockResult  <  0)


                     364 

000001ac e3500000    365 	cmp	r0,0

                     366 ;119: 	}


                     367 ;120: 	return 1;


                     368 

000001b0 a3a00001    369 	movge	r0,1

000001b4 aa000002    370 	bge	.L235

                     371 .L244:

                     372 ;115: 	{


                     373 

                     374 ;116: 		ERROR_REPORT("'listen' function has returned error");


                     375 ;117: 		closesocket(listenSocket);


                     376 

000001b8 e5950000    377 	ldr	r0,[r5]

000001bc eb000000*   378 	bl	lwip_close

                     379 ;118: 		return 0;


                     380 

000001c0 e3a00000    381 	mov	r0,0

                     382 .L235:

000001c4 e8bd8030    383 	ldmfd	[sp]!,{r4-r5,pc}

                     384 	.endf	startListening

                     385 	.align	4

                     386 

                     387 	.section ".bss","awb"

                     388 .L320:

                     389 	.data

                     390 .L321:

00000000 00         391 listenAddr:	.space	1

00000001 00         392 	.space	1

00000002 0000       393 	.space	2

00000004 00000000    394 	.space	4

00000008 00000000    395 	.space	8

0000000c 00000000 
                     396 	.type	listenAddr,$object

                     397 	.size	listenAddr,16

                     398 	.text

                     399 

                     400 ;121: }


                     401 	.align	4

                     402 .L129:

000001c8 00000000*   403 	.data.w	listenSocket

                     404 	.type	.L129,$object

                     405 	.size	.L129,4

                     406 

                     407 .L343:

000001cc 00000000*   408 	.data.w	.L321

                     409 	.type	.L343,$object

                     410 	.size	.L343,4

                     411 

                     412 	.align	4

                     413 ;listenAddr	.L321	static

                     414 

                     415 	.data


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b701.s
                     416 	.comm	listenSocket,4,4

                     417 	.type	listenSocket,$object

                     418 	.size	listenSocket,4

                     419 	.ghsnote version,6

                     420 	.ghsnote tools,3

                     421 	.ghsnote options,0

                     422 	.text

                     423 	.align	4

                     424 	.data

                     425 	.align	4

                     426 	.text

