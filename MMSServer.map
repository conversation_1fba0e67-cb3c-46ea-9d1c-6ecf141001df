Link Date:	Mon Jul 28 12:31:09 2025
Host OS:	GHS_WIN32
Version:	ELXR 4.0 (c) 1998-2003 Green Hills Software    Build: Mar 24 2006


Load Map Mon Jul 28 12:31:09 2025
Image Summary

  Section              Base      Size(hex)    Size(dec)  SecOffs
  .text                70528644  00019d64       105828   0000120
  .interfunc           705423a8  00000004            4   0019e84
  .rodata              705423ac  000005f8         1528   0019e88
  .fixaddr             705429a4  00000000            0   001a480
  .fixtype             705429a4  00000000            0   001a480
  .data                705429c0  000004fc         1276   001a49c
  .bss                 70542ebc  00399fe8      3776488   001a998
  .ghsinfo             708dcea4  000000d4          212   001a998
  .stack               708dcf78  00001000         4096   001aa6c
  .rela.text           00000000  0000bf58        48984   001aa6c
  .rela.rodata         00000000  00000000            0   00269c4
  .rela.data           00000000  00000288          648   00269c4
  .rela.bss            00000000  00000000            0   0026c4c
  .rela.ghsinfo        00000000  000000d8          216   0026c4c
  .rela.fixaddr        00000000  00000000            0   0026d24
  .rela.fixtype        00000000  00000000            0   0026d24
  .rel.text            00000000  00000030           48   0026d24
  .rela.interfunc      00000000  0000000c           12   0026d6c

Load Map Mon Jul 28 12:31:09 2025
Module Summary

  Origin+Size    Section          Module
70528644+000074  .text            c0_bss.obj
70542ebc+000008  .bss             c0_bss.obj
00000000+0000c0  .rela.text       c0_bss.obj
705286b8+000034  .text            indarchk.o
000000c0+000030  .rela.text       indarchk.o
705286ec+00002c  .text            indarchj.o
000000f0+000030  .rela.text       indarchj.o
70528718+000710  .text            ccvsprnt.o
00000120+00051c  .rela.text       ccvsprnt.o
705429c0+00005c  .data            ccvsprnt.o
00000000+000024  .rela.ghsinfo    ccvsprnt.o
70528e28+00021c  .text            ccllout.o
0000063c+00012c  .rela.text       ccllout.o
70529044+000404  .text            ccefgout.o
00000768+000204  .rela.text       ccefgout.o
70542a1c+000011  .data            ccefgout.o
70529448+0001b0  .text            iedTree.o
70542a30+00000c  .data            iedTree.o
70542ec4+000001  .bss             iedTree.o
0000096c+000174  .rela.text       iedTree.o
00000000+00000c  .rela.data       iedTree.o
705295f8+0008d0  .text            iedEntity.o
00000ae0+0003e4  .rela.text       iedEntity.o
00000024+000024  .rela.ghsinfo    iedEntity.o
70529ec8+0004b8  .text            iedObjects.o
00000ec4+0001a4  .rela.text       iedObjects.o
7052a380+000174  .text            iedFC.o
70542a3c+000048  .data            iedFC.o
705423ac+000036  .rodata          iedFC.o
00001068+000090  .rela.text       iedFC.o
0000000c+0000d8  .rela.data       iedFC.o
7052a4f4+0006d4  .text            iedControlModel.o
000010f8+0002c4  .rela.text       iedControlModel.o
7052abc8+00065c  .text            iedFinalDA.o
70542a84+000004  .data            iedFinalDA.o
000013bc+000288  .rela.text       iedFinalDA.o
7052b224+00021c  .text            iedQuality.o
00001644+000168  .rela.text       iedQuality.o
7052b440+0000ec  .text            iedBool.o
000017ac+0000b4  .rela.text       iedBool.o
7052b52c+0003e8  .text            iedFloat.o
00001860+00027c  .rela.text       iedFloat.o
7052b914+0002b0  .text            iedCodedEnum.o
00001adc+000180  .rela.text       iedCodedEnum.o
7052bbc4+000124  .text            iedInt.o
00001c5c+0000cc  .rela.text       iedInt.o
7052bce8+000124  .text            iedUInt.o
00001d28+0000cc  .rela.text       iedUInt.o
7052be0c+000188  .text            iedEnum.o
00001df4+0000fc  .rela.text       iedEnum.o
7052bf94+000144  .text            iedTimeStamp.o
00001ef0+000048  .rela.text       iedTimeStamp.o
7052c0d8+0000ac  .text            iedConstDA.o
00001f38+000084  .rela.text       iedConstDA.o
7052c184+00008c  .text            iedNoEntity.o
70542ec8+00006c  .bss             iedNoEntity.o
705423e4+000004  .rodata          iedNoEntity.o
00001fbc+0000cc  .rela.text       iedNoEntity.o
7052c210+0002c8  .text            DataSet.o
00002088+0000a8  .rela.text       DataSet.o
7052c4d8+000254  .text            ObjectNameParser.o
00002130+0000f0  .rela.text       ObjectNameParser.o
7052c72c+000664  .text            infoReport.o
70542a88+00001c  .data            infoReport.o
70542f34+000084  .bss             infoReport.o
705423e8+000010  .rodata          infoReport.o
00002220+0003b4  .rela.text       infoReport.o
000000e4+00000c  .rela.data       infoReport.o
7052cd90+000330  .text            control.o
70542aa4+000004  .data            control.o
70542fb8+006000  .bss             control.o
000025d4+00021c  .rela.text       control.o
7052d0c0+000438  .text            fast_memcpy.o
000027f0+00000c  .rela.text       fast_memcpy.o
7052d4f8+00024c  .text            BERCoder.o
000027fc+000018  .rela.text       BERCoder.o
7052d744+000008  .text            platformTools.o
00002814+000018  .rela.text       platformTools.o
7052d74c+0001fc  .text            timers_iednexus.o
70542aa8+000014  .data            timers_iednexus.o
70548fb8+000014  .bss             timers_iednexus.o
0000282c+0001ec  .rela.text       timers_iednexus.o
7052d948+0000cc  .text            main.o
00002a18+000120  .rela.text       main.o
7052da14+000080  .text            frsm.o
00002b38+000060  .rela.text       frsm.o
7052da94+001010  .text            goose.o
70542abc+000004  .data            goose.o
70548fcc+002ba0  .bss             goose.o
705423f8+000028  .rodata          goose.o
00002b98+0007bc  .rela.text       goose.o
7052eaa4+000290  .text            netTools.o
70542ac0+000004  .data            netTools.o
7054bb6c+000018  .bss             netTools.o
00003354+000288  .rela.text       netTools.o
7052ed34+000074  .text            BusError.o
70542ac4+000004  .data            BusError.o
000035dc+00006c  .rela.text       BusError.o
7052eda8+00020c  .text            ConfigFiles.o
70542ac8+000040  .data            ConfigFiles.o
00003648+0000b4  .rela.text       ConfigFiles.o
000000f0+000018  .rela.data       ConfigFiles.o
7052efb4+000620  .text            OscFiles.o
000036fc+000420  .rela.text       OscFiles.o
7052f5d4+00007c  .text            MemoryManager.o
70542b08+000008  .data            MemoryManager.o
00003b1c+00006c  .rela.text       MemoryManager.o
7052f650+0000fc  .text            mms_gocb.o
00003b88+000060  .rela.text       mms_gocb.o
7052f74c+0004c0  .text            FinalDA.o
00003be8+0001d4  .rela.text       FinalDA.o
7052fc0c+0002d8  .text            stringView.o
00003dbc+000090  .rela.text       stringView.o
7052fee4+0001a4  .text            bufView.o
00003e4c+00006c  .rela.text       bufView.o
70530088+0007bc  .text            bufViewBER.o
00003eb8+0001b0  .rela.text       bufViewBER.o
70530844+00003c  .text            bufViewMMS.o
00004068+00000c  .rela.text       bufViewMMS.o
70530880+0004ac  .text            file_system.o
70542420+00000c  .rodata          file_system.o
00004074+0002ac  .rela.text       file_system.o
70530d2c+0001d0  .text            lwip_socket.o
70542b10+000010  .data            lwip_socket.o
00004320+0000d8  .rela.text       lwip_socket.o
70530efc+000010  .text            platform_thread.o
000043f8+00000c  .rela.text       platform_thread.o
70530f0c+0015f8  .text            OscConverter.o
7054242c+000064  .rodata          OscConverter.o
00004404+000c84  .rela.text       OscConverter.o
00000108+000018  .rela.data       OscConverter.o
70532504+0001b8  .text            OscDescr.o
70542b20+000004  .data            OscDescr.o
00005088+0000f0  .rela.text       OscDescr.o
705326bc+000b28  .text            OscInfo.o
70542b24+000068  .data            OscInfo.o
7054bb84+00002c  .bss             OscInfo.o
00005178+000750  .rela.text       OscInfo.o
00000120+000138  .rela.data       OscInfo.o
705331e4+0004ac  .text            OscReadFileContext.o
000058c8+000294  .rela.text       OscReadFileContext.o
70533690+000234  .text            OscWriteBuffer.o
00005b5c+0000fc  .rela.text       OscWriteBuffer.o
705338c4+000ddc  .text            tlsf.o
70542490+000008  .rodata          tlsf.o
00005c58+00081c  .rela.text       tlsf.o
705346a0+000038  .text            timetools.o
00006474+000018  .rela.text       timetools.o
705346d8+000118  .text            crc32.o
70542498+000400  .rodata          crc32.o
0000648c+000030  .rela.text       crc32.o
705347f0+000fb8  .text            fdzipstream.o
70542b8c+000004  .data            fdzipstream.o
000064bc+00057c  .rela.text       fdzipstream.o
705357a8+0017c0  .text            AsnEncoding.o
00006a38+000258  .rela.text       AsnEncoding.o
70536f68+0003d4  .text            Cotp.o
70542898+00008c  .rodata          Cotp.o
00006c90+000180  .rela.text       Cotp.o
7053733c+00019c  .text            session.o
70542b90+000004  .data            session.o
70542924+000004  .rodata          session.o
00006e10+00006c  .rela.text       session.o
705374d8+0003cc  .text            presentation.o
70542b94+000014  .data            presentation.o
00006e7c+0001d4  .rela.text       presentation.o
705378a4+0001ac  .text            acse.o
70542ba8+000008  .data            acse.o
00007050+000108  .rela.text       acse.o
70537a50+0006e8  .text            mms.o
70542bb0+000010  .data            mms.o
00007158+000390  .rela.text       mms.o
70538138+0001fc  .text            mms_get_variable_access_attributes.o
70542928+000014  .rodata          mms_get_variable_access_attributes.o
000074e8+000114  .rela.text       mms_get_variable_access_attributes.o
70538334+00037c  .text            mms_get_data_set_access_attr.o
000075fc+0001b0  .rela.text       mms_get_data_set_access_attr.o
705386b0+000380  .text            mms_get_name_list.o
000077ac+000138  .rela.text       mms_get_name_list.o
70538a30+000464  .text            mms_rcb.o
000078e4+000198  .rela.text       mms_rcb.o
00000048+000024  .rela.ghsinfo    mms_rcb.o
70538e94+000014  .text            debug.o
00007a7c+00003c  .rela.text       debug.o
70538ea8+000010  .text            platform_critical_section.o
00007ab8+000030  .rela.text       platform_critical_section.o
70538eb8+000404  .text            mms_read.o
00007ae8+0001e0  .rela.text       mms_read.o
705392bc+000768  .text            mms_write.o
00007cc8+000198  .rela.text       mms_write.o
70539a24+000ccc  .text            mms_data.o
7054293c+00000c  .rodata          mms_data.o
00007e60+0004e0  .rela.text       mms_data.o
0000006c+000024  .rela.ghsinfo    mms_data.o
7053a6f0+0007cc  .text            mms_fs.o
70542948+000020  .rodata          mms_fs.o
00008340+000360  .rela.text       mms_fs.o
7053aebc+001238  .text            iedmodel.o
70542bc0+000044  .data            iedmodel.o
000086a0+0007e0  .rela.text       iedmodel.o
00000258+00000c  .rela.data       iedmodel.o
00000090+000048  .rela.ghsinfo    iedmodel.o
7053c0f4+000498  .text            mmsServices.o
70542c04+00000c  .data            mmsServices.o
70542968+000014  .rodata          mmsServices.o
00008e80+0001d4  .rela.text       mmsServices.o
00000264+000024  .rela.data       mmsServices.o
7053c58c+000210  .text            mms_error.o
7054297c+000008  .rodata          mms_error.o
00009054+00009c  .rela.text       mms_error.o
7053c79c+0001a0  .text            DomainNameWriter.o
000090f0+000054  .rela.text       DomainNameWriter.o
7053c93c+0004fc  .text            pwin_access.o
7054bbb0+000084  .bss             pwin_access.o
00009144+000264  .rela.text       pwin_access.o
7053ce38+0003a4  .text            DataSlice.o
7054bc34+000008  .bss             DataSlice.o
000093a8+000348  .rela.text       DataSlice.o
7053d1dc+000120  .text            out_queue.o
000096f0+000078  .rela.text       out_queue.o
7053d2fc+00009c  .text            send_thread.o
00009768+000024  .rela.text       send_thread.o
7053d398+00023c  .text            out_buffers.o
0000978c+0000a8  .rela.text       out_buffers.o
7053d5d4+000470  .text            rcb.o
70542984+000010  .rodata          rcb.o
00009834+00036c  .rela.text       rcb.o
7053da44+001534  .text            reports.o
70542c10+000010  .data            reports.o
7054bc3c+008140  .bss             reports.o
00009ba0+000738  .rela.text       reports.o
7053ef78+0003fc  .text            ReportQueue.o
0000a2d8+0000f0  .rela.text       ReportQueue.o
7053f374+000030  .text            connections.o
0000a3c8+000024  .rela.text       connections.o
7053f3a4+0000d8  .text            server.o
70542c20+000004  .data            server.o
70553d7c+000001  .bss             server.o
70542994+000010  .rodata          server.o
0000a3ec+0000d8  .rela.text       server.o
7053f47c+000004  .text            MMSServer.o
0000a4c4+00000c  .rela.text       MMSServer.o
7053f480+000004  .text            gmalloc.lib(malloc.o)
0000a4d0+00000c  .rela.text       gmalloc.lib(malloc.o)
7053f484+000004  .text            gmalloc.lib(free.o)
0000a4dc+00000c  .rela.text       gmalloc.lib(free.o)
7053f488+00002c  .text            ARMLIBV4.lib(idivmod.o)
00000000+000010  .rel.text        ARMLIBV4.lib(idivmod.o)
7053f4b4+000148  .text            ARMLIBV4.lib(idiv.o)
00000010+000008  .rel.text        ARMLIBV4.lib(idiv.o)
7053f5fc+000004  .text            ARMLIBV4.lib(div0.o)
7053f600+000074  .text            CLib.lib(atoi.o)
0000a4e8+000024  .rela.text       CLib.lib(atoi.o)
7053f674+000024  .text            CLib.lib(strlen.o)
0000a50c+00000c  .rela.text       CLib.lib(strlen.o)
7053f698+000014  .text            CLib.lib(itoa.o)
0000a518+00000c  .rela.text       CLib.lib(itoa.o)
7053f6ac+000044  .text            CLib.lib(lmt.o)
0000a524+000018  .rela.text       CLib.lib(lmt.o)
7053f6f0+0001c0  .text            CLib.lib(gmt.o)
0000a53c+000048  .rela.text       CLib.lib(gmt.o)
7053f8b0+00003c  .text            CLib.lib(strncpy.o)
0000a584+00000c  .rela.text       CLib.lib(strncpy.o)
7053f8ec+000064  .text            CLib.lib(lockinterrupt.o)
0000a590+00000c  .rela.text       CLib.lib(lockinterrupt.o)
70553d80+00000c  .bss             CLib.lib(osdata.obj)
7053f950+000018  .text            CLib.lib(setvect.obj)
7053f968+000020  .text            CLib.lib(getvect.obj)
7053f988+000018  .text            CLib.lib(memset.obj)
0000a59c+00000c  .rela.text       CLib.lib(memset.obj)
7053f9a0+000044  .text            CLib.lib(memcpy.obj)
0000a5a8+000024  .rela.text       CLib.lib(memcpy.obj)
7053f9e4+000014  .text            CLib.lib(CrTimer.obj)
7053f9f8+000028  .text            CLib.lib(exit.obj)
0000a5cc+00003c  .rela.text       CLib.lib(exit.obj)
7053fa20+000018  .text            CLib.lib(GetPH.obj)
7053fa38+00001c  .text            CLib.lib(GetPA.obj)
7053fa54+000018  .text            CLib.lib(gmema.obj)
7053fa6c+000014  .text            CLib.lib(gmemf.obj)
7053fa80+000048  .text            CLib.lib(CS.obj)
0000a608+00000c  .rela.text       CLib.lib(CS.obj)
7053fac8+00000c  .text            CLib.lib(listlist.obj)
7053fad4+000040  .text            CLib.lib(days.obj)
7053fb14+000030  .text            CLib.lib(memcmp.obj)
0000a614+000018  .rela.text       CLib.lib(memcmp.obj)
7053fb44+000044  .text            CLib.lib(thread.obj)
70542c24+000104  .data            CLib.lib(ctype.o)
7053fb88+000074  .text            CLib.lib(__ltoa.o)
0000a62c+000018  .rela.text       CLib.lib(__ltoa.o)
7053fbfc+000630  .text            lwiplib.lib(lwiplib.obj)
70542d40+000104  .data            lwiplib.lib(lwiplib.obj)
0000a644+000b10  .rela.text       lwiplib.lib(lwiplib.obj)
7054022c+000038  .text            ethbus.lib(ethbuslib.o)
70542e44+000008  .data            ethbus.lib(ethbuslib.o)
0000b154+000030  .rela.text       ethbus.lib(ethbuslib.o)
70540264+00047c  .text            libansi.a(ccdocvt.o)
70542e4c+000070  .data            libansi.a(ccdocvt.o)
0000b184+00030c  .rela.text       libansi.a(ccdocvt.o)
705406e0+0000a8  .text            libansi.a(ccsnprtf.o)
0000b490+000060  .rela.text       libansi.a(ccsnprtf.o)
705423a8+000004  .interfunc       libarch.a(indarch4.o)
00000000+00000c  .rela.interfunc  libarch.a(indarch4.o)
70540788+000058  .text            libarch.a(indbsch2.o)
0000b4f0+000030  .rela.text       libarch.a(indbsch2.o)
705407e0+000044  .text            libarch.a(indclz32.o)
0000b520+00000c  .rela.text       libarch.a(indclz32.o)
70540824+000030  .text            libind.a(indfcmp.o)
0000b52c+00000c  .rela.text       libind.a(indfcmp.o)
70540854+00003c  .text            libind.a(inddcmp.o)
0000b538+000018  .rela.text       libind.a(inddcmp.o)
70540890+000254  .text            libind.a(indfto64.o)
0000b550+0000cc  .rela.text       libind.a(indfto64.o)
70540ae4+000368  .text            libind.a(inddto64.o)
0000b61c+0000d8  .rela.text       libind.a(inddto64.o)
70540e4c+00009c  .text            libind.a(indfmul.o)
0000b6f4+00003c  .rela.text       libind.a(indfmul.o)
70540ee8+0000e4  .text            libind.a(indfdiv.o)
0000b730+000060  .rela.text       libind.a(indfdiv.o)
70540fcc+00041c  .text            libind.a(inddadd.o)
0000b790+000120  .rela.text       libind.a(inddadd.o)
705413e8+000104  .text            libind.a(inddmul.o)
0000b8b0+00003c  .rela.text       libind.a(inddmul.o)
705414ec+0003d0  .text            libind.a(indddiv.o)
0000b8ec+000060  .rela.text       libind.a(indddiv.o)
705418bc+0000b4  .text            libind.a(indfto32.o)
0000b94c+0000b4  .rela.text       libind.a(indfto32.o)
70541970+000140  .text            libind.a(inddto32.o)
0000ba00+000054  .rela.text       libind.a(inddto32.o)
70541ab0+000070  .text            libind.a(ind32tof.o)
0000ba54+00003c  .rela.text       libind.a(ind32tof.o)
70541b20+00006c  .text            libind.a(ind32tod.o)
0000ba90+00003c  .rela.text       libind.a(ind32tod.o)
70541b8c+000024  .text            libind.a(indftod.o)
0000bacc+000018  .rela.text       libind.a(indftod.o)
70541bb0+000054  .text            libind.a(inddtof.o)
0000bae4+000030  .rela.text       libind.a(inddtof.o)
70541c04+00004c  .text            libind.a(indisinf.o)
0000bb14+000024  .rela.text       libind.a(indisinf.o)
70541c50+000060  .text            libind.a(indisnan.o)
0000bb38+00003c  .rela.text       libind.a(indisnan.o)
70541cb0+0000f8  .text            libind.a(ind64_div64.o)
0000bb74+0000a8  .rela.text       libind.a(ind64_div64.o)
70541da8+0000dc  .text            libind.a(ind64_rem64.o)
0000bc1c+000054  .rela.text       libind.a(ind64_rem64.o)
70541e84+0000c0  .text            libind.a(ind64_lsh64.o)
0000bc70+0000c0  .rela.text       libind.a(ind64_lsh64.o)
70541f44+0000d4  .text            libind.a(ind64_rsh64.o)
0000bd30+0000c0  .rela.text       libind.a(ind64_rsh64.o)
70542018+0001c0  .text            libind.a(ind64_udiv64.o)
0000bdf0+00012c  .rela.text       libind.a(ind64_udiv64.o)
705421d8+000090  .text            libind.a(ind64_urem64.o)
0000bf1c+00003c  .rela.text       libind.a(ind64_urem64.o)
70542268+00002c  .text            ARMLIBV4.lib(udivmod.o)
00000018+000010  .rel.text        ARMLIBV4.lib(udivmod.o)
70542294+000114  .text            ARMLIBV4.lib(udiv.o)
00000028+000008  .rel.text        ARMLIBV4.lib(udiv.o)

Load Map Mon Jul 28 12:31:09 2025
Global Symbols (sorted alphabetically)

 .text            705378b0 AcseConnection_createAssociateResponseMessage
 .text            705378a4 AcseConnection_init
 .data            70542b9c Asn_Id_Acse
 .data            70542ba0 Asn_Id_Mms
 .text            7052d4f8 BERCoder_calcIntEncodedLen
 .text            7052d6c8 BERCoder_reverseCopy
 .text            70535cb0 BerDecoder_DecodeBitStringTLToInt
 .text            70535d84 BerDecoder_DecodeLengthOld
 .text            70535fb4 BerDecoder_DecodeObjectName
 .text            70536050 BerDecoder_DecodeObjectNameToStringView
 .text            70535f1c BerDecoder_DecodeUint32Old
 .text            70536f28 BerDecoder_decodeFloat
 .text            70535a10 BerDecoder_decodeInt32
 .text            705357e8 BerDecoder_decodeLength
 .text            70535b64 BerDecoder_decodeString
 .text            70535994 BerDecoder_decodeTLFromBufferView
 .text            70535ab4 BerDecoder_decodeUint32
 .text            70536158 BerEncoder_CompressInteger
 .text            705365d0 BerEncoder_EncodeFloatWithTL
 .text            705364d8 BerEncoder_EncodeInt32
 .text            70536864 BerEncoder_EncodeInt32WithTL
 .text            70536110 BerEncoder_Int32DetermineEncodedSize
 .text            70536d54 BerEncoder_RevertByteOrder
 .text            705360b0 BerEncoder_UInt32determineEncodedSize
 .text            70536c18 BerEncoder_determineEncodedStringSize
 .text            70536098 BerEncoder_determineFullObjectSize
 .text            7053607c BerEncoder_determineLengthSize
 .text            705369b0 BerEncoder_encodeBitString
 .text            70536b48 BerEncoder_encodeBitStringUshortBuf
 .text            70536d2c BerEncoder_encodeBoolean
 .text            705357a8 BerEncoder_encodeLength
 .text            7053696c BerEncoder_encodeOctetString
 .text            70536c40 BerEncoder_encodeStringWithTL
 .text            70535b4c BerEncoder_encodeTL
 .text            705363d8 BerEncoder_encodeUInt32
 .text            70536754 BerEncoder_encodeUInt32WithTL
 .text            70536bd8 BerEncoder_encodeUcharBitString
 .text            70536b84 BerEncoder_encodeUshortBitString
 .text            70536100 BerEncoder_uint32determineEncodedSizeTL
 .text            70530844 BufView_decodeObjectName
 .text            7053006c BufferView_advance
 .text            7052fef0 BufferView_alloc
 .text            70530200 BufferView_decodeExtTL
 .text            705302b8 BufferView_decodeInt32
 .text            70530340 BufferView_decodeInt32TL
 .text            705303f0 BufferView_decodeStringViewTL
 .text            70530170 BufferView_decodeTL
 .text            705302fc BufferView_decodeUInt32
 .text            70530398 BufferView_decodeUInt32TL
 .text            7053068c BufferView_encodeBoolean
 .text            70530728 BufferView_encodeBufferView
 .text            70530594 BufferView_encodeExtTL
 .text            705305f4 BufferView_encodeInt32
 .text            705306cc BufferView_encodeOctetString
 .text            70530514 BufferView_encodeStr
 .text            70530490 BufferView_encodeStringView
 .text            70530548 BufferView_encodeTL
 .text            70530640 BufferView_encodeUInt32
 .text            7052fee4 BufferView_init
 .text            70530088 BufferView_peekTag
 .text            70530020 BufferView_readStringView
 .text            70530730 BufferView_reverseWrite
 .text            70530128 BufferView_skipAnyObject
 .text            705300a8 BufferView_skipObject
 .text            7052ffe0 BufferView_writeData
 .text            7052ff6c BufferView_writeStr
 .text            7052ff14 BufferView_writeStringView
 .text            70530460 BufferView_writeTag
 .text            7052ff94 BufferView_writeUshortBE
 .text            7052ed80 BusError_check
 .text            7052ed78 BusError_init
 .text            7052efa8 CFGFS_closeFile
 .text            7052efa4 CFGFS_findClose
 .text            7052ee70 CFGFS_findFirst
 .text            7052eeac CFGFS_findNext
 .text            7052ee10 CFGFS_init
 .text            7052eec8 CFGFS_openFile
 .text            7052ef44 CFGFS_readFile
 .text            7053fab8 CSInit
 .text            7053fa9c CSLock
 .text            7053fa80 CSTrylock
 .text            7053fab8 CSUnlock
 .text            7052cdf0 Control_disableWaitingObjects
 .text            7052cdb8 Control_processCtrlObjects
 .text            7052cd90 Control_registerCtrlObj
 .text            7052cfe4 Control_sendNegativeCmdTermReport
 .text            7052cf28 Control_sendPositiveCmdTermReport
 .text            7052ce28 Control_sendServiceErrorReport
 .text            7053c660 CreateMmsConfirmedErrorPdu
 .text            7053f9e4 CreateTimer
 .text            70538eb4 CriticalSection_Done
 .text            70538ea8 CriticalSection_Init
 .text            70538eac CriticalSection_Lock
 .text            70538eb0 CriticalSection_Unlock
 .text            7052c3fc DataSet_calcReadLen
 .text            7052c47c DataSet_encodeRead
 .text            7052c3d0 DataSet_getDataSetObj
 .text            7052c3e4 DataSet_getFirstItem
 .text            7052c210 DataSet_init
 .text            7052c384 DataSet_postCreate
 .text            7053d140 DataSlice_getAnalogOffset
 .text            7053d05c DataSlice_getBoolFast
 .text            7053d064 DataSlice_getBoolFastCurrDS
 .text            7053d0d0 DataSlice_getBoolOffset
 .text            7053d0b4 DataSlice_getDataSliceWnd
 .text            7053d0a4 DataSlice_getFixedFastCurrDS
 .text            7053d074 DataSlice_getInt32FastCurrDS
 .text            7053d10c DataSlice_getIntOffset
 .text            7053d094 DataSlice_getRealFastCurrDS
 .text            7053d084 DataSlice_getUInt32FastCurrDS
 .text            7053d1a0 DataSlice_setCallBack
 .text            70535c50 DecodeBIT_STRINGPrimitive
 .text            70535bcc DecodeBOOLEAN
 .text            7053c858 DomainNameWriter_discardName
 .text            7053c888 DomainNameWriter_encode
 .text            7053c79c DomainNameWriter_init
 .text            7053c7f0 DomainNameWriter_pushName
 .text            7053c7c0 DomainNameWriter_setStartName
 .text            70535bf8 EncodeBIT_STRINGPrimitive
 .text            70535ba4 EncodeBOOLEAN
 .text            7052f74c FDAQuality_create
 .text            7052f8dc FDAQuality_encodeFixedData
 .text            7052f844 FDAQuality_encodeGOOSETemplate
 .text            7052f834 FDAQuality_getFixedEncodedSize
 .text            7052f8a8 FDAQuality_readAndCompare
 .text            7052f904 FDA_create
 .text            7052fbac FDA_encodeFixedData
 .text            7052fa00 FDA_encodeGOOSETemplate
 .text            7052f9b0 FDA_getFixedEncodedSize
 .text            7052fabc FDA_readAndCompare
 .text            7052e864 GOOSE_getGoEna
 .text            7052e898 GOOSE_getNdsCom
 .text            7052e93c GOOSE_init
 .text            7052e57c GOOSE_send
 .text            7052e5bc GOOSE_sendChanges
 .text            7052e8cc GOOSE_setGoEna
 .text            7052de98 GSE_init
 .text            7053fb04 GetDaysPtr
 .text            7053f968 GetIntCallBack
 .text            7053fac8 GetListOfList
 .text            7052e164 GoCB_init
 .text            7052b4e0 IEDBool_init
 .text            7052baac IEDCodedEnum_init
 .text            7052a2a8 IEDComplexObj_calcReadLen
 .text            7052a2e8 IEDComplexObj_encodeRead
 .text            70529f38 IEDComplexObj_init
 .text            7052a148 IEDComplexObj_write
 .text            7052a0b8 IEDConstDA_calcReadLen
 .text            7052a0f8 IEDConstDA_encodeRead
 .text            7052c0d8 IEDConstDA_init
 .text            7052a5b4 IEDControlDA_checkTerminate
 .text            7052a63c IEDControlDA_disconnect
 .text            7052a6c0 IEDControlDA_getOrCat
 .text            7052a680 IEDControlDA_getOrIdent
 .text            7052a8f4 IEDControlDA_init
 .text            7052a4f4 IEDControlDA_isControlDA
 .text            7052a51c IEDControlDA_isReady
 .text            7052a544 IEDControlDA_waitReady
 .text            7052ab24 IEDControlDA_write
 .text            7052aaf8 IEDControlDO_init
 .text            7052a7e8 IEDCtlNum_init
 .text            7052a010 IEDDA_init
 .text            70529fd0 IEDDO_getTimeStampDA
 .text            70529f7c IEDDO_init
 .text            70529788 IEDEntity_addChild
 .text            705295f8 IEDEntity_alloc
 .text            70529d58 IEDEntity_attachTimeStamp
 .text            705297ec IEDEntity_create
 .text            7052986c IEDEntity_createFromBER
 .text            70529e44 IEDEntity_findChanges
 .text            70529b3c IEDEntity_getChildByCStrName
 .text            70529b74 IEDEntity_getChildByFullName
 .text            70529b04 IEDEntity_getChildByName
 .text            70529c00 IEDEntity_getChildByTag
 .text            70529d2c IEDEntity_getDomainId
 .text            70529cc0 IEDEntity_getFullItemId
 .text            70529c48 IEDEntity_getFullName
 .text            705297b0 IEDEntity_init
 .text            70529820 IEDEntity_postCreate
 .text            70529dcc IEDEntity_printFullName
 .text            70529e90 IEDEntity_setReadOnlyRecursive
 .text            70529e2c IEDEntity_setTimeStamp
 .text            70529c28 IEDEntity_write
 .text            70529d94 IEDEntity_writeFullName
 .text            7052bf48 IEDEnum_init
 .data            70542a3c IEDFCTypeNames
 .text            7052a380 IEDFC_init
 .text            7052abdc IEDFinalDA_init
 .text            7052b8d8 IEDFloat_init
 .text            7052bc9c IEDInt32_init
 .text            70529f18 IEDLD_getDataSection
 .text            70529f28 IEDLD_getDataSetSection
 .text            70529ec8 IEDLD_init
 .text            7053c0b4 IEDModel_getBufferView
 .text            7053bfbc IEDModel_getChildren
 .text            7053c020 IEDModel_getTermItemDescrStruct
 .text            7053b090 IEDModel_isServiceInfo
 .text            7053bd68 IEDModel_ptrFromPos
 .text            7053bf54 IEDModel_skipServiceInfo
 .text            7052c1f4 IEDNoEntity_get
 .text            7052c1b4 IEDNoEntity_init
 .text            7052a6f4 IEDOrCat_calcReadLen
 .text            7052a810 IEDOrCat_encodeRead
 .text            7052a7b4 IEDOrCat_init
 .text            7052a728 IEDOrCat_write
 .text            7052a834 IEDOrIdent_calcReadLen
 .text            7052a858 IEDOrIdent_encodeRead
 .text            7052a780 IEDOrIdent_init
 .text            7052a87c IEDOrIdent_write
 .text            7052b344 IEDQuality_init
 .text            7052b794 IEDRealAsInt64_init
 .text            7052b678 IEDReal_init
 .text            7052ae44 IEDTermItemDA_calcReadLen
 .text            7052af8c IEDTermItemDA_encodeRead
 .text            7052ae2c IEDTermItemDA_getTerminalItemDescr
 .text            7052b0cc IEDTermItemDA_write
 .text            7052bf94 IEDTimeStampDA_calcReadLen
 .text            7052bfa4 IEDTimeStampDA_encodeRead
 .text            7052c00c IEDTimeStampDA_write
 .text            705295d0 IEDTree_addToCmpList
 .text            705294c8 IEDTree_findDataByFullName
 .text            70529504 IEDTree_findDataSetByFullName
 .text            70529540 IEDTree_findDataSetBySingleName
 .text            70529448 IEDTree_init
 .text            705294b8 IEDTree_lock
 .text            705294c0 IEDTree_unlock
 .text            7052959c IEDTree_updateFromDataSlice
 .text            70529570 IEDTree_write
 .text            7052bdc0 IEDUInt32_init
 .text            7052ada8 IEDVarDA_calcReadLen
 .text            7052add8 IEDVarDA_encodeRead
 .text            7052ae08 IEDVarDA_write
 .text            7052c8ac InfoReport_createLastApplErrorReport
 .text            7052ca74 InfoReport_createNegativeCmdTermReport
 .text            7052c944 InfoReport_createPositiveCmdTermReport
 .text            7052cccc InfoReport_send
 .text            70536e3c IsIncluded
 .text            705377f4 IsoPresentation_createUserData
 .text            70539d94 MMSData_encodeTimeStamp
 .text            7053c734 MMSError_createConfirmedErrorPdu
 .text            7052f600 MM_alloc
 .text            7052f63c MM_getAllocated
 .text            7052f5d4 MM_init
 .text            7052ecc4 NetTools_busOK
 .text            7052ec7c NetTools_getIf
 .text            7052ec94 NetTools_getMac
 .text            7052ebec NetTools_init
 .text            7052ecac NetTools_send
 .text            70532680 OSCDescr_analogName
 .text            7053268c OSCDescr_analogUnits
 .text            70532698 OSCDescr_boolName
 .text            70532514 OSCDescr_findDescrAnalogItem
 .text            70532584 OSCDescr_findDescrBoolItem
 .text            705326a4 OSCDescr_getFreq
 .text            70532660 OSCDescr_getTerminalName
 .text            70532670 OSCDescr_getTerminalVersion
 .text            705325f8 OSCDescr_init
 .text            7052f3e0 OSCFS_closeFile
 .text            7052f20c OSCFS_findClose
 .text            7052f168 OSCFS_findFirst
 .text            7052f1d4 OSCFS_findNext
 .text            7052efb4 OSCFS_init
 .text            7052f2c0 OSCFS_openFile
 .text            7052f3f0 OSCFS_readFile
 .text            70532b64 OSCFrame_getADCPeriod
 .text            70532b94 OSCFrame_getAnalogValue
 .text            70532c14 OSCFrame_getBoolValue
 .text            70532cec OSCFrame_getFreq
 .text            70532c60 OSCFrame_getTick
 .text            705327f0 OSCInfo_create
 .text            7053298c OSCInfo_destroy
 .text            70532a2c OSCInfo_getADCClkFreq
 .text            70532b38 OSCInfo_getADCFractionSize
 .text            70532b20 OSCInfo_getADCSampleSize
 .text            70533164 OSCInfo_getAnalog
 .text            7053317c OSCInfo_getAnalogCft
 .text            70532a8c OSCInfo_getAnalogCount
 .text            7053318c OSCInfo_getAnalogMax
 .text            7053319c OSCInfo_getAnalogMin
 .text            70533170 OSCInfo_getBool
 .text            70532aa4 OSCInfo_getBoolCount
 .text            70532b54 OSCInfo_getBufferContent
 .text            705329fc OSCInfo_getDateMS
 .text            70532b5c OSCInfo_getFrameBuffer
 .text            70532ad4 OSCInfo_getFrameCount
 .text            70532adc OSCInfo_getFrameOffset
 .text            70532abc OSCInfo_getHeaderSize
 .text            70532b50 OSCInfo_getOscContentOffset
 .text            70532a14 OSCInfo_getOscVersion
 .text            70532a74 OSCInfo_getPointPerFrameCount
 .text            70532a5c OSCInfo_getPrehistFirstFrameNum
 .text            70532a44 OSCInfo_getPrehistFrameCount
 .text            705329e4 OSCInfo_getUTCDate
 .text            7053274c OSCInfo_init
 .text            70532d68 OSCInfo_initContent
 .text            705329c8 OSCInfo_lockHeaderBuf
 .text            705329dc OSCInfo_unlockHeaderBuf
 .text            7052c514 ObjectNameParser_parse
 .text            705324e4 OscConverter_init
 .text            70531660 OscConverter_processCfg
 .text            70532460 OscConverter_processHdr
 .text            70530f0c OscConverter_processPeriod
 .text            7052f084 OscFiles_free
 .text            7052f014 OscFiles_malloc
 .text            7052f048 OscFiles_realloc
 .text            70533454 OscReadFileContext_closeCfg
 .text            705334b4 OscReadFileContext_closeDat
 .text            70533514 OscReadFileContext_closeHdr
 .text            705332cc OscReadFileContext_create
 .text            705333b0 OscReadFileContext_destroy
 .text            7053351c OscReadFileContext_flushAndClose
 .text            70533650 OscReadFileContext_getFreqCfg
 .text            70533644 OscReadFileContext_getFreqCount
 .text            70533544 OscReadFileContext_getPhistotyTime
 .text            70533574 OscReadFileContext_getPhistotyTimeMS
 .text            70533400 OscReadFileContext_writeCfgToStream
 .text            7053345c OscReadFileContext_writeDatToStream
 .text            705335c4 OscReadFileContext_writeFreq
 .text            705334bc OscReadFileContext_writeHdrToStream
 .text            705336dc OscWriteBuffer_attach
 .text            70533690 OscWriteBuffer_create
 .text            7053381c OscWriteBuffer_data
 .text            7053382c OscWriteBuffer_dataLen
 .text            70533844 OscWriteBuffer_destroy
 .text            70533808 OscWriteBuffer_empty
 .text            705336f8 OscWriteBuffer_reset
 .text            705337e0 OscWriteBuffer_resize
 .text            70533834 OscWriteBuffer_size
 .text            70533854 OscWriteBuffer_toBufferView
 .text            7053379c OscWriteBuffer_toWriteBuffer
 .text            70533708 OscWriteBuffer_write
 .text            7053d1f4 OutQueue_done
 .text            7053d298 OutQueue_get
 .text            7053d1dc OutQueue_init
 .text            7053d20c OutQueue_insert
 .text            7053d1f8 OutQueue_isEmpty
 .text            7052d744 PTools_lockInterrupt
 .text            7052d748 PTools_unlockInterrupt
 .text            7053efa8 ReportQueue_init
 .text            7053f338 ReportQueue_isEmpty
 .text            7053f35c ReportQueue_isOverflow
 .text            7053f2f8 ReportQueue_purge
 .text            7053f218 ReportQueue_read
 .text            7053f050 ReportQueue_write
 .text            7053ee4c Reporter_isOwnerConnection
 .text            7053ef20 Reporter_setDataSetName
 .text            7053ee60 Reporter_setEnable
 .text            7053ef54 Reporter_setGI
 .text            7053ef34 Reporter_setIntgPd
 .text            7053eec0 Reporter_setResv
 .text            7053d3f8 SessionBuffers_done
 .text            7053f950 SetIntCallBack
 .text            7052fe88 StringView_cmp
 .text            7052fea8 StringView_cmpCStr
 .text            7052fc4c StringView_findChar
 .text            7052fd40 StringView_findCharBack
 .text            7052fc24 StringView_fromCStr
 .text            7052fc18 StringView_fromStringView
 .text            7052fc0c StringView_init
 .text            7052fe34 StringView_splitChar
 .text            705346a0 TimeTools_gmtime32
 .text            705346bc TimeTools_localtime32
 .text            7052d914 Timers_getTickCount
 .text            7052d800 Timers_init
 .text            7052d8dc Timers_isTimeout
 .text            7052d790 Timers_setGoose1msCallBack
 .text            7052d79c Timers_setIntegrity1msCallBack
 .text            7052d7a8 Timers_setNetBusChek1msCallback
 .text            7053fb44 _CreateThread
 .text            7053fb7c _GetExitCodeThread
 .text            7053fa20 _GetModuleHandle
 .text            7053fa38 _GetProcAddress
 .text            7053fb64 _TerminateThread
 .text            705407e0 __CLZ32
 .bss             70542ebc __CS__
 .text            7053f4b4 __aeabi_idiv
 .text            7053f5fc __aeabi_idiv0
 .text            7053f5dc __aeabi_idivmod
 .text            7053f5fc __aeabi_ldiv0
 .text            70542294 __aeabi_uidiv
 .text            70542388 __aeabi_uidivmod
 .text            70540fd0 __dadd
 .text            70540854 __dcmp
 .text            705414ec __ddiv
 .text            7053f4b4 __divsi3
 .text            705413e8 __dmul
 .text            70540264 __docvt
 .text            70540fcc __dsub
 .text            70541bb0 __dtof
 .text            705419c8 __dtoi
 .text            70540bbc __dtoi64
 .text            70540cc4 __dtoi64r
 .text            70541a24 __dtoir
 .text            70541970 __dtou
 .text            70540ae4 __dtou64
 .text            7053f9f8 __exit
 .text            70540824 __fcmp
 .text            70540ee8 __fdiv
 .text            70540e4c __fmul
 .text            705418dc __fto32
 .text            70541b8c __ftod
 .text            705418cc __ftoi
 .text            70540924 __ftoi64
 .text            705409f0 __ftoi64r
 .text            705418d4 __ftoir
 .text            705418bc __ftou
 .text            70540890 __ftou64
 .text            705418c4 __ftour
 .text            70541cb0 __gh_div64
 .text            70529300 __gh_float_printf
 .text            70528810 __gh_hexout
 .text            70528f30 __gh_long_long_printf
 .text            70541e84 __gh_lsh64
 .text            705287c0 __gh_octhex_finish
 .text            70541da8 __gh_rem64
 .text            70541f44 __gh_rsh64
 .text            7052884c __gh_strout
 .text            70542018 __gh_udiv64
 .text            70528f74 __gh_uns_long_long_printf
 .text            705421d8 __gh_urem64
 .text            70528988 __gh_vsprintf
 .text            70528718 __gh_xxdecout
 .interfunc       705423a8 __ghsArmBLR
 .text            70540788 __ghs_bsearch_16
 .bss             70542ebc __ghsbegin_bss
 .text            70528644 __ghsbegin_text
 .bss             708dcea4 __ghsend_bss
 .text            705423a8 __ghsend_text
 .text            7053f6f0 __gmtime
 .text            70541b28 __itod
 .text            70541ab8 __itof
 .text            7053f6ac __localtime
 .text            7053fb88 __ltoa
 .text            705286b8 __sdiv10
 .text            7053f488 __sdiv_32_32
 .text            705286cc __smod10
 .text            7053f498 __smod_32_32
 .text            705286ec __udiv10
 .text            70542268 __udiv_32_32
 .text            70542294 __udivsi3
 .text            705286fc __umod10
 .text            70542278 __umod_32_32
 .text            70541b20 __utod
 .text            70541ab0 __utof
 .data            70542c24 _ctypes_
 .text            7053fa54 _globalAlloc
 .text            7053fa6c _globalFree
 .bss             70553d84 _handle
 .text            70528644 _init00
 .text            7053f698 _itoa
 .bss             70553d88 _osdata
 .bss             70553d80 _tsr
 .text            70530d44 acceptConnection
 .text            7053d3fc allocSessionOutBuffer
 .text            7053f374 allocateConnection
 .data            70542ba8 appContextNameMms
 .data            70542a88 applErrorVarSpec
 .text            7053f600 atoi
 .data            70542b94 berId
 .data            70542b98 calledPresentationSelector
 .data            70542b00 cfgFiles
 .data            70542ae4 cidFile
 .text            70537e38 closeIsoConnection
 .text            7053f438 closeServerSocket
 .bss             70553e2c cmdTermObjNameBuf
 .text            70537034 cotpReceiveData
 .text            70536f88 cotpSendData
 .text            705346d8 crc32
 .text            705373c4 createAcceptSPDU
 .text            70530efc createThread
 .bss             70553eec csHeaderBuf
 .bss             70553d8c ctrlObjects
 .bss             70557f00 currentDataSlice
 .text            7053ced8 dataSliceCapture
 .bss             70553f00 dataSliceCopy
 .text            7053cfb4 dataSliceGetBoolValue
 .text            7053cf4c dataSliceGetFloatValue
 .text            7053d028 dataSliceGetIntValue
 .text            7053cf80 dataSliceGetRealValue
 .text            7053cf3c dataSliceGetTimeStamp
 .bss             70553ef4 dataSliceIf
 .text            7053ce70 dataSliceInit
 .text            7053d1cc dataSliceRelease
 .bss             70553ef8 dataSliceSize
 .data            70542a34 dataSliceUpdateList
 .data            70542a38 dataSliceUpdateListTail
 .text            70538ea0 debugSendDump
 .text            70538e9c debugSendStrL
 .text            70538ea4 debugSendText
 .text            70538e98 debugSendUshort
 .text            70538e94 debugStart
 .data            70542bc0 defaultIed
 .text            7053eba4 disableDisconnectedReports
 .text            70529044 efout..trg.2Farm4.2Fobjs.2Flibs.2Flibansi.2Fccefgout.
 .text            7053a2cc encodeAccessAttrBitString
 .text            7053a2ec encodeAccessAttrBitStringConst
 .text            7053a4a8 encodeAccessAttrBoolean
 .text            7053a4c8 encodeAccessAttrCodedEnum
 .text            7053a530 encodeAccessAttrConst
 .text            7053a510 encodeAccessAttrEntryTime
 .text            7053a35c encodeAccessAttrFloat
 .text            7052f650 encodeAccessAttrGoCB
 .text            7053a3f0 encodeAccessAttrInt
 .text            7053a460 encodeAccessAttrInt128
 .text            7053a2ac encodeAccessAttrQuality
 .text            70538a30 encodeAccessAttrRCB
 .text            7053a3b4 encodeAccessAttrString
 .text            7053a334 encodeAccessAttrTimeStamp
 .text            7053a428 encodeAccessAttrUInt
 .text            7053a074 encodeBoolValue
 .text            7053bcb4 encodeChildrenAccessAttrs
 .text            70538544 encodeDataSetAttrs
 .text            70538334 encodeDataSetRefAttr
 .text            7053baf8 encodeObjectAccessAttrs
 .text            7053a150 encodeOctetString8Value
 .text            7052f698 encodeReadAttrGoCB
 .text            70538ae8 encodeReadAttrRCB
 .text            7053a27c encodeReadBoolean
 .text            7053a104 encodeReadCodedEnum
 .text            7053b54c encodeReadConst
 .text            70539c68 encodeReadFloat
 .text            70539cf0 encodeReadFloatSett
 .text            7053a184 encodeReadInt32
 .text            7053a1fc encodeReadInt32Sett
 .text            7053a1cc encodeReadInt32U
 .text            7053a244 encodeReadInt32USett
 .text            70539cac encodeReadReal
 .text            70539d44 encodeReadRealSett
 .text            70539e10 encodeReadTimeStamp
 .text            7053b904 encodeSimpleDataAccessAttrs
 .text            7053b70c encodeTypeAccessAttrs
 .text            7053a038 encodeUInt32Value
 .text            7054022c ethBusGetInterface
 .text            7053fcac etharp_output
 .text            7053fc9c ethernet_input
 .text            7052d0c0 fast_memcpy
 .text            7053eb8c finalizeReportRegistration
 .text            7053b28c findDomainSection
 .text            7053b3dc findObjectByFullName
 .text            7053b370 findObjectByPath
 .text            7053b148 findObjectBySimpleName
 .text            7053b210 findObjectByTag
 .text            7053f484 free
 .text            7053f3a0 freeConnection
 .text            70534818 freeMemory
 .text            7053d5c8 freeSessionOutBuffer
 .text            7052da28 frsm_alloc
 .text            7052da4c frsm_free
 .text            7052da78 frsm_getById
 .text            7052da14 frsm_init
 .text            70530c64 fs_fileClose
 .text            70530b20 fs_fileOpen
 .text            70530ccc fs_fileRead
 .text            70530b08 fs_findClose
 .text            70530924 fs_findFirst
 .text            70530a28 fs_findNext
 .text            705308b4 fs_init
 .data            70542b90 g_DataSpdu
 .bss             70553eb0 g_FRSM
 .data            70542c1c g_reportCount
 .bss             70557f04 g_reports
 .text            7053b6a4 getAlignedDescrStruct
 .text            7053af48 getBerStringLength
 .text            7053be80 getConstDAString
 .text            7053bef8 getConstDAULong
 .text            7053cf14 getCurrentDataSliceTime
 .text            7053be00 getDAValuePos
 .text            70538610 getDataSetAccessAttrs
 .text            7053b410 getDataSetByPath
 .text            70539f1c getEnumDataValue
 .text            70539e50 getEnumValue
 .text            7053cb68 getFloatSett
 .text            7053cb0c getFloatValue
 .text            7053eb58 getFreeReport
 .text            7053af90 getIEDObjectNameString
 .text            7053cbc8 getIntSett
 .text            7053b014 getObjectName
 .text            7053da44 getRCB
 .text            7053cb98 getRealSett
 .text            7053da80 getReporterByIndex
 .text            7053b2b0 getSimpleNameLen
 .text            7053b0b0 getSubObjectsPos
 .text            7053c208 handleConfirmedRequestPdu
 .text            7053f3a4 handleIncomingConnections
 .text            70537fec handleMMSConnection
 .data            70542ac8 icdFile
 .data            70542bfc iedModel
 .data            70542c00 iedModelSize
 .text            7052abc8 incSettCounter
 .text            70537318 initCOTPConnection
 .text            7053c93c initPWin
 .text            705375f4 initPresentation
 .text            7053dadc initReportCompareDataset
 .text            7053eb2c initReports
 .text            7053d398 initSessionOutBuffers
 .text            7053fc94 ipaddr_addr
 .text            7053f8ec isInterruptEnabled
 .text            7053dab4 isRCBConnected
 .text            7053fde4 is_init
 .text            70541c04 isinf
 .text            70541c50 isnan
 .text            70537608 isoPresentation_createCpaMessage
 .text            70537734 isoPresentation_parseUserData
 .text            7053744c isoSession_createDataSpdu
 .data            70542aa0 lastApplErrorName
 .bss             70553ed4 listenSocket
 .text            7053cab8 loadIedModel
 .text            7053ca20 loadRomModule
 .text            7053f904 lockInterrupt
 .text            7053fcd4 lwip_accept
 .text            7053fcc4 lwip_bind
 .text            7053fcdc lwip_close
 .text            7053fd0c lwip_connect
 .text            7053fd3c lwip_fcntl
 .text            7053fd24 lwip_getpeername
 .text            7053fd1c lwip_getsockname
 .text            7053fc8c lwip_htonl
 .text            7053fc84 lwip_htons
 .text            7053fd2c lwip_inet_ntop
 .text            7053fd34 lwip_inet_pton
 .text            7053fd04 lwip_ioctl
 .text            7053fccc lwip_listen
 .text            7053fce4 lwip_recv
 .text            7053fcfc lwip_recvfrom
 .text            7053fd14 lwip_select
 .text            7053fcec lwip_send
 .text            7053fcf4 lwip_sendto
 .text            7053fcbc lwip_setsockopt
 .text            7053fcb4 lwip_socket
 .text            7053fdec lwiplib_init
 .text            7053fddc lwipport_init
 .text            7053f47c main
 .text            7053f480 malloc
 .text            7053fb14 memcmp
 .text            7053f9a0 memcpy
 .text            7053f9a0 memmove
 .text            7053f988 memset
 .text            70537c24 mmsProcessMessage
 .text            7053c0f4 mmsServer_handleIdentifyRequest
 .text            70537f00 mmsThread
 .text            7053c58c mms_createMmsRejectPdu
 .text            705386b0 mms_createNameListResponse
 .text            7053ae3c mms_handleFileCloseRequest
 .text            7053a87c mms_handleFileDirRequest
 .text            7053abe4 mms_handleFileOpenRequest
 .text            7053ad60 mms_handleFileReadRequest
 .text            70538644 mms_handleGetDataSetAccessAttr
 .text            705387f8 mms_handleGetNameListRequest
 .text            70538138 mms_handleGetVariableAccessAttr
 .text            70538eb8 mms_handleReadRequest
 .text            705395a8 mms_handleWriteRequest
 .text            7053fc24 netif_add
 .text            7053fc44 netif_remove
 .text            7053fc3c netif_set_default
 .text            7053fc2c netif_set_link_up
 .text            7053fc34 netif_set_up
 .text            7053fc4c netifapi_netif_common
 .text            7053fdbc nwAddNetif
 .text            7053fd7c nwEthSend
 .text            7053fd64 nwGetDefaultNetif
 .text            7053fd74 nwGetEthRecvHook
 .text            7053fdd4 nwGetFeature
 .text            7053fd9c nwGetMac
 .text            7053fd5c nwGetNetif
 .text            7053fd94 nwMtiAddFilter
 .text            7053fd8c nwMtiClear
 .text            7053fd84 nwMtiEnable
 .text            7053fda4 nwRegisterReload
 .text            7053fdb4 nwReload
 .text            7053fdc4 nwRemoveNetif
 .text            7053fdac nwRemoveReload
 .text            7053fdcc nwSetDefaultNetif
 .text            7053fd6c nwSetEthRecvHook
 .data            70542ab8 oldHWTmrIsrHandlerPtr
 .bss             70553ed8 oscHeaderBuf
 .data            70542b24 oscInfoV3
 .data            70542b58 oscInfoV4
 .bss             70553ecc osc_tlsf
 .text            7053733c parseSessionMessage
 .text            7053fc5c pbuf_alloc
 .text            7053fc7c pbuf_alloc_reference
 .text            7053fc54 pbuf_copy_partial
 .text            7053fc64 pbuf_free
 .text            7053fc74 pbuf_header
 .text            7053fc6c pbuf_take
 .text            7052dab4 prepareGOOSEbuf
 .text            70537ad0 processSessionConnect
 .text            70537d24 processSessionData
 .text            7053bd8c processSubobjects
 .text            7053cddc pwaOscFindClose
 .text            7053cd94 pwaOscFindFirst
 .text            7053cdb8 pwaOscFindNext
 .text            7053cdf8 pwaOscOscRead
 .text            7053cc00 pwaWriteFloatSett
 .text            7053cd1c pwaWriteIntSett
 .text            7053cc78 pwaWriteRealSett
 .text            70539a24 qualityFromBitsFast
 .text            7053a274 readBoolValue
 .text            7053a09c readCodedEnum
 .text            70539c0c readFloatValue
 .text            70539fe8 readIntSettValue
 .text            7053a010 readIntValue
 .text            70539c2c readRealValue
 .text            70530e0c readSocket
 .text            7053aed0 readTL
 .text            7053d9fc registerAllLogicalDeviceRCB
 .text            7053d9f4 registerAllLogicalNodeRCB
 .text            7053da1c registerAllRCB
 .text            7053d96c registerBufferedReport
 .text            7053d6ac registerConfRev
 .text            7052e4e0 registerGoCBsGivenFC
 .text            7053d70c registerOptFlds
 .text            7053d97c registerRCBsGivenFC
 .text            7053d760 registerReport
 .text            7053d5d4 registerRptID
 .text            7053d65c registerTrgOps
 .text            7053d974 registerUnbufferedReport
 .data            70542c14 reportVarNameSequence
 .text            7053d2fc sendThread
 .text            7052d948 serverMain
 .text            7053aebc setIedModel
 .data            70542a84 settCounter
 .bss             70553ef0 settsDataSlice
 .bss             70553efc settsDataSliceSize
 .text            7053af0c skipObject
 .text            705406e0 snprintf
 .text            70530d2c socketInit
 .text            705406fc sputc_limit...2Fexport.2Frelmgr.2Farmmipsv800_release.2Fcvs.2Ftrg.2Farm4.2Fobjs.2Fccsnprtf.
 .text            70530e6c startListening
 .text            7053f674 strlen
 .text            7053f8b0 strncpy
 .text            7053fd44 sys_get_def_netif
 .text            7053fd4c sys_tcpip_lock
 .text            7053fd54 sys_tcpip_unlock
 .text            7053fca4 tcpip_input
 .bss             70553ed0 tlsfCS
 .text            70534184 tlsf_add_pool
 .text            70534674 tlsf_align_size
 .text            7053468c tlsf_alloc_overhead
 .text            70534128 tlsf_block_size
 .text            70534684 tlsf_block_size_max
 .text            7053467c tlsf_block_size_min
 .text            70533e6c tlsf_check
 .text            7053414c tlsf_check_pool
 .text            7053427c tlsf_create
 .text            7053432c tlsf_create_with_pool
 .text            7053435c tlsf_destroy
 .text            705344a8 tlsf_free
 .text            70534360 tlsf_get_pool
 .text            70534370 tlsf_malloc
 .text            705343a8 tlsf_memalign
 .text            7053417c tlsf_pool_overhead
 .text            70534518 tlsf_realloc
 .text            7053422c tlsf_remove_pool
 .text            70534668 tlsf_size
 .text            705340b0 tlsf_walk_pool
 .text            7053f934 unlockInterrupt
 .text            70528768 unsout..trg.2Farm4.2Fobjs.2Flibs.2Flibansi.2Fccvsprnt.
 .text            70540724 vsnprintf
 .text            7052f718 writeAttrGoCB
 .text            70538dc4 writeAttrRCB
 .text            70539414 writeBoolean
 .text            7053b450 writeChildrenNames
 .text            70539450 writeCodedEnum
 .text            705392bc writeFloatSett
 .text            70538cf0 writeGI
 .text            70539388 writeIntSett
 .text            70538d54 writeIntgPd
 .text            70539324 writeRealSett
 .text            70530e54 writeSocket
 .text            70536f68 writeTPKTHeader
 .text            7053cb44 writeTele
 .text            70541b30 x32tod...2E.2Fobjs.2Find32tod.
 .text            70541ac0 x32tof...2E.2Fobjs.2Find32tof.
 .text            70528e28 xdecout..trg.2Farm4.2Fobjs.2Flibs.2Flibansi.2Fccllout.
 .text            70534b10 zs_entrybegin
 .text            70534e04 zs_entrydata
 .text            70534fa8 zs_entryend
 .text            705350a8 zs_finish
 .text            705349e4 zs_free
 .text            7053491c zs_init
 .text            705348a8 zs_registermethod
 .text            705331e4 zs_user_writeToStream
 .text            70534a40 zs_writeentry
