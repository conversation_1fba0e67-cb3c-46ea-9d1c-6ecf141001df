                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cfo1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=MMSServer.c -o gh_cfo1.o -list=MMSServer.lst C:\Users\<USER>\AppData\Local\Temp\gh_cfo1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_cfo1.s
Source File: MMSServer.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile MMSServer.c -o

                      11 ;		MMSServer.o

                      12 ;Source File:   MMSServer.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:09 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "main.h"


                      23 ;2: #ifdef __DEBUG


                      24 ;3: #include <Clib.h> // EnableInterrupt


                      25 ;4: #endif


                      26 ;5: 


                      27 ;6: 


                      28 ;7: #ifdef __DEBUG


                      29 ;8: void _init00(void);


                      30 ;9: unsigned int updateLoadAddr(void)


                      31 ;10: {


                      32 ;11: 	volatile unsigned int init00Addr = (unsigned int)_init00;


                      33 ;12: 	Stop();


                      34 ;13: 	return (unsigned int)init00Addr;


                      35 ;14: }


                      36 ;15: 


                      37 ;16: #endif


                      38 ;17: 


                      39 ;18: void main()


                      40 	.text

                      41 	.align	4

                      42 main::

                      43 ;19: {


                      44 

                      45 ;20: #ifdef __DEBUG	


                      46 ;21: 	DisableInterrupt();


                      47 ;22: 	updateLoadAddr();


                      48 ;23: 	EnableInterrupt();


                      49 ;24: #endif


                      50 ;25: 	



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_cfo1.s
                      51 ;26:     serverMain();


                      52 

00000000 ea000000*    53 	b	serverMain

                      54 	.endf	main

                      55 	.align	4

                      56 

                      57 	.section ".bss","awb"

                      58 .L30:

                      59 	.data

                      60 	.text

                      61 

                      62 ;27: }


                      63 	.align	4

                      64 

                      65 	.data

                      66 	.ghsnote version,6

                      67 	.ghsnote tools,3

                      68 	.ghsnote options,0

                      69 	.text

                      70 	.align	4

