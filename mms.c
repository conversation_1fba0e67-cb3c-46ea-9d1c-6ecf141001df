
#include "stddef.h"
#include "server.h"
#include "platform_thread.h"
#include "Cotp.h"
#include "acse.h"
#include "session.h"
#include "mms.h"
#include "send_thread.h"
#include "AsnEncoding.h"  
#include "mms_get_name_list.h"
#include "mms_get_variable_access_attributes.h"
#include "mmsservices.h"
#include "mms_read.h"
#include "file_system.h"
#include "out_queue.h"
#include "out_buffers.h"
#include "reports.h"
#include "connections.h"
#include "control.h"
#include <debug.h>
#include <Clib.h>

//Типы тегов Ассоциации MMS
#define	MMS_LOCAL_DETAIL_CALLING	 0x80
#define MMS_MAX_SERV_OUTSTANDING_CALLING 0x81
#define MMS_MAX_SERV_OUTSTANDING_CALLED 0x82
#define MMS_DATA_STRUCTURE_NESTING_LEVEL 0x83
#define	MMS_INIT_REQUEST_DETAIL 0xa4
#define	MMS_INIT_RESPONSE_DETAIL 0xa4
#define MMS_VERSION_NUMBER 0x80
#define MMS_PARAMETER_CBB 0x81
#define MMS_SERVICES_SUPPORTED 0x82

/**********************************************************************************************
 * MMS Server Capabilities
 *********************************************************************************************/

#define MMS_SERVICE_STATUS 0x80
#define MMS_SERVICE_GET_NAME_LIST 0x40
#define MMS_SERVICE_IDENTIFY 0x20
#define MMS_SERVICE_RENAME 0x10
#define MMS_SERVICE_READ 0x08
#define MMS_SERVICE_WRITE 0x04
#define MMS_SERVICE_GET_VARIABLE_ACCESS_ATTRIBUTES 0x02
#define MMS_SERVICE_DEFINE_NAMED_VARIABLE 0x01

#define MMS_SERVICE_DEFINE_SCATTERED_ACCESS 0x80
#define MMS_SERVICE_GET_SCATTERED_ACCESS_ATTRIBUTES 0x40
#define MMS_SERVICE_DELETE_VARIABLE_ACCESS 0x20
#define MMS_SERVICE_DEFINE_NAMED_VARIABLE_LIST 0x10
#define MMS_SERVICE_GET_NAMED_VARIABLE_LIST_ATTRIBUTES 0x08
#define MMS_SERVICE_DELETE_NAMED_VARIABLE_LIST 0x04
#define MMS_SERVICE_DEFINE_NAMED_TYPE 0x02
#define MMS_SERVICE_GET_NAMED_TYPE_ATTRIBUTES 0x01

#define MMS_SERVICE_OBTAIN_FILE 0x02

#define MMS_SERVICE_READ_JOURNAL 0x40

#define MMS_SERVICE_FILE_OPEN 0x80
#define MMS_SERVICE_FILE_READ 0x40
#define MMS_SERVICE_FILE_CLOSE 0x20
#define MMS_SERVICE_FILE_RENAME 0x01
#define MMS_SERVICE_FILE_DELETE 0x08
#define MMS_SERVICE_FILE_DIRECTORY 0x04
#define MMS_SERVICE_UNSOLICITED_STATUS 0x02
#define MMS_SERVICE_INFORMATION_REPORT 0x01

#define MMS_SERVICE_CONCLUDE 0x10
#define MMS_SERVICE_CANCEL 0x08


// servicesSupported MMS bitstring
static unsigned char servicesSupported[] =
{
        0x00
        | MMS_SERVICE_STATUS
        | MMS_SERVICE_GET_NAME_LIST    
        | MMS_SERVICE_IDENTIFY
        | MMS_SERVICE_READ
        //| MMS_SERVICE_WRITE
        | MMS_SERVICE_GET_VARIABLE_ACCESS_ATTRIBUTES
        ,
        0x00
        //| MMS_SERVICE_DEFINE_NAMED_VARIABLE_LIST
        //| MMS_SERVICE_DELETE_NAMED_VARIABLE_LIST
        //| MMS_SERVICE_GET_NAMED_VARIABLE_LIST_ATTRIBUTES
        ,
        0x00,
        0x00,
        0x00,
        0x00
        //| MMS_SERVICE_OBTAIN_FILE
        ,
        0x00,
        0x00,
        0x00
        //| MMS_SERVICE_READ_JOURNAL
        ,
        0x00
        
        | MMS_SERVICE_FILE_OPEN
        | MMS_SERVICE_FILE_READ
        | MMS_SERVICE_FILE_CLOSE
        //| MMS_SERVICE_FILE_RENAME
        //| MMS_SERVICE_FILE_DELETE
        | MMS_SERVICE_FILE_DIRECTORY
        
        | MMS_SERVICE_INFORMATION_REPORT
        ,
        0x00
        | MMS_SERVICE_CONCLUDE
        | MMS_SERVICE_CANCEL
};

/* negotiated parameter CBB */
static unsigned char parameterCBB[] =
{
        0xf1,
        0x00
};

static int encodeInitResponseDetail(unsigned char* buf, int bufPos, int encode)
{
    int initResponseDetailSize = 14 + 5 + 3;
    if (!encode)
        return initResponseDetailSize + 2;
    bufPos = BerEncoder_encodeTL(MMS_INIT_RESPONSE_DETAIL, initResponseDetailSize,
                                 buf, bufPos);
    bufPos = BerEncoder_encodeUInt32WithTL(MMS_VERSION_NUMBER, 1, buf, bufPos);
    bufPos = BerEncoder_encodeBitString(MMS_PARAMETER_CBB, 11, parameterCBB,
                                        buf, bufPos);
    bufPos = BerEncoder_encodeBitString(MMS_SERVICES_SUPPORTED, 85,
                                        servicesSupported, buf, bufPos);

    return bufPos;
}

static void mms_initConnection(MmsConnection* mmsConn)
{
	mmsConn->isFileOpen = FALSE;
}

static void mms_closeConnection(MmsConnection* mmsConn)
{
	if (mmsConn->isFileOpen)
	{
		fs_fileClose(mmsConn->frsmID);
	}
}


static void initIsoConnection(IsoConnection* isoConn)
{
    isoConn->maxServOutstandingCalling
            = DEFAULT_MAX_SERV_OUTSTANDING_CALLING;
    isoConn->maxServOutstandingCalled
            = DEFAULT_MAX_SERV_OUTSTANDING_CALLED;
    isoConn->dataStructureNestingLevel
            = DEFAULT_DATA_STRUCTURE_NESTING_LEVEL;    
    isoConn->maxPduSize = CONFIG_MMS_MAXIMUM_PDU_SIZE;
	initSessionOutBuffers(&isoConn->outBuffers);
    OutQueue_init(&isoConn->outQueue);
	mms_initConnection(&isoConn->mmsConn);
}

static int createInitiateResponse(IsoConnection* isoConn, unsigned char* buf)
{
    int bufPos = 0;
    int initiateResponseLength = 0;

    initiateResponseLength += 2 + BerEncoder_UInt32determineEncodedSize(
                isoConn->maxPduSize);
    initiateResponseLength += 2 + BerEncoder_UInt32determineEncodedSize(
                isoConn->maxServOutstandingCalling);
    initiateResponseLength += 2 + BerEncoder_UInt32determineEncodedSize(
                isoConn->maxServOutstandingCalled);
    initiateResponseLength += 2 + BerEncoder_UInt32determineEncodedSize(
                isoConn->dataStructureNestingLevel);

    initiateResponseLength += encodeInitResponseDetail(NULL, 0, 0);

    /* Initiate response pdu */
    bufPos = BerEncoder_encodeTL(MMS_INITIATE_RESPONSE_PDU,
                                 initiateResponseLength, buf, bufPos);

    bufPos = BerEncoder_encodeUInt32WithTL(MMS_LOCAL_DETAIL_CALLING,
                                           isoConn->maxPduSize, buf, bufPos);

    bufPos = BerEncoder_encodeUInt32WithTL(MMS_MAX_SERV_OUTSTANDING_CALLING,
                                           isoConn->maxServOutstandingCalling,
                                           buf, bufPos);

    bufPos = BerEncoder_encodeUInt32WithTL(MMS_MAX_SERV_OUTSTANDING_CALLED,
                                           isoConn->maxServOutstandingCalled,
                                           buf, bufPos);

    bufPos = BerEncoder_encodeUInt32WithTL(MMS_DATA_STRUCTURE_NESTING_LEVEL,
                                           isoConn->dataStructureNestingLevel,
                                           buf, bufPos);

    bufPos = encodeInitResponseDetail(buf, bufPos, 1);
    return bufPos;
}

int processSessionConnect(IsoConnection* isoConn)
{
    int acseDataLen;
    int presentationDataLen;
    int mmsDataLen;



    debugSendText("Send Accept SPDU");
    mmsDataLen = createInitiateResponse( isoConn, isoConn->isoOutBuf);
    acseDataLen = AcseConnection_createAssociateResponseMessage(
                &isoConn->acse,  isoConn->acse.outBuf, isoConn->isoOutBuf,
                mmsDataLen, ACSE_RESULT_ACCEPT);

    presentationDataLen = isoPresentation_createCpaMessage(&isoConn->presentation,
                                 isoConn->presentation.outBuf,isoConn->acse.outBuf,
                                                       acseDataLen);    

    isoConn->pCurrCotpOutBuf = allocSessionOutBuffer(&isoConn->outBuffers,
		SESSION_OUT_BUF_SIZE);
    if(isoConn->pCurrCotpOutBuf == NULL)
    {
        ERROR_REPORT("No free buffer to send");
        return -1;
    }
    return createAcceptSPDU( isoConn->pCurrCotpOutBuf->cotpOutBuf,
                                          isoConn->presentation.outBuf,presentationDataLen);
}

//outBuf - куда складывать результат
//pOutLen - куда складывать длину результата
MmsIndication mmsProcessMessage(IsoConnection* isoConn,
								unsigned char* inBuf, int* pRequestPDULen, int inLen,
                                unsigned char* outBuf, int* pOutLen)
{
    //смотри ParseMmsPacket
    //       и MmsServerConnection_parseMessage
    MmsIndication retVal;
    int bufPos = 0;
    unsigned char pduType;
    int pduLength;

    if (inLen < 2)
    {
        return MMS_ERROR;
    }

    pduType = inBuf[bufPos++];
    bufPos = BerDecoder_decodeLength(inBuf, &pduLength, bufPos, inLen);

    if (bufPos < 0)
    {
        return MMS_ERROR;
    }

	//Полная длина всего PDU вместе с тэгом и длиной
	*pRequestPDULen = bufPos + pduLength;

    switch (pduType) {
    case MMS_INITIATE_REQUEST_PDU:
        debugSendText("MMS_INITIATE_REQUEST_PDU");        
        retVal = MMS_INITIATE;
        break;
    case MMS_CONFIRMED_REQUEST_PDU:
        //debugSendText("MMS_CONFIRMED_REQUEST_PDU");		
        *pOutLen = handleConfirmedRequestPdu(isoConn, inBuf, bufPos, bufPos + pduLength,
                                            outBuf, DEFAULT_BUFFER_SIZE);
        retVal = MMS_CONFIRMED_REQUEST;
        break;
    default:
        //mmsMsg_createMmsRejectPdu(NULL, MMS_ERROR_REJECT_UNKNOWN_PDU_TYPE, response);
        debugSendUshort("Unknown MMS PDU type ", pduType);
        retVal = MMS_ERROR;
        break;
    }
    return retVal;
}

int processSessionData(IsoConnection* isoConn,
                       unsigned char* inBuf, int inLen)

{	
	int mmsInPacketLen;
	int mmsInPacketPos = 0;
    int presentationDataLen;
	int mmsOutDataLen = 0;
    unsigned char* userData;
	unsigned char* outBuf = isoConn->isoOutBuf;

    MmsIndication mmsInd;
	bool confirmedRequest = false;

    //debugSendText("processSessionData");
	mmsInPacketLen = isoPresentation_parseUserData(&isoConn->presentation,
                                                          inBuf, inLen, &userData);
	if(mmsInPacketLen == -1)
    {
       return -1;
    }

	debugSendUshort("mmsPacketLen ", mmsInPacketLen);

	//mmsInPacketLen это длина всего сообщения, которая может
	//включать в себя несколько PDU с разными invokeID	

	while(mmsInPacketPos < mmsInPacketLen)
	{
		int responsePDULen;
		int requestPDULen;
		mmsInd = mmsProcessMessage(isoConn,
								   userData, &requestPDULen, mmsInPacketLen,
								   outBuf,&responsePDULen);
		if(mmsInd == MMS_CONFIRMED_REQUEST)
		{
			confirmedRequest = true;
		}


		userData += requestPDULen;
		mmsInPacketPos += requestPDULen;
		mmsOutDataLen += responsePDULen;
		outBuf += responsePDULen;
	}


	if(confirmedRequest)
    {
        //ACSE_createMessage() ;
		debugSendUshort("MmsResponseLen:", mmsOutDataLen);
        presentationDataLen =  IsoPresentation_createUserData(&isoConn->presentation,
															  isoConn->presentation.outBuf,isoConn->isoOutBuf, mmsOutDataLen);

        isoConn->pCurrCotpOutBuf = allocSessionOutBuffer(&isoConn->outBuffers,
			SESSION_OUT_BUF_SIZE);
        if(isoConn->pCurrCotpOutBuf == NULL)
        {
            ERROR_REPORT("No free buffer to send");
            return -1;
        }
        return isoSession_createDataSpdu(isoConn->pCurrCotpOutBuf->cotpOutBuf, 
            SESSION_OUT_BUF_SIZE, isoConn->presentation.outBuf,presentationDataLen);

    }
    return -1;
}

void closeIsoConnection(IsoConnection* isoConn)
{
	COTPConnection* pCotpConn = &isoConn->cotpConn;
	mms_closeConnection(&isoConn->mmsConn);
	isoConn->connected = FALSE;
	disableDisconnectedReports();
	Control_disableWaitingObjects();
    while(isoConn->sendThreadIsRunning)
    {
        Idle();
    }
	closeServerSocket(pCotpConn->socket);
	ERROR_REPORT("Connection closed");
	
	OutQueue_done(&isoConn->outQueue);
    SessionBuffers_done(&isoConn->outBuffers);
    freeConnection(isoConn);
}

void mmsThread(IsoConnection* mmsConn)
{        
    int cotpSendDataCount = 0;       
    int byteCount;        

    mmsConn->pCurrCotpOutBuf = NULL;
    debugSendText("MMS thread started");	
    while (1) {
        debugSendUshort("COTP data to send:", cotpSendDataCount);

        if(cotpSendDataCount > 0)
        {
			mmsConn->pCurrCotpOutBuf->byteCount = cotpSendDataCount;
            if(!OutQueue_insert(&mmsConn->outQueue,
                                mmsConn->pCurrCotpOutBuf))
            {
                ERROR_REPORT("Out queue overflow");
            }
            cotpSendDataCount = 0;
        }

        //Получить данные COTP
        byteCount =  cotpReceiveData(&mmsConn->cotpConn,
                                     mmsConn->cotpInBuf, COTP_IN_BUF_SIZE);

        if( byteCount  == -1 )
        {
			closeIsoConnection(mmsConn);
            ERROR_REPORT("COTP error");			
            return;
        }
        else
        {
            unsigned char* userData;
            int userDataLen;

            IsoSessionIndication sessionIndication = parseSessionMessage(mmsConn->cotpInBuf,
                                                                         byteCount, &userData, &userDataLen);

            switch(sessionIndication)
            {
            case SESSION_CONNECT:
                cotpSendDataCount = processSessionConnect(mmsConn);
                break;
            case SESSION_DATA:
                cotpSendDataCount = processSessionData(mmsConn,userData, userDataLen);
                break;
            case SESSION_ERROR:
                debugSendText("Session error");
                cotpSendDataCount = -1;
                break;
            default:
                cotpSendDataCount = -1;
                break;
            }
            if(cotpSendDataCount == -1)
            {
				closeIsoConnection(mmsConn);                
                return;
            }
        }
    }
}


void handleMMSConnection(SERVER_SOCKET socket)
{
	IsoConnection* isoConn = allocateConnection();
	if (isoConn == NULL)
	{
		ERROR_REPORT("Unable to allocate connection");
		closeServerSocket(socket);
		return;
	}

    debugSendText("\r\n==============================\r\nTCP Connected");
    initIsoConnection(isoConn);
    initPresentation(&isoConn->presentation);
    AcseConnection_init(&isoConn->acse);
    initCOTPConnection(&isoConn->cotpConn, socket);
    debugSendText("Starting MMS thread...");    
	isoConn->connected = TRUE;    
    createThread(sendThread, isoConn);
    createThread(mmsThread, isoConn);

}

