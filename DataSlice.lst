                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c301.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=DataSlice.c -o gh_c301.o -list=DataSlice.lst C:\Users\<USER>\AppData\Local\Temp\gh_c301.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_c301.s
Source File: DataSlice.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile DataSlice.c -o

                      11 ;		DataSlice.o

                      12 ;Source File:   DataSlice.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:07 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "DataSlice.h"


                      23 ;2: #include <debug.h>


                      24 ;3: #include "../../../../DataSlice/DataSliceClient/datasliceif.h"


                      25 ;4: #include "fast_memcpy.h"


                      26 ;5: #include <stdint.h>


                      27 ;6: 


                      28 ;7: #define MAX_DATA_SLICE 16384


                      29 ;8: 


                      30 ;9: //! TODO: переименовать, добавив префикс lists (напр.listsDataSlice, listsCurrentDataSlice и тд)


                      31 ;10: //Интерфейс DataSlice


                      32 ;11: DataSliceIf* dataSliceIf;


                      33 ;12: 


                      34 ;13: DataSliceWnd* currentDataSlice;


                      35 ;14: 


                      36 ;15: #pragma alignvar (4)


                      37 ;16: uint8_t  dataSliceCopy[MAX_DATA_SLICE];


                      38 ;17: size_t dataSliceSize;


                      39 ;18: 


                      40 ;19: static OnUpdateDataSliceFunc g_oldCallBack;


                      41 ;20: static OnUpdateDataSliceFunc g_newCallBack;


                      42 ;21: 


                      43 ;22: //! dataslice для уставок


                      44 ;23: DataSliceSetts* settsDataSlice;


                      45 ;24: size_t settsDataSliceSize;


                      46 ;25: 


                      47 ;26: //! возвращает интерфейс для dataslice расчетных значений или уставок


                      48 ;27: static void *getDataSliceInterface(const char *moduleId)


                      49 ;28: {


                      50 ;29: 	 GetDataSliceIf getDataSlice;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c301.s
                      51 ;30: 	 _HANDLE hModule = _GetModuleHandle((char*)moduleId);


                      52 ;31: 	if (!hModule) return NULL;


                      53 ;32: 	


                      54 ;33: 	getDataSlice = (GetDataSliceIf)_GetProcAddress(hModule,GET_DATASLICEIF_PREFIX);


                      55 ;34: 	if (!getDataSlice) return NULL;


                      56 ;35: 	


                      57 ;36: 	return getDataSlice();


                      58 ;37: 	


                      59 ;38: }


                      60 ;39: 


                      61 ;40: bool dataSliceInit(void)


                      62 ;41: {


                      63 ;42:    


                      64 ;43: 	// расчетные значения


                      65 ;44:     dataSliceIf = getDataSliceInterface(DATA_SLICE_LISTS_MODULE_ID);


                      66 ;45: 	if (!dataSliceIf) 


                      67 ;46: 		return false;


                      68 ;47: 	// getDataSliceWndSize не возвращает 0


                      69 ;48: 	dataSliceSize = dataSliceIf->getDataSliceWndSize();


                      70 ;49: 	


                      71 ;50: 	// уставки


                      72 ;51: 	settsDataSlice = getDataSliceInterface(DATA_SLICE_SETTS_MODULE_ID);


                      73 ;52: 	if (!settsDataSlice)


                      74 ;53: 		return false;


                      75 ;54: 	


                      76 ;55: 	settsDataSliceSize = settsDataSlice->getDataSliceWndSize();


                      77 ;56: 	


                      78 ;57: 	


                      79 ;58:     return true;


                      80 ;59: }


                      81 ;60: 


                      82 ;61: void dataSliceCapture(void)


                      83 ;62: {


                      84 ;63: 	//currentDataSlice = dataSliceIf->getDataSliceWnd();


                      85 ;64: 


                      86 ;65: 	fast_memcpy(dataSliceCopy, dataSliceIf->getDataSliceWnd(), dataSliceSize);


                      87 ;66: 


                      88 ;67: 	currentDataSlice = (DataSliceWnd*)dataSliceCopy;


                      89 ;68: }


                      90 ;69: 


                      91 ;70: void dataSliceRelease(void)


                      92 

                      93 ;72: 


                      94 ;73: }


                      95 

                      96 	.text

                      97 	.align	4

                      98 getDataSliceInterface:

00000000 e92d4000     99 	stmfd	[sp]!,{lr}

00000004 eb000000*   100 	bl	_GetModuleHandle

00000008 e3500000    101 	cmp	r0,0

0000000c 0a000003    102 	beq	.L27

00000010 e3e01360    103 	mvn	r1,0x80000001

00000014 e2811c80    104 	add	r1,r1,1<<15

00000018 eb000000*   105 	bl	_GetProcAddress

0000001c e1b0c000    106 	movs	r12,r0

                     107 .L27:

00000020 03a00000    108 	moveq	r0,0

00000024 0a000001    109 	beq	.L21

                     110 .L26:

00000028 e1a0e00f    111 	mov	lr,pc


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c301.s
0000002c e12fff1c*   112 	bx	r12

                     113 .L21:

00000030 e8bd4000    114 	ldmfd	[sp]!,{lr}

00000034 e12fff1e*   115 	ret	

                     116 	.endf	getDataSliceInterface

                     117 	.align	4

                     118 ;getDataSlice	r12	local

                     119 ;hModule	r1	local

                     120 

                     121 ;moduleId	none	param

                     122 

                     123 	.section ".bss","awb"

                     124 .L76:

                     125 	.data

                     126 	.text

                     127 

                     128 

                     129 	.align	4

                     130 	.align	4

                     131 dataSliceInit::

00000038 e92d4100    132 	stmfd	[sp]!,{r8,lr}

0000003c e28f0000*   133 	adr	r0,.L173

00000040 ebffffee*   134 	bl	getDataSliceInterface

00000044 e59f817c*   135 	ldr	r8,.L174

00000048 e1b0c000    136 	movs	r12,r0

0000004c e588c000    137 	str	r12,[r8]

00000050 0a000009    138 	beq	.L95

00000054 e59cc020    139 	ldr	r12,[r12,32]

00000058 e1a0e00f    140 	mov	lr,pc

0000005c e12fff1c*   141 	bx	r12

00000060 e59f1164*   142 	ldr	r1,.L175

00000064 e5810000    143 	str	r0,[r1]

00000068 e28f0000*   144 	adr	r0,.L176

0000006c ebffffe3*   145 	bl	getDataSliceInterface

00000070 e59f8168*   146 	ldr	r8,.L177

00000074 e1b0c000    147 	movs	r12,r0

00000078 e588c000    148 	str	r12,[r8]

                     149 .L95:

0000007c 03a00000    150 	moveq	r0,0

00000080 0a000005    151 	beq	.L89

                     152 .L94:

00000084 e59cc008    153 	ldr	r12,[r12,8]

00000088 e1a0e00f    154 	mov	lr,pc

0000008c e12fff1c*   155 	bx	r12

00000090 e59f114c*   156 	ldr	r1,.L178

00000094 e5810000    157 	str	r0,[r1]

00000098 e3a00001    158 	mov	r0,1

                     159 .L89:

0000009c e8bd8100    160 	ldmfd	[sp]!,{r8,pc}

                     161 	.endf	dataSliceInit

                     162 	.align	4

                     163 ;.L157	.L161	static

                     164 ;.L158	.L162	static

                     165 

                     166 	.section ".bss","awb"

                     167 .L156:

                     168 	.data

                     169 	.text

                     170 

                     171 

                     172 	.align	4


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c301.s
                     173 	.align	4

                     174 dataSliceCapture::

000000a0 e92d4100    175 	stmfd	[sp]!,{r8,lr}

000000a4 e59f811c*   176 	ldr	r8,.L174

000000a8 e598c000    177 	ldr	r12,[r8]

000000ac e59cc004    178 	ldr	r12,[r12,4]

000000b0 e1a0e00f    179 	mov	lr,pc

000000b4 e12fff1c*   180 	bx	r12

000000b8 e59f810c*   181 	ldr	r8,.L175

000000bc e1a01000    182 	mov	r1,r0

000000c0 e5982000    183 	ldr	r2,[r8]

000000c4 e59f011c*   184 	ldr	r0,.L213

000000c8 eb000000*   185 	bl	fast_memcpy

000000cc e59f0114*   186 	ldr	r0,.L213

000000d0 e59f8114*   187 	ldr	r8,.L214

000000d4 e5880000    188 	str	r0,[r8]

000000d8 e8bd8100    189 	ldmfd	[sp]!,{r8,pc}

                     190 	.endf	dataSliceCapture

                     191 	.align	4

                     192 

                     193 	.section ".bss","awb"

                     194 .L206:

                     195 	.data

                     196 	.text

                     197 

                     198 

                     199 ;74: 


                     200 ;75: unsigned long long getCurrentDataSliceTime(void)


                     201 	.align	4

                     202 	.align	4

                     203 getCurrentDataSliceTime::

000000dc e92d4100    204 	stmfd	[sp]!,{r8,lr}

                     205 ;76: {


                     206 

                     207 ;77:     return dataSliceIf->getDataSliceWnd()->time;


                     208 

000000e0 e59f80e0*   209 	ldr	r8,.L174

000000e4 e598c000    210 	ldr	r12,[r8]

000000e8 e59cc004    211 	ldr	r12,[r12,4]

000000ec e1a0e00f    212 	mov	lr,pc

000000f0 e12fff1c*   213 	bx	r12

000000f4 e8900006    214 	ldmfd	[r0],{r1-r2}

000000f8 e1a00001    215 	mov	r0,r1

000000fc e1a01002    216 	mov	r1,r2

00000100 e8bd8100    217 	ldmfd	[sp]!,{r8,pc}

                     218 	.endf	getCurrentDataSliceTime

                     219 	.align	4

                     220 

                     221 	.section ".bss","awb"

                     222 .L238:

                     223 	.data

                     224 	.text

                     225 

                     226 ;78: }


                     227 

                     228 ;79: 


                     229 ;80: unsigned long long dataSliceGetTimeStamp(void)


                     230 	.align	4

                     231 	.align	4

                     232 dataSliceGetTimeStamp::

                     233 ;81: {



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c301.s
                     234 

                     235 ;82:     return currentDataSlice->time;


                     236 

00000104 e59f00e0*   237 	ldr	r0,.L214

00000108 e5902000    238 	ldr	r2,[r0]

0000010c e8920003    239 	ldmfd	[r2],{r0-r1}

00000110 e12fff1e*   240 	ret	

                     241 	.endf	dataSliceGetTimeStamp

                     242 	.align	4

                     243 

                     244 	.section ".bss","awb"

                     245 .L270:

                     246 	.data

                     247 	.text

                     248 

                     249 ;83: }


                     250 

                     251 ;84: 


                     252 ;85: long dataSliceGetFloatValue(uint16_t offset)


                     253 	.align	4

                     254 	.align	4

                     255 dataSliceGetFloatValue::

00000114 e92d4000    256 	stmfd	[sp]!,{lr}

00000118 e59fc0a8*   257 	ldr	r12,.L174

0000011c e24dd004    258 	sub	sp,sp,4

00000120 e59c1000    259 	ldr	r1,[r12]

00000124 e591c008    260 	ldr	r12,[r1,8]

00000128 e1a0100d    261 	mov	r1,sp

0000012c e1a0e00f    262 	mov	lr,pc

00000130 e12fff1c*   263 	bx	r12

                     264 ;86: {


                     265 

                     266 ;87:     long value;    


                     267 ;88:     if(!dataSliceIf->getAnalogValue(offset, &value))


                     268 

00000134 e3500000    269 	cmp	r0,0

00000138 159d0000    270 	ldrne	r0,[sp]

                     271 ;91:         ERROR_REPORT("DataSlice analog value error");


                     272 ;92:         TRACE("Offset = %d", offset);


                     273 ;93:     }


                     274 ;94:     return value;


                     275 

                     276 ;89:     {


                     277 

                     278 ;90:         value = 0;


                     279 

0000013c 058d0000    280 	streq	r0,[sp]

                     281 ;91:         ERROR_REPORT("DataSlice analog value error");


                     282 ;92:         TRACE("Offset = %d", offset);


                     283 ;93:     }


                     284 ;94:     return value;


                     285 

00000140 e28dd004    286 	add	sp,sp,4

00000144 e8bd8000    287 	ldmfd	[sp]!,{pc}

                     288 	.endf	dataSliceGetFloatValue

                     289 	.align	4

                     290 ;value	[sp]	local

                     291 

                     292 ;offset	none	param

                     293 

                     294 	.section ".bss","awb"


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c301.s
                     295 .L328:

                     296 	.data

                     297 	.text

                     298 

                     299 ;95: }


                     300 

                     301 ;96: 


                     302 ;97: float dataSliceGetRealValue(uint16_t offset)


                     303 	.align	4

                     304 	.align	4

                     305 dataSliceGetRealValue::

00000148 e92d4000    306 	stmfd	[sp]!,{lr}

                     307 ;98: {


                     308 

                     309 ;99:     float value;


                     310 ;100:     if(!dataSliceIf->getAnalogValue(offset, (void*)&value))


                     311 

0000014c e59fc074*   312 	ldr	r12,.L174

00000150 e24dd004    313 	sub	sp,sp,4

00000154 e59c1000    314 	ldr	r1,[r12]

00000158 e591c008    315 	ldr	r12,[r1,8]

0000015c e1a0100d    316 	mov	r1,sp

00000160 e1a0e00f    317 	mov	lr,pc

00000164 e12fff1c*   318 	bx	r12

00000168 e3500000    319 	cmp	r0,0

0000016c 159d0000    320 	ldrne	r0,[sp]

                     321 ;103:         ERROR_REPORT("DataSlice analog value error");


                     322 ;104:         TRACE("Offset = %d", offset);


                     323 ;105:     }


                     324 ;106: 


                     325 ;107:     return value;


                     326 

                     327 ;101:     {


                     328 

                     329 ;102:         value = 0;


                     330 

00000170 058d0000    331 	streq	r0,[sp]

                     332 ;103:         ERROR_REPORT("DataSlice analog value error");


                     333 ;104:         TRACE("Offset = %d", offset);


                     334 ;105:     }


                     335 ;106: 


                     336 ;107:     return value;


                     337 

00000174 e28dd004    338 	add	sp,sp,4

00000178 e8bd8000    339 	ldmfd	[sp]!,{pc}

                     340 	.endf	dataSliceGetRealValue

                     341 	.align	4

                     342 ;value	[sp]	local

                     343 

                     344 ;offset	none	param

                     345 

                     346 	.section ".bss","awb"

                     347 .L387:

                     348 	.data

                     349 	.text

                     350 

                     351 ;108: }


                     352 

                     353 ;109: 


                     354 ;110: bool dataSliceGetBoolValue(uint16_t offset)


                     355 	.align	4


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c301.s
                     356 	.align	4

                     357 dataSliceGetBoolValue::

0000017c e92d4000    358 	stmfd	[sp]!,{lr}

00000180 e59fc040*   359 	ldr	r12,.L174

00000184 e24dd004    360 	sub	sp,sp,4

00000188 e59c1000    361 	ldr	r1,[r12]

0000018c e591c00c    362 	ldr	r12,[r1,12]

00000190 e28d1003    363 	add	r1,sp,3

00000194 e1a0e00f    364 	mov	lr,pc

00000198 e12fff1c*   365 	bx	r12

                     366 ;111: {


                     367 

                     368 ;112:     bool value;    


                     369 ;113: 


                     370 ;114:     if(!dataSliceIf->getBoolValue(offset, &value))


                     371 

0000019c e3500000    372 	cmp	r0,0

000001a0 15dd0003    373 	ldrneb	r0,[sp,3]

                     374 ;117:         ERROR_REPORT("DataSlice bool value error");


                     375 ;118:         TRACE("Offset = %d", offset);


                     376 ;119:     }


                     377 ;120: 


                     378 ;121:     return value != 0;


                     379 

                     380 ;115:     {


                     381 

                     382 ;116:         value = 0;


                     383 

000001a4 05cd0003    384 	streqb	r0,[sp,3]

                     385 ;117:         ERROR_REPORT("DataSlice bool value error");


                     386 ;118:         TRACE("Offset = %d", offset);


                     387 ;119:     }


                     388 ;120: 


                     389 ;121:     return value != 0;


                     390 

000001a8 e3500000    391 	cmp	r0,0

000001ac 13a00001    392 	movne	r0,1

000001b0 e28dd004    393 	add	sp,sp,4

000001b4 e8bd8000    394 	ldmfd	[sp]!,{pc}

                     395 	.endf	dataSliceGetBoolValue

                     396 	.align	4

                     397 .L173:

                     398 ;	"DataSliceClient\000"

000001b8 61746144    399 	.data.b	68,97,116,97

000001bc 63696c53    400 	.data.b	83,108,105,99

000001c0 696c4365    401 	.data.b	101,67,108,105

000001c4 00746e65    402 	.data.b	101,110,116,0

                     403 	.align 4

                     404 

                     405 	.type	.L173,$object

                     406 	.size	.L173,4

                     407 

                     408 .L174:

000001c8 00000000*   409 	.data.w	dataSliceIf

                     410 	.type	.L174,$object

                     411 	.size	.L174,4

                     412 

                     413 .L175:

000001cc 00000000*   414 	.data.w	dataSliceSize

                     415 	.type	.L175,$object

                     416 	.size	.L175,4


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c301.s
                     417 

                     418 .L176:

                     419 ;	"DataSliceSetts\000"

000001d0 61746144    420 	.data.b	68,97,116,97

000001d4 63696c53    421 	.data.b	83,108,105,99

000001d8 74655365    422 	.data.b	101,83,101,116

000001dc 7374       423 	.data.b	116,115

000001de 00         424 	.data.b	0

000001df 00         425 	.align 4

                     426 

                     427 	.type	.L176,$object

                     428 	.size	.L176,4

                     429 

                     430 .L177:

000001e0 00000000*   431 	.data.w	settsDataSlice

                     432 	.type	.L177,$object

                     433 	.size	.L177,4

                     434 

                     435 .L178:

000001e4 00000000*   436 	.data.w	settsDataSliceSize

                     437 	.type	.L178,$object

                     438 	.size	.L178,4

                     439 

                     440 .L213:

000001e8 00000000*   441 	.data.w	dataSliceCopy

                     442 	.type	.L213,$object

                     443 	.size	.L213,4

                     444 

                     445 .L214:

000001ec 00000000*   446 	.data.w	currentDataSlice

                     447 	.type	.L214,$object

                     448 	.size	.L214,4

                     449 

                     450 	.align	4

                     451 ;value	[sp,3]	local

                     452 

                     453 ;offset	none	param

                     454 

                     455 	.section ".bss","awb"

                     456 .L461:

                     457 	.data

                     458 	.text

                     459 

                     460 ;122: }


                     461 

                     462 ;123: 


                     463 ;124: int dataSliceGetIntValue(uint16_t offset)


                     464 	.align	4

                     465 	.align	4

                     466 dataSliceGetIntValue::

000001f0 e92d4000    467 	stmfd	[sp]!,{lr}

000001f4 e51fc034*   468 	ldr	r12,.L174

000001f8 e24dd004    469 	sub	sp,sp,4

000001fc e59c1000    470 	ldr	r1,[r12]

00000200 e591c010    471 	ldr	r12,[r1,16]

00000204 e1a0100d    472 	mov	r1,sp

00000208 e1a0e00f    473 	mov	lr,pc

0000020c e12fff1c*   474 	bx	r12

                     475 ;125: {


                     476 

                     477 ;126:     long value;    



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c301.s
                     478 ;127:     if(!dataSliceIf->getIntValue(offset, &value))


                     479 

00000210 e3500000    480 	cmp	r0,0

00000214 159d0000    481 	ldrne	r0,[sp]

                     482 ;130:         ERROR_REPORT("DataSlice int value error");


                     483 ;131:         TRACE("Offset = %d", offset);


                     484 ;132:     }


                     485 ;133:     return value;


                     486 

                     487 ;128:     {


                     488 

                     489 ;129:         value = 0;


                     490 

00000218 058d0000    491 	streq	r0,[sp]

                     492 ;130:         ERROR_REPORT("DataSlice int value error");


                     493 ;131:         TRACE("Offset = %d", offset);


                     494 ;132:     }


                     495 ;133:     return value;


                     496 

0000021c e28dd004    497 	add	sp,sp,4

00000220 e8bd8000    498 	ldmfd	[sp]!,{pc}

                     499 	.endf	dataSliceGetIntValue

                     500 	.align	4

                     501 ;value	[sp]	local

                     502 

                     503 ;offset	none	param

                     504 

                     505 	.section ".bss","awb"

                     506 .L520:

                     507 	.data

                     508 	.text

                     509 

                     510 ;134: }


                     511 

                     512 ;135: 


                     513 ;136: bool DataSlice_getBoolFast(void* dataSliceWnd, uint16_t offset)


                     514 	.align	4

                     515 	.align	4

                     516 DataSlice_getBoolFast::

                     517 ;137: {


                     518 

                     519 ;138:     uint8_t* pWnd = dataSliceWnd;


                     520 

                     521 ;139:     return pWnd[offset];


                     522 

00000224 e7d00001    523 	ldrb	r0,[r0,r1]

00000228 e12fff1e*   524 	ret	

                     525 	.endf	DataSlice_getBoolFast

                     526 	.align	4

                     527 

                     528 ;dataSliceWnd	r0	param

                     529 ;offset	r1	param

                     530 

                     531 	.section ".bss","awb"

                     532 .L561:

                     533 	.data

                     534 	.text

                     535 

                     536 ;140: }


                     537 

                     538 ;141: 



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c301.s
                     539 ;142: bool DataSlice_getBoolFastCurrDS(uint16_t offset)


                     540 	.align	4

                     541 	.align	4

                     542 DataSlice_getBoolFastCurrDS::

                     543 ;143: {


                     544 

                     545 ;144: 	uint8_t* pWnd = (uint8_t*)currentDataSlice;


                     546 

0000022c e51f2048*   547 	ldr	r2,.L214

00000230 e5921000    548 	ldr	r1,[r2]

                     549 ;145: 	return pWnd[offset];


                     550 

00000234 e7d10000    551 	ldrb	r0,[r1,r0]

00000238 e12fff1e*   552 	ret	

                     553 	.endf	DataSlice_getBoolFastCurrDS

                     554 	.align	4

                     555 ;pWnd	r1	local

                     556 

                     557 ;offset	r0	param

                     558 

                     559 	.section ".bss","awb"

                     560 .L593:

                     561 	.data

                     562 	.text

                     563 

                     564 ;146: }


                     565 

                     566 ;147: 


                     567 ;148: int DataSlice_getInt32FastCurrDS(uint16_t offset)


                     568 	.align	4

                     569 	.align	4

                     570 DataSlice_getInt32FastCurrDS::

                     571 ;149: {


                     572 

                     573 ;150: 	uint8_t* pWnd = (uint8_t*)currentDataSlice;


                     574 

0000023c e51f2058*   575 	ldr	r2,.L214

00000240 e5921000    576 	ldr	r1,[r2]

                     577 ;151: 	return *(int*)(pWnd+offset);


                     578 

00000244 e7910000    579 	ldr	r0,[r1,r0]

00000248 e12fff1e*   580 	ret	

                     581 	.endf	DataSlice_getInt32FastCurrDS

                     582 	.align	4

                     583 ;pWnd	r1	local

                     584 

                     585 ;offset	r0	param

                     586 

                     587 	.section ".bss","awb"

                     588 .L622:

                     589 	.data

                     590 	.text

                     591 

                     592 ;152: }


                     593 

                     594 ;153: 


                     595 ;154: uint32_t DataSlice_getUInt32FastCurrDS(uint16_t offset)


                     596 	.align	4

                     597 	.align	4

                     598 DataSlice_getUInt32FastCurrDS::

                     599 ;155: {



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c301.s
                     600 

                     601 ;156:     uint8_t* pWnd = (uint8_t*)currentDataSlice;


                     602 

0000024c e51f2068*   603 	ldr	r2,.L214

00000250 e5921000    604 	ldr	r1,[r2]

                     605 ;157:     return *(uint32_t*)(pWnd+offset);


                     606 

00000254 e7910000    607 	ldr	r0,[r1,r0]

00000258 e12fff1e*   608 	ret	

                     609 	.endf	DataSlice_getUInt32FastCurrDS

                     610 	.align	4

                     611 ;pWnd	r1	local

                     612 

                     613 ;offset	r0	param

                     614 

                     615 	.section ".bss","awb"

                     616 .L654:

                     617 	.data

                     618 	.text

                     619 

                     620 ;158: }


                     621 

                     622 ;159: 


                     623 ;160: float DataSlice_getRealFastCurrDS(uint16_t offset)


                     624 	.align	4

                     625 	.align	4

                     626 DataSlice_getRealFastCurrDS::

                     627 ;161: {


                     628 

                     629 ;162: 	uint8_t* pWnd = (uint8_t*)currentDataSlice;


                     630 

0000025c e51f2078*   631 	ldr	r2,.L214

00000260 e5921000    632 	ldr	r1,[r2]

                     633 ;163: 	return *(float*)(pWnd+offset);


                     634 

00000264 e7910000    635 	ldr	r0,[r1,r0]

00000268 e12fff1e*   636 	ret	

                     637 	.endf	DataSlice_getRealFastCurrDS

                     638 	.align	4

                     639 ;pWnd	r1	local

                     640 

                     641 ;offset	r0	param

                     642 

                     643 	.section ".bss","awb"

                     644 .L686:

                     645 	.data

                     646 	.text

                     647 

                     648 ;164: }


                     649 

                     650 ;165: 


                     651 ;166: int DataSlice_getFixedFastCurrDS(uint16_t offset)


                     652 	.align	4

                     653 	.align	4

                     654 DataSlice_getFixedFastCurrDS::

                     655 ;167: {


                     656 

                     657 ;168: 	uint8_t* pWnd = (uint8_t*)currentDataSlice;


                     658 

0000026c e51f2088*   659 	ldr	r2,.L214

00000270 e5921000    660 	ldr	r1,[r2]


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c301.s
                     661 ;169: 	return *(int*)(pWnd+offset);


                     662 

00000274 e7910000    663 	ldr	r0,[r1,r0]

00000278 e12fff1e*   664 	ret	

                     665 	.endf	DataSlice_getFixedFastCurrDS

                     666 	.align	4

                     667 ;pWnd	r1	local

                     668 

                     669 ;offset	r0	param

                     670 

                     671 	.section ".bss","awb"

                     672 .L718:

                     673 	.data

                     674 	.text

                     675 

                     676 ;170: }


                     677 

                     678 ;171: 


                     679 ;172: 


                     680 ;173: void* DataSlice_getDataSliceWnd(void)


                     681 	.align	4

                     682 	.align	4

                     683 DataSlice_getDataSliceWnd::

0000027c e92d4100    684 	stmfd	[sp]!,{r8,lr}

                     685 ;174: {


                     686 

                     687 ;175:     return dataSliceIf->getDataSliceWnd();


                     688 

00000280 e51f80c0*   689 	ldr	r8,.L174

00000284 e598c000    690 	ldr	r12,[r8]

00000288 e59cc004    691 	ldr	r12,[r12,4]

0000028c e1a0e00f    692 	mov	lr,pc

00000290 e12fff1c*   693 	bx	r12

00000294 e8bd8100    694 	ldmfd	[sp]!,{r8,pc}

                     695 	.endf	DataSlice_getDataSliceWnd

                     696 	.align	4

                     697 

                     698 	.section ".bss","awb"

                     699 .L750:

                     700 	.data

                     701 	.text

                     702 

                     703 ;176: }


                     704 

                     705 ;177: 


                     706 ;178: int DataSlice_getBoolOffset(int offset)


                     707 	.align	4

                     708 	.align	4

                     709 DataSlice_getBoolOffset::

00000298 e92d4100    710 	stmfd	[sp]!,{r8,lr}

                     711 ;179: {


                     712 

                     713 ;180:     uint16_t dsOffset;


                     714 ;181: 


                     715 ;182:     if(offset == -1)


                     716 

0000029c e3700001    717 	cmn	r0,1

000002a0 0a000009    718 	beq	.L763

                     719 ;183:     {


                     720 

                     721 ;184:         return -1;



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c301.s
                     722 

                     723 ;185:     }


                     724 ;186:     dsOffset = dataSliceIf->getBoolOffset(offset);


                     725 

000002a4 e51f80e4*   726 	ldr	r8,.L174

000002a8 e598c000    727 	ldr	r12,[r8]

000002ac e1a00800    728 	mov	r0,r0 lsl 16

000002b0 e59cc01c    729 	ldr	r12,[r12,28]

000002b4 e1a00820    730 	mov	r0,r0 lsr 16

000002b8 e1a0e00f    731 	mov	lr,pc

000002bc e12fff1c*   732 	bx	r12

                     733 ;187:     if(dsOffset == 0xFFFF)


                     734 

000002c0 e3a01cff    735 	mov	r1,255<<8

000002c4 e28110ff    736 	add	r1,r1,255

000002c8 e1500001    737 	cmp	r0,r1

                     738 ;192:     }


                     739 ;193:     return dsOffset;


                     740 

                     741 .L763:

                     742 ;188:     {


                     743 

                     744 ;189: 		TRACE("DataSlice bool offset  %x is not found", offset);


                     745 ;190:         ERROR_REPORT("DataSlice bool offset  %d is not found", offset);


                     746 ;191:         return -1;


                     747 

000002cc 03e00000    748 	mvneq	r0,0

                     749 .L757:

000002d0 e8bd8100    750 	ldmfd	[sp]!,{r8,pc}

                     751 	.endf	DataSlice_getBoolOffset

                     752 	.align	4

                     753 ;dsOffset	r0	local

                     754 

                     755 ;offset	r0	param

                     756 

                     757 	.section ".bss","awb"

                     758 .L812:

                     759 	.data

                     760 	.text

                     761 

                     762 ;194: }


                     763 

                     764 ;195: 


                     765 ;196: int DataSlice_getIntOffset(int offset)


                     766 	.align	4

                     767 	.align	4

                     768 DataSlice_getIntOffset::

000002d4 e92d4100    769 	stmfd	[sp]!,{r8,lr}

                     770 ;197: {


                     771 

                     772 ;198: 	uint16_t dsOffset;


                     773 ;199: 


                     774 ;200: 	dsOffset = dataSliceIf->getIntOffset(offset);


                     775 

000002d8 e51f8118*   776 	ldr	r8,.L174

000002dc e598c000    777 	ldr	r12,[r8]

000002e0 e1a00800    778 	mov	r0,r0 lsl 16

000002e4 e59cc018    779 	ldr	r12,[r12,24]

000002e8 e1a00820    780 	mov	r0,r0 lsr 16

000002ec e1a0e00f    781 	mov	lr,pc

000002f0 e12fff1c*   782 	bx	r12


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c301.s
                     783 ;201: 	if(dsOffset == 0xFFFF)


                     784 

000002f4 e3a01cff    785 	mov	r1,255<<8

000002f8 e28110ff    786 	add	r1,r1,255

000002fc e1500001    787 	cmp	r0,r1

                     788 ;202: 	{


                     789 

                     790 ;203: 		ERROR_REPORT("DataSlice int offset  %d is not found", offset);


                     791 ;204: 		return -1;


                     792 

00000300 03e00000    793 	mvneq	r0,0

                     794 ;205: 	}


                     795 ;206: 	return dsOffset;


                     796 

00000304 e8bd8100    797 	ldmfd	[sp]!,{r8,pc}

                     798 	.endf	DataSlice_getIntOffset

                     799 	.align	4

                     800 ;dsOffset	r0	local

                     801 

                     802 ;offset	r0	param

                     803 

                     804 	.section ".bss","awb"

                     805 .L870:

                     806 	.data

                     807 	.text

                     808 

                     809 ;207: }


                     810 

                     811 ;208: 


                     812 ;209: int DataSlice_getAnalogOffset(int offset)


                     813 	.align	4

                     814 	.align	4

                     815 DataSlice_getAnalogOffset::

00000308 e92d4100    816 	stmfd	[sp]!,{r8,lr}

                     817 ;210: {


                     818 

                     819 ;211: 	uint16_t dsOffset;


                     820 ;212: 	dsOffset = dataSliceIf->getAnalogOffset(offset);


                     821 

0000030c e51f814c*   822 	ldr	r8,.L174

00000310 e598c000    823 	ldr	r12,[r8]

00000314 e1a00800    824 	mov	r0,r0 lsl 16

00000318 e59cc014    825 	ldr	r12,[r12,20]

0000031c e1a00820    826 	mov	r0,r0 lsr 16

00000320 e1a0e00f    827 	mov	lr,pc

00000324 e12fff1c*   828 	bx	r12

                     829 ;213: 	if(dsOffset == 0xFFFF)


                     830 

00000328 e3a01cff    831 	mov	r1,255<<8

0000032c e28110ff    832 	add	r1,r1,255

00000330 e1500001    833 	cmp	r0,r1

                     834 ;214: 	{


                     835 

                     836 ;215: 		ERROR_REPORT("DataSlice analog offset  %d is not found", offset);


                     837 ;216: 		return -1;


                     838 

00000334 03e00000    839 	mvneq	r0,0

                     840 ;217: 	}


                     841 ;218: 	return dsOffset;


                     842 

00000338 e8bd8100    843 	ldmfd	[sp]!,{r8,pc}


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c301.s
                     844 	.endf	DataSlice_getAnalogOffset

                     845 	.align	4

                     846 ;dsOffset	r0	local

                     847 

                     848 ;offset	r0	param

                     849 

                     850 	.section ".bss","awb"

                     851 .L918:

                     852 	.data

                     853 	.text

                     854 

                     855 ;219: }


                     856 

                     857 ;220: 


                     858 ;221: static void DataSlice_callback()


                     859 	.align	4

                     860 	.align	4

                     861 	.align	4

                     862 DataSlice_callback:

0000033c e92d4010    863 	stmfd	[sp]!,{r4,lr}

                     864 ;222: {	


                     865 

                     866 ;223:     g_newCallBack();


                     867 

00000340 e59f4050*   868 	ldr	r4,.L979

00000344 e594c004    869 	ldr	r12,[r4,4]

00000348 e1a0e00f    870 	mov	lr,pc

0000034c e12fff1c*   871 	bx	r12

                     872 ;224:     if(g_oldCallBack)


                     873 

00000350 e594c000    874 	ldr	r12,[r4]

00000354 e35c0000    875 	cmp	r12,0

                     876 ;225:     {


                     877 

                     878 ;226:         g_oldCallBack();


                     879 

00000358 11a0e00f    880 	movne	lr,pc

0000035c 112fff1c*   881 	bxne	r12

00000360 e8bd4010    882 	ldmfd	[sp]!,{r4,lr}

00000364 e12fff1e*   883 	ret	

                     884 	.endf	DataSlice_callback

                     885 	.align	4

                     886 

                     887 	.section ".bss","awb"

                     888 .L968:

00000000 00000000    889 g_oldCallBack:	.space	4

00000004 00000000    890 g_newCallBack:	.space	4

                     891 	.data

                     892 	.text

                     893 

                     894 ;227:     }


                     895 ;228: }


                     896 

                     897 ;229: 


                     898 ;230: void DataSlice_setCallBack(void (*func)(void))


                     899 	.align	4

                     900 	.align	4

                     901 DataSlice_setCallBack::

00000368 e92d4110    902 	stmfd	[sp]!,{r4,r8,lr}

0000036c e59f4028*   903 	ldr	r4,.L1012

                     904 ;231: {



                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c301.s
                     905 

                     906 ;232:     g_newCallBack = func;


                     907 

00000370 e51f81b0*   908 	ldr	r8,.L174

00000374 e5840004    909 	str	r0,[r4,4]

00000378 e598c000    910 	ldr	r12,[r8]

0000037c e59f001c*   911 	ldr	r0,.L1013

                     912 ;233:     g_oldCallBack = dataSliceIf->setOnUpdateCallback(DataSlice_callback);


                     913 

00000380 e59cc000    914 	ldr	r12,[r12]

00000384 e1a0e00f    915 	mov	lr,pc

00000388 e12fff1c*   916 	bx	r12

0000038c e5840000    917 	str	r0,[r4]

00000390 e8bd8110    918 	ldmfd	[sp]!,{r4,r8,pc}

                     919 	.endf	DataSlice_setCallBack

                     920 	.align	4

                     921 

                     922 ;func	r12	param

                     923 

                     924 	.data

                     925 	.text

                     926 

                     927 ;234: }


                     928 	.align	4

                     929 	.align	4

                     930 dataSliceRelease::

                     931 ;71: {


                     932 

00000394 e12fff1e*   933 	ret	

                     934 	.endf	dataSliceRelease

                     935 	.align	4

                     936 

                     937 	.section ".bss","awb"

                     938 .L1038:

                     939 	.data

                     940 	.text

                     941 	.align	4

                     942 .L979:

00000398 00000000*   943 	.data.w	.L968

                     944 	.type	.L979,$object

                     945 	.size	.L979,4

                     946 

                     947 .L1012:

0000039c 00000000*   948 	.data.w	g_oldCallBack

                     949 	.type	.L1012,$object

                     950 	.size	.L1012,4

                     951 

                     952 .L1013:

000003a0 00000000*   953 	.data.w	DataSlice_callback

                     954 	.type	.L1013,$object

                     955 	.size	.L1013,4

                     956 

                     957 	.align	4

                     958 ;g_oldCallBack	g_oldCallBack	static

                     959 ;g_newCallBack	g_newCallBack	static

                     960 

                     961 	.data

                     962 	.comm	dataSliceIf,4,4

                     963 	.type	dataSliceIf,$object

                     964 	.size	dataSliceIf,4

                     965 	.comm	currentDataSlice,4,4


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_c301.s
                     966 	.type	currentDataSlice,$object

                     967 	.size	currentDataSlice,4

                     968 	.comm	dataSliceCopy,16384,4

                     969 	.type	dataSliceCopy,$object

                     970 	.size	dataSliceCopy,16384

                     971 	.comm	dataSliceSize,4,4

                     972 	.type	dataSliceSize,$object

                     973 	.size	dataSliceSize,4

                     974 	.comm	settsDataSlice,4,4

                     975 	.type	settsDataSlice,$object

                     976 	.size	settsDataSlice,4

                     977 	.comm	settsDataSliceSize,4,4

                     978 	.type	settsDataSliceSize,$object

                     979 	.size	settsDataSliceSize,4

                     980 	.ghsnote version,6

                     981 	.ghsnote tools,3

                     982 	.ghsnote options,0

                     983 	.text

                     984 	.align	4

                     985 	.section ".bss","awb"

                     986 	.align	4

                     987 	.text

