                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_84g1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=OscReadFileContext.c -o fs\gh_84g1.o -list=fs/OscReadFileContext.lst C:\Users\<USER>\AppData\Local\Temp\gh_84g1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_84g1.s
Source File: OscReadFileContext.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		fs/OscReadFileContext.c -o fs/OscReadFileContext.o

                      12 ;Source File:   fs/OscReadFileContext.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:59 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include <stdbool.h>


                      23 ;2: #include <stdint.h>


                      24 ;3: #include <stdio.h>


                      25 ;4: #include <string.h>


                      26 ;5: #include "OscReadFileContext.h"


                      27 ;6: #include "OscInfo.h"


                      28 ;7: #include "OscFiles.h"


                      29 ;8: 


                      30 ;9: int zs_user_writeToStream(int fd, void *data, int size)


                      31 	.text

                      32 	.align	4

                      33 zs_user_writeToStream::

00000000 e92d4010     34 	stmfd	[sp]!,{r4,lr}

                      35 ;10: {


                      36 

                      37 ;11: 	OscReadFileContext *readFileContext = (OscReadFileContext*)fd;


                      38 

00000004 e1a04002     39 	mov	r4,r2

00000008 e2800e4a     40 	add	r0,r0,0x04a0

0000000c eb000000*    41 	bl	OscWriteBuffer_write

                      42 ;12: 	if (OscWriteBuffer_write(&readFileContext->streamBuffer, data, size))


                      43 

00000010 e3500000     44 	cmp	r0,0

                      45 ;13: 	{


                      46 

                      47 ;14: 		return size;


                      48 

00000014 11a00004     49 	movne	r0,r4

                      50 ;15: 	}



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_84g1.s
                      51 ;16: 	else


                      52 ;17: 	{


                      53 

                      54 ;18: 		return 0;


                      55 

00000018 e8bd8010     56 	ldmfd	[sp]!,{r4,pc}

                      57 	.endf	zs_user_writeToStream

                      58 	.align	4

                      59 

                      60 ;fd	r0	param

                      61 ;data	none	param

                      62 ;size	r4	param

                      63 

                      64 	.section ".bss","awb"

                      65 .L53:

                      66 	.data

                      67 	.text

                      68 

                      69 ;19: 	}


                      70 ;20: }


                      71 

                      72 ;21: static ZIPentry *initZipEntry(OscReadFileContext *readFileContext)


                      73 	.align	4

                      74 	.align	4

                      75 initZipEntry:

0000001c e92d4000     76 	stmfd	[sp]!,{lr}

00000020 e24dd00c     77 	sub	sp,sp,12

                      78 ;22: {


                      79 

                      80 ;23: 	int64_t writeStatus;


                      81 ;24: 	return zs_entrybegin(readFileContext->zipStream,


                      82 

00000024 e28d1004     83 	add	r1,sp,4

00000028 e58d1000     84 	str	r1,[sp]

0000002c e5902008     85 	ldr	r2,[r0,8]

00000030 e280101c     86 	add	r1,r0,28

00000034 e59004b4     87 	ldr	r0,[r0,1204]

00000038 e3a03000     88 	mov	r3,0

0000003c eb000000*    89 	bl	zs_entrybegin

00000040 e28dd00c     90 	add	sp,sp,12

00000044 e8bd4000     91 	ldmfd	[sp]!,{lr}

00000048 e12fff1e*    92 	ret	

                      93 	.endf	initZipEntry

                      94 	.align	4

                      95 ;writeStatus	[sp,4]	local

                      96 

                      97 ;readFileContext	r0	param

                      98 

                      99 	.section ".bss","awb"

                     100 .L94:

                     101 	.data

                     102 	.text

                     103 

                     104 ;25: 		readFileContext->fileName,


                     105 ;26: 		readFileContext->fileInfo.t,


                     106 ;27: 		ZS_STORE, 


                     107 ;28: 		&writeStatus);


                     108 ;29: }


                     109 

                     110 ;30: static bool writeZipEntry(OscReadFileContext *readFileContext, ZIPentry *zipEntry,


                     111 	.align	4


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_84g1.s
                     112 	.align	4

                     113 writeZipEntry:

0000004c e92d40f0    114 	stmfd	[sp]!,{r4-r7,lr}

                     115 ;31: 	OscWriteBuffer *wb)


                     116 ;32: {


                     117 

                     118 ;33: 	unsigned char *data = OscWriteBuffer_data(wb);


                     119 

00000050 e24dd010    120 	sub	sp,sp,16

00000054 e1a05001    121 	mov	r5,r1

00000058 e1a07000    122 	mov	r7,r0

0000005c e1a04002    123 	mov	r4,r2

00000060 e1a00004    124 	mov	r0,r4

00000064 eb000000*   125 	bl	OscWriteBuffer_data

00000068 e1a06000    126 	mov	r6,r0

                     127 ;34: 	size_t dataLen = OscWriteBuffer_dataLen(wb);


                     128 

0000006c e1a00004    129 	mov	r0,r4

00000070 eb000000*   130 	bl	OscWriteBuffer_dataLen

00000074 e3a02000    131 	mov	r2,0

00000078 e28d1008    132 	add	r1,sp,8

0000007c e52d0004    133 	str	r0,[sp,-4]!

00000080 e1a00002    134 	mov	r0,r2

00000084 e98d0003    135 	stmfa	[sp],{r0-r1}

00000088 e8bd0008    136 	ldmfd	[sp]!,{r3}

0000008c e1a02006    137 	mov	r2,r6

00000090 e59704b4    138 	ldr	r0,[r7,1204]

00000094 e1a01005    139 	mov	r1,r5

00000098 eb000000*   140 	bl	zs_entrydata

                     141 ;35: 	int64_t writeStatus;


                     142 ;36: 	if (!zs_entrydata(readFileContext->zipStream, zipEntry, data, dataLen, &writeStatus))


                     143 

0000009c e3500000    144 	cmp	r0,0

                     145 ;37: 	{


                     146 

                     147 ;38: 		return false;


                     148 

000000a0 020000ff    149 	andeq	r0,r0,255

000000a4 0a000002    150 	beq	.L101

                     151 ;39: 	}


                     152 ;40: 	OscWriteBuffer_reset(wb);


                     153 

000000a8 e1a00004    154 	mov	r0,r4

000000ac eb000000*   155 	bl	OscWriteBuffer_reset

                     156 ;41: 	return true;


                     157 

000000b0 e3a00001    158 	mov	r0,1

                     159 .L101:

000000b4 e28dd010    160 	add	sp,sp,16

000000b8 e8bd40f0    161 	ldmfd	[sp]!,{r4-r7,lr}

000000bc e12fff1e*   162 	ret	

                     163 	.endf	writeZipEntry

                     164 	.align	4

                     165 ;data	r6	local

                     166 ;writeStatus	[sp,8]	local

                     167 

                     168 ;readFileContext	r7	param

                     169 ;zipEntry	r5	param

                     170 ;wb	r4	param

                     171 

                     172 	.section ".bss","awb"


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_84g1.s
                     173 .L149:

                     174 	.data

                     175 	.text

                     176 

                     177 ;42: }


                     178 

                     179 ;43: 


                     180 ;44: static bool flushZipEntry(OscReadFileContext *readFileContext, ZIPentry *zipEntry)


                     181 	.align	4

                     182 	.align	4

                     183 flushZipEntry:

000000c0 e92d4000    184 	stmfd	[sp]!,{lr}

000000c4 e24dd008    185 	sub	sp,sp,8

                     186 ;45: {


                     187 

                     188 ;46: 	int64_t writeStatus;


                     189 ;47: 	return zs_entryend(readFileContext->zipStream, zipEntry, &writeStatus) != NULL;


                     190 

000000c8 e59004b4    191 	ldr	r0,[r0,1204]

000000cc e1a0200d    192 	mov	r2,sp

000000d0 eb000000*   193 	bl	zs_entryend

000000d4 e3500000    194 	cmp	r0,0

000000d8 13a00001    195 	movne	r0,1

000000dc e28dd008    196 	add	sp,sp,8

000000e0 e8bd4000    197 	ldmfd	[sp]!,{lr}

000000e4 e12fff1e*   198 	ret	

                     199 	.endf	flushZipEntry

                     200 	.align	4

                     201 ;writeStatus	[sp]	local

                     202 

                     203 ;readFileContext	r0	param

                     204 ;zipEntry	none	param

                     205 

                     206 	.section ".bss","awb"

                     207 .L193:

                     208 	.data

                     209 	.text

                     210 

                     211 ;48: }	


                     212 

                     213 ;49: 


                     214 ;50: OscReadFileContext * OscReadFileContext_create(PWFileInfo *fileInfo)


                     215 	.align	4

                     216 	.align	4

                     217 OscReadFileContext_create::

000000e8 e92d4030    218 	stmfd	[sp]!,{r4-r5,lr}

000000ec e1a05000    219 	mov	r5,r0

                     220 ;51: {	


                     221 

                     222 ;52: 	


                     223 ;53: 	OscReadFileContext *readFileContext =  OscFiles_malloc(sizeof(OscReadFileContext));


                     224 

000000f0 e3a00e40    225 	mov	r0,1<<10

000000f4 e28000c4    226 	add	r0,r0,196

000000f8 eb000000*   227 	bl	OscFiles_malloc

000000fc e1b04000    228 	movs	r4,r0

                     229 ;54: 	if (!readFileContext)


                     230 

00000100 0a00002f    231 	beq	.L220

                     232 ;55: 	{


                     233 


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_84g1.s
                     234 ;56: 		return NULL;


                     235 

                     236 ;57: 	}


                     237 ;58: 	memset(readFileContext, 0, sizeof(OscReadFileContext));


                     238 

00000104 e3a02e40    239 	mov	r2,1<<10

00000108 e28220c4    240 	add	r2,r2,196

0000010c e3a01000    241 	mov	r1,0

00000110 eb000000*   242 	bl	memset

                     243 ;59: 	memcpy(&readFileContext->fileInfo, fileInfo, sizeof(PWFileInfo));


                     244 

00000114 e1a01005    245 	mov	r1,r5

00000118 e1a00004    246 	mov	r0,r4

0000011c e3a0201c    247 	mov	r2,28

00000120 eb000000*   248 	bl	memcpy

                     249 ;60: 


                     250 ;61: 	readFileContext->freqCfgCount = ~0;


                     251 

00000124 e3e00000    252 	mvn	r0,0

00000128 e584004c    253 	str	r0,[r4,76]

                     254 ;62: 


                     255 ;63: 	// zip архив


                     256 ;64: 	readFileContext->zipStream = zs_init((int)readFileContext, NULL);


                     257 

0000012c e1a00004    258 	mov	r0,r4

00000130 e3a01000    259 	mov	r1,0

00000134 eb000000*   260 	bl	zs_init

00000138 e58404b4    261 	str	r0,[r4,1204]

                     262 ;65: 	if (!readFileContext->zipStream)


                     263 

0000013c e3500000    264 	cmp	r0,0

00000140 0a00001b    265 	beq	.L221

                     266 ;66: 	{


                     267 

                     268 ;67: 		OscReadFileContext_destroy(readFileContext);


                     269 

                     270 ;68: 		return NULL;


                     271 

                     272 ;69: 	}


                     273 ;70: 


                     274 ;71: 


                     275 ;72: 	if (!OscWriteBuffer_create(&readFileContext->cfgBuffer, CFG_BUFFER_SIZE))


                     276 

00000144 e2840e45    277 	add	r0,r4,0x0450

00000148 e3a01c80    278 	mov	r1,1<<15

0000014c eb000000*   279 	bl	OscWriteBuffer_create

00000150 e3500000    280 	cmp	r0,0

00000154 0a000016    281 	beq	.L221

                     282 ;73: 	{


                     283 

                     284 ;74: 		OscReadFileContext_destroy(readFileContext);


                     285 

                     286 ;75: 		return NULL;


                     287 

                     288 ;76: 	}


                     289 ;77: 	


                     290 ;78: 	if (!OscWriteBuffer_create(&readFileContext->freqCfgBuffer, FREQ_CFG_BUFFER_SIZE))


                     291 

00000158 e2840e40    292 	add	r0,r4,1<<10

0000015c e280008c    293 	add	r0,r0,140

00000160 e3a01c80    294 	mov	r1,1<<15


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_84g1.s
00000164 eb000000*   295 	bl	OscWriteBuffer_create

00000168 e3500000    296 	cmp	r0,0

0000016c 0a000010    297 	beq	.L221

                     298 ;79: 	{


                     299 

                     300 ;80: 		OscReadFileContext_destroy(readFileContext);


                     301 

                     302 ;81: 		return NULL;


                     303 

                     304 ;82: 	}


                     305 ;83: 


                     306 ;84: 	if (!OscWriteBuffer_create(&readFileContext->datBuffer, DAT_PERIOD_BUFFER_SIZE))


                     307 

00000170 e2840e40    308 	add	r0,r4,1<<10

00000174 e2800064    309 	add	r0,r0,100

00000178 e3a01c40    310 	mov	r1,1<<14

0000017c eb000000*   311 	bl	OscWriteBuffer_create

00000180 e3500000    312 	cmp	r0,0

00000184 0a00000a    313 	beq	.L221

                     314 ;85: 	{


                     315 

                     316 ;86: 		OscReadFileContext_destroy(readFileContext);


                     317 

                     318 ;87: 		return NULL;


                     319 

                     320 ;88: 	}


                     321 ;89: 	


                     322 ;90: 


                     323 ;91: 	if (!OscWriteBuffer_create(&readFileContext->hdrBuffer, HDR_BUFFER_SIZE))


                     324 

00000188 e2840e40    325 	add	r0,r4,1<<10

0000018c e2800078    326 	add	r0,r0,120

00000190 e3a01040    327 	mov	r1,64

00000194 eb000000*   328 	bl	OscWriteBuffer_create

00000198 e3500000    329 	cmp	r0,0

0000019c 0a000004    330 	beq	.L221

                     331 ;92: 	{


                     332 

                     333 ;93: 		OscReadFileContext_destroy(readFileContext);


                     334 

                     335 ;94: 		return NULL;


                     336 

                     337 ;95: 	}


                     338 ;96: 	


                     339 ;97: 


                     340 ;98: 	if (!OscWriteBuffer_create(&readFileContext->streamBuffer, STREAM_BUFFER_SIZE))


                     341 

000001a0 e2840e4a    342 	add	r0,r4,0x04a0

000001a4 e3a01b80    343 	mov	r1,1<<17

000001a8 eb000000*   344 	bl	OscWriteBuffer_create

000001ac e3500000    345 	cmp	r0,0

000001b0 1a000003    346 	bne	.L220

                     347 .L221:

                     348 ;99: 	{


                     349 

                     350 ;100: 		OscReadFileContext_destroy(readFileContext);


                     351 

000001b4 e1a00004    352 	mov	r0,r4

000001b8 eb000003*   353 	bl	OscReadFileContext_destroy

                     354 ;101: 		return NULL;


                     355 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_84g1.s
000001bc e3a00000    356 	mov	r0,0

000001c0 ea000000    357 	b	.L200

                     358 .L220:

                     359 ;102: 	}


                     360 ;103: 


                     361 ;104: 	return readFileContext;


                     362 

000001c4 e1a00004    363 	mov	r0,r4

                     364 .L200:

000001c8 e8bd8030    365 	ldmfd	[sp]!,{r4-r5,pc}

                     366 	.endf	OscReadFileContext_create

                     367 	.align	4

                     368 ;readFileContext	r4	local

                     369 

                     370 ;fileInfo	r5	param

                     371 

                     372 	.section ".bss","awb"

                     373 .L343:

                     374 	.data

                     375 	.text

                     376 

                     377 ;105: }


                     378 

                     379 ;106: 


                     380 ;107: void OscReadFileContext_destroy(OscReadFileContext * readFileContext)


                     381 	.align	4

                     382 	.align	4

                     383 OscReadFileContext_destroy::

000001cc e92d4010    384 	stmfd	[sp]!,{r4,lr}

000001d0 e1a04000    385 	mov	r4,r0

                     386 ;108: {


                     387 

                     388 ;109: 	zs_free(readFileContext->zipStream);


                     389 

000001d4 e59404b4    390 	ldr	r0,[r4,1204]

000001d8 eb000000*   391 	bl	zs_free

                     392 ;110: 


                     393 ;111: 	OscWriteBuffer_destroy(&readFileContext->cfgBuffer);


                     394 

000001dc e2840e45    395 	add	r0,r4,0x0450

000001e0 eb000000*   396 	bl	OscWriteBuffer_destroy

                     397 ;112: 	OscWriteBuffer_destroy(&readFileContext->freqCfgBuffer);


                     398 

000001e4 e2840e40    399 	add	r0,r4,1<<10

000001e8 e280008c    400 	add	r0,r0,140

000001ec eb000000*   401 	bl	OscWriteBuffer_destroy

                     402 ;113: 	OscWriteBuffer_destroy(&readFileContext->datBuffer);


                     403 

000001f0 e2840e40    404 	add	r0,r4,1<<10

000001f4 e2800064    405 	add	r0,r0,100

000001f8 eb000000*   406 	bl	OscWriteBuffer_destroy

                     407 ;114: 	OscWriteBuffer_destroy(&readFileContext->streamBuffer);


                     408 

000001fc e2840e4a    409 	add	r0,r4,0x04a0

00000200 eb000000*   410 	bl	OscWriteBuffer_destroy

                     411 ;115: 	OscWriteBuffer_destroy(&readFileContext->hdrBuffer);


                     412 

00000204 e2840e40    413 	add	r0,r4,1<<10

00000208 e2800078    414 	add	r0,r0,120

0000020c eb000000*   415 	bl	OscWriteBuffer_destroy

                     416 ;116: 	OscFiles_free(readFileContext);



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_84g1.s
                     417 

00000210 e1a00004    418 	mov	r0,r4

00000214 e8bd4010    419 	ldmfd	[sp]!,{r4,lr}

00000218 ea000000*   420 	b	OscFiles_free

                     421 	.endf	OscReadFileContext_destroy

                     422 	.align	4

                     423 

                     424 ;readFileContext	r4	param

                     425 

                     426 	.section ".bss","awb"

                     427 .L382:

                     428 	.data

                     429 	.text

                     430 

                     431 ;117: }


                     432 

                     433 ;118: 


                     434 ;119: 


                     435 ;120: bool OscReadFileContext_writeCfgToStream(OscReadFileContext * readFileContext)


                     436 	.align	4

                     437 	.align	4

                     438 OscReadFileContext_writeCfgToStream::

0000021c e92d4010    439 	stmfd	[sp]!,{r4,lr}

00000220 e1a04000    440 	mov	r4,r0

                     441 ;121: {


                     442 

                     443 ;122: 


                     444 ;123: 	if (!readFileContext->zipCfg)


                     445 

00000224 e59414bc    446 	ldr	r1,[r4,1212]

00000228 e3510000    447 	cmp	r1,0

0000022c 1a00000a    448 	bne	.L391

                     449 ;124: 	{


                     450 

                     451 ;125: 		snprintf(readFileContext->fileName, sizeof(readFileContext->fileName),


                     452 

00000230 e5943000    453 	ldr	r3,[r4]

00000234 e28f2000*   454 	adr	r2,.L485

00000238 e284001c    455 	add	r0,r4,28

0000023c e3a01010    456 	mov	r1,16

00000240 eb000000*   457 	bl	snprintf

                     458 ;126: 			"%d.cfg", readFileContext->fileInfo.name);


                     459 ;127: 		readFileContext->zipCfg = initZipEntry(readFileContext);


                     460 

00000244 e1a00004    461 	mov	r0,r4

00000248 ebffff73*   462 	bl	initZipEntry

0000024c e58404bc    463 	str	r0,[r4,1212]

00000250 e1a01000    464 	mov	r1,r0

                     465 ;128: 		if (!readFileContext->zipCfg)


                     466 

00000254 e3500000    467 	cmp	r0,0

                     468 ;129: 		{


                     469 

                     470 ;130: 			return false;


                     471 

00000258 020000ff    472 	andeq	r0,r0,255

                     473 .L391:

                     474 ;131: 		}


                     475 ;132: 	}


                     476 ;133: 


                     477 ;134: 	return writeZipEntry(readFileContext, readFileContext->zipCfg, &readFileContext->cfgBuffer);



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_84g1.s
                     478 

0000025c 12842e45    479 	addne	r2,r4,0x0450

00000260 11a00004    480 	movne	r0,r4

00000264 18bd4010    481 	ldmnefd	[sp]!,{r4,lr}

00000268 1affff77*   482 	bne	writeZipEntry

                     483 .L389:

0000026c e8bd8010    484 	ldmfd	[sp]!,{r4,pc}

                     485 	.endf	OscReadFileContext_writeCfgToStream

                     486 	.align	4

                     487 ;.L467	.L470	static

                     488 

                     489 ;readFileContext	r4	param

                     490 

                     491 	.section ".bss","awb"

                     492 .L466:

                     493 	.data

                     494 	.text

                     495 

                     496 ;135: 	


                     497 ;136: }


                     498 

                     499 ;137: 


                     500 ;138: bool OscReadFileContext_closeCfg(OscReadFileContext * readFileContext)


                     501 	.align	4

                     502 	.align	4

                     503 OscReadFileContext_closeCfg::

                     504 ;139: {


                     505 

                     506 ;140: 	return flushZipEntry(readFileContext, readFileContext->zipCfg);


                     507 

00000270 e59014bc    508 	ldr	r1,[r0,1212]

00000274 eaffff91*   509 	b	flushZipEntry

                     510 	.endf	OscReadFileContext_closeCfg

                     511 	.align	4

                     512 

                     513 ;readFileContext	none	param

                     514 

                     515 	.section ".bss","awb"

                     516 .L513:

                     517 	.data

                     518 	.text

                     519 

                     520 ;141: }


                     521 

                     522 ;142: 


                     523 ;143: bool OscReadFileContext_writeDatToStream(OscReadFileContext * readFileContext)


                     524 	.align	4

                     525 	.align	4

                     526 OscReadFileContext_writeDatToStream::

00000278 e92d4010    527 	stmfd	[sp]!,{r4,lr}

0000027c e1a04000    528 	mov	r4,r0

                     529 ;144: {


                     530 

                     531 ;145: 	if (!readFileContext->zipDat)


                     532 

00000280 e59414b8    533 	ldr	r1,[r4,1208]

00000284 e3510000    534 	cmp	r1,0

00000288 1a00000a    535 	bne	.L522

                     536 ;146: 	{


                     537 

                     538 ;147: 		snprintf(readFileContext->fileName, sizeof(readFileContext->fileName),



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_84g1.s
                     539 

0000028c e5943000    540 	ldr	r3,[r4]

00000290 e28f2000*   541 	adr	r2,.L613

00000294 e284001c    542 	add	r0,r4,28

00000298 e3a01010    543 	mov	r1,16

0000029c eb000000*   544 	bl	snprintf

                     545 ;148: 			"%d.dat", readFileContext->fileInfo.name);


                     546 ;149: 		readFileContext->zipDat = initZipEntry(readFileContext);


                     547 

000002a0 e1a00004    548 	mov	r0,r4

000002a4 ebffff5c*   549 	bl	initZipEntry

000002a8 e58404b8    550 	str	r0,[r4,1208]

000002ac e1a01000    551 	mov	r1,r0

                     552 ;150: 		if (!readFileContext->zipDat)


                     553 

000002b0 e3500000    554 	cmp	r0,0

                     555 ;151: 		{


                     556 

                     557 ;152: 			return false;


                     558 

000002b4 020000ff    559 	andeq	r0,r0,255

                     560 .L522:

                     561 ;153: 		}


                     562 ;154: 	}


                     563 ;155: 	return writeZipEntry(readFileContext, readFileContext->zipDat, &readFileContext->datBuffer);


                     564 

000002b8 12840e40    565 	addne	r0,r4,1<<10

000002bc 12802064    566 	addne	r2,r0,100

000002c0 11a00004    567 	movne	r0,r4

000002c4 18bd4010    568 	ldmnefd	[sp]!,{r4,lr}

000002c8 1affff5f*   569 	bne	writeZipEntry

                     570 .L520:

000002cc e8bd8010    571 	ldmfd	[sp]!,{r4,pc}

                     572 	.endf	OscReadFileContext_writeDatToStream

                     573 	.align	4

                     574 ;.L595	.L598	static

                     575 

                     576 ;readFileContext	r4	param

                     577 

                     578 	.section ".bss","awb"

                     579 .L594:

                     580 	.data

                     581 	.text

                     582 

                     583 ;156: }


                     584 

                     585 ;157: bool OscReadFileContext_closeDat(OscReadFileContext * readFileContext)


                     586 	.align	4

                     587 	.align	4

                     588 OscReadFileContext_closeDat::

                     589 ;158: {


                     590 

                     591 ;159: 	return flushZipEntry(readFileContext, readFileContext->zipDat);


                     592 

000002d0 e59014b8    593 	ldr	r1,[r0,1208]

000002d4 eaffff79*   594 	b	flushZipEntry

                     595 	.endf	OscReadFileContext_closeDat

                     596 	.align	4

                     597 

                     598 ;readFileContext	none	param

                     599 


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_84g1.s
                     600 	.section ".bss","awb"

                     601 .L641:

                     602 	.data

                     603 	.text

                     604 

                     605 ;160: }


                     606 

                     607 ;161: 


                     608 ;162: bool  OscReadFileContext_writeHdrToStream(OscReadFileContext * readFileContext)


                     609 	.align	4

                     610 	.align	4

                     611 OscReadFileContext_writeHdrToStream::

000002d8 e92d4010    612 	stmfd	[sp]!,{r4,lr}

000002dc e1a04000    613 	mov	r4,r0

                     614 ;163: {


                     615 

                     616 ;164: 	if (!readFileContext->zipHdr)


                     617 

000002e0 e59414c0    618 	ldr	r1,[r4,1216]

000002e4 e3510000    619 	cmp	r1,0

000002e8 1a00000a    620 	bne	.L650

                     621 ;165: 	{


                     622 

                     623 ;166: 		snprintf(readFileContext->fileName, sizeof(readFileContext->fileName),


                     624 

000002ec e5943000    625 	ldr	r3,[r4]

000002f0 e28f2000*   626 	adr	r2,.L741

000002f4 e284001c    627 	add	r0,r4,28

000002f8 e3a01010    628 	mov	r1,16

000002fc eb000000*   629 	bl	snprintf

                     630 ;167: 			"%d.hdr", readFileContext->fileInfo.name);


                     631 ;168: 		readFileContext->zipHdr = initZipEntry(readFileContext);


                     632 

00000300 e1a00004    633 	mov	r0,r4

00000304 ebffff44*   634 	bl	initZipEntry

00000308 e58404c0    635 	str	r0,[r4,1216]

0000030c e1a01000    636 	mov	r1,r0

                     637 ;169: 		if (!readFileContext->zipHdr)


                     638 

00000310 e3500000    639 	cmp	r0,0

                     640 ;170: 		{


                     641 

                     642 ;171: 			return false;


                     643 

00000314 020000ff    644 	andeq	r0,r0,255

                     645 .L650:

                     646 ;172: 		}


                     647 ;173: 	}


                     648 ;174: 


                     649 ;175: 	return writeZipEntry(readFileContext, readFileContext->zipHdr, &readFileContext->hdrBuffer);


                     650 

00000318 12840e40    651 	addne	r0,r4,1<<10

0000031c 12802078    652 	addne	r2,r0,120

00000320 11a00004    653 	movne	r0,r4

00000324 18bd4010    654 	ldmnefd	[sp]!,{r4,lr}

00000328 1affff47*   655 	bne	writeZipEntry

                     656 .L648:

0000032c e8bd8010    657 	ldmfd	[sp]!,{r4,pc}

                     658 	.endf	OscReadFileContext_writeHdrToStream

                     659 	.align	4

                     660 ;.L723	.L726	static


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_84g1.s
                     661 

                     662 ;readFileContext	r4	param

                     663 

                     664 	.section ".bss","awb"

                     665 .L722:

                     666 	.data

                     667 	.text

                     668 

                     669 ;176: }


                     670 

                     671 ;177: 


                     672 ;178: bool OscReadFileContext_closeHdr(OscReadFileContext * readFileContext)


                     673 	.align	4

                     674 	.align	4

                     675 OscReadFileContext_closeHdr::

                     676 ;179: {


                     677 

                     678 ;180: 	return flushZipEntry(readFileContext, readFileContext->zipHdr);


                     679 

00000330 e59014c0    680 	ldr	r1,[r0,1216]

00000334 eaffff61*   681 	b	flushZipEntry

                     682 	.endf	OscReadFileContext_closeHdr

                     683 	.align	4

                     684 

                     685 ;readFileContext	none	param

                     686 

                     687 	.section ".bss","awb"

                     688 .L769:

                     689 	.data

                     690 	.text

                     691 

                     692 ;181: }


                     693 

                     694 ;182: 


                     695 ;183: bool OscReadFileContext_flushAndClose(OscReadFileContext * readFileContext)


                     696 	.align	4

                     697 	.align	4

                     698 OscReadFileContext_flushAndClose::

00000338 e92d4000    699 	stmfd	[sp]!,{lr}

0000033c e24dd008    700 	sub	sp,sp,8

                     701 ;184: {


                     702 

                     703 ;185: 	int64_t writestatus;


                     704 ;186: 	return zs_finish(readFileContext->zipStream, &writestatus) == 0;


                     705 

00000340 e59004b4    706 	ldr	r0,[r0,1204]

00000344 e1a0100d    707 	mov	r1,sp

00000348 eb000000*   708 	bl	zs_finish

0000034c e3500000    709 	cmp	r0,0

00000350 03a00001    710 	moveq	r0,1

00000354 13a00000    711 	movne	r0,0

00000358 e28dd008    712 	add	sp,sp,8

0000035c e8bd8000    713 	ldmfd	[sp]!,{pc}

                     714 	.endf	OscReadFileContext_flushAndClose

                     715 	.align	4

                     716 ;writestatus	[sp]	local

                     717 

                     718 ;readFileContext	r0	param

                     719 

                     720 	.section ".bss","awb"

                     721 .L801:


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_84g1.s
                     722 	.data

                     723 	.text

                     724 

                     725 ;187: }


                     726 

                     727 ;188: 


                     728 ;189: int OscReadFileContext_getPhistotyTime(OscReadFileContext * readFileContext)


                     729 	.align	4

                     730 	.align	4

                     731 OscReadFileContext_getPhistotyTime::

00000360 e92d4000    732 	stmfd	[sp]!,{lr}

                     733 ;190: {


                     734 

                     735 ;191: 	return (int)((readFileContext->phistoryTick + 0.5) / 1000000);


                     736 

00000364 e5901040    737 	ldr	r1,[r0,64]

00000368 e590003c    738 	ldr	r0,[r0,60]

0000036c e3a02000    739 	mov	r2,0

00000370 e3a036fe    740 	mov	r3,254<<20

00000374 e28335c0    741 	add	r3,r3,3<<28

00000378 eb000000*   742 	bl	__dadd

0000037c e59f3054*   743 	ldr	r3,.L837

00000380 e3a02000    744 	mov	r2,0

00000384 eb000000*   745 	bl	__ddiv

00000388 e8bd4000    746 	ldmfd	[sp]!,{lr}

0000038c ea000000*   747 	b	__dtoi

                     748 	.endf	OscReadFileContext_getPhistotyTime

                     749 	.align	4

                     750 

                     751 ;readFileContext	r2	param

                     752 

                     753 	.section ".bss","awb"

                     754 .L830:

                     755 	.data

                     756 	.text

                     757 

                     758 ;192: }


                     759 

                     760 ;193: 


                     761 ;194: int OscReadFileContext_getPhistotyTimeMS(OscReadFileContext * readFileContext)


                     762 	.align	4

                     763 	.align	4

                     764 OscReadFileContext_getPhistotyTimeMS::

00000390 e92d4000    765 	stmfd	[sp]!,{lr}

                     766 ;195: {


                     767 

                     768 ;196: 	return (int)((readFileContext->phistoryTick + 0.5) / 1000);


                     769 

00000394 e5901040    770 	ldr	r1,[r0,64]

00000398 e590003c    771 	ldr	r0,[r0,60]

0000039c e3a02000    772 	mov	r2,0

000003a0 e3a036fe    773 	mov	r3,254<<20

000003a4 e28335c0    774 	add	r3,r3,3<<28

000003a8 eb000000*   775 	bl	__dadd

000003ac e59f3028*   776 	ldr	r3,.L869

000003b0 e3a02000    777 	mov	r2,0

000003b4 eb000000*   778 	bl	__ddiv

000003b8 e8bd4000    779 	ldmfd	[sp]!,{lr}

000003bc ea000000*   780 	b	__dtoi

                     781 	.endf	OscReadFileContext_getPhistotyTimeMS

                     782 	.align	4


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_84g1.s
                     783 .L485:

                     784 ;	"%d.cfg\000"

000003c0 632e6425    785 	.data.b	37,100,46,99

000003c4 6766       786 	.data.b	102,103

000003c6 00         787 	.data.b	0

000003c7 00         788 	.align 4

                     789 

                     790 	.type	.L485,$object

                     791 	.size	.L485,4

                     792 

                     793 .L613:

                     794 ;	"%d.dat\000"

000003c8 642e6425    795 	.data.b	37,100,46,100

000003cc 7461       796 	.data.b	97,116

000003ce 00         797 	.data.b	0

000003cf 00         798 	.align 4

                     799 

                     800 	.type	.L613,$object

                     801 	.size	.L613,4

                     802 

                     803 .L741:

                     804 ;	"%d.hdr\000"

000003d0 682e6425    805 	.data.b	37,100,46,104

000003d4 7264       806 	.data.b	100,114

000003d6 00         807 	.data.b	0

000003d7 00         808 	.align 4

                     809 

                     810 	.type	.L741,$object

                     811 	.size	.L741,4

                     812 

                     813 .L837:

000003d8 412e8480    814 	.data.w	0x412e8480

                     815 	.type	.L837,$object

                     816 	.size	.L837,4

                     817 

                     818 .L869:

000003dc 408f4000    819 	.data.w	0x408f4000

                     820 	.type	.L869,$object

                     821 	.size	.L869,4

                     822 

                     823 	.align	4

                     824 

                     825 ;readFileContext	r2	param

                     826 

                     827 	.section ".bss","awb"

                     828 .L862:

                     829 	.data

                     830 	.text

                     831 

                     832 ;197: }


                     833 

                     834 ;198: 


                     835 ;199: bool OscReadFileContext_writeFreq(OscReadFileContext * readFileContext, float freq,


                     836 	.align	4

                     837 	.align	4

                     838 OscReadFileContext_writeFreq::

000003e0 e92d40f0    839 	stmfd	[sp]!,{r4-r7,lr}

                     840 ;200: 	unsigned int sampleNum)


                     841 ;201: {


                     842 

                     843 ;202: 	



                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_84g1.s
                     844 ;203: 	// ни одной частоты не записано


                     845 ;204: 	if (readFileContext->freqCfgCount == ~0)


                     846 

000003e4 e1a05001    847 	mov	r5,r1

000003e8 e24dd008    848 	sub	sp,sp,8

000003ec e1a04000    849 	mov	r4,r0

000003f0 e594704c    850 	ldr	r7,[r4,76]

000003f4 e1a06002    851 	mov	r6,r2

000003f8 e3770001    852 	cmn	r7,1

                     853 ;205: 	{


                     854 

                     855 ;206: 		readFileContext->lastFreq.sampleNum = sampleNum;


                     856 

000003fc 02840044    857 	addeq	r0,r4,68

                     858 ;207: 		readFileContext->lastFreq.freq = freq;


                     859 

                     860 ;208: 		readFileContext->freqCfgCount = 0;


                     861 

00000400 03a07000    862 	moveq	r7,0

00000404 088000e0    863 	stmeqea	[r0],{r5-r7}

                     864 ;209: 		return TRUE;


                     865 

00000408 03a00001    866 	moveq	r0,1

0000040c 0a000011    867 	beq	.L870

                     868 ;210: 	}


                     869 ;211: 	


                     870 ;212: 	// записывается только если частота изменилась 


                     871 ;213: 	if (readFileContext->lastFreq.freq != freq) 


                     872 

00000410 e5940044    873 	ldr	r0,[r4,68]

00000414 eb000000*   874 	bl	__fcmp

00000418 e3500000    875 	cmp	r0,0

                     876 ;222: 			&freqCfg, sizeof(OscFreqCfg));


                     877 ;223: 	}


                     878 ;224: 	else


                     879 ;225: 	{


                     880 

                     881 ;226: 		readFileContext->lastFreq.sampleNum = sampleNum;


                     882 

0000041c 05846048    883 	streq	r6,[r4,72]

                     884 ;227: 		return TRUE;


                     885 

00000420 03a00001    886 	moveq	r0,1

00000424 0a00000b    887 	beq	.L870

                     888 ;214: 	{


                     889 

                     890 ;215: 		OscFreqCfg freqCfg;


                     891 ;216: 		freqCfg = readFileContext->lastFreq;


                     892 

00000428 e5942044    893 	ldr	r2,[r4,68]

0000042c e1a0100d    894 	mov	r1,sp

00000430 e5812000    895 	str	r2,[r1]

00000434 e5940048    896 	ldr	r0,[r4,72]

00000438 e2877001    897 	add	r7,r7,1

0000043c e5810004    898 	str	r0,[r1,4]

                     899 ;217: 		readFileContext->lastFreq.freq = freq;


                     900 

                     901 ;218: 		readFileContext->lastFreq.sampleNum = sampleNum;


                     902 

                     903 ;219: 		readFileContext->freqCfgCount++;


                     904 


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_84g1.s
00000440 e2840044    905 	add	r0,r4,68

00000444 e88000e0    906 	stmea	[r0],{r5-r7}

                     907 ;220: 


                     908 ;221: 		return OscWriteBuffer_write(&readFileContext->freqCfgBuffer,


                     909 

00000448 e2840e40    910 	add	r0,r4,1<<10

0000044c e280008c    911 	add	r0,r0,140

00000450 e3a02008    912 	mov	r2,8

00000454 eb000000*   913 	bl	OscWriteBuffer_write

                     914 .L870:

00000458 e28dd008    915 	add	sp,sp,8

0000045c e8bd80f0    916 	ldmfd	[sp]!,{r4-r7,pc}

                     917 	.endf	OscReadFileContext_writeFreq

                     918 	.align	4

                     919 ;freqCfg	[sp]	local

                     920 

                     921 ;readFileContext	r4	param

                     922 ;freq	r5	param

                     923 ;sampleNum	r6	param

                     924 

                     925 	.section ".bss","awb"

                     926 .L924:

                     927 	.data

                     928 	.text

                     929 

                     930 ;228: 	}


                     931 ;229: }


                     932 

                     933 ;230: 


                     934 ;231: int OscReadFileContext_getFreqCount(OscReadFileContext * readFileContext)


                     935 	.align	4

                     936 	.align	4

                     937 OscReadFileContext_getFreqCount::

                     938 ;232: {


                     939 

                     940 ;233: 	return readFileContext->freqCfgCount + 1;


                     941 

00000460 e590004c    942 	ldr	r0,[r0,76]

00000464 e2800001    943 	add	r0,r0,1

00000468 e12fff1e*   944 	ret	

                     945 	.endf	OscReadFileContext_getFreqCount

                     946 	.align	4

                     947 

                     948 ;readFileContext	r0	param

                     949 

                     950 	.section ".bss","awb"

                     951 .L974:

                     952 	.data

                     953 	.text

                     954 

                     955 ;234: }


                     956 

                     957 ;235: 


                     958 ;236: OscFreqCfg * OscReadFileContext_getFreqCfg(OscReadFileContext * readFileContext, int num)


                     959 	.align	4

                     960 	.align	4

                     961 OscReadFileContext_getFreqCfg::

0000046c e92d4010    962 	stmfd	[sp]!,{r4,lr}

                     963 ;237: {


                     964 

                     965 ;238: 	OscFreqCfg *result;



                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_84g1.s
                     966 ;239: 	


                     967 ;240: 	// если не было изменений частоты или последняя частота


                     968 ;241: 	if (readFileContext->freqCfgCount == 0 || 


                     969 

00000470 e590204c    970 	ldr	r2,[r0,76]

00000474 e1a04001    971 	mov	r4,r1

00000478 e3520000    972 	cmp	r2,0

0000047c 11540002    973 	cmpne	r4,r2

                     974 ;242: 		num == readFileContext->freqCfgCount)


                     975 ;243: 	{


                     976 

                     977 ;244: 		return &readFileContext->lastFreq;


                     978 

00000480 02800044    979 	addeq	r0,r0,68

00000484 0a000007    980 	beq	.L981

                     981 ;245: 	}


                     982 ;246: 


                     983 ;247: 


                     984 ;248: 	if (num >= readFileContext->freqCfgCount + 1)


                     985 

00000488 e2821001    986 	add	r1,r2,1

0000048c e1540001    987 	cmp	r4,r1

                     988 ;249: 	{


                     989 

                     990 ;250: 		return NULL;


                     991 

00000490 a3a00000    992 	movge	r0,0

00000494 aa000003    993 	bge	.L981

                     994 ;251: 	}


                     995 ;252: 


                     996 ;253: 	result = OscWriteBuffer_data(&readFileContext->freqCfgBuffer);


                     997 

00000498 e2801e40    998 	add	r1,r0,1<<10

0000049c e281008c    999 	add	r0,r1,140

000004a0 eb000000*  1000 	bl	OscWriteBuffer_data

                    1001 ;254: 	return &result[num];


                    1002 

000004a4 e0800184   1003 	add	r0,r0,r4 lsl 3

                    1004 .L981:

000004a8 e8bd8010   1005 	ldmfd	[sp]!,{r4,pc}

                    1006 	.endf	OscReadFileContext_getFreqCfg

                    1007 	.align	4

                    1008 

                    1009 ;readFileContext	r0	param

                    1010 ;num	r4	param

                    1011 

                    1012 	.section ".bss","awb"

                    1013 .L1038:

                    1014 	.data

                    1015 	.text

                    1016 

                    1017 ;255: }


                    1018 	.align	4

                    1019 

                    1020 	.data

                    1021 	.ghsnote version,6

                    1022 	.ghsnote tools,3

                    1023 	.ghsnote options,0

                    1024 	.text

                    1025 	.align	4

