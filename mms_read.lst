                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8r81.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_read.c -o gh_8r81.o -list=mms_read.lst C:\Users\<USER>\AppData\Local\Temp\gh_8r81.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_8r81.s
Source File: mms_read.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile mms_read.c -o

                      11 ;		mms_read.o

                      12 ;Source File:   mms_read.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:04 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "mms_read.h"


                      23 ;2: #include "AsnEncoding.h"


                      24 ;3: #include "mms_data.h"


                      25 ;4: #include "mmsservices.h"


                      26 ;5: #include "iedmodel.h"


                      27 ;6: #include "iedTree/DataSet.h"


                      28 ;7: #include "iedTree/iedTree.h"


                      29 ;8: #include "iedTree/iedEntity.h"


                      30 ;9: #include "iedTree/iedNoEntity.h"


                      31 ;10: #include "ObjectNameParser.h"


                      32 ;11: #include "DataSlice.h"


                      33 ;12: #include "mms_error.h"


                      34 ;13: #include "bufViewMMS.h"


                      35 ;14: #include <debug.h>


                      36 ;15: #include <string.h>


                      37 ;16: #include <stddef.h>


                      38 ;17: 


                      39 ;18: // objectName должен содержать только имя объекта


                      40 ;19: // и обязательно с позиции 0


                      41 ;20: static bool encodeReadDataSetResponse(uint32_t invokeId, IEDEntity dataSet,


                      42 

                      43 ;91: 


                      44 ;92: }


                      45 

                      46 ;93: 


                      47 ;94: 


                      48 ;95: static bool encodeReadResponse(unsigned int invokeId, IEDEntity* objList,


                      49 

                      50 ;180: }



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8r81.s
                      51 

                      52 ;181: 


                      53 ;182: /*


                      54 ;183: static int handleReadNamedVariableListRequestOld(unsigned int invokeId,


                      55 ;184: 	uint8_t* pObjectName, int objectNameLen,	unsigned char* outBuf)


                      56 ;185: {


                      57 ;186: 	StringView domainId;


                      58 ;187: 	StringView itemId;


                      59 ;188: 	int dataSetPos;


                      60 ;189: 	int bufPos;


                      61 ;190: 


                      62 ;191: 	bufPos = BerDecoder_DecodeObjectNameToStringView(pObjectName, 0, 


                      63 ;192: 		objectNameLen, &domainId, &itemId);


                      64 ;193: 	RET_IF_NOT(bufPos > 1, "Unable to decode dataset name");


                      65 ;194: 


                      66 ;195: 	dataSetPos = findObjectByFullName(IED_VMD_DATA_SET_SECTION,


                      67 ;196: 		&domainId, &itemId);


                      68 ;197: 	RET_IF_NOT(dataSetPos, "DataSet is not found");


                      69 ;198: 


                      70 ;199: 	return encodeReadDataSetResponse(invokeId, dataSetPos,


                      71 ;200: 		pObjectName, objectNameLen, outBuf);


                      72 ;201: }


                      73 ;202: */


                      74 ;203: 


                      75 ;204: // Чтение DataSet по имени


                      76 ;205: // objectName должен содержать только имя объекта


                      77 ;206: // и обязательно с позиции 0.


                      78 ;207: static bool handleReadNamedVariableListRequest(unsigned int invokeId,


                      79 

                      80 ;232: }


                      81 

                      82 ;233: 


                      83 ;234: //Заполняет список объектов для чтения


                      84 ;235: //После последнего объекта записывается NULL


                      85 ;236: static bool fillReadObjList(BufferView *varSpecList, IEDEntity* objList)


                      86 

                      87 ;259: }


                      88 

                      89 ;260: 


                      90 ;261: int mms_handleReadRequest(MmsConnection* mmsConn,


                      91 	.text

                      92 	.align	4

                      93 mms_handleReadRequest::

00000000 e92d4cf0     94 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                      95 ;262:         unsigned char* inBuf, int bufPos, int maxBufPos, unsigned int invokeId,


                      96 ;263:         unsigned char* response, size_t maxRespSize)


                      97 ;264: {   	    


                      98 

00000004 e1a06002     99 	mov	r6,r2

00000008 e24dd044    100 	sub	sp,sp,68

0000000c e59d2068    101 	ldr	r2,[sp,104]

00000010 e3a05000    102 	mov	r5,0

00000014 e5cd5003    103 	strb	r5,[sp,3]

                     104 ;265:     int result;


                     105 ;266: 	BufferView objectName;


                     106 ;267:     //Чтение dataset


                     107 ;268:     bool namedVariableListRequest = FALSE;


                     108 

                     109 ;269:     unsigned char	tag;


                     110 ;270:     int				iLength;    


                     111 ;271:     BufferView specListBer;



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8r81.s
                     112 ;272: 	BufferView responseBuf;


                     113 ;273: 	BufferView_init(&responseBuf, response, maxRespSize, 0);


                     114 

00000018 e1a04000    115 	mov	r4,r0

0000001c e1a07001    116 	mov	r7,r1

00000020 e59d1064    117 	ldr	r1,[sp,100]

00000024 e28d0020    118 	add	r0,sp,32

00000028 e1a0b003    119 	mov	fp,r3

0000002c e1a03005    120 	mov	r3,r5

00000030 eb000000*   121 	bl	BufferView_init

                     122 ;274:     //Список объектов для чтения пустой


                     123 ;275:     mmsConn->readVarObjList[0] = NULL;


                     124 

00000034 e3a00c63    125 	mov	r0,99<<8

00000038 e2800068    126 	add	r0,r0,104

0000003c e7a45000    127 	str	r5,[r4,r0]!

                     128 ;276: 


                     129 ;277:     while (bufPos < maxBufPos)


                     130 

00000040 e156000b    131 	cmp	r6,fp

00000044 aa000049    132 	bge	.L174

                     133 .L175:

                     134 ;278:     {


                     135 

                     136 ;279: 


                     137 ;280:         tag = inBuf[bufPos++];


                     138 

00000048 e7d75006    139 	ldrb	r5,[r7,r6]

0000004c e2861001    140 	add	r1,r6,1

                     141 ;281: 


                     142 ;282:         bufPos = BerDecoder_DecodeLengthOld(inBuf, bufPos, maxBufPos, &iLength);


                     143 

00000050 e28d3004    144 	add	r3,sp,4

00000054 e1a0200b    145 	mov	r2,fp

00000058 e1a00007    146 	mov	r0,r7

0000005c eb000000*   147 	bl	BerDecoder_DecodeLengthOld

00000060 e1b06000    148 	movs	r6,r0

                     149 ;283: 


                     150 ;284:         if (bufPos < 0)


                     151 

00000064 4a00003f    152 	bmi	.L208

                     153 ;285:         {


                     154 

                     155 ;286:             return 0;


                     156 

                     157 ;287:         }


                     158 ;288: 


                     159 ;289:         switch (tag)


                     160 

00000068 e2555080    161 	subs	r5,r5,128

0000006c 0a000002    162 	beq	.L181

00000070 e3550021    163 	cmp	r5,33

00000074 0a000007    164 	beq	.L185

00000078 ea00003a    165 	b	.L208

                     166 .L181:

0000007c e59d0004    167 	ldr	r0,[sp,4]

                     168 ;290:         {


                     169 ;291:             case MMS_READ_SPECIFICATION_WITH_RESULT:


                     170 ;292:                 if (iLength != 1)


                     171 

00000080 e3500001    172 	cmp	r0,1


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8r81.s
00000084 1a000037    173 	bne	.L208

                     174 ;293:                 {


                     175 

                     176 ;294:                     return 0;


                     177 

                     178 ;295:                 }


                     179 ;296:                 //Пропускаем specification


                     180 ;297:                 bufPos++;


                     181 

00000088 e0866000    182 	add	r6,r6,r0

0000008c e156000b    183 	cmp	r6,fp

00000090 baffffec    184 	blt	.L175

00000094 ea000035    185 	b	.L174

                     186 .L185:

                     187 ;298:                 break;


                     188 ;299:             case MMS_READ_VARIABLE_ACCESS_SPECIFICATION:


                     189 ;300:                 tag = inBuf[bufPos++];


                     190 

00000098 e7d75006    191 	ldrb	r5,[r7,r6]

0000009c e2861001    192 	add	r1,r6,1

                     193 ;301:                 bufPos = BerDecoder_DecodeLengthOld(inBuf, bufPos,


                     194 

000000a0 e28d3004    195 	add	r3,sp,4

000000a4 e1a0200b    196 	mov	r2,fp

000000a8 e1a00007    197 	mov	r0,r7

000000ac eb000000*   198 	bl	BerDecoder_DecodeLengthOld

000000b0 e1b06000    199 	movs	r6,r0

                     200 ;302:                     maxBufPos, &iLength);


                     201 ;303:                 if (bufPos < 0)


                     202 

000000b4 4a00002b    203 	bmi	.L208

                     204 ;304:                 {


                     205 

                     206 ;305:                     return 0;


                     207 

                     208 ;306:                 }


                     209 ;307:                 switch (tag)


                     210 

000000b8 e25550a0    211 	subs	r5,r5,160

000000bc 0a00000d    212 	beq	.L192

000000c0 e3550001    213 	cmp	r5,1

000000c4 1a000027    214 	bne	.L208

                     215 ;308:                 {


                     216 ;309: 				case MMS_READ_VARIABLE_LIST_NAME://DataSet										


                     217 ;310: 					//StringView_init(&objectName, inBuf + bufPos, iLength);


                     218 ;311: 					BufferView_init(&objectName, inBuf + bufPos, iLength, 0);


                     219 

000000c8 e59d2004    220 	ldr	r2,[sp,4]

000000cc e0861007    221 	add	r1,r6,r7

000000d0 e28d0038    222 	add	r0,sp,56

000000d4 e3a03000    223 	mov	r3,0

000000d8 eb000000*   224 	bl	BufferView_init

                     225 ;312: 


                     226 ;313: 					bufPos += iLength;


                     227 

000000dc e59d0004    228 	ldr	r0,[sp,4]

000000e0 e0866000    229 	add	r6,r6,r0

                     230 ;314: 					namedVariableListRequest = TRUE;


                     231 

000000e4 e3a00001    232 	mov	r0,1

000000e8 e5cd0003    233 	strb	r0,[sp,3]


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8r81.s
000000ec e156000b    234 	cmp	r6,fp

000000f0 baffffd4    235 	blt	.L175

000000f4 ea00001d    236 	b	.L174

                     237 .L192:

                     238 ;315: 					break;


                     239 ;316:                 case MMS_READ_LIST_OF_VARIABLE:


                     240 ;317:                     BufferView_init(&specListBer, inBuf, bufPos + iLength,  bufPos);


                     241 

000000f8 e1a01007    242 	mov	r1,r7

000000fc e59d0004    243 	ldr	r0,[sp,4]

00000100 e1a03006    244 	mov	r3,r6

00000104 e0802006    245 	add	r2,r0,r6

00000108 e28d002c    246 	add	r0,sp,44

0000010c e1a06000    247 	mov	r6,r0

00000110 eb000000*   248 	bl	BufferView_init

                     249 ;318:                     if(!fillReadObjList(&specListBer,mmsConn->readVarObjList))


                     250 

                     251 ;237: {        


                     252 

00000114 e3a05000    253 	mov	r5,0

                     254 ;238:     IEDEntity object;       


                     255 ;239:     size_t objIndex = 0;


                     256 

                     257 ;240: 


                     258 ;241:     while (!BufferView_endOfBuf(varSpecList))


                     259 

00000118 e1a0a006    260 	mov	r10,r6

                     261 ;252:         {


                     262 

                     263 ;253:             ERROR_REPORT("Too many objects in the list");


                     264 ;254:             return false;


                     265 

0000011c e9960003    266 	ldmed	[r6],{r0-r1}

00000120 e1500001    267 	cmp	r0,r1

00000124 0a000009    268 	beq	.L193

                     269 .L198:

                     270 ;242:     {


                     271 

                     272 ;243:         object = ObjectNameParser_parse(varSpecList);


                     273 

00000128 e1a0000a    274 	mov	r0,r10

0000012c eb000000*   275 	bl	ObjectNameParser_parse

                     276 ;244:         if(object == NULL)


                     277 

00000130 e3500000    278 	cmp	r0,0

                     279 ;245:         {


                     280 

                     281 ;246:             ERROR_REPORT("Unable to parse object name");


                     282 ;247:             return false;


                     283 

                     284 ;248:         }


                     285 ;249:         


                     286 ;250:         objList[objIndex++] = object;


                     287 

00000134 17840105    288 	strne	r0,[r4,r5 lsl 2]

00000138 12855001    289 	addne	r5,r5,1

                     290 ;251:         if(objIndex >= MAX_READ_VARIABLE_OBJECT_LIST)


                     291 

0000013c 13550064    292 	cmpne	r5,100

00000140 2a0000a7    293 	bhs	.L253

                     294 ;252:         {



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8r81.s
                     295 

                     296 ;253:             ERROR_REPORT("Too many objects in the list");


                     297 ;254:             return false;


                     298 

00000144 e9960003    299 	ldmed	[r6],{r0-r1}

00000148 e1500001    300 	cmp	r0,r1

0000014c 1afffff5    301 	bne	.L198

                     302 .L193:

00000150 e3a01000    303 	mov	r1,0

00000154 e59d6030    304 	ldr	r6,[sp,48]

00000158 e7841105    305 	str	r1,[r4,r5 lsl 2]

                     306 ;255:         }                


                     307 ;256:     }


                     308 ;257:     objList[objIndex] = 0;


                     309 

                     310 ;258:     return true;


                     311 

                     312 ;319:                     {


                     313 

                     314 ;320:                         return CreateMmsConfirmedErrorPdu(invokeId, response,


                     315 

                     316 ;321:                             MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);


                     317 ;322:                     }


                     318 ;323:                     bufPos = specListBer.pos;


                     319 

0000015c e156000b    320 	cmp	r6,fp

00000160 baffffb8    321 	blt	.L175

00000164 ea000001    322 	b	.L174

                     323 .L208:

                     324 ;324: 					break;


                     325 ;325:                 default:


                     326 ;326:                     return 0;


                     327 

                     328 ;327:                 }


                     329 ;328:                 break;


                     330 ;329:             default:


                     331 ;330:                 return 0;


                     332 

00000168 e3a00000    333 	mov	r0,0

0000016c ea0000a2    334 	b	.L171

                     335 .L174:

                     336 ;331:         }


                     337 ;332:     }


                     338 ;333: 


                     339 ;334: 


                     340 ;335: 	IEDTree_lock();


                     341 

00000170 eb000000*   342 	bl	IEDTree_lock

                     343 ;336: 


                     344 ;337: 	if (namedVariableListRequest)


                     345 

00000174 e5dd0003    346 	ldrb	r0,[sp,3]

00000178 e3500000    347 	cmp	r0,0

0000017c 0a000047    348 	beq	.L231

                     349 ;338: 	{		


                     350 

                     351 ;339: 		


                     352 ;340: 


                     353 ;341: 		//Чтение DataSet


                     354 ;342: 		if(!handleReadNamedVariableListRequest(invokeId, &objectName, &responseBuf))


                     355 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8r81.s
                     356 ;208: 											   BufferView *objectName, BufferView *outBuf)


                     357 ;209: {


                     358 

                     359 ;210: 	StringView domainId;


                     360 ;211: 	StringView itemId;


                     361 ;212: 	IEDEntity dataSetEntity;


                     362 ;213: 


                     363 ;214: 	//Гарантируем, что pos будет указывать на имя объекта для декодирования


                     364 ;215: 	objectName->pos = 0;


                     365 

00000180 e28d2010    366 	add	r2,sp,16

00000184 e28d1018    367 	add	r1,sp,24

00000188 e3a00000    368 	mov	r0,0

0000018c e58d003c    369 	str	r0,[sp,60]

                     370 ;216: 	if(!BufView_decodeObjectName(objectName, &domainId, &itemId))


                     371 

00000190 e28d0038    372 	add	r0,sp,56

00000194 eb000000*   373 	bl	BufView_decodeObjectName

00000198 e3500000    374 	cmp	r0,0

0000019c 0a000004    375 	beq	.L219

                     376 ;217: 	{


                     377 

                     378 ;218: 		ERROR_REPORT("Unable to decode dataset name")	;


                     379 ;219: 		return false;


                     380 

                     381 ;220: 	}	


                     382 ;221: 


                     383 ;222: 	dataSetEntity =


                     384 

000001a0 e28d1010    385 	add	r1,sp,16

000001a4 e28d0018    386 	add	r0,sp,24

000001a8 eb000000*   387 	bl	IEDTree_findDataSetByFullName

000001ac e1b04000    388 	movs	r4,r0

                     389 ;223: 			IEDTree_findDataSetByFullName(&domainId, &itemId);


                     390 ;224: 


                     391 ;225: 	if(dataSetEntity == NULL)


                     392 

000001b0 1a000002    393 	bne	.L220

                     394 .L219:

                     395 ;226: 	{


                     396 

                     397 ;227: 		ERROR_REPORT("Unable to find dataset");


                     398 ;228: 		return false;


                     399 

                     400 ;356:         {


                     401 

                     402 ;357: 			ERROR_REPORT("encodeReadResponse error");


                     403 ;358:             result = 0;


                     404 

000001b4 e3a04000    405 	mov	r4,0

                     406 ;363:         }


                     407 ;364: 	}


                     408 ;365: 	IEDTree_unlock();


                     409 

000001b8 eb000000*   410 	bl	IEDTree_unlock

                     411 ;366: 


                     412 ;367: 	if(result < 1)


                     413 

000001bc ea000088    414 	b	.L253

                     415 .L220:

                     416 ;229: 	}



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8r81.s
                     417 ;230: 


                     418 ;231: 	return encodeReadDataSetResponse(invokeId, dataSetEntity, objectName,outBuf);


                     419 

                     420 ;21: 	BufferView *objectName, BufferView *outBuf)


                     421 ;22: {


                     422 

                     423 ;23: 	bool result;


                     424 ;24: 	size_t readDataSize;


                     425 ;25: 	size_t readDataSizeWithTL;


                     426 ;26: 	int objectSpecSize;


                     427 ;27: 	int objectSpecRespSize;


                     428 ;28: 	int readResponseSize;


                     429 ;29: 	int invokeIdSize;


                     430 ;30: 	int confirmedResponseContentSize;


                     431 ;31: 


                     432 ;32: 	dataSliceCapture();


                     433 

000001c0 eb000000*   434 	bl	dataSliceCapture

000001c4 e28d1008    435 	add	r1,sp,8

000001c8 e1a00004    436 	mov	r0,r4

000001cc eb000000*   437 	bl	DataSet_calcReadLen

                     438 ;33: 


                     439 ;34: 	//===============Получаем размеры===================


                     440 ;35: 


                     441 ;36: 	//Размер результатов чтения


                     442 ;37: 


                     443 ;38: 	if(!DataSet_calcReadLen(dataSet, &readDataSize))


                     444 

000001d0 e3500000    445 	cmp	r0,0

000001d4 1a000001    446 	bne	.L222

                     447 ;39: 	{


                     448 

                     449 ;40: 		ERROR_REPORT("Unable to determine dataset read size")	;		


                     450 ;41: 		return false;


                     451 

000001d8 0a00007a    452 	beq	.L228

000001dc ea00007c    453 	b	.L227

                     454 .L222:

                     455 ;42: 	}


                     456 ;43: 


                     457 ;44: 	//Размер результатов чтения с тэгом и длиной


                     458 ;45: 	readDataSizeWithTL = BerEncoder_determineFullObjectSize(readDataSize);


                     459 

000001e0 e59d0008    460 	ldr	r0,[sp,8]

000001e4 eb000000*   461 	bl	BerEncoder_determineFullObjectSize

000001e8 e1a05000    462 	mov	r5,r0

                     463 ;46: 


                     464 ;47: 	//Размер спецификации объекта ( А1)


                     465 ;48: 	objectSpecSize = BerEncoder_determineFullObjectSize(objectName->len);


                     466 

000001ec e59d0040    467 	ldr	r0,[sp,64]

000001f0 eb000000*   468 	bl	BerEncoder_determineFullObjectSize

                     469 ;49: 


                     470 ;50: 	//Весь А0


                     471 ;51: 	objectSpecRespSize = BerEncoder_determineFullObjectSize(objectSpecSize);


                     472 

000001f4 e1a0a000    473 	mov	r10,r0

000001f8 eb000000*   474 	bl	BerEncoder_determineFullObjectSize

000001fc e1a06000    475 	mov	r6,r0

                     476 ;52: 


                     477 ;53: 	//Размер размер ответа на чтение (A4)



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8r81.s
                     478 ;54: 	readResponseSize = BerEncoder_determineFullObjectSize(


                     479 

00000200 e0860005    480 	add	r0,r6,r5

00000204 eb000000*   481 	bl	BerEncoder_determineFullObjectSize

00000208 e1a07000    482 	mov	r7,r0

                     483 ;55: 		readDataSizeWithTL + objectSpecRespSize);


                     484 ;56: 


                     485 ;57: 	//Размер invokeId


                     486 ;58: 	invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;


                     487 

0000020c e59d0060    488 	ldr	r0,[sp,96]

00000210 eb000000*   489 	bl	BerEncoder_UInt32determineEncodedSize

00000214 e0800007    490 	add	r0,r0,r7

                     491 ;59: 


                     492 ;60: 	//Полный размер ответа (для A1)


                     493 ;61: 	confirmedResponseContentSize = invokeIdSize + readResponseSize;


                     494 

00000218 e2802002    495 	add	r2,r0,2

                     496 ;62: 


                     497 ;63: 	//================Кодируем=====================


                     498 ;64: 


                     499 ;65: 	// confirmed response PDU


                     500 ;66: 	BufferView_encodeTL(outBuf, 0xA1, confirmedResponseContentSize );


                     501 

0000021c e28d0020    502 	add	r0,sp,32

00000220 e3a010a1    503 	mov	r1,161

00000224 eb000000*   504 	bl	BufferView_encodeTL

                     505 ;67: 


                     506 ;68: 	// invoke id	


                     507 ;69: 	BufferView_encodeUInt32(outBuf, ASN_INTEGER, invokeId);


                     508 

00000228 e59d2060    509 	ldr	r2,[sp,96]

0000022c e28d0020    510 	add	r0,sp,32

00000230 e3a01002    511 	mov	r1,2

00000234 eb000000*   512 	bl	BufferView_encodeUInt32

                     513 ;70: 


                     514 ;71: 	// confirmed-service-response read	


                     515 ;72: 	BufferView_encodeTL(outBuf, 0xA4, objectSpecRespSize + readDataSizeWithTL);


                     516 

00000238 e0852006    517 	add	r2,r5,r6

0000023c e28d0020    518 	add	r0,sp,32

00000240 e3a010a4    519 	mov	r1,164

00000244 eb000000*   520 	bl	BufferView_encodeTL

                     521 ;73: 


                     522 ;74: 	//A0	


                     523 ;75: 	BufferView_encodeTL(outBuf, 0xA0,objectSpecSize);


                     524 

00000248 e1a0200a    525 	mov	r2,r10

0000024c e28d0020    526 	add	r0,sp,32

00000250 e3a010a0    527 	mov	r1,160

00000254 eb000000*   528 	bl	BufferView_encodeTL

                     529 ;76: 		


                     530 ;77: 	//Гарантируем, что запишем полное имя объекта


                     531 ;78: 	objectName->pos = objectName->len;


                     532 

00000258 e59d0040    533 	ldr	r0,[sp,64]

0000025c e28d2038    534 	add	r2,sp,56

00000260 e58d003c    535 	str	r0,[sp,60]

                     536 ;79: 


                     537 ;80: 	//Object name


                     538 ;81: 	BufferView_encodeBufferView(outBuf, 0xA1, objectName);	



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8r81.s
                     539 

00000264 e28d0020    540 	add	r0,sp,32

00000268 e3a010a1    541 	mov	r1,161

0000026c eb000000*   542 	bl	BufferView_encodeBufferView

                     543 ;82: 


                     544 ;83: 	// encode list of access results TL		


                     545 ;84: 	BufferView_encodeTL(outBuf, 0xA1, readDataSize);


                     546 

00000270 e59d2008    547 	ldr	r2,[sp,8]

00000274 e28d0020    548 	add	r0,sp,32

00000278 e3a010a1    549 	mov	r1,161

0000027c eb000000*   550 	bl	BufferView_encodeTL

                     551 ;85: 


                     552 ;86: 	// encode access results	


                     553 ;87: 	result = DataSet_encodeRead(dataSet, outBuf);


                     554 

00000280 e28d1020    555 	add	r1,sp,32

00000284 e1a00004    556 	mov	r0,r4

00000288 eb000000*   557 	bl	DataSet_encodeRead

0000028c e1a04000    558 	mov	r4,r0

                     559 ;88: 


                     560 ;89: 	dataSliceRelease();	


                     561 

00000290 eb000000*   562 	bl	dataSliceRelease

                     563 ;90: 	return result;


                     564 

00000294 e3540000    565 	cmp	r4,0

00000298 0a00004a    566 	beq	.L228

0000029c ea00004c    567 	b	.L227

                     568 .L231:

                     569 ;343: 		{


                     570 

                     571 ;344: 			ERROR_REPORT("handleReadNamedVariableListRequest error");


                     572 ;345: 			result = 0;


                     573 

                     574 ;346: 		}


                     575 ;347: 		else


                     576 ;348: 		{			


                     577 

                     578 ;349: 			result = responseBuf.pos;


                     579 

                     580 ;350: 		}


                     581 ;351: 	}


                     582 ;352: 	else


                     583 ;353: 	{        


                     584 

                     585 ;354: 		//Чтение переменных по списку


                     586 ;355: 		if(!encodeReadResponse(invokeId, mmsConn->readVarObjList, &responseBuf))


                     587 

                     588 ;96:                                BufferView *outBuf)


                     589 ;97: {    


                     590 

                     591 ;98:     size_t accessResultSize = 0;


                     592 

                     593 ;99:     size_t varAccessSpecSize = 0;


                     594 

                     595 ;100:     size_t listOfAccessResultsLength;


                     596 ;101:     size_t confirmedServiceResponseContentLength;


                     597 ;102:     size_t confirmedServiceResponseLength;


                     598 ;103:     size_t invokeIdSize;


                     599 ;104:     size_t confirmedResponseContentSize;



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8r81.s
                     600 ;105:     size_t objIndex;


                     601 ;106: 	IEDEntity entity;


                     602 ;107: 


                     603 ;108:     dataSliceCapture();


                     604 

000002a0 e3a06000    605 	mov	r6,0

                     606 ;114:     for(objIndex = 0; objList[objIndex] != NULL; ++objIndex)


                     607 

000002a4 eb000000*   608 	bl	dataSliceCapture

                     609 ;109: 


                     610 ;110:     //==============determine BER encoded message sizes==============	


                     611 ;111: 


                     612 ;112:     //Общий размер всех значений


                     613 ;113: 	accessResultSize = 0;


                     614 

000002a8 e1a05004    615 	mov	r5,r4

000002ac e5950000    616 	ldr	r0,[r5]

000002b0 e28d700c    617 	add	r7,sp,12

000002b4 e3500000    618 	cmp	r0,0

000002b8 0a00000b    619 	beq	.L236

                     620 .L232:

                     621 ;115: 	{


                     622 

                     623 ;116:         size_t objReadLen;			


                     624 ;117: 		entity = objList[objIndex];


                     625 

000002bc e2855004    626 	add	r5,r5,4

                     627 ;118: 		if(!entity->calcReadLen(entity,  &objReadLen))


                     628 

000002c0 e590c060    629 	ldr	r12,[r0,96]

000002c4 e1a01007    630 	mov	r1,r7

000002c8 e1a0e00f    631 	mov	lr,pc

000002cc e12fff1c*   632 	bx	r12

000002d0 e3500000    633 	cmp	r0,0

000002d4 0a00003b    634 	beq	.L228

                     635 ;119:         {			


                     636 

                     637 ;120:             return 0;


                     638 

                     639 ;121:         }


                     640 ;122:         accessResultSize += objReadLen;


                     641 

000002d8 e59d000c    642 	ldr	r0,[sp,12]

000002dc e0866000    643 	add	r6,r6,r0

000002e0 e5950000    644 	ldr	r0,[r5]

000002e4 e3500000    645 	cmp	r0,0

000002e8 1afffff3    646 	bne	.L232

                     647 .L236:

                     648 ;123: 	}


                     649 ;124: 


                     650 ;125:     listOfAccessResultsLength = 1 +


                     651 

000002ec e1a00006    652 	mov	r0,r6

000002f0 eb000000*   653 	bl	BerEncoder_determineLengthSize

000002f4 e0800006    654 	add	r0,r0,r6

000002f8 e2805001    655 	add	r5,r0,1

                     656 ;126:                 BerEncoder_determineLengthSize(accessResultSize) +


                     657 ;127:                 accessResultSize;


                     658 ;128: 


                     659 ;129:     confirmedServiceResponseContentLength = listOfAccessResultsLength + varAccessSpecSize;


                     660 


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8r81.s
                     661 ;130: 


                     662 ;131:     confirmedServiceResponseLength = 1 +


                     663 

000002fc e1a00005    664 	mov	r0,r5

00000300 eb000000*   665 	bl	BerEncoder_determineLengthSize

00000304 e0800005    666 	add	r0,r0,r5

00000308 e2807001    667 	add	r7,r0,1

                     668 ;132:             BerEncoder_determineLengthSize(confirmedServiceResponseContentLength) +


                     669 ;133:             confirmedServiceResponseContentLength;


                     670 ;134: 


                     671 ;135:     invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;


                     672 

0000030c e59d0060    673 	ldr	r0,[sp,96]

00000310 eb000000*   674 	bl	BerEncoder_UInt32determineEncodedSize

00000314 e0800007    675 	add	r0,r0,r7

                     676 ;136: 


                     677 ;137:     confirmedResponseContentSize = confirmedServiceResponseLength + invokeIdSize;


                     678 

00000318 e2802002    679 	add	r2,r0,2

                     680 ;138: 


                     681 ;139:     //================ encode message ============================


                     682 ;140:     // confirmed response PDU


                     683 ;141:     if(!BufferView_encodeTL(outBuf, 0xA1, confirmedResponseContentSize))


                     684 

0000031c e28d0020    685 	add	r0,sp,32

00000320 e3a010a1    686 	mov	r1,161

00000324 eb000000*   687 	bl	BufferView_encodeTL

00000328 e3500000    688 	cmp	r0,0

0000032c 0a000025    689 	beq	.L228

                     690 ;142:     {		


                     691 

                     692 ;143:         return false;


                     693 

                     694 ;144:     }


                     695 ;145: 


                     696 ;146:     // invoke id


                     697 ;147:     if(!BufferView_encodeUInt32(outBuf, ASN_INTEGER, invokeId))


                     698 

00000330 e59d2060    699 	ldr	r2,[sp,96]

00000334 e28d0020    700 	add	r0,sp,32

00000338 e3a01002    701 	mov	r1,2

0000033c eb000000*   702 	bl	BufferView_encodeUInt32

00000340 e3500000    703 	cmp	r0,0

00000344 0a00001f    704 	beq	.L228

                     705 ;148:     {		


                     706 

                     707 ;149:         return false;


                     708 

                     709 ;150:     }


                     710 ;151: 


                     711 ;152:     // confirmed-service-response read


                     712 ;153:     if(!BufferView_encodeTL(outBuf, 0xA4, confirmedServiceResponseContentLength))


                     713 

00000348 e1a02005    714 	mov	r2,r5

0000034c e28d0020    715 	add	r0,sp,32

00000350 e3a010a4    716 	mov	r1,164

00000354 eb000000*   717 	bl	BufferView_encodeTL

00000358 e3500000    718 	cmp	r0,0

0000035c 0a000019    719 	beq	.L228

                     720 ;154:     {		


                     721 


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8r81.s
                     722 ;155:         return false;


                     723 

                     724 ;156:     }


                     725 ;157: 


                     726 ;158:     // encode variable access specification


                     727 ;159:     //if (accessSpec != NULL)


                     728 ;160:     //    bufPos = encodeVariableAccessSpecification(accessSpec, outBuf, bufPos, true);


                     729 ;161: 


                     730 ;162:     // encode list of access results    


                     731 ;163:     if(!BufferView_encodeTL(outBuf, 0xA1, accessResultSize))


                     732 

00000360 e1a02006    733 	mov	r2,r6

00000364 e28d0020    734 	add	r0,sp,32

00000368 e3a010a1    735 	mov	r1,161

0000036c eb000000*   736 	bl	BufferView_encodeTL

00000370 e3500000    737 	cmp	r0,0

00000374 0a000013    738 	beq	.L228

                     739 ;164: 	{


                     740 

                     741 ;165:         return false;


                     742 

                     743 ;166:     }


                     744 ;167: 


                     745 ;168:     // encode access results            


                     746 ;169:     for(objIndex = 0; objList[objIndex] != NULL; ++objIndex)


                     747 

00000378 e5940000    748 	ldr	r0,[r4]

0000037c e28d5020    749 	add	r5,sp,32

                     750 ;173:         {			


                     751 

                     752 ;174:             return false;


                     753 

00000380 e3500000    754 	cmp	r0,0

00000384 0a000009    755 	beq	.L249

                     756 .L245:

                     757 ;170: 	{


                     758 

                     759 ;171: 		entity = objList[objIndex];


                     760 

00000388 e2844004    761 	add	r4,r4,4

                     762 ;172: 		if(!entity->encodeRead(entity, outBuf))


                     763 

0000038c e590c05c    764 	ldr	r12,[r0,92]

00000390 e1a01005    765 	mov	r1,r5

00000394 e1a0e00f    766 	mov	lr,pc

00000398 e12fff1c*   767 	bx	r12

0000039c e3500000    768 	cmp	r0,0

000003a0 0a000008    769 	beq	.L228

                     770 ;173:         {			


                     771 

                     772 ;174:             return false;


                     773 

000003a4 e5940000    774 	ldr	r0,[r4]

000003a8 e3500000    775 	cmp	r0,0

000003ac 1afffff5    776 	bne	.L245

                     777 .L249:

                     778 ;175:         }


                     779 ;176: 	}


                     780 ;177: 


                     781 ;178:     dataSliceRelease();	


                     782 


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8r81.s
000003b0 eb000000*   783 	bl	dataSliceRelease

                     784 ;179:     return true;


                     785 

                     786 ;359:         }


                     787 ;360:         else


                     788 ;361:         {


                     789 

                     790 ;362:             result = responseBuf.pos;


                     791 

000003b4 e59d4024    792 	ldr	r4,[sp,36]

                     793 ;363:         }


                     794 ;364: 	}


                     795 ;365: 	IEDTree_unlock();


                     796 

000003b8 eb000000*   797 	bl	IEDTree_unlock

                     798 ;366: 


                     799 ;367: 	if(result < 1)


                     800 

000003bc e3540000    801 	cmp	r4,0

000003c0 ca00000c    802 	bgt	.L252

000003c4 ea000006    803 	b	.L253

                     804 .L228:

                     805 ;356:         {


                     806 

                     807 ;357: 			ERROR_REPORT("encodeReadResponse error");


                     808 ;358:             result = 0;


                     809 

000003c8 e3a04000    810 	mov	r4,0

                     811 ;363:         }


                     812 ;364: 	}


                     813 ;365: 	IEDTree_unlock();


                     814 

000003cc eb000000*   815 	bl	IEDTree_unlock

                     816 ;366: 


                     817 ;367: 	if(result < 1)


                     818 

000003d0 ea000003    819 	b	.L253

                     820 .L227:

                     821 ;359:         }


                     822 ;360:         else


                     823 ;361:         {


                     824 

                     825 ;362:             result = responseBuf.pos;


                     826 

000003d4 e59d4024    827 	ldr	r4,[sp,36]

                     828 ;363:         }


                     829 ;364: 	}


                     830 ;365: 	IEDTree_unlock();


                     831 

000003d8 eb000000*   832 	bl	IEDTree_unlock

                     833 ;366: 


                     834 ;367: 	if(result < 1)


                     835 

000003dc e3540000    836 	cmp	r4,0

000003e0 ca000004    837 	bgt	.L252

                     838 .L253:

                     839 ;368: 	{


                     840 

                     841 ;369: 		return CreateMmsConfirmedErrorPdu(invokeId, response, 


                     842 

000003e4 e59d1064    843 	ldr	r1,[sp,100]


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8r81.s
000003e8 e59d0060    844 	ldr	r0,[sp,96]

000003ec e3a02051    845 	mov	r2,81

000003f0 eb000000*   846 	bl	CreateMmsConfirmedErrorPdu

000003f4 ea000000    847 	b	.L171

                     848 .L252:

                     849 ;370: 			MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);


                     850 ;371: 	}	


                     851 ;372: 		


                     852 ;373: 	//TODO Обработать случай когда domainIdStr и itemIdStr неинициализированы


                     853 ;374: 	return result;


                     854 

000003f8 e1a00004    855 	mov	r0,r4

                     856 .L171:

000003fc e28dd044    857 	add	sp,sp,68

00000400 e8bd8cf0    858 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                     859 	.endf	mms_handleReadRequest

                     860 	.align	4

                     861 ;result	r4	local

                     862 ;objectName	[sp,56]	local

                     863 ;namedVariableListRequest	[sp,3]	local

                     864 ;tag	r5	local

                     865 ;iLength	[sp,4]	local

                     866 ;specListBer	[sp,44]	local

                     867 ;responseBuf	[sp,32]	local

                     868 ;object	r0	local

                     869 ;objIndex	r5	local

                     870 ;domainId	[sp,24]	local

                     871 ;itemId	[sp,16]	local

                     872 ;dataSetEntity	r4	local

                     873 ;readDataSize	[sp,8]	local

                     874 ;readDataSizeWithTL	r5	local

                     875 ;objectSpecSize	r10	local

                     876 ;objectSpecRespSize	r6	local

                     877 ;readResponseSize	r7	local

                     878 ;invokeIdSize	r0	local

                     879 ;accessResultSize	r6	local

                     880 ;confirmedServiceResponseContentLength	r5	local

                     881 ;confirmedServiceResponseLength	r7	local

                     882 ;invokeIdSize	r0	local

                     883 ;objReadLen	[sp,12]	local

                     884 

                     885 ;mmsConn	r4	param

                     886 ;inBuf	r7	param

                     887 ;bufPos	r6	param

                     888 ;maxBufPos	fp	param

                     889 ;invokeId	[sp,96]	param

                     890 ;response	[sp,100]	param

                     891 ;maxRespSize	r12	param

                     892 

                     893 	.section ".bss","awb"

                     894 .L891:

                     895 	.data

                     896 	.text

                     897 

                     898 ;375: }


                     899 	.align	4

                     900 

                     901 	.data

                     902 	.ghsnote version,6

                     903 	.ghsnote tools,3

                     904 	.ghsnote options,0


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8r81.s
                     905 	.text

                     906 	.align	4

