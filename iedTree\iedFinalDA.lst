                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedFinalDA.c -o iedTree\gh_bfs1.o -list=iedTree/iedFinalDA.lst C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
Source File: iedFinalDA.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		iedTree/iedFinalDA.c -o iedTree/iedFinalDA.o

                      12 ;Source File:   iedTree/iedFinalDA.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:49 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "iedFinalDA.h"


                      23 ;2: 


                      24 ;3: #include "iedControlModel.h"


                      25 ;4: 


                      26 ;5: 


                      27 ;6: #include "iedQuality.h"


                      28 ;7: #include "iedFloat.h"


                      29 ;8: #include "iedInt.h"


                      30 ;9: #include "iedUInt.h"


                      31 ;10: #include "iedEnum.h"


                      32 ;11: #include "iedConstDA.h"


                      33 ;12: #include "../mms_data.h"


                      34 ;13: #include "../mms_rcb.h"


                      35 ;14: #include "../mms_gocb.h"


                      36 ;15: #include "../mms_write.h"


                      37 ;16: #include "../DataSlice.h"


                      38 ;17: 


                      39 ;18: #include "IEDCompile/AccessInfo.h"


                      40 ;19: #include "IEDCompile/InnerAttributeTypes.h"


                      41 ;20: 


                      42 ;21: #include "../iedmodel.h"


                      43 ;22: 


                      44 ;23: #include "../BaseAsnTypes.h"


                      45 ;24: 


                      46 ;25: //!!! Для отладки


                      47 ;26: int settCounter = 0;


                      48 ;27: void incSettCounter()


                      49 ;28: {


                      50 ;29:     settCounter++;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
                      51 ;30: }


                      52 ;31: 


                      53 ;32: 


                      54 ;33: //==================Инициализация TerminalItemDA==============


                      55 ;34: 


                      56 ;35: 


                      57 ;36: // Функция инициализирует специфические поля для Terminal Item DA.


                      58 ;37: // ber указывает на AccessInfo до выравнивания.


                      59 ;38: // По завершению функции состояние ber не сохраняется.


                      60 ;39: static bool initTermItemDA(IEDEntity entity, BufferView* ber)


                      61 

                      62 ;106: }


                      63 

                      64 ;107: 


                      65 ;108: static bool initTimeStampDA(IEDEntity entity)


                      66 

                      67 ;120: }


                      68 

                      69 	.text

                      70 	.align	4

                      71 incSettCounter::

00000000 e59f1650*    72 	ldr	r1,.L117

00000004 e5910000     73 	ldr	r0,[r1]

00000008 e2800001     74 	add	r0,r0,1

0000000c e5810000     75 	str	r0,[r1]

00000010 e12fff1e*    76 	ret	

                      77 	.endf	incSettCounter

                      78 	.align	4

                      79 

                      80 	.section ".bss","awb"

                      81 .L110:

                      82 	.data

                      83 	.text

                      84 

                      85 

                      86 ;121: 


                      87 ;122: bool IEDFinalDA_init(IEDEntity entity)


                      88 	.align	4

                      89 	.align	4

                      90 IEDFinalDA_init::

00000014 e92d4030     91 	stmfd	[sp]!,{r4-r5,lr}

                      92 ;123: {


                      93 

                      94 ;124:     enum InnerAttributeType attrType;


                      95 ;125:     BufferView ber = entity->ber;


                      96 

00000018 e24dd010     97 	sub	sp,sp,16

0000001c e28d3004     98 	add	r3,sp,4

00000020 e1a05000     99 	mov	r5,r0

00000024 e2850014    100 	add	r0,r5,20

00000028 e8900007    101 	ldmfd	[r0],{r0-r2}

0000002c e8830007    102 	stmea	[r3],{r0-r2}

                     103 ;126: 


                     104 ;127: 


                     105 ;128:     //Пропустить тэг и длину


                     106 ;129:     BufferView_decodeTL(&ber, NULL, NULL, NULL);


                     107 

00000030 e1a00003    108 	mov	r0,r3

00000034 e3a03000    109 	mov	r3,0

00000038 e1a02003    110 	mov	r2,r3

0000003c e1a01003    111 	mov	r1,r3


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
00000040 eb000000*   112 	bl	BufferView_decodeTL

                     113 ;130: 


                     114 ;131:     if(!IEDModel_skipServiceInfo(&ber))


                     115 

00000044 e28d0004    116 	add	r0,sp,4

00000048 eb000000*   117 	bl	IEDModel_skipServiceInfo

0000004c e3500000    118 	cmp	r0,0

00000050 0a000004    119 	beq	.L124

                     120 ;132:     {


                     121 

                     122 ;133:         return false;


                     123 

                     124 ;134:     }


                     125 ;135:     if(!BufferView_decodeUInt32TL(&ber, ASN_INTEGER, (uint32_t*)&attrType))


                     126 

00000054 e1a0200d    127 	mov	r2,sp

00000058 e28d0004    128 	add	r0,sp,4

0000005c e3a01002    129 	mov	r1,2

00000060 eb000000*   130 	bl	BufferView_decodeUInt32TL

00000064 e3500000    131 	cmp	r0,0

                     132 .L124:

                     133 ;136:     {


                     134 

                     135 ;137:         ERROR_REPORT("Error decoding attribute type");


                     136 ;138:         return false;


                     137 

00000068 03a00000    138 	moveq	r0,0

0000006c 0a000059    139 	beq	.L118

                     140 .L123:

                     141 ;139:     }


                     142 ;140: 


                     143 ;141:     if(attrType == INNER_TYPE_TIME_STAMP)


                     144 

00000070 e59d0000    145 	ldr	r0,[sp]

00000074 e3500003    146 	cmp	r0,3

00000078 1a00000b    147 	bne	.L126

                     148 ;142:     {


                     149 

                     150 ;143:         // ----- t


                     151 ;144:         return initTimeStampDA(entity);


                     152 

                     153 ;109: {


                     154 

                     155 ;110:     TimeStamp* extInfo;


                     156 ;111:     entity->type = IED_ENTITY_DA_TIMESTAMP;


                     157 

0000007c e3a00009    158 	mov	r0,9

00000080 e5850050    159 	str	r0,[r5,80]

                     160 ;112:     extInfo = IEDEntity_alloc(sizeof(TimeStamp));


                     161 

00000084 e3a00008    162 	mov	r0,8

00000088 eb000000*   163 	bl	IEDEntity_alloc

0000008c e1b04000    164 	movs	r4,r0

                     165 ;113:     if(extInfo == NULL)


                     166 

                     167 ;114:     {


                     168 

                     169 ;115:         return false;


                     170 

00000090 020400ff    171 	andeq	r0,r4,255

00000094 0a00004f    172 	beq	.L118


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
                     173 ;116:     }


                     174 ;117:     entity->extInfo = extInfo;


                     175 

00000098 e5854058    176 	str	r4,[r5,88]

                     177 ;118:     extInfo->timeStamp = dataSliceGetTimeStamp();


                     178 

0000009c eb000000*   179 	bl	dataSliceGetTimeStamp

000000a0 e8840003    180 	stmea	[r4],{r0-r1}

                     181 ;119:     return true;


                     182 

000000a4 e3a00001    183 	mov	r0,1

000000a8 ea00004a    184 	b	.L118

                     185 .L126:

                     186 ;145:     }


                     187 ;146:     else if(attrType == INNER_TYPE_CONST)


                     188 

000000ac e350001f    189 	cmp	r0,31

000000b0 1a000003    190 	bne	.L136

                     191 ;147:     {


                     192 

                     193 ;148:         // ----- const


                     194 ;149:         return IEDConstDA_init(entity, &ber);        


                     195 

000000b4 e28d1004    196 	add	r1,sp,4

000000b8 e1a00005    197 	mov	r0,r5

000000bc eb000000*   198 	bl	IEDConstDA_init

000000c0 ea000044    199 	b	.L118

                     200 .L136:

                     201 ;150:     }


                     202 ;151:     else


                     203 ;152:     {


                     204 

                     205 ;153:         // ----- Всё остальное


                     206 ;154: 


                     207 ;155:         entity->type = IED_ENTITY_DA_TERMINAL_ITEM;


                     208 

000000c4 e3a01008    209 	mov	r1,8

000000c8 e5851050    210 	str	r1,[r5,80]

                     211 ;156:         entity->subType = attrType;


                     212 

000000cc e5850054    213 	str	r0,[r5,84]

                     214 ;157:         return initTermItemDA(entity, &ber);


                     215 

                     216 ;40: {


                     217 

                     218 ;41:     TerminalItem* extInfo = IEDEntity_alloc(sizeof(TerminalItem));


                     219 

000000d0 e3a00040    220 	mov	r0,64

000000d4 eb000000*   221 	bl	IEDEntity_alloc

000000d8 e1b01000    222 	movs	r1,r0

                     223 ;42:     if(extInfo == NULL)


                     224 

000000dc 0a000003    225 	beq	.L145

                     226 ;43:     {


                     227 

                     228 ;44:         return false;


                     229 

                     230 ;45:     }


                     231 ;46:     entity->extInfo = extInfo;


                     232 

000000e0 e5851058    233 	str	r1,[r5,88]


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
                     234 ;47: 


                     235 ;48:     if(!IEDModel_getTermItemDescrStruct(ber, &extInfo->accessInfo))


                     236 

000000e4 e28d0004    237 	add	r0,sp,4

000000e8 eb000000*   238 	bl	IEDModel_getTermItemDescrStruct

000000ec e3500000    239 	cmp	r0,0

                     240 .L145:

                     241 ;49:     {


                     242 

                     243 ;50:         return false;


                     244 

000000f0 03a00000    245 	moveq	r0,0

000000f4 0a000037    246 	beq	.L118

                     247 .L146:

                     248 ;51:     }


                     249 ;52: 


                     250 ;53:     switch(entity->subType)


                     251 

000000f8 e5950054    252 	ldr	r0,[r5,84]

000000fc e2500007    253 	subs	r0,r0,7

00000100 2a000008    254 	bhs	.L420

00000104 e2900001    255 	adds	r0,r0,1

00000108 0a000029    256 	beq	.L153

0000010c e2900002    257 	adds	r0,r0,2

00000110 0a00001b    258 	beq	.L150

00000114 e2900002    259 	adds	r0,r0,2

00000118 0a000011    260 	beq	.L148

0000011c e2900002    261 	adds	r0,r0,2

00000120 1a00002b    262 	bne	.L156

00000124 ea00000a    263 	b	.L147

                     264 .L420:

                     265 

00000128 e2500001    266 	subs	r0,r0,1

0000012c 3a00001c    267 	blo	.L152

00000130 0a000017    268 	beq	.L151

00000134 e2500001    269 	subs	r0,r0,1

00000138 0a000021    270 	beq	.L154

0000013c e250001e    271 	subs	r0,r0,30

00000140 0a00000b    272 	beq	.L149

00000144 e3500002    273 	cmp	r0,2

                     274 ;81:             break;


                     275 ;82:         case INNER_TYPE_REAL_AS_INT64:


                     276 ;83:             IEDRealAsInt64_init(entity);


                     277 

00000148 01a00005    278 	moveq	r0,r5

0000014c 0b000000*   279 	bleq	IEDRealAsInt64_init

00000150 ea00001f    280 	b	.L156

                     281 .L147:

                     282 ;54:     {


                     283 ;55:         case INNER_TYPE_QUALITY:


                     284 ;56:             IEDQuality_init(entity);


                     285 

00000154 e1a00005    286 	mov	r0,r5

00000158 eb000000*   287 	bl	IEDQuality_init

                     288 ;84:             break;


                     289 ;85: 


                     290 ;86:         //Если неизвестный тип, то останется функция-заглушка


                     291 ;87: 


                     292 ;88:         /*!!!


                     293 ;89:         Это код для подсчёта уставок. Нужен для разработки DataSlice для уставок.


                     294 ;90:         Для нормальной работы не нужен.



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
                     295 ;91: 


                     296 ;92:         case INNER_TYPE_REAL_SETT:


                     297 ;93:         case INNER_TYPE_INT32_SETTS:


                     298 ;94:         case INNER_TYPE_ENUMERATED_SETTS:


                     299 ;95:         case INNER_TYPE_INT32U_SETTS:


                     300 ;96:         case INNER_TYPE_FLOAT_SETT:


                     301 ;97:             incSettCounter();


                     302 ;98: 


                     303 ;99: 


                     304 ;100:             break;


                     305 ;101:         */


                     306 ;102: 


                     307 ;103:     }


                     308 ;104: 


                     309 ;105:     return true;


                     310 

0000015c e3a00001    311 	mov	r0,1

00000160 ea00001c    312 	b	.L118

                     313 .L148:

                     314 ;57:             break;


                     315 ;58:         case INNER_TYPE_FLOAT_VALUE:


                     316 ;59:             IEDFloat_init(entity);


                     317 

00000164 e1a00005    318 	mov	r0,r5

00000168 eb000000*   319 	bl	IEDFloat_init

                     320 ;84:             break;


                     321 ;85: 


                     322 ;86:         //Если неизвестный тип, то останется функция-заглушка


                     323 ;87: 


                     324 ;88:         /*!!!


                     325 ;89:         Это код для подсчёта уставок. Нужен для разработки DataSlice для уставок.


                     326 ;90:         Для нормальной работы не нужен.


                     327 ;91: 


                     328 ;92:         case INNER_TYPE_REAL_SETT:


                     329 ;93:         case INNER_TYPE_INT32_SETTS:


                     330 ;94:         case INNER_TYPE_ENUMERATED_SETTS:


                     331 ;95:         case INNER_TYPE_INT32U_SETTS:


                     332 ;96:         case INNER_TYPE_FLOAT_SETT:


                     333 ;97:             incSettCounter();


                     334 ;98: 


                     335 ;99: 


                     336 ;100:             break;


                     337 ;101:         */


                     338 ;102: 


                     339 ;103:     }


                     340 ;104: 


                     341 ;105:     return true;


                     342 

0000016c e3a00001    343 	mov	r0,1

00000170 ea000018    344 	b	.L118

                     345 .L149:

                     346 ;60:             break;


                     347 ;61:         case INNER_TYPE_REAL_VALUE:


                     348 ;62:             IEDReal_init(entity);


                     349 

00000174 e1a00005    350 	mov	r0,r5

00000178 eb000000*   351 	bl	IEDReal_init

                     352 ;84:             break;


                     353 ;85: 


                     354 ;86:         //Если неизвестный тип, то останется функция-заглушка


                     355 ;87: 



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
                     356 ;88:         /*!!!


                     357 ;89:         Это код для подсчёта уставок. Нужен для разработки DataSlice для уставок.


                     358 ;90:         Для нормальной работы не нужен.


                     359 ;91: 


                     360 ;92:         case INNER_TYPE_REAL_SETT:


                     361 ;93:         case INNER_TYPE_INT32_SETTS:


                     362 ;94:         case INNER_TYPE_ENUMERATED_SETTS:


                     363 ;95:         case INNER_TYPE_INT32U_SETTS:


                     364 ;96:         case INNER_TYPE_FLOAT_SETT:


                     365 ;97:             incSettCounter();


                     366 ;98: 


                     367 ;99: 


                     368 ;100:             break;


                     369 ;101:         */


                     370 ;102: 


                     371 ;103:     }


                     372 ;104: 


                     373 ;105:     return true;


                     374 

0000017c e3a00001    375 	mov	r0,1

00000180 ea000014    376 	b	.L118

                     377 .L150:

                     378 ;63:             break;


                     379 ;64:         case INNER_TYPE_INT32:


                     380 ;65:             IEDInt32_init(entity);


                     381 

00000184 e1a00005    382 	mov	r0,r5

00000188 eb000000*   383 	bl	IEDInt32_init

                     384 ;84:             break;


                     385 ;85: 


                     386 ;86:         //Если неизвестный тип, то останется функция-заглушка


                     387 ;87: 


                     388 ;88:         /*!!!


                     389 ;89:         Это код для подсчёта уставок. Нужен для разработки DataSlice для уставок.


                     390 ;90:         Для нормальной работы не нужен.


                     391 ;91: 


                     392 ;92:         case INNER_TYPE_REAL_SETT:


                     393 ;93:         case INNER_TYPE_INT32_SETTS:


                     394 ;94:         case INNER_TYPE_ENUMERATED_SETTS:


                     395 ;95:         case INNER_TYPE_INT32U_SETTS:


                     396 ;96:         case INNER_TYPE_FLOAT_SETT:


                     397 ;97:             incSettCounter();


                     398 ;98: 


                     399 ;99: 


                     400 ;100:             break;


                     401 ;101:         */


                     402 ;102: 


                     403 ;103:     }


                     404 ;104: 


                     405 ;105:     return true;


                     406 

0000018c e3a00001    407 	mov	r0,1

00000190 ea000010    408 	b	.L118

                     409 .L151:

                     410 ;66:             break;


                     411 ;67:         case INNER_TYPE_INT32U:


                     412 ;68:             IEDUInt32_init(entity);


                     413 

00000194 e1a00005    414 	mov	r0,r5

00000198 eb000000*   415 	bl	IEDUInt32_init

                     416 ;84:             break;



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
                     417 ;85: 


                     418 ;86:         //Если неизвестный тип, то останется функция-заглушка


                     419 ;87: 


                     420 ;88:         /*!!!


                     421 ;89:         Это код для подсчёта уставок. Нужен для разработки DataSlice для уставок.


                     422 ;90:         Для нормальной работы не нужен.


                     423 ;91: 


                     424 ;92:         case INNER_TYPE_REAL_SETT:


                     425 ;93:         case INNER_TYPE_INT32_SETTS:


                     426 ;94:         case INNER_TYPE_ENUMERATED_SETTS:


                     427 ;95:         case INNER_TYPE_INT32U_SETTS:


                     428 ;96:         case INNER_TYPE_FLOAT_SETT:


                     429 ;97:             incSettCounter();


                     430 ;98: 


                     431 ;99: 


                     432 ;100:             break;


                     433 ;101:         */


                     434 ;102: 


                     435 ;103:     }


                     436 ;104: 


                     437 ;105:     return true;


                     438 

0000019c e3a00001    439 	mov	r0,1

000001a0 ea00000c    440 	b	.L118

                     441 .L152:

                     442 ;69:             break;


                     443 ;70:         case INNER_TYPE_INT8U:


                     444 ;71:             ERROR_REPORT("INNER_TYPE_INT8U values not implemented");


                     445 ;72:             break;


                     446 ;73:         case INNER_TYPE_ENUMERATED:


                     447 ;74:             IEDEnum_init(entity);


                     448 

000001a4 e1a00005    449 	mov	r0,r5

000001a8 eb000000*   450 	bl	IEDEnum_init

                     451 ;84:             break;


                     452 ;85: 


                     453 ;86:         //Если неизвестный тип, то останется функция-заглушка


                     454 ;87: 


                     455 ;88:         /*!!!


                     456 ;89:         Это код для подсчёта уставок. Нужен для разработки DataSlice для уставок.


                     457 ;90:         Для нормальной работы не нужен.


                     458 ;91: 


                     459 ;92:         case INNER_TYPE_REAL_SETT:


                     460 ;93:         case INNER_TYPE_INT32_SETTS:


                     461 ;94:         case INNER_TYPE_ENUMERATED_SETTS:


                     462 ;95:         case INNER_TYPE_INT32U_SETTS:


                     463 ;96:         case INNER_TYPE_FLOAT_SETT:


                     464 ;97:             incSettCounter();


                     465 ;98: 


                     466 ;99: 


                     467 ;100:             break;


                     468 ;101:         */


                     469 ;102: 


                     470 ;103:     }


                     471 ;104: 


                     472 ;105:     return true;


                     473 

000001ac e3a00001    474 	mov	r0,1

000001b0 ea000008    475 	b	.L118

                     476 .L153:

                     477 ;75:             break;



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
                     478 ;76:         case INNER_TYPE_BOOLEAN:


                     479 ;77:             IEDBool_init(entity);


                     480 

000001b4 e1a00005    481 	mov	r0,r5

000001b8 eb000000*   482 	bl	IEDBool_init

                     483 ;84:             break;


                     484 ;85: 


                     485 ;86:         //Если неизвестный тип, то останется функция-заглушка


                     486 ;87: 


                     487 ;88:         /*!!!


                     488 ;89:         Это код для подсчёта уставок. Нужен для разработки DataSlice для уставок.


                     489 ;90:         Для нормальной работы не нужен.


                     490 ;91: 


                     491 ;92:         case INNER_TYPE_REAL_SETT:


                     492 ;93:         case INNER_TYPE_INT32_SETTS:


                     493 ;94:         case INNER_TYPE_ENUMERATED_SETTS:


                     494 ;95:         case INNER_TYPE_INT32U_SETTS:


                     495 ;96:         case INNER_TYPE_FLOAT_SETT:


                     496 ;97:             incSettCounter();


                     497 ;98: 


                     498 ;99: 


                     499 ;100:             break;


                     500 ;101:         */


                     501 ;102: 


                     502 ;103:     }


                     503 ;104: 


                     504 ;105:     return true;


                     505 

000001bc e3a00001    506 	mov	r0,1

000001c0 ea000004    507 	b	.L118

                     508 .L154:

                     509 ;78:             break;


                     510 ;79:         case INNER_TYPE_CODEDENUM:


                     511 ;80:             IEDCodedEnum_init(entity);


                     512 

000001c4 e1a00005    513 	mov	r0,r5

000001c8 eb000000*   514 	bl	IEDCodedEnum_init

                     515 ;84:             break;


                     516 ;85: 


                     517 ;86:         //Если неизвестный тип, то останется функция-заглушка


                     518 ;87: 


                     519 ;88:         /*!!!


                     520 ;89:         Это код для подсчёта уставок. Нужен для разработки DataSlice для уставок.


                     521 ;90:         Для нормальной работы не нужен.


                     522 ;91: 


                     523 ;92:         case INNER_TYPE_REAL_SETT:


                     524 ;93:         case INNER_TYPE_INT32_SETTS:


                     525 ;94:         case INNER_TYPE_ENUMERATED_SETTS:


                     526 ;95:         case INNER_TYPE_INT32U_SETTS:


                     527 ;96:         case INNER_TYPE_FLOAT_SETT:


                     528 ;97:             incSettCounter();


                     529 ;98: 


                     530 ;99: 


                     531 ;100:             break;


                     532 ;101:         */


                     533 ;102: 


                     534 ;103:     }


                     535 ;104: 


                     536 ;105:     return true;


                     537 

000001cc e3a00001    538 	mov	r0,1


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
000001d0 ea000000    539 	b	.L118

                     540 .L156:

                     541 ;84:             break;


                     542 ;85: 


                     543 ;86:         //Если неизвестный тип, то останется функция-заглушка


                     544 ;87: 


                     545 ;88:         /*!!!


                     546 ;89:         Это код для подсчёта уставок. Нужен для разработки DataSlice для уставок.


                     547 ;90:         Для нормальной работы не нужен.


                     548 ;91: 


                     549 ;92:         case INNER_TYPE_REAL_SETT:


                     550 ;93:         case INNER_TYPE_INT32_SETTS:


                     551 ;94:         case INNER_TYPE_ENUMERATED_SETTS:


                     552 ;95:         case INNER_TYPE_INT32U_SETTS:


                     553 ;96:         case INNER_TYPE_FLOAT_SETT:


                     554 ;97:             incSettCounter();


                     555 ;98: 


                     556 ;99: 


                     557 ;100:             break;


                     558 ;101:         */


                     559 ;102: 


                     560 ;103:     }


                     561 ;104: 


                     562 ;105:     return true;


                     563 

000001d4 e3a00001    564 	mov	r0,1

                     565 .L118:

000001d8 e28dd010    566 	add	sp,sp,16

000001dc e8bd8030    567 	ldmfd	[sp]!,{r4-r5,pc}

                     568 	.endf	IEDFinalDA_init

                     569 	.align	4

                     570 ;attrType	[sp]	local

                     571 ;ber	[sp,4]	local

                     572 ;extInfo	r4	local

                     573 ;extInfo	r1	local

                     574 

                     575 ;entity	r5	param

                     576 

                     577 	.section ".bss","awb"

                     578 .L419:

                     579 	.data

                     580 	.text

                     581 

                     582 ;158:     }


                     583 ;159: }


                     584 

                     585 ;160: 


                     586 ;161: bool IEDVarDA_calcReadLen(IEDEntity entity, size_t *pLen)


                     587 	.align	4

                     588 	.align	4

                     589 IEDVarDA_calcReadLen::

                     590 ;162: {


                     591 

                     592 ;163:     if(entity->type != IED_ENTITY_DA_VAR)


                     593 

000001e0 e5902050    594 	ldr	r2,[r0,80]

000001e4 e352000a    595 	cmp	r2,10

000001e8 1a000006    596 	bne	.L505

                     597 ;164:     {


                     598 

                     599 ;165:         return false;



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
                     600 

                     601 ;166:     }


                     602 ;167:     switch(entity->subType)


                     603 

000001ec e5902054    604 	ldr	r2,[r0,84]

000001f0 e2522002    605 	subs	r2,r2,2

000001f4 0a000002    606 	beq	.L501

000001f8 e3520001    607 	cmp	r2,1

                     608 ;171:     case DA_SUBTYPE_ORCAT:


                     609 ;172:         return IEDOrCat_calcReadLen(entity, pLen);


                     610 

000001fc 0a000000*   611 	beq	IEDOrCat_calcReadLen

00000200 ea000000    612 	b	.L505

                     613 .L501:

                     614 ;168:     {


                     615 ;169:     case DA_SUBTYPE_ORIDENT:


                     616 ;170:         return IEDOrIdent_calcReadLen(entity, pLen);


                     617 

00000204 ea000000*   618 	b	IEDOrIdent_calcReadLen

                     619 .L505:

                     620 ;173:     default:


                     621 ;174:         return false;


                     622 

00000208 e3a00000    623 	mov	r0,0

0000020c e12fff1e*   624 	ret	

                     625 	.endf	IEDVarDA_calcReadLen

                     626 	.align	4

                     627 

                     628 ;entity	r0	param

                     629 ;pLen	r1	param

                     630 

                     631 	.section ".bss","awb"

                     632 .L569:

                     633 	.data

                     634 	.text

                     635 

                     636 ;175:     }


                     637 ;176: }


                     638 

                     639 ;177: 


                     640 ;178: bool IEDVarDA_encodeRead(IEDEntity entity, BufferView *outBuf)


                     641 	.align	4

                     642 	.align	4

                     643 IEDVarDA_encodeRead::

                     644 ;179: {


                     645 

                     646 ;180:     if(entity->type != IED_ENTITY_DA_VAR)


                     647 

00000210 e5902050    648 	ldr	r2,[r0,80]

00000214 e352000a    649 	cmp	r2,10

00000218 1a000006    650 	bne	.L600

                     651 ;181:     {


                     652 

                     653 ;182:         return false;


                     654 

                     655 ;183:     }


                     656 ;184:     switch(entity->subType)


                     657 

0000021c e5902054    658 	ldr	r2,[r0,84]

00000220 e2522002    659 	subs	r2,r2,2

00000224 0a000002    660 	beq	.L596


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
00000228 e3520001    661 	cmp	r2,1

                     662 ;188:     case DA_SUBTYPE_ORCAT:


                     663 ;189:         return IEDOrCat_encodeRead(entity, outBuf);


                     664 

0000022c 0a000000*   665 	beq	IEDOrCat_encodeRead

00000230 ea000000    666 	b	.L600

                     667 .L596:

                     668 ;185:     {


                     669 ;186:     case DA_SUBTYPE_ORIDENT:


                     670 ;187:         return IEDOrIdent_encodeRead(entity, outBuf);


                     671 

00000234 ea000000*   672 	b	IEDOrIdent_encodeRead

                     673 .L600:

                     674 ;190:     default:


                     675 ;191:         return false;


                     676 

00000238 e3a00000    677 	mov	r0,0

0000023c e12fff1e*   678 	ret	

                     679 	.endf	IEDVarDA_encodeRead

                     680 	.align	4

                     681 

                     682 ;entity	r0	param

                     683 ;outBuf	r1	param

                     684 

                     685 	.section ".bss","awb"

                     686 .L665:

                     687 	.data

                     688 	.text

                     689 

                     690 ;192:     }


                     691 ;193: }


                     692 

                     693 ;194: 


                     694 ;195: MmsDataAccessError IEDVarDA_write(IEDEntity entity,


                     695 	.align	4

                     696 	.align	4

                     697 IEDVarDA_write::

                     698 ;196:                                            IsoConnection* isoConn, BufferView* value)


                     699 ;197: {


                     700 

                     701 ;198:     switch (entity->subType)


                     702 

00000240 e5903054    703 	ldr	r3,[r0,84]

00000244 e2533002    704 	subs	r3,r3,2

00000248 0a000002    705 	beq	.L689

0000024c e3530001    706 	cmp	r3,1

                     707 ;202:     case DA_SUBTYPE_ORCAT:


                     708 ;203:         return IEDOrCat_write(entity, isoConn, value);


                     709 

00000250 0a000000*   710 	beq	IEDOrCat_write

00000254 ea000000    711 	b	.L693

                     712 .L689:

                     713 ;199:     {


                     714 ;200:     case DA_SUBTYPE_ORIDENT:


                     715 ;201:         return IEDOrIdent_write(entity, isoConn, value);


                     716 

00000258 ea000000*   717 	b	IEDOrIdent_write

                     718 .L693:

                     719 ;204:     default:


                     720 ;205:         ERROR_REPORT("Invalid object to write");


                     721 ;206:         return DATA_ACCESS_ERROR_OBJECT_ACCESS_UNSUPPORTED;



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
                     722 

0000025c e3a00009    723 	mov	r0,9

00000260 e12fff1e*   724 	ret	

                     725 	.endf	IEDVarDA_write

                     726 	.align	4

                     727 

                     728 ;entity	r0	param

                     729 ;isoConn	r1	param

                     730 ;value	r2	param

                     731 

                     732 	.section ".bss","awb"

                     733 .L730:

                     734 	.data

                     735 	.text

                     736 

                     737 ;207:     }


                     738 ;208: }


                     739 

                     740 ;209: 


                     741 ;210: void* IEDTermItemDA_getTerminalItemDescr(IEDEntity entity)


                     742 	.align	4

                     743 	.align	4

                     744 IEDTermItemDA_getTerminalItemDescr::

                     745 ;211: {


                     746 

                     747 ;212:     TerminalItem* extInfo;


                     748 ;213:     if(entity->type != IED_ENTITY_DA_TERMINAL_ITEM)


                     749 

00000264 e5901050    750 	ldr	r1,[r0,80]

00000268 e3510008    751 	cmp	r1,8

                     752 ;214:     {


                     753 

                     754 ;215:         return NULL;


                     755 

0000026c 13a00000    756 	movne	r0,0

                     757 ;216:     }


                     758 ;217:     extInfo = entity->extInfo;


                     759 

00000270 05900058    760 	ldreq	r0,[r0,88]

                     761 ;218:     return extInfo->accessInfo;


                     762 

00000274 05900000    763 	ldreq	r0,[r0]

00000278 e12fff1e*   764 	ret	

                     765 	.endf	IEDTermItemDA_getTerminalItemDescr

                     766 	.align	4

                     767 ;extInfo	r0	local

                     768 

                     769 ;entity	r0	param

                     770 

                     771 	.section ".bss","awb"

                     772 .L774:

                     773 	.data

                     774 	.text

                     775 

                     776 ;219: }


                     777 

                     778 ;220: 


                     779 ;221: bool IEDTermItemDA_calcReadLen(IEDEntity entity, size_t *pLen)


                     780 	.align	4

                     781 	.align	4

                     782 IEDTermItemDA_calcReadLen::


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
0000027c e92d4010    783 	stmfd	[sp]!,{r4,lr}

00000280 e1a04001    784 	mov	r4,r1

                     785 ;222: {


                     786 

                     787 ;223:     void* pDescrStruct;


                     788 ;224:     int dataLen;


                     789 ;225:     TerminalItem* termItem;


                     790 ;226: 


                     791 ;227:     if(entity->type != IED_ENTITY_DA_TERMINAL_ITEM


                     792 

00000284 e5901050    793 	ldr	r1,[r0,80]

00000288 e3510008    794 	cmp	r1,8

0000028c 1a000047    795 	bne	.L805

00000290 e5902058    796 	ldr	r2,[r0,88]

00000294 e3520000    797 	cmp	r2,0

00000298 0a000044    798 	beq	.L805

                     799 ;228:             || entity->extInfo == NULL)


                     800 ;229:     {


                     801 

                     802 ;230:         ERROR_REPORT("Invalid terminal item DA");


                     803 ;231:         return false;


                     804 

                     805 ;232:     }


                     806 ;233: 


                     807 ;234:     termItem = entity->extInfo;


                     808 

                     809 ;235:     pDescrStruct = termItem->accessInfo;


                     810 

0000029c e5900054    811 	ldr	r0,[r0,84]

000002a0 e5922000    812 	ldr	r2,[r2]

                     813 ;236:     switch (entity->subType)


                     814 

000002a4 e2500023    815 	subs	r0,r0,35

000002a8 2a000007    816 	bhs	.L921

000002ac e2900002    817 	adds	r0,r0,2

000002b0 8a00002a    818 	bhi	.L799

000002b4 0a00001b    819 	beq	.L797

000002b8 e2900001    820 	adds	r0,r0,1

000002bc 0a000012    821 	beq	.L796

000002c0 e2900015    822 	adds	r0,r0,21

000002c4 1a000039    823 	bne	.L805

000002c8 ea000008    824 	b	.L795

                     825 .L921:

                     826 

000002cc e2500000    827 	subs	r0,r0,0

000002d0 0a000029    828 	beq	.L800

000002d4 e2500002    829 	subs	r0,r0,2

000002d8 0a000020    830 	beq	.L799

000002dc e2500001    831 	subs	r0,r0,1

000002e0 0a00002c    832 	beq	.L801

000002e4 e3500002    833 	cmp	r0,2

000002e8 0a000015    834 	beq	.L798

000002ec ea00002f    835 	b	.L805

                     836 .L795:

                     837 ;237:     {


                     838 ;238:     case INNER_TYPE_INT8U:


                     839 ;239:         dataLen = encodeReadInt32U(NULL, 0, pDescrStruct, true);


                     840 

000002f0 e3a03001    841 	mov	r3,1

000002f4 e3a01000    842 	mov	r1,0

000002f8 e1a00001    843 	mov	r0,r1


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
000002fc eb000000*   844 	bl	encodeReadInt32U

                     845 ;259:         break;


                     846 ;260:     default:


                     847 ;261:         ERROR_REPORT("Unsupported data type");


                     848 ;262:         return false;


                     849 

                     850 ;263:     }


                     851 ;264:     if(dataLen <=0)


                     852 

00000300 e3500000    853 	cmp	r0,0

00000304 ca00002b    854 	bgt	.L804

00000308 ea000028    855 	b	.L805

                     856 .L796:

                     857 ;240:         break;


                     858 ;241:     case INNER_TYPE_RCB:


                     859 ;242:         dataLen = encodeReadAttrRCB(NULL, 0, pDescrStruct, true);


                     860 

0000030c e3a03001    861 	mov	r3,1

00000310 e3a01000    862 	mov	r1,0

00000314 e1a00001    863 	mov	r0,r1

00000318 eb000000*   864 	bl	encodeReadAttrRCB

                     865 ;259:         break;


                     866 ;260:     default:


                     867 ;261:         ERROR_REPORT("Unsupported data type");


                     868 ;262:         return false;


                     869 

                     870 ;263:     }


                     871 ;264:     if(dataLen <=0)


                     872 

0000031c e3500000    873 	cmp	r0,0

00000320 ca000024    874 	bgt	.L804

00000324 ea000021    875 	b	.L805

                     876 .L797:

                     877 ;243:         break;


                     878 ;244:     case INNER_TYPE_FLOAT_SETT:


                     879 ;245:         dataLen = encodeReadFloatSett(NULL, 0, pDescrStruct, true);


                     880 

00000328 e3a03001    881 	mov	r3,1

0000032c e3a01000    882 	mov	r1,0

00000330 e1a00001    883 	mov	r0,r1

00000334 eb000000*   884 	bl	encodeReadFloatSett

                     885 ;259:         break;


                     886 ;260:     default:


                     887 ;261:         ERROR_REPORT("Unsupported data type");


                     888 ;262:         return false;


                     889 

                     890 ;263:     }


                     891 ;264:     if(dataLen <=0)


                     892 

00000338 e3500000    893 	cmp	r0,0

0000033c ca00001d    894 	bgt	.L804

00000340 ea00001a    895 	b	.L805

                     896 .L798:

                     897 ;246:         break;


                     898 ;247:     case INNER_TYPE_REAL_SETT:


                     899 ;248:         dataLen = encodeReadRealSett(NULL, 0, pDescrStruct, true);


                     900 

00000344 e3a03001    901 	mov	r3,1

00000348 e3a01000    902 	mov	r1,0

0000034c e1a00001    903 	mov	r0,r1

00000350 eb000000*   904 	bl	encodeReadRealSett


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
                     905 ;259:         break;


                     906 ;260:     default:


                     907 ;261:         ERROR_REPORT("Unsupported data type");


                     908 ;262:         return false;


                     909 

                     910 ;263:     }


                     911 ;264:     if(dataLen <=0)


                     912 

00000354 e3500000    913 	cmp	r0,0

00000358 ca000016    914 	bgt	.L804

0000035c ea000013    915 	b	.L805

                     916 .L799:

                     917 ;249:         break;


                     918 ;250:     case INNER_TYPE_INT32_SETTS:


                     919 ;251:     case INNER_TYPE_ENUMERATED_SETTS:


                     920 ;252:         dataLen = encodeReadInt32Sett(NULL, 0, pDescrStruct, true);


                     921 

00000360 e3a03001    922 	mov	r3,1

00000364 e3a01000    923 	mov	r1,0

00000368 e1a00001    924 	mov	r0,r1

0000036c eb000000*   925 	bl	encodeReadInt32Sett

                     926 ;259:         break;


                     927 ;260:     default:


                     928 ;261:         ERROR_REPORT("Unsupported data type");


                     929 ;262:         return false;


                     930 

                     931 ;263:     }


                     932 ;264:     if(dataLen <=0)


                     933 

00000370 e3500000    934 	cmp	r0,0

00000374 ca00000f    935 	bgt	.L804

00000378 ea00000c    936 	b	.L805

                     937 .L800:

                     938 ;253:         break;


                     939 ;254:     case INNER_TYPE_INT32U_SETTS:


                     940 ;255:         dataLen = encodeReadInt32USett(NULL, 0, pDescrStruct, true);


                     941 

0000037c e3a03001    942 	mov	r3,1

00000380 e3a01000    943 	mov	r1,0

00000384 e1a00001    944 	mov	r0,r1

00000388 eb000000*   945 	bl	encodeReadInt32USett

                     946 ;259:         break;


                     947 ;260:     default:


                     948 ;261:         ERROR_REPORT("Unsupported data type");


                     949 ;262:         return false;


                     950 

                     951 ;263:     }


                     952 ;264:     if(dataLen <=0)


                     953 

0000038c e3500000    954 	cmp	r0,0

00000390 ca000008    955 	bgt	.L804

00000394 ea000005    956 	b	.L805

                     957 .L801:

                     958 ;256:         break;


                     959 ;257:     case INNER_TYPE_GOCB:


                     960 ;258:         dataLen = encodeReadAttrGoCB(NULL, 0, pDescrStruct, true);


                     961 

00000398 e3a03001    962 	mov	r3,1

0000039c e3a01000    963 	mov	r1,0

000003a0 e1a00001    964 	mov	r0,r1

000003a4 eb000000*   965 	bl	encodeReadAttrGoCB


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
                     966 ;259:         break;


                     967 ;260:     default:


                     968 ;261:         ERROR_REPORT("Unsupported data type");


                     969 ;262:         return false;


                     970 

                     971 ;263:     }


                     972 ;264:     if(dataLen <=0)


                     973 

000003a8 e3500000    974 	cmp	r0,0

000003ac ca000001    975 	bgt	.L804

                     976 .L805:

                     977 ;265:     {


                     978 

                     979 ;266:         ERROR_REPORT("Invalid read length");


                     980 ;267:         return false;


                     981 

000003b0 e3a00000    982 	mov	r0,0

000003b4 ea000001    983 	b	.L787

                     984 .L804:

                     985 ;268:     }


                     986 ;269:     *pLen = dataLen;


                     987 

000003b8 e5840000    988 	str	r0,[r4]

                     989 ;270:     return true;


                     990 

000003bc e3a00001    991 	mov	r0,1

                     992 .L787:

000003c0 e8bd8010    993 	ldmfd	[sp]!,{r4,pc}

                     994 	.endf	IEDTermItemDA_calcReadLen

                     995 	.align	4

                     996 ;pDescrStruct	r2	local

                     997 ;dataLen	r0	local

                     998 ;termItem	r2	local

                     999 

                    1000 ;entity	r0	param

                    1001 ;pLen	r4	param

                    1002 

                    1003 	.section ".bss","awb"

                    1004 .L920:

                    1005 	.data

                    1006 	.text

                    1007 

                    1008 ;271: }


                    1009 

                    1010 ;272: 


                    1011 ;273: bool IEDTermItemDA_encodeRead(IEDEntity entity, BufferView *outBufView)


                    1012 	.align	4

                    1013 	.align	4

                    1014 IEDTermItemDA_encodeRead::

000003c4 e92d4010   1015 	stmfd	[sp]!,{r4,lr}

000003c8 e1a03000   1016 	mov	r3,r0

000003cc e1a04001   1017 	mov	r4,r1

                    1018 ;274: {


                    1019 

                    1020 ;275:     void* pDescrStruct;


                    1021 ;276:     uint8_t *writeBuf = outBufView->p + outBufView->pos;


                    1022 

000003d0 e8940006   1023 	ldmfd	[r4],{r1-r2}

000003d4 e0820001   1024 	add	r0,r2,r1

                    1025 ;277:     int dataLen;


                    1026 ;278:     TerminalItem* termItem;



                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
                    1027 ;279: 


                    1028 ;280:     if(entity->type != IED_ENTITY_DA_TERMINAL_ITEM


                    1029 

000003d8 e5931050   1030 	ldr	r1,[r3,80]

000003dc e3510008   1031 	cmp	r1,8

000003e0 1a000045   1032 	bne	.L971

000003e4 e5932058   1033 	ldr	r2,[r3,88]

000003e8 e3520000   1034 	cmp	r2,0

000003ec 0a000042   1035 	beq	.L971

                    1036 ;281:             || entity->extInfo == NULL)


                    1037 ;282:     {


                    1038 

                    1039 ;283:         ERROR_REPORT("Invalid terminal item DA");


                    1040 ;284:         return false;


                    1041 

                    1042 ;285:     }


                    1043 ;286: 


                    1044 ;287:     termItem = entity->extInfo;


                    1045 

                    1046 ;288: 


                    1047 ;289:     pDescrStruct = termItem->accessInfo;


                    1048 

000003f0 e5931054   1049 	ldr	r1,[r3,84]

000003f4 e5922000   1050 	ldr	r2,[r2]

                    1051 ;290:     switch (entity->subType)


                    1052 

000003f8 e2511023   1053 	subs	r1,r1,35

000003fc 2a000007   1054 	bhs	.L1091

00000400 e2911002   1055 	adds	r1,r1,2

00000404 8a000026   1056 	bhi	.L962

00000408 0a000019   1057 	beq	.L960

0000040c e2911001   1058 	adds	r1,r1,1

00000410 0a000011   1059 	beq	.L959

00000414 e2911015   1060 	adds	r1,r1,21

00000418 1a000037   1061 	bne	.L971

0000041c ea000008   1062 	b	.L958

                    1063 .L1091:

                    1064 

00000420 e2511000   1065 	subs	r1,r1,0

00000424 0a000024   1066 	beq	.L963

00000428 e2511002   1067 	subs	r1,r1,2

0000042c 0a00001c   1068 	beq	.L962

00000430 e2511001   1069 	subs	r1,r1,1

00000434 0a000026   1070 	beq	.L964

00000438 e3510002   1071 	cmp	r1,2

0000043c 0a000012   1072 	beq	.L961

00000440 ea00002d   1073 	b	.L971

                    1074 .L958:

                    1075 ;291:     {


                    1076 ;292:     case INNER_TYPE_INT8U:


                    1077 ;293:         dataLen = encodeReadInt32U(writeBuf, 0, pDescrStruct, false);


                    1078 

00000444 e3a03000   1079 	mov	r3,0

00000448 e1a01003   1080 	mov	r1,r3

0000044c eb000000*  1081 	bl	encodeReadInt32U

00000450 e2501000   1082 	subs	r1,r0,0

                    1083 ;313:         break;


                    1084 ;314:     default:


                    1085 ;315:         ERROR_REPORT("Unsupported data type");


                    1086 ;316:         return false;


                    1087 


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
                    1088 ;317:     }


                    1089 ;318:     if(dataLen <=0)


                    1090 

00000454 ca000023   1091 	bgt	.L967

00000458 ea000027   1092 	b	.L971

                    1093 .L959:

                    1094 ;294:         break;


                    1095 ;295:     case INNER_TYPE_RCB:


                    1096 ;296:         dataLen = encodeReadAttrRCB(writeBuf, 0, pDescrStruct, false);


                    1097 

0000045c e3a03000   1098 	mov	r3,0

00000460 e1a01003   1099 	mov	r1,r3

00000464 eb000000*  1100 	bl	encodeReadAttrRCB

00000468 e2501000   1101 	subs	r1,r0,0

                    1102 ;313:         break;


                    1103 ;314:     default:


                    1104 ;315:         ERROR_REPORT("Unsupported data type");


                    1105 ;316:         return false;


                    1106 

                    1107 ;317:     }


                    1108 ;318:     if(dataLen <=0)


                    1109 

0000046c ca00001d   1110 	bgt	.L967

00000470 ea000021   1111 	b	.L971

                    1112 .L960:

                    1113 ;297:         break;


                    1114 ;298:     case INNER_TYPE_FLOAT_SETT:


                    1115 ;299:         dataLen = encodeReadFloatSett(writeBuf, 0, pDescrStruct, false);


                    1116 

00000474 e3a03000   1117 	mov	r3,0

00000478 e1a01003   1118 	mov	r1,r3

0000047c eb000000*  1119 	bl	encodeReadFloatSett

00000480 e2501000   1120 	subs	r1,r0,0

                    1121 ;313:         break;


                    1122 ;314:     default:


                    1123 ;315:         ERROR_REPORT("Unsupported data type");


                    1124 ;316:         return false;


                    1125 

                    1126 ;317:     }


                    1127 ;318:     if(dataLen <=0)


                    1128 

00000484 ca000017   1129 	bgt	.L967

00000488 ea00001b   1130 	b	.L971

                    1131 .L961:

                    1132 ;300:         break;


                    1133 ;301:     case INNER_TYPE_REAL_SETT:


                    1134 ;302:         dataLen = encodeReadRealSett(writeBuf, 0, pDescrStruct, false);


                    1135 

0000048c e3a03000   1136 	mov	r3,0

00000490 e1a01003   1137 	mov	r1,r3

00000494 eb000000*  1138 	bl	encodeReadRealSett

00000498 e2501000   1139 	subs	r1,r0,0

                    1140 ;313:         break;


                    1141 ;314:     default:


                    1142 ;315:         ERROR_REPORT("Unsupported data type");


                    1143 ;316:         return false;


                    1144 

                    1145 ;317:     }


                    1146 ;318:     if(dataLen <=0)


                    1147 

0000049c ca000011   1148 	bgt	.L967


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
000004a0 ea000015   1149 	b	.L971

                    1150 .L962:

                    1151 ;303:         break;


                    1152 ;304:     case INNER_TYPE_INT32_SETTS:


                    1153 ;305:     case INNER_TYPE_ENUMERATED_SETTS:


                    1154 ;306:         dataLen = encodeReadInt32Sett(writeBuf, 0, pDescrStruct, false);


                    1155 

000004a4 e3a03000   1156 	mov	r3,0

000004a8 e1a01003   1157 	mov	r1,r3

000004ac eb000000*  1158 	bl	encodeReadInt32Sett

000004b0 e2501000   1159 	subs	r1,r0,0

                    1160 ;313:         break;


                    1161 ;314:     default:


                    1162 ;315:         ERROR_REPORT("Unsupported data type");


                    1163 ;316:         return false;


                    1164 

                    1165 ;317:     }


                    1166 ;318:     if(dataLen <=0)


                    1167 

000004b4 ca00000b   1168 	bgt	.L967

000004b8 ea00000f   1169 	b	.L971

                    1170 .L963:

                    1171 ;307:         break;


                    1172 ;308:     case INNER_TYPE_INT32U_SETTS:


                    1173 ;309:         dataLen = encodeReadInt32USett(writeBuf, 0, pDescrStruct, false);


                    1174 

000004bc e3a03000   1175 	mov	r3,0

000004c0 e1a01003   1176 	mov	r1,r3

000004c4 eb000000*  1177 	bl	encodeReadInt32USett

000004c8 e2501000   1178 	subs	r1,r0,0

                    1179 ;313:         break;


                    1180 ;314:     default:


                    1181 ;315:         ERROR_REPORT("Unsupported data type");


                    1182 ;316:         return false;


                    1183 

                    1184 ;317:     }


                    1185 ;318:     if(dataLen <=0)


                    1186 

000004cc ca000005   1187 	bgt	.L967

000004d0 ea000009   1188 	b	.L971

                    1189 .L964:

                    1190 ;310:         break;


                    1191 ;311:     case INNER_TYPE_GOCB:


                    1192 ;312:         dataLen = encodeReadAttrGoCB(writeBuf, 0, pDescrStruct, false);


                    1193 

000004d4 e3a03000   1194 	mov	r3,0

000004d8 e1a01003   1195 	mov	r1,r3

000004dc eb000000*  1196 	bl	encodeReadAttrGoCB

000004e0 e2501000   1197 	subs	r1,r0,0

                    1198 ;313:         break;


                    1199 ;314:     default:


                    1200 ;315:         ERROR_REPORT("Unsupported data type");


                    1201 ;316:         return false;


                    1202 

                    1203 ;317:     }


                    1204 ;318:     if(dataLen <=0)


                    1205 

000004e4 da000004   1206 	ble	.L971

                    1207 .L967:

                    1208 ;319:     {


                    1209 


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
                    1210 ;320:         ERROR_REPORT("Invalid read length");


                    1211 ;321:         return false;


                    1212 

                    1213 ;322:     }


                    1214 ;323: 


                    1215 ;324:     if(!BufferView_advance(outBufView, dataLen))


                    1216 

000004e8 e1a00004   1217 	mov	r0,r4

000004ec eb000000*  1218 	bl	BufferView_advance

000004f0 e3500000   1219 	cmp	r0,0

                    1220 ;328:     }


                    1221 ;329:     return true;


                    1222 

000004f4 13a00001   1223 	movne	r0,1

000004f8 1a000000   1224 	bne	.L950

                    1225 .L971:

                    1226 ;325:     {


                    1227 

                    1228 ;326:         ERROR_REPORT("Buffer overflow");


                    1229 ;327:         return false;


                    1230 

000004fc e3a00000   1231 	mov	r0,0

                    1232 .L950:

00000500 e8bd8010   1233 	ldmfd	[sp]!,{r4,pc}

                    1234 	.endf	IEDTermItemDA_encodeRead

                    1235 	.align	4

                    1236 ;pDescrStruct	r2	local

                    1237 ;writeBuf	r0	local

                    1238 ;dataLen	r1	local

                    1239 ;termItem	r2	local

                    1240 

                    1241 ;entity	r3	param

                    1242 ;outBufView	r4	param

                    1243 

                    1244 	.section ".bss","awb"

                    1245 .L1090:

                    1246 	.data

                    1247 	.text

                    1248 

                    1249 ;330: }


                    1250 

                    1251 ;331: 


                    1252 ;332: MmsDataAccessError IEDTermItemDA_write(IEDEntity entity,


                    1253 	.align	4

                    1254 	.align	4

                    1255 IEDTermItemDA_write::

00000504 e92d44f0   1256 	stmfd	[sp]!,{r4-r7,r10,lr}

00000508 e24dd004   1257 	sub	sp,sp,4

0000050c e1a07000   1258 	mov	r7,r0

                    1259 ;333:                                            IsoConnection* isoConn, BufferView* value)


                    1260 ;334: {


                    1261 

                    1262 ;335:     TerminalItem* termItem = entity->extInfo;


                    1263 

00000510 e5970058   1264 	ldr	r0,[r7,88]

                    1265 ;336:     void *pDescrStruct = termItem->accessInfo;


                    1266 

00000514 e5904000   1267 	ldr	r4,[r0]

                    1268 ;337:     size_t len;


                    1269 ;338:     uint8_t *pValue;


                    1270 ;339: 



                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
                    1271 ;340:     if(entity->type != IED_ENTITY_DA_TERMINAL_ITEM)


                    1272 

00000518 e5970050   1273 	ldr	r0,[r7,80]

0000051c e3500008   1274 	cmp	r0,8

                    1275 ;341:     {


                    1276 

                    1277 ;342:         return DATA_ACCESS_ERROR_TYPE_INCONSISTENT;


                    1278 

00000520 13a00007   1279 	movne	r0,7

00000524 1a000049   1280 	bne	.L1135

00000528 e1a0a001   1281 	mov	r10,r1

0000052c e1a06002   1282 	mov	r6,r2

                    1283 ;343:     }


                    1284 ;344:     if(BufferView_endOfBuf(value))


                    1285 

00000530 e9960003   1286 	ldmed	[r6],{r0-r1}

00000534 e1500001   1287 	cmp	r0,r1

00000538 0a00000c   1288 	beq	.L1148

                    1289 ;345:     {


                    1290 

                    1291 ;346:         ERROR_REPORT("Empty write value");


                    1292 ;347:         return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                    1293 

                    1294 ;348:     }


                    1295 ;349:     pValue = value->p + value->pos;


                    1296 

0000053c e5961000   1297 	ldr	r1,[r6]

00000540 e1a0200d   1298 	mov	r2,sp

00000544 e0805001   1299 	add	r5,r0,r1

                    1300 ;350:     if(!BufferView_decodeTL(value, NULL, &len, NULL))


                    1301 

00000548 e1a00006   1302 	mov	r0,r6

0000054c e3a03000   1303 	mov	r3,0

00000550 e1a01003   1304 	mov	r1,r3

00000554 eb000000*  1305 	bl	BufferView_decodeTL

00000558 e3500000   1306 	cmp	r0,0

0000055c 0a000003   1307 	beq	.L1148

                    1308 ;351:     {


                    1309 

                    1310 ;352:         ERROR_REPORT("Invalid write value");


                    1311 ;353:         return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                    1312 

                    1313 ;354:     }


                    1314 ;355:     if(!BufferView_advance(value, len))


                    1315 

00000560 e59d1000   1316 	ldr	r1,[sp]

00000564 e1a00006   1317 	mov	r0,r6

00000568 eb000000*  1318 	bl	BufferView_advance

0000056c e3500000   1319 	cmp	r0,0

                    1320 .L1148:

                    1321 ;356:     {


                    1322 

                    1323 ;357:         ERROR_REPORT("Invalid write value");


                    1324 ;358:         return DATA_ACCESS_ERROR_OBJECT_VALUE_INVALID;


                    1325 

00000570 03a0000b   1326 	moveq	r0,11

00000574 0a000035   1327 	beq	.L1135

                    1328 .L1147:

                    1329 ;359:     }


                    1330 ;360:     switch (entity->subType)


                    1331 


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
00000578 e5970054   1332 	ldr	r0,[r7,84]

0000057c e2500022   1333 	subs	r0,r0,34

00000580 2a000007   1334 	bhs	.L1285

00000584 e2900002   1335 	adds	r0,r0,2

00000588 8a000023   1336 	bhi	.L1156

0000058c 0a00000d   1337 	beq	.L1152

00000590 e2900017   1338 	adds	r0,r0,23

00000594 0a00001b   1339 	beq	.L1155

00000598 e2900003   1340 	adds	r0,r0,3

0000059c 0a000014   1341 	beq	.L1154

000005a0 ea000029   1342 	b	.L1150

                    1343 .L1285:

                    1344 

000005a4 e2500001   1345 	subs	r0,r0,1

000005a8 9a000023   1346 	bls	.L1160

000005ac e2500002   1347 	subs	r0,r0,2

000005b0 0a000021   1348 	beq	.L1160

000005b4 e2500001   1349 	subs	r0,r0,1

000005b8 0a000008   1350 	beq	.L1153

000005bc e3500002   1351 	cmp	r0,2

000005c0 0a000019   1352 	beq	.L1158

000005c4 ea000020   1353 	b	.L1150

                    1354 .L1152:

                    1355 ;361:     {


                    1356 ;362:     case INNER_TYPE_RCB:


                    1357 ;363:         writeAttrRCB(isoConn, pDescrStruct, pValue);


                    1358 

000005c8 e1a02005   1359 	mov	r2,r5

000005cc e1a01004   1360 	mov	r1,r4

000005d0 e1a0000a   1361 	mov	r0,r10

000005d4 eb000000*  1362 	bl	writeAttrRCB

                    1363 ;382:     default:


                    1364 ;383:         ERROR_REPORT("Write to unsupported type %d ignored", entity->subType);


                    1365 ;384:         //Для неподдерживаемых типов имитируем успешную запись


                    1366 ;385:     }


                    1367 ;386:     return DATA_ACCESS_ERROR_SUCCESS;


                    1368 

000005d8 e3e00000   1369 	mvn	r0,0

000005dc ea00001b   1370 	b	.L1135

                    1371 .L1153:

                    1372 ;364:         break;


                    1373 ;365:     case INNER_TYPE_GOCB:


                    1374 ;366:         writeAttrGoCB(pDescrStruct, pValue);


                    1375 

000005e0 e1a01005   1376 	mov	r1,r5

000005e4 e1a00004   1377 	mov	r0,r4

000005e8 eb000000*  1378 	bl	writeAttrGoCB

                    1379 ;382:     default:


                    1380 ;383:         ERROR_REPORT("Write to unsupported type %d ignored", entity->subType);


                    1381 ;384:         //Для неподдерживаемых типов имитируем успешную запись


                    1382 ;385:     }


                    1383 ;386:     return DATA_ACCESS_ERROR_SUCCESS;


                    1384 

000005ec e3e00000   1385 	mvn	r0,0

000005f0 ea000016   1386 	b	.L1135

                    1387 .L1154:

                    1388 ;367:         break;


                    1389 ;368:     case INNER_TYPE_BOOLEAN:


                    1390 ;369:         writeBoolean(pDescrStruct, pValue);


                    1391 

000005f4 e1a01005   1392 	mov	r1,r5


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
000005f8 e1a00004   1393 	mov	r0,r4

000005fc eb000000*  1394 	bl	writeBoolean

                    1395 ;382:     default:


                    1396 ;383:         ERROR_REPORT("Write to unsupported type %d ignored", entity->subType);


                    1397 ;384:         //Для неподдерживаемых типов имитируем успешную запись


                    1398 ;385:     }


                    1399 ;386:     return DATA_ACCESS_ERROR_SUCCESS;


                    1400 

00000600 e3e00000   1401 	mvn	r0,0

00000604 ea000011   1402 	b	.L1135

                    1403 .L1155:

                    1404 ;370:         break;


                    1405 ;371:     case INNER_TYPE_CODEDENUM:


                    1406 ;372:         writeCodedEnum(pDescrStruct, pValue);


                    1407 

00000608 e1a01005   1408 	mov	r1,r5

0000060c e1a00004   1409 	mov	r0,r4

00000610 eb000000*  1410 	bl	writeCodedEnum

                    1411 ;382:     default:


                    1412 ;383:         ERROR_REPORT("Write to unsupported type %d ignored", entity->subType);


                    1413 ;384:         //Для неподдерживаемых типов имитируем успешную запись


                    1414 ;385:     }


                    1415 ;386:     return DATA_ACCESS_ERROR_SUCCESS;


                    1416 

00000614 e3e00000   1417 	mvn	r0,0

00000618 ea00000c   1418 	b	.L1135

                    1419 .L1156:

                    1420 ;373:         break;


                    1421 ;374:     case INNER_TYPE_FLOAT_SETT:


                    1422 ;375:         return writeFloatSett(pDescrStruct, pValue);


                    1423 

0000061c e1a01005   1424 	mov	r1,r5

00000620 e1a00004   1425 	mov	r0,r4

00000624 eb000000*  1426 	bl	writeFloatSett

00000628 ea000008   1427 	b	.L1135

                    1428 .L1158:

                    1429 ;376:     case INNER_TYPE_REAL_SETT:


                    1430 ;377:         return writeRealSett(pDescrStruct, pValue);        


                    1431 

0000062c e1a01005   1432 	mov	r1,r5

00000630 e1a00004   1433 	mov	r0,r4

00000634 eb000000*  1434 	bl	writeRealSett

00000638 ea000004   1435 	b	.L1135

                    1436 .L1160:

                    1437 ;378:     case INNER_TYPE_INT32_SETTS:


                    1438 ;379:     case INNER_TYPE_INT32U_SETTS:


                    1439 ;380:     case INNER_TYPE_ENUMERATED_SETTS:


                    1440 ;381:         return writeIntSett(pDescrStruct, pValue);        


                    1441 

0000063c e1a01005   1442 	mov	r1,r5

00000640 e1a00004   1443 	mov	r0,r4

00000644 eb000000*  1444 	bl	writeIntSett

00000648 ea000000   1445 	b	.L1135

                    1446 .L1150:

                    1447 ;382:     default:


                    1448 ;383:         ERROR_REPORT("Write to unsupported type %d ignored", entity->subType);


                    1449 ;384:         //Для неподдерживаемых типов имитируем успешную запись


                    1450 ;385:     }


                    1451 ;386:     return DATA_ACCESS_ERROR_SUCCESS;


                    1452 

0000064c e3e00000   1453 	mvn	r0,0


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bfs1.s
                    1454 .L1135:

00000650 e28dd004   1455 	add	sp,sp,4

00000654 e8bd84f0   1456 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    1457 	.endf	IEDTermItemDA_write

                    1458 	.align	4

                    1459 ;termItem	r0	local

                    1460 ;pDescrStruct	r4	local

                    1461 ;len	[sp]	local

                    1462 ;pValue	r5	local

                    1463 

                    1464 ;entity	r7	param

                    1465 ;isoConn	r10	param

                    1466 ;value	r6	param

                    1467 

                    1468 	.section ".bss","awb"

                    1469 .L1284:

                    1470 	.data

                    1471 	.text

                    1472 

                    1473 ;387: }


                    1474 	.align	4

                    1475 .L117:

00000658 00000000*  1476 	.data.w	settCounter

                    1477 	.type	.L117,$object

                    1478 	.size	.L117,4

                    1479 

                    1480 	.align	4

                    1481 

                    1482 	.data

                    1483 .L1348:

                    1484 	.globl	settCounter

00000000 00000000   1485 settCounter:	.data.b	0,0,0,0

                    1486 	.type	settCounter,$object

                    1487 	.size	settCounter,4

                    1488 	.ghsnote version,6

                    1489 	.ghsnote tools,3

                    1490 	.ghsnote options,0

                    1491 	.text

                    1492 	.align	4

                    1493 	.data

                    1494 	.align	4

                    1495 	.text

