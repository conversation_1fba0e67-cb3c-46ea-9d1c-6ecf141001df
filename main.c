#include "main.h"

#include "MemoryManager.h"
#include "mms.h"
#include "iedmodel.h"
#include "pwin_access.h"
#include "DataSlice.h"
#include "out_buffers.h"
#include "reports.h"
#include "goose.h"
#include "file_system.h"
#include "server.h"
#include "timers.h"
#include "iedTree/iedTree.h"
#include "BusError.h"

#include <debug.h>

#include <stddef.h>
#include <Clib.h>
#include "netTools.h"


void serverMain(void)
{
    int iedModelSize = 0;
    uint8_t* pIedModel;

    debugStart();

    TRACE("Init memory manager");

    if (!MM_init())
    {
        return;
    }

    TRACE("Init PWin");

    if(!initPWin())
    {
        TRACE("Error");
        return;
    }

    TRACE("dataSliceInit");
    // dataSliceInit придерживает инициализацию до прихода первого DataSlice
    // поэтому надо рассмотреть возможность переноса этого вызова пониже
    // чтобы ускорить инициализацию в целом.
    if(!dataSliceInit())
    {
        TRACE("Error");
        return;
    }

    TRACE("fs_init");
    if(!fs_init())
    {
        TRACE("Error");
        return;
    }

    TRACE("NetTools_init");
    if( !NetTools_init())
    {
        TRACE("Error");
        return;
    }

    TRACE("BusError_init");
    BusError_init();


    TRACE("Init sockets");

    if(!socketInit())
    {
        TRACE("Error");
        return;
    }

    TRACE("Load IED model");

    // TODO Убедиться что правильно работает при отсутствии ROM-модуля
    pIedModel = loadIedModel(&iedModelSize);

    //Если ROM-модуль не найден, останется информационная модель по умолчанию.
    if(pIedModel != NULL)
    {
        TRACE("Set IED model");
        setIedModel(pIedModel, iedModelSize);
    }

    // При инициализации в IEDTree элементов t (TimeStamp)
    // используется время из DataSlice, поэтому захватываем здесь
    dataSliceCapture();

    if(!IEDTree_init(iedModel, iedModelSize))
    {
        dataSliceRelease();
        ERROR_REPORT("IEDTree_init error");
        return;
    }

    //Инициализируем начальные значения из DataSlice.
    //При этом установятся флаги изменения DA, но они здесь не анализируются,
    //и ни на что не влияют
    IEDTree_updateFromDataSlice();

    dataSliceRelease();

    initReports();
    GOOSE_init();
    Timers_init();

    TRACE("Allocated MM memory %d KB", MM_getAllocated() / 1024);

    TRACE("Start listening");
    if(!startListening())
    {
        return;
    }

    handleIncomingConnections(handleMMSConnection);
}
