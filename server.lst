                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7q41.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=server.c -o gh_7q41.o -list=server.lst C:\Users\<USER>\AppData\Local\Temp\gh_7q41.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_7q41.s
Source File: server.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile server.c -o

                      11 ;		server.o

                      12 ;Source File:   server.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:09 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "server.h"


                      23 ;2: #include "MmsConst.h"


                      24 ;3: #include "platform_socket.h"


                      25 ;4: #include <platform_socket_def.h>


                      26 ;5: #include <platform_critical_section.h>


                      27 ;6: #include <debug.h>


                      28 ;7: 


                      29 ;8: extern SOCKET listenSocket;


                      30 ;9: 


                      31 ;10: static int connCounter = 0;


                      32 ;11: static CriticalSection connCounterCS;


                      33 ;12: 


                      34 ;13: 


                      35 ;14: void handleIncomingConnections(TCPConnectionHandler handleConnection)


                      36 	.text

                      37 	.align	4

                      38 handleIncomingConnections::

00000000 e92d4030     39 	stmfd	[sp]!,{r4-r5,lr}

                      40 ;15: {


                      41 

00000004 e24dd018     42 	sub	sp,sp,24

00000008 e1a04000     43 	mov	r4,r0

0000000c e59f00b8*    44 	ldr	r0,.L66

00000010 e28dc008     45 	add	r12,sp,8

00000014 e890000f     46 	ldmfd	[r0],{r0-r3}

00000018 e88c000f     47 	stmea	[r12],{r0-r3}

0000001c e3a00010     48 	mov	r0,16

00000020 e59f50a8*    49 	ldr	r5,.L67

00000024 e58d0000     50 	str	r0,[sp]


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7q41.s
                      51 ;16: 	SOCKADDR_IN_T fromAddr = { 0 };


                      52 

                      53 ;17: 	int fromAddrLen = sizeof(fromAddr);


                      54 

                      55 ;18: 	SOCKET connectionSocket;


                      56 ;19: 


                      57 ;20: 


                      58 ;21: 	CriticalSection_Init(&connCounterCS);


                      59 

00000028 e1a00005     60 	mov	r0,r5

0000002c eb000000*    61 	bl	CriticalSection_Init

                      62 ;22: 


                      63 ;23: 	while (1)


                      64 

                      65 .L6:

                      66 ;24: 	{                		


                      67 

                      68 ;25:         if (!acceptConnection(&connectionSocket, (struct sockaddr *)&fromAddr, &fromAddrLen))


                      69 

00000030 e1a0200d     70 	mov	r2,sp

00000034 e28d1008     71 	add	r1,sp,8

00000038 e28d0004     72 	add	r0,sp,4

0000003c eb000000*    73 	bl	acceptConnection

00000040 e3500000     74 	cmp	r0,0

00000044 0afffff9     75 	beq	.L6

                      76 ;26: 		{			


                      77 

                      78 ;27: 			ERROR_REPORT("'accept' function has returned an error");


                      79 ;28: 			continue;


                      80 

                      81 ;29: 		}


                      82 ;30: 		CriticalSection_Lock(&connCounterCS);


                      83 

00000048 e1a00005     84 	mov	r0,r5

0000004c eb000000*    85 	bl	CriticalSection_Lock

                      86 ;31: 		if (connCounter == MAX_CONN_COUNT)


                      87 

00000050 e59f107c*    88 	ldr	r1,.L68

00000054 e5910000     89 	ldr	r0,[r1]

00000058 e3500004     90 	cmp	r0,4

0000005c 1a000004     91 	bne	.L11

                      92 ;32: 		{


                      93 

                      94 ;33: 			closesocket(connectionSocket);


                      95 

00000060 e59d0004     96 	ldr	r0,[sp,4]

00000064 eb000000*    97 	bl	lwip_close

                      98 ;34: 			CriticalSection_Unlock(&connCounterCS);


                      99 

00000068 e1a00005    100 	mov	r0,r5

0000006c eb000000*   101 	bl	CriticalSection_Unlock

                     102 ;35: 			continue;


                     103 

00000070 eaffffee    104 	b	.L6

                     105 .L11:

                     106 ;36: 		}		


                     107 ;37: 		connCounter++;


                     108 

00000074 e2800001    109 	add	r0,r0,1

00000078 e5810000    110 	str	r0,[r1]

                     111 ;38: 		CriticalSection_Unlock(&connCounterCS);



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7q41.s
                     112 

0000007c e1a00005    113 	mov	r0,r5

00000080 eb000000*   114 	bl	CriticalSection_Unlock

                     115 ;39: 


                     116 ;40: 		handleConnection((SERVER_SOCKET)connectionSocket);


                     117 

00000084 e59d0004    118 	ldr	r0,[sp,4]

00000088 e1a0e00f    119 	mov	lr,pc

0000008c e12fff14*   120 	bx	r4

00000090 eaffffe6    121 	b	.L6

                     122 	.endf	handleIncomingConnections

                     123 	.align	4

                     124 ;fromAddr	[sp,8]	local

                     125 ;.L51	.L55	static

                     126 ;fromAddrLen	[sp]	local

                     127 ;connectionSocket	[sp,4]	local

                     128 

                     129 ;handleConnection	r4	param

                     130 

                     131 	.section ".bss","awb"

                     132 .L50:

                     133 	.data

                     134 .L52:

00000000 00000000    135 connCounter:	.data.b	0,0,0,0

                     136 	.type	connCounter,$object

                     137 	.size	connCounter,4

                     138 	.section ".bss","awb"

00000000 00         139 connCounterCS:	.space	1

                     140 	.section ".rodata","a"

00000000 00         141 .L55:	.space	1

00000001 00         142 	.space	1

00000002 0000       143 	.space	2

00000004 00000000    144 	.space	4

00000008 00000000    145 	.space	8

0000000c 00000000 
                     146 	.type	.L55,$object

                     147 	.size	.L55,16

                     148 	.data

                     149 	.text

                     150 

                     151 ;41: 	}


                     152 ;42: }


                     153 

                     154 ;43: 


                     155 ;44: void closeServerSocket(SERVER_SOCKET socket)


                     156 	.align	4

                     157 	.align	4

                     158 closeServerSocket::

00000094 e92d4030    159 	stmfd	[sp]!,{r4-r5,lr}

                     160 ;45: {


                     161 

                     162 ;46: 	CriticalSection_Lock(&connCounterCS);


                     163 

00000098 e59f4030*   164 	ldr	r4,.L67

0000009c e1a05000    165 	mov	r5,r0

000000a0 e1a00004    166 	mov	r0,r4

000000a4 eb000000*   167 	bl	CriticalSection_Lock

                     168 ;47: 	connCounter--;


                     169 

000000a8 e59f2024*   170 	ldr	r2,.L68

000000ac e5921000    171 	ldr	r1,[r2]


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_7q41.s
000000b0 e1a00004    172 	mov	r0,r4

000000b4 e2411001    173 	sub	r1,r1,1

000000b8 e5821000    174 	str	r1,[r2]

                     175 ;48: 	VERIFY(connCounter >= 0);


                     176 ;49: 	CriticalSection_Unlock(&connCounterCS);


                     177 

000000bc eb000000*   178 	bl	CriticalSection_Unlock

                     179 ;50: 	closesocket((SOCKET)socket);	


                     180 

000000c0 e1a00005    181 	mov	r0,r5

000000c4 e8bd4030    182 	ldmfd	[sp]!,{r4-r5,lr}

000000c8 ea000000*   183 	b	lwip_close

                     184 	.endf	closeServerSocket

                     185 	.align	4

                     186 

                     187 ;socket	r5	param

                     188 

                     189 	.data

                     190 	.text

                     191 

                     192 ;51: }


                     193 	.align	4

                     194 .L66:

000000cc 00000000*   195 	.data.w	.L55

                     196 	.type	.L66,$object

                     197 	.size	.L66,4

                     198 

                     199 .L67:

000000d0 00000000*   200 	.data.w	connCounterCS

                     201 	.type	.L67,$object

                     202 	.size	.L67,4

                     203 

                     204 .L68:

000000d4 00000000*   205 	.data.w	.L52

                     206 	.type	.L68,$object

                     207 	.size	.L68,4

                     208 

                     209 	.align	4

                     210 ;connCounter	.L52	static

                     211 ;connCounterCS	connCounterCS	static

                     212 

                     213 	.data

                     214 	.ghsnote version,6

                     215 	.ghsnote tools,3

                     216 	.ghsnote options,0

                     217 	.text

                     218 	.align	4

                     219 	.data

                     220 	.align	4

                     221 	.section ".rodata","a"

                     222 	.align	4

                     223 	.text

