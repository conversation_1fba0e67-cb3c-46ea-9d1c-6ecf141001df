                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=tlsf.c -o tlsf\gh_a481.o -list=tlsf/tlsf.lst C:\Users\<USER>\AppData\Local\Temp\gh_a481.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
Source File: tlsf.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile tlsf/tlsf.c -o

                      11 ;		tlsf/tlsf.o

                      12 ;Source File:   tlsf/tlsf.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:59 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include <assert.h>


                      23 ;2: #include <limits.h>


                      24 ;3: #include <stddef.h>


                      25 ;4: #include <stdio.h>


                      26 ;5: #include <stdlib.h>


                      27 ;6: #include <string.h>


                      28 ;7: 


                      29 ;8: #include "tlsf.h"


                      30 ;9: 


                      31 ;10: #if defined(__cplusplus)


                      32 ;11: #define tlsf_decl inline


                      33 ;12: #else


                      34 ;13: #define tlsf_decl static


                      35 ;14: #endif


                      36 ;15: 


                      37 ;16: /*


                      38 ;17: ** Architecture-specific bit manipulation routines.


                      39 ;18: **


                      40 ;19: ** TLSF achieves O(1) cost for malloc and free operations by limiting


                      41 ;20: ** the search for a free block to a free list of guaranteed size


                      42 ;21: ** adequate to fulfill the request, combined with efficient free list


                      43 ;22: ** queries using bitmasks and architecture-specific bit-manipulation


                      44 ;23: ** routines.


                      45 ;24: **


                      46 ;25: ** Most modern processors provide instructions to count leading zeroes


                      47 ;26: ** in a word, find the lowest and highest set bit, etc. These


                      48 ;27: ** specific implementations will be used when available, falling back


                      49 ;28: ** to a reasonably efficient generic implementation.


                      50 ;29: **



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                      51 ;30: ** NOTE: TLSF spec relies on ffs/fls returning value 0..31.


                      52 ;31: ** ffs/fls return 1-32 by default, returning 0 for error.


                      53 ;32: */


                      54 ;33: 


                      55 ;34: /*


                      56 ;35: ** Detect whether or not we are building for a 32- or 64-bit (LP/LLP)


                      57 ;36: ** architecture. There is no reliable portable method at compile-time.


                      58 ;37: */


                      59 ;38: #if defined (__alpha__) || defined (__ia64__) || defined (__x86_64__) \


                      60 ;39: 	|| defined (_WIN64) || defined (__LP64__) || defined (__LLP64__)


                      61 ;40: #define TLSF_64BIT


                      62 ;41: #endif


                      63 ;42: 


                      64 ;43: /*


                      65 ;44: ** gcc 3.4 and above have builtin support, specialized for architecture.


                      66 ;45: ** Some compilers masquerade as gcc; patchlevel test filters them out.


                      67 ;46: */


                      68 ;47: #if defined (__GNUC__) && (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4)) \


                      69 ;48: 	&& defined (__GNUC_PATCHLEVEL__)


                      70 ;49: 


                      71 ;50: #if defined (__SNC__)


                      72 ;51: /* SNC for Playstation 3. */


                      73 ;52: 


                      74 ;53: tlsf_decl int tlsf_ffs(unsigned int word)


                      75 ;54: {


                      76 ;55: 	const unsigned int reverse = word & (~word + 1);


                      77 ;56: 	const int bit = 32 - __builtin_clz(reverse);


                      78 ;57: 	return bit - 1;


                      79 ;58: }


                      80 ;59: 


                      81 ;60: #else


                      82 ;61: 


                      83 ;62: tlsf_decl int tlsf_ffs(unsigned int word)


                      84 ;63: {


                      85 ;64: 	return __builtin_ffs(word) - 1;


                      86 ;65: }


                      87 ;66: 


                      88 ;67: #endif


                      89 ;68: 


                      90 ;69: tlsf_decl int tlsf_fls(unsigned int word)


                      91 ;70: {


                      92 ;71: 	const int bit = word ? 32 - __builtin_clz(word) : 0;


                      93 ;72: 	return bit - 1;


                      94 ;73: }


                      95 ;74: 


                      96 ;75: #elif defined (_MSC_VER) && (_MSC_VER >= 1400) && (defined (_M_IX86) || defined (_M_X64))


                      97 ;76: /* Microsoft Visual C++ support on x86/X64 architectures. */


                      98 ;77: 


                      99 ;78: #include <intrin.h>


                     100 ;79: 


                     101 ;80: #pragma intrinsic(_BitScanReverse)


                     102 ;81: #pragma intrinsic(_BitScanForward)


                     103 ;82: 


                     104 ;83: tlsf_decl int tlsf_fls(unsigned int word)


                     105 ;84: {


                     106 ;85: 	unsigned long index;


                     107 ;86: 	return _BitScanReverse(&index, word) ? index : -1;


                     108 ;87: }


                     109 ;88: 


                     110 ;89: tlsf_decl int tlsf_ffs(unsigned int word)


                     111 ;90: {



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     112 ;91: 	unsigned long index;


                     113 ;92: 	return _BitScanForward(&index, word) ? index : -1;


                     114 ;93: }


                     115 ;94: 


                     116 ;95: #elif defined (_MSC_VER) && defined (_M_PPC)


                     117 ;96: /* Microsoft Visual C++ support on PowerPC architectures. */


                     118 ;97: 


                     119 ;98: #include <ppcintrinsics.h>


                     120 ;99: 


                     121 ;100: tlsf_decl int tlsf_fls(unsigned int word)


                     122 ;101: {


                     123 ;102: 	const int bit = 32 - _CountLeadingZeros(word);


                     124 ;103: 	return bit - 1;


                     125 ;104: }


                     126 ;105: 


                     127 ;106: tlsf_decl int tlsf_ffs(unsigned int word)


                     128 ;107: {


                     129 ;108: 	const unsigned int reverse = word & (~word + 1);


                     130 ;109: 	const int bit = 32 - _CountLeadingZeros(reverse);


                     131 ;110: 	return bit - 1;


                     132 ;111: }


                     133 ;112: 


                     134 ;113: #elif defined (__ARMCC_VERSION)


                     135 ;114: /* RealView Compilation Tools for ARM */


                     136 ;115: 


                     137 ;116: tlsf_decl int tlsf_ffs(unsigned int word)


                     138 ;117: {


                     139 ;118: 	const unsigned int reverse = word & (~word + 1);


                     140 ;119: 	const int bit = 32 - __clz(reverse);


                     141 ;120: 	return bit - 1;


                     142 ;121: }


                     143 ;122: 


                     144 ;123: tlsf_decl int tlsf_fls(unsigned int word)


                     145 ;124: {


                     146 ;125: 	const int bit = word ? 32 - __clz(word) : 0;


                     147 ;126: 	return bit - 1;


                     148 ;127: }


                     149 ;128: 


                     150 ;129: #elif defined (__ghs__)


                     151 ;130: /* Green Hills support for PowerPC */


                     152 ;131: 


                     153 ;132: #define tlsf_assert(p)


                     154 ;133: #define printf(p,...)


                     155 ;134: unsigned int __CLZ32(unsigned int Rm); // arm_ghs.h


                     156 ;135: //#include <ppc_ghs.h>


                     157 ;136: 


                     158 ;137: tlsf_decl int tlsf_ffs(unsigned int word)


                     159 ;138: {


                     160 ;139: 	const unsigned int reverse = word & (~word + 1);


                     161 ;140: 	const int bit = 32 - __CLZ32(reverse);


                     162 ;141: 	return bit - 1;


                     163 ;142: }


                     164 ;143: 


                     165 ;144: tlsf_decl int tlsf_fls(unsigned int word)


                     166 ;145: {


                     167 ;146: 	const int bit = word ? 32 - __CLZ32(word) : 0;


                     168 ;147: 	return bit - 1;


                     169 ;148: }


                     170 ;149: 


                     171 ;150: #else


                     172 ;151: /* Fall back to generic implementation. */



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     173 ;152: 


                     174 ;153: tlsf_decl int tlsf_fls_generic(unsigned int word)


                     175 ;154: {


                     176 ;155: 	int bit = 32;


                     177 ;156: 


                     178 ;157: 	if (!word) bit -= 1;


                     179 ;158: 	if (!(word & 0xffff0000)) { word <<= 16; bit -= 16; }


                     180 ;159: 	if (!(word & 0xff000000)) { word <<= 8; bit -= 8; }


                     181 ;160: 	if (!(word & 0xf0000000)) { word <<= 4; bit -= 4; }


                     182 ;161: 	if (!(word & 0xc0000000)) { word <<= 2; bit -= 2; }


                     183 ;162: 	if (!(word & 0x80000000)) { word <<= 1; bit -= 1; }


                     184 ;163: 


                     185 ;164: 	return bit;


                     186 ;165: }


                     187 ;166: 


                     188 ;167: /* Implement ffs in terms of fls. */


                     189 ;168: tlsf_decl int tlsf_ffs(unsigned int word)


                     190 ;169: {


                     191 ;170: 	return tlsf_fls_generic(word & (~word + 1)) - 1;


                     192 ;171: }


                     193 ;172: 


                     194 ;173: tlsf_decl int tlsf_fls(unsigned int word)


                     195 ;174: {


                     196 ;175: 	return tlsf_fls_generic(word) - 1;


                     197 ;176: }


                     198 ;177: 


                     199 ;178: #endif


                     200 ;179: 


                     201 ;180: /* Possibly 64-bit version of tlsf_fls. */


                     202 ;181: #if defined (TLSF_64BIT)


                     203 ;182: tlsf_decl int tlsf_fls_sizet(size_t size)


                     204 ;183: {


                     205 ;184: 	int high = (int)(size >> 32);


                     206 ;185: 	int bits = 0;


                     207 ;186: 	if (high)


                     208 ;187: 	{


                     209 ;188: 		bits = 32 + tlsf_fls(high);


                     210 ;189: 	}


                     211 ;190: 	else


                     212 ;191: 	{


                     213 ;192: 		bits = tlsf_fls((int)size & 0xffffffff);


                     214 ;193: 


                     215 ;194: 	}


                     216 ;195: 	return bits;


                     217 ;196: }


                     218 ;197: #else


                     219 ;198: #define tlsf_fls_sizet tlsf_fls


                     220 ;199: #endif


                     221 ;200: 


                     222 ;201: #undef tlsf_decl


                     223 ;202: 


                     224 ;203: /*


                     225 ;204: ** Constants.


                     226 ;205: */


                     227 ;206: 


                     228 ;207: /* Public constants: may be modified. */


                     229 ;208: enum tlsf_public


                     230 ;209: {


                     231 ;210: 	/* log2 of number of linear subdivisions of block sizes. Larger


                     232 ;211: 	** values require more memory in the control structure. Values of


                     233 ;212: 	** 4 or 5 are typical.



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     234 ;213: 	*/


                     235 ;214: 	SL_INDEX_COUNT_LOG2 = 5,


                     236 ;215: };


                     237 ;216: 


                     238 ;217: /* Private constants: do not modify. */


                     239 ;218: enum tlsf_private


                     240 ;219: {


                     241 ;220: #if defined (TLSF_64BIT)


                     242 ;221: 	/* All allocation sizes and addresses are aligned to 8 bytes. */


                     243 ;222: 	ALIGN_SIZE_LOG2 = 3,


                     244 ;223: #else


                     245 ;224: 	/* All allocation sizes and addresses are aligned to 4 bytes. */


                     246 ;225: 	ALIGN_SIZE_LOG2 = 2,


                     247 ;226: #endif


                     248 ;227: 	ALIGN_SIZE = (1 << ALIGN_SIZE_LOG2),


                     249 ;228: 


                     250 ;229: 	/*


                     251 ;230: 	** We support allocations of sizes up to (1 << FL_INDEX_MAX) bits.


                     252 ;231: 	** However, because we linearly subdivide the second-level lists, and


                     253 ;232: 	** our minimum size granularity is 4 bytes, it doesn't make sense to


                     254 ;233: 	** create first-level lists for sizes smaller than SL_INDEX_COUNT * 4,


                     255 ;234: 	** or (1 << (SL_INDEX_COUNT_LOG2 + 2)) bytes, as there we will be


                     256 ;235: 	** trying to split size ranges into more slots than we have available.


                     257 ;236: 	** Instead, we calculate the minimum threshold size, and place all


                     258 ;237: 	** blocks below that size into the 0th first-level list.


                     259 ;238: 	*/


                     260 ;239: 


                     261 ;240: #if defined (TLSF_64BIT)


                     262 ;241: 	/*


                     263 ;242: 	** TODO: We can increase this to support larger sizes, at the expense


                     264 ;243: 	** of more overhead in the TLSF structure.


                     265 ;244: 	*/


                     266 ;245: 	FL_INDEX_MAX = 32,


                     267 ;246: #else


                     268 ;247: 	FL_INDEX_MAX = 30,


                     269 ;248: #endif


                     270 ;249: 	SL_INDEX_COUNT = (1 << SL_INDEX_COUNT_LOG2),


                     271 ;250: 	FL_INDEX_SHIFT = (SL_INDEX_COUNT_LOG2 + ALIGN_SIZE_LOG2),


                     272 ;251: 	FL_INDEX_COUNT = (FL_INDEX_MAX - FL_INDEX_SHIFT + 1),


                     273 ;252: 


                     274 ;253: 	SMALL_BLOCK_SIZE = (1 << FL_INDEX_SHIFT),


                     275 ;254: };


                     276 ;255: 


                     277 ;256: /*


                     278 ;257: ** Cast and min/max macros.


                     279 ;258: */


                     280 ;259: 


                     281 ;260: #define tlsf_cast(t, exp)	((t) (exp))


                     282 ;261: #define tlsf_min(a, b)		((a) < (b) ? (a) : (b))


                     283 ;262: #define tlsf_max(a, b)		((a) > (b) ? (a) : (b))


                     284 ;263: 


                     285 ;264: /*


                     286 ;265: ** Set assert macro, if it has not been provided by the user.


                     287 ;266: */


                     288 ;267: #if !defined (tlsf_assert)


                     289 ;268: #define tlsf_assert assert


                     290 ;269: #endif


                     291 ;270: 


                     292 ;271: /*


                     293 ;272: ** Static assertion mechanism.


                     294 ;273: */



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     295 ;274: 


                     296 ;275: #define _tlsf_glue2(x, y) x ## y


                     297 ;276: #define _tlsf_glue(x, y) _tlsf_glue2(x, y)


                     298 ;277: #define tlsf_static_assert(exp) \


                     299 ;278: 	typedef char _tlsf_glue(static_assert, __LINE__) [(exp) ? 1 : -1]


                     300 ;279: 


                     301 ;280: /* This code has been tested on 32- and 64-bit (LP/LLP) architectures. */


                     302 ;281: tlsf_static_assert(sizeof(int) * CHAR_BIT == 32);


                     303 ;282: tlsf_static_assert(sizeof(size_t) * CHAR_BIT >= 32);


                     304 ;283: tlsf_static_assert(sizeof(size_t) * CHAR_BIT <= 64);


                     305 ;284: 


                     306 ;285: /* SL_INDEX_COUNT must be <= number of bits in sl_bitmap's storage type. */


                     307 ;286: tlsf_static_assert(sizeof(unsigned int) * CHAR_BIT >= SL_INDEX_COUNT);


                     308 ;287: 


                     309 ;288: /* Ensure we've properly tuned our sizes. */


                     310 ;289: tlsf_static_assert(ALIGN_SIZE == SMALL_BLOCK_SIZE / SL_INDEX_COUNT);


                     311 ;290: 


                     312 ;291: /*


                     313 ;292: ** Data structures and associated constants.


                     314 ;293: */


                     315 ;294: 


                     316 ;295: /*


                     317 ;296: ** Block header structure.


                     318 ;297: **


                     319 ;298: ** There are several implementation subtleties involved:


                     320 ;299: ** - The prev_phys_block field is only valid if the previous block is free.


                     321 ;300: ** - The prev_phys_block field is actually stored at the end of the


                     322 ;301: **   previous block. It appears at the beginning of this structure only to


                     323 ;302: **   simplify the implementation.


                     324 ;303: ** - The next_free / prev_free fields are only valid if the block is free.


                     325 ;304: */


                     326 ;305: typedef struct block_header_t


                     327 ;306: {


                     328 ;307: 	/* Points to the previous physical block. */


                     329 ;308: 	struct block_header_t* prev_phys_block;


                     330 ;309: 


                     331 ;310: 	/* The size of this block, excluding the block header. */


                     332 ;311: 	size_t size;


                     333 ;312: 


                     334 ;313: 	/* Next and previous free blocks. */


                     335 ;314: 	struct block_header_t* next_free;


                     336 ;315: 	struct block_header_t* prev_free;


                     337 ;316: } block_header_t;


                     338 ;317: 


                     339 ;318: /*


                     340 ;319: ** Since block sizes are always at least a multiple of 4, the two least


                     341 ;320: ** significant bits of the size field are used to store the block status:


                     342 ;321: ** - bit 0: whether block is busy or free


                     343 ;322: ** - bit 1: whether previous block is busy or free


                     344 ;323: */


                     345 ;324: static const size_t block_header_free_bit = 1 << 0;


                     346 ;325: static const size_t block_header_prev_free_bit = 1 << 1;


                     347 ;326: 


                     348 ;327: /*


                     349 ;328: ** The size of the block header exposed to used blocks is the size field.


                     350 ;329: ** The prev_phys_block field is stored *inside* the previous free block.


                     351 ;330: */


                     352 ;331: static const size_t block_header_overhead = sizeof(size_t);


                     353 ;332: 


                     354 ;333: /* User data starts directly after the size field in a used block. */


                     355 ;334: static const size_t block_start_offset =



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     356 ;335: 	offsetof(block_header_t, size) + sizeof(size_t);


                     357 ;336: 


                     358 ;337: /*


                     359 ;338: ** A free block must be large enough to store its header minus the size of


                     360 ;339: ** the prev_phys_block field, and no larger than the number of addressable


                     361 ;340: ** bits for FL_INDEX.


                     362 ;341: */


                     363 ;342: static const size_t block_size_min = 


                     364 ;343: 	sizeof(block_header_t) - sizeof(block_header_t*);


                     365 ;344: static const size_t block_size_max = tlsf_cast(size_t, 1) << FL_INDEX_MAX;


                     366 ;345: 


                     367 ;346: 


                     368 ;347: /* The TLSF control structure. */


                     369 ;348: typedef struct control_t


                     370 ;349: {


                     371 ;350: 	/* Empty lists point at this block to indicate they are free. */


                     372 ;351: 	block_header_t block_null;


                     373 ;352: 


                     374 ;353: 	/* Bitmaps for free lists. */


                     375 ;354: 	unsigned int fl_bitmap;


                     376 ;355: 	unsigned int sl_bitmap[FL_INDEX_COUNT];


                     377 ;356: 


                     378 ;357: 	/* Head of free lists. */


                     379 ;358: 	block_header_t* blocks[FL_INDEX_COUNT][SL_INDEX_COUNT];


                     380 ;359: } control_t;


                     381 ;360: 


                     382 ;361: /* A type used for casting when doing pointer arithmetic. */


                     383 ;362: typedef ptrdiff_t tlsfptr_t;


                     384 ;363: 


                     385 ;364: /*


                     386 ;365: ** block_header_t member functions.


                     387 ;366: */


                     388 ;367: 


                     389 ;368: static size_t block_size(const block_header_t* block)


                     390 ;369: {


                     391 ;370: 	return block->size & ~(block_header_free_bit | block_header_prev_free_bit);


                     392 ;371: }


                     393 ;372: 


                     394 ;373: static void block_set_size(block_header_t* block, size_t size)


                     395 ;374: {


                     396 ;375: 	const size_t oldsize = block->size;


                     397 ;376: 	block->size = size | (oldsize & (block_header_free_bit | block_header_prev_free_bit));


                     398 ;377: }


                     399 ;378: 


                     400 ;379: static int block_is_last(const block_header_t* block)


                     401 

                     402 ;382: }


                     403 

                     404 ;383: 


                     405 ;384: static int block_is_free(const block_header_t* block)


                     406 ;385: {


                     407 ;386: 	return tlsf_cast(int, block->size & block_header_free_bit);


                     408 ;387: }


                     409 ;388: 


                     410 ;389: static void block_set_free(block_header_t* block)


                     411 ;390: {


                     412 ;391: 	block->size |= block_header_free_bit;


                     413 ;392: }


                     414 ;393: 


                     415 ;394: static void block_set_used(block_header_t* block)


                     416 ;395: {



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     417 ;396: 	block->size &= ~block_header_free_bit;


                     418 ;397: }


                     419 ;398: 


                     420 ;399: static int block_is_prev_free(const block_header_t* block)


                     421 ;400: {


                     422 ;401: 	return tlsf_cast(int, block->size & block_header_prev_free_bit);


                     423 ;402: }


                     424 ;403: 


                     425 ;404: static void block_set_prev_free(block_header_t* block)


                     426 ;405: {


                     427 ;406: 	block->size |= block_header_prev_free_bit;


                     428 ;407: }


                     429 ;408: 


                     430 ;409: static void block_set_prev_used(block_header_t* block)


                     431 ;410: {


                     432 ;411: 	block->size &= ~block_header_prev_free_bit;


                     433 ;412: }


                     434 ;413: 


                     435 ;414: static block_header_t* block_from_ptr(const void* ptr)


                     436 ;415: {


                     437 ;416: 	return tlsf_cast(block_header_t*,


                     438 ;417: 		tlsf_cast(unsigned char*, ptr) - block_start_offset);


                     439 ;418: }


                     440 ;419: 


                     441 ;420: static void* block_to_ptr(const block_header_t* block)


                     442 ;421: {


                     443 ;422: 	return tlsf_cast(void*,


                     444 ;423: 		tlsf_cast(unsigned char*, block) + block_start_offset);


                     445 ;424: }


                     446 ;425: 


                     447 ;426: /* Return location of next block after block of given size. */


                     448 ;427: static block_header_t* offset_to_block(const void* ptr, size_t size)


                     449 ;428: {


                     450 ;429: 	return tlsf_cast(block_header_t*, tlsf_cast(tlsfptr_t, ptr) + size);


                     451 ;430: }


                     452 ;431: 


                     453 ;432: /* Return location of previous block. */


                     454 ;433: static block_header_t* block_prev(const block_header_t* block)


                     455 

                     456 ;437: }


                     457 

                     458 ;438: 


                     459 ;439: /* Return location of next existing block. */


                     460 ;440: static block_header_t* block_next(const block_header_t* block)


                     461 ;441: {


                     462 ;442: 	block_header_t* next = offset_to_block(block_to_ptr(block),


                     463 ;443: 		block_size(block) - block_header_overhead);


                     464 ;444: 	tlsf_assert(!block_is_last(block));


                     465 ;445: 	return next;


                     466 ;446: }


                     467 ;447: 


                     468 ;448: /* Link a new block with its physical neighbor, return the neighbor. */


                     469 ;449: static block_header_t* block_link_next(block_header_t* block)


                     470 ;450: {


                     471 ;451: 	block_header_t* next = block_next(block);


                     472 ;452: 	next->prev_phys_block = block;


                     473 ;453: 	return next;


                     474 ;454: }


                     475 ;455: 


                     476 ;456: static void block_mark_as_free(block_header_t* block)


                     477 ;457: {



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     478 ;458: 	/* Link the block to the next block, first. */


                     479 ;459: 	block_header_t* next = block_link_next(block);


                     480 ;460: 	block_set_prev_free(next);


                     481 ;461: 	block_set_free(block);


                     482 ;462: }


                     483 ;463: 


                     484 ;464: static void block_mark_as_used(block_header_t* block)


                     485 ;465: {


                     486 ;466: 	block_header_t* next = block_next(block);


                     487 ;467: 	block_set_prev_used(next);


                     488 ;468: 	block_set_used(block);


                     489 ;469: }


                     490 ;470: 


                     491 ;471: static size_t align_up(size_t x, size_t align)


                     492 

                     493 ;475: }


                     494 

                     495 ;476: 


                     496 ;477: static size_t align_down(size_t x, size_t align)


                     497 

                     498 ;481: }


                     499 

                     500 ;482: 


                     501 ;483: static void* align_ptr(const void* ptr, size_t align)


                     502 ;484: {


                     503 ;485: 	const tlsfptr_t aligned =


                     504 ;486: 		(tlsf_cast(tlsfptr_t, ptr) + (align - 1)) & ~(align - 1);


                     505 ;487: 	tlsf_assert(0 == (align & (align - 1)) && "must align to a power of two");


                     506 ;488: 	return tlsf_cast(void*, aligned);


                     507 ;489: }


                     508 ;490: 


                     509 ;491: /*


                     510 ;492: ** Adjust an allocation size to be aligned to word size, and no smaller


                     511 ;493: ** than internal minimum.


                     512 ;494: */


                     513 ;495: static size_t adjust_request_size(size_t size, size_t align)


                     514 ;496: {


                     515 ;497: 	size_t adjust = 0;


                     516 ;498: 	if (size)


                     517 ;499: 	{


                     518 ;500: 		const size_t aligned = align_up(size, align);


                     519 ;501: 


                     520 ;502: 		/* aligned sized must not exceed block_size_max or we'll go out of bounds on sl_bitmap */


                     521 ;503: 		if (aligned < block_size_max) 


                     522 ;504: 		{


                     523 ;505: 			adjust = tlsf_max(aligned, block_size_min);


                     524 ;506: 		}


                     525 ;507: 	}


                     526 ;508: 	return adjust;


                     527 ;509: }


                     528 ;510: 


                     529 ;511: /*


                     530 ;512: ** TLSF utility functions. In most cases, these are direct translations of


                     531 ;513: ** the documentation found in the white paper.


                     532 ;514: */


                     533 ;515: 


                     534 ;516: static void mapping_insert(size_t size, int* fli, int* sli)


                     535 ;517: {


                     536 ;518: 	int fl, sl;


                     537 ;519: 	if (size < SMALL_BLOCK_SIZE)


                     538 ;520: 	{



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     539 ;521: 		/* Store small blocks in first list. */


                     540 ;522: 		fl = 0;


                     541 ;523: 		sl = tlsf_cast(int, size) / (SMALL_BLOCK_SIZE / SL_INDEX_COUNT);


                     542 ;524: 	}


                     543 ;525: 	else


                     544 ;526: 	{


                     545 ;527: 		fl = tlsf_fls_sizet(size);


                     546 ;528: 		sl = tlsf_cast(int, size >> (fl - SL_INDEX_COUNT_LOG2)) ^ (1 << SL_INDEX_COUNT_LOG2);


                     547 ;529: 		fl -= (FL_INDEX_SHIFT - 1);


                     548 ;530: 	}


                     549 ;531: 	*fli = fl;


                     550 ;532: 	*sli = sl;


                     551 ;533: }


                     552 ;534: 


                     553 ;535: /* This version rounds up to the next block size (for allocations) */


                     554 ;536: static void mapping_search(size_t size, int* fli, int* sli)


                     555 

                     556 ;544: }


                     557 

                     558 ;545: 


                     559 ;546: static block_header_t* search_suitable_block(control_t* control, int* fli, int* sli)


                     560 

                     561 ;576: }


                     562 

                     563 ;577: 


                     564 ;578: /* Remove a free block from the free list.*/


                     565 ;579: static void remove_free_block(control_t* control, block_header_t* block, int fl, int sl)


                     566 ;580: {


                     567 ;581: 	block_header_t* prev = block->prev_free;


                     568 ;582: 	block_header_t* next = block->next_free;


                     569 ;583: 	tlsf_assert(prev && "prev_free field can not be null");


                     570 ;584: 	tlsf_assert(next && "next_free field can not be null");


                     571 ;585: 	next->prev_free = prev;


                     572 ;586: 	prev->next_free = next;


                     573 ;587: 


                     574 ;588: 	/* If this block is the head of the free list, set new head. */


                     575 ;589: 	if (control->blocks[fl][sl] == block)


                     576 ;590: 	{


                     577 ;591: 		control->blocks[fl][sl] = next;


                     578 ;592: 


                     579 ;593: 		/* If the new head is null, clear the bitmap. */


                     580 ;594: 		if (next == &control->block_null)


                     581 ;595: 		{


                     582 ;596: 			control->sl_bitmap[fl] &= ~(1 << sl);


                     583 ;597: 


                     584 ;598: 			/* If the second bitmap is now empty, clear the fl bitmap. */


                     585 ;599: 			if (!control->sl_bitmap[fl])


                     586 ;600: 			{


                     587 ;601: 				control->fl_bitmap &= ~(1 << fl);


                     588 ;602: 			}


                     589 ;603: 		}


                     590 ;604: 	}


                     591 ;605: }


                     592 ;606: 


                     593 ;607: /* Insert a free block into the free block list. */


                     594 ;608: static void insert_free_block(control_t* control, block_header_t* block, int fl, int sl)


                     595 

                     596 ;626: }


                     597 

                     598 ;627: 


                     599 ;628: /* Remove a given block from the free list. */



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     600 ;629: static void block_remove(control_t* control, block_header_t* block)


                     601 ;630: {


                     602 ;631: 	int fl, sl;


                     603 ;632: 	mapping_insert(block_size(block), &fl, &sl);


                     604 ;633: 	remove_free_block(control, block, fl, sl);


                     605 ;634: }


                     606 ;635: 


                     607 ;636: /* Insert a given block into the free list. */


                     608 ;637: static void block_insert(control_t* control, block_header_t* block)


                     609 ;638: {


                     610 ;639: 	int fl, sl;


                     611 ;640: 	mapping_insert(block_size(block), &fl, &sl);


                     612 ;641: 	insert_free_block(control, block, fl, sl);


                     613 ;642: }


                     614 ;643: 


                     615 ;644: static int block_can_split(block_header_t* block, size_t size)


                     616 ;645: {


                     617 ;646: 	return block_size(block) >= sizeof(block_header_t) + size;


                     618 ;647: }


                     619 ;648: 


                     620 ;649: /* Split a block into two, the second of which is free. */


                     621 ;650: static block_header_t* block_split(block_header_t* block, size_t size)


                     622 ;651: {


                     623 ;652: 	/* Calculate the amount of space left in the remaining block. */


                     624 ;653: 	block_header_t* remaining =


                     625 ;654: 		offset_to_block(block_to_ptr(block), size - block_header_overhead);


                     626 ;655: 


                     627 ;656: 	const size_t remain_size = block_size(block) - (size + block_header_overhead);


                     628 ;657: 


                     629 ;658: 	tlsf_assert(block_to_ptr(remaining) == align_ptr(block_to_ptr(remaining), ALIGN_SIZE)


                     630 ;659: 		&& "remaining block not aligned properly");


                     631 ;660: 


                     632 ;661: 	tlsf_assert(block_size(block) == remain_size + size + block_header_overhead);


                     633 ;662: 	block_set_size(remaining, remain_size);


                     634 ;663: 	tlsf_assert(block_size(remaining) >= block_size_min && "block split with invalid size");


                     635 ;664: 


                     636 ;665: 	block_set_size(block, size);


                     637 ;666: 	block_mark_as_free(remaining);


                     638 ;667: 


                     639 ;668: 	return remaining;


                     640 ;669: }


                     641 ;670: 


                     642 ;671: /* Absorb a free block's storage into an adjacent previous free block. */


                     643 ;672: static block_header_t* block_absorb(block_header_t* prev, block_header_t* block)


                     644 ;673: {


                     645 ;674: 	tlsf_assert(!block_is_last(prev) && "previous block can't be last");


                     646 ;675: 	/* Note: Leaves flags untouched. */


                     647 ;676: 	prev->size += block_size(block) + block_header_overhead;


                     648 ;677: 	block_link_next(prev);


                     649 ;678: 	return prev;


                     650 ;679: }


                     651 ;680: 


                     652 ;681: /* Merge a just-freed block with an adjacent previous free block. */


                     653 ;682: static block_header_t* block_merge_prev(control_t* control, block_header_t* block)


                     654 

                     655 ;694: }


                     656 

                     657 ;695: 


                     658 ;696: /* Merge a just-freed block with an adjacent free block. */


                     659 ;697: static block_header_t* block_merge_next(control_t* control, block_header_t* block)


                     660 ;698: {



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     661 ;699: 	block_header_t* next = block_next(block);


                     662 ;700: 	tlsf_assert(next && "next physical block can't be null");


                     663 ;701: 


                     664 ;702: 	if (block_is_free(next))


                     665 ;703: 	{


                     666 ;704: 		tlsf_assert(!block_is_last(block) && "previous block can't be last");


                     667 ;705: 		block_remove(control, next);


                     668 ;706: 		block = block_absorb(block, next);


                     669 ;707: 	}


                     670 ;708: 


                     671 ;709: 	return block;


                     672 ;710: }


                     673 ;711: 


                     674 ;712: /* Trim any trailing block space off the end of a block, return to pool. */


                     675 ;713: static void block_trim_free(control_t* control, block_header_t* block, size_t size)


                     676 

                     677 ;722: 	}


                     678 ;723: }


                     679 

                     680 ;724: 


                     681 ;725: /* Trim any trailing block space off the end of a used block, return to pool. */


                     682 ;726: static void block_trim_used(control_t* control, block_header_t* block, size_t size)


                     683 

                     684 ;737: 	}


                     685 ;738: }


                     686 

                     687 ;739: 


                     688 ;740: static block_header_t* block_trim_free_leading(control_t* control, block_header_t* block, size_t size)


                     689 

                     690 ;754: }


                     691 

                     692 ;755: 


                     693 ;756: static block_header_t* block_locate_free(control_t* control, size_t size)


                     694 ;757: {


                     695 ;758: 	int fl = 0, sl = 0;


                     696 ;759: 	block_header_t* block = 0;


                     697 ;760: 


                     698 ;761: 	if (size)


                     699 ;762: 	{


                     700 ;763: 		mapping_search(size, &fl, &sl);


                     701 ;764: 		


                     702 ;765: 		/*


                     703 ;766: 		** mapping_search can futz with the size, so for excessively large sizes it can sometimes wind up 


                     704 ;767: 		** with indices that are off the end of the block array.


                     705 ;768: 		** So, we protect against that here, since this is the only callsite of mapping_search.


                     706 ;769: 		** Note that we don't need to check sl, since it comes from a modulo operation that guarantees it's always in range.


                     707 ;770: 		*/


                     708 ;771: 		if (fl < FL_INDEX_COUNT)


                     709 ;772: 		{


                     710 ;773: 			block = search_suitable_block(control, &fl, &sl);


                     711 ;774: 		}


                     712 ;775: 	}


                     713 ;776: 


                     714 ;777: 	if (block)


                     715 ;778: 	{


                     716 ;779: 		tlsf_assert(block_size(block) >= size);


                     717 ;780: 		remove_free_block(control, block, fl, sl);


                     718 ;781: 	}


                     719 ;782: 


                     720 ;783: 	return block;


                     721 ;784: }



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     722 ;785: 


                     723 ;786: static void* block_prepare_used(control_t* control, block_header_t* block, size_t size)


                     724 ;787: {


                     725 ;788: 	void* p = 0;


                     726 ;789: 	if (block)


                     727 ;790: 	{


                     728 ;791: 		tlsf_assert(size && "size must be non-zero");


                     729 ;792: 		block_trim_free(control, block, size);


                     730 ;793: 		block_mark_as_used(block);


                     731 ;794: 		p = block_to_ptr(block);


                     732 ;795: 	}


                     733 ;796: 	return p;


                     734 ;797: }


                     735 ;798: 


                     736 ;799: /* Clear structure and point all empty lists at the null block. */


                     737 ;800: static void control_construct(control_t* control)


                     738 

                     739 ;814: 		}


                     740 ;815: 	}


                     741 ;816: }


                     742 

                     743 ;817: 


                     744 ;818: /*


                     745 ;819: ** Debugging utilities.


                     746 ;820: */


                     747 ;821: 


                     748 ;822: typedef struct integrity_t


                     749 ;823: {


                     750 ;824: 	int prev_status;


                     751 ;825: 	int status;


                     752 ;826: } integrity_t;


                     753 ;827: 


                     754 ;828: #define tlsf_insist(x) { tlsf_assert(x); if (!(x)) { status--; } }


                     755 ;829: 


                     756 ;830: static void integrity_walker(void* ptr, size_t size, int used, void* user)


                     757 ;831: {


                     758 ;832: 	block_header_t* block = block_from_ptr(ptr);


                     759 ;833: 	integrity_t* integ = tlsf_cast(integrity_t*, user);


                     760 ;834: 	const int this_prev_status = block_is_prev_free(block) ? 1 : 0;


                     761 ;835: 	const int this_status = block_is_free(block) ? 1 : 0;


                     762 ;836: 	const size_t this_block_size = block_size(block);


                     763 ;837: 


                     764 ;838: 	int status = 0;


                     765 ;839: 	(void)used;


                     766 ;840: 	tlsf_insist(integ->prev_status == this_prev_status && "prev status incorrect");


                     767 ;841: 	tlsf_insist(size == this_block_size && "block size incorrect");


                     768 ;842: 


                     769 ;843: 	integ->prev_status = this_status;


                     770 ;844: 	integ->status += status;


                     771 ;845: }


                     772 ;846: 


                     773 ;847: int tlsf_check(tlsf_t tlsf)


                     774 ;848: {


                     775 ;849: 	int i, j;


                     776 ;850: 


                     777 ;851: 	control_t* control = tlsf_cast(control_t*, tlsf);


                     778 ;852: 	int status = 0;


                     779 ;853: 


                     780 ;854: 	/* Check that the free lists and bitmaps are accurate. */


                     781 ;855: 	for (i = 0; i < FL_INDEX_COUNT; ++i)


                     782 ;856: 	{



                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     783 ;857: 		for (j = 0; j < SL_INDEX_COUNT; ++j)


                     784 ;858: 		{


                     785 ;859: 			const int fl_map = control->fl_bitmap & (1 << i);


                     786 ;860: 			const int sl_list = control->sl_bitmap[i];


                     787 ;861: 			const int sl_map = sl_list & (1 << j);


                     788 ;862: 			const block_header_t* block = control->blocks[i][j];


                     789 ;863: 


                     790 ;864: 			/* Check that first- and second-level lists agree. */


                     791 ;865: 			if (!fl_map)


                     792 ;866: 			{


                     793 ;867: 				tlsf_insist(!sl_map && "second-level map must be null");


                     794 ;868: 			}


                     795 ;869: 


                     796 ;870: 			if (!sl_map)


                     797 ;871: 			{


                     798 ;872: 				tlsf_insist(block == &control->block_null && "block list must be null");


                     799 ;873: 				continue;


                     800 ;874: 			}


                     801 ;875: 


                     802 ;876: 			/* Check that there is at least one free block. */


                     803 ;877: 			tlsf_insist(sl_list && "no free blocks in second-level map");


                     804 ;878: 			tlsf_insist(block != &control->block_null && "block should not be null");


                     805 ;879: 


                     806 ;880: 			while (block != &control->block_null)


                     807 ;881: 			{


                     808 ;882: 				int fli, sli;


                     809 ;883: 				tlsf_insist(block_is_free(block) && "block should be free");


                     810 ;884: 				tlsf_insist(!block_is_prev_free(block) && "blocks should have coalesced");


                     811 ;885: 				tlsf_insist(!block_is_free(block_next(block)) && "blocks should have coalesced");


                     812 ;886: 				tlsf_insist(block_is_prev_free(block_next(block)) && "block should be free");


                     813 ;887: 				tlsf_insist(block_size(block) >= block_size_min && "block not minimum size");


                     814 ;888: 


                     815 ;889: 				mapping_insert(block_size(block), &fli, &sli);


                     816 ;890: 				tlsf_insist(fli == i && sli == j && "block size indexed in wrong list");


                     817 ;891: 				block = block->next_free;


                     818 ;892: 			}


                     819 ;893: 		}


                     820 ;894: 	}


                     821 ;895: 


                     822 ;896: 	return status;


                     823 ;897: }


                     824 ;898: 


                     825 ;899: #undef tlsf_insist


                     826 ;900: 


                     827 ;901: static void default_walker(void* ptr, size_t size, int used, void* user)


                     828 ;902: {


                     829 ;903: 	(void)user;


                     830 ;904: 	printf("\t%p %s size: %x (%p)\n", ptr, used ? "used" : "free", (unsigned int)size, block_from_ptr(ptr));


                     831 ;905: }


                     832 ;906: 


                     833 ;907: void tlsf_walk_pool(pool_t pool, tlsf_walker walker, void* user)


                     834 ;908: {


                     835 ;909: 	tlsf_walker pool_walker = walker ? walker : default_walker;


                     836 ;910: 	block_header_t* block =


                     837 ;911: 		offset_to_block(pool, -(int)block_header_overhead);


                     838 ;912: 


                     839 ;913: 	while (block && !block_is_last(block))


                     840 ;914: 	{


                     841 ;915: 		pool_walker(


                     842 ;916: 			block_to_ptr(block),


                     843 ;917: 			block_size(block),



                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     844 ;918: 			!block_is_free(block),


                     845 ;919: 			user);


                     846 ;920: 		block = block_next(block);


                     847 ;921: 	}


                     848 ;922: }


                     849 ;923: 


                     850 ;924: size_t tlsf_block_size(void* ptr)


                     851 ;925: {


                     852 ;926: 	size_t size = 0;


                     853 ;927: 	if (ptr)


                     854 ;928: 	{


                     855 ;929: 		const block_header_t* block = block_from_ptr(ptr);


                     856 ;930: 		size = block_size(block);


                     857 ;931: 	}


                     858 ;932: 	return size;


                     859 ;933: }


                     860 ;934: 


                     861 ;935: int tlsf_check_pool(pool_t pool)


                     862 ;936: {


                     863 ;937: 	/* Check that the blocks are physically correct. */


                     864 ;938: 	integrity_t integ = { 0, 0 };


                     865 ;939: 	tlsf_walk_pool(pool, integrity_walker, &integ);


                     866 ;940: 


                     867 ;941: 	return integ.status;


                     868 ;942: }


                     869 ;943: 


                     870 ;944: /*


                     871 ;945: ** Size of the TLSF structures in a given memory block passed to


                     872 ;946: ** tlsf_create, equal to the size of a control_t


                     873 ;947: */


                     874 ;948: size_t tlsf_size(void)


                     875 

                     876 ;951: }


                     877 

                     878 ;952: 


                     879 ;953: size_t tlsf_align_size(void)


                     880 

                     881 ;956: }


                     882 

                     883 ;957: 


                     884 ;958: size_t tlsf_block_size_min(void)


                     885 

                     886 ;961: }


                     887 

                     888 ;962: 


                     889 ;963: size_t tlsf_block_size_max(void)


                     890 

                     891 ;966: }


                     892 

                     893 ;967: 


                     894 ;968: /*


                     895 ;969: ** Overhead of the TLSF structures in a given memory block passed to


                     896 ;970: ** tlsf_add_pool, equal to the overhead of a free block and the


                     897 ;971: ** sentinel block.


                     898 ;972: */


                     899 ;973: size_t tlsf_pool_overhead(void)


                     900 ;974: {


                     901 ;975: 	return 2 * block_header_overhead;


                     902 ;976: }


                     903 ;977: 


                     904 ;978: size_t tlsf_alloc_overhead(void)



                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     905 

                     906 ;981: }


                     907 

                     908 	.text

                     909 	.align	4

                     910 tlsf_ffs:

00000000 e92d4000    911 	stmfd	[sp]!,{lr}

00000004 e2601000    912 	rsb	r1,r0,0

00000008 e0010000    913 	and	r0,r1,r0

0000000c eb000000*   914 	bl	__CLZ32

00000010 e2600020    915 	rsb	r0,r0,32

00000014 e2400001    916 	sub	r0,r0,1

00000018 e8bd4000    917 	ldmfd	[sp]!,{lr}

0000001c e12fff1e*   918 	ret	

                     919 	.endf	tlsf_ffs

                     920 	.align	4

                     921 ;bit	r0	local

                     922 

                     923 ;word	r0	param

                     924 

                     925 	.section ".bss","awb"

                     926 .L302:

                     927 	.data

                     928 	.text

                     929 

                     930 

                     931 	.align	4

                     932 	.align	4

                     933 tlsf_fls:

00000020 e92d4000    934 	stmfd	[sp]!,{lr}

00000024 e3a01000    935 	mov	r1,0

00000028 e3500000    936 	cmp	r0,0

0000002c 0a000001    937 	beq	.L309

00000030 eb000000*   938 	bl	__CLZ32

00000034 e2601020    939 	rsb	r1,r0,32

                     940 .L309:

00000038 e2410001    941 	sub	r0,r1,1

0000003c e8bd4000    942 	ldmfd	[sp]!,{lr}

00000040 e12fff1e*   943 	ret	

                     944 	.endf	tlsf_fls

                     945 	.align	4

                     946 

                     947 ;word	r0	param

                     948 

                     949 	.section ".bss","awb"

                     950 .L349:

                     951 	.data

                     952 	.text

                     953 

                     954 

                     955 	.align	4

                     956 	.align	4

                     957 block_size:

00000044 e5900004    958 	ldr	r0,[r0,4]

00000048 e3c00003    959 	bic	r0,r0,3

0000004c e12fff1e*   960 	ret	

                     961 	.endf	block_size

                     962 	.align	4

                     963 

                     964 ;block	r0	param

                     965 


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                     966 	.section ".bss","awb"

                     967 .L382:

                     968 	.data

                     969 	.text

                     970 

                     971 

                     972 	.align	4

                     973 	.align	4

                     974 block_set_size:

00000050 e5902004    975 	ldr	r2,[r0,4]

00000054 e2022003    976 	and	r2,r2,3

00000058 e1821001    977 	orr	r1,r2,r1

0000005c e5801004    978 	str	r1,[r0,4]

00000060 e12fff1e*   979 	ret	

                     980 	.endf	block_set_size

                     981 	.align	4

                     982 ;oldsize	r2	local

                     983 

                     984 ;block	r0	param

                     985 ;size	r1	param

                     986 

                     987 	.section ".bss","awb"

                     988 .L414:

                     989 	.data

                     990 	.text

                     991 

                     992 

                     993 	.align	4

                     994 	.align	4

                     995 block_is_free:

00000064 e5900004    996 	ldr	r0,[r0,4]

00000068 e2000001    997 	and	r0,r0,1

0000006c e12fff1e*   998 	ret	

                     999 	.endf	block_is_free

                    1000 	.align	4

                    1001 

                    1002 ;block	r0	param

                    1003 

                    1004 	.section ".bss","awb"

                    1005 .L446:

                    1006 	.data

                    1007 	.text

                    1008 

                    1009 

                    1010 	.align	4

                    1011 	.align	4

                    1012 block_set_free:

00000070 e5901004   1013 	ldr	r1,[r0,4]

00000074 e3811001   1014 	orr	r1,r1,1

00000078 e5801004   1015 	str	r1,[r0,4]

0000007c e12fff1e*  1016 	ret	

                    1017 	.endf	block_set_free

                    1018 	.align	4

                    1019 

                    1020 ;block	r0	param

                    1021 

                    1022 	.section ".bss","awb"

                    1023 .L478:

                    1024 	.data

                    1025 	.text

                    1026 


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    1027 

                    1028 	.align	4

                    1029 	.align	4

                    1030 block_set_used:

00000080 e5901004   1031 	ldr	r1,[r0,4]

00000084 e3c11001   1032 	bic	r1,r1,1

00000088 e5801004   1033 	str	r1,[r0,4]

0000008c e12fff1e*  1034 	ret	

                    1035 	.endf	block_set_used

                    1036 	.align	4

                    1037 

                    1038 ;block	r0	param

                    1039 

                    1040 	.section ".bss","awb"

                    1041 .L510:

                    1042 	.data

                    1043 	.text

                    1044 

                    1045 

                    1046 	.align	4

                    1047 	.align	4

                    1048 block_is_prev_free:

00000090 e5900004   1049 	ldr	r0,[r0,4]

00000094 e2000002   1050 	and	r0,r0,2

00000098 e12fff1e*  1051 	ret	

                    1052 	.endf	block_is_prev_free

                    1053 	.align	4

                    1054 

                    1055 ;block	r0	param

                    1056 

                    1057 	.section ".bss","awb"

                    1058 .L542:

                    1059 	.data

                    1060 	.text

                    1061 

                    1062 

                    1063 	.align	4

                    1064 	.align	4

                    1065 block_set_prev_free:

0000009c e5901004   1066 	ldr	r1,[r0,4]

000000a0 e3811002   1067 	orr	r1,r1,2

000000a4 e5801004   1068 	str	r1,[r0,4]

000000a8 e12fff1e*  1069 	ret	

                    1070 	.endf	block_set_prev_free

                    1071 	.align	4

                    1072 

                    1073 ;block	r0	param

                    1074 

                    1075 	.section ".bss","awb"

                    1076 .L574:

                    1077 	.data

                    1078 	.text

                    1079 

                    1080 

                    1081 	.align	4

                    1082 	.align	4

                    1083 block_set_prev_used:

000000ac e5901004   1084 	ldr	r1,[r0,4]

000000b0 e3c11002   1085 	bic	r1,r1,2

000000b4 e5801004   1086 	str	r1,[r0,4]

000000b8 e12fff1e*  1087 	ret	


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    1088 	.endf	block_set_prev_used

                    1089 	.align	4

                    1090 

                    1091 ;block	r0	param

                    1092 

                    1093 	.section ".bss","awb"

                    1094 .L606:

                    1095 	.data

                    1096 	.text

                    1097 

                    1098 

                    1099 	.align	4

                    1100 	.align	4

                    1101 block_from_ptr:

000000bc e2400008   1102 	sub	r0,r0,8

000000c0 e12fff1e*  1103 	ret	

                    1104 	.endf	block_from_ptr

                    1105 	.align	4

                    1106 

                    1107 ;ptr	r0	param

                    1108 

                    1109 	.section ".bss","awb"

                    1110 .L638:

                    1111 	.data

                    1112 	.text

                    1113 

                    1114 

                    1115 	.align	4

                    1116 	.align	4

                    1117 block_to_ptr:

000000c4 e2800008   1118 	add	r0,r0,8

000000c8 e12fff1e*  1119 	ret	

                    1120 	.endf	block_to_ptr

                    1121 	.align	4

                    1122 

                    1123 ;block	r0	param

                    1124 

                    1125 	.section ".bss","awb"

                    1126 .L670:

                    1127 	.data

                    1128 	.text

                    1129 

                    1130 

                    1131 	.align	4

                    1132 	.align	4

                    1133 offset_to_block:

000000cc e0810000   1134 	add	r0,r1,r0

000000d0 e12fff1e*  1135 	ret	

                    1136 	.endf	offset_to_block

                    1137 	.align	4

                    1138 

                    1139 ;ptr	r0	param

                    1140 ;size	r1	param

                    1141 

                    1142 	.section ".bss","awb"

                    1143 .L702:

                    1144 	.data

                    1145 	.text

                    1146 

                    1147 

                    1148 	.align	4


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    1149 	.align	4

                    1150 block_next:

000000d4 e92d4030   1151 	stmfd	[sp]!,{r4-r5,lr}

000000d8 e1a04000   1152 	mov	r4,r0

000000dc ebffffd8*  1153 	bl	block_size

000000e0 e2405004   1154 	sub	r5,r0,4

000000e4 e1a00004   1155 	mov	r0,r4

000000e8 ebfffff5*  1156 	bl	block_to_ptr

000000ec e1a01005   1157 	mov	r1,r5

000000f0 ebfffff5*  1158 	bl	offset_to_block

000000f4 e8bd4030   1159 	ldmfd	[sp]!,{r4-r5,lr}

000000f8 e12fff1e*  1160 	ret	

                    1161 	.endf	block_next

                    1162 	.align	4

                    1163 

                    1164 ;block	r4	param

                    1165 

                    1166 	.section ".bss","awb"

                    1167 .L734:

                    1168 	.data

                    1169 	.text

                    1170 

                    1171 

                    1172 	.align	4

                    1173 	.align	4

                    1174 block_link_next:

000000fc e92d4010   1175 	stmfd	[sp]!,{r4,lr}

00000100 e1a04000   1176 	mov	r4,r0

00000104 ebfffff2*  1177 	bl	block_next

00000108 e5804000   1178 	str	r4,[r0]

0000010c e8bd4010   1179 	ldmfd	[sp]!,{r4,lr}

00000110 e12fff1e*  1180 	ret	

                    1181 	.endf	block_link_next

                    1182 	.align	4

                    1183 

                    1184 ;block	r4	param

                    1185 

                    1186 	.section ".bss","awb"

                    1187 .L766:

                    1188 	.data

                    1189 	.text

                    1190 

                    1191 

                    1192 	.align	4

                    1193 	.align	4

                    1194 block_mark_as_free:

00000114 e92d4010   1195 	stmfd	[sp]!,{r4,lr}

00000118 e1a04000   1196 	mov	r4,r0

0000011c ebfffff6*  1197 	bl	block_link_next

00000120 ebffffdd*  1198 	bl	block_set_prev_free

00000124 e1a00004   1199 	mov	r0,r4

00000128 ebffffd0*  1200 	bl	block_set_free

0000012c e8bd4010   1201 	ldmfd	[sp]!,{r4,lr}

00000130 e12fff1e*  1202 	ret	

                    1203 	.endf	block_mark_as_free

                    1204 	.align	4

                    1205 

                    1206 ;block	r4	param

                    1207 

                    1208 	.section ".bss","awb"

                    1209 .L798:


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    1210 	.data

                    1211 	.text

                    1212 

                    1213 

                    1214 	.align	4

                    1215 	.align	4

                    1216 block_mark_as_used:

00000134 e92d4010   1217 	stmfd	[sp]!,{r4,lr}

00000138 e1a04000   1218 	mov	r4,r0

0000013c ebffffe4*  1219 	bl	block_next

00000140 ebffffd9*  1220 	bl	block_set_prev_used

00000144 e1a00004   1221 	mov	r0,r4

00000148 ebffffcc*  1222 	bl	block_set_used

0000014c e8bd4010   1223 	ldmfd	[sp]!,{r4,lr}

00000150 e12fff1e*  1224 	ret	

                    1225 	.endf	block_mark_as_used

                    1226 	.align	4

                    1227 

                    1228 ;block	r4	param

                    1229 

                    1230 	.section ".bss","awb"

                    1231 .L830:

                    1232 	.data

                    1233 	.text

                    1234 

                    1235 

                    1236 	.align	4

                    1237 	.align	4

                    1238 align_ptr:

00000154 e2411001   1239 	sub	r1,r1,1

00000158 e0810000   1240 	add	r0,r1,r0

0000015c e1c00001   1241 	bic	r0,r0,r1

00000160 e12fff1e*  1242 	ret	

                    1243 	.endf	align_ptr

                    1244 	.align	4

                    1245 

                    1246 ;ptr	r0	param

                    1247 ;align	r1	param

                    1248 

                    1249 	.section ".bss","awb"

                    1250 .L862:

                    1251 	.data

                    1252 	.text

                    1253 

                    1254 

                    1255 	.align	4

                    1256 	.align	4

                    1257 adjust_request_size:

00000164 e1b02000   1258 	movs	r2,r0

00000168 e3a00000   1259 	mov	r0,0

0000016c 0a000007   1260 	beq	.L869

                    1261 ;472: {


                    1262 

                    1263 ;473: 	tlsf_assert(0 == (align & (align - 1)) && "must align to a power of two");


                    1264 ;474: 	return (x + (align - 1)) & ~(align - 1);


                    1265 

00000170 e2411001   1266 	sub	r1,r1,1

00000174 e0812002   1267 	add	r2,r1,r2

00000178 e1c21001   1268 	bic	r1,r2,r1

0000017c e3510440   1269 	cmp	r1,1<<30

00000180 2a000002   1270 	bhs	.L869


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
00000184 e351000c   1271 	cmp	r1,12

00000188 21a00001   1272 	movhs	r0,r1

0000018c 33a0000c   1273 	movlo	r0,12

                    1274 .L869:

00000190 e12fff1e*  1275 	ret	

                    1276 	.endf	adjust_request_size

                    1277 	.align	4

                    1278 ;adjust	r0	local

                    1279 ;aligned	r1	local

                    1280 

                    1281 ;size	r2	param

                    1282 ;align	r1	param

                    1283 

                    1284 	.section ".bss","awb"

                    1285 .L916:

                    1286 	.data

                    1287 	.text

                    1288 

                    1289 

                    1290 	.align	4

                    1291 	.align	4

                    1292 mapping_insert:

00000194 e92d4070   1293 	stmfd	[sp]!,{r4-r6,lr}

00000198 e1a06002   1294 	mov	r6,r2

0000019c e1a04000   1295 	mov	r4,r0

000001a0 e3540080   1296 	cmp	r4,128

000001a4 33a00000   1297 	movlo	r0,0

000001a8 e1a05001   1298 	mov	r5,r1

000001ac 31a01fc4   1299 	movlo	r1,r4 asr 31

000001b0 30841f21   1300 	addlo	r1,r4,r1 lsr 30

000001b4 31a01141   1301 	movlo	r1,r1 asr 2

000001b8 3a000004   1302 	blo	.L929

000001bc ebffff97*  1303 	bl	tlsf_fls

000001c0 e2401005   1304 	sub	r1,r0,5

000001c4 e1a01134   1305 	mov	r1,r4 lsr r1

000001c8 e2211020   1306 	eor	r1,r1,32

000001cc e2400006   1307 	sub	r0,r0,6

                    1308 .L929:

000001d0 e5850000   1309 	str	r0,[r5]

000001d4 e5861000   1310 	str	r1,[r6]

000001d8 e8bd4070   1311 	ldmfd	[sp]!,{r4-r6,lr}

000001dc e12fff1e*  1312 	ret	

                    1313 	.endf	mapping_insert

                    1314 	.align	4

                    1315 ;fl	r0	local

                    1316 ;sl	r1	local

                    1317 

                    1318 ;size	r4	param

                    1319 ;fli	r5	param

                    1320 ;sli	r6	param

                    1321 

                    1322 	.section ".bss","awb"

                    1323 .L970:

                    1324 	.data

                    1325 	.text

                    1326 

                    1327 

                    1328 	.align	4

                    1329 	.align	4

                    1330 remove_free_block:

000001e0 e92d0030   1331 	stmfd	[sp]!,{r4-r5}


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
000001e4 e591c008   1332 	ldr	r12,[r1,8]

000001e8 e591400c   1333 	ldr	r4,[r1,12]

000001ec e58c400c   1334 	str	r4,[r12,12]

000001f0 e584c008   1335 	str	r12,[r4,8]

000001f4 e0834282   1336 	add	r4,r3,r2 lsl 5

000001f8 e0805104   1337 	add	r5,r0,r4 lsl 2

000001fc e5955074   1338 	ldr	r5,[r5,116]

00000200 e1550001   1339 	cmp	r5,r1

00000204 00801104   1340 	addeq	r1,r0,r4 lsl 2

00000208 0581c074   1341 	streq	r12,[r1,116]

0000020c 015c0000   1342 	cmpeq	r12,r0

00000210 1a00000a   1343 	bne	.L984

00000214 e3a01001   1344 	mov	r1,1

00000218 e1a0c311   1345 	mov	r12,r1 lsl r3

0000021c e0803102   1346 	add	r3,r0,r2 lsl 2

00000220 e5931014   1347 	ldr	r1,[r3,20]

00000224 e1d1100c   1348 	bics	r1,r1,r12

00000228 e5831014   1349 	str	r1,[r3,20]

0000022c 03a01001   1350 	moveq	r1,1

00000230 01a02211   1351 	moveq	r2,r1 lsl r2

00000234 05901010   1352 	ldreq	r1,[r0,16]

00000238 01c11002   1353 	biceq	r1,r1,r2

0000023c 05801010   1354 	streq	r1,[r0,16]

                    1355 .L984:

00000240 e8bd0030   1356 	ldmfd	[sp]!,{r4-r5}

00000244 e12fff1e*  1357 	ret	

                    1358 	.endf	remove_free_block

                    1359 	.align	4

                    1360 ;prev	r4	local

                    1361 ;next	r12	local

                    1362 

                    1363 ;control	r0	param

                    1364 ;block	r1	param

                    1365 ;fl	r2	param

                    1366 ;sl	r3	param

                    1367 

                    1368 	.section ".bss","awb"

                    1369 .L1036:

                    1370 	.data

                    1371 	.text

                    1372 

                    1373 

                    1374 	.align	4

                    1375 	.align	4

                    1376 block_remove:

00000248 e92d4030   1377 	stmfd	[sp]!,{r4-r5,lr}

0000024c e24dd008   1378 	sub	sp,sp,8

00000250 e1a05000   1379 	mov	r5,r0

00000254 e1a04001   1380 	mov	r4,r1

00000258 e1a00004   1381 	mov	r0,r4

0000025c ebffff78*  1382 	bl	block_size

00000260 e28d2004   1383 	add	r2,sp,4

00000264 e1a0100d   1384 	mov	r1,sp

00000268 ebffffc9*  1385 	bl	mapping_insert

0000026c e89d000c   1386 	ldmfd	[sp],{r2-r3}

00000270 e1a01004   1387 	mov	r1,r4

00000274 e1a00005   1388 	mov	r0,r5

00000278 ebffffd8*  1389 	bl	remove_free_block

0000027c e28dd008   1390 	add	sp,sp,8

00000280 e8bd4030   1391 	ldmfd	[sp]!,{r4-r5,lr}

00000284 e12fff1e*  1392 	ret	


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    1393 	.endf	block_remove

                    1394 	.align	4

                    1395 ;fl	[sp]	local

                    1396 ;sl	[sp,4]	local

                    1397 

                    1398 ;control	r5	param

                    1399 ;block	r4	param

                    1400 

                    1401 	.section ".bss","awb"

                    1402 .L1070:

                    1403 	.data

                    1404 	.text

                    1405 

                    1406 

                    1407 	.align	4

                    1408 	.align	4

                    1409 block_insert:

00000288 e92d4030   1410 	stmfd	[sp]!,{r4-r5,lr}

0000028c e24dd008   1411 	sub	sp,sp,8

00000290 e1a04000   1412 	mov	r4,r0

00000294 e1a05001   1413 	mov	r5,r1

00000298 e1a00005   1414 	mov	r0,r5

0000029c ebffff68*  1415 	bl	block_size

000002a0 e28d2004   1416 	add	r2,sp,4

000002a4 e1a0100d   1417 	mov	r1,sp

000002a8 ebffffb9*  1418 	bl	mapping_insert

                    1419 ;609: {


                    1420 

                    1421 ;610: 	block_header_t* current = control->blocks[fl][sl];


                    1422 

000002ac e89d0005   1423 	ldmfd	[sp],{r0,r2}

000002b0 e0821280   1424 	add	r1,r2,r0 lsl 5

000002b4 e0843101   1425 	add	r3,r4,r1 lsl 2

000002b8 e5931074   1426 	ldr	r1,[r3,116]

                    1427 ;611: 	tlsf_assert(current && "free list cannot have a null entry");


                    1428 ;612: 	tlsf_assert(block && "cannot insert a null entry into the free list");


                    1429 ;613: 	block->next_free = current;


                    1430 

000002bc e585400c   1431 	str	r4,[r5,12]

                    1432 ;615: 	current->prev_free = block;


                    1433 

000002c0 e5851008   1434 	str	r1,[r5,8]

                    1435 ;614: 	block->prev_free = &control->block_null;


                    1436 

000002c4 e581500c   1437 	str	r5,[r1,12]

                    1438 ;616: 


                    1439 ;617: 	tlsf_assert(block_to_ptr(block) == align_ptr(block_to_ptr(block), ALIGN_SIZE)


                    1440 ;618: 		&& "block not aligned properly");


                    1441 ;619: 	/*


                    1442 ;620: 	** Insert the new block at the head of the list, and mark the first-


                    1443 ;621: 	** and second-level bitmaps appropriately.


                    1444 ;622: 	*/


                    1445 ;623: 	control->blocks[fl][sl] = block;


                    1446 

000002c8 e5835074   1447 	str	r5,[r3,116]

                    1448 ;624: 	control->fl_bitmap |= (1 << fl);


                    1449 

000002cc e5941010   1450 	ldr	r1,[r4,16]

000002d0 e3a03001   1451 	mov	r3,1

000002d4 e1811013   1452 	orr	r1,r1,r3 lsl r0

000002d8 e5841010   1453 	str	r1,[r4,16]


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    1454 ;625: 	control->sl_bitmap[fl] |= (1 << sl);


                    1455 

000002dc e0841100   1456 	add	r1,r4,r0 lsl 2

000002e0 e5910014   1457 	ldr	r0,[r1,20]

000002e4 e1800213   1458 	orr	r0,r0,r3 lsl r2

000002e8 e5810014   1459 	str	r0,[r1,20]

000002ec e28dd008   1460 	add	sp,sp,8

000002f0 e8bd4030   1461 	ldmfd	[sp]!,{r4-r5,lr}

000002f4 e12fff1e*  1462 	ret	

                    1463 	.endf	block_insert

                    1464 	.align	4

                    1465 ;fl	[sp]	local

                    1466 ;sl	[sp,4]	local

                    1467 ;current	r1	local

                    1468 

                    1469 ;control	r4	param

                    1470 ;block	r5	param

                    1471 

                    1472 	.section ".bss","awb"

                    1473 .L1105:

                    1474 	.data

                    1475 	.text

                    1476 

                    1477 

                    1478 	.align	4

                    1479 	.align	4

                    1480 block_can_split:

000002f8 e92d4010   1481 	stmfd	[sp]!,{r4,lr}

000002fc e2814010   1482 	add	r4,r1,16

00000300 ebffff4f*  1483 	bl	block_size

00000304 e1500004   1484 	cmp	r0,r4

00000308 23a00001   1485 	movhs	r0,1

0000030c 33a00000   1486 	movlo	r0,0

00000310 e8bd4010   1487 	ldmfd	[sp]!,{r4,lr}

00000314 e12fff1e*  1488 	ret	

                    1489 	.endf	block_can_split

                    1490 	.align	4

                    1491 

                    1492 ;block	none	param

                    1493 ;size	r4	param

                    1494 

                    1495 	.section ".bss","awb"

                    1496 .L1134:

                    1497 	.data

                    1498 	.text

                    1499 

                    1500 

                    1501 	.align	4

                    1502 	.align	4

                    1503 block_split:

00000318 e92d4070   1504 	stmfd	[sp]!,{r4-r6,lr}

0000031c e1a05001   1505 	mov	r5,r1

00000320 e1a06000   1506 	mov	r6,r0

00000324 ebffff66*  1507 	bl	block_to_ptr

00000328 e2451004   1508 	sub	r1,r5,4

0000032c ebffff66*  1509 	bl	offset_to_block

00000330 e1a04000   1510 	mov	r4,r0

00000334 e1a00006   1511 	mov	r0,r6

00000338 ebffff41*  1512 	bl	block_size

0000033c e2851004   1513 	add	r1,r5,4

00000340 e0401001   1514 	sub	r1,r0,r1


                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
00000344 e1a00004   1515 	mov	r0,r4

00000348 ebffff40*  1516 	bl	block_set_size

0000034c e1a01005   1517 	mov	r1,r5

00000350 e1a00006   1518 	mov	r0,r6

00000354 ebffff3d*  1519 	bl	block_set_size

00000358 e1a00004   1520 	mov	r0,r4

0000035c ebffff6c*  1521 	bl	block_mark_as_free

00000360 e1a00004   1522 	mov	r0,r4

00000364 e8bd4070   1523 	ldmfd	[sp]!,{r4-r6,lr}

00000368 e12fff1e*  1524 	ret	

                    1525 	.endf	block_split

                    1526 	.align	4

                    1527 

                    1528 ;block	r6	param

                    1529 ;size	r5	param

                    1530 

                    1531 	.section ".bss","awb"

                    1532 .L1166:

                    1533 	.data

                    1534 	.text

                    1535 

                    1536 

                    1537 	.align	4

                    1538 	.align	4

                    1539 block_absorb:

0000036c e92d4010   1540 	stmfd	[sp]!,{r4,lr}

00000370 e1a04000   1541 	mov	r4,r0

00000374 e1a00001   1542 	mov	r0,r1

00000378 ebffff31*  1543 	bl	block_size

0000037c e5941004   1544 	ldr	r1,[r4,4]

00000380 e0810000   1545 	add	r0,r1,r0

00000384 e2800004   1546 	add	r0,r0,4

00000388 e5840004   1547 	str	r0,[r4,4]

0000038c e1a00004   1548 	mov	r0,r4

00000390 ebffff59*  1549 	bl	block_link_next

00000394 e1a00004   1550 	mov	r0,r4

00000398 e8bd4010   1551 	ldmfd	[sp]!,{r4,lr}

0000039c e12fff1e*  1552 	ret	

                    1553 	.endf	block_absorb

                    1554 	.align	4

                    1555 

                    1556 ;prev	r4	param

                    1557 ;block	r1	param

                    1558 

                    1559 	.section ".bss","awb"

                    1560 .L1198:

                    1561 	.data

                    1562 	.text

                    1563 

                    1564 

                    1565 	.align	4

                    1566 	.align	4

                    1567 block_merge_next:

000003a0 e92d4070   1568 	stmfd	[sp]!,{r4-r6,lr}

000003a4 e1a06000   1569 	mov	r6,r0

000003a8 e1a04001   1570 	mov	r4,r1

000003ac e1a00004   1571 	mov	r0,r4

000003b0 ebffff47*  1572 	bl	block_next

000003b4 e1a05000   1573 	mov	r5,r0

000003b8 ebffff29*  1574 	bl	block_is_free

000003bc e3500000   1575 	cmp	r0,0


                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
000003c0 0a000006   1576 	beq	.L1207

000003c4 e1a01005   1577 	mov	r1,r5

000003c8 e1a00006   1578 	mov	r0,r6

000003cc ebffff9d*  1579 	bl	block_remove

000003d0 e1a01005   1580 	mov	r1,r5

000003d4 e1a00004   1581 	mov	r0,r4

000003d8 ebffffe3*  1582 	bl	block_absorb

000003dc e1a04000   1583 	mov	r4,r0

                    1584 .L1207:

000003e0 e1a00004   1585 	mov	r0,r4

000003e4 e8bd4070   1586 	ldmfd	[sp]!,{r4-r6,lr}

000003e8 e12fff1e*  1587 	ret	

                    1588 	.endf	block_merge_next

                    1589 	.align	4

                    1590 ;next	r5	local

                    1591 

                    1592 ;control	r6	param

                    1593 ;block	r4	param

                    1594 

                    1595 	.section ".bss","awb"

                    1596 .L1245:

                    1597 	.data

                    1598 	.text

                    1599 

                    1600 

                    1601 	.align	4

                    1602 	.align	4

                    1603 block_locate_free:

000003ec e92d4070   1604 	stmfd	[sp]!,{r4-r6,lr}

000003f0 e24dd008   1605 	sub	sp,sp,8

000003f4 e1a05000   1606 	mov	r5,r0

000003f8 e3a06000   1607 	mov	r6,0

000003fc e1a00006   1608 	mov	r0,r6

00000400 e88d0041   1609 	stmea	[sp],{r0,r6}

00000404 e3510000   1610 	cmp	r1,0

00000408 0a00002e   1611 	beq	.L1276

0000040c e1a04001   1612 	mov	r4,r1

                    1613 ;537: {


                    1614 

                    1615 ;538: 	if (size >= SMALL_BLOCK_SIZE)


                    1616 

00000410 e3540080   1617 	cmp	r4,128

00000414 3a000005   1618 	blo	.L1264

                    1619 ;539: 	{


                    1620 

                    1621 ;540: 		const size_t round = (1 << (tlsf_fls_sizet(size) - SL_INDEX_COUNT_LOG2)) - 1;


                    1622 

00000418 e1a00004   1623 	mov	r0,r4

0000041c ebfffeff*  1624 	bl	tlsf_fls

00000420 e2400005   1625 	sub	r0,r0,5

00000424 e3a01001   1626 	mov	r1,1

00000428 e0840011   1627 	add	r0,r4,r1 lsl r0

                    1628 ;541: 		size += round;


                    1629 

0000042c e2404001   1630 	sub	r4,r0,1

                    1631 .L1264:

                    1632 ;542: 	}


                    1633 ;543: 	mapping_insert(size, fli, sli);


                    1634 

00000430 e28d2004   1635 	add	r2,sp,4

00000434 e1a0100d   1636 	mov	r1,sp


                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
00000438 e1a00004   1637 	mov	r0,r4

0000043c ebffff54*  1638 	bl	mapping_insert

00000440 e59d4000   1639 	ldr	r4,[sp]

00000444 e3540018   1640 	cmp	r4,24

00000448 aa00001e   1641 	bge	.L1276

                    1642 ;547: {


                    1643 

                    1644 ;548: 	int fl = *fli;


                    1645 

                    1646 ;549: 	int sl = *sli;


                    1647 

0000044c e59d3004   1648 	ldr	r3,[sp,4]

                    1649 ;550: 


                    1650 ;551: 	/*


                    1651 ;552: 	** First, search for a block in the list associated with the given


                    1652 ;553: 	** fl/sl index.


                    1653 ;554: 	*/


                    1654 ;555: 	unsigned int sl_map = control->sl_bitmap[fl] & (~0U << sl);


                    1655 

00000450 e0850104   1656 	add	r0,r5,r4 lsl 2

00000454 e5900014   1657 	ldr	r0,[r0,20]

00000458 e3e01000   1658 	mvn	r1,0

0000045c e0100311   1659 	ands	r0,r0,r1 lsl r3

                    1660 ;556: 	if (!sl_map)


                    1661 

00000460 1a00000c   1662 	bne	.L1274

                    1663 ;557: 	{


                    1664 

                    1665 ;558: 		/* No block exists. Search in the next largest first-level list. */


                    1666 ;559: 		const unsigned int fl_map = control->fl_bitmap & (~0U << (fl + 1));


                    1667 

00000464 e2840001   1668 	add	r0,r4,1

00000468 e1a00011   1669 	mov	r0,r1 lsl r0

0000046c e5951010   1670 	ldr	r1,[r5,16]

00000470 e0100001   1671 	ands	r0,r0,r1

                    1672 ;560: 		if (!fl_map)


                    1673 

00000474 1a000002   1674 	bne	.L1273

                    1675 ;561: 		{


                    1676 

                    1677 ;562: 			/* No free blocks available, memory has been exhausted. */


                    1678 ;563: 			return 0;


                    1679 

00000478 e1b06000   1680 	movs	r6,r0

0000047c 0a000011   1681 	beq	.L1276

00000480 ea00000c   1682 	b	.L1277

                    1683 .L1273:

                    1684 ;564: 		}


                    1685 ;565: 


                    1686 ;566: 		fl = tlsf_ffs(fl_map);


                    1687 

00000484 ebfffedd*  1688 	bl	tlsf_ffs

00000488 e1a04000   1689 	mov	r4,r0

                    1690 ;567: 		*fli = fl;


                    1691 

0000048c e58d4000   1692 	str	r4,[sp]

                    1693 ;568: 		sl_map = control->sl_bitmap[fl];


                    1694 

00000490 e0850104   1695 	add	r0,r5,r4 lsl 2

00000494 e5900014   1696 	ldr	r0,[r0,20]

                    1697 .L1274:


                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    1698 ;569: 	}


                    1699 ;570: 	tlsf_assert(sl_map && "internal error - second level bitmap is null");


                    1700 ;571: 	sl = tlsf_ffs(sl_map);


                    1701 

00000498 ebfffed8*  1702 	bl	tlsf_ffs

                    1703 ;572: 	*sli = sl;


                    1704 

0000049c e1a03000   1705 	mov	r3,r0

000004a0 e0800284   1706 	add	r0,r0,r4 lsl 5

000004a4 e0850100   1707 	add	r0,r5,r0 lsl 2

000004a8 e5906074   1708 	ldr	r6,[r0,116]

000004ac e58d3004   1709 	str	r3,[sp,4]

                    1710 ;573: 


                    1711 ;574: 	/* Return the first block in the free list. */


                    1712 ;575: 	return control->blocks[fl][sl];


                    1713 

000004b0 e3560000   1714 	cmp	r6,0

000004b4 0a000003   1715 	beq	.L1276

                    1716 .L1277:

000004b8 e59d2000   1717 	ldr	r2,[sp]

000004bc e1a01006   1718 	mov	r1,r6

000004c0 e1a00005   1719 	mov	r0,r5

000004c4 ebffff45*  1720 	bl	remove_free_block

                    1721 .L1276:

000004c8 e1a00006   1722 	mov	r0,r6

000004cc e28dd008   1723 	add	sp,sp,8

000004d0 e8bd4070   1724 	ldmfd	[sp]!,{r4-r6,lr}

000004d4 e12fff1e*  1725 	ret	

                    1726 	.endf	block_locate_free

                    1727 	.align	4

                    1728 ;fl	[sp]	local

                    1729 ;sl	[sp,4]	local

                    1730 ;block	r6	local

                    1731 ;size	r4	local

                    1732 ;round	r0	local

                    1733 ;fl	r4	local

                    1734 ;sl_map	r0	local

                    1735 ;fl_map	r0	local

                    1736 

                    1737 ;control	r5	param

                    1738 ;size	r1	param

                    1739 

                    1740 	.section ".bss","awb"

                    1741 .L1442:

                    1742 	.data

                    1743 	.text

                    1744 

                    1745 

                    1746 	.align	4

                    1747 	.align	4

                    1748 block_prepare_used:

000004d8 e92d4070   1749 	stmfd	[sp]!,{r4-r6,lr}

000004dc e1b04001   1750 	movs	r4,r1

000004e0 e1a05002   1751 	mov	r5,r2

000004e4 e1a06000   1752 	mov	r6,r0

000004e8 e3a00000   1753 	mov	r0,0

000004ec 0a000013   1754 	beq	.L1469

                    1755 ;714: {


                    1756 

                    1757 ;715: 	tlsf_assert(block_is_free(block) && "block must be free");


                    1758 ;716: 	if (block_can_split(block, size))



                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    1759 

000004f0 e1a01005   1760 	mov	r1,r5

000004f4 e1a00004   1761 	mov	r0,r4

000004f8 ebffff7e*  1762 	bl	block_can_split

000004fc e3500000   1763 	cmp	r0,0

00000500 0a00000a   1764 	beq	.L1473

                    1765 ;717: 	{


                    1766 

                    1767 ;718: 		block_header_t* remaining_block = block_split(block, size);


                    1768 

00000504 e1a01005   1769 	mov	r1,r5

00000508 e1a00004   1770 	mov	r0,r4

0000050c ebffff81*  1771 	bl	block_split

00000510 e1a05000   1772 	mov	r5,r0

                    1773 ;719: 		block_link_next(block);


                    1774 

00000514 e1a00004   1775 	mov	r0,r4

00000518 ebfffef7*  1776 	bl	block_link_next

                    1777 ;720: 		block_set_prev_free(remaining_block);


                    1778 

0000051c e1a00005   1779 	mov	r0,r5

00000520 ebfffedd*  1780 	bl	block_set_prev_free

                    1781 ;721: 		block_insert(control, remaining_block);


                    1782 

00000524 e1a01005   1783 	mov	r1,r5

00000528 e1a00006   1784 	mov	r0,r6

0000052c ebffff55*  1785 	bl	block_insert

                    1786 .L1473:

00000530 e1a00004   1787 	mov	r0,r4

00000534 ebfffefe*  1788 	bl	block_mark_as_used

00000538 e1a00004   1789 	mov	r0,r4

0000053c ebfffee0*  1790 	bl	block_to_ptr

                    1791 .L1469:

00000540 e8bd4070   1792 	ldmfd	[sp]!,{r4-r6,lr}

00000544 e12fff1e*  1793 	ret	

                    1794 	.endf	block_prepare_used

                    1795 	.align	4

                    1796 ;p	r0	local

                    1797 ;remaining_block	r5	local

                    1798 

                    1799 ;control	r6	param

                    1800 ;block	r4	param

                    1801 ;size	r5	param

                    1802 

                    1803 	.section ".bss","awb"

                    1804 .L1529:

                    1805 	.data

                    1806 	.text

                    1807 

                    1808 

                    1809 	.align	4

                    1810 	.align	4

                    1811 integrity_walker:

00000548 e92d4cf0   1812 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

0000054c e3a05000   1813 	mov	r5,0

00000550 e1a0a001   1814 	mov	r10,r1

00000554 e1a04003   1815 	mov	r4,r3

00000558 ebfffed7*  1816 	bl	block_from_ptr

0000055c e1a06000   1817 	mov	r6,r0

00000560 ebfffeca*  1818 	bl	block_is_prev_free

00000564 e1b0b000   1819 	movs	fp,r0


                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
00000568 13a0b001   1820 	movne	fp,1

0000056c e1a00006   1821 	mov	r0,r6

00000570 ebfffebb*  1822 	bl	block_is_free

00000574 e1b07000   1823 	movs	r7,r0

00000578 13a07001   1824 	movne	r7,1

0000057c e1a00006   1825 	mov	r0,r6

00000580 ebfffeaf*  1826 	bl	block_size

00000584 e8940006   1827 	ldmfd	[r4],{r1-r2}

00000588 e151000b   1828 	cmp	r1,fp

0000058c 13e05000   1829 	mvnne	r5,0

00000590 e15a0000   1830 	cmp	r10,r0

00000594 12455001   1831 	subne	r5,r5,1

00000598 e082a005   1832 	add	r10,r2,r5

0000059c e8840480   1833 	stmea	[r4],{r7,r10}

000005a0 e8bd4cf0   1834 	ldmfd	[sp]!,{r4-r7,r10-fp,lr}

000005a4 e12fff1e*  1835 	ret	

                    1836 	.endf	integrity_walker

                    1837 	.align	4

                    1838 ;block	r6	local

                    1839 ;integ	r4	local

                    1840 ;this_prev_status	fp	local

                    1841 ;this_status	r7	local

                    1842 ;this_block_size	r0	local

                    1843 ;status	r5	local

                    1844 

                    1845 ;ptr	none	param

                    1846 ;size	r10	param

                    1847 ;used	none	param

                    1848 ;user	r4	param

                    1849 

                    1850 	.section ".bss","awb"

                    1851 .L1593:

                    1852 	.data

                    1853 	.text

                    1854 

                    1855 

                    1856 	.align	4

                    1857 	.align	4

                    1858 tlsf_check::

000005a8 e92d4ff0   1859 	stmfd	[sp]!,{r4-fp,lr}

000005ac e24dd010   1860 	sub	sp,sp,16

000005b0 e1a05000   1861 	mov	r5,r0

000005b4 e3a04000   1862 	mov	r4,0

000005b8 e1a07004   1863 	mov	r7,r4

                    1864 .L1614:

000005bc e3a0a000   1865 	mov	r10,0

000005c0 e3a08010   1866 	mov	r8,16

000005c4 e2850074   1867 	add	r0,r5,116

000005c8 e080b387   1868 	add	fp,r0,r7 lsl 7

                    1869 .L1793:

000005cc e5950010   1870 	ldr	r0,[r5,16]

000005d0 e3a0c001   1871 	mov	r12,1

000005d4 e000371c   1872 	and	r3,r0,r12 lsl r7

000005d8 e0850107   1873 	add	r0,r5,r7 lsl 2

000005dc e5902014   1874 	ldr	r2,[r0,20]

000005e0 e59b6000   1875 	ldr	r6,[fp]

000005e4 e1a00002   1876 	mov	r0,r2

000005e8 e1100a1c   1877 	tst	r0,r12 lsl r10

000005ec 0a000008   1878 	beq	.L1796

000005f0 e3530000   1879 	cmp	r3,0

000005f4 02444001   1880 	subeq	r4,r4,1


                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
000005f8 e3500000   1881 	cmp	r0,0

000005fc 02444001   1882 	subeq	r4,r4,1

00000600 e1560005   1883 	cmp	r6,r5

00000604 02444001   1884 	subeq	r4,r4,1

00000608 01560005   1885 	cmpeq	r6,r5

0000060c 1a00000c   1886 	bne	.L1802

00000610 ea00002c   1887 	b	.L1979

                    1888 .L1796:

00000614 e1560005   1889 	cmp	r6,r5

00000618 0a00002f   1890 	beq	.L1818

0000061c e2444001   1891 	sub	r4,r4,1

00000620 e1a00002   1892 	mov	r0,r2

00000624 e28a2001   1893 	add	r2,r10,1

00000628 e1a01003   1894 	mov	r1,r3

0000062c e3a03001   1895 	mov	r3,1

00000630 e1100213   1896 	tst	r0,r3 lsl r2

00000634 e59b6004   1897 	ldr	r6,[fp,4]

00000638 e28bb008   1898 	add	fp,fp,8

0000063c 0a000037   1899 	beq	.L1821

00000640 ea00002d   1900 	b	.L1819

                    1901 .L1802:

00000644 e1a00006   1902 	mov	r0,r6

00000648 ebfffe85*  1903 	bl	block_is_free

0000064c e3500000   1904 	cmp	r0,0

00000650 02444001   1905 	subeq	r4,r4,1

00000654 e1a00006   1906 	mov	r0,r6

00000658 ebfffe8c*  1907 	bl	block_is_prev_free

0000065c e3500000   1908 	cmp	r0,0

00000660 12444001   1909 	subne	r4,r4,1

00000664 e1a00006   1910 	mov	r0,r6

00000668 ebfffe99*  1911 	bl	block_next

0000066c e1a09000   1912 	mov	r9,r0

00000670 ebfffe7b*  1913 	bl	block_is_free

00000674 e3500000   1914 	cmp	r0,0

00000678 12444001   1915 	subne	r4,r4,1

0000067c e1a00009   1916 	mov	r0,r9

00000680 ebfffe82*  1917 	bl	block_is_prev_free

00000684 e3500000   1918 	cmp	r0,0

00000688 02444001   1919 	subeq	r4,r4,1

0000068c e1a00006   1920 	mov	r0,r6

00000690 ebfffe6b*  1921 	bl	block_size

00000694 e350000c   1922 	cmp	r0,12

00000698 32444001   1923 	sublo	r4,r4,1

0000069c e28d2004   1924 	add	r2,sp,4

000006a0 e1a0100d   1925 	mov	r1,sp

000006a4 ebfffeba*  1926 	bl	mapping_insert

000006a8 e59d0000   1927 	ldr	r0,[sp]

000006ac e1500007   1928 	cmp	r0,r7

000006b0 059d0004   1929 	ldreq	r0,[sp,4]

000006b4 e5966008   1930 	ldr	r6,[r6,8]

000006b8 0150000a   1931 	cmpeq	r0,r10

000006bc 12444001   1932 	subne	r4,r4,1

000006c0 e1560005   1933 	cmp	r6,r5

000006c4 1affffde   1934 	bne	.L1802

                    1935 .L1979:

000006c8 e5950010   1936 	ldr	r0,[r5,16]

000006cc e3a01001   1937 	mov	r1,1

000006d0 e0003711   1938 	and	r3,r0,r1 lsl r7

000006d4 e0850107   1939 	add	r0,r5,r7 lsl 2

000006d8 e5902014   1940 	ldr	r2,[r0,20]

                    1941 .L1818:


                                                                      Page 33
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
000006dc e1a00002   1942 	mov	r0,r2

000006e0 e28a2001   1943 	add	r2,r10,1

000006e4 e1a01003   1944 	mov	r1,r3

000006e8 e3a03001   1945 	mov	r3,1

000006ec e1100213   1946 	tst	r0,r3 lsl r2

000006f0 e59b6004   1947 	ldr	r6,[fp,4]

000006f4 e28bb008   1948 	add	fp,fp,8

000006f8 0a000008   1949 	beq	.L1821

                    1950 .L1819:

000006fc e3510000   1951 	cmp	r1,0

00000700 02444001   1952 	subeq	r4,r4,1

00000704 e3500000   1953 	cmp	r0,0

00000708 02444001   1954 	subeq	r4,r4,1

0000070c e1560005   1955 	cmp	r6,r5

00000710 02444001   1956 	subeq	r4,r4,1

00000714 01560005   1957 	cmpeq	r6,r5

00000718 1a000007   1958 	bne	.L1827

0000071c ea000028   1959 	b	.L1842

                    1960 .L1821:

00000720 e1560005   1961 	cmp	r6,r5

00000724 0a000026   1962 	beq	.L1842

00000728 e2444001   1963 	sub	r4,r4,1

0000072c e28aa002   1964 	add	r10,r10,2

00000730 e2588001   1965 	subs	r8,r8,1

00000734 1affffa4   1966 	bne	.L1793

00000738 ea000024   1967 	b	.L1615

                    1968 .L1827:

0000073c e1a00006   1969 	mov	r0,r6

00000740 ebfffe47*  1970 	bl	block_is_free

00000744 e3500000   1971 	cmp	r0,0

00000748 02444001   1972 	subeq	r4,r4,1

0000074c e1a00006   1973 	mov	r0,r6

00000750 ebfffe4e*  1974 	bl	block_is_prev_free

00000754 e3500000   1975 	cmp	r0,0

00000758 12444001   1976 	subne	r4,r4,1

0000075c e1a00006   1977 	mov	r0,r6

00000760 ebfffe5b*  1978 	bl	block_next

00000764 e1a09000   1979 	mov	r9,r0

00000768 ebfffe3d*  1980 	bl	block_is_free

0000076c e3500000   1981 	cmp	r0,0

00000770 12444001   1982 	subne	r4,r4,1

00000774 e1a00009   1983 	mov	r0,r9

00000778 ebfffe44*  1984 	bl	block_is_prev_free

0000077c e3500000   1985 	cmp	r0,0

00000780 02444001   1986 	subeq	r4,r4,1

00000784 e1a00006   1987 	mov	r0,r6

00000788 ebfffe2d*  1988 	bl	block_size

0000078c e350000c   1989 	cmp	r0,12

00000790 32444001   1990 	sublo	r4,r4,1

00000794 e28d2004   1991 	add	r2,sp,4

00000798 e1a0100d   1992 	mov	r1,sp

0000079c ebfffe7c*  1993 	bl	mapping_insert

000007a0 e59d0000   1994 	ldr	r0,[sp]

000007a4 e5966008   1995 	ldr	r6,[r6,8]

000007a8 e1500007   1996 	cmp	r0,r7

000007ac 059d1004   1997 	ldreq	r1,[sp,4]

000007b0 028a0001   1998 	addeq	r0,r10,1

000007b4 01510000   1999 	cmpeq	r1,r0

000007b8 12444001   2000 	subne	r4,r4,1

000007bc e1560005   2001 	cmp	r6,r5

000007c0 1affffdd   2002 	bne	.L1827


                                                                      Page 34
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    2003 .L1842:

000007c4 e28aa002   2004 	add	r10,r10,2

000007c8 e2588001   2005 	subs	r8,r8,1

000007cc 1affff7e   2006 	bne	.L1793

                    2007 .L1615:

000007d0 e2877001   2008 	add	r7,r7,1

000007d4 e3570018   2009 	cmp	r7,24

000007d8 baffff77   2010 	blt	.L1614

000007dc e1a00004   2011 	mov	r0,r4

000007e0 e28dd010   2012 	add	sp,sp,16

000007e4 e8bd8ff0   2013 	ldmfd	[sp]!,{r4-fp,pc}

                    2014 	.endf	tlsf_check

                    2015 	.align	4

                    2016 ;i	r7	local

                    2017 ;j	r10	local

                    2018 ;control	r5	local

                    2019 ;status	r4	local

                    2020 ;fl_map	r1	local

                    2021 ;sl_list	r0	local

                    2022 ;block	r6	local

                    2023 ;fli	[sp]	local

                    2024 ;sli	[sp,4]	local

                    2025 

                    2026 ;tlsf	r0	param

                    2027 

                    2028 	.section ".bss","awb"

                    2029 .L2240:

                    2030 	.data

                    2031 	.text

                    2032 

                    2033 

                    2034 	.align	4

                    2035 	.align	4

                    2036 default_walker:

000007e8 e12fff1e*  2037 	ret	

                    2038 	.endf	default_walker

                    2039 	.align	4

                    2040 

                    2041 ;ptr	none	param

                    2042 ;size	none	param

                    2043 ;used	none	param

                    2044 ;user	none	param

                    2045 

                    2046 	.section ".bss","awb"

                    2047 .L2382:

                    2048 	.data

                    2049 	.text

                    2050 

                    2051 

                    2052 	.align	4

                    2053 	.align	4

                    2054 tlsf_walk_pool::

000007ec e92d44f0   2055 	stmfd	[sp]!,{r4-r7,r10,lr}

000007f0 e1a05002   2056 	mov	r5,r2

000007f4 e3510000   2057 	cmp	r1,0

000007f8 11a04001   2058 	movne	r4,r1

000007fc 059f45cc*  2059 	ldreq	r4,.L2487

00000800 e3e01003   2060 	mvn	r1,3

00000804 ebfffe30*  2061 	bl	offset_to_block

00000808 e1b06000   2062 	movs	r6,r0

0000080c 0a000013   2063 	beq	.L2389


                                                                      Page 35
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
00000810 ea00000f   2064 	b	.L2400

                    2065 .L2396:

00000814 e1a00006   2066 	mov	r0,r6

00000818 ebfffe11*  2067 	bl	block_is_free

0000081c e3500000   2068 	cmp	r0,0

00000820 03a0a001   2069 	moveq	r10,1

00000824 13a0a000   2070 	movne	r10,0

00000828 e1a00006   2071 	mov	r0,r6

0000082c ebfffe24*  2072 	bl	block_to_ptr

00000830 e1a03005   2073 	mov	r3,r5

00000834 e1a0200a   2074 	mov	r2,r10

00000838 e1a01007   2075 	mov	r1,r7

0000083c e1a0e00f   2076 	mov	lr,pc

00000840 e12fff14*  2077 	bx	r4

00000844 e1a00006   2078 	mov	r0,r6

00000848 ebfffe21*  2079 	bl	block_next

0000084c e1b06000   2080 	movs	r6,r0

00000850 0a000002   2081 	beq	.L2389

                    2082 .L2400:

                    2083 ;380: {


                    2084 

                    2085 ;381: 	return block_size(block) == 0;


                    2086 

00000854 ebfffdfa*  2087 	bl	block_size

00000858 e1b07000   2088 	movs	r7,r0

0000085c 1affffec   2089 	bne	.L2396

                    2090 .L2389:

00000860 e8bd84f0   2091 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    2092 	.endf	tlsf_walk_pool

                    2093 	.align	4

                    2094 ;pool_walker	r4	local

                    2095 ;block	r6	local

                    2096 

                    2097 ;pool	r0	param

                    2098 ;walker	r1	param

                    2099 ;user	r5	param

                    2100 

                    2101 	.section ".bss","awb"

                    2102 .L2467:

                    2103 	.data

                    2104 	.text

                    2105 

                    2106 

                    2107 	.align	4

                    2108 	.align	4

                    2109 tlsf_block_size::

00000864 e92d4000   2110 	stmfd	[sp]!,{lr}

00000868 e3a01000   2111 	mov	r1,0

0000086c e3500000   2112 	cmp	r0,0

00000870 0a000002   2113 	beq	.L2490

00000874 ebfffe10*  2114 	bl	block_from_ptr

00000878 ebfffdf1*  2115 	bl	block_size

0000087c e1a01000   2116 	mov	r1,r0

                    2117 .L2490:

00000880 e1a00001   2118 	mov	r0,r1

00000884 e8bd8000   2119 	ldmfd	[sp]!,{pc}

                    2120 	.endf	tlsf_block_size

                    2121 	.align	4

                    2122 ;size	r1	local

                    2123 

                    2124 ;ptr	r0	param


                                                                      Page 36
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    2125 

                    2126 	.section ".bss","awb"

                    2127 .L2525:

                    2128 	.data

                    2129 	.text

                    2130 

                    2131 

                    2132 	.align	4

                    2133 	.align	4

                    2134 tlsf_check_pool::

00000888 e92d4000   2135 	stmfd	[sp]!,{lr}

0000088c e59f2540*  2136 	ldr	r2,.L2567

00000890 e592c000   2137 	ldr	r12,[r2]

00000894 e52dc008   2138 	str	r12,[sp,-8]!

00000898 e5922004   2139 	ldr	r2,[r2,4]

0000089c e59f1534*  2140 	ldr	r1,.L2568

000008a0 e58d2004   2141 	str	r2,[sp,4]

000008a4 e1a0200d   2142 	mov	r2,sp

000008a8 ebffffcf*  2143 	bl	tlsf_walk_pool

000008ac e59d0004   2144 	ldr	r0,[sp,4]

000008b0 e28dd008   2145 	add	sp,sp,8

000008b4 e8bd8000   2146 	ldmfd	[sp]!,{pc}

                    2147 	.endf	tlsf_check_pool

                    2148 	.align	4

                    2149 ;integ	[sp]	local

                    2150 ;.L2559	.L2562	static

                    2151 

                    2152 ;pool	none	param

                    2153 

                    2154 	.section ".bss","awb"

                    2155 .L2558:

                    2156 	.section ".rodata","a"

00000000 00000000   2157 .L2562:	.space	4

00000004 00000000   2158 	.space	4

                    2159 	.type	.L2562,$object

                    2160 	.size	.L2562,8

                    2161 	.data

                    2162 	.text

                    2163 

                    2164 

                    2165 	.align	4

                    2166 	.align	4

                    2167 tlsf_pool_overhead::

000008b8 e3a00008   2168 	mov	r0,8

000008bc e12fff1e*  2169 	ret	

                    2170 	.endf	tlsf_pool_overhead

                    2171 	.align	4

                    2172 

                    2173 	.section ".bss","awb"

                    2174 .L2590:

                    2175 	.data

                    2176 	.text

                    2177 

                    2178 

                    2179 ;982: 


                    2180 ;983: pool_t tlsf_add_pool(tlsf_t tlsf, void* mem, size_t bytes)


                    2181 	.align	4

                    2182 	.align	4

                    2183 tlsf_add_pool::

000008c0 e92d4070   2184 	stmfd	[sp]!,{r4-r6,lr}

000008c4 e1a06000   2185 	mov	r6,r0


                                                                      Page 37
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
000008c8 e1a04001   2186 	mov	r4,r1

000008cc e1a05002   2187 	mov	r5,r2

                    2188 ;984: {


                    2189 

                    2190 ;985: 	block_header_t* block;


                    2191 ;986: 	block_header_t* next;


                    2192 ;987: 


                    2193 ;988: 	const size_t pool_overhead = tlsf_pool_overhead();


                    2194 

000008d0 ebfffff8*  2195 	bl	tlsf_pool_overhead

                    2196 ;989: 	const size_t pool_bytes = align_down(bytes - pool_overhead, ALIGN_SIZE);


                    2197 

000008d4 e0450000   2198 	sub	r0,r5,r0

                    2199 ;478: {


                    2200 

                    2201 ;479: 	tlsf_assert(0 == (align & (align - 1)) && "must align to a power of two");


                    2202 ;480: 	return x - (x & (align - 1));


                    2203 

000008d8 e3c05003   2204 	bic	r5,r0,3

                    2205 ;990: 


                    2206 ;991: 	if (((ptrdiff_t)mem % ALIGN_SIZE) != 0)


                    2207 

000008dc e1a00fc4   2208 	mov	r0,r4 asr 31

000008e0 e2000003   2209 	and	r0,r0,3

000008e4 e0840000   2210 	add	r0,r4,r0

000008e8 e3c00003   2211 	bic	r0,r0,3

000008ec e1540000   2212 	cmp	r4,r0

000008f0 1a000002   2213 	bne	.L2607

                    2214 ;992: 	{


                    2215 

                    2216 ;993: 		printf("tlsf_add_pool: Memory must be aligned by %u bytes.\n",


                    2217 ;994: 			(unsigned int)ALIGN_SIZE);


                    2218 ;995: 		return 0;


                    2219 

                    2220 ;996: 	}


                    2221 ;997: 


                    2222 ;998: 	if (pool_bytes < block_size_min || pool_bytes > block_size_max)


                    2223 

000008f4 e355000c   2224 	cmp	r5,12

000008f8 22751440   2225 	rsbhss	r1,r5,1<<30

000008fc 2a000001   2226 	bhs	.L2606

                    2227 .L2607:

                    2228 ;999: 	{


                    2229 

                    2230 ;1000: #if defined (TLSF_64BIT)


                    2231 ;1001: 		printf("tlsf_add_pool: Memory size must be between 0x%x and 0x%x00 bytes.\n", 


                    2232 ;1002: 			(unsigned int)(pool_overhead + block_size_min),


                    2233 ;1003: 			(unsigned int)((pool_overhead + block_size_max) / 256));


                    2234 ;1004: #else


                    2235 ;1005: 		printf("tlsf_add_pool: Memory size must be between %u and %u bytes.\n", 


                    2236 ;1006: 			(unsigned int)(pool_overhead + block_size_min),


                    2237 ;1007: 			(unsigned int)(pool_overhead + block_size_max));


                    2238 ;1008: #endif


                    2239 ;1009: 		return 0;


                    2240 

00000900 e3a00000   2241 	mov	r0,0

00000904 ea000016   2242 	b	.L2597

                    2243 .L2606:

                    2244 ;1010: 	}


                    2245 ;1011: 


                    2246 ;1012: 	/*



                                                                      Page 38
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    2247 ;1013: 	** Create the main free block. Offset the start of the block slightly


                    2248 ;1014: 	** so that the prev_phys_block field falls outside of the pool -


                    2249 ;1015: 	** it will never be used.


                    2250 ;1016: 	*/


                    2251 ;1017: 	block = offset_to_block(mem, -(tlsfptr_t)block_header_overhead);


                    2252 

00000908 e3e01003   2253 	mvn	r1,3

0000090c e1a00004   2254 	mov	r0,r4

00000910 ebfffded*  2255 	bl	offset_to_block

                    2256 ;1018: 	block_set_size(block, pool_bytes);


                    2257 

00000914 e1a01005   2258 	mov	r1,r5

00000918 e1a05000   2259 	mov	r5,r0

0000091c ebfffdcb*  2260 	bl	block_set_size

                    2261 ;1019: 	block_set_free(block);


                    2262 

00000920 e1a00005   2263 	mov	r0,r5

00000924 ebfffdd1*  2264 	bl	block_set_free

                    2265 ;1020: 	block_set_prev_used(block);


                    2266 

00000928 e1a00005   2267 	mov	r0,r5

0000092c ebfffdde*  2268 	bl	block_set_prev_used

                    2269 ;1021: 	block_insert(tlsf_cast(control_t*, tlsf), block);


                    2270 

00000930 e1a01005   2271 	mov	r1,r5

00000934 e1a00006   2272 	mov	r0,r6

00000938 ebfffe52*  2273 	bl	block_insert

                    2274 ;1022: 


                    2275 ;1023: 	/* Split the block to create a zero-size sentinel block. */


                    2276 ;1024: 	next = block_link_next(block);


                    2277 

0000093c e1a00005   2278 	mov	r0,r5

00000940 ebfffded*  2279 	bl	block_link_next

                    2280 ;1025: 	block_set_size(next, 0);


                    2281 

00000944 e1a05000   2282 	mov	r5,r0

00000948 e3a01000   2283 	mov	r1,0

0000094c ebfffdbf*  2284 	bl	block_set_size

                    2285 ;1026: 	block_set_used(next);


                    2286 

00000950 e1a00005   2287 	mov	r0,r5

00000954 ebfffdc9*  2288 	bl	block_set_used

                    2289 ;1027: 	block_set_prev_free(next);


                    2290 

00000958 e1a00005   2291 	mov	r0,r5

0000095c ebfffdce*  2292 	bl	block_set_prev_free

                    2293 ;1028: 


                    2294 ;1029: 	return mem;


                    2295 

00000960 e1a00004   2296 	mov	r0,r4

                    2297 .L2597:

00000964 e8bd8070   2298 	ldmfd	[sp]!,{r4-r6,pc}

                    2299 	.endf	tlsf_add_pool

                    2300 	.align	4

                    2301 ;block	r5	local

                    2302 ;next	r5	local

                    2303 ;pool_bytes	r5	local

                    2304 ;x	r0	local

                    2305 

                    2306 ;tlsf	r6	param

                    2307 ;mem	r4	param


                                                                      Page 39
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    2308 ;bytes	r5	param

                    2309 

                    2310 	.section ".bss","awb"

                    2311 .L2676:

                    2312 	.data

                    2313 	.text

                    2314 

                    2315 ;1030: }


                    2316 

                    2317 ;1031: 


                    2318 ;1032: void tlsf_remove_pool(tlsf_t tlsf, pool_t pool)


                    2319 	.align	4

                    2320 	.align	4

                    2321 tlsf_remove_pool::

00000968 e92d4030   2322 	stmfd	[sp]!,{r4-r5,lr}

                    2323 ;1033: {


                    2324 

0000096c e24dd008   2325 	sub	sp,sp,8

00000970 e1a05000   2326 	mov	r5,r0

00000974 e3a02000   2327 	mov	r2,0

00000978 e1a00002   2328 	mov	r0,r2

0000097c e88d0005   2329 	stmea	[sp],{r0,r2}

                    2330 ;1034: 	control_t* control = tlsf_cast(control_t*, tlsf);


                    2331 

                    2332 ;1035: 	block_header_t* block = offset_to_block(pool, -(int)block_header_overhead);


                    2333 

00000980 e1a00001   2334 	mov	r0,r1

00000984 e3e01003   2335 	mvn	r1,3

00000988 ebfffdcf*  2336 	bl	offset_to_block

                    2337 ;1036: 


                    2338 ;1037: 	int fl = 0, sl = 0;


                    2339 

                    2340 ;1038: 


                    2341 ;1039: 	tlsf_assert(block_is_free(block) && "block should be free");


                    2342 ;1040: 	tlsf_assert(!block_is_free(block_next(block)) && "next block should not be free");


                    2343 ;1041: 	tlsf_assert(block_size(block_next(block)) == 0 && "next block size should be zero");


                    2344 ;1042: 


                    2345 ;1043: 	mapping_insert(block_size(block), &fl, &sl);


                    2346 

0000098c e1a04000   2347 	mov	r4,r0

00000990 ebfffdab*  2348 	bl	block_size

00000994 e28d2004   2349 	add	r2,sp,4

00000998 e1a0100d   2350 	mov	r1,sp

0000099c ebfffdfc*  2351 	bl	mapping_insert

                    2352 ;1044: 	remove_free_block(control, block, fl, sl);


                    2353 

000009a0 e89d000c   2354 	ldmfd	[sp],{r2-r3}

000009a4 e1a01004   2355 	mov	r1,r4

000009a8 e1a00005   2356 	mov	r0,r5

000009ac ebfffe0b*  2357 	bl	remove_free_block

000009b0 e28dd008   2358 	add	sp,sp,8

000009b4 e8bd8030   2359 	ldmfd	[sp]!,{r4-r5,pc}

                    2360 	.endf	tlsf_remove_pool

                    2361 	.align	4

                    2362 ;block	r4	local

                    2363 ;fl	[sp]	local

                    2364 ;sl	[sp,4]	local

                    2365 

                    2366 ;tlsf	r5	param

                    2367 ;pool	r1	param

                    2368 


                                                                      Page 40
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    2369 	.section ".bss","awb"

                    2370 .L2718:

                    2371 	.data

                    2372 	.text

                    2373 

                    2374 ;1045: }


                    2375 

                    2376 ;1046: 


                    2377 ;1047: /*


                    2378 ;1048: ** TLSF main interface.


                    2379 ;1049: */


                    2380 ;1050: 


                    2381 ;1051: #if _DEBUG


                    2382 ;1052: int test_ffs_fls()


                    2383 ;1053: {


                    2384 ;1054: 	/* Verify ffs/fls work properly. */


                    2385 ;1055: 	int rv = 0;


                    2386 ;1056: 	rv += (tlsf_ffs(0) == -1) ? 0 : 0x1;


                    2387 ;1057: 	rv += (tlsf_fls(0) == -1) ? 0 : 0x2;


                    2388 ;1058: 	rv += (tlsf_ffs(1) == 0) ? 0 : 0x4;


                    2389 ;1059: 	rv += (tlsf_fls(1) == 0) ? 0 : 0x8;


                    2390 ;1060: 	rv += (tlsf_ffs(0x80000000) == 31) ? 0 : 0x10;


                    2391 ;1061: 	rv += (tlsf_ffs(0x80008000) == 15) ? 0 : 0x20;


                    2392 ;1062: 	rv += (tlsf_fls(0x80000008) == 31) ? 0 : 0x40;


                    2393 ;1063: 	rv += (tlsf_fls(0x7FFFFFFF) == 30) ? 0 : 0x80;


                    2394 ;1064: 


                    2395 ;1065: #if defined (TLSF_64BIT)


                    2396 ;1066: 	rv += (tlsf_fls_sizet(0x80000000) == 31) ? 0 : 0x100;


                    2397 ;1067: 	rv += (tlsf_fls_sizet(0x100000000) == 32) ? 0 : 0x200;


                    2398 ;1068: 	rv += (tlsf_fls_sizet(0xffffffffffffffff) == 63) ? 0 : 0x400;


                    2399 ;1069: #endif


                    2400 ;1070: 


                    2401 ;1071: 	if (rv)


                    2402 ;1072: 	{


                    2403 ;1073: 		printf("test_ffs_fls: %x ffs/fls tests failed.\n", rv);


                    2404 ;1074: 	}


                    2405 ;1075: 	return rv;


                    2406 ;1076: }


                    2407 ;1077: #endif


                    2408 ;1078: 


                    2409 ;1079: tlsf_t tlsf_create(void* mem)


                    2410 	.align	4

                    2411 	.align	4

                    2412 tlsf_create::

000009b8 e92d0030   2413 	stmfd	[sp]!,{r4-r5}

                    2414 ;1080: {


                    2415 

                    2416 ;1081: #if _DEBUG


                    2417 ;1082: 	if (test_ffs_fls())


                    2418 ;1083: 	{


                    2419 ;1084: 		return 0;


                    2420 ;1085: 	}


                    2421 ;1086: #endif


                    2422 ;1087: 


                    2423 ;1088: 	if (((tlsfptr_t)mem % ALIGN_SIZE) != 0)


                    2424 

000009bc e1a01fc0   2425 	mov	r1,r0 asr 31

000009c0 e2011003   2426 	and	r1,r1,3

000009c4 e0801001   2427 	add	r1,r0,r1

000009c8 e3c11003   2428 	bic	r1,r1,3

000009cc e1500001   2429 	cmp	r0,r1


                                                                      Page 41
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    2430 ;1089: 	{


                    2431 

                    2432 ;1090: 		printf("tlsf_create: Memory must be aligned to %u bytes.\n",


                    2433 ;1091: 			(unsigned int)ALIGN_SIZE);


                    2434 ;1092: 		return 0;


                    2435 

000009d0 13a00000   2436 	movne	r0,0

000009d4 1a000021   2437 	bne	.L2725

000009d8 e5800008   2438 	str	r0,[r0,8]

                    2439 ;805: 	control->block_null.prev_free = &control->block_null;


                    2440 

000009dc e580000c   2441 	str	r0,[r0,12]

                    2442 ;806: 


                    2443 ;807: 	control->fl_bitmap = 0;


                    2444 

000009e0 e3a03000   2445 	mov	r3,0

                    2446 ;1093: 	}


                    2447 ;1094: 


                    2448 ;1095: 	control_construct(tlsf_cast(control_t*, mem));


                    2449 

                    2450 ;801: {


                    2451 

                    2452 ;802: 	int i, j;


                    2453 ;803: 


                    2454 ;804: 	control->block_null.next_free = &control->block_null;


                    2455 

000009e4 e5803010   2456 	str	r3,[r0,16]

                    2457 ;808: 	for (i = 0; i < FL_INDEX_COUNT; ++i)


                    2458 

000009e8 e1a01003   2459 	mov	r1,r3

000009ec e3a0200c   2460 	mov	r2,12

                    2461 .L2796:

000009f0 e3a04004   2462 	mov	r4,4

000009f4 e080c101   2463 	add	r12,r0,r1 lsl 2

000009f8 e58c3014   2464 	str	r3,[r12,20]

000009fc e280c074   2465 	add	r12,r0,116

00000a00 e08cc381   2466 	add	r12,r12,r1 lsl 7

                    2467 .L2804:

00000a04 e1a05000   2468 	mov	r5,r0

00000a08 e8ac0021   2469 	stmea	[r12]!,{r0,r5}

00000a0c e8ac0021   2470 	stmea	[r12]!,{r0,r5}

00000a10 e8ac0021   2471 	stmea	[r12]!,{r0,r5}

00000a14 e8ac0021   2472 	stmea	[r12]!,{r0,r5}

00000a18 e2544001   2473 	subs	r4,r4,1

00000a1c 1afffff8   2474 	bne	.L2804

00000a20 e281c001   2475 	add	r12,r1,1

00000a24 e080410c   2476 	add	r4,r0,r12 lsl 2

00000a28 e5843014   2477 	str	r3,[r4,20]

00000a2c e3a04004   2478 	mov	r4,4

00000a30 e2805074   2479 	add	r5,r0,116

00000a34 e085c38c   2480 	add	r12,r5,r12 lsl 7

                    2481 .L2814:

00000a38 e1a05000   2482 	mov	r5,r0

00000a3c e8ac0021   2483 	stmea	[r12]!,{r0,r5}

00000a40 e8ac0021   2484 	stmea	[r12]!,{r0,r5}

00000a44 e8ac0021   2485 	stmea	[r12]!,{r0,r5}

00000a48 e8ac0021   2486 	stmea	[r12]!,{r0,r5}

00000a4c e2544001   2487 	subs	r4,r4,1

00000a50 1afffff8   2488 	bne	.L2814

00000a54 e2811002   2489 	add	r1,r1,2

00000a58 e2522001   2490 	subs	r2,r2,1


                                                                      Page 42
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    2491 ;1096: 


                    2492 ;1097: 	return tlsf_cast(tlsf_t, mem);


                    2493 

00000a5c 1affffe3   2494 	bne	.L2796

                    2495 .L2725:

00000a60 e8bd0030   2496 	ldmfd	[sp]!,{r4-r5}

00000a64 e12fff1e*  2497 	ret	

                    2498 	.endf	tlsf_create

                    2499 	.align	4

                    2500 ;i	r1	local

                    2501 

                    2502 ;mem	r0	param

                    2503 

                    2504 	.section ".bss","awb"

                    2505 .L2952:

                    2506 	.data

                    2507 	.text

                    2508 

                    2509 ;1098: }


                    2510 

                    2511 ;1099: 


                    2512 ;1100: tlsf_t tlsf_create_with_pool(void* mem, size_t bytes)


                    2513 	.align	4

                    2514 	.align	4

                    2515 tlsf_create_with_pool::

00000a68 e92d4030   2516 	stmfd	[sp]!,{r4-r5,lr}

                    2517 ;1101: {


                    2518 

                    2519 ;1102: 	tlsf_t tlsf = tlsf_create(mem);


                    2520 

00000a6c e1a05001   2521 	mov	r5,r1

00000a70 e1a04000   2522 	mov	r4,r0

00000a74 ebffffcf*  2523 	bl	tlsf_create

                    2524 ;1103: 	tlsf_add_pool(tlsf, (char*)mem + tlsf_size(), bytes - tlsf_size());


                    2525 

                    2526 ;949: {


                    2527 

                    2528 ;950: 	return sizeof(control_t);


                    2529 

                    2530 ;949: {


                    2531 

                    2532 ;950: 	return sizeof(control_t);


                    2533 

00000a78 e3a03ec0   2534 	mov	r3,3<<10

00000a7c e2833074   2535 	add	r3,r3,116

00000a80 e0452003   2536 	sub	r2,r5,r3

00000a84 e0841003   2537 	add	r1,r4,r3

00000a88 e1a04000   2538 	mov	r4,r0

00000a8c ebffff8b*  2539 	bl	tlsf_add_pool

                    2540 ;1104: 	return tlsf;


                    2541 

00000a90 e1a00004   2542 	mov	r0,r4

00000a94 e8bd8030   2543 	ldmfd	[sp]!,{r4-r5,pc}

                    2544 	.endf	tlsf_create_with_pool

                    2545 	.align	4

                    2546 

                    2547 ;mem	r4	param

                    2548 ;bytes	r5	param

                    2549 

                    2550 	.section ".bss","awb"

                    2551 .L3022:


                                                                      Page 43
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    2552 	.data

                    2553 	.text

                    2554 

                    2555 ;1105: }


                    2556 

                    2557 ;1106: 


                    2558 ;1107: void tlsf_destroy(tlsf_t tlsf)


                    2559 	.align	4

                    2560 	.align	4

                    2561 tlsf_destroy::

                    2562 ;1108: {


                    2563 

                    2564 ;1109: 	/* Nothing to do. */


                    2565 ;1110: 	(void)tlsf;


                    2566 

00000a98 e12fff1e*  2567 	ret	

                    2568 	.endf	tlsf_destroy

                    2569 	.align	4

                    2570 

                    2571 ;tlsf	none	param

                    2572 

                    2573 	.section ".bss","awb"

                    2574 .L3054:

                    2575 	.data

                    2576 	.text

                    2577 

                    2578 ;1111: }


                    2579 

                    2580 ;1112: 


                    2581 ;1113: pool_t tlsf_get_pool(tlsf_t tlsf)


                    2582 	.align	4

                    2583 	.align	4

                    2584 tlsf_get_pool::

                    2585 ;1114: {


                    2586 

                    2587 ;1115: 	return tlsf_cast(pool_t, (char*)tlsf + tlsf_size());


                    2588 

                    2589 ;949: {


                    2590 

                    2591 ;950: 	return sizeof(control_t);


                    2592 

00000a9c e3a01ec0   2593 	mov	r1,3<<10

00000aa0 e2811074   2594 	add	r1,r1,116

00000aa4 e0800001   2595 	add	r0,r0,r1

00000aa8 e12fff1e*  2596 	ret	

                    2597 	.endf	tlsf_get_pool

                    2598 	.align	4

                    2599 

                    2600 ;tlsf	r0	param

                    2601 

                    2602 	.section ".bss","awb"

                    2603 .L3086:

                    2604 	.data

                    2605 	.text

                    2606 

                    2607 ;1116: }


                    2608 

                    2609 ;1117: 


                    2610 ;1118: void* tlsf_malloc(tlsf_t tlsf, size_t size)


                    2611 	.align	4

                    2612 	.align	4


                                                                      Page 44
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    2613 tlsf_malloc::

00000aac e92d4030   2614 	stmfd	[sp]!,{r4-r5,lr}

00000ab0 e1a05000   2615 	mov	r5,r0

                    2616 ;1119: {


                    2617 

                    2618 ;1120: 	control_t* control = tlsf_cast(control_t*, tlsf);


                    2619 

                    2620 ;1121: 	const size_t adjust = adjust_request_size(size, ALIGN_SIZE);


                    2621 

00000ab4 e1a00001   2622 	mov	r0,r1

00000ab8 e3a01004   2623 	mov	r1,4

00000abc ebfffda8*  2624 	bl	adjust_request_size

00000ac0 e1a04000   2625 	mov	r4,r0

                    2626 ;1122: 	block_header_t* block = block_locate_free(control, adjust);


                    2627 

00000ac4 e1a01004   2628 	mov	r1,r4

00000ac8 e1a00005   2629 	mov	r0,r5

00000acc ebfffe46*  2630 	bl	block_locate_free

                    2631 ;1123: 	return block_prepare_used(control, block, adjust);


                    2632 

00000ad0 e1a02004   2633 	mov	r2,r4

00000ad4 e1a01000   2634 	mov	r1,r0

00000ad8 e1a00005   2635 	mov	r0,r5

00000adc e8bd4030   2636 	ldmfd	[sp]!,{r4-r5,lr}

00000ae0 eafffe7c*  2637 	b	block_prepare_used

                    2638 	.endf	tlsf_malloc

                    2639 	.align	4

                    2640 ;adjust	r4	local

                    2641 

                    2642 ;tlsf	r5	param

                    2643 ;size	r1	param

                    2644 

                    2645 	.section ".bss","awb"

                    2646 .L3118:

                    2647 	.data

                    2648 	.text

                    2649 

                    2650 ;1124: }


                    2651 

                    2652 ;1125: 


                    2653 ;1126: void* tlsf_memalign(tlsf_t tlsf, size_t align, size_t size)


                    2654 	.align	4

                    2655 	.align	4

                    2656 tlsf_memalign::

00000ae4 e92d4cf0   2657 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                    2658 ;1127: {


                    2659 

                    2660 ;1128: 	control_t* control = tlsf_cast(control_t*, tlsf);


                    2661 

00000ae8 e1a05000   2662 	mov	r5,r0

                    2663 ;1129: 	const size_t adjust = adjust_request_size(size, ALIGN_SIZE);


                    2664 

00000aec e1a00002   2665 	mov	r0,r2

00000af0 e1a0a001   2666 	mov	r10,r1

00000af4 e3a01004   2667 	mov	r1,4

00000af8 ebfffd99*  2668 	bl	adjust_request_size

00000afc e1a0100a   2669 	mov	r1,r10

00000b00 e1a04000   2670 	mov	r4,r0

                    2671 ;1130: 


                    2672 ;1131: 	/*


                    2673 ;1132: 	** We must allocate an additional minimum block size bytes so that if



                                                                      Page 45
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    2674 ;1133: 	** our free block will leave an alignment gap which is smaller, we can


                    2675 ;1134: 	** trim a leading free block and release it back to the pool. We must


                    2676 ;1135: 	** do this because the previous physical block is in use, therefore


                    2677 ;1136: 	** the prev_phys_block field is not valid, and we can't simply adjust


                    2678 ;1137: 	** the size of that block.


                    2679 ;1138: 	*/


                    2680 ;1139: 	const size_t gap_minimum = sizeof(block_header_t);


                    2681 

                    2682 ;1140: 	const size_t size_with_gap = adjust_request_size(adjust + align + gap_minimum, align);


                    2683 

00000b04 e084000a   2684 	add	r0,r4,r10

00000b08 e2800010   2685 	add	r0,r0,16

00000b0c ebfffd94*  2686 	bl	adjust_request_size

                    2687 ;1141: 


                    2688 ;1142: 	/*


                    2689 ;1143: 	** If alignment is less than or equals base alignment, we're done.


                    2690 ;1144: 	** If we requested 0 bytes, return null, as tlsf_malloc(0) does.


                    2691 ;1145: 	*/


                    2692 ;1146: 	const size_t aligned_size = (adjust && align > ALIGN_SIZE) ? size_with_gap : adjust;


                    2693 

00000b10 e3540000   2694 	cmp	r4,0

00000b14 0a000007   2695 	beq	.L3127

00000b18 e35a0004   2696 	cmp	r10,4

00000b1c 91a01004   2697 	movls	r1,r4

00000b20 81a01000   2698 	movhi	r1,r0

                    2699 ;1147: 


                    2700 ;1148: 	block_header_t* block = block_locate_free(control, aligned_size);


                    2701 

00000b24 e1a00005   2702 	mov	r0,r5

00000b28 ebfffe2f*  2703 	bl	block_locate_free

00000b2c e1b06000   2704 	movs	r6,r0

                    2705 ;1149: 


                    2706 ;1150: 	/* This can't be a static assert. */


                    2707 ;1151: 	tlsf_assert(sizeof(block_header_t) == block_size_min + block_header_overhead);


                    2708 ;1152: 


                    2709 ;1153: 	if (block)


                    2710 

00000b30 0a000026   2711 	beq	.L3131

00000b34 ea000004   2712 	b	.L3132

                    2713 .L3127:

00000b38 e1a01004   2714 	mov	r1,r4

                    2715 ;1147: 


                    2716 ;1148: 	block_header_t* block = block_locate_free(control, aligned_size);


                    2717 

00000b3c e1a00005   2718 	mov	r0,r5

00000b40 ebfffe29*  2719 	bl	block_locate_free

00000b44 e1b06000   2720 	movs	r6,r0

                    2721 ;1149: 


                    2722 ;1150: 	/* This can't be a static assert. */


                    2723 ;1151: 	tlsf_assert(sizeof(block_header_t) == block_size_min + block_header_overhead);


                    2724 ;1152: 


                    2725 ;1153: 	if (block)


                    2726 

00000b48 0a000020   2727 	beq	.L3131

                    2728 .L3132:

                    2729 ;1154: 	{


                    2730 

                    2731 ;1155: 		void* ptr = block_to_ptr(block);


                    2732 

00000b4c ebfffd5c*  2733 	bl	block_to_ptr

                    2734 ;1156: 		void* aligned = align_ptr(ptr, align);



                                                                      Page 46
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    2735 

00000b50 e1a0100a   2736 	mov	r1,r10

00000b54 e1a0b000   2737 	mov	fp,r0

00000b58 ebfffd7d*  2738 	bl	align_ptr

                    2739 ;1157: 		size_t gap = tlsf_cast(size_t,


                    2740 

00000b5c e050700b   2741 	subs	r7,r0,fp

                    2742 ;1158: 			tlsf_cast(tlsfptr_t, aligned) - tlsf_cast(tlsfptr_t, ptr));


                    2743 ;1159: 


                    2744 ;1160: 		/* If gap size is too small, offset to next aligned boundary. */


                    2745 ;1161: 		if (gap && gap < gap_minimum)


                    2746 

00000b60 0a00001a   2747 	beq	.L3131

00000b64 e3570010   2748 	cmp	r7,16

00000b68 2a000007   2749 	bhs	.L3140

00000b6c e2671010   2750 	rsb	r1,r7,16

00000b70 e15a0001   2751 	cmp	r10,r1

00000b74 21a0100a   2752 	movhs	r1,r10

00000b78 e0810000   2753 	add	r0,r1,r0

00000b7c e1a0100a   2754 	mov	r1,r10

00000b80 ebfffd73*  2755 	bl	align_ptr

00000b84 e050700b   2756 	subs	r7,r0,fp

                    2757 ;1162: 		{


                    2758 

                    2759 ;1163: 			const size_t gap_remain = gap_minimum - gap;


                    2760 

                    2761 ;1164: 			const size_t offset = tlsf_max(gap_remain, align);


                    2762 

                    2763 ;1165: 			const void* next_aligned = tlsf_cast(void*,


                    2764 

                    2765 ;1166: 				tlsf_cast(tlsfptr_t, aligned) + offset);


                    2766 ;1167: 


                    2767 ;1168: 			aligned = align_ptr(next_aligned, align);


                    2768 

                    2769 ;1169: 			gap = tlsf_cast(size_t,


                    2770 

                    2771 ;1170: 				tlsf_cast(tlsfptr_t, aligned) - tlsf_cast(tlsfptr_t, ptr));


                    2772 ;1171: 		}


                    2773 ;1172: 


                    2774 ;1173: 		if (gap)


                    2775 

00000b88 0a000010   2776 	beq	.L3131

                    2777 .L3140:

                    2778 ;1174: 		{


                    2779 

                    2780 ;1175: 			tlsf_assert(gap >= gap_minimum && "gap size too small");


                    2781 ;1176: 			block = block_trim_free_leading(control, block, gap);


                    2782 

                    2783 ;741: {


                    2784 

                    2785 ;742: 	block_header_t* remaining_block = block;


                    2786 

00000b8c e1a0a006   2787 	mov	r10,r6

                    2788 ;743: 	if (block_can_split(block, size))


                    2789 

00000b90 e1a01007   2790 	mov	r1,r7

00000b94 e1a00006   2791 	mov	r0,r6

00000b98 ebfffdd6*  2792 	bl	block_can_split

00000b9c e3500000   2793 	cmp	r0,0

00000ba0 0a000009   2794 	beq	.L3138

                    2795 ;744: 	{



                                                                      Page 47
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    2796 

                    2797 ;745: 		/* We want the 2nd block. */


                    2798 ;746: 		remaining_block = block_split(block, size - block_header_overhead);


                    2799 

00000ba4 e2471004   2800 	sub	r1,r7,4

00000ba8 e1a00006   2801 	mov	r0,r6

00000bac ebfffdd9*  2802 	bl	block_split

                    2803 ;747: 		block_set_prev_free(remaining_block);


                    2804 

00000bb0 e1a0a000   2805 	mov	r10,r0

00000bb4 ebfffd38*  2806 	bl	block_set_prev_free

                    2807 ;748: 


                    2808 ;749: 		block_link_next(block);


                    2809 

00000bb8 e1a00006   2810 	mov	r0,r6

00000bbc ebfffd4e*  2811 	bl	block_link_next

                    2812 ;750: 		block_insert(control, block);


                    2813 

00000bc0 e1a01006   2814 	mov	r1,r6

00000bc4 e1a00005   2815 	mov	r0,r5

00000bc8 ebfffdae*  2816 	bl	block_insert

                    2817 .L3138:

                    2818 ;751: 	}


                    2819 ;752: 


                    2820 ;753: 	return remaining_block;


                    2821 

00000bcc e1a0600a   2822 	mov	r6,r10

                    2823 .L3131:

                    2824 ;1177: 		}


                    2825 ;1178: 	}


                    2826 ;1179: 


                    2827 ;1180: 	return block_prepare_used(control, block, adjust);


                    2828 

00000bd0 e1a02004   2829 	mov	r2,r4

00000bd4 e1a01006   2830 	mov	r1,r6

00000bd8 e1a00005   2831 	mov	r0,r5

00000bdc e8bd4cf0   2832 	ldmfd	[sp]!,{r4-r7,r10-fp,lr}

00000be0 eafffe3c*  2833 	b	block_prepare_used

                    2834 	.endf	tlsf_memalign

                    2835 	.align	4

                    2836 ;control	r5	local

                    2837 ;adjust	r4	local

                    2838 ;size_with_gap	r0	local

                    2839 ;block	r6	local

                    2840 ;ptr	fp	local

                    2841 ;aligned	r0	local

                    2842 ;gap	r7	local

                    2843 ;gap_remain	r1	local

                    2844 ;offset	r1	local

                    2845 ;remaining_block	r10	local

                    2846 

                    2847 ;tlsf	r0	param

                    2848 ;align	r10	param

                    2849 ;size	r2	param

                    2850 

                    2851 	.section ".bss","awb"

                    2852 .L3269:

                    2853 	.data

                    2854 	.text

                    2855 

                    2856 ;1181: }



                                                                      Page 48
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    2857 

                    2858 ;1182: 


                    2859 ;1183: void tlsf_free(tlsf_t tlsf, void* ptr)


                    2860 	.align	4

                    2861 	.align	4

                    2862 tlsf_free::

00000be4 e92d4070   2863 	stmfd	[sp]!,{r4-r6,lr}

                    2864 ;1184: {


                    2865 

                    2866 ;1185: 	/* Don't attempt to free a NULL pointer. */


                    2867 ;1186: 	if (ptr)


                    2868 

00000be8 e3510000   2869 	cmp	r1,0

00000bec 0a000017   2870 	beq	.L3294

                    2871 ;1187: 	{


                    2872 

                    2873 ;1188: 		control_t* control = tlsf_cast(control_t*, tlsf);


                    2874 

00000bf0 e1a04000   2875 	mov	r4,r0

                    2876 ;1189: 		block_header_t* block = block_from_ptr(ptr);


                    2877 

00000bf4 e1a00001   2878 	mov	r0,r1

00000bf8 ebfffd2f*  2879 	bl	block_from_ptr

                    2880 ;1190: 		tlsf_assert(!block_is_free(block) && "block already marked as free");


                    2881 ;1191: 		block_mark_as_free(block);


                    2882 

00000bfc e1a05000   2883 	mov	r5,r0

00000c00 ebfffd43*  2884 	bl	block_mark_as_free

                    2885 ;1192: 		block = block_merge_prev(control, block);


                    2886 

                    2887 ;683: {


                    2888 

                    2889 ;684: 	if (block_is_prev_free(block))


                    2890 

00000c04 e1a00005   2891 	mov	r0,r5

00000c08 ebfffd20*  2892 	bl	block_is_prev_free

00000c0c e3500000   2893 	cmp	r0,0

00000c10 0a000007   2894 	beq	.L3298

                    2895 ;685: 	{


                    2896 

                    2897 ;686: 		block_header_t* prev = block_prev(block);


                    2898 

                    2899 ;434: {


                    2900 

                    2901 ;435: 	tlsf_assert(block_is_prev_free(block) && "previous block must be free");


                    2902 ;436: 	return block->prev_phys_block;


                    2903 

00000c14 e5956000   2904 	ldr	r6,[r5]

                    2905 ;687: 		tlsf_assert(prev && "prev physical block can't be null");


                    2906 ;688: 		tlsf_assert(block_is_free(prev) && "prev block is not free though marked as such");


                    2907 ;689: 		block_remove(control, prev);


                    2908 

00000c18 e1a00004   2909 	mov	r0,r4

00000c1c e1a01006   2910 	mov	r1,r6

00000c20 ebfffd88*  2911 	bl	block_remove

                    2912 ;690: 		block = block_absorb(prev, block);


                    2913 

00000c24 e1a01005   2914 	mov	r1,r5

00000c28 e1a00006   2915 	mov	r0,r6

00000c2c ebfffdce*  2916 	bl	block_absorb

00000c30 e1a05000   2917 	mov	r5,r0


                                                                      Page 49
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    2918 .L3298:

                    2919 ;691: 	}


                    2920 ;692: 


                    2921 ;693: 	return block;


                    2922 

                    2923 ;1193: 		block = block_merge_next(control, block);


                    2924 

00000c34 e1a01005   2925 	mov	r1,r5

00000c38 e1a00004   2926 	mov	r0,r4

00000c3c ebfffdd7*  2927 	bl	block_merge_next

                    2928 ;1194: 		block_insert(control, block);


                    2929 

00000c40 e1a01000   2930 	mov	r1,r0

00000c44 e1a00004   2931 	mov	r0,r4

00000c48 e8bd4070   2932 	ldmfd	[sp]!,{r4-r6,lr}

00000c4c eafffd8d*  2933 	b	block_insert

                    2934 .L3294:

00000c50 e8bd8070   2935 	ldmfd	[sp]!,{r4-r6,pc}

                    2936 	.endf	tlsf_free

                    2937 	.align	4

                    2938 ;control	r4	local

                    2939 ;block	r5	local

                    2940 

                    2941 ;tlsf	r0	param

                    2942 ;ptr	r1	param

                    2943 

                    2944 	.section ".bss","awb"

                    2945 .L3348:

                    2946 	.data

                    2947 	.text

                    2948 

                    2949 ;1195: 	}


                    2950 ;1196: }


                    2951 

                    2952 ;1197: 


                    2953 ;1198: /*


                    2954 ;1199: ** The TLSF block information provides us with enough information to


                    2955 ;1200: ** provide a reasonably intelligent implementation of realloc, growing or


                    2956 ;1201: ** shrinking the currently allocated block as required.


                    2957 ;1202: **


                    2958 ;1203: ** This routine handles the somewhat esoteric edge cases of realloc:


                    2959 ;1204: ** - a non-zero size with a null pointer will behave like malloc


                    2960 ;1205: ** - a zero size with a non-null pointer will behave like free


                    2961 ;1206: ** - a request that cannot be satisfied will leave the original buffer


                    2962 ;1207: **   untouched


                    2963 ;1208: ** - an extended buffer size will leave the newly-allocated area with


                    2964 ;1209: **   contents undefined


                    2965 ;1210: */


                    2966 ;1211: void* tlsf_realloc(tlsf_t tlsf, void* ptr, size_t size)


                    2967 	.align	4

                    2968 	.align	4

                    2969 tlsf_realloc::

00000c54 e92d4ff0   2970 	stmfd	[sp]!,{r4-fp,lr}

00000c58 e24dd00c   2971 	sub	sp,sp,12

00000c5c e1b04001   2972 	movs	r4,r1

00000c60 e1a05002   2973 	mov	r5,r2

                    2974 ;1212: {


                    2975 

                    2976 ;1213: 	control_t* control = tlsf_cast(control_t*, tlsf);


                    2977 

                    2978 ;1214: 	void* p = 0;



                                                                      Page 50
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    2979 

                    2980 ;1215: 


                    2981 ;1216: 	/* Zero-size requests are treated as free. */


                    2982 ;1217: 	if (ptr && size == 0)


                    2983 

00000c64 0a000008   2984 	beq	.L3368

00000c68 e3a06000   2985 	mov	r6,0

00000c6c e1a07000   2986 	mov	r7,r0

00000c70 e1a08007   2987 	mov	r8,r7

00000c74 e3550000   2988 	cmp	r5,0

00000c78 1a000006   2989 	bne	.L3367

                    2990 ;1218: 	{


                    2991 

                    2992 ;1219: 		tlsf_free(tlsf, ptr);


                    2993 

00000c7c e1a01004   2994 	mov	r1,r4

00000c80 ebffffd7*  2995 	bl	tlsf_free

                    2996 ;1263: 		}


                    2997 ;1264: 	}


                    2998 ;1265: 


                    2999 ;1266: 	return p;


                    3000 

00000c84 e1a00006   3001 	mov	r0,r6

00000c88 ea000043   3002 	b	.L3361

                    3003 .L3368:

                    3004 ;1220: 	}


                    3005 ;1221: 	/* Requests with NULL pointers are treated as malloc. */


                    3006 ;1222: 	else if (!ptr)


                    3007 

                    3008 ;1223: 	{


                    3009 

                    3010 ;1224: 		p = tlsf_malloc(tlsf, size);


                    3011 

00000c8c e1a01005   3012 	mov	r1,r5

00000c90 ebffff85*  3013 	bl	tlsf_malloc

                    3014 ;1263: 		}


                    3015 ;1264: 	}


                    3016 ;1265: 


                    3017 ;1266: 	return p;


                    3018 

00000c94 ea000040   3019 	b	.L3361

                    3020 .L3367:

                    3021 ;1225: 	}


                    3022 ;1226: 	else


                    3023 ;1227: 	{


                    3024 

                    3025 ;1228: 		block_header_t* block = block_from_ptr(ptr);


                    3026 

00000c98 e1a00004   3027 	mov	r0,r4

00000c9c ebfffd06*  3028 	bl	block_from_ptr

                    3029 ;1229: 		block_header_t* next = block_next(block);


                    3030 

00000ca0 e1a06000   3031 	mov	r6,r0

00000ca4 ebfffd0a*  3032 	bl	block_next

00000ca8 e1a09000   3033 	mov	r9,r0

00000cac e1a0a000   3034 	mov	r10,r0

                    3035 ;1230: 


                    3036 ;1231: 		const size_t cursize = block_size(block);


                    3037 

00000cb0 e1a00006   3038 	mov	r0,r6

00000cb4 ebfffce2*  3039 	bl	block_size


                                                                      Page 51
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
00000cb8 e1a0b000   3040 	mov	fp,r0

                    3041 ;1232: 		const size_t combined = cursize + block_size(next) + block_header_overhead;


                    3042 

00000cbc e1a0000a   3043 	mov	r0,r10

00000cc0 ebfffcdf*  3044 	bl	block_size

00000cc4 e08b0000   3045 	add	r0,fp,r0

00000cc8 e280a004   3046 	add	r10,r0,4

00000ccc e58da008   3047 	str	r10,[sp,8]

                    3048 ;1233: 		const size_t adjust = adjust_request_size(size, ALIGN_SIZE);


                    3049 

00000cd0 e1a00005   3050 	mov	r0,r5

00000cd4 e3a01004   3051 	mov	r1,4

00000cd8 ebfffd21*  3052 	bl	adjust_request_size

00000cdc e1a0a000   3053 	mov	r10,r0

                    3054 ;1234: 


                    3055 ;1235: 		tlsf_assert(!block_is_free(block) && "block already marked as free");


                    3056 ;1236: 


                    3057 ;1237: 		/*


                    3058 ;1238: 		** If the next block is used, or when combined with the current


                    3059 ;1239: 		** block, does not offer enough space, we must reallocate and copy.


                    3060 ;1240: 		*/


                    3061 ;1241: 		if (adjust > cursize && (!block_is_free(next) || adjust > combined))


                    3062 

00000ce0 e15a000b   3063 	cmp	r10,fp

00000ce4 9a00001a   3064 	bls	.L3381

00000ce8 e1a00009   3065 	mov	r0,r9

00000cec ebfffcdc*  3066 	bl	block_is_free

00000cf0 e3500000   3067 	cmp	r0,0

00000cf4 0a000002   3068 	beq	.L3371

00000cf8 e59d0008   3069 	ldr	r0,[sp,8]

00000cfc e15a0000   3070 	cmp	r10,r0

00000d00 9a00000e   3071 	bls	.L3378

                    3072 .L3371:

                    3073 ;1242: 		{


                    3074 

                    3075 ;1243: 			p = tlsf_malloc(tlsf, size);


                    3076 

00000d04 e1a01005   3077 	mov	r1,r5

00000d08 e1a00007   3078 	mov	r0,r7

00000d0c ebffff66*  3079 	bl	tlsf_malloc

00000d10 e1b06000   3080 	movs	r6,r0

                    3081 ;1244: 			if (p)


                    3082 

00000d14 0a00001f   3083 	beq	.L3366

                    3084 ;1245: 			{


                    3085 

                    3086 ;1246: 				const size_t minsize = tlsf_min(cursize, size);


                    3087 

00000d18 e155000b   3088 	cmp	r5,fp

00000d1c 91a02005   3089 	movls	r2,r5

00000d20 81a0200b   3090 	movhi	r2,fp

                    3091 ;1247: 				memcpy(p, ptr, minsize);


                    3092 

00000d24 e1a01004   3093 	mov	r1,r4

00000d28 eb000000*  3094 	bl	memcpy

                    3095 ;1248: 				tlsf_free(tlsf, ptr);


                    3096 

00000d2c e1a01004   3097 	mov	r1,r4

00000d30 e1a00007   3098 	mov	r0,r7

00000d34 ebffffaa*  3099 	bl	tlsf_free

                    3100 ;1263: 		}



                                                                      Page 52
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    3101 ;1264: 	}


                    3102 ;1265: 


                    3103 ;1266: 	return p;


                    3104 

00000d38 e1a00006   3105 	mov	r0,r6

00000d3c ea000016   3106 	b	.L3361

                    3107 .L3378:

                    3108 ;1249: 			}


                    3109 ;1250: 		}


                    3110 ;1251: 		else


                    3111 ;1252: 		{


                    3112 

                    3113 ;1253: 			/* Do we need to expand to the next block? */


                    3114 ;1254: 			if (adjust > cursize)


                    3115 

                    3116 ;1255: 			{


                    3117 

                    3118 ;1256: 				block_merge_next(control, block);


                    3119 

00000d40 e1a01006   3120 	mov	r1,r6

00000d44 e1a00008   3121 	mov	r0,r8

00000d48 ebfffd94*  3122 	bl	block_merge_next

                    3123 ;1257: 				block_mark_as_used(block);


                    3124 

00000d4c e1a00006   3125 	mov	r0,r6

00000d50 ebfffcf7*  3126 	bl	block_mark_as_used

                    3127 .L3381:

                    3128 ;1258: 			}


                    3129 ;1259: 


                    3130 ;1260: 			/* Trim the resulting block and return the original pointer. */


                    3131 ;1261: 			block_trim_used(control, block, adjust);


                    3132 

                    3133 ;727: {


                    3134 

                    3135 ;728: 	tlsf_assert(!block_is_free(block) && "block must be used");


                    3136 ;729: 	if (block_can_split(block, size))


                    3137 

00000d54 e1a0100a   3138 	mov	r1,r10

00000d58 e1a00006   3139 	mov	r0,r6

00000d5c ebfffd65*  3140 	bl	block_can_split

00000d60 e3500000   3141 	cmp	r0,0

00000d64 0a00000a   3142 	beq	.L3379

                    3143 ;730: 	{


                    3144 

                    3145 ;731: 		/* If the next block is free, we must coalesce. */


                    3146 ;732: 		block_header_t* remaining_block = block_split(block, size);


                    3147 

00000d68 e1a0100a   3148 	mov	r1,r10

00000d6c e1a00006   3149 	mov	r0,r6

00000d70 ebfffd68*  3150 	bl	block_split

                    3151 ;733: 		block_set_prev_used(remaining_block);


                    3152 

00000d74 e1a05000   3153 	mov	r5,r0

00000d78 ebfffccb*  3154 	bl	block_set_prev_used

                    3155 ;734: 


                    3156 ;735: 		remaining_block = block_merge_next(control, remaining_block);


                    3157 

00000d7c e1a01005   3158 	mov	r1,r5

00000d80 e1a00008   3159 	mov	r0,r8

00000d84 ebfffd85*  3160 	bl	block_merge_next

                    3161 ;736: 		block_insert(control, remaining_block);



                                                                      Page 53
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    3162 

00000d88 e1a01000   3163 	mov	r1,r0

00000d8c e1a00008   3164 	mov	r0,r8

00000d90 ebfffd3c*  3165 	bl	block_insert

                    3166 .L3379:

                    3167 ;1262: 			p = ptr;


                    3168 

00000d94 e1a06004   3169 	mov	r6,r4

                    3170 .L3366:

                    3171 ;1263: 		}


                    3172 ;1264: 	}


                    3173 ;1265: 


                    3174 ;1266: 	return p;


                    3175 

00000d98 e1a00006   3176 	mov	r0,r6

                    3177 .L3361:

00000d9c e28dd00c   3178 	add	sp,sp,12

00000da0 e8bd8ff0   3179 	ldmfd	[sp]!,{r4-fp,pc}

                    3180 	.endf	tlsf_realloc

                    3181 	.align	4

                    3182 ;control	r8	local

                    3183 ;p	r6	local

                    3184 ;block	r6	local

                    3185 ;next	r9	local

                    3186 ;cursize	fp	local

                    3187 ;combined	[sp,8]	local

                    3188 ;adjust	r10	local

                    3189 ;remaining_block	r5	local

                    3190 

                    3191 ;tlsf	r7	param

                    3192 ;ptr	r4	param

                    3193 ;size	r5	param

                    3194 

                    3195 	.section ".bss","awb"

                    3196 .L3556:

                    3197 	.data

                    3198 	.text

                    3199 

                    3200 ;1267: }


                    3201 	.align	4

                    3202 	.align	4

                    3203 tlsf_size::

                    3204 ;949: {


                    3205 

                    3206 ;950: 	return sizeof(control_t);


                    3207 

00000da4 e3a00ec0   3208 	mov	r0,3<<10

00000da8 e2800074   3209 	add	r0,r0,116

00000dac e12fff1e*  3210 	ret	

                    3211 	.endf	tlsf_size

                    3212 	.align	4

                    3213 

                    3214 	.section ".bss","awb"

                    3215 .L3614:

                    3216 	.data

                    3217 	.text

                    3218 	.align	4

                    3219 	.align	4

                    3220 tlsf_align_size::

                    3221 ;954: {


                    3222 


                                                                      Page 54
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
                    3223 ;955: 	return ALIGN_SIZE;


                    3224 

00000db0 e3a00004   3225 	mov	r0,4

00000db4 e12fff1e*  3226 	ret	

                    3227 	.endf	tlsf_align_size

                    3228 	.align	4

                    3229 

                    3230 	.section ".bss","awb"

                    3231 .L3646:

                    3232 	.data

                    3233 	.text

                    3234 	.align	4

                    3235 	.align	4

                    3236 tlsf_block_size_min::

                    3237 ;959: {


                    3238 

                    3239 ;960: 	return block_size_min;


                    3240 

00000db8 e3a0000c   3241 	mov	r0,12

00000dbc e12fff1e*  3242 	ret	

                    3243 	.endf	tlsf_block_size_min

                    3244 	.align	4

                    3245 

                    3246 	.section ".bss","awb"

                    3247 .L3678:

                    3248 	.data

                    3249 	.text

                    3250 	.align	4

                    3251 	.align	4

                    3252 tlsf_block_size_max::

                    3253 ;964: {


                    3254 

                    3255 ;965: 	return block_size_max;


                    3256 

00000dc0 e3a00440   3257 	mov	r0,1<<30

00000dc4 e12fff1e*  3258 	ret	

                    3259 	.endf	tlsf_block_size_max

                    3260 	.align	4

                    3261 

                    3262 	.section ".bss","awb"

                    3263 .L3710:

                    3264 	.data

                    3265 	.text

                    3266 	.align	4

                    3267 	.align	4

                    3268 tlsf_alloc_overhead::

                    3269 ;979: {


                    3270 

                    3271 ;980: 	return block_header_overhead;


                    3272 

00000dc8 e3a00004   3273 	mov	r0,4

00000dcc e12fff1e*  3274 	ret	

                    3275 	.endf	tlsf_alloc_overhead

                    3276 	.align	4

                    3277 

                    3278 	.section ".bss","awb"

                    3279 .L3742:

                    3280 	.data

                    3281 	.text

                    3282 	.align	4

                    3283 .L2487:


                                                                      Page 55
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a481.s
00000dd0 00000000*  3284 	.data.w	default_walker

                    3285 	.type	.L2487,$object

                    3286 	.size	.L2487,4

                    3287 

                    3288 .L2567:

00000dd4 00000000*  3289 	.data.w	.L2562

                    3290 	.type	.L2567,$object

                    3291 	.size	.L2567,4

                    3292 

                    3293 .L2568:

00000dd8 00000000*  3294 	.data.w	integrity_walker

                    3295 	.type	.L2568,$object

                    3296 	.size	.L2568,4

                    3297 

                    3298 	.align	4

                    3299 

                    3300 	.data

                    3301 	.ghsnote version,6

                    3302 	.ghsnote tools,3

                    3303 	.ghsnote options,0

                    3304 	.text

                    3305 	.align	4

                    3306 	.section ".rodata","a"

                    3307 	.align	4

                    3308 	.text

