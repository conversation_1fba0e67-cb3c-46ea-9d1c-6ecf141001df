                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=OscInfo.c -o fs\gh_bn41.o -list=fs/OscInfo.lst C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
Source File: OscInfo.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile fs/OscInfo.c

                      11 ;		-o fs/OscInfo.o

                      12 ;Source File:   fs/OscInfo.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:59 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include <stdlib.h>


                      23 ;2: #include <string.h>


                      24 ;3: #include <stdint.h>


                      25 ;4: #include <stdbool.h>


                      26 ;5: 


                      27 ;6: #include "OscInfo.h"


                      28 ;7: #include "../bufView.h"


                      29 ;8: #include "platform_critical_section.h"


                      30 ;9: #include "OscFiles.h"


                      31 ;10: 


                      32 ;11: // заголовоки осцилограмм разных версий


                      33 ;12: #ifndef STATIC_ASSERT


                      34 ;13: #define STATIC_ASSERT(e) typedef char __C_ASSERT__[(e)?1:-1]


                      35 ;14: #endif


                      36 ;15: 


                      37 ;16: 


                      38 ;17: OscWriteBuffer oscHeaderBuf;


                      39 ;18: #pragma alignvar (4)


                      40 ;19: CriticalSection csHeaderBuf;


                      41 ;20: 


                      42 ;21: #define OSC_HEADER_SIZE offsetof(OSCInfoStruct, iface)


                      43 ;22: 


                      44 ;23: 


                      45 ;24: // NOTE: копии этих структур храняться в мониторе и соответсвующих программах log


                      46 ;25: typedef struct OSCInfoStruct3 OSCInfoStruct3;


                      47 ;26: struct OSCInfoStruct3


                      48 ;27: {


                      49 ;28: 	//! Размер осцилограммы в байтах. Размер:4	    


                      50 ;29: 	unsigned long size;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                      51 ;30: 	//! Абсолютный порядковый номер. Размер:4			    


                      52 ;31: 	unsigned long absNumber;


                      53 ;32: 	//! SizeCRC - контрольная сумма размера. Размер: 2


                      54 ;33: 	unsigned short sizeCRC;


                      55 ;34: 	//! количество кадров предыстории размер: 1		    


                      56 ;35: 	unsigned char prehistCount;


                      57 ;36: 	//! номер первого кадра предыстории размер: 1		    


                      58 ;37: 	unsigned char prehistStart;


                      59 ;38: 	//! Длительность осциллограммы в миллисекундах. Размер:4	    


                      60 ;39: 	unsigned long duration;


                      61 ;40: 	//! Тактовая частота АЦП в герцах. Размер:4		    


                      62 ;41: 	unsigned long adcClkFreq;


                      63 ;42: 	//! Дата (UTC) размер: 4					    


                      64 ;43: 	unsigned long time;


                      65 ;44: 	//! миллисекунды (Дополнение к дате, 0-999) размер: 2	    


                      66 ;45: 	unsigned short time_ms;


                      67 ;46: 	//! неиспользуемые байты 


                      68 ;47: 	unsigned char oscVersionUnusedByte;


                      69 ;48: 	//! Версия осциллографа 1 байта


                      70 ;49: 	unsigned char oscVersion;


                      71 ;50: 


                      72 ;51: 	//! колличество отсчетов на кадр


                      73 ;52: 	unsigned short pointPerFrame;


                      74 ;53: 	unsigned short reservedField;


                      75 ;54: 	//! число аналоговых каналов в осциллограмме


                      76 ;55: 	unsigned short ainCount;


                      77 ;56: 	//! число дискретных каналов в осциллограмме


                      78 ;57: 	unsigned short dinCount;


                      79 ;58: 


                      80 ;59: };


                      81 ;60: 


                      82 ;61: typedef struct OSCInfoStruct4 OSCInfoStruct4;


                      83 ;62: struct  OSCInfoStruct4


                      84 ;63: {


                      85 ;64: 	//! Размер осцилограммы в байтах. Размер:4	    


                      86 ;65: 	unsigned long size;


                      87 ;66: 	//! Абсолютный порядковый номер. Размер:4			    


                      88 ;67: 	unsigned long absNumber;


                      89 ;68: 	//! SizeCRC - контрольная сумма размера. Размер: 2


                      90 ;69: 	unsigned short sizeCRC;


                      91 ;70: 	unsigned short reservedField;


                      92 ;71: 	//! Длительность осциллограммы в миллисекундах. Размер:4	    


                      93 ;72: 	unsigned long duration;


                      94 ;73: 	//! Тактовая частота АЦП в герцах. Размер:4		    


                      95 ;74: 	unsigned long adcClkFreq;


                      96 ;75: 	//! Дата (UTC) размер: 4		


                      97 ;76: 	unsigned long time;


                      98 ;77: 	//! миллисекунды (Дополнение к дате, 0-999) размер: 2	    


                      99 ;78: 	unsigned short time_ms;


                     100 ;79: 


                     101 ;80: 	unsigned char oscVersionUnusedByte;


                     102 ;81: 	//! Версия осциллографа 1 байта


                     103 ;82: 	unsigned char oscVersion;


                     104 ;83: 


                     105 ;84: 	//! количество кадров предыстории размер: 4		    


                     106 ;85: 	unsigned long prehist_count;


                     107 ;86: 	//! номер первого кадра предыстории размер: 4	


                     108 ;87: 	unsigned long prehistStart;


                     109 ;88: 	//! колличество отсчетов на кадр


                     110 ;89: 	unsigned short pointPerFrame;


                     111 ;90: 	//! Размер одной выборки АЦП в байтах



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                     112 ;91: 	unsigned char adcSampleSize;


                     113 ;92: 	//! Количество бит после фиксированной точки в выборке АЦП,


                     114 ;93: 	//! включая незначащие биты.


                     115 ;94: 	//! (значение выборки AЦП надо поделить на 2 в этой степени)	


                     116 ;95: 	unsigned char adcFractionSize;


                     117 ;96: 	//! число аналоговых каналов в осциллограмме


                     118 ;97: 	unsigned short ainCount;


                     119 ;98: 	//! число дискретных каналов в осциллограмме


                     120 ;99: 	unsigned short dinCount;


                     121 ;100: };


                     122 ;101: 


                     123 ;102: // интерфейс


                     124 ;103: typedef struct IOSCInfo IOSCInfo;


                     125 ;104: struct IOSCInfo


                     126 ;105: {


                     127 ;106: 	// одинаковые для v3 и v4


                     128 ;107: 	unsigned long(*getUTCDate)(OSCInfoStruct *oscInfo);


                     129 ;108: 	unsigned long(*getDateMS)(OSCInfoStruct *oscInfo);


                     130 ;109: 	unsigned long(*getOscVersion)(OSCInfoStruct *oscInfo);


                     131 ;110: 	unsigned long(*getADCClkFreq)(OSCInfoStruct *oscInfo);


                     132 ;111: 	unsigned long(*getPrehistFrameCount)(OSCInfoStruct *oscInfo);


                     133 ;112: 	unsigned long(*getPrehistFirstFrameNum)(OSCInfoStruct *oscInfo);


                     134 ;113: 	unsigned long(*getPointPerFrameCount)(OSCInfoStruct *oscInfo);


                     135 ;114: 	unsigned long(*getAnalogInCount)(OSCInfoStruct *oscInfo);


                     136 ;115: 	unsigned long(*getDigInCount)(OSCInfoStruct *oscInfo);


                     137 ;116: 	unsigned int(*getOscSize)(OSCInfoStruct *oscInfo);


                     138 ;117: 	// разные для v3 и v4


                     139 ;118: 	unsigned long(*getHeaderSize)(OSCInfoStruct *oscInfo);


                     140 ;119: 	int(*getADCSampleSize)(OSCInfoStruct *oscInfo);


                     141 ;120: 	int(*getADCFractionSize)(OSCInfoStruct *oscInfo);


                     142 ;121: };


                     143 ;122: 


                     144 ;123: typedef struct OSCInfoAnalog OSCInfoAnalog;


                     145 ;124: struct OSCInfoAnalog


                     146 ;125: {


                     147 ;126: 	OscDescrAnalog *pDescr;


                     148 ;127: 	float cft;


                     149 ;128: 	int maxValue;


                     150 ;129: 	int minValue;


                     151 ;130: };


                     152 ;131: 


                     153 ;132: typedef struct OSCInfoBool OSCInfoBool;


                     154 ;133: struct OSCInfoBool


                     155 ;134: {


                     156 ;135: 	OscDescrBool *pDescr;


                     157 ;136: };


                     158 ;137: //! класс


                     159 ;138: struct  OSCInfoStruct


                     160 ;139: {


                     161 ;140: 	// должны идти первыми


                     162 ;141: 	union 


                     163 ;142: 	{


                     164 ;143: 		OSCInfoStruct3 v3;


                     165 ;144: 		OSCInfoStruct4 v4;


                     166 ;145: 	};


                     167 ;146: 	IOSCInfo *iface;


                     168 ;147: 	OscWriteBuffer wbContent;


                     169 ;148: 	OscWriteBuffer wbFrame;


                     170 ;149: 	OSCInfoAnalog *pAnalog;


                     171 ;150: 	OSCInfoBool *pBool;


                     172 ;151: 	// количество фреймов в осцилограмме, включая предысторию



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                     173 ;152: 	unsigned int frameCount; 


                     174 ;153: 	unsigned int frameSize;


                     175 ;154: 


                     176 ;155: 	unsigned int firstFrameOffset;


                     177 ;156: };


                     178 ;157: 


                     179 ;158: 


                     180 ;159: static unsigned long getUTCDate(OSCInfoStruct *oscInfo)


                     181 ;160: {


                     182 ;161: 	return oscInfo->v3.time;


                     183 ;162: }


                     184 ;163: static unsigned long getDateMS(OSCInfoStruct *oscInfo)


                     185 ;164: {


                     186 ;165: 	return oscInfo->v3.time_ms;


                     187 ;166: }


                     188 ;167: static unsigned long getOscVersion(OSCInfoStruct *oscInfo)


                     189 ;168: {


                     190 ;169: 	return oscInfo->v3.oscVersion;


                     191 ;170: }


                     192 ;171: static unsigned long getADCClkFreq(OSCInfoStruct *oscInfo)


                     193 ;172: {


                     194 ;173: 	return oscInfo->v3.adcClkFreq;


                     195 ;174: }


                     196 ;175: static unsigned long getPrehistFrameCount3(OSCInfoStruct *oscInfo)


                     197 ;176: {


                     198 ;177: 	return oscInfo->v3.prehistCount;


                     199 ;178: }


                     200 ;179: 


                     201 ;180: static unsigned long getPrehistFirstFrameNum3(OSCInfoStruct *oscInfo)


                     202 ;181: {


                     203 ;182: 	return oscInfo->v3.prehistStart;


                     204 ;183: }


                     205 ;184: 


                     206 ;185: static unsigned long getPrehistFrameCount4(OSCInfoStruct *oscInfo)


                     207 ;186: {


                     208 ;187: 	return oscInfo->v4.prehist_count;


                     209 ;188: }


                     210 ;189: 


                     211 ;190: static unsigned long getPrehistFirstFrameNum4(OSCInfoStruct *oscInfo)


                     212 ;191: {


                     213 ;192: 	return oscInfo->v4.prehistStart;


                     214 ;193: }


                     215 ;194: 


                     216 ;195: 


                     217 ;196: static unsigned long getPointPerFrameCount3(OSCInfoStruct *oscInfo)


                     218 ;197: {


                     219 ;198: 	return oscInfo->v3.pointPerFrame;


                     220 ;199: }


                     221 ;200: 


                     222 ;201: static unsigned long getPointPerFrameCount4(OSCInfoStruct *oscInfo)


                     223 ;202: {


                     224 ;203: 	return oscInfo->v4.pointPerFrame;


                     225 ;204: }


                     226 ;205: 


                     227 ;206: 


                     228 ;207: static unsigned long getAnalogInCount3(OSCInfoStruct *oscInfo)


                     229 ;208: {


                     230 ;209: 	return oscInfo->v3.ainCount;


                     231 ;210: }


                     232 ;211: static unsigned long getDigInCount3(OSCInfoStruct *oscInfo)


                     233 ;212: {



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                     234 ;213: 	return oscInfo->v3.dinCount;


                     235 ;214: }


                     236 ;215: 


                     237 ;216: static unsigned long getAnalogInCount4(OSCInfoStruct *oscInfo)


                     238 ;217: {


                     239 ;218: 	return oscInfo->v4.ainCount;


                     240 ;219: }


                     241 ;220: static unsigned long getDigInCount4(OSCInfoStruct *oscInfo)


                     242 ;221: {


                     243 ;222: 	return oscInfo->v4.dinCount;


                     244 ;223: }


                     245 ;224: 


                     246 ;225: static unsigned long getHeaderSize3(OSCInfoStruct *oscInfo)


                     247 

                     248 ;228: }


                     249 

                     250 ;229: static unsigned long getHeaderSize4(OSCInfoStruct *oscInfo)


                     251 

                     252 ;232: }


                     253 

                     254 ;233: 


                     255 ;234: 


                     256 ;235: static int getADCSampleSize3(OSCInfoStruct *oscInfo)


                     257 

                     258 ;238: }


                     259 

                     260 ;239: static int getADCFractionSize3(OSCInfoStruct *oscInfo)


                     261 

                     262 ;242: }


                     263 

                     264 ;243: 


                     265 ;244: static int getADCSampleSize4(OSCInfoStruct *oscInfo)


                     266 ;245: {


                     267 ;246: 	int adcSampleSize = oscInfo->v4.adcSampleSize;


                     268 ;247: 	if (adcSampleSize == 0)


                     269 ;248: 	{


                     270 ;249: 		//На случай кривого формата (коммент из монитора)


                     271 ;250: 		adcSampleSize = 4;


                     272 ;251: 	}


                     273 ;252: 	return adcSampleSize;


                     274 ;253: }


                     275 ;254: static int getADCFractionSize4(OSCInfoStruct *oscInfo)


                     276 ;255: {


                     277 ;256: 	return oscInfo->v4.adcFractionSize;


                     278 ;257: }


                     279 ;258: 


                     280 ;259: static int getAnalogContentIdSize(OSCInfoStruct *oscInfo)


                     281 

                     282 ;262: }


                     283 

                     284 ;263: static int getBoolContentIdSize(OSCInfoStruct *oscInfo)


                     285 

                     286 ;266: }


                     287 

                     288 ;267: static unsigned int getOscSize(OSCInfoStruct *oscInfo)


                     289 ;268: {


                     290 ;269: 	return oscInfo->v3.size;


                     291 ;270: }


                     292 ;271: // инициализация интерфейсов


                     293 ;272: IOSCInfo oscInfoV3 = 


                     294 ;273: {



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                     295 ;274: 	getUTCDate,


                     296 ;275: 	getDateMS,


                     297 ;276: 	getOscVersion,


                     298 ;277: 	getADCClkFreq,


                     299 ;278: 	getPrehistFrameCount3,


                     300 ;279: 	getPrehistFirstFrameNum3,


                     301 ;280: 	getPointPerFrameCount3,


                     302 ;281: 	getAnalogInCount3,


                     303 ;282: 	getDigInCount3,


                     304 ;283: 	getOscSize,


                     305 ;284: 	getHeaderSize3,


                     306 ;285: 	getADCSampleSize3,


                     307 ;286: 	getADCFractionSize3,


                     308 ;287: };


                     309 ;288: IOSCInfo oscInfoV4 =


                     310 ;289: {


                     311 ;290: 	getUTCDate,


                     312 ;291: 	getDateMS,


                     313 ;292: 	getOscVersion,


                     314 ;293: 	getADCClkFreq,


                     315 ;294: 	getPrehistFrameCount4,


                     316 ;295: 	getPrehistFirstFrameNum4,


                     317 ;296: 	getPointPerFrameCount4,


                     318 ;297: 	getAnalogInCount4,


                     319 ;298: 	getDigInCount4,


                     320 ;299: 	getOscSize,


                     321 ;300: 	getHeaderSize4,


                     322 ;301: 	getADCSampleSize4,


                     323 ;302: 	getADCFractionSize4


                     324 ;303: };


                     325 ;304: 


                     326 ;305: bool OSCInfo_init(void)


                     327 ;306: {	


                     328 ;307: 	#pragma alignvar (4)


                     329 ;308: 	static unsigned char oscHeader[OSC_HEADER_SIZE];


                     330 ;309: 	if (!OscWriteBuffer_attach(&oscHeaderBuf, oscHeader, OSC_HEADER_SIZE))


                     331 ;310: 	{


                     332 ;311: 		return false;


                     333 ;312: 	}


                     334 ;313: 


                     335 ;314: 	CriticalSection_Init(&csHeaderBuf);


                     336 ;315: 	return true;


                     337 ;316: }


                     338 ;317: 


                     339 ;318: static unsigned int getAdcPeriodSize(OSCInfoStruct *oscInfo)


                     340 

                     341 ;321: }


                     342 

                     343 ;322: 


                     344 ;323: static unsigned int getFrameSize(OSCInfoStruct *oscInfo)


                     345 ;324: {


                     346 ;325: 	int pointsPerFrame = OSCInfo_getPointPerFrameCount(oscInfo);


                     347 ;326: 	int analogCount = OSCInfo_getAnalogCount(oscInfo);


                     348 ;327: 	int boolCount = OSCInfo_getBoolCount(oscInfo);


                     349 ;328: 	int adcPeriodSize = getAdcPeriodSize(oscInfo);


                     350 ;329: 	int adcSampleSize = OSCInfo_getADCSampleSize(oscInfo);


                     351 ;330: 	if (adcSampleSize == 0)


                     352 ;331: 	{


                     353 ;332: 		return 0;


                     354 ;333: 	}


                     355 ;334: 	if (pointsPerFrame == 0)



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                     356 ;335: 	{


                     357 ;336: 		return 0;


                     358 ;337: 	}


                     359 ;338: 	if (adcPeriodSize == 0)


                     360 ;339: 	{


                     361 ;340: 		return 0;


                     362 ;341: 	}


                     363 ;342: 


                     364 ;343: 


                     365 ;344: 	return adcPeriodSize +


                     366 ;345: 		pointsPerFrame * adcSampleSize*analogCount


                     367 ;346: 		+ 1 * boolCount;


                     368 ;347: }


                     369 ;348: 


                     370 ;349: static unsigned int getContentInfoSize(OSCInfoStruct *oscInfo)


                     371 ;350: {


                     372 ;351: 	int analogCount;


                     373 ;352: 	int boolCount;


                     374 ;353: 	analogCount = OSCInfo_getAnalogCount(oscInfo);


                     375 ;354: 	boolCount = OSCInfo_getBoolCount(oscInfo);


                     376 ;355: 	


                     377 ;356: 	return analogCount*getAnalogContentIdSize(oscInfo)


                     378 ;357: 		+ boolCount * getBoolContentIdSize(oscInfo);


                     379 ;358: 


                     380 ;359: }


                     381 ;360: static unsigned int getFrameCount(OSCInfoStruct *oscInfo)


                     382 

                     383 ;376: }


                     384 

                     385 ;377: OSCInfoStruct* OSCInfo_create(OscWriteBuffer *headBufferView)


                     386 ;378: {


                     387 ;379: 	OSCInfoStruct *oscHeader = (OSCInfoStruct*)OscWriteBuffer_data(headBufferView);


                     388 ;380: 	OSCInfoStruct *oscInfo;


                     389 ;381: 	IOSCInfo *iface;


                     390 ;382: 	int contentSize;


                     391 ;383: 	int analogCount;


                     392 ;384: 	int boolCount;


                     393 ;385: 	


                     394 ;386: 	if (oscHeader->v3.oscVersion == 3 )


                     395 ;387: 	{


                     396 ;388: 		iface = &oscInfoV3;


                     397 ;389: 	}


                     398 ;390: 	else if (oscHeader->v4.oscVersion == 4)


                     399 ;391: 	{


                     400 ;392: 		iface = &oscInfoV4;


                     401 ;393: 	}


                     402 ;394: 	else


                     403 ;395: 	{


                     404 ;396: 		return NULL;


                     405 ;397: 	}


                     406 ;398: 


                     407 ;399: 	// память


                     408 ;400: 	oscInfo = OscFiles_malloc(sizeof(OSCInfoStruct));


                     409 ;401: 	if (!oscInfo)


                     410 ;402: 	{


                     411 ;403: 		return NULL;


                     412 ;404: 	}


                     413 ;405: 	


                     414 ;406: 	memset(oscInfo, 0, sizeof(OSCInfoStruct));


                     415 ;407: 


                     416 ;408: 	// инициализация заголовка



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                     417 ;409: 	memcpy(oscInfo, oscHeader, OSC_HEADER_SIZE);


                     418 ;410: 	// интерфейс и приватные поля


                     419 ;411: 	oscInfo->iface = iface;


                     420 ;412: 	oscInfo->pAnalog = NULL;


                     421 ;413: 	oscInfo->pBool = NULL;


                     422 ;414: 


                     423 ;415: 


                     424 ;416: 	analogCount = OSCInfo_getAnalogCount(oscInfo);


                     425 ;417: 	// не может быть без аналоговых каналов


                     426 ;418: 	if (analogCount == 0)


                     427 ;419: 	{


                     428 ;420: 		OSCInfo_destroy(oscInfo);


                     429 ;421: 		return NULL;


                     430 ;422: 	}


                     431 ;423: 	oscInfo->pAnalog = OscFiles_malloc(analogCount * sizeof(OSCInfoAnalog));


                     432 ;424: 	if (!oscInfo->pAnalog)


                     433 ;425: 	{


                     434 ;426: 		OSCInfo_destroy(oscInfo);


                     435 ;427: 		return NULL;


                     436 ;428: 	}


                     437 ;429: 


                     438 ;430: 


                     439 ;431: 	boolCount = OSCInfo_getBoolCount(oscInfo);


                     440 ;432: 	if (boolCount != 0)


                     441 ;433: 	{


                     442 ;434: 		oscInfo->pBool = OscFiles_malloc(boolCount * sizeof(OSCInfoBool));


                     443 ;435: 		if (!oscInfo->pBool)


                     444 ;436: 		{


                     445 ;437: 			OSCInfo_destroy(oscInfo);


                     446 ;438: 			return NULL;


                     447 ;439: 		}


                     448 ;440: 	}


                     449 ;441: 


                     450 ;442: 


                     451 ;443: 	contentSize = getContentInfoSize(oscInfo);


                     452 ;444: 	if (contentSize == 0)


                     453 ;445: 	{


                     454 ;446: 		OSCInfo_destroy(oscInfo);


                     455 ;447: 		return NULL;


                     456 ;448: 	}


                     457 ;449: 


                     458 ;450: 	// инициализация количества кадров


                     459 ;451: 	oscInfo->frameCount = getFrameCount(oscInfo);


                     460 ;452: 	if (oscInfo->frameCount == 0)


                     461 ;453: 	{


                     462 ;454: 		OSCInfo_destroy(oscInfo);


                     463 ;455: 		return FALSE;


                     464 ;456: 	}


                     465 ;457: 


                     466 ;458: 	oscInfo->firstFrameOffset = OSCInfo_getHeaderSize(oscInfo) +


                     467 ;459: 		contentSize;


                     468 ;460: 


                     469 ;461: 	// буфер под состав осцилограммы


                     470 ;462: 	if (!OscWriteBuffer_create(&oscInfo->wbContent, contentSize))


                     471 ;463: 	{


                     472 ;464: 		OSCInfo_destroy(oscInfo);


                     473 ;465: 		return NULL;


                     474 ;466: 	}


                     475 ;467: 	


                     476 ;468: 	// буфер под фрейм


                     477 ;469: 	oscInfo->frameSize = getFrameSize(oscInfo);



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                     478 ;470: 	if (oscInfo->frameSize == 0)


                     479 ;471: 	{


                     480 ;472: 		OSCInfo_destroy(oscInfo);


                     481 ;473: 		return NULL;


                     482 ;474: 	}


                     483 ;475: 


                     484 ;476: 	if (!OscWriteBuffer_create(&oscInfo->wbFrame, oscInfo->frameSize))


                     485 ;477: 	{


                     486 ;478: 		OSCInfo_destroy(oscInfo);


                     487 ;479: 		return NULL;


                     488 ;480: 	}


                     489 ;481: 


                     490 ;482: 	return oscInfo;


                     491 ;483: }


                     492 ;484: 


                     493 ;485: void OSCInfo_destroy(OSCInfoStruct *oscInfo)


                     494 ;486: {


                     495 ;487: 	if (oscInfo->pAnalog)


                     496 ;488: 	{


                     497 ;489: 		OscFiles_free(oscInfo->pAnalog);


                     498 ;490: 	}


                     499 ;491: 	if (oscInfo->pBool)


                     500 ;492: 	{


                     501 ;493: 		OscFiles_free(oscInfo->pBool);


                     502 ;494: 	}


                     503 ;495: 


                     504 ;496: 	OscWriteBuffer_destroy(&oscInfo->wbContent);


                     505 ;497: 	OscWriteBuffer_destroy(&oscInfo->wbFrame);


                     506 ;498: 


                     507 ;499: 	OscFiles_free(oscInfo);


                     508 ;500: }


                     509 ;501: 


                     510 ;502: OscWriteBuffer * OSCInfo_lockHeaderBuf(void)


                     511 ;503: {


                     512 ;504: 	CriticalSection_Lock(&csHeaderBuf);


                     513 ;505: 	return &oscHeaderBuf;


                     514 ;506: }


                     515 ;507: 


                     516 ;508: void OSCInfo_unlockHeaderBuf(void)


                     517 ;509: {


                     518 ;510: 	CriticalSection_Unlock(&csHeaderBuf);


                     519 ;511: }


                     520 ;512: 


                     521 ;513: unsigned long OSCInfo_getUTCDate(OSCInfoStruct *oscInfo)


                     522 ;514: {


                     523 ;515: 	return oscInfo->iface->getUTCDate(oscInfo);


                     524 ;516: }


                     525 ;517: 


                     526 ;518: unsigned long OSCInfo_getDateMS(OSCInfoStruct *oscInfo)


                     527 ;519: {


                     528 ;520: 	return oscInfo->iface->getDateMS(oscInfo);


                     529 ;521: }


                     530 ;522: 


                     531 ;523: unsigned long OSCInfo_getOscVersion(OSCInfoStruct *oscInfo)


                     532 ;524: {


                     533 ;525: 	return oscInfo->iface->getOscVersion(oscInfo);


                     534 ;526: }


                     535 ;527: 


                     536 ;528: unsigned long OSCInfo_getADCClkFreq(OSCInfoStruct *oscInfo)


                     537 ;529: {


                     538 ;530: 	return oscInfo->iface->getADCClkFreq(oscInfo);



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                     539 ;531: }


                     540 ;532: 


                     541 ;533: unsigned long OSCInfo_getPrehistFrameCount(OSCInfoStruct *oscInfo)


                     542 ;534: {


                     543 ;535: 	return oscInfo->iface->getPrehistFrameCount(oscInfo);


                     544 ;536: }


                     545 ;537: 


                     546 ;538: unsigned long OSCInfo_getPrehistFirstFrameNum(OSCInfoStruct *oscInfo)


                     547 ;539: {


                     548 ;540: 	return oscInfo->iface->getPrehistFirstFrameNum(oscInfo);


                     549 ;541: }


                     550 ;542: 


                     551 ;543: unsigned long OSCInfo_getPointPerFrameCount(OSCInfoStruct *oscInfo)


                     552 ;544: {


                     553 ;545: 	return oscInfo->iface->getPointPerFrameCount(oscInfo);


                     554 ;546: }


                     555 ;547: 


                     556 ;548: unsigned long OSCInfo_getAnalogCount(OSCInfoStruct *oscInfo)


                     557 ;549: {


                     558 ;550: 	return oscInfo->iface->getAnalogInCount(oscInfo);


                     559 ;551: }


                     560 ;552: 


                     561 ;553: unsigned long OSCInfo_getBoolCount(OSCInfoStruct *oscInfo)


                     562 ;554: {


                     563 ;555: 	return oscInfo->iface->getDigInCount(oscInfo);


                     564 ;556: }


                     565 ;557: 


                     566 ;558: unsigned long OSCInfo_getHeaderSize(OSCInfoStruct *oscInfo)


                     567 ;559: {


                     568 ;560: 	return oscInfo->iface->getHeaderSize(oscInfo);


                     569 ;561: }


                     570 ;562: 


                     571 ;563: unsigned long OSCInfo_getFrameCount(OSCInfoStruct *oscInfo)


                     572 ;564: {


                     573 ;565: 	return oscInfo->frameCount;


                     574 ;566: }


                     575 ;567: 


                     576 ;568: unsigned int OSCInfo_getFrameOffset(OSCInfoStruct *oscInfo, unsigned int frameNum)


                     577 ;569: {


                     578 ;570: 	unsigned int prehistFrameCount = OSCInfo_getPrehistFrameCount(oscInfo);


                     579 ;571: 	// если кадр из предыстории


                     580 ;572: 	if (frameNum < prehistFrameCount)


                     581 ;573: 	{


                     582 ;574: 		int firstFrameNum = OSCInfo_getPrehistFirstFrameNum(oscInfo);


                     583 ;575: 		unsigned int relativeFrameNum = firstFrameNum + frameNum;


                     584 ;576: 


                     585 ;577: 		if (relativeFrameNum >= prehistFrameCount)


                     586 ;578: 		{


                     587 ;579: 			relativeFrameNum = relativeFrameNum - prehistFrameCount;


                     588 ;580: 		}


                     589 ;581: 		return oscInfo->firstFrameOffset + relativeFrameNum * oscInfo->frameSize;


                     590 ;582: 	}


                     591 ;583: 	else


                     592 ;584: 	{


                     593 ;585: 		return oscInfo->firstFrameOffset + frameNum * oscInfo->frameSize;


                     594 ;586: 	}


                     595 ;587: }


                     596 ;588: 


                     597 ;589: int OSCInfo_getADCSampleSize(OSCInfoStruct *oscInfo)


                     598 ;590: {


                     599 ;591: 	return oscInfo->iface->getADCSampleSize(oscInfo);



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                     600 ;592: }


                     601 ;593: 


                     602 ;594: int OSCInfo_getADCFractionSize(OSCInfoStruct *oscInfo)


                     603 ;595: {


                     604 ;596: 	return oscInfo->iface->getADCFractionSize(oscInfo);


                     605 ;597: }


                     606 ;598: 


                     607 ;599: int OSCInfo_getOscContentOffset(OSCInfoStruct *oscInfo)


                     608 ;600: {


                     609 ;601: 	return OSCInfo_getHeaderSize(oscInfo);


                     610 ;602: }


                     611 ;603: 


                     612 ;604: OscWriteBuffer * OSCInfo_getBufferContent(OSCInfoStruct *oscInfo)


                     613 ;605: {


                     614 ;606: 	return &oscInfo->wbContent;


                     615 ;607: }


                     616 ;608: 


                     617 ;609: OscWriteBuffer * OSCInfo_getFrameBuffer(OSCInfoStruct *oscInfo)


                     618 ;610: {


                     619 ;611: 	return &oscInfo->wbFrame;


                     620 ;612: }


                     621 ;613: 


                     622 ;614: //! период текущего фрейма


                     623 ;615: int OSCFrame_getADCPeriod(OSCInfoStruct *oscInfo)


                     624 ;616: {


                     625 ;617: 	unsigned int result;


                     626 ;618: 	unsigned char *frame = OscWriteBuffer_data(&oscInfo->wbFrame);


                     627 ;619: 	result = frame[0];


                     628 ;620: 	result += frame[1] << 8;


                     629 ;621: 	result += frame[2] << 16;


                     630 ;622: 	result += frame[3] << 24;


                     631 ;623: 	return result;


                     632 ;624: }


                     633 ;625: static int extractWithSignExtension(void* pData, size_t byteCount)


                     634 

                     635 ;642: }


                     636 

                     637 ;643: 


                     638 ;644: 


                     639 ;645: #define MAX_COMTRADE_SAMPLE_BIT_WIDTH 20


                     640 ;646: 


                     641 ;647: static void updateADCMaxAbsValue(OSCInfoStruct *oscInfo, unsigned int analogNum)


                     642 

                     643 ;660: }


                     644 

                     645 ;661: 


                     646 ;662: static void updateAnalogCft(OSCInfoStruct *oscInfo, unsigned int analogNum)


                     647 

                     648 ;673: }


                     649 

                     650 ;674: 


                     651 ;675: int OSCFrame_getAnalogValue(OSCInfoStruct *oscInfo, unsigned int pointNum, unsigned int analogNum)


                     652 ;676: {


                     653 ;677: 	//	OSCInfoAnalog *pAnalog = &oscInfo->pAnalog[analogNum];


                     654 ;678: 	//	int analogCount = OSCInfo_getAnalogCount(oscInfo);


                     655 ;679: 	unsigned char *frame = OscWriteBuffer_data(&oscInfo->wbFrame);


                     656 ;680: 	unsigned char *value;


                     657 ;681: 	int result = 0;


                     658 ;682: 	int adcSampleSize = OSCInfo_getADCSampleSize(oscInfo);


                     659 ;683: 	int adcSampleCount = OSCInfo_getPointPerFrameCount(oscInfo);


                     660 ;684: 



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                     661 ;685: 		


                     662 ;686: 	// смещение на 0 аналоговое значение


                     663 ;687: 	value = frame + getAdcPeriodSize(oscInfo);


                     664 ;688: 	// значение с учетом номер отсчета


                     665 ;689: 	value += analogNum * adcSampleCount * adcSampleSize;


                     666 ;690: 	// значение с учетом номера канала


                     667 ;691: 	value += adcSampleSize * pointNum;


                     668 ;692: 	// память в знаковое значение


                     669 ;693: 	result = extractWithSignExtension(value, adcSampleSize);


                     670 ;694: 


                     671 ;695: 	return result;


                     672 ;696: }


                     673 ;697: 


                     674 ;698: 


                     675 ;699: int OSCFrame_getBoolValue(OSCInfoStruct *oscInfo, int sampleNum, int boolNum)


                     676 ;700: {


                     677 ;701: 	unsigned char *frame = OscWriteBuffer_data(&oscInfo->wbFrame);


                     678 ;702: 	unsigned char *value;


                     679 ;703: 	int analogCount = OSCInfo_getAnalogCount(oscInfo);


                     680 ;704: 	int adcSampleSize = OSCInfo_getADCSampleSize(oscInfo);


                     681 ;705: 	int pointPerFrame = OSCInfo_getPointPerFrameCount(oscInfo);


                     682 ;706: 


                     683 ;707: 	value = frame + getAdcPeriodSize(oscInfo) + pointPerFrame * analogCount * adcSampleSize;


                     684 ;708: 	value += boolNum;


                     685 ;709: 	return *value;


                     686 ;710: }


                     687 ;711: 


                     688 ;712: 


                     689 ;713: bool OSCFrame_getTick(OSCInfoStruct *oscInfo, double *tick)


                     690 ;714: {


                     691 ;715: 	unsigned char *frame = OscWriteBuffer_data(&oscInfo->wbFrame);


                     692 ;716: 	unsigned long adcTick;


                     693 ;717: 	double mainFreq = OSCInfo_getADCClkFreq(oscInfo);


                     694 ;718: 	double result ;


                     695 ;719: 	memcpy(&adcTick, frame, getAdcPeriodSize(oscInfo));


                     696 ;720: 	if (adcTick != 0)


                     697 ;721: 	{


                     698 ;722: 		result = 1000000 / (mainFreq / adcTick);


                     699 ;723: 		*tick = result;


                     700 ;724: 		return true;


                     701 ;725: 	}


                     702 ;726: 	{


                     703 ;727: 		return false;


                     704 ;728: 	}


                     705 ;729: }


                     706 ;730: 


                     707 ;731: bool OSCFrame_getFreq(OSCInfoStruct *oscInfo, float *freq)


                     708 ;732: {


                     709 ;733: 	unsigned char *frame = OscWriteBuffer_data(&oscInfo->wbFrame);


                     710 ;734: 	unsigned long adcFreq;


                     711 ;735: 	double mainFreq = OSCInfo_getADCClkFreq(oscInfo);


                     712 ;736: 	double result;


                     713 ;737: 	memcpy(&adcFreq, frame, getAdcPeriodSize(oscInfo));


                     714 ;738: 	if (adcFreq != 0)


                     715 ;739: 	{


                     716 ;740: 		result = mainFreq / adcFreq;


                     717 ;741: 		*freq = (float)result;


                     718 ;742: 		return true;


                     719 ;743: 	}


                     720 ;744: 	else


                     721 ;745: 	{



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                     722 ;746: 		return false;


                     723 ;747: 	}


                     724 ;748: }


                     725 ;749: 


                     726 ;750: //! указатель состав аналоговых каналов


                     727 ;751: static void *getAnalogContentPtr(OSCInfoStruct *oscInfo)


                     728 

                     729 ;756: }


                     730 

                     731 ;757: //! указатель на состав дискретных каналов


                     732 ;758: static void *getBoolContentPtr(OSCInfoStruct *oscInfo)


                     733 

                     734 ;763: }


                     735 

                     736 ;764: 


                     737 ;765: static bool boolInit(OSCInfoStruct *oscInfo)


                     738 

                     739 ;785: }


                     740 

                     741 ;786: 


                     742 ;787: static bool analogInit(OSCInfoStruct *oscInfo)


                     743 

                     744 ;807: }


                     745 

                     746 	.text

                     747 	.align	4

                     748 getUTCDate:

00000000 e5900014    749 	ldr	r0,[r0,20]

00000004 e12fff1e*   750 	ret	

                     751 	.endf	getUTCDate

                     752 	.align	4

                     753 

                     754 ;oscInfo	r0	param

                     755 

                     756 	.section ".bss","awb"

                     757 .L305:

                     758 	.data

                     759 	.text

                     760 

                     761 

                     762 	.align	4

                     763 	.align	4

                     764 getDateMS:

00000008 e1d001b8    765 	ldrh	r0,[r0,24]

0000000c e12fff1e*   766 	ret	

                     767 	.endf	getDateMS

                     768 	.align	4

                     769 

                     770 ;oscInfo	r0	param

                     771 

                     772 	.section ".bss","awb"

                     773 .L337:

                     774 	.data

                     775 	.text

                     776 

                     777 

                     778 	.align	4

                     779 	.align	4

                     780 getOscVersion:

00000010 e5d0001b    781 	ldrb	r0,[r0,27]

00000014 e12fff1e*   782 	ret	


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                     783 	.endf	getOscVersion

                     784 	.align	4

                     785 

                     786 ;oscInfo	r0	param

                     787 

                     788 	.section ".bss","awb"

                     789 .L369:

                     790 	.data

                     791 	.text

                     792 

                     793 

                     794 	.align	4

                     795 	.align	4

                     796 getADCClkFreq:

00000018 e5900010    797 	ldr	r0,[r0,16]

0000001c e12fff1e*   798 	ret	

                     799 	.endf	getADCClkFreq

                     800 	.align	4

                     801 

                     802 ;oscInfo	r0	param

                     803 

                     804 	.section ".bss","awb"

                     805 .L401:

                     806 	.data

                     807 	.text

                     808 

                     809 

                     810 	.align	4

                     811 	.align	4

                     812 getPrehistFrameCount3:

00000020 e5d0000a    813 	ldrb	r0,[r0,10]

00000024 e12fff1e*   814 	ret	

                     815 	.endf	getPrehistFrameCount3

                     816 	.align	4

                     817 

                     818 ;oscInfo	r0	param

                     819 

                     820 	.section ".bss","awb"

                     821 .L433:

                     822 	.data

                     823 	.text

                     824 

                     825 

                     826 	.align	4

                     827 	.align	4

                     828 getPrehistFirstFrameNum3:

00000028 e5d0000b    829 	ldrb	r0,[r0,11]

0000002c e12fff1e*   830 	ret	

                     831 	.endf	getPrehistFirstFrameNum3

                     832 	.align	4

                     833 

                     834 ;oscInfo	r0	param

                     835 

                     836 	.section ".bss","awb"

                     837 .L465:

                     838 	.data

                     839 	.text

                     840 

                     841 

                     842 	.align	4

                     843 	.align	4


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                     844 getPrehistFrameCount4:

00000030 e590001c    845 	ldr	r0,[r0,28]

00000034 e12fff1e*   846 	ret	

                     847 	.endf	getPrehistFrameCount4

                     848 	.align	4

                     849 

                     850 ;oscInfo	r0	param

                     851 

                     852 	.section ".bss","awb"

                     853 .L497:

                     854 	.data

                     855 	.text

                     856 

                     857 

                     858 	.align	4

                     859 	.align	4

                     860 getPrehistFirstFrameNum4:

00000038 e5900020    861 	ldr	r0,[r0,32]

0000003c e12fff1e*   862 	ret	

                     863 	.endf	getPrehistFirstFrameNum4

                     864 	.align	4

                     865 

                     866 ;oscInfo	r0	param

                     867 

                     868 	.section ".bss","awb"

                     869 .L529:

                     870 	.data

                     871 	.text

                     872 

                     873 

                     874 	.align	4

                     875 	.align	4

                     876 getPointPerFrameCount3:

00000040 e1d001bc    877 	ldrh	r0,[r0,28]

00000044 e12fff1e*   878 	ret	

                     879 	.endf	getPointPerFrameCount3

                     880 	.align	4

                     881 

                     882 ;oscInfo	r0	param

                     883 

                     884 	.section ".bss","awb"

                     885 .L561:

                     886 	.data

                     887 	.text

                     888 

                     889 

                     890 	.align	4

                     891 	.align	4

                     892 getPointPerFrameCount4:

00000048 e1d002b4    893 	ldrh	r0,[r0,36]

0000004c e12fff1e*   894 	ret	

                     895 	.endf	getPointPerFrameCount4

                     896 	.align	4

                     897 

                     898 ;oscInfo	r0	param

                     899 

                     900 	.section ".bss","awb"

                     901 .L593:

                     902 	.data

                     903 	.text

                     904 


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                     905 

                     906 	.align	4

                     907 	.align	4

                     908 getAnalogInCount3:

00000050 e1d002b0    909 	ldrh	r0,[r0,32]

00000054 e12fff1e*   910 	ret	

                     911 	.endf	getAnalogInCount3

                     912 	.align	4

                     913 

                     914 ;oscInfo	r0	param

                     915 

                     916 	.section ".bss","awb"

                     917 .L625:

                     918 	.data

                     919 	.text

                     920 

                     921 

                     922 	.align	4

                     923 	.align	4

                     924 getDigInCount3:

00000058 e1d002b2    925 	ldrh	r0,[r0,34]

0000005c e12fff1e*   926 	ret	

                     927 	.endf	getDigInCount3

                     928 	.align	4

                     929 

                     930 ;oscInfo	r0	param

                     931 

                     932 	.section ".bss","awb"

                     933 .L657:

                     934 	.data

                     935 	.text

                     936 

                     937 

                     938 	.align	4

                     939 	.align	4

                     940 getAnalogInCount4:

00000060 e1d002b8    941 	ldrh	r0,[r0,40]

00000064 e12fff1e*   942 	ret	

                     943 	.endf	getAnalogInCount4

                     944 	.align	4

                     945 

                     946 ;oscInfo	r0	param

                     947 

                     948 	.section ".bss","awb"

                     949 .L689:

                     950 	.data

                     951 	.text

                     952 

                     953 

                     954 	.align	4

                     955 	.align	4

                     956 getDigInCount4:

00000068 e1d002ba    957 	ldrh	r0,[r0,42]

0000006c e12fff1e*   958 	ret	

                     959 	.endf	getDigInCount4

                     960 	.align	4

                     961 

                     962 ;oscInfo	r0	param

                     963 

                     964 	.section ".bss","awb"

                     965 .L721:


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                     966 	.data

                     967 	.text

                     968 

                     969 

                     970 	.align	4

                     971 	.align	4

                     972 getADCSampleSize4:

00000070 e5d00026    973 	ldrb	r0,[r0,38]

00000074 e3500000    974 	cmp	r0,0

00000078 03a00004    975 	moveq	r0,4

0000007c e12fff1e*   976 	ret	

                     977 	.endf	getADCSampleSize4

                     978 	.align	4

                     979 ;adcSampleSize	r0	local

                     980 

                     981 ;oscInfo	r0	param

                     982 

                     983 	.section ".bss","awb"

                     984 .L761:

                     985 	.data

                     986 	.text

                     987 

                     988 

                     989 	.align	4

                     990 	.align	4

                     991 getADCFractionSize4:

00000080 e5d00027    992 	ldrb	r0,[r0,39]

00000084 e12fff1e*   993 	ret	

                     994 	.endf	getADCFractionSize4

                     995 	.align	4

                     996 

                     997 ;oscInfo	r0	param

                     998 

                     999 	.section ".bss","awb"

                    1000 .L801:

                    1001 	.data

                    1002 	.text

                    1003 

                    1004 

                    1005 	.align	4

                    1006 	.align	4

                    1007 getOscSize:

00000088 e5900000   1008 	ldr	r0,[r0]

0000008c e12fff1e*  1009 	ret	

                    1010 	.endf	getOscSize

                    1011 	.align	4

                    1012 

                    1013 ;oscInfo	r0	param

                    1014 

                    1015 	.section ".bss","awb"

                    1016 .L833:

                    1017 	.data

                    1018 	.text

                    1019 

                    1020 

                    1021 	.align	4

                    1022 	.align	4

                    1023 OSCInfo_init::

00000090 e92d4000   1024 	stmfd	[sp]!,{lr}

00000094 e59f1a74*  1025 	ldr	r1,.L900

00000098 e59f0a74*  1026 	ldr	r0,.L901


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
0000009c e3a0202c   1027 	mov	r2,44

000000a0 eb000000*  1028 	bl	OscWriteBuffer_attach

000000a4 e3500000   1029 	cmp	r0,0

000000a8 0a000002   1030 	beq	.L840

000000ac e59f0a64*  1031 	ldr	r0,.L902

000000b0 eb000000*  1032 	bl	CriticalSection_Init

000000b4 e3a00001   1033 	mov	r0,1

                    1034 .L840:

000000b8 e8bd8000   1035 	ldmfd	[sp]!,{pc}

                    1036 	.endf	OSCInfo_init

                    1037 	.align	4

                    1038 ;oscHeader	.L888	static

                    1039 

                    1040 	.section ".bss","awb"

                    1041 .L885:

00000000 00000000   1042 .L888:	.space	44

00000004 00000000 
00000008 00000000 
0000000c 00000000 
00000010 00000000 
00000014 00000000 
00000018 00000000 
0000001c 00000000 
00000020 00000000 
00000024 00000000 
00000028 00000000 
                    1043 	.data

                    1044 	.text

                    1045 

                    1046 

                    1047 	.align	4

                    1048 	.align	4

                    1049 getFrameSize:

000000bc e92d40f0   1050 	stmfd	[sp]!,{r4-r7,lr}

000000c0 e1a06000   1051 	mov	r6,r0

000000c4 eb0000bb*  1052 	bl	OSCInfo_getPointPerFrameCount

000000c8 e1a07000   1053 	mov	r7,r0

000000cc e1a00006   1054 	mov	r0,r6

000000d0 eb0000be*  1055 	bl	OSCInfo_getAnalogCount

000000d4 e1a04000   1056 	mov	r4,r0

000000d8 e1a00006   1057 	mov	r0,r6

000000dc eb0000c1*  1058 	bl	OSCInfo_getBoolCount

000000e0 e1a05000   1059 	mov	r5,r0

                    1060 ;319: {


                    1061 

                    1062 ;320: 	return 4;


                    1063 

000000e4 e1a00006   1064 	mov	r0,r6

000000e8 eb0000dd*  1065 	bl	OSCInfo_getADCSampleSize

000000ec e3500000   1066 	cmp	r0,0

000000f0 13570000   1067 	cmpne	r7,0

000000f4 10000097   1068 	mulne	r0,r7,r0

000000f8 10205094   1069 	mlane	r0,r4,r0,r5

000000fc 12800004   1070 	addne	r0,r0,4

00000100 03a00000   1071 	moveq	r0,0

00000104 e8bd40f0   1072 	ldmfd	[sp]!,{r4-r7,lr}

00000108 e12fff1e*  1073 	ret	

                    1074 	.endf	getFrameSize

                    1075 	.align	4

                    1076 ;pointsPerFrame	r7	local

                    1077 ;analogCount	r4	local


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                    1078 ;boolCount	r5	local

                    1079 ;adcSampleSize	r0	local

                    1080 

                    1081 ;oscInfo	r6	param

                    1082 

                    1083 	.section ".bss","awb"

                    1084 .L976:

                    1085 	.data

                    1086 	.text

                    1087 

                    1088 

                    1089 	.align	4

                    1090 	.align	4

                    1091 getContentInfoSize:

0000010c e92d4030   1092 	stmfd	[sp]!,{r4-r5,lr}

00000110 e1a05000   1093 	mov	r5,r0

00000114 eb0000ad*  1094 	bl	OSCInfo_getAnalogCount

00000118 e1a04000   1095 	mov	r4,r0

0000011c e1a00005   1096 	mov	r0,r5

00000120 eb0000b0*  1097 	bl	OSCInfo_getBoolCount

                    1098 ;260: {


                    1099 

                    1100 ;261: 	return 2;


                    1101 

                    1102 ;264: {


                    1103 

                    1104 ;265: 	return 2;


                    1105 

00000124 e0800004   1106 	add	r0,r0,r4

00000128 e1a00080   1107 	mov	r0,r0 lsl 1

0000012c e8bd4030   1108 	ldmfd	[sp]!,{r4-r5,lr}

00000130 e12fff1e*  1109 	ret	

                    1110 	.endf	getContentInfoSize

                    1111 	.align	4

                    1112 ;analogCount	r4	local

                    1113 

                    1114 ;oscInfo	r5	param

                    1115 

                    1116 	.section ".bss","awb"

                    1117 .L1022:

                    1118 	.data

                    1119 	.text

                    1120 

                    1121 

                    1122 	.align	4

                    1123 	.align	4

                    1124 	.align	4

                    1125 OSCInfo_create::

00000134 e92d40f0   1126 	stmfd	[sp]!,{r4-r7,lr}

00000138 eb000000*  1127 	bl	OscWriteBuffer_data

0000013c e1a05000   1128 	mov	r5,r0

00000140 e5d5001b   1129 	ldrb	r0,[r5,27]

00000144 e3500003   1130 	cmp	r0,3

00000148 1a000005   1131 	bne	.L1031

0000014c e59f69c8*  1132 	ldr	r6,.L1382

00000150 e3a0006c   1133 	mov	r0,108

00000154 eb000000*  1134 	bl	OscFiles_malloc

00000158 e1b04000   1135 	movs	r4,r0

0000015c 0a000007   1136 	beq	.L1039

00000160 ea000008   1137 	b	.L1038

                    1138 .L1031:


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
00000164 e3500004   1139 	cmp	r0,4

00000168 1a000004   1140 	bne	.L1039

0000016c e59f69ac*  1141 	ldr	r6,.L1383

00000170 e3a0006c   1142 	mov	r0,108

00000174 eb000000*  1143 	bl	OscFiles_malloc

00000178 e1b04000   1144 	movs	r4,r0

0000017c 1a000001   1145 	bne	.L1038

                    1146 .L1039:

00000180 e3a00000   1147 	mov	r0,0

00000184 ea000050   1148 	b	.L1029

                    1149 .L1038:

00000188 e3a0206c   1150 	mov	r2,108

0000018c e3a01000   1151 	mov	r1,0

00000190 eb000000*  1152 	bl	memset

00000194 e1a01005   1153 	mov	r1,r5

00000198 e1a00004   1154 	mov	r0,r4

0000019c e3a0202c   1155 	mov	r2,44

000001a0 eb000000*  1156 	bl	memcpy

000001a4 e584602c   1157 	str	r6,[r4,44]

000001a8 e3a00000   1158 	mov	r0,0

000001ac e5840058   1159 	str	r0,[r4,88]

000001b0 e584005c   1160 	str	r0,[r4,92]

000001b4 e1a00004   1161 	mov	r0,r4

000001b8 eb000084*  1162 	bl	OSCInfo_getAnalogCount

000001bc e3500000   1163 	cmp	r0,0

000001c0 0a00003e   1164 	beq	.L1073

000001c4 e1a00200   1165 	mov	r0,r0 lsl 4

000001c8 eb000000*  1166 	bl	OscFiles_malloc

000001cc e5840058   1167 	str	r0,[r4,88]

000001d0 e3500000   1168 	cmp	r0,0

000001d4 0a000039   1169 	beq	.L1073

000001d8 e1a00004   1170 	mov	r0,r4

000001dc eb000081*  1171 	bl	OSCInfo_getBoolCount

000001e0 e3500000   1172 	cmp	r0,0

000001e4 0a000004   1173 	beq	.L1047

000001e8 e1a00100   1174 	mov	r0,r0 lsl 2

000001ec eb000000*  1175 	bl	OscFiles_malloc

000001f0 e584005c   1176 	str	r0,[r4,92]

000001f4 e3500000   1177 	cmp	r0,0

000001f8 0a000030   1178 	beq	.L1073

                    1179 .L1047:

000001fc e1a00004   1180 	mov	r0,r4

00000200 ebffffc1*  1181 	bl	getContentInfoSize

00000204 e1b05000   1182 	movs	r5,r0

00000208 0a00002c   1183 	beq	.L1073

                    1184 ;361: {


                    1185 

                    1186 ;362: 	int dataLen = oscInfo->iface->getOscSize(oscInfo);


                    1187 

0000020c e594002c   1188 	ldr	r0,[r4,44]

00000210 e590c024   1189 	ldr	r12,[r0,36]

00000214 e1a00004   1190 	mov	r0,r4

00000218 e1a0e00f   1191 	mov	lr,pc

0000021c e12fff1c*  1192 	bx	r12

00000220 e1a06000   1193 	mov	r6,r0

                    1194 ;363: 	int frameSize = getFrameSize(oscInfo);


                    1195 

00000224 e1a00004   1196 	mov	r0,r4

00000228 ebffffa3*  1197 	bl	getFrameSize

0000022c e1b07000   1198 	movs	r7,r0

                    1199 ;364: 	if (frameSize == 0)



                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                    1200 

00000230 0a000006   1201 	beq	.L1060

                    1202 ;365: 	{


                    1203 

                    1204 ;366: 		return 0;


                    1205 

                    1206 ;367: 	}


                    1207 ;368: 	dataLen -= OSCInfo_getHeaderSize(oscInfo);


                    1208 

00000234 e1a00004   1209 	mov	r0,r4

00000238 eb000070*  1210 	bl	OSCInfo_getHeaderSize

0000023c e0466000   1211 	sub	r6,r6,r0

                    1212 ;369: 	dataLen -= getContentInfoSize(oscInfo);


                    1213 

00000240 e1a00004   1214 	mov	r0,r4

00000244 ebffffb0*  1215 	bl	getContentInfoSize

00000248 e0566000   1216 	subs	r6,r6,r0

                    1217 ;370: 	if (dataLen < 0)


                    1218 

0000024c 5a000002   1219 	bpl	.L1061

                    1220 .L1060:

                    1221 ;371: 	{


                    1222 

                    1223 ;372: 		return 0;


                    1224 

00000250 e3a00000   1225 	mov	r0,0

00000254 e5840060   1226 	str	r0,[r4,96]

00000258 ea000018   1227 	b	.L1073

                    1228 .L1061:

                    1229 ;373: 	}


                    1230 ;374: 


                    1231 ;375: 	return dataLen / frameSize;


                    1232 

0000025c e1a01006   1233 	mov	r1,r6

00000260 e1a00007   1234 	mov	r0,r7

00000264 eb000000*  1235 	bl	__sdiv_32_32

00000268 e5840060   1236 	str	r0,[r4,96]

0000026c e3500000   1237 	cmp	r0,0

00000270 0a000012   1238 	beq	.L1073

00000274 e1a00004   1239 	mov	r0,r4

00000278 eb000060*  1240 	bl	OSCInfo_getHeaderSize

0000027c e1a01005   1241 	mov	r1,r5

00000280 e0850000   1242 	add	r0,r5,r0

00000284 e5840068   1243 	str	r0,[r4,104]

00000288 e2840030   1244 	add	r0,r4,48

0000028c eb000000*  1245 	bl	OscWriteBuffer_create

00000290 e3500000   1246 	cmp	r0,0

00000294 0a000009   1247 	beq	.L1073

00000298 e1a00004   1248 	mov	r0,r4

0000029c ebffff86*  1249 	bl	getFrameSize

000002a0 e1b01000   1250 	movs	r1,r0

000002a4 e5841064   1251 	str	r1,[r4,100]

000002a8 0a000004   1252 	beq	.L1073

000002ac e2840044   1253 	add	r0,r4,68

000002b0 eb000000*  1254 	bl	OscWriteBuffer_create

000002b4 e3500000   1255 	cmp	r0,0

000002b8 11a00004   1256 	movne	r0,r4

000002bc 1a000002   1257 	bne	.L1029

                    1258 .L1073:

000002c0 e1a00004   1259 	mov	r0,r4

000002c4 eb000001*  1260 	bl	OSCInfo_destroy


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
000002c8 e3a00000   1261 	mov	r0,0

                    1262 .L1029:

000002cc e8bd80f0   1263 	ldmfd	[sp]!,{r4-r7,pc}

                    1264 	.endf	OSCInfo_create

                    1265 	.align	4

                    1266 ;oscHeader	r5	local

                    1267 ;oscInfo	r4	local

                    1268 ;iface	r6	local

                    1269 ;contentSize	r5	local

                    1270 ;analogCount	r0	local

                    1271 ;boolCount	r0	local

                    1272 ;dataLen	r6	local

                    1273 ;frameSize	r7	local

                    1274 

                    1275 ;headBufferView	none	param

                    1276 

                    1277 	.section ".bss","awb"

                    1278 .L1320:

                    1279 	.data

                    1280 	.text

                    1281 

                    1282 

                    1283 	.align	4

                    1284 	.align	4

                    1285 OSCInfo_destroy::

000002d0 e92d4010   1286 	stmfd	[sp]!,{r4,lr}

000002d4 e1a04000   1287 	mov	r4,r0

000002d8 e5940058   1288 	ldr	r0,[r4,88]

000002dc e3500000   1289 	cmp	r0,0

000002e0 1b000000*  1290 	blne	OscFiles_free

000002e4 e594005c   1291 	ldr	r0,[r4,92]

000002e8 e3500000   1292 	cmp	r0,0

000002ec 1b000000*  1293 	blne	OscFiles_free

000002f0 e2840030   1294 	add	r0,r4,48

000002f4 eb000000*  1295 	bl	OscWriteBuffer_destroy

000002f8 e2840044   1296 	add	r0,r4,68

000002fc eb000000*  1297 	bl	OscWriteBuffer_destroy

00000300 e1a00004   1298 	mov	r0,r4

00000304 e8bd4010   1299 	ldmfd	[sp]!,{r4,lr}

00000308 ea000000*  1300 	b	OscFiles_free

                    1301 	.endf	OSCInfo_destroy

                    1302 	.align	4

                    1303 

                    1304 ;oscInfo	r4	param

                    1305 

                    1306 	.section ".bss","awb"

                    1307 .L1433:

                    1308 	.data

                    1309 	.text

                    1310 

                    1311 

                    1312 	.align	4

                    1313 	.align	4

                    1314 OSCInfo_lockHeaderBuf::

0000030c e92d4000   1315 	stmfd	[sp]!,{lr}

00000310 e59f0800*  1316 	ldr	r0,.L902

00000314 eb000000*  1317 	bl	CriticalSection_Lock

00000318 e59f07f4*  1318 	ldr	r0,.L901

0000031c e8bd8000   1319 	ldmfd	[sp]!,{pc}

                    1320 	.endf	OSCInfo_lockHeaderBuf

                    1321 	.align	4


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                    1322 

                    1323 	.section ".bss","awb"

                    1324 .L1470:

                    1325 	.data

                    1326 	.text

                    1327 

                    1328 

                    1329 	.align	4

                    1330 	.align	4

                    1331 OSCInfo_unlockHeaderBuf::

00000320 e59f07f0*  1332 	ldr	r0,.L902

00000324 ea000000*  1333 	b	CriticalSection_Unlock

                    1334 	.endf	OSCInfo_unlockHeaderBuf

                    1335 	.align	4

                    1336 

                    1337 	.section ".bss","awb"

                    1338 .L1502:

                    1339 	.data

                    1340 	.text

                    1341 

                    1342 

                    1343 	.align	4

                    1344 	.align	4

                    1345 OSCInfo_getUTCDate::

00000328 e92d4000   1346 	stmfd	[sp]!,{lr}

0000032c e590c02c   1347 	ldr	r12,[r0,44]

00000330 e59cc000   1348 	ldr	r12,[r12]

00000334 e1a0e00f   1349 	mov	lr,pc

00000338 e12fff1c*  1350 	bx	r12

0000033c e8bd8000   1351 	ldmfd	[sp]!,{pc}

                    1352 	.endf	OSCInfo_getUTCDate

                    1353 	.align	4

                    1354 

                    1355 ;oscInfo	none	param

                    1356 

                    1357 	.section ".bss","awb"

                    1358 .L1534:

                    1359 	.data

                    1360 	.text

                    1361 

                    1362 

                    1363 	.align	4

                    1364 	.align	4

                    1365 OSCInfo_getDateMS::

00000340 e92d4000   1366 	stmfd	[sp]!,{lr}

00000344 e590c02c   1367 	ldr	r12,[r0,44]

00000348 e59cc004   1368 	ldr	r12,[r12,4]

0000034c e1a0e00f   1369 	mov	lr,pc

00000350 e12fff1c*  1370 	bx	r12

00000354 e8bd8000   1371 	ldmfd	[sp]!,{pc}

                    1372 	.endf	OSCInfo_getDateMS

                    1373 	.align	4

                    1374 

                    1375 ;oscInfo	none	param

                    1376 

                    1377 	.section ".bss","awb"

                    1378 .L1566:

                    1379 	.data

                    1380 	.text

                    1381 

                    1382 


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                    1383 	.align	4

                    1384 	.align	4

                    1385 OSCInfo_getOscVersion::

00000358 e92d4000   1386 	stmfd	[sp]!,{lr}

0000035c e590c02c   1387 	ldr	r12,[r0,44]

00000360 e59cc008   1388 	ldr	r12,[r12,8]

00000364 e1a0e00f   1389 	mov	lr,pc

00000368 e12fff1c*  1390 	bx	r12

0000036c e8bd8000   1391 	ldmfd	[sp]!,{pc}

                    1392 	.endf	OSCInfo_getOscVersion

                    1393 	.align	4

                    1394 

                    1395 ;oscInfo	none	param

                    1396 

                    1397 	.section ".bss","awb"

                    1398 .L1598:

                    1399 	.data

                    1400 	.text

                    1401 

                    1402 

                    1403 	.align	4

                    1404 	.align	4

                    1405 OSCInfo_getADCClkFreq::

00000370 e92d4000   1406 	stmfd	[sp]!,{lr}

00000374 e590c02c   1407 	ldr	r12,[r0,44]

00000378 e59cc00c   1408 	ldr	r12,[r12,12]

0000037c e1a0e00f   1409 	mov	lr,pc

00000380 e12fff1c*  1410 	bx	r12

00000384 e8bd8000   1411 	ldmfd	[sp]!,{pc}

                    1412 	.endf	OSCInfo_getADCClkFreq

                    1413 	.align	4

                    1414 

                    1415 ;oscInfo	none	param

                    1416 

                    1417 	.section ".bss","awb"

                    1418 .L1630:

                    1419 	.data

                    1420 	.text

                    1421 

                    1422 

                    1423 	.align	4

                    1424 	.align	4

                    1425 OSCInfo_getPrehistFrameCount::

00000388 e92d4000   1426 	stmfd	[sp]!,{lr}

0000038c e590c02c   1427 	ldr	r12,[r0,44]

00000390 e59cc010   1428 	ldr	r12,[r12,16]

00000394 e1a0e00f   1429 	mov	lr,pc

00000398 e12fff1c*  1430 	bx	r12

0000039c e8bd8000   1431 	ldmfd	[sp]!,{pc}

                    1432 	.endf	OSCInfo_getPrehistFrameCount

                    1433 	.align	4

                    1434 

                    1435 ;oscInfo	none	param

                    1436 

                    1437 	.section ".bss","awb"

                    1438 .L1662:

                    1439 	.data

                    1440 	.text

                    1441 

                    1442 

                    1443 	.align	4


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                    1444 	.align	4

                    1445 OSCInfo_getPrehistFirstFrameNum::

000003a0 e92d4000   1446 	stmfd	[sp]!,{lr}

000003a4 e590c02c   1447 	ldr	r12,[r0,44]

000003a8 e59cc014   1448 	ldr	r12,[r12,20]

000003ac e1a0e00f   1449 	mov	lr,pc

000003b0 e12fff1c*  1450 	bx	r12

000003b4 e8bd8000   1451 	ldmfd	[sp]!,{pc}

                    1452 	.endf	OSCInfo_getPrehistFirstFrameNum

                    1453 	.align	4

                    1454 

                    1455 ;oscInfo	none	param

                    1456 

                    1457 	.section ".bss","awb"

                    1458 .L1694:

                    1459 	.data

                    1460 	.text

                    1461 

                    1462 

                    1463 	.align	4

                    1464 	.align	4

                    1465 OSCInfo_getPointPerFrameCount::

000003b8 e92d4000   1466 	stmfd	[sp]!,{lr}

000003bc e590c02c   1467 	ldr	r12,[r0,44]

000003c0 e59cc018   1468 	ldr	r12,[r12,24]

000003c4 e1a0e00f   1469 	mov	lr,pc

000003c8 e12fff1c*  1470 	bx	r12

000003cc e8bd8000   1471 	ldmfd	[sp]!,{pc}

                    1472 	.endf	OSCInfo_getPointPerFrameCount

                    1473 	.align	4

                    1474 

                    1475 ;oscInfo	none	param

                    1476 

                    1477 	.section ".bss","awb"

                    1478 .L1726:

                    1479 	.data

                    1480 	.text

                    1481 

                    1482 

                    1483 	.align	4

                    1484 	.align	4

                    1485 OSCInfo_getAnalogCount::

000003d0 e92d4000   1486 	stmfd	[sp]!,{lr}

000003d4 e590c02c   1487 	ldr	r12,[r0,44]

000003d8 e59cc01c   1488 	ldr	r12,[r12,28]

000003dc e1a0e00f   1489 	mov	lr,pc

000003e0 e12fff1c*  1490 	bx	r12

000003e4 e8bd8000   1491 	ldmfd	[sp]!,{pc}

                    1492 	.endf	OSCInfo_getAnalogCount

                    1493 	.align	4

                    1494 

                    1495 ;oscInfo	none	param

                    1496 

                    1497 	.section ".bss","awb"

                    1498 .L1758:

                    1499 	.data

                    1500 	.text

                    1501 

                    1502 

                    1503 	.align	4

                    1504 	.align	4


                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                    1505 OSCInfo_getBoolCount::

000003e8 e92d4000   1506 	stmfd	[sp]!,{lr}

000003ec e590c02c   1507 	ldr	r12,[r0,44]

000003f0 e59cc020   1508 	ldr	r12,[r12,32]

000003f4 e1a0e00f   1509 	mov	lr,pc

000003f8 e12fff1c*  1510 	bx	r12

000003fc e8bd8000   1511 	ldmfd	[sp]!,{pc}

                    1512 	.endf	OSCInfo_getBoolCount

                    1513 	.align	4

                    1514 

                    1515 ;oscInfo	none	param

                    1516 

                    1517 	.section ".bss","awb"

                    1518 .L1790:

                    1519 	.data

                    1520 	.text

                    1521 

                    1522 

                    1523 	.align	4

                    1524 	.align	4

                    1525 OSCInfo_getHeaderSize::

00000400 e92d4000   1526 	stmfd	[sp]!,{lr}

00000404 e590c02c   1527 	ldr	r12,[r0,44]

00000408 e59cc028   1528 	ldr	r12,[r12,40]

0000040c e1a0e00f   1529 	mov	lr,pc

00000410 e12fff1c*  1530 	bx	r12

00000414 e8bd8000   1531 	ldmfd	[sp]!,{pc}

                    1532 	.endf	OSCInfo_getHeaderSize

                    1533 	.align	4

                    1534 

                    1535 ;oscInfo	none	param

                    1536 

                    1537 	.section ".bss","awb"

                    1538 .L1822:

                    1539 	.data

                    1540 	.text

                    1541 

                    1542 

                    1543 	.align	4

                    1544 	.align	4

                    1545 OSCInfo_getFrameCount::

00000418 e5900060   1546 	ldr	r0,[r0,96]

0000041c e12fff1e*  1547 	ret	

                    1548 	.endf	OSCInfo_getFrameCount

                    1549 	.align	4

                    1550 

                    1551 ;oscInfo	r0	param

                    1552 

                    1553 	.section ".bss","awb"

                    1554 .L1854:

                    1555 	.data

                    1556 	.text

                    1557 

                    1558 

                    1559 	.align	4

                    1560 	.align	4

                    1561 OSCInfo_getFrameOffset::

00000420 e92d4070   1562 	stmfd	[sp]!,{r4-r6,lr}

00000424 e1a04001   1563 	mov	r4,r1

00000428 e1a05000   1564 	mov	r5,r0

0000042c ebffffd5*  1565 	bl	OSCInfo_getPrehistFrameCount


                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
00000430 e1a06000   1566 	mov	r6,r0

00000434 e1540006   1567 	cmp	r4,r6

00000438 2a000005   1568 	bhs	.L1861

0000043c e1a00005   1569 	mov	r0,r5

00000440 ebffffd6*  1570 	bl	OSCInfo_getPrehistFirstFrameNum

00000444 e0800004   1571 	add	r0,r0,r4

00000448 e0501006   1572 	subs	r1,r0,r6

0000044c 31a01000   1573 	movlo	r1,r0

00000450 e1a04001   1574 	mov	r4,r1

                    1575 .L1861:

00000454 e5951068   1576 	ldr	r1,[r5,104]

00000458 e5950064   1577 	ldr	r0,[r5,100]

0000045c e0201094   1578 	mla	r0,r4,r0,r1

00000460 e8bd8070   1579 	ldmfd	[sp]!,{r4-r6,pc}

                    1580 	.endf	OSCInfo_getFrameOffset

                    1581 	.align	4

                    1582 ;prehistFrameCount	r6	local

                    1583 ;relativeFrameNum	r0	local

                    1584 

                    1585 ;oscInfo	r5	param

                    1586 ;frameNum	r4	param

                    1587 

                    1588 	.section ".bss","awb"

                    1589 .L1920:

                    1590 	.data

                    1591 	.text

                    1592 

                    1593 

                    1594 	.align	4

                    1595 	.align	4

                    1596 OSCInfo_getADCSampleSize::

00000464 e92d4000   1597 	stmfd	[sp]!,{lr}

00000468 e590c02c   1598 	ldr	r12,[r0,44]

0000046c e59cc02c   1599 	ldr	r12,[r12,44]

00000470 e1a0e00f   1600 	mov	lr,pc

00000474 e12fff1c*  1601 	bx	r12

00000478 e8bd8000   1602 	ldmfd	[sp]!,{pc}

                    1603 	.endf	OSCInfo_getADCSampleSize

                    1604 	.align	4

                    1605 

                    1606 ;oscInfo	none	param

                    1607 

                    1608 	.section ".bss","awb"

                    1609 .L1950:

                    1610 	.data

                    1611 	.text

                    1612 

                    1613 

                    1614 	.align	4

                    1615 	.align	4

                    1616 OSCInfo_getADCFractionSize::

0000047c e92d4000   1617 	stmfd	[sp]!,{lr}

00000480 e590c02c   1618 	ldr	r12,[r0,44]

00000484 e59cc030   1619 	ldr	r12,[r12,48]

00000488 e1a0e00f   1620 	mov	lr,pc

0000048c e12fff1c*  1621 	bx	r12

00000490 e8bd8000   1622 	ldmfd	[sp]!,{pc}

                    1623 	.endf	OSCInfo_getADCFractionSize

                    1624 	.align	4

                    1625 

                    1626 ;oscInfo	none	param


                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                    1627 

                    1628 	.section ".bss","awb"

                    1629 .L1982:

                    1630 	.data

                    1631 	.text

                    1632 

                    1633 

                    1634 	.align	4

                    1635 	.align	4

                    1636 OSCInfo_getOscContentOffset::

00000494 eaffffd9*  1637 	b	OSCInfo_getHeaderSize

                    1638 	.endf	OSCInfo_getOscContentOffset

                    1639 	.align	4

                    1640 

                    1641 ;oscInfo	none	param

                    1642 

                    1643 	.section ".bss","awb"

                    1644 .L2014:

                    1645 	.data

                    1646 	.text

                    1647 

                    1648 

                    1649 	.align	4

                    1650 	.align	4

                    1651 OSCInfo_getBufferContent::

00000498 e2800030   1652 	add	r0,r0,48

0000049c e12fff1e*  1653 	ret	

                    1654 	.endf	OSCInfo_getBufferContent

                    1655 	.align	4

                    1656 

                    1657 ;oscInfo	r0	param

                    1658 

                    1659 	.section ".bss","awb"

                    1660 .L2046:

                    1661 	.data

                    1662 	.text

                    1663 

                    1664 

                    1665 	.align	4

                    1666 	.align	4

                    1667 OSCInfo_getFrameBuffer::

000004a0 e2800044   1668 	add	r0,r0,68

000004a4 e12fff1e*  1669 	ret	

                    1670 	.endf	OSCInfo_getFrameBuffer

                    1671 	.align	4

                    1672 

                    1673 ;oscInfo	r0	param

                    1674 

                    1675 	.section ".bss","awb"

                    1676 .L2078:

                    1677 	.data

                    1678 	.text

                    1679 

                    1680 

                    1681 	.align	4

                    1682 	.align	4

                    1683 OSCFrame_getADCPeriod::

000004a8 e92d4000   1684 	stmfd	[sp]!,{lr}

000004ac e2800044   1685 	add	r0,r0,68

000004b0 eb000000*  1686 	bl	OscWriteBuffer_data

000004b4 e5d02001   1687 	ldrb	r2,[r0,1]


                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
000004b8 e5d01000   1688 	ldrb	r1,[r0]

000004bc e0811402   1689 	add	r1,r1,r2 lsl 8

000004c0 e5d02002   1690 	ldrb	r2,[r0,2]

000004c4 e5d00003   1691 	ldrb	r0,[r0,3]

000004c8 e0811802   1692 	add	r1,r1,r2 lsl 16

000004cc e1a00c00   1693 	mov	r0,r0 lsl 24

000004d0 e0810000   1694 	add	r0,r1,r0

000004d4 e8bd8000   1695 	ldmfd	[sp]!,{pc}

                    1696 	.endf	OSCFrame_getADCPeriod

                    1697 	.align	4

                    1698 ;result	r1	local

                    1699 

                    1700 ;oscInfo	r0	param

                    1701 

                    1702 	.section ".bss","awb"

                    1703 .L2110:

                    1704 	.data

                    1705 	.text

                    1706 

                    1707 

                    1708 	.align	4

                    1709 	.align	4

                    1710 OSCFrame_getAnalogValue::

000004d8 e92d44f0   1711 	stmfd	[sp]!,{r4-r7,r10,lr}

000004dc e24dd004   1712 	sub	sp,sp,4

000004e0 e1a06001   1713 	mov	r6,r1

000004e4 e1a0a002   1714 	mov	r10,r2

000004e8 e1a07000   1715 	mov	r7,r0

000004ec e2870044   1716 	add	r0,r7,68

000004f0 eb000000*  1717 	bl	OscWriteBuffer_data

000004f4 e1a05000   1718 	mov	r5,r0

000004f8 e1a00007   1719 	mov	r0,r7

000004fc ebffffd8*  1720 	bl	OSCInfo_getADCSampleSize

00000500 e1a04000   1721 	mov	r4,r0

00000504 e1a00007   1722 	mov	r0,r7

00000508 ebffffaa*  1723 	bl	OSCInfo_getPointPerFrameCount

                    1724 ;319: {


                    1725 

                    1726 ;320: 	return 4;


                    1727 

0000050c e000009a   1728 	mul	r0,r10,r0

00000510 e0205094   1729 	mla	r0,r4,r0,r5

00000514 e0200694   1730 	mla	r0,r4,r6,r0

00000518 e3540004   1731 	cmp	r4,4

                    1732 ;630: 	{


                    1733 

                    1734 ;631: 		//Не лезет в int


                    1735 ;632: 		return 0;


                    1736 

0000051c e2801004   1737 	add	r1,r0,4

                    1738 ;626: {


                    1739 

00000520 e3a00000   1740 	mov	r0,0

00000524 e58d0000   1741 	str	r0,[sp]

                    1742 ;627: 	int result = 0;


                    1743 

                    1744 ;628: 	int insignificantBitCount;


                    1745 ;629: 	if (byteCount > sizeof(int))


                    1746 

00000528 8a000008   1747 	bhi	.L2117

                    1748 ;633: 	}



                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                    1749 ;634: 


                    1750 ;635: 


                    1751 ;636: 	memcpy(&result, pData, byteCount);


                    1752 

0000052c e1a02004   1753 	mov	r2,r4

00000530 e1a0000d   1754 	mov	r0,sp

00000534 eb000000*  1755 	bl	memcpy

                    1756 ;637: 	//Расширяем знак


                    1757 ;638: 	insignificantBitCount = (sizeof(int) - byteCount) * 8;


                    1758 

00000538 e1a00184   1759 	mov	r0,r4 lsl 3

0000053c e59d1000   1760 	ldr	r1,[sp]

00000540 e2600020   1761 	rsb	r0,r0,32

                    1762 ;639: 	result <<= insignificantBitCount;


                    1763 

00000544 e1a01011   1764 	mov	r1,r1 lsl r0

                    1765 ;640: 	result >>= insignificantBitCount;


                    1766 

00000548 e1a00051   1767 	mov	r0,r1 asr r0

0000054c e58d0000   1768 	str	r0,[sp]

                    1769 ;641: 	return result;


                    1770 

                    1771 .L2117:

00000550 e28dd004   1772 	add	sp,sp,4

00000554 e8bd84f0   1773 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    1774 	.endf	OSCFrame_getAnalogValue

                    1775 	.align	4

                    1776 ;frame	r5	local

                    1777 ;value	r1	local

                    1778 ;byteCount	r4	local

                    1779 ;result	[sp]	local

                    1780 ;insignificantBitCount	r0	local

                    1781 

                    1782 ;oscInfo	r7	param

                    1783 ;pointNum	r6	param

                    1784 ;analogNum	r10	param

                    1785 

                    1786 	.section ".bss","awb"

                    1787 .L2176:

                    1788 	.data

                    1789 	.text

                    1790 

                    1791 

                    1792 	.align	4

                    1793 	.align	4

                    1794 OSCFrame_getBoolValue::

00000558 e92d44f0   1795 	stmfd	[sp]!,{r4-r7,r10,lr}

0000055c e1a06002   1796 	mov	r6,r2

00000560 e1a07000   1797 	mov	r7,r0

00000564 e2870044   1798 	add	r0,r7,68

00000568 eb000000*  1799 	bl	OscWriteBuffer_data

0000056c e1a04000   1800 	mov	r4,r0

00000570 e1a00007   1801 	mov	r0,r7

00000574 ebffff95*  1802 	bl	OSCInfo_getAnalogCount

00000578 e1a0a000   1803 	mov	r10,r0

0000057c e1a00007   1804 	mov	r0,r7

00000580 ebffffb7*  1805 	bl	OSCInfo_getADCSampleSize

00000584 e1a05000   1806 	mov	r5,r0

00000588 e1a00007   1807 	mov	r0,r7

0000058c ebffff89*  1808 	bl	OSCInfo_getPointPerFrameCount

                    1809 ;319: {



                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                    1810 

                    1811 ;320: 	return 4;


                    1812 

00000590 e000009a   1813 	mul	r0,r10,r0

00000594 e0204095   1814 	mla	r0,r5,r0,r4

00000598 e0800006   1815 	add	r0,r0,r6

0000059c e5d00004   1816 	ldrb	r0,[r0,4]

000005a0 e8bd84f0   1817 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    1818 	.endf	OSCFrame_getBoolValue

                    1819 	.align	4

                    1820 ;frame	r4	local

                    1821 ;value	r0	local

                    1822 ;analogCount	r10	local

                    1823 ;adcSampleSize	r5	local

                    1824 

                    1825 ;oscInfo	r7	param

                    1826 ;sampleNum	none	param

                    1827 ;boolNum	r6	param

                    1828 

                    1829 	.section ".bss","awb"

                    1830 .L2222:

                    1831 	.data

                    1832 	.text

                    1833 

                    1834 

                    1835 	.align	4

                    1836 	.align	4

                    1837 OSCFrame_getTick::

000005a4 e92d40f0   1838 	stmfd	[sp]!,{r4-r7,lr}

000005a8 e24dd004   1839 	sub	sp,sp,4

000005ac e1a06001   1840 	mov	r6,r1

000005b0 e1a04000   1841 	mov	r4,r0

000005b4 e2840044   1842 	add	r0,r4,68

000005b8 eb000000*  1843 	bl	OscWriteBuffer_data

000005bc e1a07000   1844 	mov	r7,r0

000005c0 e1a00004   1845 	mov	r0,r4

000005c4 ebffff69*  1846 	bl	OSCInfo_getADCClkFreq

000005c8 eb000000*  1847 	bl	__utod

000005cc e1a05001   1848 	mov	r5,r1

                    1849 ;319: {


                    1850 

                    1851 ;320: 	return 4;


                    1852 

000005d0 e1a01007   1853 	mov	r1,r7

000005d4 e1a04000   1854 	mov	r4,r0

000005d8 e1a0000d   1855 	mov	r0,sp

000005dc e3a02004   1856 	mov	r2,4

000005e0 eb000000*  1857 	bl	memcpy

000005e4 e59d1000   1858 	ldr	r1,[sp]

000005e8 e1b00001   1859 	movs	r0,r1

000005ec 020000ff   1860 	andeq	r0,r0,255

000005f0 0a00000c   1861 	beq	.L2229

000005f4 eb000000*  1862 	bl	__utod

000005f8 e1a02000   1863 	mov	r2,r0

000005fc e1a00004   1864 	mov	r0,r4

00000600 e1a03001   1865 	mov	r3,r1

00000604 e1a01005   1866 	mov	r1,r5

00000608 eb000000*  1867 	bl	__ddiv

0000060c e1a02000   1868 	mov	r2,r0

00000610 e1a03001   1869 	mov	r3,r1

00000614 e59f1508*  1870 	ldr	r1,.L2291


                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
00000618 e3a00000   1871 	mov	r0,0

0000061c eb000000*  1872 	bl	__ddiv

00000620 e8860003   1873 	stmea	[r6],{r0-r1}

00000624 e3a00001   1874 	mov	r0,1

                    1875 .L2229:

00000628 e28dd004   1876 	add	sp,sp,4

0000062c e8bd80f0   1877 	ldmfd	[sp]!,{r4-r7,pc}

                    1878 	.endf	OSCFrame_getTick

                    1879 	.align	4

                    1880 ;frame	r7	local

                    1881 ;adcTick	[sp]	local

                    1882 ;mainFreq	r4	local

                    1883 

                    1884 ;oscInfo	r4	param

                    1885 ;tick	r6	param

                    1886 

                    1887 	.section ".bss","awb"

                    1888 .L2277:

                    1889 	.data

                    1890 	.text

                    1891 

                    1892 

                    1893 	.align	4

                    1894 	.align	4

                    1895 OSCFrame_getFreq::

00000630 e92d40f0   1896 	stmfd	[sp]!,{r4-r7,lr}

00000634 e24dd004   1897 	sub	sp,sp,4

00000638 e1a06001   1898 	mov	r6,r1

0000063c e1a04000   1899 	mov	r4,r0

00000640 e2840044   1900 	add	r0,r4,68

00000644 eb000000*  1901 	bl	OscWriteBuffer_data

00000648 e1a07000   1902 	mov	r7,r0

0000064c e1a00004   1903 	mov	r0,r4

00000650 ebffff46*  1904 	bl	OSCInfo_getADCClkFreq

00000654 eb000000*  1905 	bl	__utod

00000658 e1a05001   1906 	mov	r5,r1

                    1907 ;319: {


                    1908 

                    1909 ;320: 	return 4;


                    1910 

0000065c e1a01007   1911 	mov	r1,r7

00000660 e1a04000   1912 	mov	r4,r0

00000664 e1a0000d   1913 	mov	r0,sp

00000668 e3a02004   1914 	mov	r2,4

0000066c eb000000*  1915 	bl	memcpy

00000670 e59d1000   1916 	ldr	r1,[sp]

00000674 e1b00001   1917 	movs	r0,r1

00000678 020000ff   1918 	andeq	r0,r0,255

0000067c 0a000008   1919 	beq	.L2292

00000680 eb000000*  1920 	bl	__utod

00000684 e1a02000   1921 	mov	r2,r0

00000688 e1a00004   1922 	mov	r0,r4

0000068c e1a03001   1923 	mov	r3,r1

00000690 e1a01005   1924 	mov	r1,r5

00000694 eb000000*  1925 	bl	__ddiv

00000698 eb000000*  1926 	bl	__dtof

0000069c e5860000   1927 	str	r0,[r6]

000006a0 e3a00001   1928 	mov	r0,1

                    1929 .L2292:

000006a4 e28dd004   1930 	add	sp,sp,4

000006a8 e8bd80f0   1931 	ldmfd	[sp]!,{r4-r7,pc}


                                                                      Page 33
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                    1932 	.endf	OSCFrame_getFreq

                    1933 	.align	4

                    1934 ;frame	r7	local

                    1935 ;adcFreq	[sp]	local

                    1936 ;mainFreq	r4	local

                    1937 

                    1938 ;oscInfo	r4	param

                    1939 ;freq	r6	param

                    1940 

                    1941 	.section ".bss","awb"

                    1942 .L2341:

                    1943 	.data

                    1944 	.text

                    1945 

                    1946 

                    1947 ;808: 


                    1948 ;809: bool OSCInfo_initContent(OSCInfoStruct *oscInfo)


                    1949 	.align	4

                    1950 	.align	4

                    1951 OSCInfo_initContent::

000006ac e92d4ff0   1952 	stmfd	[sp]!,{r4-fp,lr}

000006b0 e3a086ff   1953 	mov	r8,255<<20

000006b4 e28885c0   1954 	add	r8,r8,3<<28

                    1955 ;810: {


                    1956 

                    1957 ;811: 	return analogInit(oscInfo) && boolInit(oscInfo);


                    1958 

000006b8 e3a07000   1959 	mov	r7,0

000006bc e1a06007   1960 	mov	r6,r7

000006c0 e24dd024   1961 	sub	sp,sp,36

000006c4 e28d1010   1962 	add	r1,sp,16

000006c8 e88101c0   1963 	stmea	[r1],{r6-r8}

                    1964 ;788: {


                    1965 

                    1966 ;789: 	size_t count = OSCInfo_getAnalogCount(oscInfo);


                    1967 

000006cc e1a04000   1968 	mov	r4,r0

000006d0 ebffff3e*  1969 	bl	OSCInfo_getAnalogCount

000006d4 e1a05000   1970 	mov	r5,r0

                    1971 ;790: 	unsigned short *pIndexs = getAnalogContentPtr(oscInfo);


                    1972 

                    1973 ;752: {


                    1974 

                    1975 ;753: 	unsigned char *data = OscWriteBuffer_data(&oscInfo->wbContent);


                    1976 

000006d8 e2840030   1977 	add	r0,r4,48

000006dc eb000000*  1978 	bl	OscWriteBuffer_data

                    1979 ;754: 	unsigned short *pIndexs = (unsigned short*)data;


                    1980 

                    1981 ;755: 	return pIndexs;


                    1982 

000006e0 e1a0a000   1983 	mov	r10,r0

                    1984 ;791: 	unsigned int i;


                    1985 ;792: 	OSCInfoAnalog *pAnalog = oscInfo->pAnalog;


                    1986 

000006e4 e5940058   1987 	ldr	r0,[r4,88]

000006e8 e3550000   1988 	cmp	r5,0

000006ec e58d0008   1989 	str	r0,[sp,8]

                    1990 ;793: 	


                    1991 ;794: 	for (i = 0; i < count; ++i)


                    1992 


                                                                      Page 34
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
000006f0 a1a08005   1993 	movge	r8,r5

000006f4 b3a08000   1994 	movlt	r8,0

000006f8 e1b070a8   1995 	movs	r7,r8 lsr 1

000006fc 0a00005b   1996 	beq	.L2529

                    1997 .L2530:

00000700 e1a00086   1998 	mov	r0,r6 lsl 1

00000704 e19a00b0   1999 	ldrh	r0,[r10,r0]

00000708 eb000000*  2000 	bl	OSCDescr_findDescrAnalogItem

0000070c e3500000   2001 	cmp	r0,0

00000710 0a0000e0   2002 	beq	.L2358

00000714 e59d2008   2003 	ldr	r2,[sp,8]

00000718 e1a09206   2004 	mov	r9,r6 lsl 4

0000071c e7890002   2005 	str	r0,[r9,r2]

00000720 e5940058   2006 	ldr	r0,[r4,88]

00000724 e0895000   2007 	add	r5,r9,r0

00000728 e4950008   2008 	ldr	r0,[r5],8

0000072c e5d0b004   2009 	ldrb	fp,[r0,4]

00000730 e1a00004   2010 	mov	r0,r4

00000734 ebffff50*  2011 	bl	OSCInfo_getADCFractionSize

00000738 e08b0000   2012 	add	r0,fp,r0

0000073c e2400001   2013 	sub	r0,r0,1

00000740 e3a0b001   2014 	mov	fp,1

00000744 e1a0001b   2015 	mov	r0,fp lsl r0

00000748 e2400001   2016 	sub	r0,r0,1

0000074c e2602000   2017 	rsb	r2,r0,0

00000750 e8850005   2018 	stmea	[r5],{r0,r2}

00000754 e5940058   2019 	ldr	r0,[r4,88]

00000758 e0895000   2020 	add	r5,r9,r0

0000075c e5950000   2021 	ldr	r0,[r5]

00000760 e5900000   2022 	ldr	r0,[r0]

00000764 eb000000*  2023 	bl	__ftod

00000768 e58d1020   2024 	str	r1,[sp,32]

0000076c e58d001c   2025 	str	r0,[sp,28]

00000770 e1a00004   2026 	mov	r0,r4

00000774 ebffff40*  2027 	bl	OSCInfo_getADCFractionSize

00000778 e1a0001b   2028 	mov	r0,fp lsl r0

0000077c eb000000*  2029 	bl	__itod

00000780 e1a02000   2030 	mov	r2,r0

00000784 e1a03001   2031 	mov	r3,r1

00000788 e59d001c   2032 	ldr	r0,[sp,28]

0000078c e59d1020   2033 	ldr	r1,[sp,32]

00000790 eb000000*  2034 	bl	__dmul

00000794 e1a03001   2035 	mov	r3,r1

00000798 e1a02000   2036 	mov	r2,r0

0000079c e59d0014   2037 	ldr	r0,[sp,20]

000007a0 e59d1018   2038 	ldr	r1,[sp,24]

000007a4 e2866001   2039 	add	r6,r6,1

000007a8 eb000000*  2040 	bl	__ddiv

000007ac eb000000*  2041 	bl	__dtof

000007b0 e5850004   2042 	str	r0,[r5,4]

000007b4 e1a00086   2043 	mov	r0,r6 lsl 1

000007b8 e1a0500a   2044 	mov	r5,r10

000007bc e19500b0   2045 	ldrh	r0,[r5,r0]

000007c0 eb000000*  2046 	bl	OSCDescr_findDescrAnalogItem

000007c4 e3500000   2047 	cmp	r0,0

000007c8 0a0000b2   2048 	beq	.L2358

000007cc e59d2008   2049 	ldr	r2,[sp,8]

000007d0 e7820206   2050 	str	r0,[r2,r6 lsl 4]

000007d4 e5940058   2051 	ldr	r0,[r4,88]

000007d8 e0805206   2052 	add	r5,r0,r6 lsl 4

000007dc e4950008   2053 	ldr	r0,[r5],8


                                                                      Page 35
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
000007e0 e5d0b004   2054 	ldrb	fp,[r0,4]

000007e4 e1a00004   2055 	mov	r0,r4

000007e8 ebffff23*  2056 	bl	OSCInfo_getADCFractionSize

000007ec e08b0000   2057 	add	r0,fp,r0

000007f0 e2400001   2058 	sub	r0,r0,1

000007f4 e3a0b001   2059 	mov	fp,1

000007f8 e1a0001b   2060 	mov	r0,fp lsl r0

000007fc e2400001   2061 	sub	r0,r0,1

00000800 e2602000   2062 	rsb	r2,r0,0

00000804 e8850005   2063 	stmea	[r5],{r0,r2}

00000808 e5940058   2064 	ldr	r0,[r4,88]

0000080c e0805206   2065 	add	r5,r0,r6 lsl 4

00000810 e5950000   2066 	ldr	r0,[r5]

00000814 e5900000   2067 	ldr	r0,[r0]

00000818 eb000000*  2068 	bl	__ftod

0000081c e58d1020   2069 	str	r1,[sp,32]

00000820 e58d001c   2070 	str	r0,[sp,28]

00000824 e1a00004   2071 	mov	r0,r4

00000828 ebffff13*  2072 	bl	OSCInfo_getADCFractionSize

0000082c e1a0001b   2073 	mov	r0,fp lsl r0

00000830 eb000000*  2074 	bl	__itod

00000834 e1a02000   2075 	mov	r2,r0

00000838 e1a03001   2076 	mov	r3,r1

0000083c e59d1020   2077 	ldr	r1,[sp,32]

00000840 e59d001c   2078 	ldr	r0,[sp,28]

00000844 eb000000*  2079 	bl	__dmul

00000848 e1a03001   2080 	mov	r3,r1

0000084c e1a02000   2081 	mov	r2,r0

00000850 e59d0014   2082 	ldr	r0,[sp,20]

00000854 e59d1018   2083 	ldr	r1,[sp,24]

00000858 e2866001   2084 	add	r6,r6,1

0000085c eb000000*  2085 	bl	__ddiv

00000860 eb000000*  2086 	bl	__dtof

00000864 e5850004   2087 	str	r0,[r5,4]

00000868 e2577001   2088 	subs	r7,r7,1

0000086c 1affffa3   2089 	bne	.L2530

                    2090 .L2529:

00000870 e2187001   2091 	ands	r7,r8,1

00000874 0a000030   2092 	beq	.L2371

00000878 e1a08206   2093 	mov	r8,r6 lsl 4

                    2094 .L2540:

0000087c e1a00086   2095 	mov	r0,r6 lsl 1

00000880 e19a00b0   2096 	ldrh	r0,[r10,r0]

00000884 eb000000*  2097 	bl	OSCDescr_findDescrAnalogItem

00000888 e3500000   2098 	cmp	r0,0

0000088c 0a000081   2099 	beq	.L2358

00000890 e59d1008   2100 	ldr	r1,[sp,8]

00000894 e1a05008   2101 	mov	r5,r8

00000898 e7810005   2102 	str	r0,[r1,r5]

0000089c e5940058   2103 	ldr	r0,[r4,88]

000008a0 e7b50000   2104 	ldr	r0,[r5,r0]!

000008a4 e5d0b004   2105 	ldrb	fp,[r0,4]

000008a8 e1a00004   2106 	mov	r0,r4

000008ac ebfffef2*  2107 	bl	OSCInfo_getADCFractionSize

000008b0 e08b0000   2108 	add	r0,fp,r0

000008b4 e2400001   2109 	sub	r0,r0,1

000008b8 e3a0b001   2110 	mov	fp,1

000008bc e1a0001b   2111 	mov	r0,fp lsl r0

000008c0 e2400001   2112 	sub	r0,r0,1

000008c4 e5850008   2113 	str	r0,[r5,8]

000008c8 e2600000   2114 	rsb	r0,r0,0


                                                                      Page 36
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
000008cc e585000c   2115 	str	r0,[r5,12]

000008d0 e5940058   2116 	ldr	r0,[r4,88]

000008d4 e0885000   2117 	add	r5,r8,r0

000008d8 e5950000   2118 	ldr	r0,[r5]

000008dc e5900000   2119 	ldr	r0,[r0]

000008e0 eb000000*  2120 	bl	__ftod

000008e4 e58d1020   2121 	str	r1,[sp,32]

000008e8 e58d001c   2122 	str	r0,[sp,28]

000008ec e1a00004   2123 	mov	r0,r4

000008f0 ebfffee1*  2124 	bl	OSCInfo_getADCFractionSize

000008f4 e1a0001b   2125 	mov	r0,fp lsl r0

000008f8 eb000000*  2126 	bl	__itod

000008fc e1a02000   2127 	mov	r2,r0

00000900 e1a03001   2128 	mov	r3,r1

00000904 e59d1020   2129 	ldr	r1,[sp,32]

00000908 e59d001c   2130 	ldr	r0,[sp,28]

0000090c eb000000*  2131 	bl	__dmul

00000910 e1a03001   2132 	mov	r3,r1

00000914 e1a02000   2133 	mov	r2,r0

00000918 e59d0014   2134 	ldr	r0,[sp,20]

0000091c e59d1018   2135 	ldr	r1,[sp,24]

00000920 e2866001   2136 	add	r6,r6,1

00000924 eb000000*  2137 	bl	__ddiv

00000928 eb000000*  2138 	bl	__dtof

0000092c e5850004   2139 	str	r0,[r5,4]

00000930 e2888010   2140 	add	r8,r8,16

00000934 e2577001   2141 	subs	r7,r7,1

00000938 1affffcf   2142 	bne	.L2540

                    2143 .L2371:

                    2144 ;805: 	}


                    2145 ;806: 	return true;


                    2146 

                    2147 ;766: {


                    2148 

                    2149 ;767: 	size_t count = OSCInfo_getBoolCount(oscInfo);


                    2150 

0000093c e1a00004   2151 	mov	r0,r4

00000940 ebfffea8*  2152 	bl	OSCInfo_getBoolCount

00000944 e1a07000   2153 	mov	r7,r0

                    2154 ;768: 	unsigned short *pOffsets = getBoolContentPtr(oscInfo);


                    2155 

                    2156 ;759: {


                    2157 

                    2158 ;760: 	size_t offset = OSCInfo_getAnalogCount(oscInfo) * getAnalogContentIdSize(oscInfo);


                    2159 

                    2160 ;260: {


                    2161 

                    2162 ;261: 	return 2;


                    2163 

00000948 e1a00004   2164 	mov	r0,r4

0000094c ebfffe9f*  2165 	bl	OSCInfo_getAnalogCount

00000950 e1a05000   2166 	mov	r5,r0

                    2167 ;761: 	unsigned char *data = OscWriteBuffer_data(&oscInfo->wbContent);


                    2168 

00000954 e2840030   2169 	add	r0,r4,48

00000958 eb000000*  2170 	bl	OscWriteBuffer_data

                    2171 ;762: 	return (unsigned short*)(data + offset);


                    2172 

0000095c e0805085   2173 	add	r5,r0,r5 lsl 1

                    2174 ;769: 


                    2175 ;770: 	unsigned int i;



                                                                      Page 37
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                    2176 ;771: 	OSCInfoBool *pBool = oscInfo->pBool;


                    2177 

00000960 e594605c   2178 	ldr	r6,[r4,92]

                    2179 ;772: 


                    2180 ;773: 


                    2181 ;774: 	for (i = 0; i < count; ++i)


                    2182 

00000964 e3a04000   2183 	mov	r4,0

00000968 e3570000   2184 	cmp	r7,0

0000096c a1a0b007   2185 	movge	fp,r7

00000970 b3a0b000   2186 	movlt	fp,0

00000974 e1b071ab   2187 	movs	r7,fp lsr 3

00000978 0a000039   2188 	beq	.L2488

                    2189 .L2489:

0000097c e1a00084   2190 	mov	r0,r4 lsl 1

00000980 e19500b0   2191 	ldrh	r0,[r5,r0]

00000984 eb000000*  2192 	bl	OSCDescr_findDescrBoolItem

00000988 e3500000   2193 	cmp	r0,0

0000098c 0a000041   2194 	beq	.L2358

00000990 e7860104   2195 	str	r0,[r6,r4 lsl 2]

00000994 e2844001   2196 	add	r4,r4,1

00000998 e1a00084   2197 	mov	r0,r4 lsl 1

0000099c e19500b0   2198 	ldrh	r0,[r5,r0]

000009a0 eb000000*  2199 	bl	OSCDescr_findDescrBoolItem

000009a4 e3500000   2200 	cmp	r0,0

000009a8 0a00003a   2201 	beq	.L2358

000009ac e7860104   2202 	str	r0,[r6,r4 lsl 2]

000009b0 e2844001   2203 	add	r4,r4,1

000009b4 e1a00084   2204 	mov	r0,r4 lsl 1

000009b8 e19500b0   2205 	ldrh	r0,[r5,r0]

000009bc eb000000*  2206 	bl	OSCDescr_findDescrBoolItem

000009c0 e3500000   2207 	cmp	r0,0

000009c4 0a000033   2208 	beq	.L2358

000009c8 e7860104   2209 	str	r0,[r6,r4 lsl 2]

000009cc e2844001   2210 	add	r4,r4,1

000009d0 e1a00084   2211 	mov	r0,r4 lsl 1

000009d4 e19500b0   2212 	ldrh	r0,[r5,r0]

000009d8 eb000000*  2213 	bl	OSCDescr_findDescrBoolItem

000009dc e3500000   2214 	cmp	r0,0

000009e0 0a00002c   2215 	beq	.L2358

000009e4 e7860104   2216 	str	r0,[r6,r4 lsl 2]

000009e8 e2844001   2217 	add	r4,r4,1

000009ec e1a00084   2218 	mov	r0,r4 lsl 1

000009f0 e19500b0   2219 	ldrh	r0,[r5,r0]

000009f4 eb000000*  2220 	bl	OSCDescr_findDescrBoolItem

000009f8 e3500000   2221 	cmp	r0,0

000009fc 0a000025   2222 	beq	.L2358

00000a00 e7860104   2223 	str	r0,[r6,r4 lsl 2]

00000a04 e2844001   2224 	add	r4,r4,1

00000a08 e1a00084   2225 	mov	r0,r4 lsl 1

00000a0c e19500b0   2226 	ldrh	r0,[r5,r0]

00000a10 eb000000*  2227 	bl	OSCDescr_findDescrBoolItem

00000a14 e3500000   2228 	cmp	r0,0

00000a18 0a00001e   2229 	beq	.L2358

00000a1c e7860104   2230 	str	r0,[r6,r4 lsl 2]

00000a20 e2844001   2231 	add	r4,r4,1

00000a24 e1a00084   2232 	mov	r0,r4 lsl 1

00000a28 e19500b0   2233 	ldrh	r0,[r5,r0]

00000a2c eb000000*  2234 	bl	OSCDescr_findDescrBoolItem

00000a30 e3500000   2235 	cmp	r0,0

00000a34 0a000017   2236 	beq	.L2358


                                                                      Page 38
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
00000a38 e7860104   2237 	str	r0,[r6,r4 lsl 2]

00000a3c e2844001   2238 	add	r4,r4,1

00000a40 e1a00084   2239 	mov	r0,r4 lsl 1

00000a44 e19500b0   2240 	ldrh	r0,[r5,r0]

00000a48 eb000000*  2241 	bl	OSCDescr_findDescrBoolItem

00000a4c e3500000   2242 	cmp	r0,0

00000a50 0a000010   2243 	beq	.L2358

00000a54 e7860104   2244 	str	r0,[r6,r4 lsl 2]

00000a58 e2844001   2245 	add	r4,r4,1

00000a5c e2577001   2246 	subs	r7,r7,1

00000a60 1affffc5   2247 	bne	.L2489

                    2248 .L2488:

00000a64 e21b7007   2249 	ands	r7,fp,7

00000a68 0a000008   2250 	beq	.L2368

                    2251 .L2523:

00000a6c e1a00084   2252 	mov	r0,r4 lsl 1

00000a70 e19500b0   2253 	ldrh	r0,[r5,r0]

00000a74 eb000000*  2254 	bl	OSCDescr_findDescrBoolItem

00000a78 e3500000   2255 	cmp	r0,0

00000a7c 0a000005   2256 	beq	.L2358

00000a80 e7860104   2257 	str	r0,[r6,r4 lsl 2]

00000a84 e2844001   2258 	add	r4,r4,1

00000a88 e2577001   2259 	subs	r7,r7,1

00000a8c 1afffff6   2260 	bne	.L2523

                    2261 .L2368:

                    2262 ;783: 	}


                    2263 ;784: 	return true;


                    2264 

00000a90 e3a00001   2265 	mov	r0,1

00000a94 e58d0010   2266 	str	r0,[sp,16]

                    2267 .L2358:

00000a98 e59d1010   2268 	ldr	r1,[sp,16]

00000a9c e20100ff   2269 	and	r0,r1,255

00000aa0 e28dd024   2270 	add	sp,sp,36

00000aa4 e8bd8ff0   2271 	ldmfd	[sp]!,{r4-fp,pc}

                    2272 	.endf	OSCInfo_initContent

                    2273 	.align	4

                    2274 ;count	r5	local

                    2275 ;pIndexs	r10	local

                    2276 ;i	r6	local

                    2277 ;pAnalog	[sp,8]	local

                    2278 ;descr	r0	local

                    2279 ;pAnalog	r5	local

                    2280 ;adcIntPartSize	fp	local

                    2281 ;comtradeSampleSize	r0	local

                    2282 ;adcMaxAbsValue	r0	local

                    2283 ;pAnalog	r5	local

                    2284 ;count	r7	local

                    2285 ;pOffsets	r5	local

                    2286 ;i	r4	local

                    2287 ;pBool	r6	local

                    2288 ;offset	r5	local

                    2289 ;descr	r0	local

                    2290 ;secondaryCoef	[sp,28]	local

                    2291 

                    2292 ;oscInfo	r4	param

                    2293 

                    2294 	.section ".bss","awb"

                    2295 .L2982:

                    2296 	.data

                    2297 	.text


                                                                      Page 39
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                    2298 

                    2299 ;812: }


                    2300 

                    2301 ;813: 


                    2302 ;814: OscDescrAnalog * OSCInfo_getAnalog(OSCInfoStruct *oscInfo, int index)


                    2303 	.align	4

                    2304 	.align	4

                    2305 OSCInfo_getAnalog::

                    2306 ;815: {


                    2307 

                    2308 ;816: 	return oscInfo->pAnalog[index].pDescr;


                    2309 

00000aa8 e5900058   2310 	ldr	r0,[r0,88]

00000aac e7900201   2311 	ldr	r0,[r0,r1 lsl 4]

00000ab0 e12fff1e*  2312 	ret	

                    2313 	.endf	OSCInfo_getAnalog

                    2314 	.align	4

                    2315 

                    2316 ;oscInfo	r0	param

                    2317 ;index	r1	param

                    2318 

                    2319 	.section ".bss","awb"

                    2320 .L3089:

                    2321 	.data

                    2322 	.text

                    2323 

                    2324 ;817: }


                    2325 

                    2326 ;818: 


                    2327 ;819: OscDescrBool * OSCInfo_getBool(OSCInfoStruct *oscInfo, int index)


                    2328 	.align	4

                    2329 	.align	4

                    2330 OSCInfo_getBool::

                    2331 ;820: {


                    2332 

                    2333 ;821: 	return oscInfo->pBool[index].pDescr;


                    2334 

00000ab4 e590005c   2335 	ldr	r0,[r0,92]

00000ab8 e7900101   2336 	ldr	r0,[r0,r1 lsl 2]

00000abc e12fff1e*  2337 	ret	

                    2338 	.endf	OSCInfo_getBool

                    2339 	.align	4

                    2340 

                    2341 ;oscInfo	r0	param

                    2342 ;index	r1	param

                    2343 

                    2344 	.section ".bss","awb"

                    2345 .L3121:

                    2346 	.data

                    2347 	.text

                    2348 

                    2349 ;822: }


                    2350 

                    2351 ;823: 


                    2352 ;824: float OSCInfo_getAnalogCft(OSCInfoStruct *oscInfo, int analogNum)


                    2353 	.align	4

                    2354 	.align	4

                    2355 OSCInfo_getAnalogCft::

                    2356 ;825: {


                    2357 

                    2358 ;826: 	OSCInfoAnalog *pAnalog = &oscInfo->pAnalog[analogNum];



                                                                      Page 40
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                    2359 

00000ac0 e5900058   2360 	ldr	r0,[r0,88]

00000ac4 e0800201   2361 	add	r0,r0,r1 lsl 4

                    2362 ;827: 	return pAnalog->cft;


                    2363 

00000ac8 e5900004   2364 	ldr	r0,[r0,4]

00000acc e12fff1e*  2365 	ret	

                    2366 	.endf	OSCInfo_getAnalogCft

                    2367 	.align	4

                    2368 ;pAnalog	r0	local

                    2369 

                    2370 ;oscInfo	r0	param

                    2371 ;analogNum	r1	param

                    2372 

                    2373 	.section ".bss","awb"

                    2374 .L3150:

                    2375 	.data

                    2376 	.text

                    2377 

                    2378 ;828: }


                    2379 

                    2380 ;829: 


                    2381 ;830: int OSCInfo_getAnalogMax(OSCInfoStruct *oscInfo, int index)


                    2382 	.align	4

                    2383 	.align	4

                    2384 OSCInfo_getAnalogMax::

                    2385 ;831: {


                    2386 

                    2387 ;832: 	OSCInfoAnalog *pAnalog = &oscInfo->pAnalog[index];


                    2388 

00000ad0 e5900058   2389 	ldr	r0,[r0,88]

00000ad4 e0800201   2390 	add	r0,r0,r1 lsl 4

                    2391 ;833: 	return pAnalog->maxValue;


                    2392 

00000ad8 e5900008   2393 	ldr	r0,[r0,8]

00000adc e12fff1e*  2394 	ret	

                    2395 	.endf	OSCInfo_getAnalogMax

                    2396 	.align	4

                    2397 ;pAnalog	r0	local

                    2398 

                    2399 ;oscInfo	r0	param

                    2400 ;index	r1	param

                    2401 

                    2402 	.section ".bss","awb"

                    2403 .L3182:

                    2404 	.data

                    2405 	.text

                    2406 

                    2407 ;834: }


                    2408 

                    2409 ;835: 


                    2410 ;836: int OSCInfo_getAnalogMin(OSCInfoStruct *oscInfo, int index)


                    2411 	.align	4

                    2412 	.align	4

                    2413 OSCInfo_getAnalogMin::

                    2414 ;837: {


                    2415 

                    2416 ;838: 	OSCInfoAnalog *pAnalog = &oscInfo->pAnalog[index];


                    2417 

00000ae0 e5900058   2418 	ldr	r0,[r0,88]

00000ae4 e0800201   2419 	add	r0,r0,r1 lsl 4


                                                                      Page 41
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                    2420 ;839: 	return pAnalog->minValue;


                    2421 

00000ae8 e590000c   2422 	ldr	r0,[r0,12]

00000aec e12fff1e*  2423 	ret	

                    2424 	.endf	OSCInfo_getAnalogMin

                    2425 	.align	4

                    2426 ;pAnalog	r0	local

                    2427 

                    2428 ;oscInfo	r0	param

                    2429 ;index	r1	param

                    2430 

                    2431 	.section ".bss","awb"

                    2432 .L3214:

                    2433 	.data

                    2434 	.text

                    2435 

                    2436 ;840: }


                    2437 	.align	4

                    2438 	.align	4

                    2439 getHeaderSize3:

                    2440 ;226: {


                    2441 

                    2442 ;227: 	return sizeof(OSCInfoStruct3);


                    2443 

00000af0 e3a00024   2444 	mov	r0,36

00000af4 e12fff1e*  2445 	ret	

                    2446 	.endf	getHeaderSize3

                    2447 	.align	4

                    2448 

                    2449 ;oscInfo	none	param

                    2450 

                    2451 	.section ".bss","awb"

                    2452 .L3246:

                    2453 	.data

                    2454 	.text

                    2455 	.align	4

                    2456 	.align	4

                    2457 getHeaderSize4:

                    2458 ;230: {


                    2459 

                    2460 ;231: 	return sizeof(OSCInfoStruct4);


                    2461 

00000af8 e3a0002c   2462 	mov	r0,44

00000afc e12fff1e*  2463 	ret	

                    2464 	.endf	getHeaderSize4

                    2465 	.align	4

                    2466 

                    2467 ;oscInfo	none	param

                    2468 

                    2469 	.section ".bss","awb"

                    2470 .L3278:

                    2471 	.data

                    2472 	.text

                    2473 	.align	4

                    2474 	.align	4

                    2475 getADCSampleSize3:

                    2476 ;236: {


                    2477 

                    2478 ;237: 	return 2;


                    2479 

00000b00 e3a00002   2480 	mov	r0,2


                                                                      Page 42
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
00000b04 e12fff1e*  2481 	ret	

                    2482 	.endf	getADCSampleSize3

                    2483 	.align	4

                    2484 

                    2485 ;oscInfo	none	param

                    2486 

                    2487 	.section ".bss","awb"

                    2488 .L3310:

                    2489 	.data

                    2490 	.text

                    2491 	.align	4

                    2492 	.align	4

                    2493 getADCFractionSize3:

                    2494 ;240: {


                    2495 

                    2496 ;241: 	return 0;


                    2497 

00000b08 e3a00000   2498 	mov	r0,0

00000b0c e12fff1e*  2499 	ret	

                    2500 	.endf	getADCFractionSize3

                    2501 	.align	4

                    2502 

                    2503 ;oscInfo	none	param

                    2504 

                    2505 	.section ".bss","awb"

                    2506 .L3342:

                    2507 	.data

                    2508 	.text

                    2509 	.align	4

                    2510 .L900:

00000b10 00000000*  2511 	.data.w	.L888

                    2512 	.type	.L900,$object

                    2513 	.size	.L900,4

                    2514 

                    2515 .L901:

00000b14 00000000*  2516 	.data.w	oscHeaderBuf

                    2517 	.type	.L901,$object

                    2518 	.size	.L901,4

                    2519 

                    2520 .L902:

00000b18 00000000*  2521 	.data.w	csHeaderBuf

                    2522 	.type	.L902,$object

                    2523 	.size	.L902,4

                    2524 

                    2525 .L1382:

00000b1c 00000000*  2526 	.data.w	oscInfoV3

                    2527 	.type	.L1382,$object

                    2528 	.size	.L1382,4

                    2529 

                    2530 .L1383:

00000b20 00000000*  2531 	.data.w	oscInfoV4

                    2532 	.type	.L1383,$object

                    2533 	.size	.L1383,4

                    2534 

                    2535 .L2291:

00000b24 412e8480   2536 	.data.w	0x412e8480

                    2537 	.type	.L2291,$object

                    2538 	.size	.L2291,4

                    2539 

                    2540 	.align	4

                    2541 


                                                                      Page 43
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bn41.s
                    2542 	.data

                    2543 .L3364:

                    2544 	.globl	oscInfoV3

00000000 00000000*  2545 oscInfoV3:	.data.w	getUTCDate

00000004 00000000*  2546 	.data.w	getDateMS

00000008 00000000*  2547 	.data.w	getOscVersion

0000000c 00000000*  2548 	.data.w	getADCClkFreq

00000010 00000000*  2549 	.data.w	getPrehistFrameCount3

00000014 00000000*  2550 	.data.w	getPrehistFirstFrameNum3

00000018 00000000*  2551 	.data.w	getPointPerFrameCount3

0000001c 00000000*  2552 	.data.w	getAnalogInCount3

00000020 00000000*  2553 	.data.w	getDigInCount3

00000024 00000000*  2554 	.data.w	getOscSize

00000028 00000000*  2555 	.data.w	getHeaderSize3

0000002c 00000000*  2556 	.data.w	getADCSampleSize3

00000030 00000000*  2557 	.data.w	getADCFractionSize3

                    2558 	.type	oscInfoV3,$object

                    2559 	.size	oscInfoV3,52

                    2560 .L3365:

                    2561 	.globl	oscInfoV4

00000034 00000000*  2562 oscInfoV4:	.data.w	getUTCDate

00000038 00000000*  2563 	.data.w	getDateMS

0000003c 00000000*  2564 	.data.w	getOscVersion

00000040 00000000*  2565 	.data.w	getADCClkFreq

00000044 00000000*  2566 	.data.w	getPrehistFrameCount4

00000048 00000000*  2567 	.data.w	getPrehistFirstFrameNum4

0000004c 00000000*  2568 	.data.w	getPointPerFrameCount4

00000050 00000000*  2569 	.data.w	getAnalogInCount4

00000054 00000000*  2570 	.data.w	getDigInCount4

00000058 00000000*  2571 	.data.w	getOscSize

0000005c 00000000*  2572 	.data.w	getHeaderSize4

00000060 00000000*  2573 	.data.w	getADCSampleSize4

00000064 00000000*  2574 	.data.w	getADCFractionSize4

                    2575 	.type	oscInfoV4,$object

                    2576 	.size	oscInfoV4,52

                    2577 	.comm	oscHeaderBuf,20,4

                    2578 	.type	oscHeaderBuf,$object

                    2579 	.size	oscHeaderBuf,20

                    2580 	.comm	csHeaderBuf,4,4

                    2581 	.type	csHeaderBuf,$object

                    2582 	.size	csHeaderBuf,4

                    2583 	.ghsnote version,6

                    2584 	.ghsnote tools,3

                    2585 	.ghsnote options,0

                    2586 	.text

                    2587 	.align	4

                    2588 	.data

                    2589 	.align	4

                    2590 	.section ".bss","awb"

                    2591 	.align	4

                    2592 	.text

