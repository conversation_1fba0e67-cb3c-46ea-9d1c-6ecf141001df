#pragma once

//Комментарий для проверки кодировки

#include <stdint.h>

#include "out_buffers.h"
#include "IsoConnectionForward.h"
#include "ReportQueue.h"
#include "iedTree/iedEntity.h"
#include "iedTree/DataSet.h"

struct ReporterStruct {
	RCB rcb;
	//! Счётчик миллисекунд для Integrity 
	uint32_t intgPdCounter;
	//! Сработал таймер Integrity
	bool intgTimerAlam;	
	IEDEntity dataSetEntity;	
	DataSet *dataSet;
	IsoConnection* connection;	
	ReportQueue buffer;
	SessionOutBuffer sessionOutBuffer;
};

typedef struct ReporterStruct Reporter;

