                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5m41.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=out_buffers.c -o gh_5m41.o -list=out_buffers.lst C:\Users\<USER>\AppData\Local\Temp\gh_5m41.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_5m41.s
Source File: out_buffers.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile out_buffers.c

                      11 ;		-o out_buffers.o

                      12 ;Source File:   out_buffers.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:07 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "out_buffers.h"


                      23 ;2: 


                      24 ;3: #include <platform_critical_section.h>


                      25 ;4: #include <debug.h>


                      26 ;5: #include <stddef.h>


                      27 ;6: 


                      28 ;7: void initSessionOutBuffers(SessionBuffers* buffers)


                      29 	.text

                      30 	.align	4

                      31 initSessionOutBuffers::

00000000 e92d4030     32 	stmfd	[sp]!,{r4-r5,lr}

00000004 e3a04000     33 	mov	r4,0

00000008 e1a05000     34 	mov	r5,r0

0000000c eb000000*    35 	bl	CriticalSection_Init

00000010 e5c54004     36 	strb	r4,[r5,4]

00000014 e2850c60     37 	add	r0,r5,3<<13

00000018 e5c0400c     38 	strb	r4,[r0,12]

0000001c e2850cc0     39 	add	r0,r5,3<<14

00000020 e5c04014     40 	strb	r4,[r0,20]

00000024 e2850b48     41 	add	r0,r5,9<<13

00000028 e5c0401c     42 	strb	r4,[r0,28]

0000002c e2850b60     43 	add	r0,r5,3<<15

00000030 e5c04024     44 	strb	r4,[r0,36]

00000034 e2850b78     45 	add	r0,r5,15<<13

00000038 e5c0402c     46 	strb	r4,[r0,44]

0000003c e2850b90     47 	add	r0,r5,9<<14

00000040 e5c04034     48 	strb	r4,[r0,52]

00000044 e2850ba8     49 	add	r0,r5,42<<12

00000048 e5c0403c     50 	strb	r4,[r0,60]


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5m41.s
0000004c e2850bc0     51 	add	r0,r5,3<<16

00000050 e5c04044     52 	strb	r4,[r0,68]

                      53 ;8: {


                      54 

                      55 ;9:     int i;


                      56 ;10:     CriticalSection_Init(&buffers->cs);


                      57 

                      58 ;11:     for(i = 0; i < COTP_OUT_BUFFERS_COUNT; ++i)


                      59 

00000054 e2850bd8     60 	add	r0,r5,54<<12

00000058 e5c0404c     61 	strb	r4,[r0,76]

0000005c e8bd8030     62 	ldmfd	[sp]!,{r4-r5,pc}

                      63 	.endf	initSessionOutBuffers

                      64 	.align	4

                      65 

                      66 ;buffers	r5	param

                      67 

                      68 	.section ".bss","awb"

                      69 .L88:

                      70 	.data

                      71 	.text

                      72 

                      73 ;14:     }


                      74 ;15:     //CriticalSection_Unlock(&cs);


                      75 ;16: }


                      76 

                      77 ;17: 


                      78 ;18: void SessionBuffers_done(SessionBuffers* buffers)


                      79 	.align	4

                      80 	.align	4

                      81 SessionBuffers_done::

                      82 ;19: {


                      83 

                      84 ;20: 	CriticalSection_Done(&buffers->cs);


                      85 

00000060 ea000000*    86 	b	CriticalSection_Done

                      87 	.endf	SessionBuffers_done

                      88 	.align	4

                      89 

                      90 ;buffers	none	param

                      91 

                      92 	.section ".bss","awb"

                      93 .L126:

                      94 	.data

                      95 	.text

                      96 

                      97 ;21: }


                      98 

                      99 ;22: 


                     100 ;23: SessionOutBuffer* allocSessionOutBuffer(SessionBuffers* buffers, int size)


                     101 	.align	4

                     102 	.align	4

                     103 allocSessionOutBuffer::

00000064 e92d4070    104 	stmfd	[sp]!,{r4-r6,lr}

                     105 ;24: {


                     106 

                     107 ;25:     int i;


                     108 ;26:     SessionOutBuffer* result = NULL;


                     109 

                     110 ;27: 	if (size > SESSION_OUT_BUF_SIZE)


                     111 


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5m41.s
00000068 e3510c60    112 	cmp	r1,3<<13

                     113 ;28: 	{


                     114 

                     115 ;29: 		ERROR_REPORT("Requested buffer is too large");


                     116 ;30: 		return NULL;


                     117 

0000006c c3a00000    118 	movgt	r0,0

00000070 ca00006d    119 	bgt	.L133

00000074 e3a06001    120 	mov	r6,1

00000078 e3a04000    121 	mov	r4,0

0000007c e1a05000    122 	mov	r5,r0

00000080 eb000000*   123 	bl	CriticalSection_Lock

                     124 ;31: 	}


                     125 ;32: 


                     126 ;33:     CriticalSection_Lock(&buffers->cs);


                     127 

                     128 ;34:     for(i = 0; i < COTP_OUT_BUFFERS_COUNT; ++i)


                     129 

00000084 e5d50004    130 	ldrb	r0,[r5,4]

00000088 e2851004    131 	add	r1,r5,4

0000008c e3500000    132 	cmp	r0,0

00000090 1a000005    133 	bne	.L195

00000094 e1a04001    134 	mov	r4,r1

00000098 e5c46000    135 	strb	r6,[r4]

                     136 ;41:         }


                     137 ;42:     }


                     138 ;43:     CriticalSection_Unlock(&buffers->cs);


                     139 

0000009c e1a00005    140 	mov	r0,r5

000000a0 eb000000*   141 	bl	CriticalSection_Unlock

                     142 ;44:     return result;


                     143 

000000a4 e1a00004    144 	mov	r0,r4

000000a8 ea00005f    145 	b	.L133

                     146 .L195:

000000ac e3a00c60    147 	mov	r0,3<<13

000000b0 e2800008    148 	add	r0,r0,8

000000b4 e7f02001    149 	ldrb	r2,[r0,r1]!

000000b8 e3520000    150 	cmp	r2,0

000000bc 1a000005    151 	bne	.L198

000000c0 e1a04000    152 	mov	r4,r0

000000c4 e5c46000    153 	strb	r6,[r4]

                     154 ;41:         }


                     155 ;42:     }


                     156 ;43:     CriticalSection_Unlock(&buffers->cs);


                     157 

000000c8 e1a00005    158 	mov	r0,r5

000000cc eb000000*   159 	bl	CriticalSection_Unlock

                     160 ;44:     return result;


                     161 

000000d0 e1a00004    162 	mov	r0,r4

000000d4 ea000054    163 	b	.L133

                     164 .L198:

000000d8 e3a00cc0    165 	mov	r0,3<<14

000000dc e2800010    166 	add	r0,r0,16

000000e0 e7f02001    167 	ldrb	r2,[r0,r1]!

000000e4 e3520000    168 	cmp	r2,0

000000e8 1a000005    169 	bne	.L201

000000ec e1a04000    170 	mov	r4,r0

000000f0 e5c46000    171 	strb	r6,[r4]

                     172 ;41:         }



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5m41.s
                     173 ;42:     }


                     174 ;43:     CriticalSection_Unlock(&buffers->cs);


                     175 

000000f4 e1a00005    176 	mov	r0,r5

000000f8 eb000000*   177 	bl	CriticalSection_Unlock

                     178 ;44:     return result;


                     179 

000000fc e1a00004    180 	mov	r0,r4

00000100 ea000049    181 	b	.L133

                     182 .L201:

00000104 e3a00b48    183 	mov	r0,9<<13

00000108 e2800018    184 	add	r0,r0,24

0000010c e7f02001    185 	ldrb	r2,[r0,r1]!

00000110 e3520000    186 	cmp	r2,0

00000114 1a000005    187 	bne	.L204

00000118 e1a04000    188 	mov	r4,r0

0000011c e5c46000    189 	strb	r6,[r4]

                     190 ;41:         }


                     191 ;42:     }


                     192 ;43:     CriticalSection_Unlock(&buffers->cs);


                     193 

00000120 e1a00005    194 	mov	r0,r5

00000124 eb000000*   195 	bl	CriticalSection_Unlock

                     196 ;44:     return result;


                     197 

00000128 e1a00004    198 	mov	r0,r4

0000012c ea00003e    199 	b	.L133

                     200 .L204:

00000130 e3a00b60    201 	mov	r0,3<<15

00000134 e2800020    202 	add	r0,r0,32

00000138 e7f02001    203 	ldrb	r2,[r0,r1]!

0000013c e3520000    204 	cmp	r2,0

00000140 1a000005    205 	bne	.L207

00000144 e1a04000    206 	mov	r4,r0

00000148 e5c46000    207 	strb	r6,[r4]

                     208 ;41:         }


                     209 ;42:     }


                     210 ;43:     CriticalSection_Unlock(&buffers->cs);


                     211 

0000014c e1a00005    212 	mov	r0,r5

00000150 eb000000*   213 	bl	CriticalSection_Unlock

                     214 ;44:     return result;


                     215 

00000154 e1a00004    216 	mov	r0,r4

00000158 ea000033    217 	b	.L133

                     218 .L207:

0000015c e3a00b78    219 	mov	r0,15<<13

00000160 e2800028    220 	add	r0,r0,40

00000164 e7f02001    221 	ldrb	r2,[r0,r1]!

00000168 e3520000    222 	cmp	r2,0

0000016c 1a000005    223 	bne	.L210

00000170 e1a04000    224 	mov	r4,r0

00000174 e5c46000    225 	strb	r6,[r4]

                     226 ;41:         }


                     227 ;42:     }


                     228 ;43:     CriticalSection_Unlock(&buffers->cs);


                     229 

00000178 e1a00005    230 	mov	r0,r5

0000017c eb000000*   231 	bl	CriticalSection_Unlock

                     232 ;44:     return result;


                     233 


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5m41.s
00000180 e1a00004    234 	mov	r0,r4

00000184 ea000028    235 	b	.L133

                     236 .L210:

00000188 e3a00b90    237 	mov	r0,9<<14

0000018c e2800030    238 	add	r0,r0,48

00000190 e7f02001    239 	ldrb	r2,[r0,r1]!

00000194 e3520000    240 	cmp	r2,0

00000198 1a000005    241 	bne	.L213

0000019c e1a04000    242 	mov	r4,r0

000001a0 e5c46000    243 	strb	r6,[r4]

                     244 ;41:         }


                     245 ;42:     }


                     246 ;43:     CriticalSection_Unlock(&buffers->cs);


                     247 

000001a4 e1a00005    248 	mov	r0,r5

000001a8 eb000000*   249 	bl	CriticalSection_Unlock

                     250 ;44:     return result;


                     251 

000001ac e1a00004    252 	mov	r0,r4

000001b0 ea00001d    253 	b	.L133

                     254 .L213:

000001b4 e3a00ba8    255 	mov	r0,42<<12

000001b8 e2800038    256 	add	r0,r0,56

000001bc e7f02001    257 	ldrb	r2,[r0,r1]!

000001c0 e3520000    258 	cmp	r2,0

000001c4 1a000005    259 	bne	.L216

000001c8 e1a04000    260 	mov	r4,r0

000001cc e5c46000    261 	strb	r6,[r4]

                     262 ;41:         }


                     263 ;42:     }


                     264 ;43:     CriticalSection_Unlock(&buffers->cs);


                     265 

000001d0 e1a00005    266 	mov	r0,r5

000001d4 eb000000*   267 	bl	CriticalSection_Unlock

                     268 ;44:     return result;


                     269 

000001d8 e1a00004    270 	mov	r0,r4

000001dc ea000012    271 	b	.L133

                     272 .L216:

000001e0 e3a00bc0    273 	mov	r0,3<<16

000001e4 e2800040    274 	add	r0,r0,64

000001e8 e7f02001    275 	ldrb	r2,[r0,r1]!

000001ec e3520000    276 	cmp	r2,0

000001f0 1a000005    277 	bne	.L219

000001f4 e1a04000    278 	mov	r4,r0

000001f8 e5c46000    279 	strb	r6,[r4]

                     280 ;41:         }


                     281 ;42:     }


                     282 ;43:     CriticalSection_Unlock(&buffers->cs);


                     283 

000001fc e1a00005    284 	mov	r0,r5

00000200 eb000000*   285 	bl	CriticalSection_Unlock

                     286 ;44:     return result;


                     287 

00000204 e1a00004    288 	mov	r0,r4

00000208 ea000007    289 	b	.L133

                     290 .L219:

0000020c e2810bd8    291 	add	r0,r1,54<<12

00000210 e5f01048    292 	ldrb	r1,[r0,72]!

00000214 e3510000    293 	cmp	r1,0

00000218 01a04000    294 	moveq	r4,r0


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_5m41.s
0000021c 05c46000    295 	streqb	r6,[r4]

                     296 ;41:         }


                     297 ;42:     }


                     298 ;43:     CriticalSection_Unlock(&buffers->cs);


                     299 

00000220 e1a00005    300 	mov	r0,r5

00000224 eb000000*   301 	bl	CriticalSection_Unlock

                     302 ;44:     return result;


                     303 

00000228 e1a00004    304 	mov	r0,r4

                     305 .L133:

0000022c e8bd8070    306 	ldmfd	[sp]!,{r4-r6,pc}

                     307 	.endf	allocSessionOutBuffer

                     308 	.align	4

                     309 ;result	r4	local

                     310 

                     311 ;buffers	r5	param

                     312 ;size	r1	param

                     313 

                     314 	.section ".bss","awb"

                     315 .L388:

                     316 	.data

                     317 	.text

                     318 

                     319 ;45: }


                     320 

                     321 ;46: 


                     322 ;47: void freeSessionOutBuffer(SessionOutBuffer* buf)


                     323 	.align	4

                     324 	.align	4

                     325 freeSessionOutBuffer::

                     326 ;48: {


                     327 

                     328 ;49: 	buf->busy = FALSE;


                     329 

00000230 e3a01000    330 	mov	r1,0

00000234 e5c01000    331 	strb	r1,[r0]

00000238 e12fff1e*   332 	ret	

                     333 	.endf	freeSessionOutBuffer

                     334 	.align	4

                     335 

                     336 ;buf	r0	param

                     337 

                     338 	.section ".bss","awb"

                     339 .L478:

                     340 	.data

                     341 	.text

                     342 

                     343 ;50: }


                     344 	.align	4

                     345 

                     346 	.data

                     347 	.ghsnote version,6

                     348 	.ghsnote tools,3

                     349 	.ghsnote options,0

                     350 	.text

                     351 	.align	4

