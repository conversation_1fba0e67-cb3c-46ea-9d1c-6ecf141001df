                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=goose.c -o gh_b981.o -list=goose.lst C:\Users\<USER>\AppData\Local\Temp\gh_b981.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
Source File: goose.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile goose.c -o

                      11 ;		goose.o

                      12 ;Source File:   goose.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:54 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "goose.h"


                      23 ;2: #include "bufViewBER.h"


                      24 ;3: #include "AsnEncoding.h"


                      25 ;4: #include "IEDCompile/InnerAttributeTypes.h"


                      26 ;5: #include "BaseAsnTypes.h"


                      27 ;6: #include "iedmodel.h"


                      28 ;7: #include "iedTree/iedTree.h"


                      29 ;8: #include "iedTree/DataSet.h"


                      30 ;9: #include "FinalDA.h"


                      31 ;10: #include "iedTree/iedFinalDA.h"


                      32 ;11: #include "mms_data.h"


                      33 ;12: #include "netTools.h"


                      34 ;13: #include "DataSlice.h"


                      35 ;14: #include "timers.h"


                      36 ;15: #include "platformTools.h"


                      37 ;16: #include <debug.h>


                      38 ;17: #include <string.h>


                      39 ;18: #include <stdlib.h>


                      40 ;19: #include <stdint.h>


                      41 ;20: 


                      42 ;21: 


                      43 ;22: 


                      44 ;23: #define  MAX_GO_CB_REF_SIZE 66


                      45 ;24: #define TIME_ALLOWED_TO_LIVE 4000


                      46 ;25: #define MAX_GOCB_COUNT 4


                      47 ;26: 


                      48 ;27: // Количество посылок с интервалом T1 после первой посылки изменений.


                      49 ;28: // Минимальное значение 1


                      50 ;29: #define T1_COUNT 4



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                      51 ;30: 


                      52 ;31: //Максимальное количество GSE в одном GoCB


                      53 ;32: #define MAX_GSE_COUNT 3


                      54 ;33: 


                      55 ;34: //"Context specific" тэг настроек GSE VLAN


                      56 ;35: #define BER_VLAN_INFO 0xA0


                      57 ;36: //"Context specific" тэг настроек T0 и T1


                      58 ;37: #define BER_GOOSE_INTERVALS 0xA1


                      59 ;38: 


                      60 ;39: //Maximum transfer unit


                      61 ;40: #define MTU_SIZE 1480


                      62 ;41: #define MAX_GOOSE_DA_COUNT 600


                      63 ;42: 


                      64 ;43: #define ETHTYPE_GOOSE	0x88B8


                      65 ;44: 


                      66 ;45: typedef struct VLANInfoStruct * VLANInfo;


                      67 ;46: 


                      68 ;47: typedef struct {


                      69 ;48: 	size_t ldPos;


                      70 ;49: 	size_t lnPos;


                      71 ;50: 	size_t fcPos;


                      72 ;51: 	size_t goCBPos;


                      73 ;52: } GoCBPath;


                      74 ;53: 


                      75 ;54: //Для StNum и SqNum


                      76 ;55: typedef struct


                      77 ;56: {


                      78 ;57: 	//Куда записывать


                      79 ;58: 	uint8_t* p;


                      80 ;59: 	uint32_t value;


                      81 ;60: } GOOSEPktNum;


                      82 ;61: 


                      83 ;62: struct VLANInfoStruct


                      84 ;63: {


                      85 ;64: 	bool useVLAN;


                      86 ;65: 	//Уточнить тип


                      87 ;66: 	int id;


                      88 ;67: 	//Уточнить тип


                      89 ;68: 	int priority;


                      90 ;69: };


                      91 ;70: 


                      92 ;71: 


                      93 ;72: struct GSESettingsStruct


                      94 ;73: {


                      95 ;74: 	//!Номер сетевого интерфейса


                      96 ;75: 	uint8_t ifNum;


                      97 ;76: 	void* netIf;


                      98 ;77: 	uint8_t src[6];


                      99 ;78: 	uint8_t dst[6];


                     100 ;79: 	struct VLANInfoStruct vlan;


                     101 ;80: 	uint16_t appID;	


                     102 ;81: 	uint8_t* outPkt;


                     103 ;82: 	size_t outPktSize;


                     104 ;83: 	//Указатель и размер чтобы копировать PDU между пакетами


                     105 ;84: 	uint8_t* pPDU;


                     106 ;85: 	size_t pduSize;


                     107 ;86: 	//Место в пакете, куда положить время


                     108 ;87: 	uint8_t* pPktTime;


                     109 ;88: 	//Номер пакета


                     110 ;89: 	GOOSEPktNum sqNum;


                     111 ;90: 	//Номер изменения состояния



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                     112 ;91: 	GOOSEPktNum stNum;


                     113 ;92: 	uint32_t t0;


                     114 ;93: 	uint32_t t1;


                     115 ;94: 	uint32_t currT;


                     116 ;95:     //Счётчик количества первых посылок с интервалом t1


                     117 ;96:     uint32_t t1Counter;


                     118 ;97: 	uint32_t msCounter;


                     119 ;98: };


                     120 ;99: 


                     121 ;100: struct GoCBStruct


                     122 ;101: {	


                     123 ;102: 	bool goEna;


                     124 ;103: 	StringView appID;


                     125 ;104: 	StringView dataSetName;	


                     126 ;105: 	IEDEntity dataSetEntity;


                     127 ;106: 	DataSet* dataSet;


                     128 ;107: 	bool ndsCom;


                     129 ;108:     char goCBRef[66];


                     130 ;109: 	uint16_t confRev;


                     131 ;110: 	uint32_t timeAllowedToLive;


                     132 ;111: 	size_t gseCount;


                     133 ;112: 	struct GSESettingsStruct gse[MAX_GSE_COUNT];


                     134 ;113: 	size_t daCount;


                     135 ;114: 	void* daList[MAX_GOOSE_DA_COUNT];


                     136 ;115: };


                     137 ;116: 


                     138 ;117: //Структура содержит размеры разных частей пакета GOOSE.


                     139 ;118: 


                     140 ;119: typedef struct  


                     141 ;120: {


                     142 ;121: 	//Размер пакета без заголовка Ethernet


                     143 ;122: 	uint16_t goosePktSize;


                     144 ;123: 	uint16_t pduWithTLSize;


                     145 ;124: 	uint16_t pduSize;


                     146 ;125: 	uint16_t dataTLSize;


                     147 ;126: 	uint16_t dataSize;


                     148 ;127: } GOOSESizes;


                     149 ;128: 


                     150 ;129: static size_t g_goCBCount = 0;


                     151 ;130: static struct GoCBStruct g_goCBs[MAX_GOCB_COUNT];


                     152 ;131: 


                     153 ;132: //Используется для хранения пути к GoCB при инициализации.


                     154 ;133: static GoCBPath g_goCBPath;


                     155 ;134: 


                     156 ;135: 


                     157 ;136: static void writeUlongBE(uint8_t* p, uint32_t data)


                     158 ;137: {	


                     159 ;138: 	p[0] = data >> 24;


                     160 ;139: 	p[1] = (data >> 16) & 0xFF;	


                     161 ;140: 	p[2] = (data >> 8) & 0xFF;


                     162 ;141: 	p[3] = data & 0xFF;


                     163 ;142: }


                     164 ;143: 


                     165 ;144: static GoCB getFreeGoCB(void)


                     166 

                     167 ;152: }


                     168 

                     169 ;153: 


                     170 ;154: //Пишем время в исходящий пакет GSE


                     171 ;155: static void writePktTime(GSESettings gse)


                     172 


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                     173 ;159: }


                     174 

                     175 ;160: 


                     176 ;161: 


                     177 ;162: static bool calcGOOSEDataSize(GoCB goCB, GOOSESizes* gSizes)


                     178 

                     179 ;178: }


                     180 

                     181 ;179: 


                     182 ;180: static bool calcGOOSEPDUSize(GoCB goCB, GOOSESizes* gSizes)


                     183 

                     184 ;213: }


                     185 

                     186 ;214: 


                     187 ;215: static bool calcGOOSEPktSize(GoCB goCB, GSESettings gse,GOOSESizes* gSizes)


                     188 

                     189 ;241: }


                     190 

                     191 ;242: 


                     192 ;243: static bool calcGOOSESizes(GoCB goCB, GSESettings gse, GOOSESizes* gSizes)


                     193 

                     194 ;262: }


                     195 

                     196 ;263: 


                     197 ;264: static bool writeVLANInfo(VLANInfo vlan, BufferView* gooseBuf)


                     198 

                     199 ;290: }


                     200 

                     201 ;291: 


                     202 ;292: static bool writeGOOSEHeader(GSESettings gse, GOOSESizes* gooseSizes,


                     203 

                     204 ;338: }


                     205 

                     206 ;339: 


                     207 ;340: static bool writeDataTemplate(GoCB goCB, GOOSESizes* gooseSizes, 


                     208 

                     209 ;360: }


                     210 

                     211 ;361: 


                     212 ;362: static bool writeGOOSEPduTemplate(GoCB goCB, GSESettings gse, 


                     213 

                     214 ;448: }


                     215 

                     216 ;449: 


                     217 ;450: // Подготавливает буфер GOOSE


                     218 ;451: // Должна вызываться при инициализации


                     219 ;452: bool prepareGOOSEbuf(GoCB goCB, GSESettings gse, BufferView* gooseBuf)


                     220 ;453: {	


                     221 ;454: 	GOOSESizes gooseSizes;


                     222 ;455: 


                     223 ;456: 	if (!calcGOOSESizes(goCB, gse, &gooseSizes))


                     224 ;457: 	{


                     225 ;458: 		ERROR_REPORT("Unable to calculate GOOSE sizes");


                     226 ;459: 		return FALSE;


                     227 ;460: 	}


                     228 ;461: 


                     229 ;462: 	if (!writeGOOSEHeader(gse, &gooseSizes, gooseBuf))


                     230 ;463: 	{


                     231 ;464: 		return FALSE;


                     232 ;465: 	}


                     233 ;466: 



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                     234 ;467: 	//GOOSE PDU	


                     235 ;468: 	if (!writeGOOSEPduTemplate(goCB, gse, &gooseSizes, gooseBuf))


                     236 ;469: 	{


                     237 ;470: 		return FALSE;


                     238 ;471: 	}


                     239 ;472: 	


                     240 ;473: 	return TRUE;


                     241 ;474: }


                     242 ;475: 


                     243 ;476: // Декодирует конфигурацию VLAN. Если нужный тэг не найден,


                     244 ;477: // устанавливает useVLAN = FALSE


                     245 ;478: static bool decodeVLANinfo(VLANInfo vlan, BufferView* berData)


                     246 

                     247 ;512: }


                     248 

                     249 ;513: 


                     250 ;514: bool GSE_init(GSESettings gse, BufferView* berData)


                     251 ;515: {


                     252 ;516: 	uint8_t tag;	


                     253 ;517: 	uint32_t ifNum;


                     254 ;518: 	uint32_t appID;


                     255 ;519: 	StringView dest;


                     256 ;520: 	uint32_t t0, t1;


                     257 ;521: 


                     258 ;522: 	//Тэг и длина GSE


                     259 ;523: 	if (!BufferView_decodeTL(berData, &tag, NULL, NULL))


                     260 ;524: 	{


                     261 ;525: 		ERROR_REPORT("Unable to decode GSE tag and length");


                     262 ;526: 		return FALSE;


                     263 ;527: 	}


                     264 ;528: 	if (tag != ASN_SEQUENCE)


                     265 ;529: 	{


                     266 ;530: 		return FALSE;


                     267 ;531: 	}


                     268 ;532: 


                     269 ;533: 	//Номер сетевого интерфейса


                     270 ;534: 	if (!BufferView_decodeUInt32TL(berData, ASN_INTEGER, &ifNum))


                     271 ;535: 	{


                     272 ;536: 		ERROR_REPORT("Unable to decode GSE ifNum");


                     273 ;537: 		return FALSE;


                     274 ;538: 	}


                     275 ;539: 	gse->ifNum = (uint8_t)ifNum;


                     276 ;540: 	//Сетевой интерфейс


                     277 ;541: 	if (!NetTools_getIf(gse->ifNum, &gse->netIf))


                     278 ;542: 	{


                     279 ;543: 		ERROR_REPORT("Unable to optain net interface");


                     280 ;544: 		return FALSE;


                     281 ;545: 	}


                     282 ;546: 	//Source


                     283 ;547: 	if (!NetTools_getMac(gse->netIf, gse->src))


                     284 ;548: 	{


                     285 ;549: 		ERROR_REPORT("Unable to optain interface MAC");


                     286 ;550: 		return FALSE;


                     287 ;551: 	}


                     288 ;552: 


                     289 ;553: 	//Destination


                     290 ;554: 	if (!BufferView_decodeStringViewTL(berData, ASN_OCTET_STRING, &dest)


                     291 ;555: 		|| dest.len != 6)


                     292 ;556: 	{


                     293 ;557: 		ERROR_REPORT("Unable to decode GSE destination");


                     294 ;558: 		return FALSE;



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                     295 ;559: 	}


                     296 ;560: 	memcpy(&gse->dst, dest.p, dest.len);


                     297 ;561: 


                     298 ;562: 	//VLAN info


                     299 ;563: 	if (!decodeVLANinfo(&gse->vlan, berData))


                     300 ;564: 	{


                     301 ;565: 		ERROR_REPORT("Unable to decode GSE VLAN info");


                     302 ;566: 		return FALSE;


                     303 ;567: 	}


                     304 ;568: 	//APP ID


                     305 ;569: 	if (!BufferView_decodeUInt32TL(berData, ASN_INTEGER, &appID))


                     306 ;570: 	{


                     307 ;571: 		ERROR_REPORT("Unable to decode GSE appID");


                     308 ;572: 		return FALSE;


                     309 ;573: 	}


                     310 ;574: 	gse->appID = (uint16_t)appID;


                     311 ;575: 	//Тэг и длина T info


                     312 ;576: 	if (!BufferView_decodeTL(berData, &tag, NULL, NULL) 


                     313 ;577: 		|| tag != BER_GOOSE_INTERVALS)


                     314 ;578: 	{


                     315 ;579:         ERROR_REPORT("Unable to decode GSE T0/T1 configuration");


                     316 ;580: 		return FALSE;


                     317 ;581: 	}


                     318 ;582: 


                     319 ;583: 	//T0


                     320 ;584: 	if (!BufferView_decodeUInt32TL(berData, ASN_INTEGER, &t0))


                     321 ;585: 	{


                     322 ;586: 		ERROR_REPORT("Unable to decode T0");


                     323 ;587: 		return FALSE;


                     324 ;588: 	}


                     325 ;589: 	gse->t0 = t0;


                     326 ;590: 


                     327 ;591: 	//T1


                     328 ;592: 	if (!BufferView_decodeUInt32TL(berData, ASN_INTEGER, &t1))


                     329 ;593: 	{


                     330 ;594: 		ERROR_REPORT("Unable to decode T1");


                     331 ;595: 		return FALSE;


                     332 ;596: 	}


                     333 ;597: 	gse->t1 = t1;


                     334 ;598: 


                     335 ;599: 	return TRUE;


                     336 ;600: }


                     337 ;601: 


                     338 ;602: static bool initDatSet(size_t goCBPos, GoCB goCB)


                     339 

                     340 ;626: }


                     341 

                     342 ;627: 


                     343 ;628: //Если delimiter NULL, то пишется конец стоки - 0


                     344 ;629: static bool writeObjectNameToBuf(size_t pos, char* delimiter, BufferView* buf)


                     345 ;630: {


                     346 ;631: 	StringView nameView;	


                     347 ;632: 


                     348 ;633: 	if (!getObjectName(pos, &nameView))


                     349 ;634: 	{	


                     350 ;635: 		return false;


                     351 ;636: 	}


                     352 ;637: 	if (!BufferView_writeStringView(buf, &nameView))


                     353 ;638: 	{


                     354 ;639: 		return false;


                     355 ;640: 	}



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                     356 ;641: 	if (delimiter != NULL)


                     357 ;642: 	{	


                     358 ;643: 		// Записываем разделитель


                     359 ;644: 		if (!BufferView_writeStr(buf, delimiter))


                     360 ;645: 		{


                     361 ;646: 			return false;


                     362 ;647: 		}


                     363 ;648: 		return true;


                     364 ;649: 	}


                     365 ;650: 	//Записываем конец строки


                     366 ;651: 	if (1 != BufferView_writeData(buf, "", 1))


                     367 ;652: 	{


                     368 ;653: 		return false;


                     369 ;654: 	}


                     370 ;655: 	return true;


                     371 ;656: }


                     372 ;657: 


                     373 ;658: //Заполняет goCBRef правильной строкой ASCIIZ


                     374 ;659: static bool initGoCBRef(GoCB goCB)


                     375 

                     376 ;688: }


                     377 

                     378 ;689: 


                     379 ;690: static bool initGoCBvars(GoCB goCB, size_t goCBPos)


                     380 

                     381 ;724: }


                     382 

                     383 ;725: 


                     384 ;726: static bool initGSEList(GoCB goCB, size_t goCBPos)


                     385 

                     386 ;786: }


                     387 

                     388 ;787: 


                     389 ;788: static bool initDAObjects(GoCB goCB)


                     390 

                     391 ;818: }


                     392 

                     393 ;819: 


                     394 ;820: static bool initMsgTemplates(GoCB goCB)


                     395 

                     396 ;846: }


                     397 

                     398 ;847: 


                     399 ;848: static GOOSE_resetPktCounters(GoCB goCB)


                     400 ;849: {


                     401 ;850: 	size_t gseIdx;


                     402 ;851: 	for (gseIdx = 0; gseIdx < goCB->gseCount; ++gseIdx)


                     403 ;852: 	{


                     404 ;853: 		GSESettings gse = goCB->gse + gseIdx;


                     405 ;854: 		gse->sqNum.value = 1;


                     406 ;855: 		gse->stNum.value = 1;


                     407 ;856: 	}


                     408 ;857: }


                     409 ;858: 


                     410 ;859: static GOOSE_resetPktTimers(GoCB goCB)


                     411 ;860: {


                     412 ;861: 	size_t gseIdx;


                     413 ;862: 	for (gseIdx = 0; gseIdx < goCB->gseCount; ++gseIdx)


                     414 ;863: 	{


                     415 ;864: 		GSESettings gse = goCB->gse + gseIdx;


                     416 ;865: 		gse->currT = gse->t0;



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                     417 ;866:         gse->t1Counter = 1;


                     418 ;867:         gse->msCounter = 0;


                     419 ;868: 	}


                     420 ;869: }


                     421 ;870: 


                     422 ;871: static GOOSE_writePktCounters(GSESettings gse)


                     423 

                     424 ;875: }


                     425 

                     426 ;876: 


                     427 ;877: bool GoCB_init(GoCB goCB, size_t goCBPos)


                     428 ;878: {


                     429 ;879: 	goCB->ndsCom = TRUE;


                     430 ;880: 


                     431 ;881: 	if (!initGoCBvars(goCB, goCBPos))


                     432 ;882: 	{		


                     433 ;883: 		return FALSE;


                     434 ;884: 	}


                     435 ;885: 	if (!initGSEList(goCB, goCBPos) || goCB->gseCount == 0)


                     436 ;886: 	{


                     437 ;887: 		return FALSE;


                     438 ;888: 	}


                     439 ;889: 


                     440 ;890: 	//Пока передаются только Final DA эта функция вызывается здесь.


                     441 ;891: 	//Когда надо будет передавать DO, возможно этот код должен будет


                     442 ;892: 	//выполняться в процессе создания шаблонов сообщений


                     443 ;893: 	if (!initDAObjects(goCB))


                     444 ;894: 	{


                     445 ;895: 		ERROR_REPORT("Unable to init dataset DA objects");


                     446 ;896: 		return FALSE;


                     447 ;897: 	}


                     448 ;898: 	if (!initMsgTemplates(goCB))


                     449 ;899: 	{


                     450 ;900: 		ERROR_REPORT("Unable to init GOOSE message templates");


                     451 ;901: 		return FALSE;


                     452 ;902: 	}


                     453 ;903: 


                     454 ;904: 	GOOSE_resetPktCounters(goCB);


                     455 ;905: 	GOOSE_resetPktTimers(goCB);


                     456 ;906: 


                     457 ;907: 	goCB->ndsCom = FALSE;


                     458 ;908: 	return TRUE;


                     459 ;909: }


                     460 ;910: 


                     461 ;911: /*


                     462 ;912: static BOOL_T readDATypeInfo(BufferView* encodedFinalDA, 


                     463 ;913: 	enum InnerAttributeType* outAttrType, void** accessInfo)


                     464 ;914: {


                     465 ;915: 	uint32_t attrType;


                     466 ;916: 	StringView encodedElemStruct;


                     467 ;917: 	void* daObject;


                     468 ;918: 	void* accessInfo;


                     469 ;919: 	//Какое смещение использовано для выравнивания структуры описания


                     470 ;920: 	uint8_t* pDescrStructAlignOffset;


                     471 ;921: 


                     472 ;922: 	if (!BufferView_decodeTL(encodedFinalDA))


                     473 ;923: 	{


                     474 ;924: 		ERROR_REPORT("Unable to decode Final DA");


                     475 ;925: 		return NULL;


                     476 ;926: 	}


                     477 ;927: 	//Получить тип элемента



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                     478 ;928: 	if (!BufferView_decodeUInt32TL(encodedFinalDA, ASN_INTEGER, &attrType))


                     479 ;929: 	{


                     480 ;930: 		ERROR_REPORT("Unable to decode Final DA type");


                     481 ;931: 		return NULL;


                     482 ;932: 	}


                     483 ;933: 	*outAttrType = attrType;


                     484 ;934: 


                     485 ;935: 	//Получаем структуру элемента


                     486 ;936: 	if (BufferView_decodeStringViewTL(encodedFinalDA, ASN_OCTET_STRING,


                     487 ;937: 		&encodedElemStruct))


                     488 ;938: 	{


                     489 ;939: 		ERROR_REPORT("Unable to decode Final DA struct");


                     490 ;940: 		return NULL;


                     491 ;941: 	}


                     492 ;942: 	//Указатель непосредственно на структуру	


                     493 ;943: 	pDescrStructAlignOffset = encodedElemStruct.p;


                     494 ;944: 	if (*pDescrStructAlignOffset > 3)


                     495 ;945: 	{


                     496 ;946: 		ERROR_REPORT("Invalid alignment");


                     497 ;947: 		return NULL;


                     498 ;948: 	}


                     499 ;949: 	*accessInfo = pDescrStructAlignOffset + *pDescrStructAlignOffset + 1;


                     500 ;950: 	return TRUE;


                     501 ;951: }


                     502 ;952: 


                     503 ;953: 


                     504 ;954: 


                     505 ;955: //Создаёт объект FinalDA согласно описанию из информационной модели


                     506 ;956: // encodedFinalDA.


                     507 ;957: //Одновременно генерирует шаблон для кодирования исходящих данных.


                     508 ;958: //Возвращает указатель на объект FinalDA или NULL при ошибке


                     509 ;959: void* GoCB_initFinalDA(BufferView* encodedFinalDA, BufferView* templateBuf)


                     510 ;960: {


                     511 ;961: 	InnerAttributeType attrType;


                     512 ;962: 	void* daObject;


                     513 ;963: 


                     514 ;964: 	if (readDATypeInfo())


                     515 ;965: 	{


                     516 ;966: 		return NULL;


                     517 ;967: 	}


                     518 ;968: 	


                     519 ;969: 	//Создать элемент


                     520 ;970: 	daObject = FDA_create(attrType, accessInfo);


                     521 ;971: 	if (daObject == NULL)


                     522 ;972: 	{


                     523 ;973: 		ERROR_REPORT("Unable to create Final DA object");


                     524 ;974: 		return NULL;


                     525 ;975: 	}


                     526 ;976: 


                     527 ;977: 	//Генерировать шаблон


                     528 ;978: 	if (!FDA_encodeTemplate(daObject, templateBuf))


                     529 ;979: 	{


                     530 ;980: 		ERROR_REPORT("Unable to create Final DA template");


                     531 ;981: 		return NULL;


                     532 ;982: 	}


                     533 ;983: 


                     534 ;984: 	return daObject;


                     535 ;985: }


                     536 ;986: 


                     537 ;987: //encodedChildren - буфер, содержащий закодированный список детей


                     538 ;988: BOOL_T GoCB_initDataSetIemChildren(BufferView* encodedChildren, 



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                     539 ;989: 	BufferView* templateBuf)


                     540 ;990: {


                     541 ;991: 	while (encodedChildren->pos < encodedChildren->len)


                     542 ;992: 	{


                     543 ;993: 		if (!GoCB_initDataSetItem(encodedChildren, templateBuf, daList, pDaListPos,


                     544 ;994: 			maxDaListPos))


                     545 ;995: 		{


                     546 ;996: 			return FALSE;


                     547 ;997: 		}


                     548 ;998: 	}


                     549 ;999: 	return TRUE;


                     550 ;1000: }


                     551 ;1001: 


                     552 ;1002: 


                     553 ;1003: //Рекурсивно обходит элемент набора данных, создавая список FinalDA и 


                     554 ;1004: //одновременно генерирует шаблон для кодирования исходящих данных.


                     555 ;1005: //


                     556 ;1006: //Шаблон это полноценно закодированный элемент набора данных, но на месте


                     557 ;1007: //значений - зарезервированное пустое место, в которое потом можно вписать 


                     558 ;1008: //фактическое занчение. Для значений переменной длины должно быть


                     559 ;1009: //зарезервировано максимальное разумное значение


                     560 ;1010: 


                     561 ;1011: BOOL_T GoCB_initDataSetItem(BufferView* encodedItem, BufferView* templateBuf,


                     562 ;1012: 	void** daList, size_t* pDaListPos, size_t maxDaListPos)


                     563 ;1013: {


                     564 ;1014: 	uint8_t objTag;


                     565 ;1015: 


                     566 ;1016: 	if (*pDaListPos == maxDaListPos)


                     567 ;1017: 	{


                     568 ;1018: 		ERROR_REPORT("Too many DA in the dataset");


                     569 ;1019: 		return FALSE;


                     570 ;1020: 	}


                     571 ;1021: 	if (!BufferView_peekTag(&objTag))


                     572 ;1022: 	{


                     573 ;1023: 		ERROR_REPORT("Unable to read tag");


                     574 ;1024: 		return FALSE;


                     575 ;1025: 	}


                     576 ;1026: 


                     577 ;1027: 	if (objTag == IED_DA_FINAL)


                     578 ;1028: 	{


                     579 ;1029: 		void* daObject = GoCB_initFinalDA(encodedItem, templateBuf);


                     580 ;1030: 		if (daObject == NULL)


                     581 ;1031: 		{


                     582 ;1032: 			return FALSE;


                     583 ;1033: 		}


                     584 ;1034: 		daList[(*pDaListPos)] = daObject;


                     585 ;1035: 		(*pDaListPos)++;


                     586 ;1036: 	}


                     587 ;1037: 	else


                     588 ;1038: 	{


                     589 ;1039: 		BufferView subObjects;


                     590 ;1040: 		//Получаем указатель на список детей и размер


                     591 ;1041: 		if (!getSubObjectsBufView(encodedItem, &subObjects))


                     592 ;1042: 		{			


                     593 ;1043: 			ERROR_REPORT("Unable to get subobjects");


                     594 ;1044: 			return FALSE;


                     595 ;1045: 		}


                     596 ;1046: 		if (!BufferView_decodeTL())


                     597 ;1047: 		{


                     598 ;1048: 			ERROR_REPORT("Unable to decode tag and length");


                     599 ;1049: 			return FALSE;



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                     600 ;1050: 		}


                     601 ;1051: 		//Пишем Sequence и размер


                     602 ;1052: 		!!!


                     603 ;1053: 		//Цикл по детям


                     604 ;1054: 		if (!GoCB_initDataSetIemChildren())


                     605 ;1055: 		{


                     606 ;1056: 			return FALSE;


                     607 ;1057: 		}


                     608 ;1058: 	}


                     609 ;1059: }


                     610 ;1060: */


                     611 ;1061: 


                     612 ;1062: static void registerGoCB(int goCBPos)


                     613 ;1063: {		


                     614 ;1064: 	GoCB goCB = getFreeGoCB();


                     615 ;1065: 	g_goCBPath.goCBPos = goCBPos;


                     616 ;1066: 	TRACE("GoCB is found at 0x%04X", goCBPos);


                     617 ;1067: 	if (goCB == NULL)


                     618 ;1068: 	{


                     619 ;1069: 		ERROR_REPORT("Unable to register GoCB: too many GoCBs");


                     620 ;1070: 		return;


                     621 ;1071: 	}


                     622 ;1072: 	if (!GoCB_init(goCB, goCBPos))


                     623 ;1073: 	{


                     624 ;1074: 		ERROR_REPORT("Error initializing GoCB");


                     625 ;1075: 	}


                     626 ;1076: }


                     627 ;1077: 


                     628 ;1078: 


                     629 ;1079: // Регистрирует все GoCB, которые найдёт в указанном объекте FC


                     630 ;1080: // если FC "GO". Если FC другой, то не делает ничего


                     631 ;1081: void registerGoCBsGivenFC(int fcPos)


                     632 ;1082: {


                     633 ;1083: 	StringView fcName;


                     634 ;1084: 


                     635 ;1085: 	g_goCBPath.fcPos = fcPos;


                     636 ;1086: 


                     637 ;1087: 	if (!getObjectName(fcPos, &fcName))


                     638 ;1088: 	{


                     639 ;1089: 		ERROR_REPORT("Unable to read FC name");


                     640 ;1090: 		return;


                     641 ;1091: 	}


                     642 ;1092: 	if (fcName.len != 2)


                     643 ;1093: 	{


                     644 ;1094: 		ERROR_REPORT("Invalid FC name");


                     645 ;1095: 		return;


                     646 ;1096: 	}


                     647 ;1097: 	if (memcmp("GO", fcName.p, 2) == 0)


                     648 ;1098: 	{		


                     649 ;1099: 		processSubobjects(fcPos, registerGoCB);


                     650 ;1100: 	}	


                     651 ;1101: }


                     652 ;1102: 


                     653 ;1103: static void registerAllLogicalNodeGoCB(int lnPos)


                     654 ;1104: {


                     655 ;1105: 	g_goCBPath.lnPos = lnPos;


                     656 ;1106: 	processSubobjects(lnPos, registerGoCBsGivenFC);


                     657 ;1107: }


                     658 ;1108: 


                     659 ;1109: static void registerAllLogicalDeviceGoCB(int ldPos)


                     660 ;1110: {



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                     661 ;1111: 	int dataSectionPos;


                     662 ;1112: 


                     663 ;1113: 	g_goCBPath.ldPos = ldPos;


                     664 ;1114: 	dataSectionPos = findObjectByTag(ldPos, IED_VMD_DATA_SECTION);


                     665 ;1115: 	if (!dataSectionPos)


                     666 ;1116: 	{


                     667 ;1117: 		ERROR_REPORT("Data section is not found");


                     668 ;1118: 		return;


                     669 ;1119: 	}


                     670 ;1120: 


                     671 ;1121: 	processSubobjects(dataSectionPos, registerAllLogicalNodeGoCB);


                     672 ;1122: }


                     673 ;1123: 


                     674 ;1124: static void registerAllGoCB(void)


                     675 

                     676 ;1127: }


                     677 

                     678 ;1128: 


                     679 ;1129: void GOOSE_send(GSESettings gse)


                     680 ;1130: {


                     681 ;1131: 	GOOSE_writePktCounters(gse);


                     682 ;1132: 	gse->sqNum.value++;


                     683 ;1133: 	NetTools_send(gse->netIf, gse->outPkt, gse->outPktSize);


                     684 ;1134: }


                     685 ;1135: 


                     686 ;1136: static bool GOOSE_readAndDetectChange(GoCB goCB, void* dataSliceWnd)


                     687 

                     688 ;1152: }


                     689 

                     690 ;1153: 


                     691 ;1154: static void GOOSE_writeDataToFirstGSEBuf(GoCB goCB)


                     692 

                     693 ;1161: 	}


                     694 ;1162: }


                     695 

                     696 ;1163: 


                     697 ;1164: //Копирует PDU пакета


                     698 ;1165: static void copyGSEPPDU(GSESettings gseSrc, GSESettings gseDst)


                     699 

                     700 ;1168: }


                     701 

                     702 ;1169: 


                     703 ;1170: void GOOSE_sendChanges(void)


                     704 ;1171: {


                     705 ;1172: 	GSESettings gse;


                     706 ;1173: 	size_t goCBIdx;


                     707 ;1174: 	size_t gseIdx;


                     708 ;1175:     void* dataSliceWnd;


                     709 ;1176:     int interruptState = PTools_lockInterrupt();    


                     710 ;1177:     dataSliceWnd = DataSlice_getDataSliceWnd();


                     711 ;1178: 	for (goCBIdx = 0; goCBIdx < g_goCBCount; ++goCBIdx)


                     712 ;1179: 	{


                     713 ;1180: 		GoCB goCB = g_goCBs + goCBIdx;


                     714 ;1181: 		


                     715 ;1182: 		if (goCB->goEna && !goCB->ndsCom)


                     716 ;1183: 		{


                     717 ;1184:             if (GOOSE_readAndDetectChange(goCB, dataSliceWnd))


                     718 ;1185: 			{


                     719 ;1186: 				//Одинаковые данные для всех GSE


                     720 ;1187: 				gse = goCB->gse;


                     721 ;1188: 				writePktTime(gse);



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                     722 ;1189: 				GOOSE_writeDataToFirstGSEBuf(goCB);


                     723 ;1190: 				gse->stNum.value++;


                     724 ;1191: 				gse->sqNum.value = 1;


                     725 ;1192: 


                     726 ;1193: 				for (gseIdx = 0; gseIdx < goCB->gseCount; ++gseIdx)


                     727 ;1194: 				{


                     728 ;1195: 					gse = goCB->gse + gseIdx;


                     729 ;1196: 					


                     730 ;1197: 					if (gseIdx > 0)


                     731 ;1198: 					{						


                     732 ;1199: 						copyGSEPPDU(goCB->gse, gse);


                     733 ;1200: 					}


                     734 ;1201: 


                     735 ;1202: 					gse->currT = gse->t1;


                     736 ;1203: 					gse->msCounter = 0;


                     737 ;1204:                     gse->t1Counter = T1_COUNT;


                     738 ;1205: 					GOOSE_send(gse);


                     739 ;1206: 				}


                     740 ;1207: 			}


                     741 ;1208: 		}


                     742 ;1209: 	}


                     743 ;1210:     PTools_unlockInterrupt(interruptState);


                     744 ;1211: }


                     745 ;1212: 


                     746 ;1213: static void processGSETimer(GSESettings gse)


                     747 

                     748 ;1233:             }


                     749 ;1234:         }


                     750 ;1235: 	}	


                     751 ;1236: }


                     752 

                     753 	.text

                     754 	.align	4

                     755 writeUlongBE:

00000000 e1a02c21    756 	mov	r2,r1 lsr 24

00000004 e5c02000    757 	strb	r2,[r0]

00000008 e1a02821    758 	mov	r2,r1 lsr 16

0000000c e5c02001    759 	strb	r2,[r0,1]

00000010 e1a02421    760 	mov	r2,r1 lsr 8

00000014 e5c02002    761 	strb	r2,[r0,2]

00000018 e5c01003    762 	strb	r1,[r0,3]

0000001c e12fff1e*   763 	ret	

                     764 	.endf	writeUlongBE

                     765 	.align	4

                     766 

                     767 ;p	r0	param

                     768 ;data	r1	param

                     769 

                     770 	.section ".bss","awb"

                     771 .L814:

                     772 	.data

                     773 	.text

                     774 

                     775 

                     776 	.align	4

                     777 	.align	4

                     778 	.align	4

                     779 prepareGOOSEbuf::

00000020 e92d4cf0    780 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                     781 ;244: {	


                     782 


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                     783 ;245: 	if (!calcGOOSEDataSize(goCB, gSizes))


                     784 

                     785 ;163: {	


                     786 

00000024 e1a0b001    787 	mov	fp,r1

00000028 e1a05002    788 	mov	r5,r2

0000002c e1a06000    789 	mov	r6,r0

00000030 e286af61    790 	add	r10,r6,0x0184

00000034 e3a04000    791 	mov	r4,0

                     792 ;164: 	size_t daIdx;


                     793 ;165: 	uint16_t totalSize = 0;


                     794 

                     795 ;166: 	for (daIdx = 0; daIdx < goCB->daCount; ++daIdx)


                     796 

00000038 e24dd020    797 	sub	sp,sp,32

0000003c e5960180    798 	ldr	r0,[r6,384]

00000040 e1a07004    799 	mov	r7,r4

00000044 e1570000    800 	cmp	r7,r0

00000048 2a00000c    801 	bhs	.L846

                     802 .L828:

0000004c e49a0004    803 	ldr	r0,[r10],4

00000050 e1a0100d    804 	mov	r1,sp

00000054 eb000000*   805 	bl	FDA_getFixedEncodedSize

                     806 ;167: 	{


                     807 

                     808 ;168: 		size_t daSize;


                     809 ;169: 		if (!FDA_getFixedEncodedSize(goCB->daList[daIdx], &daSize))


                     810 

00000058 e3500000    811 	cmp	r0,0

0000005c 0a0000e3    812 	beq	.L870

                     813 ;170: 		{


                     814 

                     815 ;171: 			return FALSE;


                     816 

                     817 ;172: 		}


                     818 ;173: 		totalSize += (uint16_t)daSize;


                     819 

00000060 e1dd00b0    820 	ldrh	r0,[sp]

00000064 e2877001    821 	add	r7,r7,1

00000068 e0844000    822 	add	r4,r4,r0

0000006c e1a04804    823 	mov	r4,r4 lsl 16

00000070 e5960180    824 	ldr	r0,[r6,384]

00000074 e1a04824    825 	mov	r4,r4 lsr 16

00000078 e1570000    826 	cmp	r7,r0

0000007c 3afffff2    827 	blo	.L828

                     828 .L846:

00000080 e1cd41bc    829 	strh	r4,[sp,28]

00000084 e1a00004    830 	mov	r0,r4

00000088 eb000000*   831 	bl	BerEncoder_determineFullObjectSize

0000008c e1a0a000    832 	mov	r10,r0

00000090 e1cda1ba    833 	strh	r10,[sp,26]

00000094 e286001d    834 	add	r0,r6,29

00000098 eb000000*   835 	bl	strlen

0000009c eb000000*   836 	bl	BerEncoder_determineFullObjectSize

000000a0 e1a07000    837 	mov	r7,r0

000000a4 e5960064    838 	ldr	r0,[r6,100]

000000a8 eb000000*   839 	bl	BerEncoder_UInt32determineEncodedSize

000000ac eb000000*   840 	bl	BerEncoder_determineFullObjectSize

000000b0 e0877000    841 	add	r7,r7,r0

000000b4 e596000c    842 	ldr	r0,[r6,12]

000000b8 eb000000*   843 	bl	BerEncoder_determineFullObjectSize


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
000000bc e0877000    844 	add	r7,r7,r0

000000c0 e5960004    845 	ldr	r0,[r6,4]

000000c4 eb000000*   846 	bl	BerEncoder_determineFullObjectSize

000000c8 e0877000    847 	add	r7,r7,r0

000000cc e1d606b0    848 	ldrh	r0,[r6,96]

000000d0 e2877019    849 	add	r7,r7,25

000000d4 eb000000*   850 	bl	BerEncoder_UInt32determineEncodedSize

000000d8 eb000000*   851 	bl	BerEncoder_determineFullObjectSize

000000dc e0877000    852 	add	r7,r7,r0

000000e0 e5960180    853 	ldr	r0,[r6,384]

000000e4 e2877003    854 	add	r7,r7,3

000000e8 eb000000*   855 	bl	BerEncoder_UInt32determineEncodedSize

000000ec eb000000*   856 	bl	BerEncoder_determineFullObjectSize

000000f0 e0877000    857 	add	r7,r7,r0

000000f4 e08aa007    858 	add	r10,r10,r7

000000f8 e1cda1b8    859 	strh	r10,[sp,24]

000000fc e1a0a80a    860 	mov	r10,r10 lsl 16

00000100 e1a0a82a    861 	mov	r10,r10 lsr 16

00000104 e1a0000a    862 	mov	r0,r10

00000108 eb000000*   863 	bl	BerEncoder_determineFullObjectSize

0000010c e1a07800    864 	mov	r7,r0 lsl 16

00000110 e1a07827    865 	mov	r7,r7 lsr 16

00000114 e1cd71b6    866 	strh	r7,[sp,22]

00000118 e28b100e    867 	add	r1,fp,14

0000011c e2870008    868 	add	r0,r7,8

00000120 e1cd00ba    869 	strh	r0,[sp,10]

00000124 e1cd01b4    870 	strh	r0,[sp,20]

                     871 ;174: 	}


                     872 ;175: 	gSizes->dataSize = totalSize;


                     873 

                     874 ;176: 	gSizes->dataTLSize = BerEncoder_determineFullObjectSize(gSizes->dataSize);


                     875 

                     876 ;177: 	return TRUE;


                     877 

                     878 ;246: 	{


                     879 

                     880 ;247: 		ERROR_REPORT("Unable to calculate GOOSE data size");


                     881 ;248: 		return FALSE;


                     882 

                     883 ;249: 	}


                     884 ;250: 	if (!calcGOOSEPDUSize(goCB, gSizes))


                     885 

                     886 ;181: {


                     887 

                     888 ;182: 	uint16_t totalSize = 0;	


                     889 

                     890 ;183: 	//gocbRef, String


                     891 ;184: 	totalSize += BerEncoder_determineFullObjectSize(strlen(goCB->goCBRef));


                     892 

                     893 ;185: 	//timeAlowedToLive, INT32U, ms


                     894 ;186: 	totalSize += BerEncoder_determineFullObjectSize(


                     895 

                     896 ;187: 		BerEncoder_UInt32determineEncodedSize(goCB->timeAllowedToLive));


                     897 ;188: 	//datSet, String


                     898 ;189: 	totalSize += BerEncoder_determineFullObjectSize(goCB->dataSetName.len);


                     899 

                     900 ;190: 	//goID, String


                     901 ;191: 	totalSize += BerEncoder_determineFullObjectSize(goCB->appID.len);


                     902 

                     903 ;192: 	//t, TimeStamp


                     904 ;193: 	totalSize += 10;



                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                     905 

                     906 ;194: 	//stNum, INT32U


                     907 ;195: 	totalSize += 6;


                     908 

                     909 ;196: 	//sqNum, INT32U


                     910 ;197: 	totalSize += 6;


                     911 

                     912 ;198: 	//test, bool


                     913 ;199: 	totalSize += 3;


                     914 

                     915 ;200: 	//confRev, INT32U


                     916 ;201: 	totalSize += BerEncoder_determineFullObjectSize(


                     917 

                     918 ;202: 		BerEncoder_UInt32determineEncodedSize(goCB->confRev));


                     919 ;203: 	//ndsCom


                     920 ;204: 	totalSize += 3;


                     921 

                     922 ;205: 	//numDatSetEntries


                     923 ;206: 	totalSize += BerEncoder_determineFullObjectSize(


                     924 

                     925 ;207: 		BerEncoder_UInt32determineEncodedSize(


                     926 ;208: 			goCB->daCount));


                     927 ;209: 	


                     928 ;210: 	gSizes->pduSize = totalSize + gSizes->dataTLSize;


                     929 

                     930 ;211: 	gSizes->pduWithTLSize = BerEncoder_determineFullObjectSize(gSizes->pduSize);


                     931 

                     932 ;212: 	return TRUE;


                     933 

                     934 ;251: 	{


                     935 

                     936 ;252: 		ERROR_REPORT("Unable to calculate GOOSE PDU size");


                     937 ;253: 		return FALSE;


                     938 

                     939 ;254: 	}		


                     940 ;255: 	//goosePktSize;			


                     941 ;256: 	if (!calcGOOSEPktSize(goCB, gse, gSizes))


                     942 

                     943 ;216: {


                     944 

                     945 ;217: 	uint16_t totalSize = 0;


                     946 

                     947 ;218: 	/*


                     948 ;219: 	//Destination	


                     949 ;220: 	totalSize += 6;


                     950 ;221: 	//Source	


                     951 ;222: 	totalSize += 6;


                     952 ;223: 	//VLAN	


                     953 ;224: 	if (gse->vlan.useVLAN)


                     954 ;225: 	{


                     955 ;226: 		totalSize += 4;


                     956 ;227: 	}


                     957 ;228: 	//Ethernet Type	


                     958 ;229: 	totalSize += 2;


                     959 ;230: 	*/


                     960 ;231: 	//APP ID


                     961 ;232: 	totalSize += 2;


                     962 

                     963 ;233: 	//Length	


                     964 ;234: 	totalSize += 2;


                     965 


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                     966 ;235: 	//Reserved1


                     967 ;236: 	totalSize += 2;


                     968 

                     969 ;237: 	//Reserved2


                     970 ;238: 	totalSize += 2;


                     971 

                     972 ;239: 	gSizes->goosePktSize = totalSize + gSizes->pduWithTLSize;


                     973 

                     974 ;240: 	return TRUE;


                     975 

                     976 ;257: 	{


                     977 

                     978 ;258: 		ERROR_REPORT("Unable to calculate full GOOSE packet size");


                     979 ;259: 		return FALSE;


                     980 

                     981 ;260: 	}


                     982 ;261: 	return TRUE;


                     983 

                     984 ;293: 	BufferView* gooseBuf)


                     985 ;294: {


                     986 

                     987 ;295: 	//Destination


                     988 ;296: 	if (BufferView_writeData(gooseBuf, gse->dst, 6) < 6)


                     989 

00000128 e1a00005    990 	mov	r0,r5

0000012c e3a02006    991 	mov	r2,6

00000130 eb000000*   992 	bl	BufferView_writeData

00000134 e3500006    993 	cmp	r0,6

00000138 3a0000ac    994 	blo	.L870

                     995 ;297: 	{


                     996 

                     997 ;298: 		return FALSE;


                     998 

                     999 ;299: 	}


                    1000 ;300: 	//Source


                    1001 ;301: 	if (BufferView_writeData(gooseBuf, gse->src, 6) < 6)


                    1002 

0000013c e28b1008   1003 	add	r1,fp,8

00000140 e1a00005   1004 	mov	r0,r5

00000144 e3a02006   1005 	mov	r2,6

00000148 eb000000*  1006 	bl	BufferView_writeData

0000014c e3500006   1007 	cmp	r0,6

00000150 3a0000a6   1008 	blo	.L870

                    1009 ;302: 	{


                    1010 

                    1011 ;303: 		return FALSE;


                    1012 

                    1013 ;304: 	}


                    1014 ;305: 


                    1015 ;306: 	//VLAN


                    1016 ;307: 	if (!writeVLANInfo(&gse->vlan, gooseBuf))


                    1017 

                    1018 ;265: {


                    1019 

                    1020 ;266: 	if (vlan->useVLAN)


                    1021 

00000154 e5db0014   1022 	ldrb	r0,[fp,20]

00000158 e3500000   1023 	cmp	r0,0

0000015c 0a000014   1024 	beq	.L857

                    1025 ;267: 	{


                    1026 


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    1027 ;268: 		unsigned char vlanBuf[4];


                    1028 ;269: 		uint8_t tci1 = vlan->priority << 5;


                    1029 

00000160 e59b001c   1030 	ldr	r0,[fp,28]

00000164 e1a01280   1031 	mov	r1,r0 lsl 5

                    1032 ;270: 		uint8_t tci2 = vlan->id % 256;


                    1033 

00000168 e59b0018   1034 	ldr	r0,[fp,24]

0000016c e1a02fc0   1035 	mov	r2,r0 asr 31

00000170 e0803002   1036 	add	r3,r0,r2

00000174 e3c330ff   1037 	bic	r3,r3,255

00000178 e0403003   1038 	sub	r3,r0,r3

                    1039 ;271: 		tci1 += vlan->id / 256;


                    1040 

0000017c e5cd3007   1041 	strb	r3,[sp,7]

                    1042 ;281: 


                    1043 ;282: 		if (BufferView_writeData(gooseBuf, vlanBuf, sizeof(vlanBuf)) < sizeof(vlanBuf))


                    1044 

00000180 e0800c22   1045 	add	r0,r0,r2 lsr 24

00000184 e0811440   1046 	add	r1,r1,r0 asr 8

                    1047 ;272: 


                    1048 ;273: 		// идентификатор vlan


                    1049 ;274: 		vlanBuf[0] = 0x81;


                    1050 

00000188 e5cd1006   1051 	strb	r1,[sp,6]

                    1052 ;279: 		// id


                    1053 ;280: 		vlanBuf[3] = tci2; 


                    1054 

0000018c e28d1004   1055 	add	r1,sp,4

00000190 e3a00081   1056 	mov	r0,129

00000194 e5cd0004   1057 	strb	r0,[sp,4]

                    1058 ;275: 		vlanBuf[1] = 0x00;


                    1059 

00000198 e3a00000   1060 	mov	r0,0

0000019c e5cd0005   1061 	strb	r0,[sp,5]

                    1062 ;276: 


                    1063 ;277: 		// priority + id


                    1064 ;278: 		vlanBuf[2] = tci1; 


                    1065 

000001a0 e1a00005   1066 	mov	r0,r5

000001a4 e3a02004   1067 	mov	r2,4

000001a8 eb000000*  1068 	bl	BufferView_writeData

000001ac e3500004   1069 	cmp	r0,4

000001b0 3a00008e   1070 	blo	.L870

                    1071 .L857:

                    1072 ;283: 		{


                    1073 

                    1074 ;284: 			return FALSE;


                    1075 

                    1076 ;285: 		}


                    1077 ;286: 


                    1078 ;287: 		return TRUE;


                    1079 

                    1080 ;288: 	}


                    1081 ;289: 	return TRUE;


                    1082 

                    1083 ;308: 	{


                    1084 

                    1085 ;309: 		return FALSE;


                    1086 

                    1087 ;310: 	}



                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    1088 ;311: 


                    1089 ;312: 	//Ethernet Type


                    1090 ;313: 	if (!BufferView_writeUshortBE(gooseBuf, ETHTYPE_GOOSE))


                    1091 

000001b4 e3a01c88   1092 	mov	r1,34<<10

000001b8 e28110b8   1093 	add	r1,r1,184

000001bc e1a00005   1094 	mov	r0,r5

000001c0 eb000000*  1095 	bl	BufferView_writeUshortBE

000001c4 e3500000   1096 	cmp	r0,0

000001c8 0a000088   1097 	beq	.L870

                    1098 ;314: 	{


                    1099 

                    1100 ;315: 		return FALSE;


                    1101 

                    1102 ;316: 	}


                    1103 ;317: 


                    1104 ;318: 	//APP ID


                    1105 ;319: 	if (!BufferView_writeUshortBE(gooseBuf, gse->appID))


                    1106 

000001cc e1db12b0   1107 	ldrh	r1,[fp,32]

000001d0 e1a00005   1108 	mov	r0,r5

000001d4 eb000000*  1109 	bl	BufferView_writeUshortBE

000001d8 e3500000   1110 	cmp	r0,0

000001dc 0a000083   1111 	beq	.L870

                    1112 ;320: 	{


                    1113 

                    1114 ;321: 		return FALSE;


                    1115 

                    1116 ;322: 	}


                    1117 ;323: 


                    1118 ;324: 	


                    1119 ;325: 	//Это длина всего пакета GOOSE без Ehthernet-заголовка


                    1120 ;326: 	if (!BufferView_writeUshortBE(gooseBuf, gooseSizes->goosePktSize))


                    1121 

000001e0 e1dd10ba   1122 	ldrh	r1,[sp,10]

000001e4 e1a00005   1123 	mov	r0,r5

000001e8 eb000000*  1124 	bl	BufferView_writeUshortBE

000001ec e3500000   1125 	cmp	r0,0

000001f0 0a00007e   1126 	beq	.L870

                    1127 ;327: 	{


                    1128 

                    1129 ;328: 		return FALSE;


                    1130 

                    1131 ;329: 	}


                    1132 ;330: 


                    1133 ;331: 	//Reserved


                    1134 ;332: 	if (!BufferView_writeUshortBE(gooseBuf, 0) ||


                    1135 

000001f4 e1a00005   1136 	mov	r0,r5

000001f8 e3a01000   1137 	mov	r1,0

000001fc eb000000*  1138 	bl	BufferView_writeUshortBE

00000200 e3500000   1139 	cmp	r0,0

00000204 0a000079   1140 	beq	.L870

00000208 e1a00005   1141 	mov	r0,r5

0000020c e3a01000   1142 	mov	r1,0

00000210 eb000000*  1143 	bl	BufferView_writeUshortBE

00000214 e3500000   1144 	cmp	r0,0

00000218 0a000074   1145 	beq	.L870

                    1146 ;333: 		!BufferView_writeUshortBE(gooseBuf, 0))


                    1147 ;334: 	{


                    1148 


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    1149 ;335: 		return FALSE;


                    1150 

                    1151 ;336: 	}


                    1152 ;337: 	return TRUE;


                    1153 

                    1154 ;363: 	GOOSESizes* gooseSizes, BufferView* templateBuf)


                    1155 ;364: {


                    1156 

0000021c e59f0da8*  1157 	ldr	r0,.L1657

00000220 e5902000   1158 	ldr	r2,[r0]

00000224 e58d200c   1159 	str	r2,[sp,12]

00000228 e5900004   1160 	ldr	r0,[r0,4]

0000022c e58d0010   1161 	str	r0,[sp,16]

                    1162 ;365: 	uint8_t dummyTime[8] = { 0 };


                    1163 

                    1164 ;366: 


                    1165 ;367: 	//Указатель на PDU чтобы потом копировать


                    1166 ;368: 	gse->pPDU = templateBuf->p + templateBuf->pos;


                    1167 

00000230 e8950006   1168 	ldmfd	[r5],{r1-r2}

00000234 e58b7030   1169 	str	r7,[fp,48]

                    1170 ;370: 


                    1171 ;371: 	if (!BufferView_encodeTL(templateBuf, 0x61, gooseSizes->pduSize))


                    1172 

00000238 e0820001   1173 	add	r0,r2,r1

0000023c e1a0200a   1174 	mov	r2,r10

00000240 e58b002c   1175 	str	r0,[fp,44]

                    1176 ;369: 	gse->pduSize = gooseSizes->pduWithTLSize;


                    1177 

00000244 e1a00005   1178 	mov	r0,r5

00000248 e3a01061   1179 	mov	r1,97

0000024c eb000000*  1180 	bl	BufferView_encodeTL

00000250 e3500000   1181 	cmp	r0,0

00000254 0a000065   1182 	beq	.L870

                    1183 ;372: 	{


                    1184 

                    1185 ;373: 		return FALSE;


                    1186 

                    1187 ;374: 	}


                    1188 ;375: 


                    1189 ;376: 	//gocbRef	


                    1190 ;377: 	if (!BufferView_encodeStr(templateBuf, 0x80, goCB->goCBRef))


                    1191 

00000258 e286201d   1192 	add	r2,r6,29

0000025c e1a00005   1193 	mov	r0,r5

00000260 e3a01080   1194 	mov	r1,128

00000264 eb000000*  1195 	bl	BufferView_encodeStr

00000268 e3500000   1196 	cmp	r0,0

0000026c 0a00005f   1197 	beq	.L870

                    1198 ;378: 	{


                    1199 

                    1200 ;379: 		return FALSE;


                    1201 

                    1202 ;380: 	}	


                    1203 ;381: 	//timeAllowedToLive (int)


                    1204 ;382: 	if (!BufferView_encodeUInt32(templateBuf, 0x81, goCB->timeAllowedToLive))


                    1205 

00000270 e5962064   1206 	ldr	r2,[r6,100]

00000274 e1a00005   1207 	mov	r0,r5

00000278 e3a01081   1208 	mov	r1,129

0000027c eb000000*  1209 	bl	BufferView_encodeUInt32


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
00000280 e3500000   1210 	cmp	r0,0

00000284 0a000059   1211 	beq	.L870

                    1212 ;383: 	{


                    1213 

                    1214 ;384: 		return FALSE;


                    1215 

                    1216 ;385: 	}


                    1217 ;386: 	//datSet


                    1218 ;387: 	if (!BufferView_encodeStringView(templateBuf, 0x82, &goCB->dataSetName))


                    1219 

00000288 e286200c   1220 	add	r2,r6,12

0000028c e1a00005   1221 	mov	r0,r5

00000290 e3a01082   1222 	mov	r1,130

00000294 eb000000*  1223 	bl	BufferView_encodeStringView

00000298 e3500000   1224 	cmp	r0,0

0000029c 0a000053   1225 	beq	.L870

                    1226 ;388: 	{


                    1227 

                    1228 ;389: 		return FALSE;


                    1229 

                    1230 ;390: 	}


                    1231 ;391: 	//goID


                    1232 ;392: 	if (!BufferView_encodeStringView(templateBuf, 0x83, &goCB->appID))


                    1233 

000002a0 e2862004   1234 	add	r2,r6,4

000002a4 e1a00005   1235 	mov	r0,r5

000002a8 e3a01083   1236 	mov	r1,131

000002ac eb000000*  1237 	bl	BufferView_encodeStringView

000002b0 e3500000   1238 	cmp	r0,0

000002b4 0a00004d   1239 	beq	.L870

                    1240 ;393: 	{


                    1241 

                    1242 ;394: 		return FALSE;


                    1243 

                    1244 ;395: 	}


                    1245 ;396: 	//t


                    1246 ;397: 	//Запоминаем куда потом писать время


                    1247 ;398: 	gse->pPktTime = templateBuf->p + templateBuf->pos;


                    1248 

000002b8 e8950006   1249 	ldmfd	[r5],{r1-r2}

000002bc e3a03008   1250 	mov	r3,8

000002c0 e0820001   1251 	add	r0,r2,r1

000002c4 e28d200c   1252 	add	r2,sp,12

000002c8 e58b0034   1253 	str	r0,[fp,52]

                    1254 ;399: 	if (!BufferView_encodeOctetString(templateBuf, 0x84, dummyTime, 8))


                    1255 

000002cc e1a00005   1256 	mov	r0,r5

000002d0 e3a01084   1257 	mov	r1,132

000002d4 eb000000*  1258 	bl	BufferView_encodeOctetString

000002d8 e3500000   1259 	cmp	r0,0

000002dc 0a000043   1260 	beq	.L870

                    1261 ;400: 	{


                    1262 

                    1263 ;401: 		return FALSE;


                    1264 

                    1265 ;402: 	}	


                    1266 ;403: 	//stNum


                    1267 ;404: 	//Запоминаем позицию, куда потом вписывать stNum


                    1268 ;405: 	gse->stNum.p = templateBuf->p + templateBuf->pos + 2;


                    1269 

000002e0 e8950006   1270 	ldmfd	[r5],{r1-r2}


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
000002e4 e3a03004   1271 	mov	r3,4

000002e8 e0820001   1272 	add	r0,r2,r1

000002ec e28d200c   1273 	add	r2,sp,12

000002f0 e2800002   1274 	add	r0,r0,2

000002f4 e58b0040   1275 	str	r0,[fp,64]

                    1276 ;406: 	if (!BufferView_encodeOctetString(templateBuf, 0x85, dummyTime, 4))


                    1277 

000002f8 e1a00005   1278 	mov	r0,r5

000002fc e3a01085   1279 	mov	r1,133

00000300 eb000000*  1280 	bl	BufferView_encodeOctetString

00000304 e3500000   1281 	cmp	r0,0

00000308 0a000038   1282 	beq	.L870

                    1283 ;407: 	{


                    1284 

                    1285 ;408: 		return FALSE;


                    1286 

                    1287 ;409: 	}	


                    1288 ;410: 	//sqNum	


                    1289 ;411: 	//Запоминаем позицию, куда потом вписывать sqNum


                    1290 ;412: 	gse->sqNum.p = templateBuf->p + templateBuf->pos + 2;


                    1291 

0000030c e8950006   1292 	ldmfd	[r5],{r1-r2}

00000310 e3a03004   1293 	mov	r3,4

00000314 e0820001   1294 	add	r0,r2,r1

00000318 e28d200c   1295 	add	r2,sp,12

0000031c e2800002   1296 	add	r0,r0,2

00000320 e58b0038   1297 	str	r0,[fp,56]

                    1298 ;413: 	if (!BufferView_encodeOctetString(templateBuf, 0x86, dummyTime, 4))


                    1299 

00000324 e1a00005   1300 	mov	r0,r5

00000328 e3a01086   1301 	mov	r1,134

0000032c eb000000*  1302 	bl	BufferView_encodeOctetString

00000330 e3500000   1303 	cmp	r0,0

00000334 0a00002d   1304 	beq	.L870

                    1305 ;414: 	{


                    1306 

                    1307 ;415: 		return FALSE;


                    1308 

                    1309 ;416: 	}


                    1310 ;417: 	//test


                    1311 ;418: 	if (!BufferView_encodeBoolean(templateBuf, 0x87, FALSE))


                    1312 

00000338 e1a00005   1313 	mov	r0,r5

0000033c e3a02000   1314 	mov	r2,0

00000340 e3a01087   1315 	mov	r1,135

00000344 eb000000*  1316 	bl	BufferView_encodeBoolean

00000348 e3500000   1317 	cmp	r0,0

0000034c 0a000027   1318 	beq	.L870

                    1319 ;419: 	{


                    1320 

                    1321 ;420: 		return FALSE;


                    1322 

                    1323 ;421: 	}


                    1324 ;422: 		


                    1325 ;423: 	//confRev


                    1326 ;424: 	if (!BufferView_encodeUInt32(templateBuf, 0x88, goCB->confRev))


                    1327 

00000350 e1d626b0   1328 	ldrh	r2,[r6,96]

00000354 e1a00005   1329 	mov	r0,r5

00000358 e3a01088   1330 	mov	r1,136

0000035c eb000000*  1331 	bl	BufferView_encodeUInt32


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
00000360 e3500000   1332 	cmp	r0,0

00000364 0a000021   1333 	beq	.L870

                    1334 ;425: 	{


                    1335 

                    1336 ;426: 		return FALSE;


                    1337 

                    1338 ;427: 	}


                    1339 ;428: 	//ndsCom


                    1340 ;429: 	//На момент формирования шаблона ndsCom неизвестен.


                    1341 ;430: 	//Но поскольку GOOSE с ndsCom не посылается вообще,


                    1342 ;431: 	//можно смело писать false


                    1343 ;432: 	if (!BufferView_encodeBoolean(templateBuf, 0x89, false/*goCB->ndsCom*/))


                    1344 

00000368 e1a00005   1345 	mov	r0,r5

0000036c e3a02000   1346 	mov	r2,0

00000370 e3a01089   1347 	mov	r1,137

00000374 eb000000*  1348 	bl	BufferView_encodeBoolean

00000378 e3500000   1349 	cmp	r0,0

0000037c 0a00001b   1350 	beq	.L870

                    1351 ;433: 	{


                    1352 

                    1353 ;434: 		return FALSE;


                    1354 

                    1355 ;435: 	}


                    1356 ;436: 	//numDatSetEntries


                    1357 ;437: 	if (!BufferView_encodeUInt32(templateBuf, 0x8A, goCB->daCount))


                    1358 

00000380 e5962180   1359 	ldr	r2,[r6,384]

00000384 e1a00005   1360 	mov	r0,r5

00000388 e3a0108a   1361 	mov	r1,138

0000038c eb000000*  1362 	bl	BufferView_encodeUInt32

00000390 e3500000   1363 	cmp	r0,0

00000394 0a000015   1364 	beq	.L870

                    1365 ;438: 	{


                    1366 

                    1367 ;439: 		return FALSE;


                    1368 

                    1369 ;440: 	}


                    1370 ;441: 	//allData


                    1371 ;442: 	if (!writeDataTemplate(goCB, gooseSizes, templateBuf))


                    1372 

                    1373 ;341: 	BufferView* templateBuf)


                    1374 ;342: {


                    1375 

                    1376 ;343: 	size_t daIdx;


                    1377 ;344: 	


                    1378 ;345: 	if (!BufferView_encodeTL(templateBuf, 0xAB, gooseSizes->dataSize))


                    1379 

00000398 e1a02004   1380 	mov	r2,r4

0000039c e1a00005   1381 	mov	r0,r5

000003a0 e3a010ab   1382 	mov	r1,171

000003a4 eb000000*  1383 	bl	BufferView_encodeTL

000003a8 e3500000   1384 	cmp	r0,0

000003ac 0a00000f   1385 	beq	.L870

                    1386 ;346: 	{


                    1387 

                    1388 ;347: 		ERROR_REPORT("Error creating GOOSE data template");


                    1389 ;348: 		return FALSE;


                    1390 

                    1391 ;349: 	}


                    1392 ;350: 



                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    1393 ;351: 	for (daIdx = 0; daIdx < goCB->daCount; ++daIdx)


                    1394 

000003b0 e2867f61   1395 	add	r7,r6,0x0184

000003b4 e5960180   1396 	ldr	r0,[r6,384]

000003b8 e3a04000   1397 	mov	r4,0

000003bc e1540000   1398 	cmp	r4,r0

000003c0 2a00000c   1399 	bhs	.L869

                    1400 .L900:

                    1401 ;352: 	{


                    1402 

                    1403 ;353: 		if (!FDA_encodeGOOSETemplate(goCB->daList[daIdx], templateBuf))


                    1404 

000003c4 e5970000   1405 	ldr	r0,[r7]

000003c8 e1a01005   1406 	mov	r1,r5

000003cc eb000000*  1407 	bl	FDA_encodeGOOSETemplate

000003d0 e3500000   1408 	cmp	r0,0

000003d4 0a000005   1409 	beq	.L870

                    1410 ;354: 		{


                    1411 

                    1412 ;355: 			ERROR_REPORT("Error creating GOODE data template");


                    1413 ;356: 			return FALSE;


                    1414 

000003d8 e2877004   1415 	add	r7,r7,4

000003dc e5960180   1416 	ldr	r0,[r6,384]

000003e0 e2844001   1417 	add	r4,r4,1

000003e4 e1540000   1418 	cmp	r4,r0

000003e8 3afffff5   1419 	blo	.L900

000003ec ea000001   1420 	b	.L869

                    1421 .L870:

                    1422 ;357: 		}


                    1423 ;358: 	}	


                    1424 ;359: 	return TRUE;


                    1425 

                    1426 ;443: 	{


                    1427 

                    1428 ;444: 		return FALSE;


                    1429 

                    1430 ;445: 	}


                    1431 ;446:     WRITE_TO_FILE("gseData.bin", gse->pPDU, gse->pduSize);


                    1432 ;447: 	return TRUE;


                    1433 

000003f0 e3a00000   1434 	mov	r0,0

000003f4 ea000000   1435 	b	.L821

                    1436 .L869:

000003f8 e3a00001   1437 	mov	r0,1

                    1438 .L821:

000003fc e28dd020   1439 	add	sp,sp,32

00000400 e8bd8cf0   1440 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                    1441 	.endf	prepareGOOSEbuf

                    1442 	.align	4

                    1443 ;gooseSizes	[sp,20]	local

                    1444 ;daIdx	r7	local

                    1445 ;totalSize	r4	local

                    1446 ;daSize	[sp]	local

                    1447 ;totalSize	r7	local

                    1448 ;vlanBuf	[sp,4]	local

                    1449 ;tci1	r1	local

                    1450 ;tci2	r3	local

                    1451 ;dummyTime	[sp,12]	local

                    1452 ;daIdx	r4	local

                    1453 


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    1454 ;goCB	r6	param

                    1455 ;gse	fp	param

                    1456 ;gooseBuf	r5	param

                    1457 

                    1458 	.section ".bss","awb"

                    1459 .L1583:

                    1460 	.section ".rodata","a"

                    1461 .L1584:

00000000 00        1462 __UNNAMED_1_static_in_writeGOOSEPduTemplate:	.space	1

00000001 00000000   1463 	.space	7

00000005 000000 
                    1464 	.type	__UNNAMED_1_static_in_writeGOOSEPduTemplate,$object

                    1465 	.size	__UNNAMED_1_static_in_writeGOOSEPduTemplate,8

                    1466 .L1585:

                    1467 __UNNAMED_2_static_in_initGoCBvars:;	"ConfRev\000"

00000008 666e6f43   1468 	.data.b	67,111,110,102

0000000c 00766552   1469 	.data.b	82,101,118,0

                    1470 	.type	__UNNAMED_2_static_in_initGoCBvars,$object

                    1471 	.size	__UNNAMED_2_static_in_initGoCBvars,8

                    1472 	.data

                    1473 .L1586:

00000000 00000000   1474 g_goCBCount:	.data.b	0,0,0,0

                    1475 	.type	g_goCBCount,$object

                    1476 	.size	g_goCBCount,4

                    1477 	.section ".rodata","a"

                    1478 .L1587:

                    1479 __UNNAMED_1_static_in_initGoCBRef:;	"/\000"

00000010 002f      1480 	.data.b	47,0

                    1481 	.type	__UNNAMED_1_static_in_initGoCBRef,$object

                    1482 	.size	__UNNAMED_1_static_in_initGoCBRef,2

                    1483 .L1588:

                    1484 __UNNAMED_2_static_in_initGoCBRef:;	"$\000"

00000012 0024      1485 	.data.b	36,0

                    1486 	.type	__UNNAMED_2_static_in_initGoCBRef,$object

                    1487 	.size	__UNNAMED_2_static_in_initGoCBRef,2

                    1488 .L1589:

                    1489 __UNNAMED_1_static_in_initGoCBvars:;	"AppID\000"

00000014 49707041   1490 	.data.b	65,112,112,73

00000018 0044      1491 	.data.b	68,0

0000001a 0000      1492 	.space	2

                    1493 	.type	__UNNAMED_1_static_in_initGoCBvars,$object

                    1494 	.size	__UNNAMED_1_static_in_initGoCBvars,8

                    1495 .L1590:

                    1496 __UNNAMED_1_static_in_initDatSet:;	"DatSet\000"

0000001c 53746144   1497 	.data.b	68,97,116,83

00000020 7465      1498 	.data.b	101,116

00000022 00        1499 	.data.b	0

00000023 00        1500 	.space	1

                    1501 	.type	__UNNAMED_1_static_in_initDatSet,$object

                    1502 	.size	__UNNAMED_1_static_in_initDatSet,8

                    1503 	.section ".bss","awb"

00000000 00000000   1504 g_goCBPath:	.space	16

00000004 00000000 
00000008 00000000 
0000000c 00000000 
00000010 00000000   1505 g_goCBs:	.space	11152

00000014 00000000 
00000018 00000000 
0000001c 00000000 
00000020 00000000 
00000024 00000000 

                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
00000028 00000000 
0000002c 00000000 
00000030 00000000 
00000034 00000000 
00000038 00000000 
0000003c 00000000 
00000040 00000000 
00000044 00000000 
00000048 00000000 
0000004c 00000000 
00000050 00000000 
00000054 00000000 
00000058 00000000 
0000005c 00000000 
00000060 00000000 
00000064 00000000 
00000068 00000000 
0000006c 00000000 
00000070 00000000 
00000074 00000000 
00000078 00000000 
0000007c 00000000 
00000080 00000000 
00000084 00000000 
00000088 00000000 
0000008c 00000000 
00000090 00000000 
00000094 00000000 
00000098 00000000 
0000009c 00000000 
000000a0 00000000 
000000a4 00000000 
000000a8 00000000 
000000ac 00000000 
000000b0 00000000 
000000b4 00000000 
000000b8 00000000 
000000bc 00000000 
000000c0 00000000 
000000c4 00000000 
000000c8 00000000 
000000cc 00000000 
000000d0 00000000 
000000d4 00000000 
000000d8 00000000 
000000dc 00000000 
000000e0 00000000 
000000e4 00000000 
000000e8 00000000 
000000ec 00000000 
000000f0 00000000 
000000f4 00000000 
000000f8 00000000 
000000fc 00000000 
00000100 00000000 
00000104 00000000 
00000108 00000000 
0000010c 00000000 
                    1506 	.data

                    1507 	.text

                    1508 


                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    1509 

                    1510 	.align	4

                    1511 	.align	4

                    1512 GSE_init::

00000404 e92d4030   1513 	stmfd	[sp]!,{r4-r5,lr}

00000408 e24dd024   1514 	sub	sp,sp,36

0000040c e1a04001   1515 	mov	r4,r1

00000410 e28d1003   1516 	add	r1,sp,3

00000414 e1a05000   1517 	mov	r5,r0

00000418 e1a00004   1518 	mov	r0,r4

0000041c e3a03000   1519 	mov	r3,0

00000420 e1a02003   1520 	mov	r2,r3

00000424 eb000000*  1521 	bl	BufferView_decodeTL

00000428 e3500000   1522 	cmp	r0,0

0000042c 0a000065   1523 	beq	.L1708

00000430 e5dd0003   1524 	ldrb	r0,[sp,3]

00000434 e3500030   1525 	cmp	r0,48

00000438 1a000062   1526 	bne	.L1708

0000043c e28d2004   1527 	add	r2,sp,4

00000440 e1a00004   1528 	mov	r0,r4

00000444 e3a01002   1529 	mov	r1,2

00000448 eb000000*  1530 	bl	BufferView_decodeUInt32TL

0000044c e3500000   1531 	cmp	r0,0

00000450 0a00005c   1532 	beq	.L1708

00000454 e5dd0004   1533 	ldrb	r0,[sp,4]

00000458 e2851004   1534 	add	r1,r5,4

0000045c e5c50000   1535 	strb	r0,[r5]

00000460 eb000000*  1536 	bl	NetTools_getIf

00000464 e3500000   1537 	cmp	r0,0

00000468 0a000056   1538 	beq	.L1708

0000046c e5950004   1539 	ldr	r0,[r5,4]

00000470 e2851008   1540 	add	r1,r5,8

00000474 eb000000*  1541 	bl	NetTools_getMac

00000478 e3500000   1542 	cmp	r0,0

0000047c 0a000051   1543 	beq	.L1708

00000480 e28d201c   1544 	add	r2,sp,28

00000484 e1a00004   1545 	mov	r0,r4

00000488 e3a01004   1546 	mov	r1,4

0000048c eb000000*  1547 	bl	BufferView_decodeStringViewTL

00000490 e3500000   1548 	cmp	r0,0

00000494 0a00004b   1549 	beq	.L1708

00000498 e59d001c   1550 	ldr	r0,[sp,28]

0000049c e3500006   1551 	cmp	r0,6

000004a0 1a000048   1552 	bne	.L1708

000004a4 e59d1020   1553 	ldr	r1,[sp,32]

000004a8 e285000e   1554 	add	r0,r5,14

000004ac e3a02006   1555 	mov	r2,6

000004b0 eb000000*  1556 	bl	memcpy

                    1557 ;479: {


                    1558 

                    1559 ;480: 	uint32_t vlanID;


                    1560 ;481: 	uint32_t vlanPriority;


                    1561 ;482: 


                    1562 ;483: 	vlan->useVLAN = FALSE;


                    1563 

000004b4 e3a00000   1564 	mov	r0,0

000004b8 e5c50014   1565 	strb	r0,[r5,20]

                    1566 ;484: 	if (!BufferView_checkTag(berData, BER_VLAN_INFO))


                    1567 

000004bc e9940003   1568 	ldmed	[r4],{r0-r1}

000004c0 e1500001   1569 	cmp	r0,r1


                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
000004c4 2a00001c   1570 	bhs	.L1679

000004c8 e5941000   1571 	ldr	r1,[r4]

000004cc e7d10000   1572 	ldrb	r0,[r1,r0]

000004d0 e35000a0   1573 	cmp	r0,160

000004d4 1a000018   1574 	bne	.L1679

                    1575 ;485: 	{


                    1576 

                    1577 ;486: 		//Конфигурации VLAN нет. Это нормально.		


                    1578 ;487: 		return TRUE;


                    1579 

                    1580 ;488: 	}


                    1581 ;489: 	//Пропускаем тэг и длину информации о VLAN


                    1582 ;490: 	if (!BufferView_decodeTL(berData, NULL, NULL, NULL))


                    1583 

000004d8 e1a00004   1584 	mov	r0,r4

000004dc e3a03000   1585 	mov	r3,0

000004e0 e1a02003   1586 	mov	r2,r3

000004e4 e1a01003   1587 	mov	r1,r3

000004e8 eb000000*  1588 	bl	BufferView_decodeTL

000004ec e3500000   1589 	cmp	r0,0

000004f0 0a000034   1590 	beq	.L1708

                    1591 ;491: 	{


                    1592 

                    1593 ;492: 		ERROR_REPORT("Unable to decode VLAN info tag and length");


                    1594 ;493: 		return FALSE;


                    1595 

                    1596 ;494: 	}


                    1597 ;495: 	//VLAN ID


                    1598 ;496: 	if (!BufferView_decodeUInt32TL(berData, ASN_INTEGER, &vlanID))


                    1599 

000004f4 e28d2014   1600 	add	r2,sp,20

000004f8 e1a00004   1601 	mov	r0,r4

000004fc e3a01002   1602 	mov	r1,2

00000500 eb000000*  1603 	bl	BufferView_decodeUInt32TL

00000504 e3500000   1604 	cmp	r0,0

00000508 0a00002e   1605 	beq	.L1708

                    1606 ;497: 	{


                    1607 

                    1608 ;498: 		ERROR_REPORT("Unable to decode VLAN ID");


                    1609 ;499: 		return FALSE;


                    1610 

                    1611 ;500: 	}


                    1612 ;501: 	vlan->id = vlanID;


                    1613 

0000050c e59d0014   1614 	ldr	r0,[sp,20]

00000510 e28d2018   1615 	add	r2,sp,24

00000514 e5850018   1616 	str	r0,[r5,24]

                    1617 ;502: 


                    1618 ;503: 	//VLAN priority


                    1619 ;504: 	if (!BufferView_decodeUInt32TL(berData, ASN_INTEGER, &vlanPriority))


                    1620 

00000518 e1a00004   1621 	mov	r0,r4

0000051c e3a01002   1622 	mov	r1,2

00000520 eb000000*  1623 	bl	BufferView_decodeUInt32TL

00000524 e3500000   1624 	cmp	r0,0

00000528 0a000026   1625 	beq	.L1708

                    1626 ;505: 	{


                    1627 

                    1628 ;506: 		ERROR_REPORT("Unable to decode VLAN priority");


                    1629 ;507: 		return FALSE;


                    1630 


                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    1631 ;508: 	}	


                    1632 ;509: 	vlan->priority = vlanPriority;


                    1633 

0000052c e59d0018   1634 	ldr	r0,[sp,24]

00000530 e585001c   1635 	str	r0,[r5,28]

                    1636 ;510: 	vlan->useVLAN = TRUE;


                    1637 

00000534 e3a00001   1638 	mov	r0,1

00000538 e5c50014   1639 	strb	r0,[r5,20]

                    1640 ;511: 	return TRUE;


                    1641 

                    1642 .L1679:

0000053c e28d2008   1643 	add	r2,sp,8

00000540 e1a00004   1644 	mov	r0,r4

00000544 e3a01002   1645 	mov	r1,2

00000548 eb000000*  1646 	bl	BufferView_decodeUInt32TL

0000054c e3500000   1647 	cmp	r0,0

00000550 0a00001c   1648 	beq	.L1708

00000554 e59d0008   1649 	ldr	r0,[sp,8]

00000558 e28d1003   1650 	add	r1,sp,3

0000055c e1c502b0   1651 	strh	r0,[r5,32]

00000560 e1a00004   1652 	mov	r0,r4

00000564 e3a03000   1653 	mov	r3,0

00000568 e1a02003   1654 	mov	r2,r3

0000056c eb000000*  1655 	bl	BufferView_decodeTL

00000570 e3500000   1656 	cmp	r0,0

00000574 0a000013   1657 	beq	.L1708

00000578 e5dd0003   1658 	ldrb	r0,[sp,3]

0000057c e35000a1   1659 	cmp	r0,161

00000580 1a000010   1660 	bne	.L1708

00000584 e28d200c   1661 	add	r2,sp,12

00000588 e1a00004   1662 	mov	r0,r4

0000058c e3a01002   1663 	mov	r1,2

00000590 eb000000*  1664 	bl	BufferView_decodeUInt32TL

00000594 e3500000   1665 	cmp	r0,0

00000598 0a00000a   1666 	beq	.L1708

0000059c e59d000c   1667 	ldr	r0,[sp,12]

000005a0 e28d2010   1668 	add	r2,sp,16

000005a4 e5850048   1669 	str	r0,[r5,72]

000005a8 e1a00004   1670 	mov	r0,r4

000005ac e3a01002   1671 	mov	r1,2

000005b0 eb000000*  1672 	bl	BufferView_decodeUInt32TL

000005b4 e3500000   1673 	cmp	r0,0

000005b8 159d0010   1674 	ldrne	r0,[sp,16]

000005bc 1585004c   1675 	strne	r0,[r5,76]

000005c0 13a00001   1676 	movne	r0,1

000005c4 1a000000   1677 	bne	.L1658

                    1678 .L1708:

000005c8 e3a00000   1679 	mov	r0,0

                    1680 .L1658:

000005cc e28dd024   1681 	add	sp,sp,36

000005d0 e8bd8030   1682 	ldmfd	[sp]!,{r4-r5,pc}

                    1683 	.endf	GSE_init

                    1684 	.align	4

                    1685 ;tag	[sp,3]	local

                    1686 ;ifNum	[sp,4]	local

                    1687 ;appID	[sp,8]	local

                    1688 ;dest	[sp,28]	local

                    1689 ;t0	[sp,12]	local

                    1690 ;t1	[sp,16]	local

                    1691 ;vlanID	[sp,20]	local


                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    1692 ;vlanPriority	[sp,24]	local

                    1693 

                    1694 ;gse	r5	param

                    1695 ;berData	r4	param

                    1696 

                    1697 	.section ".bss","awb"

                    1698 .L2096:

                    1699 	.data

                    1700 	.text

                    1701 

                    1702 

                    1703 	.align	4

                    1704 	.align	4

                    1705 writeObjectNameToBuf:

000005d4 e92d4030   1706 	stmfd	[sp]!,{r4-r5,lr}

000005d8 e1a05002   1707 	mov	r5,r2

000005dc e24dd008   1708 	sub	sp,sp,8

000005e0 e1a04001   1709 	mov	r4,r1

000005e4 e1a0100d   1710 	mov	r1,sp

000005e8 eb000000*  1711 	bl	getObjectName

000005ec e3500000   1712 	cmp	r0,0

000005f0 0a000011   1713 	beq	.L2175

000005f4 e1a0100d   1714 	mov	r1,sp

000005f8 e1a00005   1715 	mov	r0,r5

000005fc eb000000*  1716 	bl	BufferView_writeStringView

00000600 e3500000   1717 	cmp	r0,0

00000604 0a00000c   1718 	beq	.L2175

00000608 e1a00005   1719 	mov	r0,r5

0000060c e3540000   1720 	cmp	r4,0

00000610 0a000004   1721 	beq	.L2168

00000614 e1a01004   1722 	mov	r1,r4

00000618 eb000000*  1723 	bl	BufferView_writeStr

0000061c e3500000   1724 	cmp	r0,0

00000620 0a000005   1725 	beq	.L2175

00000624 ea000006   1726 	b	.L2174

                    1727 .L2168:

00000628 e59f19a0*  1728 	ldr	r1,.L2289

0000062c e3a02001   1729 	mov	r2,1

00000630 eb000000*  1730 	bl	BufferView_writeData

00000634 e3500001   1731 	cmp	r0,1

00000638 0a000001   1732 	beq	.L2174

                    1733 .L2175:

0000063c e3a00000   1734 	mov	r0,0

00000640 ea000000   1735 	b	.L2160

                    1736 .L2174:

00000644 e3a00001   1737 	mov	r0,1

                    1738 .L2160:

00000648 e28dd008   1739 	add	sp,sp,8

0000064c e8bd4030   1740 	ldmfd	[sp]!,{r4-r5,lr}

00000650 e12fff1e*  1741 	ret	

                    1742 	.endf	writeObjectNameToBuf

                    1743 	.align	4

                    1744 ;nameView	[sp]	local

                    1745 ;.L2269	.L2272	static

                    1746 

                    1747 ;pos	none	param

                    1748 ;delimiter	r4	param

                    1749 ;buf	r5	param

                    1750 

                    1751 	.section ".bss","awb"

                    1752 .L2268:


                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    1753 	.section ".rodata","a"

                    1754 .L2272:;	"\000"

00000024 00        1755 	.data.b	0

                    1756 	.type	.L2272,$object

                    1757 	.size	.L2272,1

                    1758 	.data

                    1759 	.text

                    1760 

                    1761 

                    1762 	.align	4

                    1763 	.align	4

                    1764 GOOSE_resetPktCounters:

00000654 e3a03001   1765 	mov	r3,1

00000658 e280106c   1766 	add	r1,r0,108

0000065c e5900068   1767 	ldr	r0,[r0,104]

00000660 e3a02000   1768 	mov	r2,0

00000664 e1520000   1769 	cmp	r2,r0

00000668 2a000005   1770 	bhs	.L2290

                    1771 .L2294:

0000066c e581303c   1772 	str	r3,[r1,60]

00000670 e5813044   1773 	str	r3,[r1,68]

00000674 e281105c   1774 	add	r1,r1,92

00000678 e2822001   1775 	add	r2,r2,1

0000067c e1520000   1776 	cmp	r2,r0

00000680 3afffff9   1777 	blo	.L2294

                    1778 .L2290:

00000684 e12fff1e*  1779 	ret	

                    1780 	.endf	GOOSE_resetPktCounters

                    1781 	.align	4

                    1782 ;gseIdx	r2	local

                    1783 

                    1784 ;goCB	r0	param

                    1785 

                    1786 	.section ".bss","awb"

                    1787 .L2331:

                    1788 	.data

                    1789 	.text

                    1790 

                    1791 

                    1792 	.align	4

                    1793 	.align	4

                    1794 GOOSE_resetPktTimers:

00000688 e92d0010   1795 	stmfd	[sp]!,{r4}

0000068c e280106c   1796 	add	r1,r0,108

00000690 e3a02000   1797 	mov	r2,0

00000694 e1a03002   1798 	mov	r3,r2

00000698 e5904068   1799 	ldr	r4,[r0,104]

0000069c e3a0c001   1800 	mov	r12,1

000006a0 e1520004   1801 	cmp	r2,r4

000006a4 2a000007   1802 	bhs	.L2341

                    1803 .L2345:

000006a8 e5910048   1804 	ldr	r0,[r1,72]

000006ac e5a10050   1805 	str	r0,[r1,80]!

000006b0 e1a0000c   1806 	mov	r0,r12

000006b4 e9810009   1807 	stmfa	[r1],{r0,r3}

000006b8 e281100c   1808 	add	r1,r1,12

000006bc e2822001   1809 	add	r2,r2,1

000006c0 e1520004   1810 	cmp	r2,r4

000006c4 3afffff7   1811 	blo	.L2345

                    1812 .L2341:

000006c8 e8bd0010   1813 	ldmfd	[sp]!,{r4}


                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
000006cc e12fff1e*  1814 	ret	

                    1815 	.endf	GOOSE_resetPktTimers

                    1816 	.align	4

                    1817 ;gseIdx	r2	local

                    1818 

                    1819 ;goCB	r0	param

                    1820 

                    1821 	.section ".bss","awb"

                    1822 .L2379:

                    1823 	.data

                    1824 	.text

                    1825 

                    1826 

                    1827 	.align	4

                    1828 	.align	4

                    1829 GoCB_init::

000006d0 e92d44f0   1830 	stmfd	[sp]!,{r4-r7,r10,lr}

000006d4 e1a05000   1831 	mov	r5,r0

000006d8 e2852004   1832 	add	r2,r5,4

000006dc e24dd030   1833 	sub	sp,sp,48

000006e0 e3a00001   1834 	mov	r0,1

000006e4 e5c5001c   1835 	strb	r0,[r5,28]

                    1836 ;691: {	


                    1837 

                    1838 ;692: 	uint32_t confRev;


                    1839 ;693: 	goCB->goEna = FALSE;


                    1840 

000006e8 e3a00000   1841 	mov	r0,0

000006ec e5c50000   1842 	strb	r0,[r5]

                    1843 ;694: 


                    1844 ;695: 	//AppID


                    1845 ;696: 	if (!getConstDAString(goCBPos, "AppID", &goCB->appID))


                    1846 

000006f0 e1a04001   1847 	mov	r4,r1

000006f4 e59f18d8*  1848 	ldr	r1,.L3221

000006f8 e1a00004   1849 	mov	r0,r4

000006fc eb000000*  1850 	bl	getConstDAString

00000700 e3500000   1851 	cmp	r0,0

00000704 0a0000b1   1852 	beq	.L2461

                    1853 ;697: 	{


                    1854 

                    1855 ;698: 		ERROR_REPORT("Unable to init AppID");


                    1856 ;699: 		return FALSE;


                    1857 

                    1858 ;700: 	}


                    1859 ;701: 


                    1860 ;702: 	//ConfRev


                    1861 ;703: 	if (!getConstDAULong(goCBPos, "ConfRev", &confRev))


                    1862 

00000708 e28d2004   1863 	add	r2,sp,4

0000070c e59f18c4*  1864 	ldr	r1,.L3222

00000710 e1a00004   1865 	mov	r0,r4

00000714 eb000000*  1866 	bl	getConstDAULong

00000718 e3500000   1867 	cmp	r0,0

0000071c 0a0000ab   1868 	beq	.L2461

                    1869 ;704: 	{


                    1870 

                    1871 ;705: 		ERROR_REPORT("Unable to init ConfRev");


                    1872 ;706: 		return FALSE;


                    1873 

                    1874 ;707: 	}	



                                                                      Page 33
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    1875 ;708: 	goCB->confRev = (uint16_t)confRev;


                    1876 

00000720 e59d0004   1877 	ldr	r0,[sp,4]

00000724 e285200c   1878 	add	r2,r5,12

00000728 e1c506b0   1879 	strh	r0,[r5,96]

                    1880 ;709: 


                    1881 ;710: 	if (!initDatSet(goCBPos, goCB))


                    1882 

                    1883 ;603: {	


                    1884 

                    1885 ;604: 	IEDEntity dataSetEntity;


                    1886 ;605: 


                    1887 ;606: 	if (!getConstDAString(goCBPos, "DatSet", &goCB->dataSetName))


                    1888 

0000072c e59f18a8*  1889 	ldr	r1,.L3223

00000730 e1a00004   1890 	mov	r0,r4

00000734 eb000000*  1891 	bl	getConstDAString

00000738 e3500000   1892 	cmp	r0,0

0000073c 0a0000a3   1893 	beq	.L2461

                    1894 ;607: 	{


                    1895 

                    1896 ;608: 		return false;


                    1897 

                    1898 ;609: 	}


                    1899 ;610: 


                    1900 ;611: 	//Получаем DataSet в IEDTree


                    1901 ;612: 	dataSetEntity = IEDTree_findDataSetBySingleName(&goCB->dataSetName);


                    1902 

00000740 e285000c   1903 	add	r0,r5,12

00000744 eb000000*  1904 	bl	IEDTree_findDataSetBySingleName

00000748 e1b01000   1905 	movs	r1,r0

                    1906 ;613: 	if(dataSetEntity == NULL)


                    1907 

0000074c 0a00009f   1908 	beq	.L2461

                    1909 ;614: 	{


                    1910 

                    1911 ;615: 		return false;


                    1912 

                    1913 ;616: 	}


                    1914 ;617: 	goCB->dataSetEntity = dataSetEntity;


                    1915 

00000750 e5851014   1916 	str	r1,[r5,20]

                    1917 ;618: 	goCB->dataSet = DataSet_getDataSetObj(dataSetEntity);


                    1918 

00000754 eb000000*  1919 	bl	DataSet_getDataSetObj

00000758 e5850018   1920 	str	r0,[r5,24]

                    1921 ;619: 	if(goCB->dataSet == NULL)


                    1922 

0000075c e3500000   1923 	cmp	r0,0

00000760 0a00009a   1924 	beq	.L2461

                    1925 ;620: 	{


                    1926 

                    1927 ;621: 		ERROR_REPORT("Invalid DataSet");


                    1928 ;622: 		return false;


                    1929 

                    1930 ;623: 	}


                    1931 ;624: 


                    1932 ;625: 	return true;


                    1933 

                    1934 ;711: 	{


                    1935 


                                                                      Page 34
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    1936 ;712: 		ERROR_REPORT("Unable to init DatSet");


                    1937 ;713: 		return FALSE;


                    1938 

                    1939 ;714: 	}


                    1940 ;715: 


                    1941 ;716: 	if (!initGoCBRef(goCB))


                    1942 

                    1943 ;660: {	


                    1944 

                    1945 ;661: 	BufferView refBuf;


                    1946 ;662:     BufferView_init(&refBuf, (uint8_t*)goCB->goCBRef, MAX_GO_CB_REF_SIZE, 0);


                    1947 

00000764 e285101d   1948 	add	r1,r5,29

00000768 e28d0024   1949 	add	r0,sp,36

0000076c e3a03000   1950 	mov	r3,0

00000770 e3a02042   1951 	mov	r2,66

00000774 eb000000*  1952 	bl	BufferView_init

                    1953 ;663: 


                    1954 ;664: 	if (!writeObjectNameToBuf(g_goCBPath.ldPos, "/", &refBuf))


                    1955 

00000778 e59f6860*  1956 	ldr	r6,.L3224

0000077c e59f1860*  1957 	ldr	r1,.L3225

00000780 e5960000   1958 	ldr	r0,[r6]

00000784 e28d2024   1959 	add	r2,sp,36

00000788 ebffff91*  1960 	bl	writeObjectNameToBuf

0000078c e3500000   1961 	cmp	r0,0

00000790 0a00008e   1962 	beq	.L2461

                    1963 ;665: 	{


                    1964 

                    1965 ;666: 		ERROR_REPORT("Unable to write LD name");


                    1966 ;667: 		return false;


                    1967 

                    1968 ;668: 	}


                    1969 ;669: 


                    1970 ;670: 	if (!writeObjectNameToBuf(g_goCBPath.lnPos, "$", &refBuf))


                    1971 

00000794 e28d2024   1972 	add	r2,sp,36

00000798 e59f7848*  1973 	ldr	r7,.L3226

0000079c e5960004   1974 	ldr	r0,[r6,4]

000007a0 e1a01007   1975 	mov	r1,r7

000007a4 ebffff8a*  1976 	bl	writeObjectNameToBuf

000007a8 e3500000   1977 	cmp	r0,0

000007ac 0a000087   1978 	beq	.L2461

                    1979 ;671: 	{


                    1980 

                    1981 ;672: 		ERROR_REPORT("Unable to write LN name");


                    1982 ;673: 		return false;


                    1983 

                    1984 ;674: 	}


                    1985 ;675: 


                    1986 ;676: 	if (!writeObjectNameToBuf(g_goCBPath.fcPos, "$", &refBuf))


                    1987 

000007b0 e28d2024   1988 	add	r2,sp,36

000007b4 e5960008   1989 	ldr	r0,[r6,8]

000007b8 e1a01007   1990 	mov	r1,r7

000007bc ebffff84*  1991 	bl	writeObjectNameToBuf

000007c0 e3500000   1992 	cmp	r0,0

000007c4 0a000081   1993 	beq	.L2461

                    1994 ;677: 	{


                    1995 

                    1996 ;678: 		ERROR_REPORT("Unable to write FC name");



                                                                      Page 35
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    1997 ;679: 		return false;


                    1998 

                    1999 ;680: 	}


                    2000 ;681: 


                    2001 ;682: 	if (!writeObjectNameToBuf(g_goCBPath.goCBPos, NULL, &refBuf))


                    2002 

000007c8 e28d2024   2003 	add	r2,sp,36

000007cc e596000c   2004 	ldr	r0,[r6,12]

000007d0 e3a01000   2005 	mov	r1,0

000007d4 ebffff7e*  2006 	bl	writeObjectNameToBuf

000007d8 e3500000   2007 	cmp	r0,0

000007dc 0a00007b   2008 	beq	.L2461

000007e0 e3a00efa   2009 	mov	r0,0x0fa0

000007e4 e5850064   2010 	str	r0,[r5,100]

                    2011 ;683: 	{


                    2012 

                    2013 ;684: 		ERROR_REPORT("Unable to write GoCB name");


                    2014 ;685: 		return false;


                    2015 

                    2016 ;686: 	}


                    2017 ;687: 	return true;


                    2018 

                    2019 ;717: 	{


                    2020 

                    2021 ;718: 		ERROR_REPORT("Unable to init GoCBRef");


                    2022 ;719: 		return FALSE;


                    2023 

                    2024 ;720: 	}


                    2025 ;721: 


                    2026 ;722: 	goCB->timeAllowedToLive = TIME_ALLOWED_TO_LIVE;


                    2027 

                    2028 ;723: 	return TRUE;


                    2029 

                    2030 ;727: {


                    2031 

                    2032 ;728: 	size_t gseIndex;


                    2033 ;729: 	BufferView gseBER;


                    2034 ;730: 	uint8_t gseListTag;


                    2035 ;731: 	size_t gseListLen;


                    2036 ;732: 	void* pGSEList;


                    2037 ;733: 


                    2038 ;734: 	//Пропускаем TL от goCB


                    2039 ;735: 	size_t currPos = readTL(goCBPos, NULL, NULL, NULL);


                    2040 

000007e8 e1a00004   2041 	mov	r0,r4

000007ec e3a03000   2042 	mov	r3,0

000007f0 e1a02003   2043 	mov	r2,r3

000007f4 e1a01003   2044 	mov	r1,r3

000007f8 eb000000*  2045 	bl	readTL

                    2046 ;736: 	if (currPos == 0)


                    2047 

000007fc e3500000   2048 	cmp	r0,0

00000800 0a000072   2049 	beq	.L2461

                    2050 ;737: 	{


                    2051 

                    2052 ;738: 		ERROR_REPORT("Unable to read GoCB");


                    2053 ;739: 		return FALSE;


                    2054 

                    2055 ;740: 	}


                    2056 ;741: 	//Пропускаем имя


                    2057 ;742: 	currPos = skipObject(currPos);



                                                                      Page 36
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    2058 

00000804 eb000000*  2059 	bl	skipObject

                    2060 ;743: 	if (currPos == 0)


                    2061 

00000808 e3500000   2062 	cmp	r0,0

0000080c 0a00006f   2063 	beq	.L2461

                    2064 ;744: 	{


                    2065 

                    2066 ;745: 		ERROR_REPORT("Unable to skip GoCB name");


                    2067 ;746: 		return FALSE;


                    2068 

                    2069 ;747: 	}


                    2070 ;748: 	//Получаем TL


                    2071 ;749:     currPos = readTL(currPos, &gseListTag, (int*)&gseListLen, NULL);


                    2072 

00000810 e28d2008   2073 	add	r2,sp,8

00000814 e28d1003   2074 	add	r1,sp,3

00000818 e3a03000   2075 	mov	r3,0

0000081c eb000000*  2076 	bl	readTL

00000820 e1b01000   2077 	movs	r1,r0

                    2078 ;750: 	if (currPos == 0)


                    2079 

00000824 0a000069   2080 	beq	.L2461

                    2081 ;751: 	{


                    2082 

                    2083 ;752: 		ERROR_REPORT("Unable to read GSE list");


                    2084 ;753: 		return FALSE;


                    2085 

                    2086 ;754: 	}


                    2087 ;755: 	//Проверяем что список GSE


                    2088 ;756: 	if (gseListTag != IED_GSE_LIST)


                    2089 

00000828 e5dd0003   2090 	ldrb	r0,[sp,3]

0000082c e35000f0   2091 	cmp	r0,240

00000830 1a000066   2092 	bne	.L2461

                    2093 ;757: 	{


                    2094 

                    2095 ;758: 		ERROR_REPORT("Invalid GSE list tag");


                    2096 ;759: 		return FALSE;


                    2097 

                    2098 ;760: 	}


                    2099 ;761: 	


                    2100 ;762: 	pGSEList = IEDModel_ptrFromPos(currPos);


                    2101 

00000834 e1a00001   2102 	mov	r0,r1

00000838 eb000000*  2103 	bl	IEDModel_ptrFromPos

0000083c e1b01000   2104 	movs	r1,r0

                    2105 ;763: 	if (pGSEList == NULL)


                    2106 

00000840 0a000062   2107 	beq	.L2461

00000844 e59d2008   2108 	ldr	r2,[sp,8]

00000848 e28d0018   2109 	add	r0,sp,24

0000084c e3a03000   2110 	mov	r3,0

00000850 eb000000*  2111 	bl	BufferView_init

00000854 e3a00000   2112 	mov	r0,0

00000858 e59d2020   2113 	ldr	r2,[sp,32]

0000085c e59d101c   2114 	ldr	r1,[sp,28]

00000860 e5850068   2115 	str	r0,[r5,104]

                    2116 ;764: 	{


                    2117 

                    2118 ;765: 		ERROR_REPORT("Error getting GSE list pointer");



                                                                      Page 37
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    2119 ;766: 		return FALSE;


                    2120 

                    2121 ;767: 	}


                    2122 ;768: 


                    2123 ;769: 	//Из длины и текущей позиции делаем BufferView


                    2124 ;770: 	BufferView_init(&gseBER, pGSEList, gseListLen, 0);


                    2125 

                    2126 ;771: 


                    2127 ;772: 	goCB->gseCount = 0;	


                    2128 

                    2129 ;773: 	for (gseIndex = 0; gseIndex < MAX_GSE_COUNT; gseIndex++)


                    2130 

00000864 e1510002   2131 	cmp	r1,r2

00000868 0a00001f   2132 	beq	.L2424

0000086c e28d1018   2133 	add	r1,sp,24

00000870 e285006c   2134 	add	r0,r5,108

00000874 ebfffee2*  2135 	bl	GSE_init

00000878 e3500000   2136 	cmp	r0,0

0000087c 0a000053   2137 	beq	.L2461

00000880 e5950068   2138 	ldr	r0,[r5,104]

00000884 e59d2020   2139 	ldr	r2,[sp,32]

00000888 e2800001   2140 	add	r0,r0,1

0000088c e59d101c   2141 	ldr	r1,[sp,28]

00000890 e5850068   2142 	str	r0,[r5,104]

00000894 e1510002   2143 	cmp	r1,r2

00000898 0a000013   2144 	beq	.L2424

0000089c e28d1018   2145 	add	r1,sp,24

000008a0 e28500c8   2146 	add	r0,r5,200

000008a4 ebfffed6*  2147 	bl	GSE_init

000008a8 e3500000   2148 	cmp	r0,0

000008ac 0a000047   2149 	beq	.L2461

000008b0 e5950068   2150 	ldr	r0,[r5,104]

000008b4 e59d2020   2151 	ldr	r2,[sp,32]

000008b8 e2800001   2152 	add	r0,r0,1

000008bc e59d101c   2153 	ldr	r1,[sp,28]

000008c0 e5850068   2154 	str	r0,[r5,104]

000008c4 e1510002   2155 	cmp	r1,r2

000008c8 0a000007   2156 	beq	.L2424

000008cc e28d1018   2157 	add	r1,sp,24

000008d0 e2850f49   2158 	add	r0,r5,0x0124

000008d4 ebfffeca*  2159 	bl	GSE_init

000008d8 e3500000   2160 	cmp	r0,0

000008dc 0a00003b   2161 	beq	.L2461

000008e0 e5950068   2162 	ldr	r0,[r5,104]

000008e4 e2800001   2163 	add	r0,r0,1

000008e8 e5850068   2164 	str	r0,[r5,104]

                    2165 .L2424:

                    2166 ;784: 	}


                    2167 ;785: 	return TRUE;


                    2168 

000008ec e3500000   2169 	cmp	r0,0

000008f0 0a000036   2170 	beq	.L2461

                    2171 ;789: {    


                    2172 

                    2173 ;790:     IEDEntity da;	


                    2174 ;791:     void* finalDAobj;


                    2175 ;792: 


                    2176 ;793: 	DataSetItem* dsItem = goCB->dataSet->firstItem;


                    2177 

000008f4 e5950018   2178 	ldr	r0,[r5,24]

000008f8 e5904000   2179 	ldr	r4,[r0]


                                                                      Page 38
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    2180 ;794: 	while(dsItem != NULL)


                    2181 

000008fc e3540000   2182 	cmp	r4,0

00000900 0a000012   2183 	beq	.L2464

                    2184 .L2451:

                    2185 ;795: 	{


                    2186 

                    2187 ;796: 		da = dsItem->obj;


                    2188 

00000904 e5946014   2189 	ldr	r6,[r4,20]

                    2190 ;797: 


                    2191 ;798: 		if(da->type != IED_ENTITY_DA_TERMINAL_ITEM)


                    2192 

00000908 e5960050   2193 	ldr	r0,[r6,80]

0000090c e3500008   2194 	cmp	r0,8

00000910 1a00002e   2195 	bne	.L2461

                    2196 ;799: 		{


                    2197 

                    2198 ;800: 			ERROR_REPORT("GOOSE: Unsupported object type");


                    2199 ;801: 			return false;


                    2200 

                    2201 ;802: 		}


                    2202 ;803: 		finalDAobj = FDA_create((enum InnerAttributeType)da->subType,


                    2203 

00000914 e1a00006   2204 	mov	r0,r6

00000918 eb000000*  2205 	bl	IEDTermItemDA_getTerminalItemDescr

0000091c e1a01000   2206 	mov	r1,r0

00000920 e5960054   2207 	ldr	r0,[r6,84]

00000924 eb000000*  2208 	bl	FDA_create

                    2209 ;804: 								IEDTermItemDA_getTerminalItemDescr(da));


                    2210 ;805: 		if (finalDAobj == NULL)


                    2211 

00000928 e3500000   2212 	cmp	r0,0

0000092c 0a000027   2213 	beq	.L2461

                    2214 ;806: 		{


                    2215 

                    2216 ;807: 			ERROR_REPORT("GOOSE: Unable to create the final DA object");


                    2217 ;808: 			return false;


                    2218 

                    2219 ;809: 		}


                    2220 ;810: 		goCB->daList[goCB->daCount] = finalDAobj;


                    2221 

00000930 e5951180   2222 	ldr	r1,[r5,384]

00000934 e0852101   2223 	add	r2,r5,r1 lsl 2

00000938 e5820184   2224 	str	r0,[r2,388]

                    2225 ;811: 


                    2226 ;812: 		goCB->daCount++;


                    2227 

0000093c e2810001   2228 	add	r0,r1,1

00000940 e5944000   2229 	ldr	r4,[r4]

00000944 e5850180   2230 	str	r0,[r5,384]

                    2231 ;813: 		dsItem = dsItem->next;


                    2232 

00000948 e3540000   2233 	cmp	r4,0

0000094c 1affffec   2234 	bne	.L2451

                    2235 .L2464:

                    2236 ;814: 	}


                    2237 ;815: 


                    2238 ;816: 


                    2239 ;817: 	return true;


                    2240 


                                                                      Page 39
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    2241 ;821: {


                    2242 

                    2243 ;822: 	size_t gseIdx;


                    2244 ;823: 	BufferView gooseBufView;


                    2245 ;824: 	//Цикл по GSE с созданием шаблонов сообщений


                    2246 ;825: 	for (gseIdx = 0; gseIdx < goCB->gseCount; ++gseIdx)


                    2247 

00000950 e285706c   2248 	add	r7,r5,108

00000954 e5950068   2249 	ldr	r0,[r5,104]

00000958 e28da00c   2250 	add	r10,sp,12

0000095c e1540000   2251 	cmp	r4,r0

00000960 2a00001c   2252 	bhs	.L2460

                    2253 .L2465:

                    2254 ;826: 	{


                    2255 

                    2256 ;827: 		//Выделяем память для пакета


                    2257 ;828: 		GSESettings gse = goCB->gse + gseIdx;


                    2258 

00000964 e1a06007   2259 	mov	r6,r7

                    2260 ;829: 		uint8_t* buf = malloc(MTU_SIZE);


                    2261 

00000968 e3a00e50   2262 	mov	r0,5<<8

0000096c e28000c8   2263 	add	r0,r0,200

00000970 eb000000*  2264 	bl	malloc

                    2265 ;830: 		if (buf == NULL)


                    2266 

00000974 e3500000   2267 	cmp	r0,0

00000978 0a000014   2268 	beq	.L2461

                    2269 ;831: 		{


                    2270 

                    2271 ;832: 			ERROR_REPORT("Unable to allocate GOOSE buffer");


                    2272 ;833: 			return FALSE;


                    2273 

                    2274 ;834: 		}


                    2275 ;835: 		gse->outPkt = buf;


                    2276 

0000097c e3a02e50   2277 	mov	r2,5<<8

00000980 e28220c8   2278 	add	r2,r2,200

00000984 e5860024   2279 	str	r0,[r6,36]

                    2280 ;836: 		BufferView_init(&gooseBufView, buf, MTU_SIZE, 0);


                    2281 

00000988 e1a01000   2282 	mov	r1,r0

0000098c e1a0000a   2283 	mov	r0,r10

00000990 e3a03000   2284 	mov	r3,0

00000994 eb000000*  2285 	bl	BufferView_init

                    2286 ;837: 		if (!prepareGOOSEbuf(goCB, gse, &gooseBufView))


                    2287 

00000998 e1a0200a   2288 	mov	r2,r10

0000099c e1a01006   2289 	mov	r1,r6

000009a0 e1a00005   2290 	mov	r0,r5

000009a4 ebfffd9d*  2291 	bl	prepareGOOSEbuf

000009a8 e3500000   2292 	cmp	r0,0

000009ac 0a000007   2293 	beq	.L2461

                    2294 ;838: 		{


                    2295 

                    2296 ;839: 			ERROR_REPORT("Unable to prepare GOOSE out buffer");


                    2297 ;840: 			return FALSE;


                    2298 

                    2299 ;841: 		}


                    2300 ;842: 		gse->outPktSize = gooseBufView.pos;


                    2301 


                                                                      Page 40
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
000009b0 e59d0010   2302 	ldr	r0,[sp,16]

000009b4 e287705c   2303 	add	r7,r7,92

000009b8 e5860028   2304 	str	r0,[r6,40]

000009bc e5950068   2305 	ldr	r0,[r5,104]

000009c0 e2844001   2306 	add	r4,r4,1

000009c4 e1540000   2307 	cmp	r4,r0

000009c8 3affffe5   2308 	blo	.L2465

000009cc ea000001   2309 	b	.L2460

                    2310 .L2461:

                    2311 ;843: 		WRITE_TO_FILE("gse.bin", gse->outPkt, gooseBufView.pos);


                    2312 ;844: 	}


                    2313 ;845: 	return TRUE;


                    2314 

000009d0 e3a00000   2315 	mov	r0,0

000009d4 ea000006   2316 	b	.L2389

                    2317 .L2460:

000009d8 e1a00005   2318 	mov	r0,r5

000009dc ebffff1c*  2319 	bl	GOOSE_resetPktCounters

000009e0 e1a00005   2320 	mov	r0,r5

000009e4 ebffff27*  2321 	bl	GOOSE_resetPktTimers

000009e8 e3a00000   2322 	mov	r0,0

000009ec e5c5001c   2323 	strb	r0,[r5,28]

000009f0 e3a00001   2324 	mov	r0,1

                    2325 .L2389:

000009f4 e28dd030   2326 	add	sp,sp,48

000009f8 e8bd84f0   2327 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    2328 	.endf	GoCB_init

                    2329 	.align	4

                    2330 ;confRev	[sp,4]	local

                    2331 ;dataSetEntity	r1	local

                    2332 ;refBuf	[sp,36]	local

                    2333 ;gseBER	[sp,24]	local

                    2334 ;gseListTag	[sp,3]	local

                    2335 ;gseListLen	[sp,8]	local

                    2336 ;pGSEList	r1	local

                    2337 ;currPos	r1	local

                    2338 ;da	r6	local

                    2339 ;finalDAobj	r0	local

                    2340 ;dsItem	r4	local

                    2341 ;gseIdx	r4	local

                    2342 ;gooseBufView	[sp,12]	local

                    2343 ;gse	r6	local

                    2344 ;buf	r0	local

                    2345 

                    2346 ;goCB	r5	param

                    2347 ;goCBPos	r4	param

                    2348 

                    2349 	.data

                    2350 	.text

                    2351 

                    2352 

                    2353 	.align	4

                    2354 	.align	4

                    2355 registerGoCB:

000009fc e92d4000   2356 	stmfd	[sp]!,{lr}

                    2357 ;145: {	


                    2358 

                    2359 ;146: 	if (g_goCBCount == MAX_GOCB_COUNT)


                    2360 

00000a00 e59f25e4*  2361 	ldr	r2,.L3303

00000a04 e1a01000   2362 	mov	r1,r0


                                                                      Page 41
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
00000a08 e5920000   2363 	ldr	r0,[r2]

00000a0c e59fc5cc*  2364 	ldr	r12,.L3224

00000a10 e3500004   2365 	cmp	r0,4

                    2366 ;147: 	{


                    2367 

                    2368 ;148: 		return NULL;


                    2369 

00000a14 03a00000   2370 	moveq	r0,0

00000a18 058c100c   2371 	streq	r1,[r12,12]

00000a1c 0a000008   2372 	beq	.L3227

                    2373 ;149: 	}


                    2374 ;150: 	g_goCBCount++;


                    2375 

00000a20 e2800001   2376 	add	r0,r0,1

00000a24 e5820000   2377 	str	r0,[r2]

                    2378 ;151: 	return g_goCBs + g_goCBCount - 1;


                    2379 

00000a28 e3a02ea0   2380 	mov	r2,5<<9

00000a2c e28220e4   2381 	add	r2,r2,228

00000a30 e28c3010   2382 	add	r3,r12,16

00000a34 e0203092   2383 	mla	r0,r2,r0,r3

00000a38 e58c100c   2384 	str	r1,[r12,12]

00000a3c e0500002   2385 	subs	r0,r0,r2

00000a40 1bffff22*  2386 	blne	GoCB_init

                    2387 .L3227:

00000a44 e8bd4000   2388 	ldmfd	[sp]!,{lr}

00000a48 e12fff1e*  2389 	ret	

                    2390 	.endf	registerGoCB

                    2391 	.align	4

                    2392 ;goCB	r0	local

                    2393 

                    2394 ;goCBPos	r1	param

                    2395 

                    2396 	.data

                    2397 	.text

                    2398 

                    2399 

                    2400 	.align	4

                    2401 	.align	4

                    2402 registerGoCBsGivenFC::

00000a4c e92d4010   2403 	stmfd	[sp]!,{r4,lr}

00000a50 e59f1588*  2404 	ldr	r1,.L3224

00000a54 e24dd008   2405 	sub	sp,sp,8

00000a58 e5810008   2406 	str	r0,[r1,8]

00000a5c e1a0100d   2407 	mov	r1,sp

00000a60 e1a04000   2408 	mov	r4,r0

00000a64 eb000000*  2409 	bl	getObjectName

00000a68 e3500000   2410 	cmp	r0,0

00000a6c 0a00000a   2411 	beq	.L3304

00000a70 e59d0000   2412 	ldr	r0,[sp]

00000a74 e3500002   2413 	cmp	r0,2

00000a78 1a000007   2414 	bne	.L3304

00000a7c e59d1004   2415 	ldr	r1,[sp,4]

00000a80 e59f0568*  2416 	ldr	r0,.L3384

00000a84 e3a02002   2417 	mov	r2,2

00000a88 eb000000*  2418 	bl	memcmp

00000a8c e3500000   2419 	cmp	r0,0

00000a90 059f155c*  2420 	ldreq	r1,.L3385

00000a94 01a00004   2421 	moveq	r0,r4

00000a98 0b000000*  2422 	bleq	processSubobjects

                    2423 .L3304:


                                                                      Page 42
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
00000a9c e28dd008   2424 	add	sp,sp,8

00000aa0 e8bd8010   2425 	ldmfd	[sp]!,{r4,pc}

                    2426 	.endf	registerGoCBsGivenFC

                    2427 	.align	4

                    2428 ;fcName	[sp]	local

                    2429 ;.L3366	.L3369	static

                    2430 

                    2431 ;fcPos	r4	param

                    2432 

                    2433 	.section ".rodata","a"

                    2434 .L3369:;	"GO\000"

00000025 4f47      2435 	.data.b	71,79

00000027 00        2436 	.data.b	0

                    2437 	.type	.L3369,$object

                    2438 	.size	.L3369,3

                    2439 	.data

                    2440 	.text

                    2441 

                    2442 

                    2443 	.align	4

                    2444 	.align	4

                    2445 registerAllLogicalNodeGoCB:

00000aa4 e92d4000   2446 	stmfd	[sp]!,{lr}

00000aa8 e59f2530*  2447 	ldr	r2,.L3224

00000aac e59f1544*  2448 	ldr	r1,.L3412

00000ab0 e5820004   2449 	str	r0,[r2,4]

00000ab4 eb000000*  2450 	bl	processSubobjects

00000ab8 e8bd4000   2451 	ldmfd	[sp]!,{lr}

00000abc e12fff1e*  2452 	ret	

                    2453 	.endf	registerAllLogicalNodeGoCB

                    2454 	.align	4

                    2455 

                    2456 ;lnPos	none	param

                    2457 

                    2458 	.data

                    2459 	.text

                    2460 

                    2461 

                    2462 	.align	4

                    2463 	.align	4

                    2464 registerAllLogicalDeviceGoCB:

00000ac0 e92d4000   2465 	stmfd	[sp]!,{lr}

00000ac4 e59f1514*  2466 	ldr	r1,.L3224

00000ac8 e5810000   2467 	str	r0,[r1]

00000acc e3a010ec   2468 	mov	r1,236

00000ad0 eb000000*  2469 	bl	findObjectByTag

00000ad4 e3500000   2470 	cmp	r0,0

00000ad8 159f151c*  2471 	ldrne	r1,.L3458

00000adc 1b000000*  2472 	blne	processSubobjects

00000ae0 e8bd4000   2473 	ldmfd	[sp]!,{lr}

00000ae4 e12fff1e*  2474 	ret	

                    2475 	.endf	registerAllLogicalDeviceGoCB

                    2476 	.align	4

                    2477 ;dataSectionPos	r1	local

                    2478 

                    2479 ;ldPos	none	param

                    2480 

                    2481 	.data

                    2482 	.text

                    2483 

                    2484 


                                                                      Page 43
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    2485 	.align	4

                    2486 	.align	4

                    2487 GOOSE_send::

00000ae8 e92d4010   2488 	stmfd	[sp]!,{r4,lr}

00000aec e2804040   2489 	add	r4,r0,64

                    2490 ;872: {


                    2491 

                    2492 ;873: 	writeUlongBE(gse->stNum.p, gse->stNum.value & 0x7FFFFFFF);


                    2493 

00000af0 e8940009   2494 	ldmfd	[r4],{r0,r3}

00000af4 e3c31480   2495 	bic	r1,r3,1<<31

00000af8 ebfffd40*  2496 	bl	writeUlongBE

                    2497 ;874: 	writeUlongBE(gse->sqNum.p, gse->sqNum.value & 0x7FFFFFFF);


                    2498 

00000afc e9140009   2499 	ldmea	[r4],{r0,r3}

00000b00 e3c31480   2500 	bic	r1,r3,1<<31

00000b04 ebfffd3d*  2501 	bl	writeUlongBE

00000b08 e5140004   2502 	ldr	r0,[r4,-4]

00000b0c e5142018   2503 	ldr	r2,[r4,-24]

00000b10 e2800001   2504 	add	r0,r0,1

00000b14 e5040004   2505 	str	r0,[r4,-4]

00000b18 e514101c   2506 	ldr	r1,[r4,-28]

00000b1c e514003c   2507 	ldr	r0,[r4,-60]

00000b20 e8bd4010   2508 	ldmfd	[sp]!,{r4,lr}

00000b24 ea000000*  2509 	b	NetTools_send

                    2510 	.endf	GOOSE_send

                    2511 	.align	4

                    2512 

                    2513 ;gse	r4	param

                    2514 

                    2515 	.section ".bss","awb"

                    2516 .L3489:

                    2517 	.data

                    2518 	.text

                    2519 

                    2520 

                    2521 	.align	4

                    2522 	.align	4

                    2523 GOOSE_sendChanges::

00000b28 e92d4ff0   2524 	stmfd	[sp]!,{r4-fp,lr}

00000b2c e24dd014   2525 	sub	sp,sp,20

00000b30 eb000000*  2526 	bl	PTools_lockInterrupt

00000b34 e1a0a000   2527 	mov	r10,r0

00000b38 eb000000*  2528 	bl	DataSlice_getDataSliceWnd

00000b3c e3a08000   2529 	mov	r8,0

00000b40 e59f14a4*  2530 	ldr	r1,.L3303

00000b44 e1a09000   2531 	mov	r9,r0

00000b48 e5910000   2532 	ldr	r0,[r1]

00000b4c e1a01008   2533 	mov	r1,r8

00000b50 e1510000   2534 	cmp	r1,r0

00000b54 2a000054   2535 	bhs	.L3498

                    2536 .L3500:

00000b58 e1a03001   2537 	mov	r3,r1

00000b5c e3a01ea0   2538 	mov	r1,5<<9

00000b60 e59f2498*  2539 	ldr	r2,.L3836

00000b64 e28110e4   2540 	add	r1,r1,228

00000b68 e0252193   2541 	mla	r5,r3,r1,r2

00000b6c e5d51000   2542 	ldrb	r1,[r5]

00000b70 e3510000   2543 	cmp	r1,0

00000b74 0a000048   2544 	beq	.L3499

00000b78 e5d5401c   2545 	ldrb	r4,[r5,28]


                                                                      Page 44
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
00000b7c e3540000   2546 	cmp	r4,0

00000b80 1a000045   2547 	bne	.L3499

                    2548 ;1137: {


                    2549 

00000b84 e3a06000   2550 	mov	r6,0

00000b88 e5cd6007   2551 	strb	r6,[sp,7]

                    2552 ;1138: 	size_t daIdx;


                    2553 ;1139: 	//Есть изменения в каком-нибудь DA


                    2554 ;1140: 	bool changed = false;


                    2555 

                    2556 ;1141: 	//Конкретный DA изменился


                    2557 ;1142: 	bool daChanged = false;


                    2558 

                    2559 ;1143: 	


                    2560 ;1144: 	//Обязательно нужно обойти все DA, даже если изменения обнаружены


                    2561 ;1145: 	//раньше, чтобы считать все данные для передачи.


                    2562 ;1146: 	for (daIdx = 0; daIdx < goCB->daCount; ++daIdx)


                    2563 

00000b8c e5950180   2564 	ldr	r0,[r5,384]

00000b90 e2857f61   2565 	add	r7,r5,0x0184

00000b94 e1560000   2566 	cmp	r6,r0

00000b98 2a00000d   2567 	bhs	.L3507

                    2568 .L3510:

                    2569 ;1147: 	{


                    2570 

                    2571 ;1148:         FDA_readAndCompare(goCB->daList[daIdx], dataSliceWnd, &daChanged);


                    2572 

00000b9c e28d2007   2573 	add	r2,sp,7

00000ba0 e4970004   2574 	ldr	r0,[r7],4

00000ba4 e1a01009   2575 	mov	r1,r9

00000ba8 eb000000*  2576 	bl	FDA_readAndCompare

                    2577 ;1149: 		changed = (changed || daChanged);


                    2578 

00000bac e3540000   2579 	cmp	r4,0

00000bb0 05dd1007   2580 	ldreqb	r1,[sp,7]

00000bb4 e3a00001   2581 	mov	r0,1

00000bb8 03510000   2582 	cmpeq	r1,0

00000bbc 01a00001   2583 	moveq	r0,r1

00000bc0 e20040ff   2584 	and	r4,r0,255

00000bc4 e5950180   2585 	ldr	r0,[r5,384]

00000bc8 e2866001   2586 	add	r6,r6,1

00000bcc e1560000   2587 	cmp	r6,r0

00000bd0 3afffff1   2588 	blo	.L3510

                    2589 .L3507:

                    2590 ;1150: 	}


                    2591 ;1151: 	return changed;


                    2592 

00000bd4 e3540000   2593 	cmp	r4,0

00000bd8 0a00002d   2594 	beq	.L3670

00000bdc e285406c   2595 	add	r4,r5,108

                    2596 ;156: {


                    2597 

                    2598 ;157: 	uint64_t timeStamp = dataSliceGetTimeStamp();	


                    2599 

00000be0 e3a06000   2600 	mov	r6,0

00000be4 eb000000*  2601 	bl	dataSliceGetTimeStamp

                    2602 ;158: 	MMSData_encodeTimeStamp(0x84, timeStamp, gse->pPktTime, 0);


                    2603 

00000be8 e58d6000   2604 	str	r6,[sp]

00000bec e59530a0   2605 	ldr	r3,[r5,160]

00000bf0 e1a02001   2606 	mov	r2,r1


                                                                      Page 45
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
00000bf4 e1a01000   2607 	mov	r1,r0

00000bf8 e3a00084   2608 	mov	r0,132

00000bfc eb000000*  2609 	bl	MMSData_encodeTimeStamp

                    2610 ;1155: {


                    2611 

                    2612 ;1156: 	size_t daIdx;


                    2613 ;1157: 		


                    2614 ;1158: 	for (daIdx = 0; daIdx < goCB->daCount; ++daIdx)


                    2615 

00000c00 e5950180   2616 	ldr	r0,[r5,384]

00000c04 e2857f61   2617 	add	r7,r5,0x0184

00000c08 e1560000   2618 	cmp	r6,r0

00000c0c 2a000005   2619 	bhs	.L3521

                    2620 .L3524:

                    2621 ;1159: 	{


                    2622 

                    2623 ;1160: 		FDA_encodeFixedData(goCB->daList[daIdx]);


                    2624 

00000c10 e4970004   2625 	ldr	r0,[r7],4

00000c14 eb000000*  2626 	bl	FDA_encodeFixedData

00000c18 e5950180   2627 	ldr	r0,[r5,384]

00000c1c e2866001   2628 	add	r6,r6,1

00000c20 e1560000   2629 	cmp	r6,r0

00000c24 3afffff9   2630 	blo	.L3524

                    2631 .L3521:

00000c28 e285706c   2632 	add	r7,r5,108

00000c2c e5940044   2633 	ldr	r0,[r4,68]

00000c30 e3a0b004   2634 	mov	fp,4

00000c34 e2800001   2635 	add	r0,r0,1

00000c38 e5840044   2636 	str	r0,[r4,68]

00000c3c e3a00001   2637 	mov	r0,1

00000c40 e584003c   2638 	str	r0,[r4,60]

00000c44 e5950068   2639 	ldr	r0,[r5,104]

00000c48 e3a06000   2640 	mov	r6,0

00000c4c e1560000   2641 	cmp	r6,r0

00000c50 2a00000f   2642 	bhs	.L3670

                    2643 .L3529:

00000c54 e3560000   2644 	cmp	r6,0

                    2645 ;1166: {


                    2646 

                    2647 ;1167: 	memcpy(gseSrc->pPDU, gseDst->pPDU, gseSrc->pduSize);


                    2648 

00000c58 e1a04007   2649 	mov	r4,r7

00000c5c 15950098   2650 	ldrne	r0,[r5,152]

00000c60 1594102c   2651 	ldrne	r1,[r4,44]

00000c64 1595209c   2652 	ldrne	r2,[r5,156]

00000c68 1b000000*  2653 	blne	memcpy

00000c6c e5b4004c   2654 	ldr	r0,[r4,76]!

00000c70 e3a0c000   2655 	mov	r12,0

00000c74 e9841801   2656 	stmfa	[r4],{r0,fp-r12}

00000c78 e287705c   2657 	add	r7,r7,92

00000c7c e244004c   2658 	sub	r0,r4,76

00000c80 ebffff98*  2659 	bl	GOOSE_send

00000c84 e5950068   2660 	ldr	r0,[r5,104]

00000c88 e2866001   2661 	add	r6,r6,1

00000c8c e1560000   2662 	cmp	r6,r0

00000c90 3affffef   2663 	blo	.L3529

                    2664 .L3670:

00000c94 e59f1350*  2665 	ldr	r1,.L3303

00000c98 e5910000   2666 	ldr	r0,[r1]

                    2667 .L3499:


                                                                      Page 46
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
00000c9c e2888001   2668 	add	r8,r8,1

00000ca0 e1a01008   2669 	mov	r1,r8

00000ca4 e1510000   2670 	cmp	r1,r0

00000ca8 3affffaa   2671 	blo	.L3500

                    2672 .L3498:

00000cac e1a0000a   2673 	mov	r0,r10

00000cb0 eb000000*  2674 	bl	PTools_unlockInterrupt

00000cb4 e28dd014   2675 	add	sp,sp,20

00000cb8 e8bd8ff0   2676 	ldmfd	[sp]!,{r4-fp,pc}

                    2677 	.endf	GOOSE_sendChanges

                    2678 	.align	4

                    2679 ;gse	r4	local

                    2680 ;goCBIdx	r8	local

                    2681 ;gseIdx	r6	local

                    2682 ;dataSliceWnd	r9	local

                    2683 ;interruptState	r10	local

                    2684 ;goCB	r5	local

                    2685 ;daIdx	r6	local

                    2686 ;changed	r4	local

                    2687 ;daChanged	[sp,7]	local

                    2688 ;daIdx	r6	local

                    2689 

                    2690 	.data

                    2691 	.text

                    2692 

                    2693 

                    2694 ;1237: 


                    2695 ;1238: static void timerProc(void)


                    2696 	.align	4

                    2697 	.align	4

                    2698 timerProc:

00000cbc e92d4cf0   2699 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                    2700 ;1239: {


                    2701 

                    2702 ;1240: 	size_t goCBIdx;


                    2703 ;1241: 	size_t gseIdx;


                    2704 ;1242:     int interruptState = PTools_lockInterrupt();


                    2705 

00000cc0 eb000000*  2706 	bl	PTools_lockInterrupt

00000cc4 e59f1320*  2707 	ldr	r1,.L3303

00000cc8 e1a0b000   2708 	mov	fp,r0

                    2709 ;1243: 	for (goCBIdx = 0; goCBIdx < g_goCBCount; ++goCBIdx)


                    2710 

00000ccc e5910000   2711 	ldr	r0,[r1]

00000cd0 e3a0a000   2712 	mov	r10,0

00000cd4 e15a0000   2713 	cmp	r10,r0

00000cd8 2a000038   2714 	bhs	.L3839

                    2715 .L3841:

                    2716 ;1244: 	{


                    2717 

                    2718 ;1245: 		GoCB goCB = g_goCBs + goCBIdx;


                    2719 

00000cdc e3a01ea0   2720 	mov	r1,5<<9

00000ce0 e59f2318*  2721 	ldr	r2,.L3836

00000ce4 e28110e4   2722 	add	r1,r1,228

00000ce8 e026219a   2723 	mla	r6,r10,r1,r2

                    2724 ;1246: 


                    2725 ;1247: 		if (goCB->goEna && !goCB->ndsCom)


                    2726 

00000cec e5d61000   2727 	ldrb	r1,[r6]

00000cf0 e3510000   2728 	cmp	r1,0


                                                                      Page 47
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
00000cf4 0a00002e   2729 	beq	.L3840

00000cf8 e5d6401c   2730 	ldrb	r4,[r6,28]

00000cfc e3540000   2731 	cmp	r4,0

00000d00 1a00002b   2732 	bne	.L3840

                    2733 ;1248: 		{		


                    2734 

                    2735 ;1249: 			for (gseIdx = 0; gseIdx < goCB->gseCount; ++gseIdx)


                    2736 

00000d04 e286506c   2737 	add	r5,r6,108

00000d08 e5960068   2738 	ldr	r0,[r6,104]

00000d0c e3a07000   2739 	mov	r7,0

00000d10 e1540000   2740 	cmp	r4,r0

00000d14 2a000024   2741 	bhs	.L3960

                    2742 .L3852:

                    2743 ;1250: 			{


                    2744 

                    2745 ;1251: 				processGSETimer(goCB->gse + gseIdx);


                    2746 

                    2747 ;1214: {	    


                    2748 

                    2749 ;1215: 	if (gse->msCounter < gse->currT)


                    2750 

00000d18 e5951050   2751 	ldr	r1,[r5,80]

00000d1c e5950058   2752 	ldr	r0,[r5,88]

00000d20 e1500001   2753 	cmp	r0,r1

00000d24 2a000007   2754 	bhs	.L3854

                    2755 ;1216: 	{


                    2756 

                    2757 ;1217: 		gse->msCounter++;


                    2758 

00000d28 e2800001   2759 	add	r0,r0,1

00000d2c e5850058   2760 	str	r0,[r5,88]

00000d30 e285505c   2761 	add	r5,r5,92

00000d34 e5960068   2762 	ldr	r0,[r6,104]

00000d38 e2844001   2763 	add	r4,r4,1

00000d3c e1540000   2764 	cmp	r4,r0

00000d40 3afffff4   2765 	blo	.L3852

00000d44 ea000018   2766 	b	.L3960

                    2767 .L3854:

                    2768 ;1218: 	}


                    2769 ;1219: 	else


                    2770 ;1220: 	{


                    2771 

                    2772 ;1221: 		GOOSE_send(gse);                        


                    2773 

00000d48 e1a00005   2774 	mov	r0,r5

00000d4c ebffff65*  2775 	bl	GOOSE_send

                    2776 ;1222: 		gse->msCounter = 0;


                    2777 

00000d50 e5950054   2778 	ldr	r0,[r5,84]

00000d54 e5857058   2779 	str	r7,[r5,88]

                    2780 ;1223:         if(gse->t1Counter > 1)


                    2781 

00000d58 e3500001   2782 	cmp	r0,1

00000d5c 9a000007   2783 	bls	.L3856

                    2784 ;1224:         {


                    2785 

                    2786 ;1225:             gse->t1Counter--;


                    2787 

00000d60 e2400001   2788 	sub	r0,r0,1

00000d64 e5850054   2789 	str	r0,[r5,84]


                                                                      Page 48
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
00000d68 e285505c   2790 	add	r5,r5,92

00000d6c e5960068   2791 	ldr	r0,[r6,104]

00000d70 e2844001   2792 	add	r4,r4,1

00000d74 e1540000   2793 	cmp	r4,r0

00000d78 3affffe6   2794 	blo	.L3852

00000d7c ea00000a   2795 	b	.L3960

                    2796 .L3856:

                    2797 ;1226:         }


                    2798 ;1227:         else


                    2799 ;1228:         {


                    2800 

                    2801 ;1229:             gse->currT *= 2;


                    2802 

00000d80 e5950050   2803 	ldr	r0,[r5,80]

00000d84 e5951048   2804 	ldr	r1,[r5,72]

00000d88 e1a00080   2805 	mov	r0,r0 lsl 1

00000d8c e5850050   2806 	str	r0,[r5,80]

                    2807 ;1230:             if (gse->currT > gse->t0)


                    2808 

00000d90 e1500001   2809 	cmp	r0,r1

                    2810 ;1231:             {


                    2811 

                    2812 ;1232:                 gse->currT = gse->t0;


                    2813 

00000d94 85851050   2814 	strhi	r1,[r5,80]

00000d98 e285505c   2815 	add	r5,r5,92

00000d9c e5960068   2816 	ldr	r0,[r6,104]

00000da0 e2844001   2817 	add	r4,r4,1

00000da4 e1540000   2818 	cmp	r4,r0

00000da8 3affffda   2819 	blo	.L3852

                    2820 .L3960:

00000dac e59f1238*  2821 	ldr	r1,.L3303

00000db0 e5910000   2822 	ldr	r0,[r1]

                    2823 .L3840:

00000db4 e28aa001   2824 	add	r10,r10,1

00000db8 e15a0000   2825 	cmp	r10,r0

00000dbc 3affffc6   2826 	blo	.L3841

                    2827 .L3839:

                    2828 ;1252: 			}


                    2829 ;1253: 		}


                    2830 ;1254: 	}


                    2831 ;1255:     PTools_unlockInterrupt(interruptState);


                    2832 

00000dc0 e1a0000b   2833 	mov	r0,fp

00000dc4 eb000000*  2834 	bl	PTools_unlockInterrupt

00000dc8 e8bd4cf0   2835 	ldmfd	[sp]!,{r4-r7,r10-fp,lr}

00000dcc e12fff1e*  2836 	ret	

                    2837 	.endf	timerProc

                    2838 	.align	4

                    2839 ;goCBIdx	r10	local

                    2840 ;gseIdx	r4	local

                    2841 ;interruptState	fp	local

                    2842 ;goCB	r6	local

                    2843 

                    2844 	.data

                    2845 	.text

                    2846 

                    2847 ;1256: }


                    2848 

                    2849 ;1257: 


                    2850 ;1258: bool GOOSE_getGoEna(size_t cbIndex, bool* goEna)



                                                                      Page 49
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    2851 	.align	4

                    2852 	.align	4

                    2853 GOOSE_getGoEna::

                    2854 ;1259: {


                    2855 

                    2856 ;1260: 	if (cbIndex >= g_goCBCount)


                    2857 

00000dd0 e59f3214*  2858 	ldr	r3,.L3303

00000dd4 e5932000   2859 	ldr	r2,[r3]

00000dd8 e1500002   2860 	cmp	r0,r2

                    2861 ;1261: 	{


                    2862 

                    2863 ;1262: 		ERROR_REPORT("GoCB index is too big");


                    2864 ;1263: 		return FALSE;


                    2865 

00000ddc 23a00000   2866 	movhs	r0,0

00000de0 2a000006   2867 	bhs	.L4085

                    2868 ;1264: 	}


                    2869 ;1265: 	*goEna = g_goCBs[cbIndex].goEna;


                    2870 

00000de4 e3a02ea0   2871 	mov	r2,5<<9

00000de8 e28220e4   2872 	add	r2,r2,228

00000dec e0000092   2873 	mul	r0,r2,r0

00000df0 e59f2208*  2874 	ldr	r2,.L3836

00000df4 e7d00002   2875 	ldrb	r0,[r0,r2]

00000df8 e5c10000   2876 	strb	r0,[r1]

                    2877 ;1266: 	return TRUE;


                    2878 

00000dfc e3a00001   2879 	mov	r0,1

                    2880 .L4085:

00000e00 e12fff1e*  2881 	ret	

                    2882 	.endf	GOOSE_getGoEna

                    2883 	.align	4

                    2884 

                    2885 ;cbIndex	r0	param

                    2886 ;goEna	r1	param

                    2887 

                    2888 	.data

                    2889 	.text

                    2890 

                    2891 ;1267: }


                    2892 

                    2893 ;1268: 


                    2894 ;1269: bool GOOSE_getNdsCom(size_t cbIndex, bool* ndsCom)


                    2895 	.align	4

                    2896 	.align	4

                    2897 GOOSE_getNdsCom::

                    2898 ;1270: {


                    2899 

                    2900 ;1271: 	if (cbIndex >= g_goCBCount)


                    2901 

00000e04 e59f31e0*  2902 	ldr	r3,.L3303

00000e08 e5932000   2903 	ldr	r2,[r3]

00000e0c e1500002   2904 	cmp	r0,r2

                    2905 ;1272: 	{


                    2906 

                    2907 ;1273: 		ERROR_REPORT("GoCB index is too big");


                    2908 ;1274: 		return FALSE;


                    2909 

00000e10 23a00000   2910 	movhs	r0,0

00000e14 2a000006   2911 	bhs	.L4136


                                                                      Page 50
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    2912 ;1275: 	}


                    2913 ;1276: 	*ndsCom = g_goCBs[cbIndex].ndsCom;


                    2914 

00000e18 e3a03ea0   2915 	mov	r3,5<<9

00000e1c e59f21dc*  2916 	ldr	r2,.L3836

00000e20 e28330e4   2917 	add	r3,r3,228

00000e24 e0202093   2918 	mla	r0,r3,r0,r2

00000e28 e5d0001c   2919 	ldrb	r0,[r0,28]

00000e2c e5c10000   2920 	strb	r0,[r1]

                    2921 ;1277: 	return TRUE;


                    2922 

00000e30 e3a00001   2923 	mov	r0,1

                    2924 .L4136:

00000e34 e12fff1e*  2925 	ret	

                    2926 	.endf	GOOSE_getNdsCom

                    2927 	.align	4

                    2928 

                    2929 ;cbIndex	r0	param

                    2930 ;ndsCom	r1	param

                    2931 

                    2932 	.data

                    2933 	.text

                    2934 

                    2935 ;1278: }


                    2936 

                    2937 ;1279: 


                    2938 ;1280: bool GOOSE_setGoEna(size_t cbIndex, bool value)


                    2939 	.align	4

                    2940 	.align	4

                    2941 GOOSE_setGoEna::

00000e38 e92d40f0   2942 	stmfd	[sp]!,{r4-r7,lr}

                    2943 ;1281: {


                    2944 

                    2945 ;1282: 	GoCB goCB;


                    2946 ;1283: 	if (cbIndex >= g_goCBCount)


                    2947 

00000e3c e59f21a8*  2948 	ldr	r2,.L3303

00000e40 e1a04001   2949 	mov	r4,r1

00000e44 e5921000   2950 	ldr	r1,[r2]

00000e48 e1500001   2951 	cmp	r0,r1

00000e4c 2a000007   2952 	bhs	.L4185

                    2953 ;1284: 	{


                    2954 

                    2955 ;1285: 		ERROR_REPORT("GoCB index is too big");


                    2956 ;1286: 		return FALSE;


                    2957 

                    2958 ;1287: 	}


                    2959 ;1288: 	goCB = g_goCBs + cbIndex;


                    2960 

00000e50 e3a01ea0   2961 	mov	r1,5<<9

00000e54 e28110e4   2962 	add	r1,r1,228

00000e58 e0060190   2963 	mul	r6,r0,r1

00000e5c e59f719c*  2964 	ldr	r7,.L3836

00000e60 e0865007   2965 	add	r5,r6,r7

                    2966 ;1289: 


                    2967 ;1290: 	if (goCB->ndsCom)


                    2968 

00000e64 e5d5001c   2969 	ldrb	r0,[r5,28]

00000e68 e3500000   2970 	cmp	r0,0

00000e6c 0a000001   2971 	beq	.L4184

                    2972 .L4185:


                                                                      Page 51
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    2973 ;1291: 	{


                    2974 

                    2975 ;1292: 		return false;


                    2976 

00000e70 e3a00000   2977 	mov	r0,0

00000e74 ea00000a   2978 	b	.L4179

                    2979 .L4184:

                    2980 ;1293: 	}


                    2981 ;1294: 


                    2982 ;1295: 	if (!goCB->goEna && value)


                    2983 

00000e78 e5d50000   2984 	ldrb	r0,[r5]

00000e7c e3500000   2985 	cmp	r0,0

00000e80 1a000005   2986 	bne	.L4187

00000e84 e3540000   2987 	cmp	r4,0

00000e88 0a000003   2988 	beq	.L4187

                    2989 ;1296: 	{


                    2990 

                    2991 ;1297: 		//Если включаем


                    2992 ;1298:         GOOSE_resetPktCounters(goCB);


                    2993 

00000e8c e1a00005   2994 	mov	r0,r5

00000e90 ebfffdef*  2995 	bl	GOOSE_resetPktCounters

                    2996 ;1299: 		GOOSE_resetPktTimers(goCB);


                    2997 

00000e94 e1a00005   2998 	mov	r0,r5

00000e98 ebfffdfa*  2999 	bl	GOOSE_resetPktTimers

                    3000 .L4187:

                    3001 ;1300: 	}


                    3002 ;1301: 	g_goCBs[cbIndex].goEna = value;


                    3003 

00000e9c e7c74006   3004 	strb	r4,[r7,r6]

                    3005 ;1302: 	return TRUE;


                    3006 

00000ea0 e3a00001   3007 	mov	r0,1

                    3008 .L4179:

00000ea4 e8bd80f0   3009 	ldmfd	[sp]!,{r4-r7,pc}

                    3010 	.endf	GOOSE_setGoEna

                    3011 	.align	4

                    3012 ;goCB	r5	local

                    3013 

                    3014 ;cbIndex	r0	param

                    3015 ;value	r4	param

                    3016 

                    3017 	.data

                    3018 	.text

                    3019 

                    3020 ;1303: }


                    3021 

                    3022 ;1304: 


                    3023 ;1305: void GOOSE_init(void)


                    3024 	.align	4

                    3025 	.align	4

                    3026 GOOSE_init::

00000ea8 e92d44f0   3027 	stmfd	[sp]!,{r4-r7,r10,lr}

00000eac e59f414c*  3028 	ldr	r4,.L3836

00000eb0 e59f114c*  3029 	ldr	r1,.L4707

                    3030 ;1306: {


                    3031 

                    3032 ;1307:     size_t i;


                    3033 ;1308:     registerAllGoCB();    	            



                                                                      Page 52
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    3034 

                    3035 ;1125: {


                    3036 

                    3037 ;1126: 	processSubobjects(0, registerAllLogicalDeviceGoCB);


                    3038 

00000eb4 e3a00000   3039 	mov	r0,0

00000eb8 eb000000*  3040 	bl	processSubobjects

                    3041 ;1309:     DataSlice_setCallBack(GOOSE_sendChanges);


                    3042 

00000ebc e59f0144*  3043 	ldr	r0,.L4708

00000ec0 eb000000*  3044 	bl	DataSlice_setCallBack

                    3045 ;1310:     Timers_setGoose1msCallBack(timerProc);


                    3046 

00000ec4 e59f0140*  3047 	ldr	r0,.L4709

00000ec8 eb000000*  3048 	bl	Timers_setGoose1msCallBack

                    3049 ;1311: 


                    3050 ;1312:     //Включаем все GoCB


                    3051 ;1313:     for(i = 0; i < g_goCBCount; i++)


                    3052 

00000ecc e59f1118*  3053 	ldr	r1,.L3303

00000ed0 e5910000   3054 	ldr	r0,[r1]

00000ed4 e3a06000   3055 	mov	r6,0

00000ed8 e3500000   3056 	cmp	r0,0

00000edc a1a07000   3057 	movge	r7,r0

00000ee0 b3a07000   3058 	movlt	r7,0

00000ee4 e1b031a7   3059 	movs	r3,r7 lsr 3

00000ee8 0a000029   3060 	beq	.L4320

00000eec e2840c41   3061 	add	r0,r4,65<<8

00000ef0 e2800058   3062 	add	r0,r0,88

00000ef4 e2841d54   3063 	add	r1,r4,21<<8

00000ef8 e28110c8   3064 	add	r1,r1,200

00000efc e2842dd8   3065 	add	r2,r4,54<<8

00000f00 e282c074   3066 	add	r12,r2,116

00000f04 e2842ea0   3067 	add	r2,r4,5<<9

00000f08 e28250e4   3068 	add	r5,r2,228

00000f0c e3a02001   3069 	mov	r2,1

00000f10 e1a06183   3070 	mov	r6,r3 lsl 3

                    3071 .L4321:

00000f14 e555aac8   3072 	ldrb	r10,[r5,-2760]

00000f18 e35a0000   3073 	cmp	r10,0

00000f1c 05452ae4   3074 	streqb	r2,[r5,-2788]

00000f20 e551aac8   3075 	ldrb	r10,[r1,-2760]

00000f24 e35a0000   3076 	cmp	r10,0

00000f28 e5d1a01c   3077 	ldrb	r10,[r1,28]

00000f2c 05412ae4   3078 	streqb	r2,[r1,-2788]

00000f30 e35a0000   3079 	cmp	r10,0

00000f34 e5d1ab00   3080 	ldrb	r10,[r1,2816]

00000f38 05c12000   3081 	streqb	r2,[r1]

00000f3c e35a0000   3082 	cmp	r10,0

00000f40 05c12ae4   3083 	streqb	r2,[r1,2788]

00000f44 e55caac8   3084 	ldrb	r10,[r12,-2760]

00000f48 e35a0000   3085 	cmp	r10,0

00000f4c 054c2ae4   3086 	streqb	r2,[r12,-2788]

00000f50 e550aac8   3087 	ldrb	r10,[r0,-2760]

00000f54 e35a0000   3088 	cmp	r10,0

00000f58 e5d0a01c   3089 	ldrb	r10,[r0,28]

00000f5c 05402ae4   3090 	streqb	r2,[r0,-2788]

00000f60 e35a0000   3091 	cmp	r10,0

00000f64 e5d0ab00   3092 	ldrb	r10,[r0,2816]

00000f68 05c02000   3093 	streqb	r2,[r0]

00000f6c e35a0000   3094 	cmp	r10,0


                                                                      Page 53
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
00000f70 e3a0ac57   3095 	mov	r10,87<<8

00000f74 e28aa020   3096 	add	r10,r10,32

00000f78 05c02ae4   3097 	streqb	r2,[r0,2788]

00000f7c e080000a   3098 	add	r0,r0,r10

00000f80 e081100a   3099 	add	r1,r1,r10

00000f84 e08cc00a   3100 	add	r12,r12,r10

00000f88 e085500a   3101 	add	r5,r5,r10

00000f8c e2533001   3102 	subs	r3,r3,1

00000f90 1affffdf   3103 	bne	.L4321

                    3104 .L4320:

00000f94 e2173007   3105 	ands	r3,r7,7

00000f98 0a00000a   3106 	beq	.L4285

00000f9c e3a00ea0   3107 	mov	r0,5<<9

00000fa0 e28000e4   3108 	add	r0,r0,228

00000fa4 e0204096   3109 	mla	r0,r6,r0,r4

00000fa8 e3a02001   3110 	mov	r2,1

                    3111 .L4355:

00000fac e5d0101c   3112 	ldrb	r1,[r0,28]

00000fb0 e3510000   3113 	cmp	r1,0

00000fb4 e2801ea0   3114 	add	r1,r0,5<<9

00000fb8 05c02000   3115 	streqb	r2,[r0]

00000fbc e28100e4   3116 	add	r0,r1,228

00000fc0 e2533001   3117 	subs	r3,r3,1

00000fc4 1afffff8   3118 	bne	.L4355

                    3119 .L4285:

00000fc8 e8bd84f0   3120 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    3121 	.endf	GOOSE_init

                    3122 	.align	4

                    3123 .L1657:

00000fcc 00000000*  3124 	.data.w	.L1584

                    3125 	.type	.L1657,$object

                    3126 	.size	.L1657,4

                    3127 

                    3128 .L2289:

00000fd0 00000000*  3129 	.data.w	.L2272

                    3130 	.type	.L2289,$object

                    3131 	.size	.L2289,4

                    3132 

                    3133 .L3221:

00000fd4 00000000*  3134 	.data.w	.L1589

                    3135 	.type	.L3221,$object

                    3136 	.size	.L3221,4

                    3137 

                    3138 .L3222:

00000fd8 00000000*  3139 	.data.w	.L1585

                    3140 	.type	.L3222,$object

                    3141 	.size	.L3222,4

                    3142 

                    3143 .L3223:

00000fdc 00000000*  3144 	.data.w	.L1590

                    3145 	.type	.L3223,$object

                    3146 	.size	.L3223,4

                    3147 

                    3148 .L3224:

00000fe0 00000000*  3149 	.data.w	g_goCBPath

                    3150 	.type	.L3224,$object

                    3151 	.size	.L3224,4

                    3152 

                    3153 .L3225:

00000fe4 00000000*  3154 	.data.w	.L1587

                    3155 	.type	.L3225,$object


                                                                      Page 54
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    3156 	.size	.L3225,4

                    3157 

                    3158 .L3226:

00000fe8 00000000*  3159 	.data.w	.L1588

                    3160 	.type	.L3226,$object

                    3161 	.size	.L3226,4

                    3162 

                    3163 .L3303:

00000fec 00000000*  3164 	.data.w	.L1586

                    3165 	.type	.L3303,$object

                    3166 	.size	.L3303,4

                    3167 

                    3168 .L3384:

00000ff0 00000000*  3169 	.data.w	.L3369

                    3170 	.type	.L3384,$object

                    3171 	.size	.L3384,4

                    3172 

                    3173 .L3385:

00000ff4 00000000*  3174 	.data.w	registerGoCB

                    3175 	.type	.L3385,$object

                    3176 	.size	.L3385,4

                    3177 

                    3178 .L3412:

00000ff8 00000000*  3179 	.data.w	registerGoCBsGivenFC

                    3180 	.type	.L3412,$object

                    3181 	.size	.L3412,4

                    3182 

                    3183 .L3458:

00000ffc 00000000*  3184 	.data.w	registerAllLogicalNodeGoCB

                    3185 	.type	.L3458,$object

                    3186 	.size	.L3458,4

                    3187 

                    3188 .L3836:

00001000 00000000*  3189 	.data.w	g_goCBs

                    3190 	.type	.L3836,$object

                    3191 	.size	.L3836,4

                    3192 

                    3193 .L4707:

00001004 00000000*  3194 	.data.w	registerAllLogicalDeviceGoCB

                    3195 	.type	.L4707,$object

                    3196 	.size	.L4707,4

                    3197 

                    3198 .L4708:

00001008 00000000*  3199 	.data.w	GOOSE_sendChanges

                    3200 	.type	.L4708,$object

                    3201 	.size	.L4708,4

                    3202 

                    3203 .L4709:

0000100c 00000000*  3204 	.data.w	timerProc

                    3205 	.type	.L4709,$object

                    3206 	.size	.L4709,4

                    3207 

                    3208 	.align	4

                    3209 ;i	r6	local

                    3210 

                    3211 	.data

                    3212 	.text

                    3213 

                    3214 ;1318:         }


                    3215 ;1319:     }


                    3216 ;1320: }



                                                                      Page 55
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b981.s
                    3217 	.align	4

                    3218 ;g_goCBCount	.L1586	static

                    3219 ;g_goCBs	g_goCBs	static

                    3220 ;g_goCBPath	g_goCBPath	static

                    3221 ;__UNNAMED_1_static_in_writeGOOSEPduTemplate	.L1584	static

                    3222 ;__UNNAMED_1_static_in_initDatSet	.L1590	static

                    3223 ;__UNNAMED_1_static_in_initGoCBRef	.L1587	static

                    3224 ;__UNNAMED_2_static_in_initGoCBRef	.L1588	static

                    3225 ;__UNNAMED_1_static_in_initGoCBvars	.L1589	static

                    3226 ;__UNNAMED_2_static_in_initGoCBvars	.L1585	static

                    3227 

                    3228 	.data

                    3229 	.ghsnote version,6

                    3230 	.ghsnote tools,3

                    3231 	.ghsnote options,0

                    3232 	.text

                    3233 	.align	4

                    3234 	.data

                    3235 	.align	4

                    3236 	.section ".bss","awb"

                    3237 	.align	4

                    3238 	.section ".rodata","a"

                    3239 	.align	4

                    3240 	.text

