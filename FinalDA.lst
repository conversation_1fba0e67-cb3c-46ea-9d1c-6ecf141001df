                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=FinalDA.c -o gh_8ok1.o -list=FinalDA.lst C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s
Source File: FinalDA.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile FinalDA.c -o

                      11 ;		FinalDA.o

                      12 ;Source File:   FinalDA.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:56 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: //В этом файле FinalDA исключительно для GOOSE


                      23 ;2: 


                      24 ;3: #include "FinalDA.h"


                      25 ;4: #include "MemoryManager.h"


                      26 ;5: #include "IEDCompile/AccessInfo.h"


                      27 ;6: #include "IEDCompile/InnerAttributeTypes.h"


                      28 ;7: #include "iedmodel.h"


                      29 ;8: #include "BaseAsnTypes.h"


                      30 ;9: #include "AsnEncoding.h"


                      31 ;10: #include "mms_data.h"


                      32 ;11: #include "DataSlice.h"


                      33 ;12: #include <debug.h>


                      34 ;13: #include <stdint.h>


                      35 ;14: 


                      36 ;15: #define QUALITY_ENCODED_SIZE 5


                      37 ;16: #define CODED_ENUM_ENCODED_SIZE 4


                      38 ;17: 


                      39 ;18: struct FDAQualityStruct


                      40 ;19: {


                      41 ;20: 	enum InnerAttributeType attrType;


                      42 ;21:     QualityAccsessInfo accessInfo;


                      43 ;22: 	uint16_t value;


                      44 ;23: 	uint8_t* fixedEncodeBuf;


                      45 ;24: };


                      46 ;25: 


                      47 ;26: typedef struct FDAQualityStruct* FDAQuality;


                      48 ;27: 


                      49 ;28: static void FDAQuality_init(FDAQuality q, QualityAccsessInfo* accessInfo)


                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s
                      51 ;59:             DataSlice_getBoolOffset(info->operatorBlockedOffset);


                      52 ;60: }


                      53 

                      54 ;61: 


                      55 ;62: FDAQuality FDAQuality_create(QualityAccsessInfo* accessInfo)


                      56 ;63: {


                      57 ;64: 	FDAQuality q = MM_alloc(sizeof(struct FDAQualityStruct));


                      58 ;65: 	if (q == NULL)


                      59 ;66: 	{


                      60 ;67: 		return NULL;


                      61 ;68: 	}


                      62 ;69: 	FDAQuality_init(q, accessInfo);


                      63 ;70: 	return q;


                      64 ;71: }


                      65 ;72: 


                      66 ;73: bool FDAQuality_getFixedEncodedSize(FDAQuality q, size_t* size)


                      67 ;74: {


                      68 ;75: 	*size = QUALITY_ENCODED_SIZE;


                      69 ;76: 	return TRUE;


                      70 ;77: }


                      71 ;78: 


                      72 ;79: bool FDAQuality_encodeGOOSETemplate(FDAQuality q, BufferView* outBuf)


                      73 ;80: {


                      74 ;81: 	uint16_t dummy = 0;


                      75 ;82: 	uint8_t *pEncodeBuf;	


                      76 ;83: 


                      77 ;84: 	if (!BufferView_alloc(outBuf, QUALITY_ENCODED_SIZE, &pEncodeBuf))


                      78 ;85: 	{


                      79 ;86: 		ERROR_REPORT("Trouble creating Quality template");


                      80 ;87: 		return FALSE;


                      81 ;88: 	}


                      82 ;89: 


                      83 ;90: 	q->fixedEncodeBuf = pEncodeBuf;


                      84 ;91: 


                      85 ;92: 	BerEncoder_encodeBitStringUshortBuf(ASN_TYPEDESCRIPTION_BIT_STRING,


                      86 ;93: 		13, dummy, pEncodeBuf, 0);


                      87 ;94: 	if (!BufferView_advance(outBuf, QUALITY_ENCODED_SIZE))


                      88 ;95: 	{


                      89 ;96: 		ERROR_REPORT("Trouble creating Quality template");


                      90 ;97: 		return FALSE;


                      91 ;98: 	}


                      92 ;99: 	return TRUE;


                      93 ;100: }


                      94 ;101: 


                      95 ;102: 


                      96 ;103: bool FDAQuality_readAndCompare(FDAQuality q, void* dataSliceWnd,


                      97 ;104:                                  bool* changed)


                      98 ;105: {


                      99 ;106:     uint16_t newValue = qualityFromBitsFast(dataSliceWnd, &q->accessInfo);


                     100 ;107: 	*changed = (q->value != newValue);


                     101 ;108: 	q->value = newValue;


                     102 ;109: 	return TRUE;


                     103 ;110: }


                     104 ;111: 


                     105 ;112: 


                     106 ;113: void FDAQuality_encodeFixedData(FDAQuality q)


                     107 ;114: {


                     108 ;115: 	BerEncoder_encodeBitStringUshortBuf(ASN_TYPEDESCRIPTION_BIT_STRING,


                     109 ;116: 		13, q->value, q->fixedEncodeBuf, 0);


                     110 ;117: }


                     111 ;118: 



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s
                     112 ;119: //=========================Boolean===================================


                     113 ;120: struct FDABooleanStruct


                     114 ;121: {


                     115 ;122: 	enum InnerAttributeType attrType;


                     116 ;123:     IntBoolAccessInfo* accessInfo;


                     117 ;124:     //Смещение в окне DataSlice


                     118 ;125:     int dataSliceOffset;


                     119 ;126: 	bool value;


                     120 ;127: 	uint8_t* fixedEncodeBuf;


                     121 ;128: };


                     122 ;129: 


                     123 ;130: typedef struct FDABooleanStruct* FDABoolean;


                     124 ;131: 


                     125 ;132: 


                     126 ;133: static void FDABoolean_init(FDABoolean daBool, IntBoolAccessInfo* accessInfo)


                     127 

                     128 ;139: }


                     129 

                     130 ;140: 


                     131 ;141: static FDABoolean FDABoolean_create(IntBoolAccessInfo* accessInfo)


                     132 

                     133 ;150: }


                     134 

                     135 ;151: 


                     136 ;152: static bool FDABoolean_getFixedEncodedSize(FDABoolean da, size_t* size)


                     137 

                     138 ;156: }


                     139 

                     140 ;157: 


                     141 ;158: static bool FDABoolean_encodeGOOSETemplate(FDABoolean da, BufferView* outBuf)


                     142 

                     143 ;168: }


                     144 

                     145 ;169: 


                     146 ;170: static bool FDABoolean_readAndCompare(FDABoolean da,


                     147 

                     148 ;183: }


                     149 

                     150 ;184: 


                     151 ;185: static void FDABoolean_encodeFixedData(FDABoolean da)


                     152 

                     153 ;188: }


                     154 

                     155 ;189: 


                     156 ;190: //================================CODEDENUM===================================


                     157 ;191: //Поддерживает только два бита. Например, DPS.stVal


                     158 ;192: 


                     159 ;193: struct FDACodedEnumStruct


                     160 ;194: {


                     161 ;195: 	enum InnerAttributeType attrType;


                     162 ;196: 	CodedEnumAccessInfo* accessInfo;	


                     163 ;197:     //Смещения в окне DataSlice


                     164 ;198:     int dataSliceOffset0;


                     165 ;199:     int dataSliceOffset1;


                     166 ;200: 	uint8_t value;


                     167 ;201: 	uint8_t* fixedEncodeBuf;


                     168 ;202: };


                     169 ;203: 


                     170 ;204: typedef struct FDACodedEnumStruct* FDACodedEnum;


                     171 ;205: 


                     172 ;206: static void FDACodedEnum_init(FDACodedEnum da, CodedEnumAccessInfo* accessInfo)



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s
                     173 

                     174 ;212: }


                     175 

                     176 ;213: 


                     177 ;214: static FDACodedEnum FDACodedEnum_create(CodedEnumAccessInfo* accessInfo)


                     178 

                     179 ;230: }


                     180 

                     181 ;231: 


                     182 ;232: static bool FDACodedEnum_getFixedEncodedSize(FDACodedEnum da, size_t* size)


                     183 

                     184 ;236: }


                     185 

                     186 ;237: 


                     187 ;238: static bool FDACodedEnum_encodeGOOSETemplate(FDACodedEnum da, BufferView* outBuf)


                     188 

                     189 ;259: }


                     190 

                     191 ;260: 


                     192 ;261: static bool FDACodedEnum_readAndCompare(FDACodedEnum da,


                     193 

                     194 ;279: }


                     195 

                     196 ;280: 


                     197 ;281: 


                     198 ;282: static void FDACodedEnum_encodeFixedData(FDACodedEnum da)


                     199 

                     200 ;285: 		2, da->value, da->fixedEncodeBuf, 0);


                     201 ;286: }


                     202 

                     203 	.text

                     204 	.align	4

                     205 FDAQuality_create::

00000000 e92d4070    206 	stmfd	[sp]!,{r4-r6,lr}

00000004 e1a04000    207 	mov	r4,r0

00000008 e3a00048    208 	mov	r0,72

0000000c eb000000*   209 	bl	MM_alloc

00000010 e1b05000    210 	movs	r5,r0

00000014 0a000031    211 	beq	.L257

                     212 ;29: {	


                     213 

                     214 ;30:     QualityAccsessInfo* info = &q->accessInfo;


                     215 

00000018 e2856004    216 	add	r6,r5,4

                     217 ;31: 	q->attrType = INNER_TYPE_QUALITY;


                     218 

0000001c e3a00000    219 	mov	r0,0

00000020 e5850000    220 	str	r0,[r5]

                     221 ;32:     *info = *accessInfo;


                     222 

00000024 e8b4000f    223 	ldmfd	[r4]!,{r0-r3}

00000028 e8a6000f    224 	stmea	[r6]!,{r0-r3}

0000002c e8b4000f    225 	ldmfd	[r4]!,{r0-r3}

00000030 e8a6000f    226 	stmea	[r6]!,{r0-r3}

00000034 e8b4000f    227 	ldmfd	[r4]!,{r0-r3}

00000038 e8a6000f    228 	stmea	[r6]!,{r0-r3}

0000003c e8940007    229 	ldmfd	[r4],{r0-r2}

00000040 e8860007    230 	stmea	[r6],{r0-r2}

                     231 ;33:     //Заменяем абсолютные смещения на смещения в DataSlice


                     232 ;34:     info->goodInvalidOffset =


                     233 


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s
00000044 e5950008    234 	ldr	r0,[r5,8]

00000048 eb000000*   235 	bl	DataSlice_getBoolOffset

0000004c e5850008    236 	str	r0,[r5,8]

                     237 ;35:             DataSlice_getBoolOffset(info->goodInvalidOffset);


                     238 ;36:     info->reservedQuestionableOffset =


                     239 

00000050 e595000c    240 	ldr	r0,[r5,12]

00000054 eb000000*   241 	bl	DataSlice_getBoolOffset

00000058 e585000c    242 	str	r0,[r5,12]

                     243 ;37:             DataSlice_getBoolOffset(info->reservedQuestionableOffset);


                     244 ;38:     info->overflowOffset =


                     245 

0000005c e5950010    246 	ldr	r0,[r5,16]

00000060 eb000000*   247 	bl	DataSlice_getBoolOffset

00000064 e5850010    248 	str	r0,[r5,16]

                     249 ;39:             DataSlice_getBoolOffset(info->overflowOffset);


                     250 ;40:     info->outOfRangeOffset =


                     251 

00000068 e5950014    252 	ldr	r0,[r5,20]

0000006c eb000000*   253 	bl	DataSlice_getBoolOffset

00000070 e5850014    254 	str	r0,[r5,20]

                     255 ;41:             DataSlice_getBoolOffset(info->outOfRangeOffset);


                     256 ;42:     info->badReferenceOffset =


                     257 

00000074 e5950018    258 	ldr	r0,[r5,24]

00000078 eb000000*   259 	bl	DataSlice_getBoolOffset

0000007c e5850018    260 	str	r0,[r5,24]

                     261 ;43:             DataSlice_getBoolOffset(info->badReferenceOffset);


                     262 ;44:     info->oscillatoryOffset =


                     263 

00000080 e595001c    264 	ldr	r0,[r5,28]

00000084 eb000000*   265 	bl	DataSlice_getBoolOffset

00000088 e585001c    266 	str	r0,[r5,28]

                     267 ;45:             DataSlice_getBoolOffset(info->oscillatoryOffset);


                     268 ;46:     info->failureOffset =


                     269 

0000008c e5950020    270 	ldr	r0,[r5,32]

00000090 eb000000*   271 	bl	DataSlice_getBoolOffset

00000094 e5850020    272 	str	r0,[r5,32]

                     273 ;47:             DataSlice_getBoolOffset(info->failureOffset);


                     274 ;48:     info->oldDataOffset =


                     275 

00000098 e5950024    276 	ldr	r0,[r5,36]

0000009c eb000000*   277 	bl	DataSlice_getBoolOffset

000000a0 e5850024    278 	str	r0,[r5,36]

                     279 ;49:             DataSlice_getBoolOffset(info->oldDataOffset);


                     280 ;50:     info->inconsistentOffset =


                     281 

000000a4 e5950028    282 	ldr	r0,[r5,40]

000000a8 eb000000*   283 	bl	DataSlice_getBoolOffset

000000ac e5850028    284 	str	r0,[r5,40]

                     285 ;51:             DataSlice_getBoolOffset(info->inconsistentOffset);


                     286 ;52:     info->inaccurateOffset =


                     287 

000000b0 e595002c    288 	ldr	r0,[r5,44]

000000b4 eb000000*   289 	bl	DataSlice_getBoolOffset

000000b8 e585002c    290 	str	r0,[r5,44]

                     291 ;53:             DataSlice_getBoolOffset(info->inaccurateOffset);


                     292 ;54:     info->processSubstitutedOffset =


                     293 

000000bc e5950030    294 	ldr	r0,[r5,48]


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s
000000c0 eb000000*   295 	bl	DataSlice_getBoolOffset

000000c4 e5850030    296 	str	r0,[r5,48]

                     297 ;55:             DataSlice_getBoolOffset(info->processSubstitutedOffset);


                     298 ;56:     info->testOffset =


                     299 

000000c8 e5950034    300 	ldr	r0,[r5,52]

000000cc eb000000*   301 	bl	DataSlice_getBoolOffset

000000d0 e5850034    302 	str	r0,[r5,52]

                     303 ;57:             DataSlice_getBoolOffset(info->testOffset);


                     304 ;58:     info->operatorBlockedOffset =


                     305 

000000d4 e5950038    306 	ldr	r0,[r5,56]

000000d8 eb000000*   307 	bl	DataSlice_getBoolOffset

000000dc e5850038    308 	str	r0,[r5,56]

                     309 .L257:

000000e0 e1a00005    310 	mov	r0,r5

000000e4 e8bd8070    311 	ldmfd	[sp]!,{r4-r6,pc}

                     312 	.endf	FDAQuality_create

                     313 	.align	4

                     314 ;q	r5	local

                     315 

                     316 ;accessInfo	r4	param

                     317 

                     318 	.section ".bss","awb"

                     319 .L280:

                     320 	.data

                     321 	.text

                     322 

                     323 

                     324 	.align	4

                     325 	.align	4

                     326 FDAQuality_getFixedEncodedSize::

000000e8 e3a00005    327 	mov	r0,5

000000ec e5810000    328 	str	r0,[r1]

000000f0 e3a00001    329 	mov	r0,1

000000f4 e12fff1e*   330 	ret	

                     331 	.endf	FDAQuality_getFixedEncodedSize

                     332 	.align	4

                     333 

                     334 ;q	none	param

                     335 ;size	r1	param

                     336 

                     337 	.section ".bss","awb"

                     338 .L318:

                     339 	.data

                     340 	.text

                     341 

                     342 

                     343 	.align	4

                     344 	.align	4

                     345 FDAQuality_encodeGOOSETemplate::

000000f8 e92d4030    346 	stmfd	[sp]!,{r4-r5,lr}

000000fc e24dd008    347 	sub	sp,sp,8

00000100 e28d2004    348 	add	r2,sp,4

00000104 e1a05000    349 	mov	r5,r0

00000108 e1a04001    350 	mov	r4,r1

0000010c e1a00004    351 	mov	r0,r4

00000110 e3a01005    352 	mov	r1,5

00000114 eb000000*   353 	bl	BufferView_alloc

00000118 e3500000    354 	cmp	r0,0

0000011c 0a00000b    355 	beq	.L331


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s
00000120 e59d3004    356 	ldr	r3,[sp,4]

00000124 e3a02000    357 	mov	r2,0

00000128 e5853044    358 	str	r3,[r5,68]

0000012c e58d2000    359 	str	r2,[sp]

00000130 e3a0100d    360 	mov	r1,13

00000134 e3a00084    361 	mov	r0,132

00000138 eb000000*   362 	bl	BerEncoder_encodeBitStringUshortBuf

0000013c e1a00004    363 	mov	r0,r4

00000140 e3a01005    364 	mov	r1,5

00000144 eb000000*   365 	bl	BufferView_advance

00000148 e3500000    366 	cmp	r0,0

0000014c 13a00001    367 	movne	r0,1

                     368 .L331:

00000150 03a00000    369 	moveq	r0,0

                     370 .L325:

00000154 e28dd008    371 	add	sp,sp,8

00000158 e8bd8030    372 	ldmfd	[sp]!,{r4-r5,pc}

                     373 	.endf	FDAQuality_encodeGOOSETemplate

                     374 	.align	4

                     375 ;pEncodeBuf	[sp,4]	local

                     376 

                     377 ;q	r5	param

                     378 ;outBuf	r4	param

                     379 

                     380 	.section ".bss","awb"

                     381 .L380:

                     382 	.data

                     383 	.text

                     384 

                     385 

                     386 	.align	4

                     387 	.align	4

                     388 FDAQuality_readAndCompare::

0000015c e92d4030    389 	stmfd	[sp]!,{r4-r5,lr}

00000160 e1a05002    390 	mov	r5,r2

00000164 e1a04000    391 	mov	r4,r0

00000168 e1a00001    392 	mov	r0,r1

0000016c e2841004    393 	add	r1,r4,4

00000170 eb000000*   394 	bl	qualityFromBitsFast

00000174 e1d414b0    395 	ldrh	r1,[r4,64]

00000178 e0511000    396 	subs	r1,r1,r0

0000017c 13a01001    397 	movne	r1,1

00000180 e5c51000    398 	strb	r1,[r5]

00000184 e1c404b0    399 	strh	r0,[r4,64]

00000188 e3a00001    400 	mov	r0,1

0000018c e8bd8030    401 	ldmfd	[sp]!,{r4-r5,pc}

                     402 	.endf	FDAQuality_readAndCompare

                     403 	.align	4

                     404 

                     405 ;q	r4	param

                     406 ;dataSliceWnd	r3	param

                     407 ;changed	r5	param

                     408 

                     409 	.section ".bss","awb"

                     410 .L414:

                     411 	.data

                     412 	.text

                     413 

                     414 

                     415 	.align	4

                     416 	.align	4


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s
                     417 FDAQuality_encodeFixedData::

00000190 e92d4000    418 	stmfd	[sp]!,{lr}

00000194 e3a01000    419 	mov	r1,0

00000198 e52d1004    420 	str	r1,[sp,-4]!

0000019c e3a0100d    421 	mov	r1,13

000001a0 e5903044    422 	ldr	r3,[r0,68]

000001a4 e1d024b0    423 	ldrh	r2,[r0,64]

000001a8 e3a00084    424 	mov	r0,132

000001ac eb000000*   425 	bl	BerEncoder_encodeBitStringUshortBuf

000001b0 e28dd004    426 	add	sp,sp,4

000001b4 e8bd8000    427 	ldmfd	[sp]!,{pc}

                     428 	.endf	FDAQuality_encodeFixedData

                     429 	.align	4

                     430 

                     431 ;q	r0	param

                     432 

                     433 	.section ".bss","awb"

                     434 .L446:

                     435 	.data

                     436 	.text

                     437 

                     438 

                     439 ;287: 


                     440 ;288: //===================================================================


                     441 ;289: 


                     442 ;290: void* FDA_create(enum InnerAttributeType attrType, void* accessInfo)


                     443 	.align	4

                     444 	.align	4

                     445 FDA_create::

000001b8 e92d4030    446 	stmfd	[sp]!,{r4-r5,lr}

                     447 ;291: {	


                     448 

                     449 ;292: 	switch (attrType)


                     450 

000001bc e1a05001    451 	mov	r5,r1

000001c0 e2500000    452 	subs	r0,r0,0

000001c4 0a000005    453 	beq	.L457

000001c8 e2500006    454 	subs	r0,r0,6

000001cc 0a000006    455 	beq	.L462

000001d0 e3500003    456 	cmp	r0,3

                     457 ;300: 	default:


                     458 ;301:         ERROR_REPORT("GOOSE: Unsupported DA type");


                     459 ;302: 		return NULL;


                     460 

000001d4 13a00000    461 	movne	r0,0

000001d8 1a000020    462 	bne	.L453

000001dc ea00000d    463 	b	.L470

                     464 .L457:

                     465 ;293: 	{


                     466 ;294: 	case INNER_TYPE_QUALITY:


                     467 ;295: 		return FDAQuality_create(accessInfo);


                     468 

000001e0 e1a00005    469 	mov	r0,r5

000001e4 e8bd4030    470 	ldmfd	[sp]!,{r4-r5,lr}

000001e8 eaffff84*   471 	b	FDAQuality_create

                     472 .L462:

                     473 ;296: 	case INNER_TYPE_BOOLEAN:


                     474 ;297: 		return FDABoolean_create(accessInfo);


                     475 

                     476 ;142: {


                     477 


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s
                     478 ;143: 	FDABoolean da = MM_alloc(sizeof(struct FDABooleanStruct));


                     479 

000001ec e3a00014    480 	mov	r0,20

000001f0 eb000000*   481 	bl	MM_alloc

000001f4 e1b04000    482 	movs	r4,r0

                     483 ;144: 	if (da == NULL)


                     484 

000001f8 0a000004    485 	beq	.L460

                     486 ;145: 	{


                     487 

                     488 ;146: 		return NULL;


                     489 

                     490 ;147: 	}


                     491 ;148: 	FDABoolean_init(da, accessInfo);


                     492 

                     493 ;134: {


                     494 

                     495 ;135: 	daBool->attrType = INNER_TYPE_BOOLEAN;


                     496 

000001fc e3a00006    497 	mov	r0,6

00000200 e8840021    498 	stmea	[r4],{r0,r5}

                     499 ;136:     daBool->accessInfo = accessInfo;


                     500 

                     501 ;137: 


                     502 ;138:     daBool->dataSliceOffset = DataSlice_getBoolOffset(accessInfo->valueOffset);


                     503 

00000204 e5950004    504 	ldr	r0,[r5,4]

00000208 eb000000*   505 	bl	DataSlice_getBoolOffset

0000020c e5840008    506 	str	r0,[r4,8]

                     507 ;149: 	return da;


                     508 

                     509 .L460:

00000210 e1a00004    510 	mov	r0,r4

00000214 ea000011    511 	b	.L453

                     512 .L470:

                     513 ;298: 	case INNER_TYPE_CODEDENUM:


                     514 ;299: 		return FDACodedEnum_create(accessInfo);


                     515 

                     516 ;215: {


                     517 

                     518 ;216: 	FDACodedEnum da;


                     519 ;217: 	if (accessInfo->bitCount != 2)


                     520 

00000218 e5950004    521 	ldr	r0,[r5,4]

0000021c e3500002    522 	cmp	r0,2

00000220 1a000003    523 	bne	.L473

                     524 ;218: 	{


                     525 

                     526 ;219: 		ERROR_REPORT("Only two-bit coded enum supported (like DPS.stVal)");


                     527 ;220: 		return NULL;


                     528 

                     529 ;221: 	}


                     530 ;222: 


                     531 ;223: 	da = MM_alloc(sizeof(struct FDACodedEnumStruct));


                     532 

00000224 e3a00018    533 	mov	r0,24

00000228 eb000000*   534 	bl	MM_alloc

0000022c e1b04000    535 	movs	r4,r0

                     536 ;224: 	if (da == NULL)


                     537 

00000230 1a000001    538 	bne	.L474


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s
                     539 .L473:

                     540 ;225: 	{


                     541 

                     542 ;226: 		return NULL;


                     543 

00000234 e3a00000    544 	mov	r0,0

00000238 ea000008    545 	b	.L453

                     546 .L474:

                     547 ;227: 	}


                     548 ;228: 	FDACodedEnum_init(da, accessInfo);


                     549 

                     550 ;207: {


                     551 

                     552 ;208: 	da->attrType = INNER_TYPE_CODEDENUM;


                     553 

0000023c e3a00009    554 	mov	r0,9

00000240 e8840021    555 	stmea	[r4],{r0,r5}

                     556 ;209: 	da->accessInfo = accessInfo;


                     557 

                     558 ;210:     da->dataSliceOffset0 = DataSlice_getBoolOffset(accessInfo->valueOffsets[0]);


                     559 

00000244 e5950008    560 	ldr	r0,[r5,8]

00000248 eb000000*   561 	bl	DataSlice_getBoolOffset

0000024c e5840008    562 	str	r0,[r4,8]

                     563 ;211:     da->dataSliceOffset1 = DataSlice_getBoolOffset(accessInfo->valueOffsets[1]);


                     564 

00000250 e595000c    565 	ldr	r0,[r5,12]

00000254 eb000000*   566 	bl	DataSlice_getBoolOffset

00000258 e584000c    567 	str	r0,[r4,12]

                     568 ;229: 	return da;


                     569 

0000025c e1a00004    570 	mov	r0,r4

                     571 .L453:

00000260 e8bd8030    572 	ldmfd	[sp]!,{r4-r5,pc}

                     573 	.endf	FDA_create

                     574 	.align	4

                     575 ;da	r4	local

                     576 ;da	r4	local

                     577 

                     578 ;attrType	r0	param

                     579 ;accessInfo	r5	param

                     580 

                     581 	.section ".bss","awb"

                     582 .L597:

                     583 	.data

                     584 	.text

                     585 

                     586 ;303: 	}	


                     587 ;304: }


                     588 

                     589 ;305: 


                     590 ;306: bool FDA_getFixedEncodedSize(void* da, size_t* size)


                     591 	.align	4

                     592 	.align	4

                     593 FDA_getFixedEncodedSize::

                     594 ;307: {


                     595 

                     596 ;308: 	enum InnerAttributeType* attrType = da;


                     597 

00000264 e1a02000    598 	mov	r2,r0

                     599 ;309: 



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s
                     600 ;310: 	//Проверка на всякий случай


                     601 ;311: 	if ((((uint32_t)attrType) & 3) != 0)


                     602 

00000268 e3120003    603 	tst	r2,3

0000026c 1a00000e    604 	bne	.L653

                     605 ;312: 	{


                     606 

                     607 ;313: 		ERROR_REPORT("Alignment error");


                     608 ;314: 		return false;


                     609 

                     610 ;315: 	}


                     611 ;316: 


                     612 ;317: 


                     613 ;318: 	switch (*attrType)


                     614 

00000270 e5922000    615 	ldr	r2,[r2]

00000274 e2522000    616 	subs	r2,r2,0

00000278 0a00000a    617 	beq	.L639

0000027c e2522006    618 	subs	r2,r2,6

                     619 ;322: 	case INNER_TYPE_BOOLEAN:


                     620 ;323: 		return FDABoolean_getFixedEncodedSize(da, size);


                     621 

                     622 ;153: {


                     623 

                     624 ;154: 	*size = 3;


                     625 

00000280 03a00003    626 	moveq	r0,3

00000284 05810000    627 	streq	r0,[r1]

                     628 ;155: 	return TRUE;


                     629 

00000288 03a00001    630 	moveq	r0,1

0000028c 0a000007    631 	beq	.L632

00000290 e3520003    632 	cmp	r2,3

                     633 ;324: 	case INNER_TYPE_CODEDENUM:


                     634 ;325: 		return FDACodedEnum_getFixedEncodedSize(da, size);


                     635 

                     636 ;233: {


                     637 

                     638 ;234: 	*size = CODED_ENUM_ENCODED_SIZE;


                     639 

00000294 03a00004    640 	moveq	r0,4

00000298 05810000    641 	streq	r0,[r1]

                     642 ;235: 	return TRUE;


                     643 

0000029c 03a00001    644 	moveq	r0,1

000002a0 0a000002    645 	beq	.L632

000002a4 ea000000    646 	b	.L653

                     647 .L639:

                     648 ;319: 	{


                     649 ;320: 	case INNER_TYPE_QUALITY:


                     650 ;321: 		return FDAQuality_getFixedEncodedSize(da, size);


                     651 

000002a8 eaffff8e*   652 	b	FDAQuality_getFixedEncodedSize

                     653 .L653:

                     654 ;326: 	default:


                     655 ;327: 		ERROR_REPORT("Unsupported attribute type");


                     656 ;328: 		return FALSE;


                     657 

000002ac e3a00000    658 	mov	r0,0

                     659 .L632:

000002b0 e12fff1e*   660 	ret	


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s
                     661 	.endf	FDA_getFixedEncodedSize

                     662 	.align	4

                     663 ;attrType	r2	local

                     664 

                     665 ;da	r0	param

                     666 ;size	r1	param

                     667 

                     668 	.section ".bss","awb"

                     669 .L720:

                     670 	.data

                     671 	.text

                     672 

                     673 ;329: 	}


                     674 ;330: }


                     675 

                     676 ;331: 


                     677 ;332: bool FDA_encodeGOOSETemplate(void* da, BufferView* templateBuf)


                     678 	.align	4

                     679 	.align	4

                     680 FDA_encodeGOOSETemplate::

000002b4 e92d4030    681 	stmfd	[sp]!,{r4-r5,lr}

000002b8 e24dd008    682 	sub	sp,sp,8

                     683 ;333: {


                     684 

                     685 ;334: 	enum InnerAttributeType* attrType = da;


                     686 

                     687 ;335: 	switch (*attrType)


                     688 

000002bc e5902000    689 	ldr	r2,[r0]

000002c0 e2522000    690 	subs	r2,r2,0

000002c4 0a000005    691 	beq	.L746

000002c8 e2522006    692 	subs	r2,r2,6

000002cc 0a000005    693 	beq	.L749

000002d0 e3520003    694 	cmp	r2,3

                     695 ;343: 	default:


                     696 ;344: 		ERROR_REPORT("Unsupported attribute type");


                     697 ;345: 		return FALSE;


                     698 

000002d4 13a00000    699 	movne	r0,0

000002d8 1a000022    700 	bne	.L742

000002dc ea00000c    701 	b	.L756

                     702 .L746:

                     703 ;336: 	{


                     704 ;337: 	case INNER_TYPE_QUALITY:


                     705 ;338: 		return FDAQuality_encodeGOOSETemplate(da, templateBuf);


                     706 

000002e0 ebffff84*   707 	bl	FDAQuality_encodeGOOSETemplate

000002e4 ea00001f    708 	b	.L742

                     709 .L749:

000002e8 e8910018    710 	ldmfd	[r1],{r3-r4}

000002ec e0842003    711 	add	r2,r4,r3

000002f0 e2822002    712 	add	r2,r2,2

000002f4 e5802010    713 	str	r2,[r0,16]

000002f8 e3a02000    714 	mov	r2,0

000002fc e1a00001    715 	mov	r0,r1

00000300 e3a01083    716 	mov	r1,131

00000304 eb000000*   717 	bl	BufferView_encodeBoolean

00000308 e3500000    718 	cmp	r0,0

0000030c 13a00001    719 	movne	r0,1

                     720 ;339: 	case INNER_TYPE_BOOLEAN:


                     721 ;340: 		return FDABoolean_encodeGOOSETemplate(da, templateBuf);



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s
                     722 

                     723 ;159: {	


                     724 

                     725 ;160: 	//В рабочем режиме будем кодировать только значение, без длины и тэга


                     726 ;161: 	da->fixedEncodeBuf = outBuf->p + outBuf->pos + 2;


                     727 

                     728 ;162: 	


                     729 ;163: 	if (!BufferView_encodeBoolean(outBuf, IEC61850_BER_BOOLEAN, FALSE))


                     730 

                     731 ;164: 	{


                     732 

                     733 ;165: 		return FALSE;


                     734 

                     735 ;166: 	}	


                     736 ;167: 	return TRUE;


                     737 

00000310 ea000014    738 	b	.L742

                     739 .L756:

                     740 ;341: 	case INNER_TYPE_CODEDENUM:


                     741 ;342: 		return FDACodedEnum_encodeGOOSETemplate(da, templateBuf);


                     742 

00000314 e28d2004    743 	add	r2,sp,4

00000318 e1a04000    744 	mov	r4,r0

0000031c e1a05001    745 	mov	r5,r1

                     746 ;239: {	


                     747 

                     748 ;240: 	uint8_t dummy = 0;


                     749 

                     750 ;241: 	uint8_t *pEncodeBuf;


                     751 ;242: 


                     752 ;243: 	if (!BufferView_alloc(outBuf, CODED_ENUM_ENCODED_SIZE, &pEncodeBuf))


                     753 

00000320 e1a00005    754 	mov	r0,r5

00000324 e3a01004    755 	mov	r1,4

00000328 eb000000*   756 	bl	BufferView_alloc

0000032c e3500000    757 	cmp	r0,0

00000330 0a00000b    758 	beq	.L762

                     759 ;244: 	{


                     760 

                     761 ;245: 		ERROR_REPORT("Trouble creating double point value template");


                     762 ;246: 		return FALSE;


                     763 

                     764 ;247: 	}


                     765 ;248: 


                     766 ;249: 	da->fixedEncodeBuf = pEncodeBuf;


                     767 

00000334 e59d3004    768 	ldr	r3,[sp,4]

00000338 e3a02000    769 	mov	r2,0

0000033c e5843014    770 	str	r3,[r4,20]

                     771 ;250: 


                     772 ;251: 	BerEncoder_encodeUcharBitString(ASN_TYPEDESCRIPTION_BIT_STRING,


                     773 

00000340 e58d2000    774 	str	r2,[sp]

00000344 e3a01002    775 	mov	r1,2

00000348 e3a00084    776 	mov	r0,132

0000034c eb000000*   777 	bl	BerEncoder_encodeUcharBitString

                     778 ;252: 		2, dummy, pEncodeBuf, 0);


                     779 ;253: 	if (!BufferView_advance(outBuf, CODED_ENUM_ENCODED_SIZE))


                     780 

00000350 e1a00005    781 	mov	r0,r5

00000354 e3a01004    782 	mov	r1,4


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s
00000358 eb000000*   783 	bl	BufferView_advance

0000035c e3500000    784 	cmp	r0,0

                     785 ;257: 	}


                     786 ;258: 	return TRUE;


                     787 

00000360 13a00001    788 	movne	r0,1

                     789 .L762:

                     790 ;254: 	{


                     791 

                     792 ;255: 		ERROR_REPORT("Trouble creating double point value template");


                     793 ;256: 		return FALSE;


                     794 

00000364 03a00000    795 	moveq	r0,0

                     796 .L742:

00000368 e28dd008    797 	add	sp,sp,8

0000036c e8bd8030    798 	ldmfd	[sp]!,{r4-r5,pc}

                     799 	.endf	FDA_encodeGOOSETemplate

                     800 	.align	4

                     801 ;da	r4	local

                     802 ;outBuf	r5	local

                     803 ;pEncodeBuf	[sp,4]	local

                     804 

                     805 ;da	r0	param

                     806 ;templateBuf	r1	param

                     807 

                     808 	.section ".bss","awb"

                     809 .L864:

                     810 	.data

                     811 	.text

                     812 

                     813 ;346: 	}


                     814 ;347: }


                     815 

                     816 ;348: 


                     817 ;349: bool FDA_readAndCompare(void* da, void *dataSliceWnd,


                     818 	.align	4

                     819 	.align	4

                     820 FDA_readAndCompare::

00000370 e92d40f0    821 	stmfd	[sp]!,{r4-r7,lr}

                     822 ;350:                           bool* changed)


                     823 ;351: {


                     824 

                     825 ;352: 	enum InnerAttributeType* attrType = da;


                     826 

                     827 ;353: 	switch (*attrType)


                     828 

00000374 e5903000    829 	ldr	r3,[r0]

00000378 e2533000    830 	subs	r3,r3,0

0000037c 0a000006    831 	beq	.L896

00000380 e2533006    832 	subs	r3,r3,6

00000384 0a000006    833 	beq	.L898

00000388 e3530003    834 	cmp	r3,3

                     835 ;361: 	default:


                     836 ;362: 		ERROR_REPORT("Unsupported attribute type");


                     837 ;363: 		*changed = FALSE;


                     838 

0000038c 13a00000    839 	movne	r0,0

00000390 15c20000    840 	strneb	r0,[r2]

                     841 ;364: 		return FALSE;


                     842 

00000394 1a000030    843 	bne	.L892


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s
00000398 ea000013    844 	b	.L906

                     845 .L896:

                     846 ;354: 	{


                     847 ;355: 	case INNER_TYPE_QUALITY:


                     848 ;356:         return FDAQuality_readAndCompare(da, dataSliceWnd, changed);


                     849 

0000039c e8bd40f0    850 	ldmfd	[sp]!,{r4-r7,lr}

000003a0 eaffff6d*   851 	b	FDAQuality_readAndCompare

                     852 .L898:

                     853 ;357: 	case INNER_TYPE_BOOLEAN:


                     854 ;358:         return FDABoolean_readAndCompare(da, dataSliceWnd, changed);


                     855 

000003a4 e1a04002    856 	mov	r4,r2

                     857 ;171:                                         void* dataSliceWnd, bool* changed)


                     858 ;172: {


                     859 

000003a8 e1a05000    860 	mov	r5,r0

000003ac e1a00001    861 	mov	r0,r1

000003b0 e5951008    862 	ldr	r1,[r5,8]

000003b4 e3a02000    863 	mov	r2,0

                     864 ;173:     bool newValue = 0;


                     865 

                     866 ;174: 


                     867 ;175:     if(da->dataSliceOffset != -1)


                     868 

000003b8 e3710001    869 	cmn	r1,1

000003bc 0a000003    870 	beq	.L903

                     871 ;176:     {


                     872 

                     873 ;177:         newValue = DataSlice_getBoolFast(dataSliceWnd, da->dataSliceOffset);


                     874 

000003c0 e1a01801    875 	mov	r1,r1 lsl 16

000003c4 e1a01821    876 	mov	r1,r1 lsr 16

000003c8 eb000000*   877 	bl	DataSlice_getBoolFast

000003cc e1a02000    878 	mov	r2,r0

                     879 .L903:

                     880 ;178:     }


                     881 ;179: 


                     882 ;180: 	*changed = (da->value != newValue);


                     883 

000003d0 e5d5000c    884 	ldrb	r0,[r5,12]

000003d4 e0500002    885 	subs	r0,r0,r2

000003d8 13a00001    886 	movne	r0,1

000003dc e5c40000    887 	strb	r0,[r4]

                     888 ;181: 	da->value = newValue;


                     889 

000003e0 e5c5200c    890 	strb	r2,[r5,12]

                     891 ;182: 	return TRUE;	


                     892 

000003e4 e3a00001    893 	mov	r0,1

000003e8 ea00001b    894 	b	.L892

                     895 .L906:

                     896 ;359: 	case INNER_TYPE_CODEDENUM:


                     897 ;360:         return FDACodedEnum_readAndCompare(da, dataSliceWnd, changed);


                     898 

000003ec e1a07002    899 	mov	r7,r2

                     900 ;262:                                           void* dataSliceWnd, bool* changed)


                     901 ;263: {	


                     902 

000003f0 e1a05000    903 	mov	r5,r0

000003f4 e1a06001    904 	mov	r6,r1


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s
000003f8 e5951008    905 	ldr	r1,[r5,8]

000003fc e3a04000    906 	mov	r4,0

                     907 ;264: 	uint8_t newValue = 0;


                     908 

                     909 ;265: 


                     910 ;266:     if(da->dataSliceOffset0 != -1)


                     911 

00000400 e3710001    912 	cmn	r1,1

00000404 0a000004    913 	beq	.L911

                     914 ;267:     {


                     915 

                     916 ;268:         newValue |= DataSlice_getBoolFast(dataSliceWnd, da->dataSliceOffset0);


                     917 

00000408 e1a01801    918 	mov	r1,r1 lsl 16

0000040c e1a01821    919 	mov	r1,r1 lsr 16

00000410 e1a00006    920 	mov	r0,r6

00000414 eb000000*   921 	bl	DataSlice_getBoolFast

00000418 e1a04000    922 	mov	r4,r0

                     923 .L911:

                     924 ;269:     }


                     925 ;270:     newValue <<= 1;


                     926 

0000041c e1a04084    927 	mov	r4,r4 lsl 1

00000420 e595100c    928 	ldr	r1,[r5,12]

00000424 e20440ff    929 	and	r4,r4,255

                     930 ;271:     if(da->dataSliceOffset1 != -1)


                     931 

00000428 e3710001    932 	cmn	r1,1

0000042c 0a000004    933 	beq	.L913

                     934 ;272:     {


                     935 

                     936 ;273:         newValue |= DataSlice_getBoolFast(dataSliceWnd, da->dataSliceOffset1);


                     937 

00000430 e1a01801    938 	mov	r1,r1 lsl 16

00000434 e1a01821    939 	mov	r1,r1 lsr 16

00000438 e1a00006    940 	mov	r0,r6

0000043c eb000000*   941 	bl	DataSlice_getBoolFast

00000440 e1844000    942 	orr	r4,r4,r0

                     943 .L913:

                     944 ;274:     }


                     945 ;275: 					


                     946 ;276: 	*changed = (da->value != newValue);


                     947 

00000444 e5d50010    948 	ldrb	r0,[r5,16]

00000448 e0500004    949 	subs	r0,r0,r4

0000044c 13a00001    950 	movne	r0,1

00000450 e5c70000    951 	strb	r0,[r7]

                     952 ;277: 	da->value = newValue;


                     953 

00000454 e5c54010    954 	strb	r4,[r5,16]

                     955 ;278: 	return true;	


                     956 

00000458 e3a00001    957 	mov	r0,1

                     958 .L892:

0000045c e8bd80f0    959 	ldmfd	[sp]!,{r4-r7,pc}

                     960 	.endf	FDA_readAndCompare

                     961 	.align	4

                     962 ;da	r5	local

                     963 ;dataSliceWnd	r0	local

                     964 ;changed	r4	local

                     965 ;newValue	r2	local


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s
                     966 ;da	r5	local

                     967 ;dataSliceWnd	r6	local

                     968 ;changed	r7	local

                     969 ;newValue	r4	local

                     970 

                     971 ;da	r0	param

                     972 ;dataSliceWnd	r1	param

                     973 ;changed	r2	param

                     974 

                     975 	.section ".bss","awb"

                     976 .L1014:

                     977 	.data

                     978 	.text

                     979 

                     980 ;365: 	}


                     981 ;366: }


                     982 

                     983 ;367: 


                     984 ;368: void FDA_encodeFixedData(void* da)


                     985 	.align	4

                     986 	.align	4

                     987 FDA_encodeFixedData::

00000460 e92d4000    988 	stmfd	[sp]!,{lr}

00000464 e24dd004    989 	sub	sp,sp,4

                     990 ;369: {


                     991 

                     992 ;370: 	enum InnerAttributeType* attrType = da;


                     993 

                     994 ;371: 	switch (*attrType)


                     995 

00000468 e5901000    996 	ldr	r1,[r0]

0000046c e2511000    997 	subs	r1,r1,0

00000470 0a000007    998 	beq	.L1056

00000474 e2511006    999 	subs	r1,r1,6

                    1000 ;376: 	case INNER_TYPE_BOOLEAN:


                    1001 ;377: 		FDABoolean_encodeFixedData(da);


                    1002 

                    1003 ;186: {


                    1004 

                    1005 ;187: 	*da->fixedEncodeBuf = da->value;


                    1006 

00000478 05901010   1007 	ldreq	r1,[r0,16]

0000047c 05d0000c   1008 	ldreqb	r0,[r0,12]

00000480 05c10000   1009 	streqb	r0,[r1]

00000484 0a00000b   1010 	beq	.L1052

00000488 e3510003   1011 	cmp	r1,3

0000048c 0a000002   1012 	beq	.L1067

00000490 ea000008   1013 	b	.L1052

                    1014 .L1056:

                    1015 ;372: 	{


                    1016 ;373: 	case INNER_TYPE_QUALITY:


                    1017 ;374:         FDAQuality_encodeFixedData(da);


                    1018 

00000494 ebffff3d*  1019 	bl	FDAQuality_encodeFixedData

                    1020 ;375:         return;


                    1021 

00000498 ea000006   1022 	b	.L1052

                    1023 .L1067:

                    1024 ;378: 		return;


                    1025 

                    1026 ;379: 	case INNER_TYPE_CODEDENUM:



                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_8ok1.s
                    1027 ;380: 		FDACodedEnum_encodeFixedData(da);


                    1028 

                    1029 ;283: {


                    1030 

                    1031 ;284: 	BerEncoder_encodeUcharBitString(ASN_TYPEDESCRIPTION_BIT_STRING,


                    1032 

0000049c e3a01000   1033 	mov	r1,0

000004a0 e58d1000   1034 	str	r1,[sp]

000004a4 e3a01002   1035 	mov	r1,2

000004a8 e5903014   1036 	ldr	r3,[r0,20]

000004ac e5d02010   1037 	ldrb	r2,[r0,16]

000004b0 e3a00084   1038 	mov	r0,132

000004b4 eb000000*  1039 	bl	BerEncoder_encodeUcharBitString

                    1040 .L1052:

                    1041 ;381: 		return;


                    1042 

                    1043 ;382: 	default:


                    1044 ;383: 		ERROR_REPORT("Unsupported attribute type");	


                    1045 ;384:         return;


                    1046 

000004b8 e28dd004   1047 	add	sp,sp,4

000004bc e8bd8000   1048 	ldmfd	[sp]!,{pc}

                    1049 	.endf	FDA_encodeFixedData

                    1050 	.align	4

                    1051 

                    1052 ;da	r0	param

                    1053 

                    1054 	.section ".bss","awb"

                    1055 .L1106:

                    1056 	.data

                    1057 	.text

                    1058 

                    1059 ;385: 	}


                    1060 ;386: }


                    1061 	.align	4

                    1062 

                    1063 	.data

                    1064 	.ghsnote version,6

                    1065 	.ghsnote tools,3

                    1066 	.ghsnote options,0

                    1067 	.text

                    1068 	.align	4

