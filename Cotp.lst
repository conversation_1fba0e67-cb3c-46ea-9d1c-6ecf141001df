                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=Cotp.c -o gh_2io1.o -list=Cotp.lst C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s
Source File: Cotp.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile Cotp.c -o

                      11 ;		Cotp.o

                      12 ;Source File:   Cotp.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:01 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "Cotp.h"


                      23 ;2: 


                      24 ;3: #include <debug.h>


                      25 ;4: #include "platform_socket.h"


                      26 ;5: #include <stddef.h>


                      27 ;6: #include <string.h>


                      28 ;7: 


                      29 ;8: #define TPDU_TYPE_CONNECT_REQUEST 0xe0


                      30 ;9: #define TPDU_TYPE_CONNECT_RESPONSE 0xd0


                      31 ;10: #define TPDU_TYPE_DATA 0xf0


                      32 ;11: 


                      33 ;12: #define CONNECTION_RESPONSE_HEADER_LENGTH 7


                      34 ;13: #define DATA_HEADER_LENGTH 3


                      35 ;14: 


                      36 ;15: #define OPT_TPDU_SIZE 0xC0


                      37 ;16: 


                      38 ;17: // Читает заголовок TPKT, LI, и DT в буфер заголовка


                      39 ;18: // Возвращает полный размер TPDU или -1 при ошибке


                      40 ;19: static int readHeader(COTPConnection * conn)


                      41 

                      42 ;44: }


                      43 

                      44 ;45: 


                      45 ;46: static int parseCOTPoptions(COTPConnection* cotpConn, unsigned char* options,


                      46 

                      47 ;79: }


                      48 

                      49 ;80: 


                      50 ;81: static int getCOTPoptionsLength()



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s
                      51 

                      52 ;85: }


                      53 

                      54 ;86: 


                      55 ;87: void writeTPKTHeader(int messageLen, unsigned char* buf)


                      56 ;88: {


                      57 ;89:     TPKTHeader* header = (TPKTHeader*)buf;


                      58 ;90:     header->version = 3;


                      59 ;91:     header->reserved = 0;


                      60 ;92:     header->packetLenH = messageLen >> 8;


                      61 ;93:     header->packetLenL = messageLen & 0xFF;


                      62 ;94: }


                      63 ;95: 


                      64 ;96: static void writeCOTPConnectResponseHeader(COTPConnection* cotpConn,int optionsLength,


                      65 

                      66 ;109: }


                      67 

                      68 ;110: 


                      69 ;111: static void writeCOTPoptions(unsigned char* buf)


                      70 

                      71 ;116: }


                      72 

                      73 ;117: 


                      74 ;118: static int sendCOTPConnectionResponse(COTPConnection* cotpConn, unsigned char* buf)


                      75 

                      76 ;134: }


                      77 

                      78 ;135: 


                      79 ;136: static int processCOTPconnectRequest(COTPConnection* cotpConn, int tpduSize)


                      80 

                      81 ;183: }


                      82 

                      83 ;184: 


                      84 ;185: // Получает остаток пакета DATA TPDU и складывает в текущую позицию буфера


                      85 ;186: // Возвращает


                      86 ;187: // 0 если не последний пакет,


                      87 ;188: // 1 если последний


                      88 ;189: // -1 при ошибке


                      89 ;190: static int processCOTPdataTPDU(COTPConnection* cotpConn,


                      90 

                      91 ;231:      }


                      92 ;232: }


                      93 

                      94 ;233: 


                      95 ;234: //Формирует TPKT пакет с данными и посылает.


                      96 ;235: //Возвращает, сколько фактически послано данных включая заголовок


                      97 ;236: //или -1


                      98 ;237: static int cotpSendDataTPDU(COTPConnection* cotpConn, void* sendData, int sendByteCount,


                      99 

                     100 ;257: }


                     101 

                     102 	.text

                     103 	.align	4

                     104 writeTPKTHeader::

00000000 e3a02003    105 	mov	r2,3

00000004 e5c12000    106 	strb	r2,[r1]

00000008 e3a02000    107 	mov	r2,0

0000000c e5c12001    108 	strb	r2,[r1,1]

00000010 e1a02440    109 	mov	r2,r0 asr 8

00000014 e5c12002    110 	strb	r2,[r1,2]

00000018 e5c10003    111 	strb	r0,[r1,3]


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s
0000001c e12fff1e*   112 	ret	

                     113 	.endf	writeTPKTHeader

                     114 	.align	4

                     115 

                     116 ;messageLen	r0	param

                     117 ;buf	r1	param

                     118 

                     119 	.section ".bss","awb"

                     120 .L286:

                     121 	.data

                     122 	.text

                     123 

                     124 

                     125 ;258: 


                     126 ;259: 


                     127 ;260: //Посылает данные по COTP, разбивая на фрагменты по необходимости.


                     128 ;261: //Возвращает byteCount


                     129 ;262: //или -1 при ошибке


                     130 ;263: int cotpSendData(COTPConnection* cotpConn, void* data, int byteCount)


                     131 	.align	4

                     132 	.align	4

                     133 cotpSendData::

00000020 e92d4cfc    134 	stmfd	[sp]!,{r2-r7,r10-fp,lr}

                     135 ;264: {


                     136 

                     137 ;265:     unsigned char* buf = data;


                     138 

00000024 e1a0a000    139 	mov	r10,r0

00000028 e1a0b001    140 	mov	fp,r1

                     141 ;266:     int bytesRemain = byteCount;


                     142 

0000002c e59d6000    143 	ldr	r6,[sp]

                     144 ;267:     int eot;


                     145 ;268:     do


                     146 

                     147 .L297:

                     148 ;269:     {


                     149 

00000030 e3a07000    150 	mov	r7,0

                     151 ;273:         if(bytesToSend >= bytesRemain)


                     152 

00000034 e3a04fc0    153 	mov	r4,3<<8

00000038 e28440f9    154 	add	r4,r4,249

                     155 ;270:         int bytesToSend = MAX_TPKT_TPDU_SIZE - sizeof(TPKTHeader)


                     156 

                     157 ;271:                 - DATA_HEADER_LENGTH;


                     158 ;272:         eot = 0;


                     159 

0000003c e1560004    160 	cmp	r6,r4

                     161 ;274:         {


                     162 

                     163 ;275:             bytesToSend = bytesRemain;


                     164 

00000040 d3a07001    165 	movle	r7,1

                     166 ;277:         }


                     167 ;278:         if( cotpSendDataTPDU(cotpConn, buf, bytesToSend, eot) == -1 )


                     168 

                     169 ;238:                             int eot)


                     170 ;239: {


                     171 

                     172 ;240:     //RFC905 13.7.1    



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s
                     173 ;241:     unsigned char* buf = cotpConn->outBuf;


                     174 

00000044 d1a04006    175 	movle	r4,r6

                     176 ;276:             eot = 1;


                     177 

00000048 e2840007    178 	add	r0,r4,7

0000004c e58d0004    179 	str	r0,[sp,4]

                     180 ;244: 	


                     181 ;245:     writeTPKTHeader(messageLen, buf);


                     182 

00000050 e28a5f45    183 	add	r5,r10,0x0114

                     184 ;242:     int bufPos = 0;


                     185 

                     186 ;243:     int messageLen = sizeof(TPKTHeader) + DATA_HEADER_LENGTH + sendByteCount;


                     187 

00000054 e1a01005    188 	mov	r1,r5

00000058 ebffffe8*   189 	bl	writeTPKTHeader

                     190 ;246:     bufPos += sizeof(TPKTHeader);


                     191 

                     192 ;247:     //Write COTP data header


                     193 ;248:     //li


                     194 ;249:     buf[bufPos++] = 2;// DT + EOT


                     195 

0000005c e1a02004    196 	mov	r2,r4

00000060 e3a01002    197 	mov	r1,2

00000064 e5ca1118    198 	strb	r1,[r10,280]

                     199 ;250:     //dt


                     200 ;251:     buf[bufPos++] =COTP_DATA_TRANSFER;


                     201 

00000068 e3a010f0    202 	mov	r1,240

0000006c e5ca1119    203 	strb	r1,[r10,281]

                     204 ;252:     //EOT


                     205 ;253:     buf[bufPos++] = eot ? 0x80: 0;


                     206 

00000070 e3a01000    207 	mov	r1,0

00000074 e3570000    208 	cmp	r7,0

00000078 13a01080    209 	movne	r1,128

0000007c e5ca111a    210 	strb	r1,[r10,282]

                     211 ;254:     //Write user data


                     212 ;255:     memcpy(buf + bufPos, sendData, sendByteCount);    


                     213 

00000080 e1a0100b    214 	mov	r1,fp

00000084 e2850007    215 	add	r0,r5,7

00000088 eb000000*   216 	bl	memcpy

                     217 ;256:     return writeSocket(cotpConn->socket, buf, messageLen);


                     218 

0000008c e59d2004    219 	ldr	r2,[sp,4]

00000090 e59a0000    220 	ldr	r0,[r10]

00000094 e1a01005    221 	mov	r1,r5

00000098 eb000000*   222 	bl	writeSocket

0000009c e3700001    223 	cmn	r0,1

000000a0 1a000003    224 	bne	.L300

                     225 ;279:         {


                     226 

                     227 ;280:             debugSendText("Error sending TPDU");


                     228 

000000a4 e28f0000*   229 	adr	r0,.L413

000000a8 eb000000*   230 	bl	debugSendText

                     231 ;281:             return -1;


                     232 

000000ac e3e00000    233 	mvn	r0,0


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s
000000b0 ea000004    234 	b	.L293

                     235 .L300:

                     236 ;282:         }        


                     237 ;283:         bytesRemain -= bytesToSend;


                     238 

000000b4 e0466004    239 	sub	r6,r6,r4

                     240 ;284:         buf += bytesToSend;


                     241 

000000b8 e08bb004    242 	add	fp,fp,r4

000000bc e3570000    243 	cmp	r7,0

000000c0 0affffda    244 	beq	.L297

                     245 ;285:     }


                     246 ;286:     while(!eot);


                     247 ;287:     return byteCount;


                     248 

000000c4 e59d0000    249 	ldr	r0,[sp]

                     250 .L293:

000000c8 e8bd8cfc    251 	ldmfd	[sp]!,{r2-r7,r10-fp,pc}

                     252 	.endf	cotpSendData

                     253 	.align	4

                     254 ;buf	fp	local

                     255 ;bytesRemain	r6	local

                     256 ;eot	r7	local

                     257 ;bytesToSend	r4	local

                     258 ;buf	r5	local

                     259 ;bufPos	r0	local

                     260 ;messageLen	[sp,4]	local

                     261 ;.L384	.L387	static

                     262 

                     263 ;cotpConn	r10	param

                     264 ;data	r1	param

                     265 ;byteCount	[sp]	param

                     266 

                     267 	.section ".bss","awb"

                     268 .L383:

                     269 	.data

                     270 	.text

                     271 

                     272 ;288: }


                     273 

                     274 ;289: 


                     275 ;290: int cotpReceiveData(COTPConnection* cotpConn, void* recvBuf, int maxByteCount )


                     276 	.align	4

                     277 	.align	4

                     278 	.align	4

                     279 cotpReceiveData::

000000cc e92d4cf2    280 	stmfd	[sp]!,{r1,r4-r7,r10-fp,lr}

                     281 ;291: {


                     282 

                     283 ;292:     int lastDataPacket;


                     284 ;293:     cotpConn->inBufPos = 0;


                     285 

000000d0 e24dd004    286 	sub	sp,sp,4

000000d4 e58d1004    287 	str	r1,[sp,4]

000000d8 e1a0b002    288 	mov	fp,r2

000000dc e1a05000    289 	mov	r5,r0

000000e0 e3a00000    290 	mov	r0,0

000000e4 e585000c    291 	str	r0,[r5,12]

                     292 ;294:     while(1)


                     293 

                     294 .L421:


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s
                     295 ;295:     {


                     296 

                     297 ;296:         //Читаем заголовок пакета (TPKT + кусочек COTP)


                     298 ;297:         int tpduSize = readHeader(cotpConn);


                     299 

                     300 ;20: {


                     301 

                     302 ;21:     TPKTHeader* pTPKTHeader = &conn->resvTPKTHeader.tpktHeader;


                     303 

000000e8 e2854010    304 	add	r4,r5,16

                     305 ;22:     int recvResult;


                     306 ;23: 	int cotpLength;


                     307 ;24: 


                     308 ;25:     //Получаем заголовок TPKT, LI, DT


                     309 ;26:     recvResult = readSocket(conn->socket, &conn->resvTPKTHeader, sizeof(PacketHeader));


                     310 

000000ec e1a01004    311 	mov	r1,r4

000000f0 e5950000    312 	ldr	r0,[r5]

000000f4 e3a02006    313 	mov	r2,6

000000f8 eb000000*   314 	bl	readSocket

                     315 ;27:     if(!recvResult)


                     316 

000000fc e3500000    317 	cmp	r0,0

00000100 0a000004    318 	beq	.L425

                     319 ;28:     {


                     320 

                     321 ;29:         //Ошибка чтения


                     322 ;30:         ERROR_REPORT("Read TPKT header Error");


                     323 ;31:         return -1;


                     324 

                     325 ;32:     }


                     326 ;33: 


                     327 ;34:     //проверяем заголовок TPKT


                     328 ;35:     if(pTPKTHeader->version != 3 || pTPKTHeader->reserved != 0 )


                     329 

00000104 e5d50010    330 	ldrb	r0,[r5,16]

00000108 e3500003    331 	cmp	r0,3

0000010c 05d40001    332 	ldreqb	r0,[r4,1]

00000110 03500000    333 	cmpeq	r0,0

00000114 0a000002    334 	beq	.L426

                     335 .L425:

                     336 ;36:     {


                     337 

                     338 ;37:         //Неверный заголовок TPKT


                     339 ;38: 		ERROR_REPORT("Invalid TPKT header");


                     340 ;39:         return -1;


                     341 

00000118 e3e04000    342 	mvn	r4,0

                     343 ;298:         if(tpduSize == -1)


                     344 

0000011c e3740001    345 	cmn	r4,1

00000120 0a000080    346 	beq	.L478

                     347 .L426:

                     348 ;40:     }


                     349 ;41: 


                     350 ;42: 	cotpLength = pTPKTHeader->packetLenH * 256 + pTPKTHeader->packetLenL;		


                     351 

00000124 05d42003    352 	ldreqb	r2,[r4,3]

00000128 05d40002    353 	ldreqb	r0,[r4,2]

0000012c 00824400    354 	addeq	r4,r2,r0 lsl 8

                     355 ;43:     return cotpLength;



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s
                     356 

                     357 ;298:         if(tpduSize == -1)


                     358 

00000130 03740001    359 	cmneq	r4,1

00000134 0a00007b    360 	beq	.L478

                     361 .L428:

                     362 ;299:         {


                     363 

                     364 ;300:             return -1;


                     365 

                     366 ;301:         }


                     367 ;302: 


                     368 ;303:         switch (cotpConn->resvTPKTHeader.dt) {


                     369 

00000138 e5d51015    370 	ldrb	r1,[r5,21]

0000013c e25100e0    371 	subs	r0,r1,224

00000140 0a000002    372 	beq	.L433

00000144 e3500010    373 	cmp	r0,16

00000148 0a000057    374 	beq	.L462

0000014c ea00007b    375 	b	.L484

                     376 .L433:

                     377 ;304:         case TPDU_TYPE_CONNECT_REQUEST:


                     378 ;305:             debugSendText("Received COTP connect request");


                     379 

00000150 e28f0000*   380 	adr	r0,.L996

00000154 eb000000*   381 	bl	debugSendText

                     382 ;306:             if(processCOTPconnectRequest(cotpConn, tpduSize) < 0)


                     383 

                     384 ;137: {


                     385 

                     386 ;138:     //RFC905 13.3.1


                     387 ;139:    ConnectRequest* pConnectRequest = &cotpConn->recvConnectRequest;


                     388 

                     389 ;140: 


                     390 ;141:     //Размер пакета без заголовка TPKT, LI и DT


                     391 ;142:     int remainPacketSize = tpduSize - sizeof (PacketHeader);


                     392 

00000158 e2442006    393 	sub	r2,r4,6

                     394 ;143: 


                     395 ;144:     unsigned char li = cotpConn->resvTPKTHeader.li;


                     396 

                     397 ;145: 


                     398 ;146:     if(remainPacketSize > sizeof(ConnectRequest))


                     399 

0000015c e35200fe    400 	cmp	r2,254

00000160 9a000003    401 	bls	.L440

                     402 ;147:     {


                     403 

                     404 ;148:         //Пакет не лезет в буфер


                     405 ;149:         debugSendText("TPDU does not fit in the buffer");


                     406 

00000164 e59f021c*   407 	ldr	r0,.L997

                     408 ;150:         return -1;


                     409 

00000168 eb000000*   410 	bl	debugSendText

                     411 ;315:             {


                     412 

                     413 ;316:                 return -1;


                     414 

0000016c e3e00000    415 	mvn	r0,0

00000170 ea000075    416 	b	.L414


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s
                     417 .L440:

00000174 e5d56014    418 	ldrb	r6,[r5,20]

                     419 ;151:     }


                     420 ;152: 


                     421 ;153:     if(li < 6)


                     422 

00000178 e2857016    423 	add	r7,r5,22

0000017c e3560006    424 	cmp	r6,6

00000180 3a000068    425 	blo	.L478

                     426 ;154:     {


                     427 

                     428 ;155:         return -1;


                     429 

                     430 ;156:     }


                     431 ;157: 


                     432 ;158:     //Читаем оставшуюся часть пакета


                     433 ;159:     if(readSocket(cotpConn->socket, pConnectRequest, remainPacketSize ) == -1)


                     434 

00000184 e5950000    435 	ldr	r0,[r5]

00000188 e1a01007    436 	mov	r1,r7

0000018c eb000000*   437 	bl	readSocket

00000190 e3700001    438 	cmn	r0,1

00000194 0a000063    439 	beq	.L478

                     440 ;160:     {


                     441 

                     442 ;161:         //Ошибка чтения


                     443 ;162:         return -1;


                     444 

                     445 ;163:     }


                     446 ;164:     cotpConn->remoteRef = (pConnectRequest->srcRefH << 8) + pConnectRequest->srcRefL;


                     447 

00000198 e5d72002    448 	ldrb	r2,[r7,2]

0000019c e5d70003    449 	ldrb	r0,[r7,3]

000001a0 e3a04000    450 	mov	r4,0

                     451 ;49:     int optionPos = 0;    


                     452 

                     453 ;50:     while(optionPos < allOptionsLen)


                     454 

000001a4 e0820400    455 	add	r0,r2,r0 lsl 8

000001a8 e1c500b4    456 	strh	r0,[r5,4]

                     457 ;165:     //debugSendUshort("\tremoteRef:", cotpConn->remoteRef);


                     458 ;166:     cotpConn->protocolClass = pConnectRequest->protocolClass;


                     459 

000001ac e5d70004    460 	ldrb	r0,[r7,4]

000001b0 e246a006    461 	sub	r10,r6,6

000001b4 e5c50008    462 	strb	r0,[r5,8]

                     463 ;167:     //debugSendUshort("\tprotocolClass:", cotpConn->protocolClass);


                     464 ;168: 


                     465 ;169:     //  Внимание! LI не входит в длину


                     466 ;170:     if(parseCOTPoptions(cotpConn, pConnectRequest->variablePart, li - 6) < 0)


                     467 

                     468 ;47:                             int allOptionsLen)


                     469 ;48: {


                     470 

000001b8 e154000a    471 	cmp	r4,r10

000001bc aa000016    472 	bge	.L455

                     473 .L445:

                     474 ;51:     {                


                     475 

                     476 ;52:         int optionType = options[optionPos++];


                     477 


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s
000001c0 e0840007    478 	add	r0,r4,r7

000001c4 e2844001    479 	add	r4,r4,1

000001c8 e5d01005    480 	ldrb	r1,[r0,5]

                     481 ;53:         int optionLen = options[optionPos++];        


                     482 

000001cc e0840007    483 	add	r0,r4,r7

000001d0 e5d06005    484 	ldrb	r6,[r0,5]

                     485 ;54:         if(optionPos + optionLen > allOptionsLen)


                     486 

000001d4 e2844001    487 	add	r4,r4,1

000001d8 e0860004    488 	add	r0,r6,r4

000001dc e150000a    489 	cmp	r0,r10

000001e0 da000003    490 	ble	.L447

                     491 ;55:         {


                     492 

                     493 ;56:             //option length error


                     494 ;57:             debugSendText("option length error");


                     495 

000001e4 e59f01a0*   496 	ldr	r0,.L998

                     497 ;58:             return -1;


                     498 

000001e8 eb000000*   499 	bl	debugSendText

                     500 ;315:             {


                     501 

                     502 ;316:                 return -1;


                     503 

000001ec e3e00000    504 	mvn	r0,0

000001f0 ea000055    505 	b	.L414

                     506 .L447:

                     507 ;59:         }


                     508 ;60:         switch(optionType)


                     509 

000001f4 e35100c0    510 	cmp	r1,192

000001f8 1a000004    511 	bne	.L450

                     512 ;61:         {


                     513 ;62:         case OPT_TPDU_SIZE:


                     514 ;63:             if (optionLen == 1) {


                     515 

000001fc e3560001    516 	cmp	r6,1

                     517 ;64:                 //TODO Use requestedTpduSize


                     518 ;65:                 //int requestedTpduSize = (1 << options[optionPos]);


                     519 ;66:                 //debugSendUshort("\tmax TPDU size:", requestedTpduSize);


                     520 ;67:             }


                     521 ;68:             else


                     522 ;69:             {


                     523 

                     524 ;70:                debugSendUshort("\tInvalid option size", optionType);


                     525 

00000200 11a01801    526 	movne	r1,r1 lsl 16

00000204 159f0184*   527 	ldrne	r0,.L999

00000208 11a01821    528 	movne	r1,r1 lsr 16

0000020c 1b000000*   529 	blne	debugSendUshort

                     530 .L450:

                     531 ;71:             }


                     532 ;72:             break;        


                     533 ;73:         //default:


                     534 ;74:             //debugSendUshort("\tUnknown option:", optionType);


                     535 ;75:         }


                     536 ;76:         optionPos += optionLen;


                     537 

00000210 e0844006    538 	add	r4,r4,r6


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s
00000214 e154000a    539 	cmp	r4,r10

00000218 baffffe8    540 	blt	.L445

                     541 .L455:

                     542 ;77:     }


                     543 ;78:     return 1;


                     544 

                     545 ;171:     {


                     546 

                     547 ;172:         return -1;


                     548 

                     549 ;173:     }


                     550 ;174: 


                     551 ;175:     if(sendCOTPConnectionResponse(cotpConn, cotpConn->outBuf) == -1)


                     552 

                     553 ;119: {


                     554 

                     555 ;120:     int bufPos = 0;


                     556 

                     557 ;121:     int optionsLength = getCOTPoptionsLength();    


                     558 

                     559 ;82: {


                     560 

                     561 ;83:     //TPDU size option;


                     562 ;84:     return 3;


                     563 

                     564 ;122:     //4 + 7 + 3


                     565 ;123:     int messageLen = sizeof(TPKTHeader) + CONNECTION_RESPONSE_HEADER_LENGTH


                     566 

                     567 ;124:             + optionsLength;


                     568 ;125: 


                     569 ;126:     writeTPKTHeader(messageLen, buf);


                     570 

0000021c e2851f45    571 	add	r1,r5,0x0114

00000220 e3a0000e    572 	mov	r0,14

00000224 ebffff75*   573 	bl	writeTPKTHeader

                     574 ;127:     bufPos += sizeof( TPKTHeader);


                     575 

                     576 ;128:     writeCOTPConnectResponseHeader(cotpConn, optionsLength, buf + bufPos );


                     577 

                     578 ;97:                                            unsigned char* buf)


                     579 ;98: {


                     580 

                     581 ;99: 


                     582 ;100:     int headerLengthValue =


                     583 

                     584 ;101:             CONNECTION_RESPONSE_HEADER_LENGTH -1;//Длина не входит


                     585 ;102:     buf[0] =  headerLengthValue  + optionsLength;


                     586 

00000228 e3a00009    587 	mov	r0,9

0000022c e5c50118    588 	strb	r0,[r5,280]

                     589 ;103:     buf[1] = TPDU_TYPE_CONNECT_RESPONSE;


                     590 

00000230 e3a000d0    591 	mov	r0,208

00000234 e5c50119    592 	strb	r0,[r5,281]

                     593 ;104:     buf[2] = cotpConn->remoteRef >> 8;


                     594 

00000238 e1d500b4    595 	ldrh	r0,[r5,4]

0000023c e2851f45    596 	add	r1,r5,0x0114

                     597 ;112: {


                     598 

                     599 ;113:         buf[0] = OPT_TPDU_SIZE;



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s
                     600 

00000240 e1a02440    601 	mov	r2,r0 asr 8

00000244 e5c5011b    602 	strb	r0,[r5,283]

                     603 ;106:     buf[4] = cotpConn->localRef >> 8;


                     604 

00000248 e1d500b6    605 	ldrh	r0,[r5,6]

0000024c e5c5211a    606 	strb	r2,[r5,282]

                     607 ;105:     buf[3] = cotpConn->remoteRef & 0xff;


                     608 

00000250 e1a02440    609 	mov	r2,r0 asr 8

00000254 e5c5011d    610 	strb	r0,[r5,285]

                     611 ;108:     buf[6] = cotpConn->protocolClass;


                     612 

00000258 e5d50008    613 	ldrb	r0,[r5,8]

0000025c e5c5211c    614 	strb	r2,[r5,284]

                     615 ;107:     buf[5] = cotpConn->localRef & 0xff;


                     616 

00000260 e5c5011e    617 	strb	r0,[r5,286]

                     618 ;129:     bufPos += CONNECTION_RESPONSE_HEADER_LENGTH;


                     619 

                     620 ;130: 


                     621 ;131:     writeCOTPoptions(buf + bufPos );


                     622 

00000264 e3a000c0    623 	mov	r0,192

00000268 e5c5011f    624 	strb	r0,[r5,287]

                     625 ;114:         buf[1] = 1;// Размер опции


                     626 

0000026c e3a00001    627 	mov	r0,1

00000270 e5c50120    628 	strb	r0,[r5,288]

                     629 ;115:         buf[2] = 10;//Максимальный размер TPDU: степень 2


                     630 

00000274 e3a0000a    631 	mov	r0,10

00000278 e5c50121    632 	strb	r0,[r5,289]

                     633 ;132: 


                     634 ;133:     return writeSocket(cotpConn->socket, buf, messageLen);


                     635 

0000027c e5950000    636 	ldr	r0,[r5]

00000280 e3a0200e    637 	mov	r2,14

00000284 eb000000*   638 	bl	writeSocket

00000288 e3700001    639 	cmn	r0,1

0000028c 1a000003    640 	bne	.L457

                     641 ;176:     {


                     642 

                     643 ;177:         debugSendText("sendCOTPConnectionResponse error");


                     644 

00000290 e59f00fc*   645 	ldr	r0,.L1000

                     646 ;178:         return -1;


                     647 

00000294 eb000000*   648 	bl	debugSendText

                     649 ;315:             {


                     650 

                     651 ;316:                 return -1;


                     652 

00000298 e3e00000    653 	mvn	r0,0

0000029c ea00002a    654 	b	.L414

                     655 .L457:

                     656 ;179:     }


                     657 ;180: 


                     658 ;181:     debugSendText("Connection response is sent");


                     659 

000002a0 e59f00f0*   660 	ldr	r0,.L1001


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s
000002a4 eb000000*   661 	bl	debugSendText

                     662 ;182:     return 1;


                     663 

000002a8 eaffff8e    664 	b	.L421

                     665 .L462:

                     666 ;307:             {


                     667 

                     668 ;308:                 return -1;


                     669 

                     670 ;309:             }


                     671 ;310:             continue;


                     672 

                     673 ;311:         case TPDU_TYPE_DATA:			            


                     674 ;312:             lastDataPacket = processCOTPdataTPDU(cotpConn, recvBuf, maxByteCount,


                     675 

000002ac e1a06005    676 	mov	r6,r5

000002b0 e596000c    677 	ldr	r0,[r6,12]

000002b4 e2444007    678 	sub	r4,r4,7

                     679 ;196: 


                     680 ;197:     if(cotpConn->inBufPos +  dataSize > inBufferSize)


                     681 

000002b8 e0840000    682 	add	r0,r4,r0

000002bc e150000b    683 	cmp	r0,fp

                     684 ;198:     {


                     685 

                     686 ;199:         //Не лезет в буфер


                     687 ;200:         return -1;


                     688 

                     689 ;201:     }


                     690 ;202: 


                     691 ;203:     //RFC905 13.7.1


                     692 ;204:     if (cotpConn->resvTPKTHeader.li != 2)// DT + TPDU-NR


                     693 

000002c0 d5d60014    694 	ldrleb	r0,[r6,20]

000002c4 e59d7004    695 	ldr	r7,[sp,4]

                     696 ;191:                                unsigned char* inBuffer, int inBufferSize, int tpduSize)


                     697 ;192: {


                     698 

                     699 ;193:     unsigned char flowControl;


                     700 ;194: 


                     701 ;195:     int dataSize = tpduSize - sizeof(PacketHeader) - 1/*flow control*/;


                     702 

000002c8 d3500002    703 	cmple	r0,2

000002cc 1a00000c    704 	bne	.L472

                     705 ;205:     {


                     706 

                     707 ;206:          return -1;


                     708 

                     709 ;207:     }


                     710 ;208: 


                     711 ;209:     //Читаем оставшуюся часть заголовка


                     712 ;210:     if(readSocket(cotpConn->socket, &flowControl, 1) == -1)


                     713 

000002d0 e28d1003    714 	add	r1,sp,3

000002d4 e5960000    715 	ldr	r0,[r6]

000002d8 e3a02001    716 	mov	r2,1

000002dc eb000000*   717 	bl	readSocket

000002e0 e3700001    718 	cmn	r0,1

000002e4 0a000006    719 	beq	.L472

                     720 ;211:     {


                     721 


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s
                     722 ;212:         return -1;


                     723 

                     724 ;213:     }


                     725 ;214: 


                     726 ;215:     //Читаем сами данные


                     727 ;216:     if(readSocket(cotpConn->socket, inBuffer + cotpConn->inBufPos, dataSize) == -1)


                     728 

000002e8 e596000c    729 	ldr	r0,[r6,12]

000002ec e0801007    730 	add	r1,r0,r7

000002f0 e5960000    731 	ldr	r0,[r6]

000002f4 e1a02004    732 	mov	r2,r4

000002f8 eb000000*   733 	bl	readSocket

000002fc e3700001    734 	cmn	r0,1

00000300 1a000001    735 	bne	.L473

                     736 .L472:

                     737 ;217:     {


                     738 

                     739 ;218:             return -1;


                     740 

00000304 e3e00000    741 	mvn	r0,0

                     742 ;223:      {


                     743 

                     744 ;224:          //Последний пакет


                     745 ;225:          return 1;


                     746 

                     747 ;226:      }


                     748 ;227:      else


                     749 ;228:      {


                     750 

                     751 ;229:          //Не последний пакет


                     752 ;230:          return 0;


                     753 

                     754 ;313:                                                  tpduSize);


                     755 ;314:             if(lastDataPacket < 0)


                     756 

00000308 ea000006    757 	b	.L478

                     758 .L473:

                     759 ;219:     }


                     760 ;220:     cotpConn->inBufPos += dataSize;


                     761 

0000030c e596000c    762 	ldr	r0,[r6,12]

00000310 e0800004    763 	add	r0,r0,r4

00000314 e586000c    764 	str	r0,[r6,12]

00000318 e5dd0003    765 	ldrb	r0,[sp,3]

0000031c e1a00c00    766 	mov	r0,r0 lsl 24

00000320 e1b00fa0    767 	movs	r0,r0 lsr 31

                     768 ;221: 


                     769 ;222:      if (flowControl & 0x80)


                     770 

                     771 ;223:      {


                     772 

                     773 ;224:          //Последний пакет


                     774 ;225:          return 1;


                     775 

                     776 ;226:      }


                     777 ;227:      else


                     778 ;228:      {


                     779 

                     780 ;229:          //Не последний пакет


                     781 ;230:          return 0;


                     782 


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s
                     783 ;313:                                                  tpduSize);


                     784 ;314:             if(lastDataPacket < 0)


                     785 

00000324 5a000001    786 	bpl	.L477

                     787 .L478:

                     788 ;315:             {


                     789 

                     790 ;316:                 return -1;


                     791 

00000328 e3e00000    792 	mvn	r0,0

0000032c ea000006    793 	b	.L414

                     794 .L477:

                     795 ;317:             }


                     796 ;318:             if(lastDataPacket)


                     797 

00000330 e3500000    798 	cmp	r0,0

00000334 0affff6b    799 	beq	.L421

                     800 ;319:             {


                     801 

                     802 ;320:                 return cotpConn->inBufPos;


                     803 

00000338 e595000c    804 	ldr	r0,[r5,12]

0000033c ea000002    805 	b	.L414

                     806 .L484:

                     807 ;321:             }


                     808 ;322:             continue;


                     809 

                     810 ;323:         default:


                     811 ;324:             debugSendUshort("Unknown TPDU type", cotpConn->resvTPKTHeader.dt);


                     812 

00000340 e28f0000*   813 	adr	r0,.L1002

00000344 eb000000*   814 	bl	debugSendUshort

                     815 ;325:             continue;


                     816 

00000348 eaffff66    817 	b	.L421

                     818 .L414:

0000034c e28dd004    819 	add	sp,sp,4

00000350 e8bd8cf2    820 	ldmfd	[sp]!,{r1,r4-r7,r10-fp,pc}

                     821 	.endf	cotpReceiveData

                     822 	.align	4

                     823 .L413:

                     824 ;	"Error sending TPDU\000"

00000354 6f727245    825 	.data.b	69,114,114,111

00000358 65732072    826 	.data.b	114,32,115,101

0000035c 6e69646e    827 	.data.b	110,100,105,110

00000360 50542067    828 	.data.b	103,32,84,80

00000364 5544       829 	.data.b	68,85

00000366 00         830 	.data.b	0

00000367 00         831 	.align 4

                     832 

                     833 	.type	.L413,$object

                     834 	.size	.L413,4

                     835 

                     836 .L996:

                     837 ;	"Received COTP connect request\000"

00000368 65636552    838 	.data.b	82,101,99,101

0000036c 64657669    839 	.data.b	105,118,101,100

00000370 544f4320    840 	.data.b	32,67,79,84

00000374 6f632050    841 	.data.b	80,32,99,111

00000378 63656e6e    842 	.data.b	110,110,101,99

0000037c 65722074    843 	.data.b	116,32,114,101


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s
00000380 73657571    844 	.data.b	113,117,101,115

00000384 0074       845 	.data.b	116,0

00000386 0000       846 	.align 4

                     847 

                     848 	.type	.L996,$object

                     849 	.size	.L996,4

                     850 

                     851 .L997:

00000388 00000000*   852 	.data.w	.L895

                     853 	.type	.L997,$object

                     854 	.size	.L997,4

                     855 

                     856 .L998:

0000038c 00000000*   857 	.data.w	.L896

                     858 	.type	.L998,$object

                     859 	.size	.L998,4

                     860 

                     861 .L999:

00000390 00000000*   862 	.data.w	.L898

                     863 	.type	.L999,$object

                     864 	.size	.L999,4

                     865 

                     866 .L1000:

00000394 00000000*   867 	.data.w	.L899

                     868 	.type	.L1000,$object

                     869 	.size	.L1000,4

                     870 

                     871 .L1001:

00000398 00000000*   872 	.data.w	.L897

                     873 	.type	.L1001,$object

                     874 	.size	.L1001,4

                     875 

                     876 .L1002:

                     877 ;	"Unknown TPDU type\000"

0000039c 6e6b6e55    878 	.data.b	85,110,107,110

000003a0 206e776f    879 	.data.b	111,119,110,32

000003a4 55445054    880 	.data.b	84,80,68,85

000003a8 70797420    881 	.data.b	32,116,121,112

000003ac 0065       882 	.data.b	101,0

000003ae 0000       883 	.align 4

                     884 

                     885 	.type	.L1002,$object

                     886 	.size	.L1002,4

                     887 

                     888 	.align	4

                     889 ;lastDataPacket	r0	local

                     890 ;tpduSize	r4	local

                     891 ;pTPKTHeader	r4	local

                     892 ;.L893	.L902	static

                     893 ;pConnectRequest	r7	local

                     894 ;remainPacketSize	r2	local

                     895 ;li	r6	local

                     896 ;optionPos	r4	local

                     897 ;optionType	r1	local

                     898 ;optionLen	r6	local

                     899 ;cotpConn	r6	local

                     900 ;inBuffer	r7	local

                     901 ;flowControl	[sp,3]	local

                     902 ;dataSize	r4	local

                     903 ;.L894	.L903	static

                     904 


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s
                     905 ;cotpConn	r5	param

                     906 ;recvBuf	[sp,4]	param

                     907 ;maxByteCount	fp	param

                     908 

                     909 	.section ".bss","awb"

                     910 .L892:

                     911 	.section ".rodata","a"

                     912 .L895:

                     913 __UNNAMED_1_static_in_processCOTPconnectRequest:;	"TPDU does not fit in the buffer\000"

00000000 55445054    914 	.data.b	84,80,68,85

00000004 656f6420    915 	.data.b	32,100,111,101

00000008 6f6e2073    916 	.data.b	115,32,110,111

0000000c 69662074    917 	.data.b	116,32,102,105

00000010 6e692074    918 	.data.b	116,32,105,110

00000014 65687420    919 	.data.b	32,116,104,101

00000018 66756220    920 	.data.b	32,98,117,102

0000001c 00726566    921 	.data.b	102,101,114,0

                     922 	.type	__UNNAMED_1_static_in_processCOTPconnectRequest,$object

                     923 	.size	__UNNAMED_1_static_in_processCOTPconnectRequest,32

                     924 .L896:

                     925 __UNNAMED_1_static_in_parseCOTPoptions:;	"option length error\000"

00000020 6974706f    926 	.data.b	111,112,116,105

00000024 6c206e6f    927 	.data.b	111,110,32,108

00000028 74676e65    928 	.data.b	101,110,103,116

0000002c 72652068    929 	.data.b	104,32,101,114

00000030 00726f72    930 	.data.b	114,111,114,0

                     931 	.type	__UNNAMED_1_static_in_parseCOTPoptions,$object

                     932 	.size	__UNNAMED_1_static_in_parseCOTPoptions,20

                     933 .L897:

                     934 __UNNAMED_3_static_in_processCOTPconnectRequest:;	"Connection response is sent\000"

00000034 6e6e6f43    935 	.data.b	67,111,110,110

00000038 69746365    936 	.data.b	101,99,116,105

0000003c 72206e6f    937 	.data.b	111,110,32,114

00000040 6f707365    938 	.data.b	101,115,112,111

00000044 2065736e    939 	.data.b	110,115,101,32

00000048 73207369    940 	.data.b	105,115,32,115

0000004c 00746e65    941 	.data.b	101,110,116,0

                     942 	.type	__UNNAMED_3_static_in_processCOTPconnectRequest,$object

                     943 	.size	__UNNAMED_3_static_in_processCOTPconnectRequest,28

                     944 .L898:

                     945 __UNNAMED_2_static_in_parseCOTPoptions:;	"\tInvalid option size\000"

00000050 766e4909    946 	.data.b	9,73,110,118

00000054 64696c61    947 	.data.b	97,108,105,100

00000058 74706f20    948 	.data.b	32,111,112,116

0000005c 206e6f69    949 	.data.b	105,111,110,32

00000060 657a6973    950 	.data.b	115,105,122,101

00000064 00         951 	.data.b	0

00000065 000000     952 	.space	3

                     953 	.type	__UNNAMED_2_static_in_parseCOTPoptions,$object

                     954 	.size	__UNNAMED_2_static_in_parseCOTPoptions,24

                     955 .L899:

                     956 __UNNAMED_2_static_in_processCOTPconnectRequest:;	"sendCOTPConnectionResponse error\000"

00000068 646e6573    957 	.data.b	115,101,110,100

0000006c 50544f43    958 	.data.b	67,79,84,80

00000070 6e6e6f43    959 	.data.b	67,111,110,110

00000074 69746365    960 	.data.b	101,99,116,105

00000078 65526e6f    961 	.data.b	111,110,82,101

0000007c 6e6f7073    962 	.data.b	115,112,111,110

00000080 65206573    963 	.data.b	115,101,32,101

00000084 726f7272    964 	.data.b	114,114,111,114

00000088 00         965 	.data.b	0


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s
00000089 000000     966 	.space	3

                     967 	.type	__UNNAMED_2_static_in_processCOTPconnectRequest,$object

                     968 	.size	__UNNAMED_2_static_in_processCOTPconnectRequest,36

                     969 	.data

                     970 	.text

                     971 

                     972 ;326:         }


                     973 ;327:     }


                     974 ;328: 


                     975 ;329: }


                     976 

                     977 ;330: 


                     978 ;331: /*


                     979 ;332: //Возвращает количество полученных данных


                     980 ;333: int cotp(COTPConnection* cotpConn, void* sendData, int sendByteCount, void* recvBuf,


                     981 ;334:          int maxByteCount)


                     982 ;335: {    


                     983 ;336:     int lastDataPacket;


                     984 ;337:     cotpConn->inBufPos = 0;


                     985 ;338:     if(sendData != NULL && sendByteCount)


                     986 ;339:     {


                     987 ;340:         if(cotpSendData(cotpConn, sendData, sendByteCount) == -1)


                     988 ;341:         {


                     989 ;342:             return -1;


                     990 ;343:         }


                     991 ;344:     }


                     992 ;345:     while(1)


                     993 ;346:     {


                     994 ;347:         //Читаем заголовок пакета (TPKT + кусочек COTP)


                     995 ;348:         int tpduSize = readHeader(cotpConn);


                     996 ;349:         if(tpduSize == -1)


                     997 ;350:         {


                     998 ;351:             return -1;


                     999 ;352:         }


                    1000 ;353: 


                    1001 ;354:         switch (cotpConn->resvTPKTHeader.dt) {


                    1002 ;355:         case TPDU_TYPE_CONNECT_REQUEST:            


                    1003 ;356:             debugSendText("Received COTP connect request");


                    1004 ;357:             if(processCOTPconnectRequest(cotpConn, tpduSize) < 0)


                    1005 ;358:             {


                    1006 ;359:                 return -1;


                    1007 ;360:             }


                    1008 ;361:             continue;


                    1009 ;362:         case TPDU_TYPE_DATA:


                    1010 ;363:             //debugSendText("Received COTP data");


                    1011 ;364:             lastDataPacket = processCOTPdataTPDU(cotpConn, recvBuf, maxByteCount,


                    1012 ;365:                                                  tpduSize);


                    1013 ;366:             if(lastDataPacket < 0)


                    1014 ;367:             {


                    1015 ;368:                 return -1;


                    1016 ;369:             }


                    1017 ;370:             if(lastDataPacket)


                    1018 ;371:             {


                    1019 ;372:                 return cotpConn->inBufPos;


                    1020 ;373:             }


                    1021 ;374:             continue;


                    1022 ;375:         default:


                    1023 ;376:             debugSendUshort("Unknown TPDU type", cotpConn->resvTPKTHeader.dt);


                    1024 ;377:             continue;


                    1025 ;378:         }


                    1026 ;379:     }    



                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2io1.s
                    1027 ;380: }


                    1028 ;381: */


                    1029 ;382: 


                    1030 ;383: void initCOTPConnection(COTPConnection* cotpConn, SERVER_SOCKET socket)


                    1031 	.align	4

                    1032 	.align	4

                    1033 initCOTPConnection::

                    1034 ;384: {


                    1035 

                    1036 ;385:     cotpConn->socket = socket;


                    1037 

000003b0 e5801000   1038 	str	r1,[r0]

                    1039 ;386:     cotpConn->protocolClass = 0xFF;


                    1040 

000003b4 e3a010ff   1041 	mov	r1,255

000003b8 e5c01008   1042 	strb	r1,[r0,8]

                    1043 ;387:     cotpConn->remoteRef = 0xFFFF;


                    1044 

000003bc e3a01cff   1045 	mov	r1,255<<8

000003c0 e28110ff   1046 	add	r1,r1,255

000003c4 e1c010b4   1047 	strh	r1,[r0,4]

                    1048 ;388:     cotpConn->localRef = 1;


                    1049 

000003c8 e3a01001   1050 	mov	r1,1

000003cc e1c010b6   1051 	strh	r1,[r0,6]

000003d0 e12fff1e*  1052 	ret	

                    1053 	.endf	initCOTPConnection

                    1054 	.align	4

                    1055 

                    1056 ;cotpConn	r0	param

                    1057 ;socket	r1	param

                    1058 

                    1059 	.section ".bss","awb"

                    1060 .L1022:

                    1061 	.data

                    1062 	.text

                    1063 

                    1064 ;389: }


                    1065 	.align	4

                    1066 ;__UNNAMED_1_static_in_parseCOTPoptions	.L896	static

                    1067 ;__UNNAMED_2_static_in_parseCOTPoptions	.L898	static

                    1068 ;__UNNAMED_1_static_in_processCOTPconnectRequest	.L895	static

                    1069 ;__UNNAMED_2_static_in_processCOTPconnectRequest	.L899	static

                    1070 ;__UNNAMED_3_static_in_processCOTPconnectRequest	.L897	static

                    1071 

                    1072 	.data

                    1073 	.ghsnote version,6

                    1074 	.ghsnote tools,3

                    1075 	.ghsnote options,0

                    1076 	.text

                    1077 	.align	4

                    1078 	.section ".rodata","a"

                    1079 	.align	4

                    1080 	.text

