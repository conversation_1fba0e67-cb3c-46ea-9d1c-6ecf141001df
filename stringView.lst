                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bkg1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=stringView.c -o gh_bkg1.o -list=stringView.lst C:\Users\<USER>\AppData\Local\Temp\gh_bkg1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_bkg1.s
Source File: stringView.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile stringView.c

                      11 ;		-o stringView.o

                      12 ;Source File:   stringView.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:56 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "stringView.h"


                      23 ;2: #include <stddef.h>


                      24 ;3: #include <string.h>


                      25 ;4: 


                      26 ;5: void StringView_init(StringView* strView, const char * str, size_t len)


                      27 	.text

                      28 	.align	4

                      29 StringView_init::

                      30 ;6: {


                      31 

                      32 ;7: 	strView->p = str;


                      33 

00000000 e1a03001     34 	mov	r3,r1

00000004 e880000c     35 	stmea	[r0],{r2-r3}

                      36 ;8: 	strView->len = len;


                      37 

00000008 e12fff1e*    38 	ret	

                      39 	.endf	StringView_init

                      40 	.align	4

                      41 

                      42 ;strView	r0	param

                      43 ;str	r1	param

                      44 ;len	r2	param

                      45 

                      46 	.section ".bss","awb"

                      47 .L30:

                      48 	.data

                      49 	.text

                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bkg1.s
                      51 ;9: }


                      52 

                      53 ;10: 


                      54 ;11: void StringView_fromStringView(StringView* strView, StringView* src)


                      55 	.align	4

                      56 	.align	4

                      57 StringView_fromStringView::

                      58 ;12: {


                      59 

                      60 ;13: 	StringView_init(strView, src->p, src->len);


                      61 

0000000c e891000c     62 	ldmfd	[r1],{r2-r3}

00000010 e1a01003     63 	mov	r1,r3

00000014 eafffff9*    64 	b	StringView_init

                      65 	.endf	StringView_fromStringView

                      66 	.align	4

                      67 

                      68 ;strView	none	param

                      69 ;src	r1	param

                      70 

                      71 	.section ".bss","awb"

                      72 .L62:

                      73 	.data

                      74 	.text

                      75 

                      76 ;14: }


                      77 

                      78 ;15: 


                      79 ;16: void StringView_fromCStr(StringView* strView, char * str)


                      80 	.align	4

                      81 	.align	4

                      82 StringView_fromCStr::

00000018 e92d4030     83 	stmfd	[sp]!,{r4-r5,lr}

0000001c e1a05000     84 	mov	r5,r0

00000020 e1a04001     85 	mov	r4,r1

                      86 ;17: {


                      87 

                      88 ;18:     StringView_init(strView, str, strlen(str));


                      89 

00000024 e1a00004     90 	mov	r0,r4

00000028 eb000000*    91 	bl	strlen

0000002c e1a01004     92 	mov	r1,r4

00000030 e1a02000     93 	mov	r2,r0

00000034 e1a00005     94 	mov	r0,r5

00000038 e8bd4030     95 	ldmfd	[sp]!,{r4-r5,lr}

0000003c eaffffef*    96 	b	StringView_init

                      97 	.endf	StringView_fromCStr

                      98 	.align	4

                      99 

                     100 ;strView	r5	param

                     101 ;str	r4	param

                     102 

                     103 	.section ".bss","awb"

                     104 .L94:

                     105 	.data

                     106 	.text

                     107 

                     108 ;19: }


                     109 

                     110 ;20: 


                     111 ;21: int StringView_findChar(StringView * strView, unsigned char ch)



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bkg1.s
                     112 	.align	4

                     113 	.align	4

                     114 StringView_findChar::

00000040 e92d0030    115 	stmfd	[sp]!,{r4-r5}

                     116 ;22: {


                     117 

                     118 ;23: 	size_t pos;


                     119 ;24: 


                     120 ;25: 	if (strView->p == NULL)


                     121 

00000044 e590c004    122 	ldr	r12,[r0,4]

00000048 e35c0000    123 	cmp	r12,0

0000004c 0a000035    124 	beq	.L106

                     125 ;26: 	{


                     126 

                     127 ;27: 		return -1;


                     128 

                     129 ;28: 	}


                     130 ;29: 


                     131 ;30: 	for (pos = 0; pos < strView->len; ++pos)


                     132 

00000050 e5900000    133 	ldr	r0,[r0]

00000054 e3a02000    134 	mov	r2,0

00000058 e3500000    135 	cmp	r0,0

0000005c a1a04000    136 	movge	r4,r0

00000060 b3a04000    137 	movlt	r4,0

00000064 e1b031a4    138 	movs	r3,r4 lsr 3

00000068 0a000023    139 	beq	.L159

0000006c e1a0000c    140 	mov	r0,r12

                     141 .L160:

00000070 e5d05000    142 	ldrb	r5,[r0]

00000074 e1550001    143 	cmp	r5,r1

00000078 0a000024    144 	beq	.L195

0000007c e5d05001    145 	ldrb	r5,[r0,1]

00000080 e1550001    146 	cmp	r5,r1

00000084 02820001    147 	addeq	r0,r2,1

00000088 0a000027    148 	beq	.L101

0000008c e5d05002    149 	ldrb	r5,[r0,2]

00000090 e1550001    150 	cmp	r5,r1

00000094 02820002    151 	addeq	r0,r2,2

00000098 0a000023    152 	beq	.L101

0000009c e5d05003    153 	ldrb	r5,[r0,3]

000000a0 e1550001    154 	cmp	r5,r1

000000a4 02820003    155 	addeq	r0,r2,3

000000a8 0a00001f    156 	beq	.L101

000000ac e5d05004    157 	ldrb	r5,[r0,4]

000000b0 e1550001    158 	cmp	r5,r1

000000b4 02820004    159 	addeq	r0,r2,4

000000b8 0a00001b    160 	beq	.L101

000000bc e5d05005    161 	ldrb	r5,[r0,5]

000000c0 e1550001    162 	cmp	r5,r1

000000c4 02820005    163 	addeq	r0,r2,5

000000c8 0a000017    164 	beq	.L101

000000cc e5d05006    165 	ldrb	r5,[r0,6]

000000d0 e1550001    166 	cmp	r5,r1

000000d4 02820006    167 	addeq	r0,r2,6

000000d8 0a000013    168 	beq	.L101

000000dc e5d05007    169 	ldrb	r5,[r0,7]

000000e0 e1550001    170 	cmp	r5,r1

000000e4 02820007    171 	addeq	r0,r2,7

000000e8 0a00000f    172 	beq	.L101


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bkg1.s
000000ec e2800008    173 	add	r0,r0,8

000000f0 e2822008    174 	add	r2,r2,8

000000f4 e2533001    175 	subs	r3,r3,1

000000f8 1affffdc    176 	bne	.L160

                     177 .L159:

000000fc e2143007    178 	ands	r3,r4,7

00000100 0a000008    179 	beq	.L106

00000104 e082000c    180 	add	r0,r2,r12

                     181 .L194:

00000108 e5d0c000    182 	ldrb	r12,[r0]

0000010c e15c0001    183 	cmp	r12,r1

                     184 .L195:

00000110 01a00002    185 	moveq	r0,r2

00000114 0a000004    186 	beq	.L101

                     187 .L197:

00000118 e2800001    188 	add	r0,r0,1

0000011c e2822001    189 	add	r2,r2,1

00000120 e2533001    190 	subs	r3,r3,1

00000124 1afffff7    191 	bne	.L194

                     192 .L106:

                     193 ;35: 		}


                     194 ;36: 	}


                     195 ;37: 	return -1;


                     196 

00000128 e3e00000    197 	mvn	r0,0

                     198 .L101:

0000012c e8bd0030    199 	ldmfd	[sp]!,{r4-r5}

00000130 e12fff1e*   200 	ret	

                     201 	.endf	StringView_findChar

                     202 	.align	4

                     203 ;pos	r2	local

                     204 

                     205 ;strView	r0	param

                     206 ;ch	r1	param

                     207 

                     208 	.section ".bss","awb"

                     209 .L452:

                     210 	.data

                     211 	.text

                     212 

                     213 ;38: }


                     214 

                     215 ;39: 


                     216 ;40: int StringView_findCharBack(StringView * strView, unsigned char ch)


                     217 	.align	4

                     218 	.align	4

                     219 StringView_findCharBack::

00000134 e92d0030    220 	stmfd	[sp]!,{r4-r5}

                     221 ;41: {


                     222 

                     223 ;42:     int pos;


                     224 ;43: 


                     225 ;44:     if (strView->p == NULL)


                     226 

00000138 e590c004    227 	ldr	r12,[r0,4]

0000013c e35c0000    228 	cmp	r12,0

00000140 0a000035    229 	beq	.L529

                     230 ;45:     {


                     231 

                     232 ;46:         return -1;


                     233 


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bkg1.s
                     234 ;47:     }


                     235 ;48: 


                     236 ;49:     for (pos = strView->len - 1; pos >= 0; --pos)


                     237 

00000144 e5902000    238 	ldr	r2,[r0]

00000148 e3520000    239 	cmp	r2,0

0000014c e2420001    240 	sub	r0,r2,1

00000150 a1a04002    241 	movge	r4,r2

00000154 b3a04000    242 	movlt	r4,0

00000158 e1b031a4    243 	movs	r3,r4 lsr 3

0000015c 0a000024    244 	beq	.L575

00000160 e08c2000    245 	add	r2,r12,r0

00000164 e2422007    246 	sub	r2,r2,7

                     247 .L576:

00000168 e5d25007    248 	ldrb	r5,[r2,7]

0000016c e1550001    249 	cmp	r5,r1

00000170 0a00002a    250 	beq	.L524

00000174 e5d25006    251 	ldrb	r5,[r2,6]

00000178 e1550001    252 	cmp	r5,r1

0000017c 02400001    253 	subeq	r0,r0,1

00000180 0a000026    254 	beq	.L524

00000184 e5d25005    255 	ldrb	r5,[r2,5]

00000188 e1550001    256 	cmp	r5,r1

0000018c 02400002    257 	subeq	r0,r0,2

00000190 0a000022    258 	beq	.L524

00000194 e5d25004    259 	ldrb	r5,[r2,4]

00000198 e1550001    260 	cmp	r5,r1

0000019c 02400003    261 	subeq	r0,r0,3

000001a0 0a00001e    262 	beq	.L524

000001a4 e5d25003    263 	ldrb	r5,[r2,3]

000001a8 e1550001    264 	cmp	r5,r1

000001ac 02400004    265 	subeq	r0,r0,4

000001b0 0a00001a    266 	beq	.L524

000001b4 e5d25002    267 	ldrb	r5,[r2,2]

000001b8 e1550001    268 	cmp	r5,r1

000001bc 02400005    269 	subeq	r0,r0,5

000001c0 0a000016    270 	beq	.L524

000001c4 e5d25001    271 	ldrb	r5,[r2,1]

000001c8 e1550001    272 	cmp	r5,r1

000001cc 02400006    273 	subeq	r0,r0,6

000001d0 0a000012    274 	beq	.L524

000001d4 e5d25000    275 	ldrb	r5,[r2]

000001d8 e1550001    276 	cmp	r5,r1

000001dc 02400007    277 	subeq	r0,r0,7

000001e0 0a00000e    278 	beq	.L524

000001e4 e2422008    279 	sub	r2,r2,8

000001e8 e2400008    280 	sub	r0,r0,8

000001ec e2533001    281 	subs	r3,r3,1

000001f0 1affffdc    282 	bne	.L576

                     283 .L575:

000001f4 e2143007    284 	ands	r3,r4,7

000001f8 0a000007    285 	beq	.L529

000001fc e080200c    286 	add	r2,r0,r12

                     287 .L610:

00000200 e5d2c000    288 	ldrb	r12,[r2]

00000204 e15c0001    289 	cmp	r12,r1

00000208 0a000004    290 	beq	.L524

                     291 .L613:

0000020c e2422001    292 	sub	r2,r2,1

00000210 e2400001    293 	sub	r0,r0,1

00000214 e2533001    294 	subs	r3,r3,1


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bkg1.s
00000218 1afffff8    295 	bne	.L610

                     296 .L529:

                     297 ;54:         }


                     298 ;55:     }


                     299 ;56:     return -1;


                     300 

0000021c e3e00000    301 	mvn	r0,0

                     302 .L524:

00000220 e8bd0030    303 	ldmfd	[sp]!,{r4-r5}

00000224 e12fff1e*   304 	ret	

                     305 	.endf	StringView_findCharBack

                     306 	.align	4

                     307 ;pos	r0	local

                     308 

                     309 ;strView	r0	param

                     310 ;ch	r1	param

                     311 

                     312 	.section ".bss","awb"

                     313 .L868:

                     314 	.data

                     315 	.text

                     316 

                     317 ;57: }


                     318 

                     319 ;58: 


                     320 ;59: int StringView_splitChar(StringView* src, unsigned char splitChar,


                     321 	.align	4

                     322 	.align	4

                     323 StringView_splitChar::

00000228 e92d4070    324 	stmfd	[sp]!,{r4-r6,lr}

                     325 ;60: 	StringView* dst1, StringView* dst2)


                     326 ;61: {


                     327 

                     328 ;62: 	int splitPos = StringView_findChar(src, splitChar);


                     329 

0000022c e1a06002    330 	mov	r6,r2

00000230 e1a05003    331 	mov	r5,r3

00000234 e1a04000    332 	mov	r4,r0

00000238 ebffff80*   333 	bl	StringView_findChar

                     334 ;63: 	if (splitPos == -1)


                     335 

0000023c e3700001    336 	cmn	r0,1

                     337 ;64: 	{


                     338 

                     339 ;65: 		return 0;


                     340 

00000240 03a00000    341 	moveq	r0,0

00000244 0a00000b    342 	beq	.L940

                     343 ;66: 	}


                     344 ;67: 	dst1->len = splitPos;


                     345 

00000248 e5941004    346 	ldr	r1,[r4,4]

0000024c e5860000    347 	str	r0,[r6]

                     348 ;68: 	dst1->p = src->p;


                     349 

00000250 e5861004    350 	str	r1,[r6,4]

                     351 ;69: 	dst2->len = src->len - splitPos - 1;


                     352 

00000254 e5941000    353 	ldr	r1,[r4]

00000258 e0411000    354 	sub	r1,r1,r0

0000025c e2411001    355 	sub	r1,r1,1


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bkg1.s
00000260 e5851000    356 	str	r1,[r5]

                     357 ;70: 	dst2->p = src->p + splitPos + 1;


                     358 

00000264 e5941004    359 	ldr	r1,[r4,4]

00000268 e0800001    360 	add	r0,r0,r1

0000026c e2800001    361 	add	r0,r0,1

00000270 e5850004    362 	str	r0,[r5,4]

                     363 ;71: 	return 1;


                     364 

00000274 e3a00001    365 	mov	r0,1

                     366 .L940:

00000278 e8bd8070    367 	ldmfd	[sp]!,{r4-r6,pc}

                     368 	.endf	StringView_splitChar

                     369 	.align	4

                     370 ;splitPos	r0	local

                     371 

                     372 ;src	r4	param

                     373 ;splitChar	none	param

                     374 ;dst1	r6	param

                     375 ;dst2	r5	param

                     376 

                     377 	.section ".bss","awb"

                     378 .L982:

                     379 	.data

                     380 	.text

                     381 

                     382 ;72: }


                     383 

                     384 ;73: 


                     385 ;74: int StringView_cmp(StringView * strView1, StringView * strView2)


                     386 	.align	4

                     387 	.align	4

                     388 StringView_cmp::

0000027c e1a03000    389 	mov	r3,r0

                     390 ;75: {


                     391 

                     392 ;76: 	int result;


                     393 ;77:     int lenDiff;


                     394 ;78:     lenDiff = strView1->len - strView2->len;


                     395 

00000280 e5910000    396 	ldr	r0,[r1]

00000284 e5932000    397 	ldr	r2,[r3]

00000288 e0520000    398 	subs	r0,r2,r0

                     399 ;79: 	if (lenDiff != 0)


                     400 

                     401 ;82: 	}


                     402 ;83: 	result = memcmp(strView1->p, strView2->p, strView1->len);


                     403 

0000028c 05930004    404 	ldreq	r0,[r3,4]

00000290 05911004    405 	ldreq	r1,[r1,4]

00000294 0a000000*   406 	beq	memcmp

                     407 ;80: 	{


                     408 

                     409 ;81: 		return lenDiff;


                     410 

00000298 e12fff1e*   411 	ret	

                     412 	.endf	StringView_cmp

                     413 	.align	4

                     414 ;lenDiff	r0	local

                     415 

                     416 ;strView1	r3	param


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_bkg1.s
                     417 ;strView2	r1	param

                     418 

                     419 	.section ".bss","awb"

                     420 .L1030:

                     421 	.data

                     422 	.text

                     423 

                     424 ;85: }


                     425 

                     426 ;86: 


                     427 ;87: int StringView_cmpCStr(StringView * strView, const char* cstr)


                     428 	.align	4

                     429 	.align	4

                     430 StringView_cmpCStr::

0000029c e92d4030    431 	stmfd	[sp]!,{r4-r5,lr}

                     432 ;88: {


                     433 

                     434 ;89: 	StringView cstrView;


                     435 ;90: 


                     436 ;91:     StringView_init(&cstrView, cstr, strlen(cstr));


                     437 

000002a0 e24dd008    438 	sub	sp,sp,8

000002a4 e1a05000    439 	mov	r5,r0

000002a8 e1a04001    440 	mov	r4,r1

000002ac e1a00004    441 	mov	r0,r4

000002b0 eb000000*   442 	bl	strlen

000002b4 e1a01004    443 	mov	r1,r4

000002b8 e1a02000    444 	mov	r2,r0

000002bc e1a0000d    445 	mov	r0,sp

000002c0 ebffff4e*   446 	bl	StringView_init

                     447 ;92: 	return StringView_cmp(strView, &cstrView);


                     448 

000002c4 e1a0100d    449 	mov	r1,sp

000002c8 e1a00005    450 	mov	r0,r5

000002cc ebffffea*   451 	bl	StringView_cmp

000002d0 e28dd008    452 	add	sp,sp,8

000002d4 e8bd8030    453 	ldmfd	[sp]!,{r4-r5,pc}

                     454 	.endf	StringView_cmpCStr

                     455 	.align	4

                     456 ;cstrView	[sp]	local

                     457 

                     458 ;strView	r5	param

                     459 ;cstr	r4	param

                     460 

                     461 	.section ".bss","awb"

                     462 .L1070:

                     463 	.data

                     464 	.text

                     465 

                     466 ;93: }


                     467 	.align	4

                     468 

                     469 	.data

                     470 	.ghsnote version,6

                     471 	.ghsnote tools,3

                     472 	.ghsnote options,0

                     473 	.text

                     474 	.align	4

