                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=infoReport.c -o gh_6ss1.o -list=infoReport.lst C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
Source File: infoReport.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile infoReport.c

                      11 ;		-o infoReport.o

                      12 ;Source File:   infoReport.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:52 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "infoReport.h"


                      23 ;2: 


                      24 ;3: #include "AsnEncoding.h"


                      25 ;4: #include "bufViewBER.h"


                      26 ;5: #include "mms.h"


                      27 ;6: #include "iedTree/iedEntity.h"


                      28 ;7: #include "iedTree/iedObjects.h"


                      29 ;8: #include <debug.h>


                      30 ;9: 


                      31 ;10: #pragma alignvar (4)


                      32 ;11: 


                      33 ;12: //ApplError information report variable access specification


                      34 ;13: //Эта информация неизменна и всегда содержит


                      35 ;14: //строку "LastApplError"


                      36 ;15: uint8_t applErrorVarSpec[] = {


                      37 ;16: 0xA0, 0x13, 0x30, 0x11, 0xA0, 0x0F, 0x80, 0x0D,


                      38 ;17: 0x4C, 0x61, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6C,


                      39 ;18: 0x45, 0x72, 0x72, 0x6F, 0x72 };


                      40 ;19: 


                      41 ;20: char *lastApplErrorName = "LastApplError";


                      42 ;21: 


                      43 ;22: 


                      44 ;23: typedef struct


                      45 ;24: {


                      46 ;25:     size_t cntrlObjName;


                      47 ;26:     size_t error;


                      48 ;27:     size_t originFields;


                      49 ;28:     size_t origin;


                      50 ;29:     size_t ctlNum;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
                      51 ;30:     size_t addCause;


                      52 ;31:     size_t allFields;


                      53 ;32:     size_t fullStruct;


                      54 ;33: } LastApplErrorSizes;


                      55 ;34: 


                      56 ;35: // Имя объекта управления (скорее всего pos) при при передаче


                      57 ;36: // negative CommandTermination.


                      58 ;37: // Используется из потока отчётов reportsThread


                      59 ;38: static uint8_t ctrlObjNameBuf[MAX_OBJECT_REFERENCE];


                      60 ;39: 


                      61 ;40: static void calcLastApplErrSizes(LastApplErrorSizes* sizes,


                      62 	.text

                      63 	.align	4

                      64 calcLastApplErrSizes:

00000000 e92d4cf0     65 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

00000004 e1a0b001     66 	mov	fp,r1

00000008 e1a05002     67 	mov	r5,r2

0000000c e1a06003     68 	mov	r6,r3

00000010 e59d701c     69 	ldr	r7,[sp,28]

00000014 e1a04000     70 	mov	r4,r0

00000018 e5dd0024     71 	ldrb	r0,[sp,36]

0000001c e5dda020     72 	ldrb	r10,[sp,32]

                      73 ;41:                                  StringView* cntrlObj, uint8_t error, uint8_t orCat,


                      74 ;42:                                  StringView* orIdent, uint8_t ctlNum, uint8_t addCause)


                      75 ;43: {


                      76 

                      77 ;44:     size_t orIdentSize;


                      78 ;45:     size_t orCatSize;


                      79 ;46: 


                      80 ;47:     sizes->addCause = BerEncoder_uint32determineEncodedSizeTL(addCause);


                      81 

00000020 eb000000*    82 	bl	BerEncoder_uint32determineEncodedSizeTL

00000024 e5840014     83 	str	r0,[r4,20]

                      84 ;48:     sizes->ctlNum = BerEncoder_uint32determineEncodedSizeTL(ctlNum);


                      85 

00000028 e1a0000a     86 	mov	r0,r10

0000002c eb000000*    87 	bl	BerEncoder_uint32determineEncodedSizeTL

00000030 e5840010     88 	str	r0,[r4,16]

                      89 ;49: 


                      90 ;50:     orIdentSize = BerEncoder_determineFullObjectSize(orIdent->len);


                      91 

00000034 e5970000     92 	ldr	r0,[r7]

00000038 eb000000*    93 	bl	BerEncoder_determineFullObjectSize

0000003c e1a07000     94 	mov	r7,r0

                      95 ;51:     orCatSize = BerEncoder_uint32determineEncodedSizeTL(orCat);


                      96 

00000040 e1a00006     97 	mov	r0,r6

00000044 eb000000*    98 	bl	BerEncoder_uint32determineEncodedSizeTL

                      99 ;52:     sizes->originFields = orIdentSize + orCatSize;


                     100 

00000048 e0800007    101 	add	r0,r0,r7

0000004c e5840008    102 	str	r0,[r4,8]

                     103 ;53: 


                     104 ;54:     sizes->origin = BerEncoder_determineFullObjectSize(orCatSize + orIdentSize);


                     105 

00000050 eb000000*   106 	bl	BerEncoder_determineFullObjectSize

00000054 e584000c    107 	str	r0,[r4,12]

                     108 ;55:     sizes->error = BerEncoder_uint32determineEncodedSizeTL(error);


                     109 

00000058 e1a00005    110 	mov	r0,r5

0000005c eb000000*   111 	bl	BerEncoder_uint32determineEncodedSizeTL


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
00000060 e5840004    112 	str	r0,[r4,4]

                     113 ;56:     sizes->cntrlObjName = BerEncoder_determineFullObjectSize(cntrlObj->len);


                     114 

00000064 e59b0000    115 	ldr	r0,[fp]

00000068 eb000000*   116 	bl	BerEncoder_determineFullObjectSize

0000006c e484000c    117 	str	r0,[r4],12

                     118 ;57: 


                     119 ;58:     sizes->allFields = sizes->cntrlObjName + sizes->error + sizes->origin


                     120 

00000070 e5141008    121 	ldr	r1,[r4,-8]

00000074 e8940068    122 	ldmfd	[r4],{r3,r5-r6}

00000078 e0811006    123 	add	r1,r1,r6

0000007c e0832001    124 	add	r2,r3,r1

00000080 e0851002    125 	add	r1,r5,r2

00000084 e0800001    126 	add	r0,r0,r1

00000088 e584000c    127 	str	r0,[r4,12]

                     128 ;59:             + sizes->ctlNum + sizes->addCause;


                     129 ;60:     sizes->fullStruct = BerEncoder_determineFullObjectSize(sizes->allFields);


                     130 

0000008c eb000000*   131 	bl	BerEncoder_determineFullObjectSize

00000090 e5840010    132 	str	r0,[r4,16]

00000094 e8bd4cf0    133 	ldmfd	[sp]!,{r4-r7,r10-fp,lr}

00000098 e12fff1e*   134 	ret	

                     135 	.endf	calcLastApplErrSizes

                     136 	.align	4

                     137 ;orIdentSize	r7	local

                     138 

                     139 ;sizes	r4	param

                     140 ;cntrlObj	fp	param

                     141 ;error	r5	param

                     142 ;orCat	r6	param

                     143 ;orIdent	r7	param

                     144 ;ctlNum	r10	param

                     145 ;addCause	r12	param

                     146 

                     147 	.section ".bss","awb"

                     148 .L30:

                     149 	.data

                     150 	.text

                     151 

                     152 ;61: }


                     153 

                     154 ;62: 


                     155 ;63: static bool encodeLastApplErrErr(BufferView* wrBuf, LastApplErrorSizes* sizes,


                     156 	.align	4

                     157 	.align	4

                     158 encodeLastApplErrErr:

0000009c e92d4cf4    159 	stmfd	[sp]!,{r2,r4-r7,r10-fp,lr}

                     160 ;64:                               StringView* cntrlObjName, uint8_t error, uint8_t orCat,


                     161 ;65:                               StringView* orIdent, uint8_t ctlNum, uint8_t addCause)


                     162 ;66: {


                     163 

                     164 ;67:     //Структура


                     165 ;68:     if(!BufferView_encodeTL(wrBuf, 0xA2,  sizes->allFields))


                     166 

000000a0 e1a0b003    167 	mov	fp,r3

000000a4 e59d6024    168 	ldr	r6,[sp,36]

000000a8 e5dd7028    169 	ldrb	r7,[sp,40]

000000ac e5dda02c    170 	ldrb	r10,[sp,44]

000000b0 e1a05001    171 	mov	r5,r1

000000b4 e5952018    172 	ldr	r2,[r5,24]


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
000000b8 e1a04000    173 	mov	r4,r0

000000bc e3a010a2    174 	mov	r1,162

000000c0 eb000000*   175 	bl	BufferView_encodeTL

000000c4 e3500000    176 	cmp	r0,0

000000c8 0a000029    177 	beq	.L61

                     178 ;69:     {


                     179 

                     180 ;70:         return false;


                     181 

                     182 ;71:     }


                     183 ;72:     //Поля структуры


                     184 ;73:     //cntrlObj


                     185 ;74:     if(!BufferView_encodeStringView(wrBuf, IEC61850_BER_VISIBLE_STRING,


                     186 

000000cc e59d2000    187 	ldr	r2,[sp]

000000d0 e1a00004    188 	mov	r0,r4

000000d4 e3a0108a    189 	mov	r1,138

000000d8 eb000000*   190 	bl	BufferView_encodeStringView

000000dc e3500000    191 	cmp	r0,0

000000e0 0a000023    192 	beq	.L61

                     193 ;75:                                     cntrlObjName))


                     194 ;76:     {


                     195 

                     196 ;77:         return false;


                     197 

                     198 ;78:     }


                     199 ;79:     //error


                     200 ;80:     if(!BufferView_encodeUInt32(wrBuf, IEC61850_BER_INTEGER, error))


                     201 

000000e4 e1a0200b    202 	mov	r2,fp

000000e8 e1a00004    203 	mov	r0,r4

000000ec e3a01085    204 	mov	r1,133

000000f0 eb000000*   205 	bl	BufferView_encodeUInt32

000000f4 e3500000    206 	cmp	r0,0

000000f8 0a00001d    207 	beq	.L61

                     208 ;81:     {


                     209 

                     210 ;82:         return false;


                     211 

                     212 ;83:     }


                     213 ;84: 


                     214 ;85:     //origin


                     215 ;86:     if(!BufferView_encodeTL(wrBuf, 0xA2, sizes->originFields))


                     216 

000000fc e5952008    217 	ldr	r2,[r5,8]

00000100 e1a00004    218 	mov	r0,r4

00000104 e3a010a2    219 	mov	r1,162

00000108 eb000000*   220 	bl	BufferView_encodeTL

0000010c e3500000    221 	cmp	r0,0

00000110 0a000017    222 	beq	.L61

                     223 ;87:     {


                     224 

                     225 ;88:         return false;


                     226 

                     227 ;89:     }


                     228 ;90:     //orCat


                     229 ;91:     if(!BufferView_encodeUInt32(wrBuf, IEC61850_BER_INTEGER, orCat))


                     230 

00000114 e5dd2020    231 	ldrb	r2,[sp,32]

00000118 e1a00004    232 	mov	r0,r4

0000011c e3a01085    233 	mov	r1,133


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
00000120 eb000000*   234 	bl	BufferView_encodeUInt32

00000124 e3500000    235 	cmp	r0,0

00000128 0a000011    236 	beq	.L61

                     237 ;92:     {


                     238 

                     239 ;93:         return false;


                     240 

                     241 ;94:     }


                     242 ;95:     //orIdent


                     243 ;96:     if(!BufferView_encodeStringView(wrBuf, IEC61850_BER_OCTET_STRING,


                     244 

0000012c e1a02006    245 	mov	r2,r6

00000130 e1a00004    246 	mov	r0,r4

00000134 e3a01089    247 	mov	r1,137

00000138 eb000000*   248 	bl	BufferView_encodeStringView

0000013c e3500000    249 	cmp	r0,0

00000140 0a00000b    250 	beq	.L61

                     251 ;97:                                     orIdent))


                     252 ;98:     {


                     253 

                     254 ;99:         return false;


                     255 

                     256 ;100:     }


                     257 ;101:     //ctlNum


                     258 ;102:     if(!BufferView_encodeUInt32(wrBuf, IEC61850_BER_UNSIGNED_INTEGER,


                     259 

00000144 e1a02007    260 	mov	r2,r7

00000148 e1a00004    261 	mov	r0,r4

0000014c e3a01086    262 	mov	r1,134

00000150 eb000000*   263 	bl	BufferView_encodeUInt32

00000154 e3500000    264 	cmp	r0,0

00000158 0a000005    265 	beq	.L61

                     266 ;103:                                 ctlNum))


                     267 ;104:     {


                     268 

                     269 ;105:         return false;


                     270 

                     271 ;106:     }


                     272 ;107:     //addCause


                     273 ;108:     if(!BufferView_encodeUInt32(wrBuf, IEC61850_BER_INTEGER, addCause))


                     274 

0000015c e1a0200a    275 	mov	r2,r10

00000160 e1a00004    276 	mov	r0,r4

00000164 e3a01085    277 	mov	r1,133

00000168 eb000000*   278 	bl	BufferView_encodeUInt32

0000016c e3500000    279 	cmp	r0,0

                     280 ;111:     }


                     281 ;112:     return true;


                     282 

00000170 13a00001    283 	movne	r0,1

                     284 .L61:

                     285 ;109:     {


                     286 

                     287 ;110:         return false;


                     288 

00000174 03a00000    289 	moveq	r0,0

                     290 .L37:

00000178 e8bd4cf4    291 	ldmfd	[sp]!,{r2,r4-r7,r10-fp,lr}

0000017c e12fff1e*   292 	ret	

                     293 	.endf	encodeLastApplErrErr

                     294 	.align	4


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
                     295 

                     296 ;wrBuf	r4	param

                     297 ;sizes	r5	param

                     298 ;cntrlObjName	[sp]	param

                     299 ;error	fp	param

                     300 ;orCat	[sp,32]	param

                     301 ;orIdent	r6	param

                     302 ;ctlNum	r7	param

                     303 ;addCause	r10	param

                     304 

                     305 	.section ".bss","awb"

                     306 .L168:

                     307 	.data

                     308 	.text

                     309 

                     310 ;113: }


                     311 

                     312 ;114: 


                     313 ;115: bool InfoReport_createLastApplErrorReport(BufferView* wrBuf,


                     314 	.align	4

                     315 	.align	4

                     316 	.align	4

                     317 InfoReport_createLastApplErrorReport::

00000180 e92d4cf0    318 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                     319 ;116:                                       StringView* cntrlObj, uint8_t error, uint8_t orCat,


                     320 ;117:                                       StringView* orIdent, uint8_t ctlNum, uint8_t addCause)


                     321 ;118: {


                     322 

                     323 ;119:     LastApplErrorSizes errSizes;


                     324 ;120: 


                     325 ;121:     //Variable acess specification (0xA0)


                     326 ;122:     //Эта часть для этого вида отчёта всегда одинаковая


                     327 ;123:     if(!BufferView_writeData(wrBuf, applErrorVarSpec, sizeof(applErrorVarSpec)))


                     328 

00000184 e1a06003    329 	mov	r6,r3

00000188 e24dd030    330 	sub	sp,sp,48

0000018c e59d704c    331 	ldr	r7,[sp,76]

00000190 e5dda054    332 	ldrb	r10,[sp,84]

00000194 e1a04001    333 	mov	r4,r1

00000198 e59f14b8*   334 	ldr	r1,.L288

0000019c e1a0b000    335 	mov	fp,r0

000001a0 e1a05002    336 	mov	r5,r2

000001a4 e3a02015    337 	mov	r2,21

000001a8 eb000000*   338 	bl	BufferView_writeData

000001ac e3500000    339 	cmp	r0,0

000001b0 0a00000c    340 	beq	.L209

                     341 ;124:     {


                     342 

                     343 ;125:         return false;


                     344 

                     345 ;126:     }


                     346 ;127: 


                     347 ;128:     //Вычисляем размеры для List of access result (0xA0)


                     348 ;129:     calcLastApplErrSizes(&errSizes, cntrlObj, error, orCat, orIdent, ctlNum, addCause);


                     349 

000001b4 e5dd1050    350 	ldrb	r1,[sp,80]

000001b8 e1a00007    351 	mov	r0,r7

000001bc e88d0403    352 	stmea	[sp],{r0-r1,r10}

000001c0 e1a03006    353 	mov	r3,r6

000001c4 e1a02005    354 	mov	r2,r5

000001c8 e1a01004    355 	mov	r1,r4


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
000001cc e28d0010    356 	add	r0,sp,16

000001d0 ebffff8a*   357 	bl	calcLastApplErrSizes

                     358 ;130: 


                     359 ;131: 


                     360 ;132:     // Кодируем List of access result (0xA0)


                     361 ;133:     if(!BufferView_encodeTL(wrBuf, 0xA0, errSizes.fullStruct))


                     362 

000001d4 e59d202c    363 	ldr	r2,[sp,44]

000001d8 e1a0000b    364 	mov	r0,fp

000001dc e3a010a0    365 	mov	r1,160

000001e0 eb000000*   366 	bl	BufferView_encodeTL

000001e4 e3500000    367 	cmp	r0,0

                     368 .L209:

                     369 ;134:     {


                     370 

                     371 ;135:         return false;


                     372 

000001e8 03a00000    373 	moveq	r0,0

000001ec 0a000007    374 	beq	.L203

                     375 .L208:

                     376 ;136:     }


                     377 ;137: 


                     378 ;138:     return encodeLastApplErrErr(wrBuf, &errSizes, cntrlObj, error, orCat, orIdent,


                     379 

000001f0 e1a0c00a    380 	mov	r12,r10

000001f4 e5dda050    381 	ldrb	r10,[sp,80]

000001f8 e88d14c0    382 	stmea	[sp],{r6-r7,r10,r12}

000001fc e1a03005    383 	mov	r3,r5

00000200 e1a02004    384 	mov	r2,r4

00000204 e28d1010    385 	add	r1,sp,16

00000208 e1a0000b    386 	mov	r0,fp

0000020c ebffffa2*   387 	bl	encodeLastApplErrErr

                     388 .L203:

00000210 e28dd030    389 	add	sp,sp,48

00000214 e8bd8cf0    390 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                     391 	.endf	InfoReport_createLastApplErrorReport

                     392 	.align	4

                     393 ;errSizes	[sp,16]	local

                     394 

                     395 ;wrBuf	fp	param

                     396 ;cntrlObj	r4	param

                     397 ;error	r5	param

                     398 ;orCat	r6	param

                     399 ;orIdent	r7	param

                     400 ;ctlNum	[sp,80]	param

                     401 ;addCause	r10	param

                     402 

                     403 	.data

                     404 .L274:

                     405 	.section ".rodata","a"

                     406 .L275:;	"LastApplError\000"

00000000 7473614c    407 	.data.b	76,97,115,116

00000004 6c707041    408 	.data.b	65,112,112,108

00000008 6f727245    409 	.data.b	69,114,114,111

0000000c 0072       410 	.data.b	114,0

0000000e 0000       411 	.space	2

                     412 	.section ".bss","awb"

00000000 00000000    413 ctrlObjNameBuf:	.space	129

00000004 00000000 
00000008 00000000 
0000000c 00000000 

                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
00000010 00000000 
00000014 00000000 
00000018 00000000 
0000001c 00000000 
00000020 00000000 
00000024 00000000 
00000028 00000000 
0000002c 00000000 
00000030 00000000 
00000034 00000000 
00000038 00000000 
0000003c 00000000 
00000040 00000000 
00000044 00000000 
00000048 00000000 
0000004c 00000000 
00000050 00000000 
00000054 00000000 
00000058 00000000 
0000005c 00000000 
00000060 00000000 
00000064 00000000 
00000068 00000000 
0000006c 00000000 
00000070 00000000 
00000074 00000000 
00000078 00000000 
0000007c 00000000 
00000080 00 
00000081 000000     414 	.space	3

                     415 	.data

                     416 	.text

                     417 

                     418 ;139:                                 ctlNum, addCause);


                     419 ;140: }


                     420 

                     421 ;141: 


                     422 ;142: 


                     423 ;143: bool InfoReport_createPositiveCmdTermReport( IEDEntity cntrlObj,


                     424 	.align	4

                     425 	.align	4

                     426 InfoReport_createPositiveCmdTermReport::

00000218 e92d4ff0    427 	stmfd	[sp]!,{r4-fp,lr}

                     428 ;144:     BufferView* wrBuf, StringView* domainId, StringView* itemId)


                     429 ;145: {


                     430 

                     431 ;146:     size_t cntrlObjLen;


                     432 ;147:     size_t domainSpecificLen;


                     433 ;148:     size_t nameObjLen;


                     434 ;149:     size_t listOfVarLen;


                     435 ;150:     size_t varAccessSpec;


                     436 ;151: 


                     437 ;152:     //===============Вычисляем размеры=============


                     438 ;153:     //Variable acess specification (0xA0)


                     439 ;154:     domainSpecificLen = BerEncoder_determineFullObjectSize(domainId->len)


                     440 

0000021c e1a04001    441 	mov	r4,r1

00000220 e24dd00c    442 	sub	sp,sp,12

00000224 e1a05000    443 	mov	r5,r0

00000228 e1a07002    444 	mov	r7,r2

0000022c e5970000    445 	ldr	r0,[r7]


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
00000230 e1a0a003    446 	mov	r10,r3

00000234 eb000000*   447 	bl	BerEncoder_determineFullObjectSize

00000238 e1a06000    448 	mov	r6,r0

0000023c e59a0000    449 	ldr	r0,[r10]

00000240 eb000000*   450 	bl	BerEncoder_determineFullObjectSize

00000244 e0806006    451 	add	r6,r0,r6

                     452 ;155:             + BerEncoder_determineFullObjectSize(itemId->len);


                     453 ;156:     nameObjLen = BerEncoder_determineFullObjectSize(domainSpecificLen);


                     454 

00000248 e1a00006    455 	mov	r0,r6

0000024c eb000000*   456 	bl	BerEncoder_determineFullObjectSize

                     457 ;157:     listOfVarLen = BerEncoder_determineFullObjectSize(nameObjLen);


                     458 

00000250 e1a08000    459 	mov	r8,r0

00000254 eb000000*   460 	bl	BerEncoder_determineFullObjectSize

                     461 ;158:     varAccessSpec = BerEncoder_determineFullObjectSize(listOfVarLen);


                     462 

00000258 e1a0b000    463 	mov	fp,r0

0000025c eb000000*   464 	bl	BerEncoder_determineFullObjectSize

00000260 e595c060    465 	ldr	r12,[r5,96]

00000264 e1a0100d    466 	mov	r1,sp

00000268 e1a09000    467 	mov	r9,r0

                     468 ;159: 


                     469 ;160:     //Control object


                     470 ;161:     if(!cntrlObj->calcReadLen(cntrlObj, &cntrlObjLen))


                     471 

0000026c e1a00005    472 	mov	r0,r5

00000270 e1a0e00f    473 	mov	lr,pc

00000274 e12fff1c*   474 	bx	r12

00000278 e3500000    475 	cmp	r0,0

0000027c 0a000028    476 	beq	.L313

                     477 ;162:     {


                     478 

                     479 ;163:         return false;


                     480 

                     481 ;164:     }


                     482 ;165: 


                     483 ;166:     //===================Кодируем==================


                     484 ;167:     //List of varibles (0xA0)


                     485 ;168:     if(!BufferView_encodeTL(wrBuf, 0xA0, varAccessSpec))


                     486 

00000280 e1a02009    487 	mov	r2,r9

00000284 e1a00004    488 	mov	r0,r4

00000288 e3a010a0    489 	mov	r1,160

0000028c eb000000*   490 	bl	BufferView_encodeTL

00000290 e3500000    491 	cmp	r0,0

00000294 0a000022    492 	beq	.L313

                     493 ;169:     {


                     494 

                     495 ;170:         return false;


                     496 

                     497 ;171:     }


                     498 ;172:     //Имя всегда в тэге SEQUENCE (0x30)


                     499 ;173:     if(!BufferView_encodeTL(wrBuf, ASN_SEQUENCE, listOfVarLen))


                     500 

00000298 e1a0200b    501 	mov	r2,fp

0000029c e1a00004    502 	mov	r0,r4

000002a0 e3a01030    503 	mov	r1,48

000002a4 eb000000*   504 	bl	BufferView_encodeTL

000002a8 e3500000    505 	cmp	r0,0

000002ac 0a00001c    506 	beq	.L313


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
                     507 ;174:     {


                     508 

                     509 ;175:         return false;


                     510 

                     511 ;176:     }


                     512 ;177:     //Name(0xA0)


                     513 ;178:     if(!BufferView_encodeTL(wrBuf, 0xA0, nameObjLen))


                     514 

000002b0 e1a02008    515 	mov	r2,r8

000002b4 e1a00004    516 	mov	r0,r4

000002b8 e3a010a0    517 	mov	r1,160

000002bc eb000000*   518 	bl	BufferView_encodeTL

000002c0 e3500000    519 	cmp	r0,0

000002c4 0a000016    520 	beq	.L313

                     521 ;179:     {


                     522 

                     523 ;180:         return false;


                     524 

                     525 ;181:     }


                     526 ;182:     //Domain specific(0xA1)


                     527 ;183:     if(!BufferView_encodeTL(wrBuf, 0xA1, domainSpecificLen))


                     528 

000002c8 e1a02006    529 	mov	r2,r6

000002cc e1a00004    530 	mov	r0,r4

000002d0 e3a010a1    531 	mov	r1,161

000002d4 eb000000*   532 	bl	BufferView_encodeTL

000002d8 e3500000    533 	cmp	r0,0

000002dc 0a000010    534 	beq	.L313

                     535 ;184:     {


                     536 

                     537 ;185:         return false;


                     538 

                     539 ;186:     }


                     540 ;187:     //Domain ID


                     541 ;188:     if(!BufferView_encodeStringView(wrBuf, ASN_VISIBLE_STRING, domainId))


                     542 

000002e0 e1a02007    543 	mov	r2,r7

000002e4 e1a00004    544 	mov	r0,r4

000002e8 e3a0101a    545 	mov	r1,26

000002ec eb000000*   546 	bl	BufferView_encodeStringView

000002f0 e3500000    547 	cmp	r0,0

000002f4 0a00000a    548 	beq	.L313

                     549 ;189:     {


                     550 

                     551 ;190:         return false;


                     552 

                     553 ;191:     }


                     554 ;192:     //Item ID


                     555 ;193:     if(!BufferView_encodeStringView(wrBuf, ASN_VISIBLE_STRING, itemId))


                     556 

000002f8 e1a0200a    557 	mov	r2,r10

000002fc e1a00004    558 	mov	r0,r4

00000300 e3a0101a    559 	mov	r1,26

00000304 eb000000*   560 	bl	BufferView_encodeStringView

00000308 e3500000    561 	cmp	r0,0

0000030c 0a000004    562 	beq	.L313

                     563 ;194:     {


                     564 

                     565 ;195:         return false;


                     566 

                     567 ;196:     }



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
                     568 ;197: 


                     569 ;198:     // Кодируем List of access result (0xA0)


                     570 ;199:     if(!BufferView_encodeTL(wrBuf, 0xA0, cntrlObjLen))


                     571 

00000310 e59d2000    572 	ldr	r2,[sp]

00000314 e1a00004    573 	mov	r0,r4

00000318 e3a010a0    574 	mov	r1,160

0000031c eb000000*   575 	bl	BufferView_encodeTL

00000320 e3500000    576 	cmp	r0,0

                     577 .L313:

                     578 ;200:     {


                     579 

                     580 ;201:         return false;


                     581 

00000324 03a00000    582 	moveq	r0,0

00000328 0a000004    583 	beq	.L289

                     584 .L312:

                     585 ;202:     }


                     586 ;203:     //Control object


                     587 ;204:     return cntrlObj->encodeRead(cntrlObj, wrBuf);


                     588 

0000032c e595c05c    589 	ldr	r12,[r5,92]

00000330 e1a01004    590 	mov	r1,r4

00000334 e1a00005    591 	mov	r0,r5

00000338 e1a0e00f    592 	mov	lr,pc

0000033c e12fff1c*   593 	bx	r12

                     594 .L289:

00000340 e28dd00c    595 	add	sp,sp,12

00000344 e8bd8ff0    596 	ldmfd	[sp]!,{r4-fp,pc}

                     597 	.endf	InfoReport_createPositiveCmdTermReport

                     598 	.align	4

                     599 ;cntrlObjLen	[sp]	local

                     600 ;domainSpecificLen	r6	local

                     601 ;nameObjLen	r8	local

                     602 ;listOfVarLen	fp	local

                     603 ;varAccessSpec	r9	local

                     604 

                     605 ;cntrlObj	r5	param

                     606 ;wrBuf	r4	param

                     607 ;domainId	r7	param

                     608 ;itemId	r10	param

                     609 

                     610 	.section ".bss","awb"

                     611 .L436:

                     612 	.data

                     613 	.text

                     614 

                     615 ;205: }


                     616 

                     617 ;206: 


                     618 ;207: bool InfoReport_createNegativeCmdTermReport( IEDEntity ctrlObj,


                     619 	.align	4

                     620 	.align	4

                     621 InfoReport_createNegativeCmdTermReport::

00000348 e92d4ff0    622 	stmfd	[sp]!,{r4-fp,lr}

                     623 ;208:     BufferView* wrBuf, StringView* domainId, StringView* itemId, uint8_t addCause)


                     624 ;209: {


                     625 

                     626 ;210:     LastApplErrorSizes errSizes;


                     627 ;211:     StringView ctrlObjParentFullName;


                     628 ;212:     BufferView ctrlObjParentNameBuf;



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
                     629 ;213:     int32_t orCat;


                     630 ;214:     StringView orIdent;


                     631 ;215:     size_t ctrlObjLen;


                     632 ;216:     size_t errNameLen;


                     633 ;217:     size_t errNameVmdSpecificLen;


                     634 ;218:     size_t errNameStrucLen;


                     635 ;219:     size_t operDomainSpecificLen;


                     636 ;220:     size_t operVarSpecName;


                     637 ;221:     size_t operVarNameStrucLen;


                     638 ;222:     size_t allVarSpec;


                     639 ;223:     size_t operNameLen;


                     640 ;224:     uint8_t error = 0;


                     641 

                     642 ;225:     uint8_t ctlNum = 0;


                     643 

                     644 ;226: 


                     645 ;227: 


                     646 ;228: 


                     647 ;229: 


                     648 ;230:     //Получаем полное имя родителя


                     649 ;231:     BufferView_init(&ctrlObjParentNameBuf, ctrlObjNameBuf,


                     650 

0000034c e24dd068    651 	sub	sp,sp,104

00000350 e5dda08c    652 	ldrb	r10,[sp,140]

00000354 e1a05000    653 	mov	r5,r0

00000358 e1a04001    654 	mov	r4,r1

0000035c e59f12f8*   655 	ldr	r1,.L754

00000360 e28d0034    656 	add	r0,sp,52

00000364 e1a07003    657 	mov	r7,r3

00000368 e3a03000    658 	mov	r3,0

0000036c e1a06002    659 	mov	r6,r2

00000370 e3a02081    660 	mov	r2,129

00000374 eb000000*   661 	bl	BufferView_init

                     662 ;232:                     sizeof(ctrlObjNameBuf), 0);


                     663 ;233:     if(!IEDEntity_getFullName(ctrlObj->parent, &ctrlObjParentNameBuf ))


                     664 

00000378 e5950000    665 	ldr	r0,[r5]

0000037c e28d1034    666 	add	r1,sp,52

00000380 eb000000*   667 	bl	IEDEntity_getFullName

00000384 e3500000    668 	cmp	r0,0

00000388 0a000081    669 	beq	.L509

                     670 ;234:     {


                     671 

                     672 ;235:         ERROR_REPORT("Unable to get full object name");


                     673 ;236:         return false;


                     674 

                     675 ;237:     }


                     676 ;238:     StringView_init(&ctrlObjParentFullName, (char*)ctrlObjParentNameBuf.p,


                     677 

0000038c e59d2038    678 	ldr	r2,[sp,56]

00000390 e59d1034    679 	ldr	r1,[sp,52]

00000394 e28d0040    680 	add	r0,sp,64

00000398 eb000000*   681 	bl	StringView_init

                     682 ;239:                     ctrlObjParentNameBuf.pos);


                     683 ;240: 


                     684 ;241: 


                     685 ;242:     if(!IEDControlDA_getOrCat(ctrlObj, &orCat))


                     686 

0000039c e28d1010    687 	add	r1,sp,16

000003a0 e1a00005    688 	mov	r0,r5

000003a4 eb000000*   689 	bl	IEDControlDA_getOrCat


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
000003a8 e3500000    690 	cmp	r0,0

000003ac 0a000078    691 	beq	.L509

                     692 ;243:     {


                     693 

                     694 ;244:         ERROR_REPORT("Unable to get orCat");


                     695 ;245:         return false;


                     696 

                     697 ;246:     }


                     698 ;247: 


                     699 ;248:     if(!IEDControlDA_getOrIdent(ctrlObj, &orIdent))


                     700 

000003b0 e28d102c    701 	add	r1,sp,44

000003b4 e1a00005    702 	mov	r0,r5

000003b8 eb000000*   703 	bl	IEDControlDA_getOrIdent

000003bc e3500000    704 	cmp	r0,0

000003c0 0a000073    705 	beq	.L509

                     706 ;249:     {


                     707 

                     708 ;250:         ERROR_REPORT("Unable to get orIdent");


                     709 ;251:         return false;


                     710 

                     711 ;252:     }


                     712 ;253: 


                     713 ;254:     //===============Вычисляем размеры=============


                     714 ;255:     //Variable acess specification (0xA0)


                     715 ;256:     //LastApplError


                     716 ;257:     errNameLen = BerEncoder_determineFullObjectSize(strlen(lastApplErrorName));


                     717 

000003c4 e59f1294*   718 	ldr	r1,.L755

000003c8 e5910000    719 	ldr	r0,[r1]

000003cc eb000000*   720 	bl	strlen

000003d0 eb000000*   721 	bl	BerEncoder_determineFullObjectSize

                     722 ;258:     errNameVmdSpecificLen = BerEncoder_determineFullObjectSize(errNameLen);


                     723 

000003d4 e58d0024    724 	str	r0,[sp,36]

000003d8 eb000000*   725 	bl	BerEncoder_determineFullObjectSize

                     726 ;259:     errNameStrucLen = BerEncoder_determineFullObjectSize(errNameVmdSpecificLen);


                     727 

000003dc e58d0028    728 	str	r0,[sp,40]

000003e0 eb000000*   729 	bl	BerEncoder_determineFullObjectSize

000003e4 e1a08000    730 	mov	r8,r0

                     731 ;260: 


                     732 ;261:     //Oper


                     733 ;262:     operNameLen = BerEncoder_determineFullObjectSize(domainId->len)


                     734 

000003e8 e5960000    735 	ldr	r0,[r6]

000003ec eb000000*   736 	bl	BerEncoder_determineFullObjectSize

000003f0 e1a0b000    737 	mov	fp,r0

000003f4 e5970000    738 	ldr	r0,[r7]

000003f8 eb000000*   739 	bl	BerEncoder_determineFullObjectSize

000003fc e080b00b    740 	add	fp,r0,fp

                     741 ;263:             + BerEncoder_determineFullObjectSize(itemId->len);


                     742 ;264:     operDomainSpecificLen = BerEncoder_determineFullObjectSize(operNameLen);


                     743 

00000400 e1a0000b    744 	mov	r0,fp

00000404 eb000000*   745 	bl	BerEncoder_determineFullObjectSize

                     746 ;265:     operVarSpecName = BerEncoder_determineFullObjectSize(operDomainSpecificLen);


                     747 

00000408 e1a09000    748 	mov	r9,r0

0000040c eb000000*   749 	bl	BerEncoder_determineFullObjectSize

                     750 ;266:     operVarNameStrucLen = BerEncoder_determineFullObjectSize(operVarSpecName);



                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
                     751 

00000410 e58d0020    752 	str	r0,[sp,32]

00000414 eb000000*   753 	bl	BerEncoder_determineFullObjectSize

                     754 ;267: 


                     755 ;268:     //allVarSpec = BerEncoder_determineFullObjectSize(errNameStrucLen + operVarNameStrucLen);


                     756 ;269:     allVarSpec = errNameStrucLen + operVarNameStrucLen;


                     757 

00000418 e3a02000    758 	mov	r2,0

0000041c e0808008    759 	add	r8,r0,r8

                     760 ;270: 


                     761 ;271:     //Размер данных


                     762 ;272:     //LastApplError


                     763 ;273:     calcLastApplErrSizes(&errSizes, &ctrlObjParentFullName, error, orCat, &orIdent,


                     764 

00000420 e28d002c    765 	add	r0,sp,44

00000424 e88d0405    766 	stmea	[sp],{r0,r2,r10}

00000428 e5dd3010    767 	ldrb	r3,[sp,16]

0000042c e28d1040    768 	add	r1,sp,64

00000430 e28d0048    769 	add	r0,sp,72

00000434 ebfffef1*   770 	bl	calcLastApplErrSizes

                     771 ;274:                          ctlNum, addCause);


                     772 ;275: 


                     773 ;276:     //Control object


                     774 ;277:     if(!ctrlObj->calcReadLen(ctrlObj, &ctrlObjLen))


                     775 

00000438 e595c060    776 	ldr	r12,[r5,96]

0000043c e28d1014    777 	add	r1,sp,20

00000440 e1a00005    778 	mov	r0,r5

00000444 e1a0e00f    779 	mov	lr,pc

00000448 e12fff1c*   780 	bx	r12

0000044c e3500000    781 	cmp	r0,0

00000450 0a00004f    782 	beq	.L509

                     783 ;278:     {


                     784 

                     785 ;279:         return false;


                     786 

                     787 ;280:     }


                     788 ;281: 


                     789 ;282:     //===================Кодируем==================


                     790 ;283:     //List of varibles (0xA0)


                     791 ;284:     if(!BufferView_encodeTL(wrBuf, 0xA0, allVarSpec))


                     792 

00000454 e1a02008    793 	mov	r2,r8

00000458 e1a00004    794 	mov	r0,r4

0000045c e3a010a0    795 	mov	r1,160

00000460 eb000000*   796 	bl	BufferView_encodeTL

00000464 e3500000    797 	cmp	r0,0

00000468 0a000049    798 	beq	.L509

                     799 ;285:     {


                     800 

                     801 ;286:         return false;


                     802 

                     803 ;287:     }


                     804 ;288:     //LastApplError


                     805 ;289:     //Имя всегда в тэге SEQUENCE (0x30)


                     806 ;290:     if(!BufferView_encodeTL(wrBuf, ASN_SEQUENCE, errNameVmdSpecificLen))


                     807 

0000046c e59d2028    808 	ldr	r2,[sp,40]

00000470 e1a00004    809 	mov	r0,r4

00000474 e3a01030    810 	mov	r1,48

00000478 eb000000*   811 	bl	BufferView_encodeTL


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
0000047c e3500000    812 	cmp	r0,0

00000480 0a000043    813 	beq	.L509

                     814 ;291:     {


                     815 

                     816 ;292:         return false;


                     817 

                     818 ;293:     }


                     819 ;294:     //0xA0


                     820 ;295:     if(!BufferView_encodeTL(wrBuf, 0xA0, errNameLen ))


                     821 

00000484 e59d2024    822 	ldr	r2,[sp,36]

00000488 e1a00004    823 	mov	r0,r4

0000048c e3a010a0    824 	mov	r1,160

00000490 eb000000*   825 	bl	BufferView_encodeTL

00000494 e3500000    826 	cmp	r0,0

00000498 0a00003d    827 	beq	.L509

                     828 ;296:     {


                     829 

                     830 ;297:         return false;


                     831 

                     832 ;298:     }


                     833 ;299:     //0x80


                     834 ;300:     if(!BufferView_encodeStr(wrBuf, 0x80 ,lastApplErrorName))


                     835 

0000049c e59f01bc*   836 	ldr	r0,.L755

000004a0 e3a01080    837 	mov	r1,128

000004a4 e5902000    838 	ldr	r2,[r0]

000004a8 e1a00004    839 	mov	r0,r4

000004ac eb000000*   840 	bl	BufferView_encodeStr

000004b0 e3500000    841 	cmp	r0,0

000004b4 0a000036    842 	beq	.L509

                     843 ;301:     {


                     844 

                     845 ;302:         return false;


                     846 

                     847 ;303:     }


                     848 ;304: 


                     849 ;305:     //Oper


                     850 ;306:     //Имя всегда в тэге SEQUENCE (0x30)


                     851 ;307:     if(!BufferView_encodeTL(wrBuf, ASN_SEQUENCE, operVarSpecName))


                     852 

000004b8 e59d2020    853 	ldr	r2,[sp,32]

000004bc e1a00004    854 	mov	r0,r4

000004c0 e3a01030    855 	mov	r1,48

000004c4 eb000000*   856 	bl	BufferView_encodeTL

000004c8 e3500000    857 	cmp	r0,0

000004cc 0a000030    858 	beq	.L509

                     859 ;308:     {


                     860 

                     861 ;309:         return false;


                     862 

                     863 ;310:     }


                     864 ;311:     //Name(0xA0)


                     865 ;312:     if(!BufferView_encodeTL(wrBuf, 0xA0, operDomainSpecificLen))


                     866 

000004d0 e1a02009    867 	mov	r2,r9

000004d4 e1a00004    868 	mov	r0,r4

000004d8 e3a010a0    869 	mov	r1,160

000004dc eb000000*   870 	bl	BufferView_encodeTL

000004e0 e3500000    871 	cmp	r0,0

000004e4 0a00002a    872 	beq	.L509


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
                     873 ;313:     {


                     874 

                     875 ;314:         return false;


                     876 

                     877 ;315:     }


                     878 ;316:     //Domain specific(0xA1)


                     879 ;317:     if(!BufferView_encodeTL(wrBuf, 0xA1, operNameLen))


                     880 

000004e8 e1a0200b    881 	mov	r2,fp

000004ec e1a00004    882 	mov	r0,r4

000004f0 e3a010a1    883 	mov	r1,161

000004f4 eb000000*   884 	bl	BufferView_encodeTL

000004f8 e3500000    885 	cmp	r0,0

000004fc 0a000024    886 	beq	.L509

                     887 ;318:     {


                     888 

                     889 ;319:         return false;


                     890 

                     891 ;320:     }


                     892 ;321:     //Domain ID


                     893 ;322:     if(!BufferView_encodeStringView(wrBuf, ASN_VISIBLE_STRING, domainId))


                     894 

00000500 e1a02006    895 	mov	r2,r6

00000504 e1a00004    896 	mov	r0,r4

00000508 e3a0101a    897 	mov	r1,26

0000050c eb000000*   898 	bl	BufferView_encodeStringView

00000510 e3500000    899 	cmp	r0,0

00000514 0a00001e    900 	beq	.L509

                     901 ;323:     {


                     902 

                     903 ;324:         return false;


                     904 

                     905 ;325:     }


                     906 ;326:     //Item ID


                     907 ;327:     if(!BufferView_encodeStringView(wrBuf, ASN_VISIBLE_STRING, itemId))


                     908 

00000518 e1a02007    909 	mov	r2,r7

0000051c e1a00004    910 	mov	r0,r4

00000520 e3a0101a    911 	mov	r1,26

00000524 eb000000*   912 	bl	BufferView_encodeStringView

00000528 e3500000    913 	cmp	r0,0

0000052c 0a000018    914 	beq	.L509

                     915 ;328:     {


                     916 

                     917 ;329:         return false;


                     918 

                     919 ;330:     }


                     920 ;331: 


                     921 ;332: 


                     922 ;333:     // Кодируем List of access result (0xA0)


                     923 ;334:     if(!BufferView_encodeTL(wrBuf, 0xA0, errSizes.fullStruct + ctrlObjLen))


                     924 

00000530 e59d1064    925 	ldr	r1,[sp,100]

00000534 e59d0014    926 	ldr	r0,[sp,20]

00000538 e0802001    927 	add	r2,r0,r1

0000053c e1a00004    928 	mov	r0,r4

00000540 e3a010a0    929 	mov	r1,160

00000544 eb000000*   930 	bl	BufferView_encodeTL

00000548 e3500000    931 	cmp	r0,0

0000054c 0a000010    932 	beq	.L509

                     933 ;335:     {



                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
                     934 

                     935 ;336:         return false;


                     936 

                     937 ;337:     }


                     938 ;338: 


                     939 ;339:     //LastApplError


                     940 ;340:     if(!encodeLastApplErrErr(wrBuf, &errSizes,&ctrlObjParentFullName, error, orCat,


                     941 

00000550 e3a03000    942 	mov	r3,0

00000554 e5dd0010    943 	ldrb	r0,[sp,16]

00000558 e28d102c    944 	add	r1,sp,44

0000055c e88d040b    945 	stmea	[sp],{r0-r1,r3,r10}

00000560 e28d2040    946 	add	r2,sp,64

00000564 e28d1048    947 	add	r1,sp,72

00000568 e1a00004    948 	mov	r0,r4

0000056c ebfffeca*   949 	bl	encodeLastApplErrErr

00000570 e3500000    950 	cmp	r0,0

00000574 0a000006    951 	beq	.L509

                     952 ;341:                              &orIdent, ctlNum, addCause))


                     953 ;342:     {


                     954 

                     955 ;343:         return false;


                     956 

                     957 ;344:     }


                     958 ;345:     //Oper


                     959 ;346:     if(!ctrlObj->encodeRead(ctrlObj, wrBuf))


                     960 

00000578 e595c05c    961 	ldr	r12,[r5,92]

0000057c e1a01004    962 	mov	r1,r4

00000580 e1a00005    963 	mov	r0,r5

00000584 e1a0e00f    964 	mov	lr,pc

00000588 e12fff1c*   965 	bx	r12

0000058c e3500000    966 	cmp	r0,0

                     967 ;349:     }


                     968 ;350:     return true;


                     969 

00000590 13a00001    970 	movne	r0,1

                     971 .L509:

                     972 ;347:     {


                     973 

                     974 ;348:         return false;


                     975 

00000594 03a00000    976 	moveq	r0,0

                     977 .L461:

00000598 e28dd068    978 	add	sp,sp,104

0000059c e8bd8ff0    979 	ldmfd	[sp]!,{r4-fp,pc}

                     980 	.endf	InfoReport_createNegativeCmdTermReport

                     981 	.align	4

                     982 ;errSizes	[sp,72]	local

                     983 ;ctrlObjParentFullName	[sp,64]	local

                     984 ;ctrlObjParentNameBuf	[sp,52]	local

                     985 ;orCat	[sp,16]	local

                     986 ;orIdent	[sp,44]	local

                     987 ;ctrlObjLen	[sp,20]	local

                     988 ;errNameLen	[sp,36]	local

                     989 ;errNameVmdSpecificLen	[sp,40]	local

                     990 ;errNameStrucLen	r8	local

                     991 ;operDomainSpecificLen	r9	local

                     992 ;operVarSpecName	[sp,32]	local

                     993 ;allVarSpec	r8	local

                     994 ;operNameLen	fp	local


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
                     995 

                     996 ;ctrlObj	r5	param

                     997 ;wrBuf	r4	param

                     998 ;domainId	r6	param

                     999 ;itemId	r7	param

                    1000 ;addCause	r10	param

                    1001 

                    1002 	.data

                    1003 	.text

                    1004 

                    1005 ;351: }


                    1006 

                    1007 ;352: 


                    1008 ;353: 


                    1009 ;354: //Оформляет и отправляет отчёт


                    1010 ;355: void InfoReport_send(IsoConnection* isoConn, uint8_t * infoReport,


                    1011 	.align	4

                    1012 	.align	4

                    1013 InfoReport_send::

000005a0 e92d4cf2   1014 	stmfd	[sp]!,{r1,r4-r7,r10-fp,lr}

                    1015 ;356:                      size_t byteCount, uint8_t* reportMmsBuf,


                    1016 ;357:                      uint8_t* reportPresentationBuf)


                    1017 ;358: {


                    1018 

                    1019 ;359:     int sessionDataLen;


                    1020 ;360:     int presentationDataLen;


                    1021 ;361:     int bufPos = 0;


                    1022 

                    1023 ;362:     int reportLen;


                    1024 ;363: 


                    1025 ;364:     SessionOutBuffer* sessionOutBuf = allocSessionOutBuffer(


                    1026 

000005a4 e1a04002   1027 	mov	r4,r2

000005a8 e1a06003   1028 	mov	r6,r3

000005ac e59da020   1029 	ldr	r10,[sp,32]

000005b0 e1a07000   1030 	mov	r7,r0

000005b4 e2870e80   1031 	add	r0,r7,1<<11

000005b8 e2800018   1032 	add	r0,r0,24

000005bc e3a01c60   1033 	mov	r1,3<<13

000005c0 eb000000*  1034 	bl	allocSessionOutBuffer

000005c4 e1b05000   1035 	movs	r5,r0

                    1036 ;365:         &isoConn->outBuffers, SESSION_OUT_BUF_SIZE);


                    1037 ;366:     if (!sessionOutBuf)


                    1038 

000005c8 0a000021   1039 	beq	.L756

                    1040 ;367:     {


                    1041 

                    1042 ;368:         ERROR_REPORT("Unable to allocate buffer for the report");


                    1043 ;369:         return;


                    1044 

                    1045 ;370:     }


                    1046 ;371: 


                    1047 ;372:     //Определяем длины


                    1048 ;373: 


                    1049 ;374:     //A0 Information report


                    1050 ;375:     reportLen = 1 +


                    1051 

000005cc e1a00004   1052 	mov	r0,r4

000005d0 eb000000*  1053 	bl	BerEncoder_determineLengthSize

000005d4 e1a02006   1054 	mov	r2,r6

000005d8 e3a03000   1055 	mov	r3,0


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
000005dc e0800004   1056 	add	r0,r0,r4

000005e0 e2801001   1057 	add	r1,r0,1

                    1058 ;376:         BerEncoder_determineLengthSize(byteCount) + byteCount;


                    1059 ;377: 


                    1060 ;378:     // Кодируем


                    1061 ;379:     //Unconfirmed PDU


                    1062 ;380:     bufPos = BerEncoder_encodeTL(0xA3, reportLen, reportMmsBuf, bufPos);


                    1063 

000005e4 e3a000a3   1064 	mov	r0,163

000005e8 eb000000*  1065 	bl	BerEncoder_encodeTL

                    1066 ;381:     bufPos = BerEncoder_encodeTL(0xA0, byteCount, reportMmsBuf, bufPos);


                    1067 

000005ec e1a02006   1068 	mov	r2,r6

000005f0 e1a01004   1069 	mov	r1,r4

000005f4 e1a03000   1070 	mov	r3,r0

000005f8 e3a000a0   1071 	mov	r0,160

000005fc eb000000*  1072 	bl	BerEncoder_encodeTL

00000600 e1a02004   1073 	mov	r2,r4

00000604 e1a0b000   1074 	mov	fp,r0

                    1075 ;382:     memcpy(reportMmsBuf + bufPos, infoReport, byteCount);


                    1076 

00000608 e59d1000   1077 	ldr	r1,[sp]

0000060c e08b0006   1078 	add	r0,fp,r6

00000610 eb000000*  1079 	bl	memcpy

                    1080 ;383:     bufPos += byteCount;


                    1081 

00000614 e08b3004   1082 	add	r3,fp,r4

                    1083 ;384:     byteCount = bufPos;


                    1084 

                    1085 ;385: 


                    1086 ;386:     presentationDataLen = IsoPresentation_createUserData(&isoConn->presentation,


                    1087 

00000618 e1a02006   1088 	mov	r2,r6

0000061c e1a0100a   1089 	mov	r1,r10

00000620 e2870edb   1090 	add	r0,r7,0x0db0

00000624 e2800bf0   1091 	add	r0,r0,15<<14

00000628 eb000000*  1092 	bl	IsoPresentation_createUserData

                    1093 ;387:         reportPresentationBuf, reportMmsBuf, byteCount);


                    1094 ;388: 


                    1095 ;389:     sessionDataLen = isoSession_createDataSpdu(sessionOutBuf->cotpOutBuf,


                    1096 

0000062c e1a0200a   1097 	mov	r2,r10

00000630 e1a03000   1098 	mov	r3,r0

00000634 e2850008   1099 	add	r0,r5,8

00000638 e3a01c60   1100 	mov	r1,3<<13

0000063c eb000000*  1101 	bl	isoSession_createDataSpdu

                    1102 ;390:         SESSION_OUT_BUF_SIZE, reportPresentationBuf, presentationDataLen);


                    1103 ;391:     sessionOutBuf->byteCount = sessionDataLen;


                    1104 

00000640 e1a01005   1105 	mov	r1,r5

00000644 e5850004   1106 	str	r0,[r5,4]

                    1107 ;392:     if (!OutQueue_insert(&isoConn->outQueue, sessionOutBuf))


                    1108 

00000648 e2870bf2   1109 	add	r0,r7,242<<10

0000064c e280006c   1110 	add	r0,r0,108

00000650 eb000000*  1111 	bl	OutQueue_insert

                    1112 .L756:

                    1113 ;393:     {


                    1114 

                    1115 ;394:         ERROR_REPORT("Out queue overflow");


                    1116 ;395:         return;



                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
                    1117 

00000654 e8bd8cf2   1118 	ldmfd	[sp]!,{r1,r4-r7,r10-fp,pc}

                    1119 	.endf	InfoReport_send

                    1120 	.align	4

                    1121 ;bufPos	fp	local

                    1122 ;sessionOutBuf	r5	local

                    1123 

                    1124 ;isoConn	r7	param

                    1125 ;infoReport	[sp]	param

                    1126 ;byteCount	r4	param

                    1127 ;reportMmsBuf	r6	param

                    1128 ;reportPresentationBuf	r10	param

                    1129 

                    1130 	.section ".bss","awb"

                    1131 .L792:

                    1132 	.data

                    1133 	.text

                    1134 

                    1135 ;396:     }


                    1136 ;397: }


                    1137 	.align	4

                    1138 .L288:

00000658 00000000*  1139 	.data.w	applErrorVarSpec

                    1140 	.type	.L288,$object

                    1141 	.size	.L288,4

                    1142 

                    1143 .L754:

0000065c 00000000*  1144 	.data.w	ctrlObjNameBuf

                    1145 	.type	.L754,$object

                    1146 	.size	.L754,4

                    1147 

                    1148 .L755:

00000660 00000000*  1149 	.data.w	lastApplErrorName

                    1150 	.type	.L755,$object

                    1151 	.size	.L755,4

                    1152 

                    1153 	.align	4

                    1154 ;ctrlObjNameBuf	ctrlObjNameBuf	static

                    1155 ;.L801	.L275	static

                    1156 

                    1157 	.data

                    1158 .L805:

                    1159 	.globl	applErrorVarSpec

00000000 113013a0   1160 applErrorVarSpec:	.data.b	160,19,48,17

00000004 0d800fa0   1161 	.data.b	160,15,128,13

00000008 7473614c   1162 	.data.b	76,97,115,116

0000000c 6c707041   1163 	.data.b	65,112,112,108

00000010 6f727245   1164 	.data.b	69,114,114,111

00000014 72        1165 	.data.b	114

00000015 000000    1166 	.space	3

                    1167 	.type	applErrorVarSpec,$object

                    1168 	.size	applErrorVarSpec,24

                    1169 .L806:

                    1170 	.globl	lastApplErrorName

00000018 00000000*  1171 lastApplErrorName:	.data.w	.L275

                    1172 	.type	lastApplErrorName,$object

                    1173 	.size	lastApplErrorName,4

                    1174 	.ghsnote version,6

                    1175 	.ghsnote tools,3

                    1176 	.ghsnote options,0

                    1177 	.text


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6ss1.s
                    1178 	.align	4

                    1179 	.data

                    1180 	.align	4

                    1181 	.section ".bss","awb"

                    1182 	.align	4

                    1183 	.section ".rodata","a"

                    1184 	.align	4

                    1185 	.text

