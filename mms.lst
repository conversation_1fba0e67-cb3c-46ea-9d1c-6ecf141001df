                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms.c -o gh_atk1.o -list=mms.lst C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
Source File: mms.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile mms.c -o mms.o

                      11 ;Source File:   mms.c

                      12 ;Directory:     

                      13 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      14 ;Compile Date:  Mon Jul 28 12:31:02 2025

                      15 ;Host OS:       Win32

                      16 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      17 ;Release:       MULTI v4.2.3

                      18 ;Revision Date: Wed Mar 29 05:25:47 2006

                      19 ;Release Date:  Fri Mar 31 10:02:14 2006

                      20 

                      21 ;1: 


                      22 ;2: #include "stddef.h"


                      23 ;3: #include "server.h"


                      24 ;4: #include "platform_thread.h"


                      25 ;5: #include "Cotp.h"


                      26 ;6: #include "acse.h"


                      27 ;7: #include "session.h"


                      28 ;8: #include "mms.h"


                      29 ;9: #include "send_thread.h"


                      30 ;10: #include "AsnEncoding.h"  


                      31 ;11: #include "mms_get_name_list.h"


                      32 ;12: #include "mms_get_variable_access_attributes.h"


                      33 ;13: #include "mmsservices.h"


                      34 ;14: #include "mms_read.h"


                      35 ;15: #include "file_system.h"


                      36 ;16: #include "out_queue.h"


                      37 ;17: #include "out_buffers.h"


                      38 ;18: #include "reports.h"


                      39 ;19: #include "connections.h"


                      40 ;20: #include "control.h"


                      41 ;21: #include <debug.h>


                      42 ;22: #include <Clib.h>


                      43 ;23: 


                      44 ;24: //Типы тегов Ассоциации MMS


                      45 ;25: #define	MMS_LOCAL_DETAIL_CALLING	 0x80


                      46 ;26: #define MMS_MAX_SERV_OUTSTANDING_CALLING 0x81


                      47 ;27: #define MMS_MAX_SERV_OUTSTANDING_CALLED 0x82


                      48 ;28: #define MMS_DATA_STRUCTURE_NESTING_LEVEL 0x83


                      49 ;29: #define	MMS_INIT_REQUEST_DETAIL 0xa4


                      50 ;30: #define	MMS_INIT_RESPONSE_DETAIL 0xa4



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
                      51 ;31: #define MMS_VERSION_NUMBER 0x80


                      52 ;32: #define MMS_PARAMETER_CBB 0x81


                      53 ;33: #define MMS_SERVICES_SUPPORTED 0x82


                      54 ;34: 


                      55 ;35: /**********************************************************************************************


                      56 ;36:  * MMS Server Capabilities


                      57 ;37:  *********************************************************************************************/


                      58 ;38: 


                      59 ;39: #define MMS_SERVICE_STATUS 0x80


                      60 ;40: #define MMS_SERVICE_GET_NAME_LIST 0x40


                      61 ;41: #define MMS_SERVICE_IDENTIFY 0x20


                      62 ;42: #define MMS_SERVICE_RENAME 0x10


                      63 ;43: #define MMS_SERVICE_READ 0x08


                      64 ;44: #define MMS_SERVICE_WRITE 0x04


                      65 ;45: #define MMS_SERVICE_GET_VARIABLE_ACCESS_ATTRIBUTES 0x02


                      66 ;46: #define MMS_SERVICE_DEFINE_NAMED_VARIABLE 0x01


                      67 ;47: 


                      68 ;48: #define MMS_SERVICE_DEFINE_SCATTERED_ACCESS 0x80


                      69 ;49: #define MMS_SERVICE_GET_SCATTERED_ACCESS_ATTRIBUTES 0x40


                      70 ;50: #define MMS_SERVICE_DELETE_VARIABLE_ACCESS 0x20


                      71 ;51: #define MMS_SERVICE_DEFINE_NAMED_VARIABLE_LIST 0x10


                      72 ;52: #define MMS_SERVICE_GET_NAMED_VARIABLE_LIST_ATTRIBUTES 0x08


                      73 ;53: #define MMS_SERVICE_DELETE_NAMED_VARIABLE_LIST 0x04


                      74 ;54: #define MMS_SERVICE_DEFINE_NAMED_TYPE 0x02


                      75 ;55: #define MMS_SERVICE_GET_NAMED_TYPE_ATTRIBUTES 0x01


                      76 ;56: 


                      77 ;57: #define MMS_SERVICE_OBTAIN_FILE 0x02


                      78 ;58: 


                      79 ;59: #define MMS_SERVICE_READ_JOURNAL 0x40


                      80 ;60: 


                      81 ;61: #define MMS_SERVICE_FILE_OPEN 0x80


                      82 ;62: #define MMS_SERVICE_FILE_READ 0x40


                      83 ;63: #define MMS_SERVICE_FILE_CLOSE 0x20


                      84 ;64: #define MMS_SERVICE_FILE_RENAME 0x01


                      85 ;65: #define MMS_SERVICE_FILE_DELETE 0x08


                      86 ;66: #define MMS_SERVICE_FILE_DIRECTORY 0x04


                      87 ;67: #define MMS_SERVICE_UNSOLICITED_STATUS 0x02


                      88 ;68: #define MMS_SERVICE_INFORMATION_REPORT 0x01


                      89 ;69: 


                      90 ;70: #define MMS_SERVICE_CONCLUDE 0x10


                      91 ;71: #define MMS_SERVICE_CANCEL 0x08


                      92 ;72: 


                      93 ;73: 


                      94 ;74: // servicesSupported MMS bitstring


                      95 ;75: static unsigned char servicesSupported[] =


                      96 ;76: {


                      97 ;77:         0x00


                      98 ;78:         | MMS_SERVICE_STATUS


                      99 ;79:         | MMS_SERVICE_GET_NAME_LIST    


                     100 ;80:         | MMS_SERVICE_IDENTIFY


                     101 ;81:         | MMS_SERVICE_READ


                     102 ;82:         //| MMS_SERVICE_WRITE


                     103 ;83:         | MMS_SERVICE_GET_VARIABLE_ACCESS_ATTRIBUTES


                     104 ;84:         ,


                     105 ;85:         0x00


                     106 ;86:         //| MMS_SERVICE_DEFINE_NAMED_VARIABLE_LIST


                     107 ;87:         //| MMS_SERVICE_DELETE_NAMED_VARIABLE_LIST


                     108 ;88:         //| MMS_SERVICE_GET_NAMED_VARIABLE_LIST_ATTRIBUTES


                     109 ;89:         ,


                     110 ;90:         0x00,


                     111 ;91:         0x00,



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
                     112 ;92:         0x00,


                     113 ;93:         0x00


                     114 ;94:         //| MMS_SERVICE_OBTAIN_FILE


                     115 ;95:         ,


                     116 ;96:         0x00,


                     117 ;97:         0x00,


                     118 ;98:         0x00


                     119 ;99:         //| MMS_SERVICE_READ_JOURNAL


                     120 ;100:         ,


                     121 ;101:         0x00


                     122 ;102:         


                     123 ;103:         | MMS_SERVICE_FILE_OPEN


                     124 ;104:         | MMS_SERVICE_FILE_READ


                     125 ;105:         | MMS_SERVICE_FILE_CLOSE


                     126 ;106:         //| MMS_SERVICE_FILE_RENAME


                     127 ;107:         //| MMS_SERVICE_FILE_DELETE


                     128 ;108:         | MMS_SERVICE_FILE_DIRECTORY


                     129 ;109:         


                     130 ;110:         | MMS_SERVICE_INFORMATION_REPORT


                     131 ;111:         ,


                     132 ;112:         0x00


                     133 ;113:         | MMS_SERVICE_CONCLUDE


                     134 ;114:         | MMS_SERVICE_CANCEL


                     135 ;115: };


                     136 ;116: 


                     137 ;117: /* negotiated parameter CBB */


                     138 ;118: static unsigned char parameterCBB[] =


                     139 ;119: {


                     140 ;120:         0xf1,


                     141 ;121:         0x00


                     142 ;122: };


                     143 ;123: 


                     144 ;124: static int encodeInitResponseDetail(unsigned char* buf, int bufPos, int encode)


                     145 ;125: {


                     146 ;126:     int initResponseDetailSize = 14 + 5 + 3;


                     147 ;127:     if (!encode)


                     148 ;128:         return initResponseDetailSize + 2;


                     149 ;129:     bufPos = BerEncoder_encodeTL(MMS_INIT_RESPONSE_DETAIL, initResponseDetailSize,


                     150 ;130:                                  buf, bufPos);


                     151 ;131:     bufPos = BerEncoder_encodeUInt32WithTL(MMS_VERSION_NUMBER, 1, buf, bufPos);


                     152 ;132:     bufPos = BerEncoder_encodeBitString(MMS_PARAMETER_CBB, 11, parameterCBB,


                     153 ;133:                                         buf, bufPos);


                     154 ;134:     bufPos = BerEncoder_encodeBitString(MMS_SERVICES_SUPPORTED, 85,


                     155 ;135:                                         servicesSupported, buf, bufPos);


                     156 ;136: 


                     157 ;137:     return bufPos;


                     158 ;138: }


                     159 ;139: 


                     160 ;140: static void mms_initConnection(MmsConnection* mmsConn)


                     161 

                     162 ;143: }


                     163 

                     164 ;144: 


                     165 ;145: static void mms_closeConnection(MmsConnection* mmsConn)


                     166 

                     167 ;150: 	}


                     168 ;151: }


                     169 

                     170 ;152: 


                     171 ;153: 


                     172 ;154: static void initIsoConnection(IsoConnection* isoConn)



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
                     173 

                     174 ;166: }


                     175 

                     176 ;167: 


                     177 ;168: static int createInitiateResponse(IsoConnection* isoConn, unsigned char* buf)


                     178 

                     179 ;205: }


                     180 

                     181 	.text

                     182 	.align	4

                     183 encodeInitResponseDetail:

00000000 e92d4030    184 	stmfd	[sp]!,{r4-r5,lr}

00000004 e24dd004    185 	sub	sp,sp,4

00000008 e59f5278*   186 	ldr	r5,.L118

0000000c e3520000    187 	cmp	r2,0

00000010 03a00018    188 	moveq	r0,24

00000014 0a000016    189 	beq	.L69

00000018 e1a04000    190 	mov	r4,r0

0000001c e1a02004    191 	mov	r2,r4

00000020 e1a03001    192 	mov	r3,r1

00000024 e3a01016    193 	mov	r1,22

00000028 e3a000a4    194 	mov	r0,164

0000002c eb000000*   195 	bl	BerEncoder_encodeTL

00000030 e1a02004    196 	mov	r2,r4

00000034 e3a01001    197 	mov	r1,1

00000038 e1a03000    198 	mov	r3,r0

0000003c e3a00080    199 	mov	r0,128

00000040 eb000000*   200 	bl	BerEncoder_encodeUInt32WithTL

00000044 e1a03004    201 	mov	r3,r4

00000048 e1a02005    202 	mov	r2,r5

0000004c e3a0100b    203 	mov	r1,11

00000050 e58d0000    204 	str	r0,[sp]

00000054 e3a00081    205 	mov	r0,129

00000058 eb000000*   206 	bl	BerEncoder_encodeBitString

0000005c e1a03004    207 	mov	r3,r4

00000060 e2852004    208 	add	r2,r5,4

00000064 e3a01055    209 	mov	r1,85

00000068 e58d0000    210 	str	r0,[sp]

0000006c e3a00082    211 	mov	r0,130

00000070 eb000000*   212 	bl	BerEncoder_encodeBitString

                     213 .L69:

00000074 e28dd004    214 	add	sp,sp,4

00000078 e8bd4030    215 	ldmfd	[sp]!,{r4-r5,lr}

0000007c e12fff1e*   216 	ret	

                     217 	.endf	encodeInitResponseDetail

                     218 	.align	4

                     219 

                     220 ;buf	r4	param

                     221 ;bufPos	r1	param

                     222 ;encode	r2	param

                     223 

                     224 	.data

                     225 .L102:

                     226 .L103:

00000000 f1         227 parameterCBB:	.data.b	241

00000001 00         228 	.space	1

                     229 	.type	parameterCBB,$object

                     230 	.size	parameterCBB,2

00000002 0000       231 	.space	2

                     232 .L104:

00000004 ea         233 servicesSupported:	.data.b	234


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
00000005 00         234 	.space	1

00000006 00         235 	.space	1

00000007 00         236 	.space	1

00000008 00         237 	.space	1

00000009 00         238 	.space	1

0000000a 00         239 	.space	1

0000000b 00         240 	.space	1

0000000c 00         241 	.space	1

0000000d 18e5       242 	.data.b	229,24

0000000f 00         243 	.space	1

                     244 	.type	servicesSupported,$object

                     245 	.size	servicesSupported,12

                     246 	.text

                     247 

                     248 

                     249 ;206: 


                     250 ;207: int processSessionConnect(IsoConnection* isoConn)


                     251 	.align	4

                     252 	.align	4

                     253 processSessionConnect::

00000080 e92d4070    254 	stmfd	[sp]!,{r4-r6,lr}

00000084 e1a04000    255 	mov	r4,r0

                     256 ;208: {


                     257 

                     258 ;209:     int acseDataLen;


                     259 ;210:     int presentationDataLen;


                     260 ;211:     int mmsDataLen;


                     261 ;212: 


                     262 ;213: 


                     263 ;214: 


                     264 ;215:     debugSendText("Send Accept SPDU");


                     265 

00000088 e28f0000*   266 	adr	r0,.L165

0000008c e24dd004    267 	sub	sp,sp,4

00000090 eb000000*   268 	bl	debugSendText

                     269 ;216:     mmsDataLen = createInitiateResponse( isoConn, isoConn->isoOutBuf);


                     270 

                     271 ;169: {


                     272 

                     273 ;170:     int bufPos = 0;


                     274 

                     275 ;171:     int initiateResponseLength = 0;


                     276 

                     277 ;172: 


                     278 ;173:     initiateResponseLength += 2 + BerEncoder_UInt32determineEncodedSize(


                     279 

00000094 e594000c    280 	ldr	r0,[r4,12]

00000098 eb000000*   281 	bl	BerEncoder_UInt32determineEncodedSize

0000009c e2805002    282 	add	r5,r0,2

                     283 ;174:                 isoConn->maxPduSize);


                     284 ;175:     initiateResponseLength += 2 + BerEncoder_UInt32determineEncodedSize(


                     285 

000000a0 e5940000    286 	ldr	r0,[r4]

000000a4 eb000000*   287 	bl	BerEncoder_UInt32determineEncodedSize

000000a8 e0800005    288 	add	r0,r0,r5

000000ac e2805002    289 	add	r5,r0,2

                     290 ;176:                 isoConn->maxServOutstandingCalling);


                     291 ;177:     initiateResponseLength += 2 + BerEncoder_UInt32determineEncodedSize(


                     292 

000000b0 e5940004    293 	ldr	r0,[r4,4]

000000b4 eb000000*   294 	bl	BerEncoder_UInt32determineEncodedSize


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
000000b8 e0800005    295 	add	r0,r0,r5

000000bc e2805002    296 	add	r5,r0,2

                     297 ;178:                 isoConn->maxServOutstandingCalled);


                     298 ;179:     initiateResponseLength += 2 + BerEncoder_UInt32determineEncodedSize(


                     299 

000000c0 e5940008    300 	ldr	r0,[r4,8]

000000c4 eb000000*   301 	bl	BerEncoder_UInt32determineEncodedSize

000000c8 e3a02000    302 	mov	r2,0

000000cc e1a01002    303 	mov	r1,r2

000000d0 e0800005    304 	add	r0,r0,r5

000000d4 e2805002    305 	add	r5,r0,2

                     306 ;180:                 isoConn->dataStructureNestingLevel);


                     307 ;181: 


                     308 ;182:     initiateResponseLength += encodeInitResponseDetail(NULL, 0, 0);


                     309 

000000d8 e1a00002    310 	mov	r0,r2

000000dc ebffffc7*   311 	bl	encodeInitResponseDetail

000000e0 e0851000    312 	add	r1,r5,r0

                     313 ;183: 


                     314 ;184:     /* Initiate response pdu */


                     315 ;185:     bufPos = BerEncoder_encodeTL(MMS_INITIATE_RESPONSE_PDU,


                     316 

000000e4 e3a05fd7    317 	mov	r5,0x035c

000000e8 e2855a50    318 	add	r5,r5,5<<16

000000ec e0842005    319 	add	r2,r4,r5

000000f0 e3a03000    320 	mov	r3,0

000000f4 e3a000a9    321 	mov	r0,169

000000f8 eb000000*   322 	bl	BerEncoder_encodeTL

                     323 ;186:                                  initiateResponseLength, buf, bufPos);


                     324 ;187: 


                     325 ;188:     bufPos = BerEncoder_encodeUInt32WithTL(MMS_LOCAL_DETAIL_CALLING,


                     326 

000000fc e0842005    327 	add	r2,r4,r5

00000100 e1a03000    328 	mov	r3,r0

00000104 e594100c    329 	ldr	r1,[r4,12]

00000108 e3a00080    330 	mov	r0,128

0000010c eb000000*   331 	bl	BerEncoder_encodeUInt32WithTL

                     332 ;189:                                            isoConn->maxPduSize, buf, bufPos);


                     333 ;190: 


                     334 ;191:     bufPos = BerEncoder_encodeUInt32WithTL(MMS_MAX_SERV_OUTSTANDING_CALLING,


                     335 

00000110 e0842005    336 	add	r2,r4,r5

00000114 e1a03000    337 	mov	r3,r0

00000118 e5941000    338 	ldr	r1,[r4]

0000011c e3a00081    339 	mov	r0,129

00000120 eb000000*   340 	bl	BerEncoder_encodeUInt32WithTL

                     341 ;192:                                            isoConn->maxServOutstandingCalling,


                     342 ;193:                                            buf, bufPos);


                     343 ;194: 


                     344 ;195:     bufPos = BerEncoder_encodeUInt32WithTL(MMS_MAX_SERV_OUTSTANDING_CALLED,


                     345 

00000124 e0842005    346 	add	r2,r4,r5

00000128 e1a03000    347 	mov	r3,r0

0000012c e5941004    348 	ldr	r1,[r4,4]

00000130 e3a00082    349 	mov	r0,130

00000134 eb000000*   350 	bl	BerEncoder_encodeUInt32WithTL

                     351 ;196:                                            isoConn->maxServOutstandingCalled,


                     352 ;197:                                            buf, bufPos);


                     353 ;198: 


                     354 ;199:     bufPos = BerEncoder_encodeUInt32WithTL(MMS_DATA_STRUCTURE_NESTING_LEVEL,


                     355 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
00000138 e0842005    356 	add	r2,r4,r5

0000013c e1a03000    357 	mov	r3,r0

00000140 e5941008    358 	ldr	r1,[r4,8]

00000144 e3a00083    359 	mov	r0,131

00000148 eb000000*   360 	bl	BerEncoder_encodeUInt32WithTL

                     361 ;200:                                            isoConn->dataStructureNestingLevel,


                     362 ;201:                                            buf, bufPos);


                     363 ;202: 


                     364 ;203:     bufPos = encodeInitResponseDetail(buf, bufPos, 1);


                     365 

0000014c e1a01000    366 	mov	r1,r0

00000150 e0840005    367 	add	r0,r4,r5

00000154 e3a02001    368 	mov	r2,1

00000158 ebffffa8*   369 	bl	encodeInitResponseDetail

                     370 ;204:     return bufPos;


                     371 

                     372 ;217:     acseDataLen = AcseConnection_createAssociateResponseMessage(


                     373 

0000015c e0842005    374 	add	r2,r4,r5

00000160 e2841db7    375 	add	r1,r4,0x2dc0

00000164 e2816a40    376 	add	r6,r1,1<<18

00000168 e3a01000    377 	mov	r1,0

0000016c e58d1000    378 	str	r1,[sp]

00000170 e2861004    379 	add	r1,r6,4

00000174 e1a03000    380 	mov	r3,r0

00000178 e1a00006    381 	mov	r0,r6

0000017c eb000000*   382 	bl	AcseConnection_createAssociateResponseMessage

                     383 ;218:                 &isoConn->acse,  isoConn->acse.outBuf, isoConn->isoOutBuf,


                     384 ;219:                 mmsDataLen, ACSE_RESULT_ACCEPT);


                     385 ;220: 


                     386 ;221:     presentationDataLen = isoPresentation_createCpaMessage(&isoConn->presentation,


                     387 

00000180 e2862004    388 	add	r2,r6,4

00000184 e2841edb    389 	add	r1,r4,0x0db0

00000188 e2815bf0    390 	add	r5,r1,15<<14

0000018c e285100c    391 	add	r1,r5,12

00000190 e1a03000    392 	mov	r3,r0

00000194 e1a00005    393 	mov	r0,r5

00000198 eb000000*   394 	bl	isoPresentation_createCpaMessage

0000019c e1a06000    395 	mov	r6,r0

                     396 ;222:                                  isoConn->presentation.outBuf,isoConn->acse.outBuf,


                     397 ;223:                                                        acseDataLen);    


                     398 ;224: 


                     399 ;225:     isoConn->pCurrCotpOutBuf = allocSessionOutBuffer(&isoConn->outBuffers,


                     400 

000001a0 e2840e80    401 	add	r0,r4,1<<11

000001a4 e2800018    402 	add	r0,r0,24

000001a8 e3a01c60    403 	mov	r1,3<<13

000001ac eb000000*   404 	bl	allocSessionOutBuffer

000001b0 e3500000    405 	cmp	r0,0

                     406 ;228:     {


                     407 

                     408 ;229:         ERROR_REPORT("No free buffer to send");


                     409 ;230:         return -1;


                     410 

000001b4 11a02006    411 	movne	r2,r6

000001b8 1285100c    412 	addne	r1,r5,12

000001bc e5840814    413 	str	r0,[r4,2068]

                     414 ;226: 		SESSION_OUT_BUF_SIZE);


                     415 ;227:     if(isoConn->pCurrCotpOutBuf == NULL)


                     416 


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
000001c0 03e00000    417 	mvneq	r0,0

                     418 ;231:     }


                     419 ;232:     return createAcceptSPDU( isoConn->pCurrCotpOutBuf->cotpOutBuf,


                     420 

000001c4 12800008    421 	addne	r0,r0,8

000001c8 1b000000*   422 	blne	createAcceptSPDU

000001cc e28dd004    423 	add	sp,sp,4

000001d0 e8bd8070    424 	ldmfd	[sp]!,{r4-r6,pc}

                     425 	.endf	processSessionConnect

                     426 	.align	4

                     427 ;presentationDataLen	r6	local

                     428 ;.L151	.L154	static

                     429 ;initiateResponseLength	r5	local

                     430 

                     431 ;isoConn	r4	param

                     432 

                     433 	.section ".bss","awb"

                     434 .L150:

                     435 	.data

                     436 	.text

                     437 

                     438 ;233:                                           isoConn->presentation.outBuf,presentationDataLen);


                     439 ;234: }


                     440 

                     441 ;235: 


                     442 ;236: //outBuf - куда складывать результат


                     443 ;237: //pOutLen - куда складывать длину результата


                     444 ;238: MmsIndication mmsProcessMessage(IsoConnection* isoConn,


                     445 	.align	4

                     446 	.align	4

                     447 mmsProcessMessage::

000001d4 e92d4cf0    448 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                     449 ;239: 								unsigned char* inBuf, int* pRequestPDULen, int inLen,


                     450 ;240:                                 unsigned char* outBuf, int* pOutLen)


                     451 ;241: {


                     452 

000001d8 e1a0b000    453 	mov	fp,r0

000001dc e1a04001    454 	mov	r4,r1

000001e0 e24dd00c    455 	sub	sp,sp,12

000001e4 e59d7028    456 	ldr	r7,[sp,40]

000001e8 e59da02c    457 	ldr	r10,[sp,44]

000001ec e1a06002    458 	mov	r6,r2

000001f0 e3a02000    459 	mov	r2,0

                     460 ;242:     //смотри ParseMmsPacket


                     461 ;243:     //       и MmsServerConnection_parseMessage


                     462 ;244:     MmsIndication retVal;


                     463 ;245:     int bufPos = 0;


                     464 

                     465 ;246:     unsigned char pduType;


                     466 ;247:     int pduLength;


                     467 ;248: 


                     468 ;249:     if (inLen < 2)


                     469 

000001f4 e3530002    470 	cmp	r3,2

000001f8 ba000006    471 	blt	.L172

                     472 ;250:     {


                     473 

                     474 ;251:         return MMS_ERROR;


                     475 

                     476 ;252:     }


                     477 ;253: 



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
                     478 ;254:     pduType = inBuf[bufPos++];


                     479 

000001fc e28d1008    480 	add	r1,sp,8

00000200 e1a00004    481 	mov	r0,r4

00000204 e7d45002    482 	ldrb	r5,[r4,r2]

                     483 ;255:     bufPos = BerDecoder_decodeLength(inBuf, &pduLength, bufPos, inLen);


                     484 

00000208 e3a02001    485 	mov	r2,1

0000020c eb000000*   486 	bl	BerDecoder_decodeLength

00000210 e1b02000    487 	movs	r2,r0

                     488 ;256: 


                     489 ;257:     if (bufPos < 0)


                     490 

00000214 5a000001    491 	bpl	.L171

                     492 .L172:

                     493 ;258:     {


                     494 

                     495 ;259:         return MMS_ERROR;


                     496 

00000218 e3a00000    497 	mov	r0,0

0000021c ea000017    498 	b	.L166

                     499 .L171:

                     500 ;260:     }


                     501 ;261: 


                     502 ;262: 	//Полная длина всего PDU вместе с тэгом и длиной


                     503 ;263: 	*pRequestPDULen = bufPos + pduLength;


                     504 

00000220 e59d1008    505 	ldr	r1,[sp,8]

00000224 e0810002    506 	add	r0,r1,r2

00000228 e5860000    507 	str	r0,[r6]

                     508 ;264: 


                     509 ;265:     switch (pduType) {


                     510 

0000022c e25500a0    511 	subs	r0,r5,160

00000230 0a000005    512 	beq	.L177

00000234 e3500008    513 	cmp	r0,8

00000238 1a00000c    514 	bne	.L178

                     515 ;266:     case MMS_INITIATE_REQUEST_PDU:


                     516 ;267:         debugSendText("MMS_INITIATE_REQUEST_PDU");        


                     517 

0000023c e28f0000*   518 	adr	r0,.L283

00000240 eb000000*   519 	bl	debugSendText

                     520 ;268:         retVal = MMS_INITIATE;


                     521 

00000244 e3a00001    522 	mov	r0,1

                     523 ;280:         break;


                     524 ;281:     }


                     525 ;282:     return retVal;


                     526 

00000248 ea00000c    527 	b	.L166

                     528 .L177:

                     529 ;269:         break;


                     530 ;270:     case MMS_CONFIRMED_REQUEST_PDU:


                     531 ;271:         //debugSendText("MMS_CONFIRMED_REQUEST_PDU");		


                     532 ;272:         *pOutLen = handleConfirmedRequestPdu(isoConn, inBuf, bufPos, bufPos + pduLength,


                     533 

0000024c e3a0cc60    534 	mov	r12,3<<13

00000250 e88d1080    535 	stmea	[sp],{r7,r12}

00000254 e0813002    536 	add	r3,r1,r2

00000258 e1a01004    537 	mov	r1,r4

0000025c e1a0000b    538 	mov	r0,fp


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
00000260 eb000000*   539 	bl	handleConfirmedRequestPdu

00000264 e58a0000    540 	str	r0,[r10]

                     541 ;273:                                             outBuf, DEFAULT_BUFFER_SIZE);


                     542 ;274:         retVal = MMS_CONFIRMED_REQUEST;


                     543 

00000268 e3a00002    544 	mov	r0,2

                     545 ;280:         break;


                     546 ;281:     }


                     547 ;282:     return retVal;


                     548 

0000026c ea000003    549 	b	.L166

                     550 .L178:

                     551 ;275:         break;


                     552 ;276:     default:


                     553 ;277:         //mmsMsg_createMmsRejectPdu(NULL, MMS_ERROR_REJECT_UNKNOWN_PDU_TYPE, response);


                     554 ;278:         debugSendUshort("Unknown MMS PDU type ", pduType);


                     555 

00000270 e28f0000*   556 	adr	r0,.L284

00000274 e1a01005    557 	mov	r1,r5

00000278 eb000000*   558 	bl	debugSendUshort

                     559 ;279:         retVal = MMS_ERROR;


                     560 

0000027c e3a00000    561 	mov	r0,0

                     562 ;280:         break;


                     563 ;281:     }


                     564 ;282:     return retVal;


                     565 

                     566 .L166:

00000280 e28dd00c    567 	add	sp,sp,12

00000284 e8bd8cf0    568 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                     569 	.endf	mmsProcessMessage

                     570 	.align	4

                     571 .L118:

00000288 00000000*   572 	.data.w	.L102

                     573 	.type	.L118,$object

                     574 	.size	.L118,4

                     575 

                     576 .L165:

                     577 ;	"Send Accept SPDU\000"

0000028c 646e6553    578 	.data.b	83,101,110,100

00000290 63634120    579 	.data.b	32,65,99,99

00000294 20747065    580 	.data.b	101,112,116,32

00000298 55445053    581 	.data.b	83,80,68,85

0000029c 00         582 	.data.b	0

0000029d 000000     583 	.align 4

                     584 

                     585 	.type	.L165,$object

                     586 	.size	.L165,4

                     587 

                     588 .L283:

                     589 ;	"MMS_INITIATE_REQUEST_PDU\000"

000002a0 5f534d4d    590 	.data.b	77,77,83,95

000002a4 54494e49    591 	.data.b	73,78,73,84

000002a8 45544149    592 	.data.b	73,65,84,69

000002ac 5145525f    593 	.data.b	95,82,69,81

000002b0 54534555    594 	.data.b	85,69,83,84

000002b4 5544505f    595 	.data.b	95,80,68,85

000002b8 00         596 	.data.b	0

000002b9 000000     597 	.align 4

                     598 

                     599 	.type	.L283,$object


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
                     600 	.size	.L283,4

                     601 

                     602 .L284:

                     603 ;	"Unknown MMS PDU type \000"

000002bc 6e6b6e55    604 	.data.b	85,110,107,110

000002c0 206e776f    605 	.data.b	111,119,110,32

000002c4 20534d4d    606 	.data.b	77,77,83,32

000002c8 20554450    607 	.data.b	80,68,85,32

000002cc 65707974    608 	.data.b	116,121,112,101

000002d0 0020       609 	.data.b	32,0

000002d2 0000       610 	.align 4

                     611 

                     612 	.type	.L284,$object

                     613 	.size	.L284,4

                     614 

                     615 	.align	4

                     616 ;bufPos	r2	local

                     617 ;pduType	r5	local

                     618 ;pduLength	[sp,8]	local

                     619 ;.L261	.L266	static

                     620 ;.L262	.L265	static

                     621 

                     622 ;isoConn	fp	param

                     623 ;inBuf	r4	param

                     624 ;pRequestPDULen	r6	param

                     625 ;inLen	r3	param

                     626 ;outBuf	r7	param

                     627 ;pOutLen	r10	param

                     628 

                     629 	.section ".bss","awb"

                     630 .L260:

                     631 	.data

                     632 	.text

                     633 

                     634 ;283: }


                     635 

                     636 ;284: 


                     637 ;285: int processSessionData(IsoConnection* isoConn,


                     638 	.align	4

                     639 	.align	4

                     640 processSessionData::

000002d4 e92d4df0    641 	stmfd	[sp]!,{r4-r8,r10-fp,lr}

                     642 ;286:                        unsigned char* inBuf, int inLen)


                     643 ;287: 


                     644 ;288: {	


                     645 

000002d8 e3a04000    646 	mov	r4,0

000002dc e1a07004    647 	mov	r7,r4

000002e0 e1a0b004    648 	mov	fp,r4

                     649 ;289: 	int mmsInPacketLen;


                     650 ;290: 	int mmsInPacketPos = 0;


                     651 

                     652 ;291:     int presentationDataLen;


                     653 ;292: 	int mmsOutDataLen = 0;


                     654 

                     655 ;293:     unsigned char* userData;


                     656 ;294: 	unsigned char* outBuf = isoConn->isoOutBuf;


                     657 

000002e4 e1a0a000    658 	mov	r10,r0

000002e8 e28a0fd7    659 	add	r0,r10,0x035c

000002ec e2805a50    660 	add	r5,r0,5<<16


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
                     661 ;295: 


                     662 ;296:     MmsIndication mmsInd;


                     663 ;297: 	bool confirmedRequest = false;


                     664 

                     665 ;298: 


                     666 ;299:     //debugSendText("processSessionData");


                     667 ;300: 	mmsInPacketLen = isoPresentation_parseUserData(&isoConn->presentation,


                     668 

000002f0 e28a0edb    669 	add	r0,r10,0x0db0

000002f4 e2800bf0    670 	add	r0,r0,15<<14

000002f8 e1a08000    671 	mov	r8,r0

000002fc e24dd018    672 	sub	sp,sp,24

00000300 e28d3008    673 	add	r3,sp,8

00000304 eb000000*   674 	bl	isoPresentation_parseUserData

00000308 e1a06000    675 	mov	r6,r0

                     676 ;301:                                                           inBuf, inLen, &userData);


                     677 ;302: 	if(mmsInPacketLen == -1)


                     678 

0000030c e3760001    679 	cmn	r6,1

00000310 0a000031    680 	beq	.L295

                     681 ;303:     {


                     682 

                     683 ;304:        return -1;


                     684 

                     685 ;305:     }


                     686 ;306: 


                     687 ;307: 	debugSendUshort("mmsPacketLen ", mmsInPacketLen);


                     688 

00000314 e1a01806    689 	mov	r1,r6 lsl 16

00000318 e28f0000*   690 	adr	r0,.L426

0000031c e1a01821    691 	mov	r1,r1 lsr 16

00000320 eb000000*   692 	bl	debugSendUshort

                     693 ;308: 


                     694 ;309: 	//mmsInPacketLen это длина всего сообщения, которая может


                     695 ;310: 	//включать в себя несколько PDU с разными invokeID	


                     696 ;311: 


                     697 ;312: 	while(mmsInPacketPos < mmsInPacketLen)


                     698 

00000324 e1540006    699 	cmp	r4,r6

00000328 aa000011    700 	bge	.L291

                     701 .L292:

                     702 ;313: 	{


                     703 

                     704 ;314: 		int responsePDULen;


                     705 ;315: 		int requestPDULen;


                     706 ;316: 		mmsInd = mmsProcessMessage(isoConn,


                     707 

0000032c e28dc00c    708 	add	r12,sp,12

00000330 e88d1020    709 	stmea	[sp],{r5,r12}

00000334 e1a03006    710 	mov	r3,r6

00000338 e28d2010    711 	add	r2,sp,16

0000033c e59d1008    712 	ldr	r1,[sp,8]

00000340 e1a0000a    713 	mov	r0,r10

00000344 ebffffa2*   714 	bl	mmsProcessMessage

                     715 ;317: 								   userData, &requestPDULen, mmsInPacketLen,


                     716 ;318: 								   outBuf,&responsePDULen);


                     717 ;319: 		if(mmsInd == MMS_CONFIRMED_REQUEST)


                     718 

00000348 e3500002    719 	cmp	r0,2

                     720 ;320: 		{


                     721 


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
                     722 ;321: 			confirmedRequest = true;


                     723 

0000034c 03a0b001    724 	moveq	fp,1

                     725 ;322: 		}


                     726 ;323: 


                     727 ;324: 


                     728 ;325: 		userData += requestPDULen;


                     729 

00000350 e28d1008    730 	add	r1,sp,8

00000354 e891000d    731 	ldmfd	[r1],{r0,r2-r3}

00000358 e0877002    732 	add	r7,r7,r2

                     733 ;328: 		outBuf += responsePDULen;


                     734 

0000035c e0800003    735 	add	r0,r0,r3

00000360 e58d0008    736 	str	r0,[sp,8]

                     737 ;326: 		mmsInPacketPos += requestPDULen;


                     738 

00000364 e0855002    739 	add	r5,r5,r2

00000368 e0844003    740 	add	r4,r4,r3

                     741 ;327: 		mmsOutDataLen += responsePDULen;


                     742 

0000036c e1540006    743 	cmp	r4,r6

00000370 baffffed    744 	blt	.L292

                     745 .L291:

                     746 ;329: 	}


                     747 ;330: 


                     748 ;331: 


                     749 ;332: 	if(confirmedRequest)


                     750 

00000374 e35b0000    751 	cmp	fp,0

00000378 0a000017    752 	beq	.L295

                     753 ;333:     {


                     754 

                     755 ;334:         //ACSE_createMessage() ;


                     756 ;335: 		debugSendUshort("MmsResponseLen:", mmsOutDataLen);


                     757 

0000037c e1a01807    758 	mov	r1,r7 lsl 16

00000380 e28f0000*   759 	adr	r0,.L427

00000384 e1a01821    760 	mov	r1,r1 lsr 16

00000388 eb000000*   761 	bl	debugSendUshort

                     762 ;336:         presentationDataLen =  IsoPresentation_createUserData(&isoConn->presentation,


                     763 

0000038c e1a03007    764 	mov	r3,r7

00000390 e28a0fd7    765 	add	r0,r10,0x035c

00000394 e2802a50    766 	add	r2,r0,5<<16

00000398 e1a00008    767 	mov	r0,r8

0000039c e280100c    768 	add	r1,r0,12

000003a0 eb000000*   769 	bl	IsoPresentation_createUserData

000003a4 e1a04000    770 	mov	r4,r0

                     771 ;337: 															  isoConn->presentation.outBuf,isoConn->isoOutBuf, mmsOutDataLen);


                     772 ;338: 


                     773 ;339:         isoConn->pCurrCotpOutBuf = allocSessionOutBuffer(&isoConn->outBuffers,


                     774 

000003a8 e28a0e80    775 	add	r0,r10,1<<11

000003ac e2800018    776 	add	r0,r0,24

000003b0 e3a01c60    777 	mov	r1,3<<13

000003b4 eb000000*   778 	bl	allocSessionOutBuffer

000003b8 e58a0814    779 	str	r0,[r10,2068]

                     780 ;340: 			SESSION_OUT_BUF_SIZE);


                     781 ;341:         if(isoConn->pCurrCotpOutBuf == NULL)


                     782 


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
000003bc e3500000    783 	cmp	r0,0

000003c0 0a000005    784 	beq	.L295

                     785 ;342:         {


                     786 

                     787 ;343:             ERROR_REPORT("No free buffer to send");


                     788 ;344:             return -1;


                     789 

                     790 ;345:         }


                     791 ;346:         return isoSession_createDataSpdu(isoConn->pCurrCotpOutBuf->cotpOutBuf, 


                     792 

000003c4 e1a03004    793 	mov	r3,r4

000003c8 e288200c    794 	add	r2,r8,12

000003cc e2800008    795 	add	r0,r0,8

000003d0 e3a01c60    796 	mov	r1,3<<13

000003d4 eb000000*   797 	bl	isoSession_createDataSpdu

000003d8 ea000000    798 	b	.L285

                     799 .L295:

                     800 ;347:             SESSION_OUT_BUF_SIZE, isoConn->presentation.outBuf,presentationDataLen);


                     801 ;348: 


                     802 ;349:     }


                     803 ;350:     return -1;


                     804 

000003dc e3e00000    805 	mvn	r0,0

                     806 .L285:

000003e0 e28dd018    807 	add	sp,sp,24

000003e4 e8bd8df0    808 	ldmfd	[sp]!,{r4-r8,r10-fp,pc}

                     809 	.endf	processSessionData

                     810 	.align	4

                     811 ;mmsInPacketLen	r6	local

                     812 ;mmsInPacketPos	r4	local

                     813 ;presentationDataLen	r4	local

                     814 ;mmsOutDataLen	r7	local

                     815 ;userData	[sp,8]	local

                     816 ;outBuf	r5	local

                     817 ;confirmedRequest	fp	local

                     818 ;.L391	.L396	static

                     819 ;responsePDULen	[sp,12]	local

                     820 ;requestPDULen	[sp,16]	local

                     821 ;.L392	.L395	static

                     822 

                     823 ;isoConn	r10	param

                     824 ;inBuf	none	param

                     825 ;inLen	none	param

                     826 

                     827 	.section ".bss","awb"

                     828 .L390:

                     829 	.data

                     830 	.text

                     831 

                     832 ;351: }


                     833 

                     834 ;352: 


                     835 ;353: void closeIsoConnection(IsoConnection* isoConn)


                     836 	.align	4

                     837 	.align	4

                     838 closeIsoConnection::

000003e8 e92d4030    839 	stmfd	[sp]!,{r4-r5,lr}

000003ec e1a04000    840 	mov	r4,r0

                     841 ;354: {


                     842 

                     843 ;355: 	COTPConnection* pCotpConn = &isoConn->cotpConn;



                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
                     844 

000003f0 e2840bf2    845 	add	r0,r4,242<<10

000003f4 e280509c    846 	add	r5,r0,156

                     847 ;356: 	mms_closeConnection(&isoConn->mmsConn);


                     848 

                     849 ;146: {


                     850 

                     851 ;147: 	if (mmsConn->isFileOpen)


                     852 

000003f8 e3a00f8f    853 	mov	r0,0x023c

000003fc e2600a49    854 	rsb	r0,r0,73<<12

00000400 e0840000    855 	add	r0,r4,r0

00000404 e2800c75    856 	add	r0,r0,117<<8

00000408 e5d01093    857 	ldrb	r1,[r0,147]

0000040c e2800090    858 	add	r0,r0,144

00000410 e3510000    859 	cmp	r1,0

                     860 ;148: 	{


                     861 

                     862 ;149: 		fs_fileClose(mmsConn->frsmID);


                     863 

00000414 15900004    864 	ldrne	r0,[r0,4]

00000418 1b000000*   865 	blne	fs_fileClose

                     866 ;357: 	isoConn->connected = FALSE;


                     867 

0000041c e3a00000    868 	mov	r0,0

00000420 e3a01bf2    869 	mov	r1,242<<10

00000424 e2811098    870 	add	r1,r1,152

00000428 e7c10004    871 	strb	r0,[r1,r4]

                     872 ;358: 	disableDisconnectedReports();


                     873 

0000042c eb000000*   874 	bl	disableDisconnectedReports

                     875 ;359: 	Control_disableWaitingObjects();


                     876 

00000430 eb000000*   877 	bl	Control_disableWaitingObjects

                     878 ;360:     while(isoConn->sendThreadIsRunning)


                     879 

00000434 e3a01bf2    880 	mov	r1,242<<10

00000438 e2811099    881 	add	r1,r1,153

0000043c e7d10004    882 	ldrb	r0,[r1,r4]

00000440 e3500000    883 	cmp	r0,0

00000444 0a00000e    884 	beq	.L436

                     885 .L437:

                     886 ;361:     {


                     887 

                     888 ;362:         Idle();


                     889 

00000448 ea000007    890 	b	.L512

                     891 	.align	4

                     892 .L426:

                     893 ;	"mmsPacketLen \000"

0000044c 50736d6d    894 	.data.b	109,109,115,80

00000450 656b6361    895 	.data.b	97,99,107,101

00000454 6e654c74    896 	.data.b	116,76,101,110

00000458 0020       897 	.data.b	32,0

0000045a 0000       898 	.align 4

                     899 

                     900 	.type	.L426,$object

                     901 	.size	.L426,4

                     902 

                     903 .L427:

                     904 ;	"MmsResponseLen:\000"


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
0000045c 52736d4d    905 	.data.b	77,109,115,82

00000460 6f707365    906 	.data.b	101,115,112,111

00000464 4c65736e    907 	.data.b	110,115,101,76

00000468 003a6e65    908 	.data.b	101,110,58,0

                     909 	.align 4

                     910 

                     911 	.type	.L427,$object

                     912 	.size	.L427,4

                     913 

                     914 .L512:

                     915 

0000046c e6000010    916 	.word	0xE6000010

                     917 

00000470 e3a01bf2    918 	mov	r1,242<<10

00000474 e2811099    919 	add	r1,r1,153

00000478 e7d10004    920 	ldrb	r0,[r1,r4]

0000047c e3500000    921 	cmp	r0,0

00000480 1afffff0    922 	bne	.L437

                     923 .L436:

                     924 ;363:     }


                     925 ;364: 	closeServerSocket(pCotpConn->socket);


                     926 

00000484 e5950000    927 	ldr	r0,[r5]

00000488 eb000000*   928 	bl	closeServerSocket

                     929 ;365: 	ERROR_REPORT("Connection closed");


                     930 ;366: 	


                     931 ;367: 	OutQueue_done(&isoConn->outQueue);


                     932 

0000048c e2840bf2    933 	add	r0,r4,242<<10

00000490 e280006c    934 	add	r0,r0,108

00000494 eb000000*   935 	bl	OutQueue_done

                     936 ;368:     SessionBuffers_done(&isoConn->outBuffers);


                     937 

00000498 e2840e80    938 	add	r0,r4,1<<11

0000049c e2800018    939 	add	r0,r0,24

000004a0 eb000000*   940 	bl	SessionBuffers_done

                     941 ;369:     freeConnection(isoConn);


                     942 

000004a4 e1a00004    943 	mov	r0,r4

000004a8 e8bd4030    944 	ldmfd	[sp]!,{r4-r5,lr}

000004ac ea000000*   945 	b	freeConnection

                     946 	.endf	closeIsoConnection

                     947 	.align	4

                     948 ;pCotpConn	r5	local

                     949 

                     950 ;isoConn	r4	param

                     951 

                     952 	.section ".bss","awb"

                     953 .L492:

                     954 	.data

                     955 	.text

                     956 

                     957 ;370: }


                     958 

                     959 ;371: 


                     960 ;372: void mmsThread(IsoConnection* mmsConn)


                     961 	.align	4

                     962 	.align	4

                     963 mmsThread::

000004b0 e92d4070    964 	stmfd	[sp]!,{r4-r6,lr}

                     965 ;373: {        



                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
                     966 

000004b4 e24dd008    967 	sub	sp,sp,8

000004b8 e3a06000    968 	mov	r6,0

                     969 ;374:     int cotpSendDataCount = 0;       


                     970 

                     971 ;375:     int byteCount;        


                     972 ;376: 


                     973 ;377:     mmsConn->pCurrCotpOutBuf = NULL;


                     974 

000004bc e1a04000    975 	mov	r4,r0

000004c0 e28f0000*   976 	adr	r0,.L711

000004c4 e5846814    977 	str	r6,[r4,2068]

                     978 ;378:     debugSendText("MMS thread started");	


                     979 

000004c8 eb000000*   980 	bl	debugSendText

                     981 ;379:     while (1) {


                     982 

000004cc e28f5000*   983 	adr	r5,.L712

                     984 .L517:

                     985 ;380:         debugSendUshort("COTP data to send:", cotpSendDataCount);


                     986 

000004d0 e1a01806    987 	mov	r1,r6 lsl 16

000004d4 e1a01821    988 	mov	r1,r1 lsr 16

000004d8 e1a00005    989 	mov	r0,r5

000004dc eb000000*   990 	bl	debugSendUshort

                     991 ;381: 


                     992 ;382:         if(cotpSendDataCount > 0)


                     993 

000004e0 e3560000    994 	cmp	r6,0

000004e4 c5941814    995 	ldrgt	r1,[r4,2068]

000004e8 c2840bf2    996 	addgt	r0,r4,242<<10

000004ec c5816004    997 	strgt	r6,[r1,4]

000004f0 c280006c    998 	addgt	r0,r0,108

000004f4 cb000000*   999 	blgt	OutQueue_insert

                    1000 ;383:         {


                    1001 

                    1002 ;384: 			mmsConn->pCurrCotpOutBuf->byteCount = cotpSendDataCount;


                    1003 

                    1004 ;385:             if(!OutQueue_insert(&mmsConn->outQueue,


                    1005 

                    1006 ;386:                                 mmsConn->pCurrCotpOutBuf))


                    1007 ;387:             {


                    1008 

                    1009 ;388:                 ERROR_REPORT("Out queue overflow");


                    1010 ;389:             }


                    1011 ;390:             cotpSendDataCount = 0;


                    1012 

                    1013 ;391:         }


                    1014 ;392: 


                    1015 ;393:         //Получить данные COTP


                    1016 ;394:         byteCount =  cotpReceiveData(&mmsConn->cotpConn,


                    1017 

000004f8 e2841014   1018 	add	r1,r4,20

000004fc e2840bf2   1019 	add	r0,r4,242<<10

00000500 e280009c   1020 	add	r0,r0,156

00000504 e3a02e80   1021 	mov	r2,1<<11

00000508 eb000000*  1022 	bl	cotpReceiveData

0000050c e1a01000   1023 	mov	r1,r0

                    1024 ;395:                                      mmsConn->cotpInBuf, COTP_IN_BUF_SIZE);


                    1025 ;396: 


                    1026 ;397:         if( byteCount  == -1 )



                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
                    1027 

00000510 e3710001   1028 	cmn	r1,1

00000514 0a00001c   1029 	beq	.L533

                    1030 ;398:         {


                    1031 

                    1032 ;399: 			closeIsoConnection(mmsConn);


                    1033 

                    1034 ;400:             ERROR_REPORT("COTP error");			


                    1035 ;401:             return;


                    1036 

                    1037 ;402:         }


                    1038 ;403:         else


                    1039 ;404:         {


                    1040 

                    1041 ;405:             unsigned char* userData;


                    1042 ;406:             int userDataLen;


                    1043 ;407: 


                    1044 ;408:             IsoSessionIndication sessionIndication = parseSessionMessage(mmsConn->cotpInBuf,


                    1045 

00000518 e28d3004   1046 	add	r3,sp,4

0000051c e1a0200d   1047 	mov	r2,sp

00000520 e2840014   1048 	add	r0,r4,20

00000524 eb000000*  1049 	bl	parseSessionMessage

                    1050 ;409:                                                                          byteCount, &userData, &userDataLen);


                    1051 ;410: 


                    1052 ;411:             switch(sessionIndication)


                    1053 

00000528 e2500001   1054 	subs	r0,r0,1

0000052c 0a000011   1055 	beq	.L530

00000530 e2500001   1056 	subs	r0,r0,1

00000534 0a000002   1057 	beq	.L528

00000538 e3500002   1058 	cmp	r0,2

                    1059 ;422:                 break;


                    1060 ;423:             default:


                    1061 ;424:                 cotpSendDataCount = -1;


                    1062 

0000053c 1a000012   1063 	bne	.L533

00000540 ea000005   1064 	b	.L529

                    1065 .L528:

                    1066 ;412:             {


                    1067 ;413:             case SESSION_CONNECT:


                    1068 ;414:                 cotpSendDataCount = processSessionConnect(mmsConn);


                    1069 

00000544 e1a00004   1070 	mov	r0,r4

00000548 ebfffecc*  1071 	bl	processSessionConnect

0000054c e1a06000   1072 	mov	r6,r0

                    1073 ;425:                 break;


                    1074 ;426:             }


                    1075 ;427:             if(cotpSendDataCount == -1)


                    1076 

00000550 e3760001   1077 	cmn	r6,1

00000554 1affffdd   1078 	bne	.L517

00000558 ea00000b   1079 	b	.L533

                    1080 .L529:

                    1081 ;415:                 break;


                    1082 ;416:             case SESSION_DATA:


                    1083 ;417:                 cotpSendDataCount = processSessionData(mmsConn,userData, userDataLen);


                    1084 

0000055c e89d0006   1085 	ldmfd	[sp],{r1-r2}

00000560 e1a00004   1086 	mov	r0,r4

00000564 ebffff5a*  1087 	bl	processSessionData


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
00000568 e1a06000   1088 	mov	r6,r0

                    1089 ;425:                 break;


                    1090 ;426:             }


                    1091 ;427:             if(cotpSendDataCount == -1)


                    1092 

0000056c e3760001   1093 	cmn	r6,1

00000570 1affffd6   1094 	bne	.L517

00000574 ea000004   1095 	b	.L533

                    1096 .L530:

                    1097 ;418:                 break;


                    1098 ;419:             case SESSION_ERROR:


                    1099 ;420:                 debugSendText("Session error");


                    1100 

00000578 e28f0000*  1101 	adr	r0,.L713

0000057c eb000000*  1102 	bl	debugSendText

                    1103 ;421:                 cotpSendDataCount = -1;


                    1104 

                    1105 ;428:             {


                    1106 

                    1107 ;429: 				closeIsoConnection(mmsConn);                


                    1108 

00000580 e1a00004   1109 	mov	r0,r4

00000584 ebffff97*  1110 	bl	closeIsoConnection

                    1111 ;430:                 return;


                    1112 

00000588 ea000001   1113 	b	.L513

                    1114 .L533:

                    1115 ;428:             {


                    1116 

                    1117 ;429: 				closeIsoConnection(mmsConn);                


                    1118 

0000058c e1a00004   1119 	mov	r0,r4

00000590 ebffff94*  1120 	bl	closeIsoConnection

                    1121 ;430:                 return;


                    1122 

                    1123 .L513:

00000594 e28dd008   1124 	add	sp,sp,8

00000598 e8bd8070   1125 	ldmfd	[sp]!,{r4-r6,pc}

                    1126 	.endf	mmsThread

                    1127 	.align	4

                    1128 ;cotpSendDataCount	r6	local

                    1129 ;byteCount	r1	local

                    1130 ;.L673	.L679	static

                    1131 ;.L674	.L680	static

                    1132 ;userData	[sp]	local

                    1133 ;userDataLen	[sp,4]	local

                    1134 ;.L675	.L678	static

                    1135 

                    1136 ;mmsConn	r4	param

                    1137 

                    1138 	.section ".bss","awb"

                    1139 .L672:

                    1140 	.data

                    1141 	.text

                    1142 

                    1143 ;431:             }


                    1144 ;432:         }


                    1145 ;433:     }


                    1146 ;434: }


                    1147 

                    1148 ;435: 



                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
                    1149 ;436: 


                    1150 ;437: void handleMMSConnection(SERVER_SOCKET socket)


                    1151 	.align	4

                    1152 	.align	4

                    1153 handleMMSConnection::

0000059c e92d4030   1154 	stmfd	[sp]!,{r4-r5,lr}

000005a0 e1a05000   1155 	mov	r5,r0

                    1156 ;438: {


                    1157 

                    1158 ;439: 	IsoConnection* isoConn = allocateConnection();


                    1159 

000005a4 eb000000*  1160 	bl	allocateConnection

000005a8 e1b04000   1161 	movs	r4,r0

                    1162 ;440: 	if (isoConn == NULL)


                    1163 

                    1164 ;441: 	{


                    1165 

                    1166 ;442: 		ERROR_REPORT("Unable to allocate connection");


                    1167 ;443: 		closeServerSocket(socket);


                    1168 

000005ac 01a00005   1169 	moveq	r0,r5

000005b0 08bd4030   1170 	ldmeqfd	[sp]!,{r4-r5,lr}

000005b4 0a000000*  1171 	beq	closeServerSocket

                    1172 ;445: 	}


                    1173 ;446: 


                    1174 ;447:     debugSendText("\r\n==============================\r\nTCP Connected");


                    1175 

000005b8 e28f0000*  1176 	adr	r0,.L776

000005bc eb000000*  1177 	bl	debugSendText

                    1178 ;448:     initIsoConnection(isoConn);


                    1179 

                    1180 ;155: {


                    1181 

                    1182 ;156:     isoConn->maxServOutstandingCalling


                    1183 

000005c0 e3a00005   1184 	mov	r0,5

000005c4 e1a02000   1185 	mov	r2,r0

                    1186 ;157:             = DEFAULT_MAX_SERV_OUTSTANDING_CALLING;


                    1187 ;158:     isoConn->maxServOutstandingCalled


                    1188 

                    1189 ;159:             = DEFAULT_MAX_SERV_OUTSTANDING_CALLED;


                    1190 ;160:     isoConn->dataStructureNestingLevel


                    1191 

000005c8 e3a0300a   1192 	mov	r3,10

                    1193 ;161:             = DEFAULT_DATA_STRUCTURE_NESTING_LEVEL;    


                    1194 ;162:     isoConn->maxPduSize = CONFIG_MMS_MAXIMUM_PDU_SIZE;


                    1195 

000005cc e3a0cb40   1196 	mov	r12,1<<16

000005d0 e884100d   1197 	stmea	[r4],{r0,r2-r3,r12}

                    1198 ;163: 	initSessionOutBuffers(&isoConn->outBuffers);


                    1199 

000005d4 e2840e80   1200 	add	r0,r4,1<<11

000005d8 e2800018   1201 	add	r0,r0,24

000005dc eb000000*  1202 	bl	initSessionOutBuffers

                    1203 ;164:     OutQueue_init(&isoConn->outQueue);


                    1204 

000005e0 e2840bf2   1205 	add	r0,r4,242<<10

000005e4 e280006c   1206 	add	r0,r0,108

000005e8 eb000000*  1207 	bl	OutQueue_init

                    1208 ;165: 	mms_initConnection(&isoConn->mmsConn);


                    1209 


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
                    1210 ;141: {


                    1211 

                    1212 ;142: 	mmsConn->isFileOpen = FALSE;


                    1213 

000005ec e3a00f8f   1214 	mov	r0,0x023c

000005f0 e2600a49   1215 	rsb	r0,r0,73<<12

000005f4 e0840000   1216 	add	r0,r4,r0

000005f8 e2800c75   1217 	add	r0,r0,117<<8

000005fc e3a01000   1218 	mov	r1,0

00000600 e5c01093   1219 	strb	r1,[r0,147]

                    1220 ;449:     initPresentation(&isoConn->presentation);


                    1221 

00000604 e2840edb   1222 	add	r0,r4,0x0db0

00000608 e2800bf0   1223 	add	r0,r0,15<<14

0000060c eb000000*  1224 	bl	initPresentation

                    1225 ;450:     AcseConnection_init(&isoConn->acse);


                    1226 

00000610 e2840db7   1227 	add	r0,r4,0x2dc0

00000614 e2800a40   1228 	add	r0,r0,1<<18

00000618 eb000000*  1229 	bl	AcseConnection_init

                    1230 ;451:     initCOTPConnection(&isoConn->cotpConn, socket);


                    1231 

0000061c e1a01005   1232 	mov	r1,r5

00000620 e2840bf2   1233 	add	r0,r4,242<<10

00000624 e280009c   1234 	add	r0,r0,156

00000628 eb000000*  1235 	bl	initCOTPConnection

                    1236 ;452:     debugSendText("Starting MMS thread...");    


                    1237 

0000062c e28f0000*  1238 	adr	r0,.L777

00000630 eb000000*  1239 	bl	debugSendText

                    1240 ;453: 	isoConn->connected = TRUE;    


                    1241 

00000634 e3a00001   1242 	mov	r0,1

00000638 e3a01bf2   1243 	mov	r1,242<<10

0000063c e2811098   1244 	add	r1,r1,152

00000640 e7c10004   1245 	strb	r0,[r1,r4]

                    1246 ;454:     createThread(sendThread, isoConn);


                    1247 

00000644 e59f0094*  1248 	ldr	r0,.L778

00000648 e1a01004   1249 	mov	r1,r4

0000064c eb000000*  1250 	bl	createThread

                    1251 ;455:     createThread(mmsThread, isoConn);


                    1252 

00000650 e1a01004   1253 	mov	r1,r4

00000654 e59f0088*  1254 	ldr	r0,.L779

00000658 e8bd4030   1255 	ldmfd	[sp]!,{r4-r5,lr}

0000065c ea000000*  1256 	b	createThread

                    1257 	.endf	handleMMSConnection

                    1258 	.align	4

                    1259 .L711:

                    1260 ;	"MMS thread started\000"

00000660 20534d4d   1261 	.data.b	77,77,83,32

00000664 65726874   1262 	.data.b	116,104,114,101

00000668 73206461   1263 	.data.b	97,100,32,115

0000066c 74726174   1264 	.data.b	116,97,114,116

00000670 6465      1265 	.data.b	101,100

00000672 00        1266 	.data.b	0

00000673 00        1267 	.align 4

                    1268 

                    1269 	.type	.L711,$object

                    1270 	.size	.L711,4


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
                    1271 

                    1272 .L712:

                    1273 ;	"COTP data to send:\000"

00000674 50544f43   1274 	.data.b	67,79,84,80

00000678 74616420   1275 	.data.b	32,100,97,116

0000067c 6f742061   1276 	.data.b	97,32,116,111

00000680 6e657320   1277 	.data.b	32,115,101,110

00000684 3a64      1278 	.data.b	100,58

00000686 00        1279 	.data.b	0

00000687 00        1280 	.align 4

                    1281 

                    1282 	.type	.L712,$object

                    1283 	.size	.L712,4

                    1284 

                    1285 .L713:

                    1286 ;	"Session error\000"

00000688 73736553   1287 	.data.b	83,101,115,115

0000068c 206e6f69   1288 	.data.b	105,111,110,32

00000690 6f727265   1289 	.data.b	101,114,114,111

00000694 0072      1290 	.data.b	114,0

00000696 0000      1291 	.align 4

                    1292 

                    1293 	.type	.L713,$object

                    1294 	.size	.L713,4

                    1295 

                    1296 .L776:

                    1297 ;	"\r\n==============================\r\nTCP Connected\000"

00000698 3d3d0a0d   1298 	.data.b	13,10,61,61

0000069c 3d3d3d3d   1299 	.data.b	61,61,61,61

000006a0 3d3d3d3d   1300 	.data.b	61,61,61,61

000006a4 3d3d3d3d   1301 	.data.b	61,61,61,61

000006a8 3d3d3d3d   1302 	.data.b	61,61,61,61

000006ac 3d3d3d3d   1303 	.data.b	61,61,61,61

000006b0 3d3d3d3d   1304 	.data.b	61,61,61,61

000006b4 3d3d3d3d   1305 	.data.b	61,61,61,61

000006b8 43540a0d   1306 	.data.b	13,10,84,67

000006bc 6f432050   1307 	.data.b	80,32,67,111

000006c0 63656e6e   1308 	.data.b	110,110,101,99

000006c4 00646574   1309 	.data.b	116,101,100,0

                    1310 	.align 4

                    1311 

                    1312 	.type	.L776,$object

                    1313 	.size	.L776,4

                    1314 

                    1315 .L777:

                    1316 ;	"Starting MMS thread...\000"

000006c8 72617453   1317 	.data.b	83,116,97,114

000006cc 676e6974   1318 	.data.b	116,105,110,103

000006d0 534d4d20   1319 	.data.b	32,77,77,83

000006d4 72687420   1320 	.data.b	32,116,104,114

000006d8 2e646165   1321 	.data.b	101,97,100,46

000006dc 2e2e      1322 	.data.b	46,46

000006de 00        1323 	.data.b	0

000006df 00        1324 	.align 4

                    1325 

                    1326 	.type	.L777,$object

                    1327 	.size	.L777,4

                    1328 

                    1329 .L778:

000006e0 00000000*  1330 	.data.w	sendThread

                    1331 	.type	.L778,$object


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_atk1.s
                    1332 	.size	.L778,4

                    1333 

                    1334 .L779:

000006e4 00000000*  1335 	.data.w	mmsThread

                    1336 	.type	.L779,$object

                    1337 	.size	.L779,4

                    1338 

                    1339 	.align	4

                    1340 ;isoConn	r4	local

                    1341 ;.L759	.L763	static

                    1342 ;.L760	.L764	static

                    1343 

                    1344 ;socket	r5	param

                    1345 

                    1346 	.section ".bss","awb"

                    1347 .L758:

                    1348 	.data

                    1349 	.text

                    1350 

                    1351 ;456: 


                    1352 ;457: }


                    1353 	.align	4

                    1354 ;servicesSupported	.L104	static

                    1355 ;parameterCBB	.L103	static

                    1356 

                    1357 	.data

                    1358 	.ghsnote version,6

                    1359 	.ghsnote tools,1

                    1360 	.ghsnote options,0

                    1361 	.text

                    1362 	.align	4

                    1363 	.data

                    1364 	.align	4

                    1365 	.text

