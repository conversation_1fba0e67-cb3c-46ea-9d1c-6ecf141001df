                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1o41.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedConstDA.c -o iedTree\gh_1o41.o -list=iedTree/iedConstDA.lst C:\Users\<USER>\AppData\Local\Temp\gh_1o41.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_1o41.s
Source File: iedConstDA.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		iedTree/iedConstDA.c -o iedTree/iedConstDA.o

                      12 ;Source File:   iedTree/iedConstDA.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:51 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "iedConstDA.h"


                      23 ;2: 


                      24 ;3: 


                      25 ;4: #include "iedControlModel.h"


                      26 ;5: #include "../iedmodel.h"


                      27 ;6: 


                      28 ;7: 


                      29 ;8: bool IEDConstDA_init(IEDEntity entity, BufferView* ber)


                      30 	.text

                      31 	.align	4

                      32 IEDConstDA_init::

00000000 e92d4030     33 	stmfd	[sp]!,{r4-r5,lr}

                      34 ;9: {


                      35 

                      36 ;10:     // Некоторые константы на самом деле не константы,


                      37 ;11:     // а части модели управления


                      38 ;12:     if( StringView_cmpCStr(&entity->name, "orIdent") == 0 )


                      39 

00000004 e1a04000     40 	mov	r4,r0

00000008 e1a05001     41 	mov	r5,r1

0000000c e28f1000*    42 	adr	r1,.L93

00000010 e2840048     43 	add	r0,r4,72

00000014 eb000000*    44 	bl	StringView_cmpCStr

00000018 e3500000     45 	cmp	r0,0

                      46 ;13:     {


                      47 

                      48 ;14:         return IEDOrIdent_init(entity);


                      49 

0000001c 01a00004     50 	moveq	r0,r4


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1o41.s
00000020 08bd4030     51 	ldmeqfd	[sp]!,{r4-r5,lr}

00000024 0a000000*    52 	beq	IEDOrIdent_init

                      53 ;15:     }


                      54 ;16:     else if(StringView_cmpCStr(&entity->name, "orCat") == 0)


                      55 

00000028 e28f1000*    56 	adr	r1,.L94

0000002c e2840048     57 	add	r0,r4,72

00000030 eb000000*    58 	bl	StringView_cmpCStr

00000034 e3500000     59 	cmp	r0,0

                      60 ;17:     {


                      61 

                      62 ;18:         return IEDOrCat_init(entity);


                      63 

00000038 01a00004     64 	moveq	r0,r4

0000003c 08bd4030     65 	ldmeqfd	[sp]!,{r4-r5,lr}

00000040 0a000000*    66 	beq	IEDOrCat_init

                      67 ;19:     }


                      68 ;20:     else if(StringView_cmpCStr(&entity->name, "ctlNum") == 0)


                      69 

00000044 e28f1000*    70 	adr	r1,.L95

00000048 e2840048     71 	add	r0,r4,72

0000004c eb000000*    72 	bl	StringView_cmpCStr

00000050 e3500000     73 	cmp	r0,0

                      74 ;21:     {


                      75 

                      76 ;22:         return IEDCtlNum_init(entity, ber);


                      77 

00000054 01a01005     78 	moveq	r1,r5

00000058 01a00004     79 	moveq	r0,r4

0000005c 08bd4030     80 	ldmeqfd	[sp]!,{r4-r5,lr}

00000060 0a000000*    81 	beq	IEDCtlNum_init

                      82 ;23:     }


                      83 ;24: 


                      84 ;25:     entity->type = IED_ENTITY_DA_CONST;    


                      85 

00000064 e3a00007     86 	mov	r0,7

00000068 e5840050     87 	str	r0,[r4,80]

                      88 ;26:     entity->readOnly = true;


                      89 

0000006c e3a00001     90 	mov	r0,1

00000070 e5c40022     91 	strb	r0,[r4,34]

                      92 ;27: 


                      93 ;28:     //Сохраняем позицию константы в информационной модели


                      94 ;29:     entity->extInfo = (void*)(ber->p + ber->pos - iedModel);


                      95 

00000074 e895000c     96 	ldmfd	[r5],{r2-r3}

00000078 e0831002     97 	add	r1,r3,r2

0000007c e59f3024*    98 	ldr	r3,.L96

00000080 e5932000     99 	ldr	r2,[r3]

00000084 e0411002    100 	sub	r1,r1,r2

00000088 e5841058    101 	str	r1,[r4,88]

                     102 ;30:     return true;


                     103 

0000008c e8bd8030    104 	ldmfd	[sp]!,{r4-r5,pc}

                     105 	.endf	IEDConstDA_init

                     106 	.align	4

                     107 ;.L64	.L69	static

                     108 ;.L65	.L70	static

                     109 ;.L66	.L71	static

                     110 

                     111 ;entity	r4	param


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1o41.s
                     112 ;ber	r5	param

                     113 

                     114 	.section ".bss","awb"

                     115 .L63:

                     116 	.data

                     117 	.text

                     118 

                     119 ;31: }


                     120 	.align	4

                     121 .L93:

                     122 ;	"orIdent\000"

00000090 6449726f    123 	.data.b	111,114,73,100

00000094 00746e65    124 	.data.b	101,110,116,0

                     125 	.align 4

                     126 

                     127 	.type	.L93,$object

                     128 	.size	.L93,4

                     129 

                     130 .L94:

                     131 ;	"orCat\000"

00000098 6143726f    132 	.data.b	111,114,67,97

0000009c 0074       133 	.data.b	116,0

0000009e 0000       134 	.align 4

                     135 

                     136 	.type	.L94,$object

                     137 	.size	.L94,4

                     138 

                     139 .L95:

                     140 ;	"ctlNum\000"

000000a0 4e6c7463    141 	.data.b	99,116,108,78

000000a4 6d75       142 	.data.b	117,109

000000a6 00         143 	.data.b	0

000000a7 00         144 	.align 4

                     145 

                     146 	.type	.L95,$object

                     147 	.size	.L95,4

                     148 

                     149 .L96:

000000a8 00000000*   150 	.data.w	iedModel

                     151 	.type	.L96,$object

                     152 	.size	.L96,4

                     153 

                     154 	.align	4

                     155 ;iedModel	iedModel	import

                     156 

                     157 	.data

                     158 	.ghsnote version,6

                     159 	.ghsnote tools,3

                     160 	.ghsnote options,0

                     161 	.text

                     162 	.align	4

