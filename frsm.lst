                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o81.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=frsm.c -o gh_9o81.o -list=frsm.lst C:\Users\<USER>\AppData\Local\Temp\gh_9o81.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_9o81.s
Source File: frsm.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile frsm.c -o

                      11 ;		frsm.o

                      12 ;Source File:   frsm.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:54 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "frsm.h"


                      23 ;2: 


                      24 ;3: 


                      25 ;4: FRSM g_FRSM;


                      26 ;5: 


                      27 ;6: //ID для одиночного FRSM


                      28 ;7: #define SOLITARY_FRSM_ID 0x55


                      29 ;8: 


                      30 ;9: static void frsm_lock()


                      31 

                      32 ;11: 


                      33 ;12: }


                      34 

                      35 ;13: 


                      36 ;14: static void frsm_unlock()


                      37 

                      38 ;16: 


                      39 ;17: }


                      40 

                      41 ;18: 


                      42 ;19: bool frsm_init(void)


                      43 	.text

                      44 	.align	4

                      45 frsm_init::

                      46 ;20: {


                      47 

                      48 ;21:     g_FRSM.busy = FALSE;


                      49 

00000000 e59f1074*    50 	ldr	r1,.L69


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o81.s
00000004 e3a00000     51 	mov	r0,0

00000008 e5c10000     52 	strb	r0,[r1]

                      53 ;22:     return TRUE;


                      54 

0000000c e3a00001     55 	mov	r0,1

00000010 e12fff1e*    56 	ret	

                      57 	.endf	frsm_init

                      58 	.align	4

                      59 

                      60 	.section ".bss","awb"

                      61 .L62:

                      62 	.data

                      63 	.text

                      64 

                      65 ;23: }


                      66 

                      67 ;24: 


                      68 ;25: bool frsm_alloc(uint32_t* id)


                      69 	.align	4

                      70 	.align	4

                      71 frsm_alloc::

                      72 ;26: {


                      73 

                      74 ;27:     frsm_lock();


                      75 

                      76 ;10: {


                      77 

                      78 ;28:     if (g_FRSM.busy)


                      79 

00000014 e59f1060*    80 	ldr	r1,.L69

00000018 e5d12000     81 	ldrb	r2,[r1]

0000001c e3520000     82 	cmp	r2,0

                      83 ;29:     {


                      84 

                      85 ;30:         frsm_unlock();


                      86 

                      87 ;15: {


                      88 

                      89 ;31:         return FALSE;


                      90 

00000020 13a00000     91 	movne	r0,0

                      92 ;32:     }


                      93 ;33:     *id = SOLITARY_FRSM_ID;


                      94 

00000024 03a02055     95 	moveq	r2,85

00000028 05802000     96 	streq	r2,[r0]

                      97 ;34:     g_FRSM.busy = TRUE;


                      98 

0000002c 03a00001     99 	moveq	r0,1

00000030 05c10000    100 	streqb	r0,[r1]

                     101 ;35:     frsm_unlock();


                     102 

                     103 ;15: {


                     104 

                     105 ;36:     return TRUE;


                     106 

00000034 e12fff1e*   107 	ret	

                     108 	.endf	frsm_alloc

                     109 	.align	4

                     110 

                     111 ;id	r0	param


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o81.s
                     112 

                     113 	.section ".bss","awb"

                     114 .L118:

                     115 	.data

                     116 	.text

                     117 

                     118 ;37: }


                     119 

                     120 ;38: 


                     121 ;39: bool frsm_free(uint32_t id)


                     122 	.align	4

                     123 	.align	4

                     124 frsm_free::

00000038 e92d4000    125 	stmfd	[sp]!,{lr}

0000003c e24dd004    126 	sub	sp,sp,4

00000040 e1a0100d    127 	mov	r1,sp

00000044 eb000006*   128 	bl	frsm_getById

                     129 ;40: {


                     130 

                     131 ;41:     FRSM* frsm;


                     132 ;42:     frsm_lock();


                     133 

                     134 ;10: {


                     135 

                     136 ;43: 


                     137 ;44:     if (!frsm_getById(id, &frsm))


                     138 

00000048 e3500000    139 	cmp	r0,0

                     140 ;48:     }


                     141 ;49: 


                     142 ;50:     frsm->busy = FALSE;


                     143 

0000004c 159d0000    144 	ldrne	r0,[sp]

00000050 13a01000    145 	movne	r1,0

00000054 15c01000    146 	strneb	r1,[r0]

                     147 ;51:     frsm_unlock();


                     148 

                     149 ;15: {


                     150 

                     151 ;52:     return TRUE;


                     152 

00000058 13a00001    153 	movne	r0,1

                     154 ;45:     {


                     155 

                     156 ;46:         frsm_unlock();


                     157 

                     158 ;15: {


                     159 

                     160 ;47:         return FALSE;


                     161 

0000005c e28dd004    162 	add	sp,sp,4

00000060 e8bd8000    163 	ldmfd	[sp]!,{pc}

                     164 	.endf	frsm_free

                     165 	.align	4

                     166 ;frsm	[sp]	local

                     167 

                     168 ;id	none	param

                     169 

                     170 	.section ".bss","awb"

                     171 .L197:

                     172 	.data


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9o81.s
                     173 	.text

                     174 

                     175 ;53: }


                     176 

                     177 ;54: 


                     178 ;55: bool frsm_getById(uint32_t id, FRSM** frsm)


                     179 	.align	4

                     180 	.align	4

                     181 frsm_getById::

                     182 ;56: {


                     183 

                     184 ;57:     if (id != SOLITARY_FRSM_ID)


                     185 

00000064 e3500055    186 	cmp	r0,85

                     187 ;58:     {


                     188 

                     189 ;59:         return FALSE;


                     190 

00000068 13a00000    191 	movne	r0,0

                     192 ;60:     }


                     193 ;61:     *frsm = &g_FRSM;


                     194 

0000006c 059f0008*   195 	ldreq	r0,.L69

00000070 05810000    196 	streq	r0,[r1]

                     197 ;62:     return TRUE;


                     198 

00000074 03a00001    199 	moveq	r0,1

00000078 e12fff1e*   200 	ret	

                     201 	.endf	frsm_getById

                     202 	.align	4

                     203 

                     204 ;id	r0	param

                     205 ;frsm	r1	param

                     206 

                     207 	.section ".bss","awb"

                     208 .L246:

                     209 	.data

                     210 	.text

                     211 

                     212 ;63: }


                     213 	.align	4

                     214 .L69:

0000007c 00000000*   215 	.data.w	g_FRSM

                     216 	.type	.L69,$object

                     217 	.size	.L69,4

                     218 

                     219 	.align	4

                     220 

                     221 	.data

                     222 	.comm	g_FRSM,28,4

                     223 	.type	g_FRSM,$object

                     224 	.size	g_FRSM,28

                     225 	.ghsnote version,6

                     226 	.ghsnote tools,3

                     227 	.ghsnote options,0

                     228 	.text

                     229 	.align	4

