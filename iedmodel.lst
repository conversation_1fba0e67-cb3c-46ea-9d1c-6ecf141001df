                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedmodel.c -o gh_3lk1.o -list=iedmodel.lst C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
Source File: iedmodel.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile iedmodel.c -o

                      11 ;		iedmodel.o

                      12 ;Source File:   iedmodel.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:05 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "iedmodel.h"


                      23 ;2: #include "AsnEncoding.h"


                      24 ;3: #include "MmsConst.h"


                      25 ;4: #include "mms_data.h"


                      26 ;5: #include "mms_rcb.h"


                      27 ;6: #include "mms_gocb.h"


                      28 ;7: #include "bufViewBER.h"


                      29 ;8: #include "tools.h"


                      30 ;9: #include "IEDCompile/InnerAttributeTypes.h"


                      31 ;10: #include <debug.h>


                      32 ;11: #include <string.h>


                      33 ;12: #include <stdbool.h>


                      34 ;13: 


                      35 ;14: static int encodeStructAccessAttrs(uint8_t* outBuf, int bufPos, int objectPos,


                      36 ;15:     bool determineSize, bool topStruct);


                      37 ;16: 


                      38 ;17: // Нужно писать перед байтовыми массивами, потому что


                      39 ;18: // без этого иногда собирается падающая программа.


                      40 ;19: // Возможно, баг postlink


                      41 ;20: #pragma alignvar (4)


                      42 ;21: 


                      43 ;22: unsigned char defaultIed[] = {


                      44 ;23:         0xea, 0x3a, 0x1a, 0x03, 0x49, 0x45, 0x44, 0xe2,


                      45 ;24:         0x33, 0x1a, 0x07, 0x4c, 0x44, 0x65, 0x76, 0x69,


                      46 ;25:         0x63, 0x65, 0xe4, 0x28, 0x1a, 0x05, 0x4d, 0x4d,


                      47 ;26:         0x58, 0x55, 0x30, 0xe6, 0x1f, 0x1a, 0x04, 0x54,


                      48 ;27:         0x6f, 0x74, 0x57, 0xe8, 0x17, 0x1a, 0x03, 0x6d,


                      49 ;28:         0x61, 0x67, 0xe9, 0x10, 0x1a, 0x01, 0x66, 0x02,


                      50 ;29:         0x01, 0x02, 0x04, 0x08, 0xc0, 0x00, 0x00, 0x00,



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                      51 ;30:         0xcf, 0x1a, 0x31, 0x35


                      52 ;31: };


                      53 ;32: 


                      54 ;33: unsigned char* iedModel = defaultIed;


                      55 ;34: int iedModelSize = sizeof(defaultIed);


                      56 ;35: 


                      57 ;36: void setIedModel(unsigned char* pModel, int modelSize)


                      58 ;37: {


                      59 ;38:     iedModel = pModel;


                      60 ;39:     iedModelSize = modelSize;


                      61 ;40: }


                      62 ;41: 


                      63 ;42: int readTL(int pos, uint8_t* pTag, int* pLen, int* pFullLen)


                      64 ;43: {


                      65 ;44:     BufferView bv;


                      66 ;45: 


                      67 ;46:     bv.len = iedModelSize;


                      68 ;47:     bv.p = iedModel;


                      69 ;48:     bv.pos = pos;


                      70 ;49: 


                      71 ;50:     if (BerDecoder_decodeTLFromBufferView(&bv, pTag, pLen, pFullLen))


                      72 ;51:     {


                      73 ;52:         return bv.pos;


                      74 ;53:     }


                      75 ;54: 


                      76 ;55:     return 0;


                      77 ;56: }


                      78 ;57: 


                      79 ;58: int skipObject(int pos)


                      80 ;59: {


                      81 ;60:     int len;


                      82 ;61:     //пропускаем тэг


                      83 ;62:     pos++;


                      84 ;63:     //определяем и пропускаем длину


                      85 ;64:     pos = BerDecoder_decodeLength(iedModel, &len, pos, iedModelSize);


                      86 ;65:     if( pos <= 0)


                      87 ;66:     {


                      88 ;67:         return 0;


                      89 ;68:     }


                      90 ;69:     //пропускаем содержимое


                      91 ;70:     pos+=len;


                      92 ;71:     return pos;


                      93 ;72: }


                      94 ;73: 


                      95 ;74: //Возвращает полную длину строки (вместе с тегом и длиной),


                      96 ;75: // закодированной BER(например, VisibleString)


                      97 ;76: //Работает только с тэгом в один байт


                      98 ;77: // При ошибке возвращает 0


                      99 ;78: int getBerStringLength(int berStringPos)


                     100 ;79: {


                     101 ;80:     int length;


                     102 ;81:     int newPos;


                     103 ;82:     berStringPos++;//Пропускаем тэг


                     104 ;83:     newPos = BerDecoder_decodeLength(iedModel, &length, berStringPos ,


                     105 ;84:                                      iedModelSize);


                     106 ;85:     if(newPos < 1)


                     107 ;86:     {


                     108 ;87:         return 0;


                     109 ;88:     }


                     110 ;89:     else


                     111 ;90:     {



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                     112 ;91:         return 1 + (newPos - berStringPos) + length;


                     113 ;92:     }


                     114 ;93: }


                     115 ;94: 


                     116 ;95: // Возвращает имя объекта IED из описания информационной модели


                     117 ;96: // в виде BER VisibleString.


                     118 ;97: // Результат складывается в buf, размер результата возвращается.


                     119 ;98: // pObjPos принимает позицию объекта, и возвращает позицию,


                     120 ;99: // следующую за объектом.


                     121 ;100: // Если buf == NULL, то функция не пишет в buf (используется


                     122 ;101: // для определения полного размера имени).


                     123 ;102: // При ошибке возвращает 0


                     124 ;103: int getIEDObjectNameString(int *pObjPos, unsigned char* buf)


                     125 ;104: {


                     126 ;105:     int objLen;


                     127 ;106:     int nameLen;


                     128 ;107:     int namePos;


                     129 ;108:     int pos = *pObjPos;


                     130 ;109:     pos++;//Пропускаем тэг


                     131 ;110:     //Получаем длину объекта и позицию имени.


                     132 ;111:     namePos = BerDecoder_decodeLength(iedModel, &objLen, pos , iedModelSize);


                     133 ;112:     if(namePos < 1)


                     134 ;113:     {


                     135 ;114:         return 0;


                     136 ;115:     }


                     137 ;116: 


                     138 ;117:     nameLen = getBerStringLength(namePos);


                     139 ;118:     if( !nameLen )


                     140 ;119:     {


                     141 ;120:         return 0;


                     142 ;121:     }


                     143 ;122: 


                     144 ;123:     if(buf != NULL)


                     145 ;124:     {


                     146 ;125:         memcpy(buf, iedModel + namePos, nameLen);


                     147 ;126:     }


                     148 ;127:     *pObjPos = namePos + objLen;


                     149 ;128:     return nameLen;


                     150 ;129: }


                     151 ;130: 


                     152 ;131: bool getObjectName(int objPos, StringView* result)


                     153 ;132: {


                     154 ;133:     uint8_t nameTag;


                     155 ;134:     int nameLen;


                     156 ;135:     int namePos;


                     157 ;136:     int objectLen;


                     158 ;137: 


                     159 ;138:     //Пропускаем тэг и длину


                     160 ;139:     int pos = readTL(objPos, NULL, &objectLen, NULL);


                     161 ;140:     if(!pos)


                     162 ;141:     {


                     163 ;142:         return FALSE;


                     164 ;143:     }


                     165 ;144:     if (objectLen == 0)


                     166 ;145:     {


                     167 ;146:         ERROR_REPORT("Empty object");


                     168 ;147:         return FALSE;


                     169 ;148:     }


                     170 ;149: 


                     171 ;150:     namePos = readTL(pos, &nameTag, &nameLen, NULL);


                     172 ;151: 



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                     173 ;152:     if(!namePos || nameTag != ASN_VISIBLE_STRING)


                     174 ;153:     {


                     175 ;154:         return FALSE;


                     176 ;155:     }


                     177 ;156: 


                     178 ;157: 


                     179 ;158:     StringView_init(result, (char*)iedModel + namePos, nameLen);


                     180 ;159: 


                     181 ;160:     return TRUE;


                     182 ;161: }


                     183 ;162: 


                     184 ;163: bool IEDModel_isServiceInfo(uint8_t tag)


                     185 ;164: {


                     186 ;165:     return tag == ASN_VISIBLE_STRING


                     187 ;166:             || tag ==  IED_GSE_LIST


                     188 ;167:             || tag == IED_CONTROL_INFO


                     189 ;168:             || tag == IED_OBJ_FLAGS;


                     190 ;169: }


                     191 ;170: 


                     192 ;171: int getSubObjectsPos(int rootObjPos, int* pEndPos)


                     193 ;172: {


                     194 ;173:     int endPos;


                     195 ;174:     int rootObjLen;


                     196 ;175:     int pos = rootObjPos;


                     197 ;176:     //Пропускаем тэг корневого объекта


                     198 ;177:     pos++;


                     199 ;178: 


                     200 ;179:     //Получаем длину объекта


                     201 ;180:     pos = BerDecoder_decodeLength(iedModel, &rootObjLen, pos, iedModelSize);


                     202 ;181:     if( pos <= 0)


                     203 ;182:     {


                     204 ;183:         return 0;


                     205 ;184:     }


                     206 ;185: 


                     207 ;186:     //Получаем позицию конца объекта


                     208 ;187:     endPos = pos + rootObjLen;


                     209 ;188:     if(endPos > iedModelSize)


                     210 ;189:     {


                     211 ;190:         //Объект неправдоподобно большой


                     212 ;191:         return 0;


                     213 ;192:     }


                     214 ;193:     *pEndPos = endPos;


                     215 ;194: 


                     216 ;195:     if (pos >= endPos)


                     217 ;196:     {


                     218 ;197:         //Пустой объект


                     219 ;198:         return pos;


                     220 ;199:     }


                     221 ;200: 


                     222 ;201:     //Пропускаем служебную информацию


                     223 ;202:     while (pos < endPos)


                     224 ;203:     {


                     225 ;204:         if(IEDModel_isServiceInfo(iedModel[pos]))


                     226 ;205:         {


                     227 ;206:             pos = skipObject(pos);


                     228 ;207:             if (pos == 0)


                     229 ;208:             {


                     230 ;209:                 return 0;


                     231 ;210:             }


                     232 ;211:         }


                     233 ;212:         else



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                     234 ;213:         {


                     235 ;214:             return pos;


                     236 ;215:         }


                     237 ;216:     }


                     238 ;217: 


                     239 ;218:     //Пустой объект


                     240 ;219:     return pos;


                     241 ;220: }


                     242 ;221: 


                     243 ;222: int findObjectBySimpleName(int rootObjPos, unsigned char* name, int argNameLen)


                     244 ;223: {


                     245 ;224:     int objPos;


                     246 ;225:     int endPos;


                     247 ;226: 


                     248 ;227:     objPos = getSubObjectsPos(rootObjPos, &endPos);


                     249 ;228:     if( objPos == 0)


                     250 ;229:     {


                     251 ;230:         return 0;


                     252 ;231:     }


                     253 ;232: 


                     254 ;233:     while (objPos < endPos)


                     255 ;234:     {


                     256 ;235:         int nameCompareResult;


                     257 ;236:         int nameLen;


                     258 ;237:         int objLen;


                     259 ;238:         int namePos;


                     260 ;239:         int nextObjPos;


                     261 ;240:         int pos = objPos;


                     262 ;241:         pos++;//Пропускаем тэг


                     263 ;242:         //Получаем длину объекта и позицию имени.


                     264 ;243:         namePos = BerDecoder_decodeLength(iedModel, &objLen, pos, iedModelSize);


                     265 ;244:         if (namePos < 1)


                     266 ;245:         {


                     267 ;246:             return 0;


                     268 ;247:         }


                     269 ;248:         if (iedModel[namePos] != ASN_VISIBLE_STRING)


                     270 ;249:         {


                     271 ;250:             //Не найдено имя объекта


                     272 ;251:             return 0;


                     273 ;252:         }


                     274 ;253:         nextObjPos = namePos + objLen;


                     275 ;254: 


                     276 ;255:         // Пропускаем тэг


                     277 ;256:         pos = namePos + 1;


                     278 ;257: 


                     279 ;258:         //Получаем длину имени


                     280 ;259:         //namePos в результате должен указывать на саму строку


                     281 ;260:         namePos = BerDecoder_decodeLength(iedModel, &nameLen, pos, iedModelSize);


                     282 ;261:         if (namePos < 1)


                     283 ;262:         {


                     284 ;263:             return 0;


                     285 ;264:         }


                     286 ;265:         //Сравниваем имена


                     287 ;266:         nameCompareResult = memcmp(name, iedModel + namePos, nameLen);


                     288 ;267:         if (nameLen == argNameLen &&  nameCompareResult == 0)


                     289 ;268:         {


                     290 ;269:             return objPos;


                     291 ;270:         }


                     292 ;271:         objPos = nextObjPos;


                     293 ;272:     }


                     294 ;273: 



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                     295 ;274:     //debugSendText("\t!!!!!!!!!!Object is not found");


                     296 ;275:     return 0;


                     297 ;276: }


                     298 ;277: 


                     299 ;278: int findObjectByTag(int rootObjPos, uint8_t tagToFind)


                     300 ;279: {


                     301 ;280:     int objPos;


                     302 ;281:     int endPos;


                     303 ;282: 


                     304 ;283:     objPos = getSubObjectsPos(rootObjPos, &endPos);


                     305 ;284:     if( objPos == 0)


                     306 ;285:     {


                     307 ;286:         return 0;


                     308 ;287:     }


                     309 ;288: 


                     310 ;289:     while (objPos < endPos)


                     311 ;290:     {


                     312 ;291:         int objLen;


                     313 ;292:         int pos = objPos;


                     314 ;293:         uint8_t tag = iedModel[pos];


                     315 ;294:         if(tag == tagToFind)


                     316 ;295:         {


                     317 ;296:             return pos;


                     318 ;297:         }


                     319 ;298:         pos++;


                     320 ;299:         //Получаем длину объекта и позицию имени.


                     321 ;300:         pos = BerDecoder_decodeLength(iedModel, &objLen, pos, iedModelSize);


                     322 ;301:         if (pos < 1)


                     323 ;302:         {


                     324 ;303:             ERROR_REPORT("Unable to decode length");


                     325 ;304:             return 0;


                     326 ;305:         }


                     327 ;306:         objPos = pos + objLen;


                     328 ;307:     }


                     329 ;308: 


                     330 ;309:     ERROR_REPORT("Object tag is not found");


                     331 ;310:     return 0;


                     332 ;311: }


                     333 ;312: 


                     334 ;313: int findDomainSection(int section, uint8_t* domainId, int domainIdLen)


                     335 ;314: {


                     336 ;315:     int vmdPos = findObjectBySimpleName(0, domainId, domainIdLen);


                     337 ;316:     if (vmdPos == 0)


                     338 ;317:     {


                     339 ;318:         return 0;


                     340 ;319:     }


                     341 ;320:     return findObjectByTag(vmdPos, section);


                     342 ;321: }


                     343 ;322: 


                     344 ;323: // Длина первого простого в сложном имени до разделителя или конца строки


                     345 ;324: int getSimpleNameLen(uint8_t* name, int fullNameLen, bool* delimiterFound)


                     346 ;325: {


                     347 ;326:     int len = 0;


                     348 ;327:     *delimiterFound = FALSE;


                     349 ;328:     while (len < fullNameLen)


                     350 ;329:     {


                     351 ;330:         if (name[len] == '$')


                     352 ;331:         {


                     353 ;332:             *delimiterFound = TRUE;


                     354 ;333:             return len;


                     355 ;334:         }



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                     356 ;335:         len++;


                     357 ;336:     }


                     358 ;337:     return len;


                     359 ;338: }


                     360 ;339: 


                     361 ;340: int findObjectByPath(int rootObjPos, uint8_t* name, int fullNameLen)


                     362 ;341: {


                     363 ;342:     int objPos = rootObjPos;


                     364 ;343:     while (fullNameLen != 0)


                     365 ;344:     {


                     366 ;345:         bool delimiterfound;


                     367 ;346:         int simpleNameLen = getSimpleNameLen(name, fullNameLen,


                     368 ;347:             &delimiterfound);


                     369 ;348:         objPos = findObjectBySimpleName(objPos, name, simpleNameLen);


                     370 ;349:         if (objPos == 0)


                     371 ;350:         {


                     372 ;351:             return 0;


                     373 ;352:         }


                     374 ;353: 


                     375 ;354:         if (delimiterfound)


                     376 ;355:         {


                     377 ;356:             simpleNameLen++;


                     378 ;357:         }


                     379 ;358: 


                     380 ;359:         name += simpleNameLen;


                     381 ;360:         fullNameLen -= simpleNameLen;


                     382 ;361:     }


                     383 ;362:     return objPos;


                     384 ;363: }


                     385 ;364: 


                     386 ;365: int findObjectByFullName(int section, StringView* domainName, StringView* objectName)


                     387 ;366: {


                     388 ;367:     int sectionPos;


                     389 ;368:     int objectPos;


                     390 ;369:     sectionPos = findDomainSection(section, (uint8_t*)domainName->p, domainName->len);


                     391 ;370:     if(sectionPos == 0)


                     392 ;371:     {


                     393 ;372:         return 0;


                     394 ;373:     }


                     395 ;374:     objectPos = findObjectByPath(sectionPos, (uint8_t*)objectName->p, objectName->len);


                     396 ;375:     if(objectPos == 0)


                     397 ;376:     {


                     398 ;377:         return 0;


                     399 ;378:     }


                     400 ;379:     return objectPos;


                     401 ;380: }


                     402 ;381: 


                     403 ;382: int getDataSetByPath(StringView* pDatasetPath)


                     404 ;383: {


                     405 ;384:     StringView domainName;


                     406 ;385:     StringView objectName;


                     407 ;386:     int dataSetPos;


                     408 ;387: 


                     409 ;388:     if(! StringView_splitChar(pDatasetPath, '/', &domainName, &objectName))


                     410 ;389:     {


                     411 ;390:         ERROR_REPORT("Unable to split dataset name");


                     412 ;391:         return 0;


                     413 ;392:     }


                     414 ;393: 


                     415 ;394:     dataSetPos = findObjectByFullName(IED_VMD_DATA_SET_SECTION,


                     416 ;395:                                       &domainName, &objectName);



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                     417 ;396:     if(dataSetPos == 0)


                     418 ;397:     {


                     419 ;398:         ERROR_REPORT("Unable to find dataset");


                     420 ;399:         return 0;


                     421 ;400:     }


                     422 ;401: 


                     423 ;402:     return dataSetPos;


                     424 ;403: }


                     425 ;404: 


                     426 ;405: void writeChildrenNames(int rootObjPos, DomainNameWriter* writer,


                     427 ;406:     bool recursive, int objectsTagToWrite)


                     428 ;407: {


                     429 ;408:     int objEndPos;


                     430 ;409:     int childObjectPos = getSubObjectsPos(rootObjPos, &objEndPos);


                     431 ;410:     if (childObjectPos == 0)


                     432 ;411:     {


                     433 ;412:         //Подобъектов нет или ошибка.


                     434 ;413:         //Это нормальное завешение функции для IED_DA_FINAL, например.


                     435 ;414:         return;


                     436 ;415:     }


                     437 ;416:     while (childObjectPos < objEndPos)


                     438 ;417:     {


                     439 ;418:         int subObjLen;


                     440 ;419:         int nameLen;


                     441 ;420:         int namePos;


                     442 ;421:         int objContentPos;


                     443 ;422:         int pos = childObjectPos;


                     444 ;423:         uint8_t tag = iedModel[pos++];


                     445 ;424:         //Позиция имени в формате ber и размер подобъекта


                     446 ;425:         namePos = BerDecoder_decodeLength(iedModel, &subObjLen, pos,


                     447 ;426:             iedModelSize);


                     448 ;427:         objContentPos = namePos;


                     449 ;428:         if (namePos < 1)


                     450 ;429:         {


                     451 ;430:             //Если ошибка, считаем что больше детей нет


                     452 ;431:             return;


                     453 ;432:         }


                     454 ;433:         if (iedModel[namePos++] != ASN_VISIBLE_STRING)


                     455 ;434:         {


                     456 ;435:             //Если ошибка, считаем что больше детей нет


                     457 ;436:             return;


                     458 ;437:         }


                     459 ;438:         //Начало непосредственно строки имени и длина этой строки


                     460 ;439:         namePos = BerDecoder_decodeLength(iedModel, &nameLen, namePos,


                     461 ;440:             iedModelSize);


                     462 ;441:         if (namePos < 1)


                     463 ;442:         {


                     464 ;443:             //Если ошибка, считаем что больше детей нет


                     465 ;444:             return;


                     466 ;445:         }


                     467 ;446:         DomainNameWriter_pushName(writer, iedModel + namePos, nameLen);


                     468 ;447:         if (objectsTagToWrite == IED_ANY_TAG || objectsTagToWrite == tag)


                     469 ;448:         {


                     470 ;449:             DomainNameWriter_encode(writer);


                     471 ;450:         }


                     472 ;451:         if(recursive)


                     473 ;452:         {


                     474 ;453:             writeChildrenNames(childObjectPos, writer, TRUE, objectsTagToWrite);


                     475 ;454:         }


                     476 ;455:         DomainNameWriter_discardName(writer);


                     477 ;456:         childObjectPos = objContentPos + subObjLen;



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                     478 ;457:     }


                     479 ;458: }


                     480 ;459: 


                     481 ;460: int encodeReadConst(uint8_t* outBuf, int bufPos, int constObjPos, bool determineSize)


                     482 ;461: {


                     483 ;462:     //Размер длины и тэга вместе


                     484 ;463:     int sizeOfTL;


                     485 ;464: 


                     486 ;465:     int fullSize;


                     487 ;466:     int objectSize;


                     488 ;467:     uint8_t constTypeTag;


                     489 ;468:     uint8_t mmsTag;


                     490 ;469:     //Пропускаем тэг


                     491 ;470:     int pos = constObjPos +1;


                     492 ;471:     pos = BerDecoder_decodeLength(iedModel, &objectSize, pos, iedModelSize);


                     493 ;472:     sizeOfTL = pos - constObjPos;


                     494 ;473:     fullSize = sizeOfTL + objectSize;


                     495 ;474:     if(determineSize)


                     496 ;475:     {


                     497 ;476:         return fullSize;


                     498 ;477:     }


                     499 ;478: 


                     500 ;479:     memcpy(outBuf + bufPos, iedModel + constObjPos, fullSize);


                     501 ;480: 


                     502 ;481:     constTypeTag = outBuf[bufPos];


                     503 ;482: 


                     504 ;483:     if ((constTypeTag & BER_TAG_CLASS_MASK) != BER_CONTEXT_SPECIFIC)


                     505 ;484:     {


                     506 ;485:         ERROR_REPORT("Invalid constant tag");


                     507 ;486:         return 0;


                     508 ;487:     }


                     509 ;488:     constTypeTag &= ~BER_TAG_CLASS_MASK;


                     510 ;489: 


                     511 ;490:     switch (constTypeTag)


                     512 ;491:     {


                     513 ;492:     case IEC61850_BOOLEAN:


                     514 ;493:         mmsTag = IEC61850_BER_BOOLEAN;


                     515 ;494:         break;


                     516 ;495:     case IEC61850_INT32:


                     517 ;496:     case IEC61850_INT64:


                     518 ;497:     case IEC61850_ENUMERATED:


                     519 ;498:         mmsTag = IEC61850_BER_INTEGER;


                     520 ;499:         break;


                     521 ;500:     case IEC61850_INT8U:


                     522 ;501:     case IEC61850_INT16U:


                     523 ;502:     case IEC61850_INT32U:


                     524 ;503:         mmsTag = IEC61850_BER_UNSIGNED_INTEGER;


                     525 ;504:         break;


                     526 ;505:     case IEC61850_VISIBLE_STRING_32:


                     527 ;506:     case IEC61850_VISIBLE_STRING_64:


                     528 ;507:     case IEC61850_VISIBLE_STRING_65:


                     529 ;508:     case IEC61850_VISIBLE_STRING_129:


                     530 ;509:     case IEC61850_VISIBLE_STRING_255:


                     531 ;510:         mmsTag = IEC61850_BER_VISIBLE_STRING;


                     532 ;511:         break;


                     533 ;512:     case IEC61850_UNICODE_STRING_255:


                     534 ;513:         mmsTag = IEC61850_BER_MMS_STRING;


                     535 ;514:         break;


                     536 ;515:     case IEC61850_OCTET_STRING_6:


                     537 ;516:     case IEC61850_OCTET_STRING_64:


                     538 ;517:         mmsTag = IEC61850_BER_OCTET_STRING;



                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                     539 ;518:         break;


                     540 ;519:     case IEC61850_GENERIC_BITSTRING:


                     541 ;520:     case IEC61850_QUALITY:


                     542 ;521:         mmsTag = IEC61850_BER_BIT_STRING;


                     543 ;522:         break;


                     544 ;523:     case IEC61850_ENTRY_TIME:


                     545 ;524:         mmsTag = IEC61850_BER_BINARY_TIME;


                     546 ;525:         break;


                     547 ;526:     default:


                     548 ;527:         //Если неизвестно что, то пусть будет строка


                     549 ;528:         ERROR_REPORT("Unknown constant tag %02X", constTypeTag);


                     550 ;529:         mmsTag = IEC61850_BER_VISIBLE_STRING;


                     551 ;530:     }


                     552 ;531:     outBuf[bufPos] = mmsTag;


                     553 ;532:     return bufPos + fullSize;


                     554 ;533: }


                     555 ;534: 


                     556 ;535: uint8_t* getAlignedDescrStruct(int pos)


                     557 ;536: {


                     558 ;537:     //Какое смещение использовано для выравнивания структуры описания


                     559 ;538:     uint8_t* pDescrStructAlignOffset;


                     560 ;539:     uint8_t descrTag;


                     561 ;540:     int descrLen;


                     562 ;541:     //Получаем указатель на структуру описания


                     563 ;542:     descrTag = iedModel[pos++];


                     564 ;543:     if (descrTag != ASN_OCTET_STRING)


                     565 ;544:     {


                     566 ;545:         return NULL;


                     567 ;546:     };


                     568 ;547:     pos = BerDecoder_decodeLength(iedModel, &descrLen, pos, iedModelSize);


                     569 ;548:     if (pos == -1)


                     570 ;549:     {


                     571 ;550:         return NULL;


                     572 ;551:     }


                     573 ;552: 


                     574 ;553:     //Получаем структуру описания с учётом выравнивания


                     575 ;554:     pDescrStructAlignOffset = iedModel + pos;


                     576 ;555:     RET_IF_NOT(*pDescrStructAlignOffset < 4, "Invalid alignment");


                     577 ;556:     return pDescrStructAlignOffset + *pDescrStructAlignOffset + 1;


                     578 ;557: }


                     579 ;558: 


                     580 ;559: int encodeTypeAccessAttrs(uint8_t* outBuf, int bufPos, enum InnerAttributeType type,


                     581 ;560:                     //Позиция константы или структуры для доступа к значению


                     582 ;561:                           int attrDataPos,


                     583 ;562:                           bool determineSize)


                     584 ;563: {


                     585 ;564:     switch(type)


                     586 ;565:     {


                     587 ;566:     case INNER_TYPE_CONST:


                     588 ;567:         return encodeAccessAttrConst(outBuf, bufPos, attrDataPos, determineSize);


                     589 ;568:     case INNER_TYPE_REAL_VALUE:


                     590 ;569:     case INNER_TYPE_REAL_SETT:


                     591 ;570:     case INNER_TYPE_FLOAT_VALUE:


                     592 ;571:     case INNER_TYPE_FLOAT_SETT:


                     593 ;572:         return encodeAccessAttrFloat(outBuf, bufPos, determineSize);


                     594 ;573:     case INNER_TYPE_QUALITY:


                     595 ;574:         return encodeAccessAttrQuality(outBuf, bufPos, determineSize);


                     596 ;575:     case INNER_TYPE_TIME_STAMP:


                     597 ;576:         return encodeAccessAttrTimeStamp(outBuf, bufPos, determineSize);


                     598 ;577:     case INNER_TYPE_INT32_SETTS:


                     599 ;578:     case INNER_TYPE_INT32:



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                     600 ;579:         return encodeAccessAttrInt(outBuf, bufPos, 32, determineSize);


                     601 ;580:     case INNER_TYPE_ENUMERATED:


                     602 ;581:     case INNER_TYPE_ENUMERATED_SETTS:


                     603 ;582:         return encodeAccessAttrInt(outBuf, bufPos, 8, determineSize);


                     604 ;583:     case INNER_TYPE_INT32U_SETTS:


                     605 ;584:     case INNER_TYPE_INT32U:


                     606 ;585:         return encodeAccessAttrUInt(outBuf, bufPos, 32, determineSize);


                     607 ;586:     case INNER_TYPE_INT8U:


                     608 ;587:         return encodeAccessAttrUInt(outBuf, bufPos, 8, determineSize);


                     609 ;588:     case INNER_TYPE_REAL_AS_INT64:


                     610 ;589:             return encodeAccessAttrInt(outBuf, bufPos, 64, determineSize);


                     611 ;590:     case INNER_TYPE_BOOLEAN:


                     612 ;591:         return encodeAccessAttrBoolean(outBuf, bufPos, determineSize);


                     613 ;592:     case INNER_TYPE_RCB:


                     614 ;593:         return encodeAccessAttrRCB(outBuf, bufPos, attrDataPos, determineSize);


                     615 ;594:     case INNER_TYPE_GOCB:


                     616 ;595:         return encodeAccessAttrGoCB(outBuf, bufPos, attrDataPos, determineSize);


                     617 ;596:     case INNER_TYPE_CODEDENUM:


                     618 ;597:         return encodeAccessAttrCodedEnum(outBuf, bufPos, attrDataPos,


                     619 ;598:             determineSize);


                     620 ;599:     default:


                     621 ;600:         ERROR_REPORT("Unsupported inner type %d", type);


                     622 ;601:         return 0;


                     623 ;602:     }


                     624 ;603: }


                     625 ;604: 


                     626 ;605: //Для GetValibaleAccessAttributes


                     627 ;606: //Пишет имя в буфер с тэгом 0х80


                     628 ;607: //или определяет размер


                     629 ;608: static int encodeNameAttr(uint8_t* outBuf, int bufPos, int namePos ,


                     630 ;609:                           bool determineSize)


                     631 ;610: {


                     632 ;611:     int totalLen;


                     633 ;612:     int len;


                     634 ;613:     int pos  = namePos;


                     635 ;614:     //пропускаем тэг


                     636 ;615:     pos++;


                     637 ;616:     //определяем и пропускаем длину


                     638 ;617:     pos = BerDecoder_decodeLength(iedModel, &len, pos, iedModelSize);


                     639 ;618:     if( pos <= 0)


                     640 ;619:     {


                     641 ;620:         return 0;


                     642 ;621:     }


                     643 ;622: 


                     644 ;623:     pos+=len;


                     645 ;624:     totalLen = pos - namePos;


                     646 ;625:     if(determineSize)


                     647 ;626:     {


                     648 ;627:         return totalLen;


                     649 ;628:     }


                     650 ;629:     memcpy(outBuf + bufPos, iedModel + namePos, totalLen);


                     651 ;630:     outBuf[bufPos] = 0x80;


                     652 ;631: 


                     653 ;632:     return bufPos + totalLen;


                     654 ;633: }


                     655 ;634: 


                     656 ;635: int encodeSimpleDataAccessAttrs(uint8_t* outBuf, int bufPos, int objectPos, bool determineSize,


                     657 ;636:                                 bool topStruct)


                     658 ;637: {


                     659 ;638:     uint8_t typeIdTag;


                     660 ;639:     int typeIdLen;



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                     661 ;640:     enum InnerAttributeType typeId;


                     662 ;641:     int typeDescrSize;


                     663 ;642:     int pos = objectPos;


                     664 ;643:     int objectSize;


                     665 ;644:     int sequenceSize;


                     666 ;645:     int totalSize;


                     667 ;646:     int objectNamePos;


                     668 ;647:     int nameLen;


                     669 ;648:     int daDataPos;


                     670 ;649:     //!!!


                     671 ;650:     int result;


                     672 ;651: 


                     673 ;652:     //======================Получаем иденитификатор типа============


                     674 ;653: 


                     675 ;654:     //Пропускаем тэг


                     676 ;655:     pos++;


                     677 ;656:     //Опеределяем длину


                     678 ;657:     pos = BerDecoder_decodeLength(iedModel, &objectSize, pos, iedModelSize);


                     679 ;658:     if(pos == -1)


                     680 ;659:     {


                     681 ;660:         return 0;


                     682 ;661:     }


                     683 ;662: 


                     684 ;663:     //Запоминаем положение имени и пропускаем


                     685 ;664:     objectNamePos = pos;


                     686 ;665:     pos = skipObject(pos);


                     687 ;666:     if (pos == 0)


                     688 ;667:     {


                     689 ;668:         return 0;


                     690 ;669:     }


                     691 ;670: 


                     692 ;671:     //Пропускаем прочую служебную информацию


                     693 ;672:     while(IEDModel_isServiceInfo(iedModel[pos]))


                     694 ;673:     {


                     695 ;674:         pos = skipObject(pos);


                     696 ;675:     }


                     697 ;676: 


                     698 ;677:     //Получаем идентификатор типа


                     699 ;678:     typeIdTag = iedModel[pos++];


                     700 ;679:     if(typeIdTag != ASN_INTEGER)


                     701 ;680:     {


                     702 ;681:         return 0;


                     703 ;682:     };


                     704 ;683:     typeIdLen = iedModel[pos++];


                     705 ;684:     //Получаем идентификатор типа


                     706 ;685:     typeId = (enum InnerAttributeType)BerDecoder_decodeUint32(iedModel, typeIdLen, pos);


                     707 ;686:     pos+=typeIdLen;


                     708 ;687:     daDataPos = pos;


                     709 ;688: 


                     710 ;689:     //===================== Определяем длину =========================


                     711 ;690:     typeDescrSize = encodeTypeAccessAttrs(outBuf, bufPos, typeId, daDataPos , TRUE);


                     712 ;691:     if(topStruct)


                     713 ;692:     {


                     714 ;693:         sequenceSize = typeDescrSize;


                     715 ;694:         //+Deletable size


                     716 ;695:         sequenceSize += 3;


                     717 ;696:     }


                     718 ;697:     else


                     719 ;698:     {


                     720 ;699:         sequenceSize = 1


                     721 ;700:                 + BerEncoder_determineLengthSize(typeDescrSize)



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                     722 ;701:                 + typeDescrSize;


                     723 ;702:         //+Name size


                     724 ;703:         nameLen = encodeNameAttr(NULL, 0, objectNamePos, TRUE);


                     725 ;704:         if(nameLen < 1)


                     726 ;705:         {


                     727 ;706:             return 0;


                     728 ;707:         }


                     729 ;708:         sequenceSize += nameLen;


                     730 ;709:     }


                     731 ;710: 


                     732 ;711: 


                     733 ;712:     if(determineSize)


                     734 ;713:     {


                     735 ;714:         totalSize = 1


                     736 ;715:                 + BerEncoder_determineLengthSize(sequenceSize)


                     737 ;716:                 + sequenceSize;


                     738 ;717:         return totalSize;


                     739 ;718:     }


                     740 ;719: 


                     741 ;720:     //=======================Пишем================================


                     742 ;721:     if(!topStruct)


                     743 ;722:     {


                     744 ;723:         //Если не topStruct пишем sequence


                     745 ;724:         bufPos = BerEncoder_encodeTL(ASN_SEQUENCE, sequenceSize,


                     746 ;725:                                      outBuf, bufPos);


                     747 ;726:         //Имя объекта


                     748 ;727:         bufPos = encodeNameAttr(outBuf, bufPos, objectNamePos, FALSE);


                     749 ;728:         //Тэг и размер описания типа


                     750 ;729:         bufPos = BerEncoder_encodeTL(0xA1, typeDescrSize, outBuf, bufPos);


                     751 ;730:     }


                     752 ;731:     else


                     753 ;732:     {


                     754 ;733:         //Deletable


                     755 ;734:         bufPos = BerEncoder_encodeBoolean(VAR_DELETABLE, FALSE, outBuf, bufPos);


                     756 ;735:         //Тэг и размер описания типа


                     757 ;736:         bufPos = BerEncoder_encodeTL(0xA2, typeDescrSize, outBuf, bufPos);


                     758 ;737:     }


                     759 ;738:     


                     760 ;739:     //Вызываем соответствующую типу функцию


                     761 ;740:     result = encodeTypeAccessAttrs(outBuf, bufPos, typeId, daDataPos, FALSE);


                     762 ;741:     return result;


                     763 ;742: }


                     764 ;743: 


                     765 ;744: int encodeObjectAccessAttrs(uint8_t* outBuf, int bufPos, int objectPos, bool determineSize,


                     766 ;745:                             bool topStruct)


                     767 ;746: {


                     768 ;747:     uint8_t tag = iedModel[objectPos];


                     769 ;748:     if(tag == IED_DA_FINAL)


                     770 ;749:     {


                     771 ;750:         return encodeSimpleDataAccessAttrs( outBuf, bufPos, objectPos, determineSize, topStruct);


                     772 ;751:     }


                     773 ;752:     else


                     774 ;753:     {


                     775 ;754:         return encodeStructAccessAttrs(outBuf, bufPos, objectPos, determineSize, topStruct);


                     776 ;755:     }


                     777 ;756: }


                     778 ;757: 


                     779 ;758: int encodeChildrenAccessAttrs(uint8_t* outBuf, int bufPos, int rootObjPos, bool determineSize)


                     780 ;759: {


                     781 ;760:     int totalSize = 0;


                     782 ;761:     int objEndPos;



                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                     783 ;762:     int childObjectPos = getSubObjectsPos(rootObjPos, &objEndPos);


                     784 ;763:     if (childObjectPos == 0)


                     785 ;764:     {


                     786 ;765:         //Если ошибка, считаем что детей нет


                     787 ;766:         return 0;


                     788 ;767:     }


                     789 ;768:     while (childObjectPos < objEndPos)


                     790 ;769:     {


                     791 ;770:         int subObjLen;


                     792 ;771:         int objContentPos;


                     793 ;772:         int pos = childObjectPos;


                     794 ;773:         //Пропускаем тэг


                     795 ;774:         pos++;


                     796 ;775:         //Получаем размер подобъекта


                     797 ;776:         objContentPos = BerDecoder_decodeLength(iedModel, &subObjLen, pos,


                     798 ;777:             iedModelSize);


                     799 ;778:         if (objContentPos < 1)


                     800 ;779:         {


                     801 ;780:             //Если ошибка, считаем что больше детей нет


                     802 ;781:             return 0;


                     803 ;782:         }


                     804 ;783:         bufPos = encodeObjectAccessAttrs(outBuf, bufPos, childObjectPos, determineSize,


                     805 ;784:                                          FALSE);


                     806 ;785:         if (determineSize)


                     807 ;786:         {


                     808 ;787:             totalSize += bufPos;


                     809 ;788:         }


                     810 ;789: 


                     811 ;790:         childObjectPos = objContentPos + subObjLen;


                     812 ;791:     }


                     813 ;792: 


                     814 ;793:     if (determineSize)


                     815 ;794:     {


                     816 ;795:         return totalSize;


                     817 ;796:     }


                     818 ;797:     else


                     819 ;798:     {


                     820 ;799:         return bufPos;


                     821 ;800:     }


                     822 ;801: }


                     823 ;802: 


                     824 ;803: static int encodeStructAccessAttrs(uint8_t* outBuf, int bufPos, int objectPos, bool determineSize,


                     825 

                     826 ;903: }


                     827 

                     828 	.text

                     829 	.align	4

                     830 setIedModel::

00000000 e59f2870*   831 	ldr	r2,.L101

00000004 e5820000    832 	str	r0,[r2]

00000008 e59f086c*   833 	ldr	r0,.L102

0000000c e5801000    834 	str	r1,[r0]

00000010 e12fff1e*   835 	ret	

                     836 	.endf	setIedModel

                     837 	.align	4

                     838 

                     839 ;pModel	r0	param

                     840 ;modelSize	r1	param

                     841 

                     842 	.data

                     843 .L94:


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                     844 	.text

                     845 

                     846 

                     847 	.align	4

                     848 	.align	4

                     849 readTL::

00000014 e92d4100    850 	stmfd	[sp]!,{r8,lr}

00000018 e59f885c*   851 	ldr	r8,.L102

0000001c e24dd00c    852 	sub	sp,sp,12

00000020 e598c000    853 	ldr	r12,[r8]

00000024 e59f884c*   854 	ldr	r8,.L101

00000028 e58dc008    855 	str	r12,[sp,8]

0000002c e598c000    856 	ldr	r12,[r8]

00000030 e1a0e000    857 	mov	lr,r0

00000034 e88d5000    858 	stmea	[sp],{r12,lr}

00000038 e1a0000d    859 	mov	r0,sp

0000003c eb000000*   860 	bl	BerDecoder_decodeTLFromBufferView

00000040 e3500000    861 	cmp	r0,0

00000044 159d0004    862 	ldrne	r0,[sp,4]

00000048 e28dd00c    863 	add	sp,sp,12

0000004c e8bd8100    864 	ldmfd	[sp]!,{r8,pc}

                     865 	.endf	readTL

                     866 	.align	4

                     867 ;bv	[sp]	local

                     868 

                     869 ;pos	r0	param

                     870 ;pTag	none	param

                     871 ;pLen	none	param

                     872 ;pFullLen	none	param

                     873 

                     874 	.section ".bss","awb"

                     875 .L149:

                     876 	.data

                     877 	.text

                     878 

                     879 

                     880 	.align	4

                     881 	.align	4

                     882 skipObject::

00000050 e92d4000    883 	stmfd	[sp]!,{lr}

00000054 e24dd004    884 	sub	sp,sp,4

00000058 e2802001    885 	add	r2,r0,1

0000005c e59f0818*   886 	ldr	r0,.L102

00000060 e59fc810*   887 	ldr	r12,.L101

00000064 e5903000    888 	ldr	r3,[r0]

00000068 e59c0000    889 	ldr	r0,[r12]

0000006c e1a0100d    890 	mov	r1,sp

00000070 eb000000*   891 	bl	BerDecoder_decodeLength

00000074 e3500000    892 	cmp	r0,0

00000078 c59d1000    893 	ldrgt	r1,[sp]

0000007c d3a00000    894 	movle	r0,0

00000080 c0800001    895 	addgt	r0,r0,r1

00000084 e28dd004    896 	add	sp,sp,4

00000088 e8bd8000    897 	ldmfd	[sp]!,{pc}

                     898 	.endf	skipObject

                     899 	.align	4

                     900 ;len	[sp]	local

                     901 

                     902 ;pos	r0	param

                     903 

                     904 	.section ".bss","awb"


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                     905 .L198:

                     906 	.data

                     907 	.text

                     908 

                     909 

                     910 	.align	4

                     911 	.align	4

                     912 getBerStringLength::

0000008c e92d4010    913 	stmfd	[sp]!,{r4,lr}

00000090 e2804001    914 	add	r4,r0,1

00000094 e1a02004    915 	mov	r2,r4

00000098 e24dd004    916 	sub	sp,sp,4

0000009c e59f07d8*   917 	ldr	r0,.L102

000000a0 e59fc7d0*   918 	ldr	r12,.L101

000000a4 e5903000    919 	ldr	r3,[r0]

000000a8 e59c0000    920 	ldr	r0,[r12]

000000ac e1a0100d    921 	mov	r1,sp

000000b0 eb000000*   922 	bl	BerDecoder_decodeLength

000000b4 e3500000    923 	cmp	r0,0

000000b8 d3a00000    924 	movle	r0,0

000000bc c59d1000    925 	ldrgt	r1,[sp]

000000c0 c0400004    926 	subgt	r0,r0,r4

000000c4 c0811000    927 	addgt	r1,r1,r0

000000c8 c2810001    928 	addgt	r0,r1,1

000000cc e28dd004    929 	add	sp,sp,4

000000d0 e8bd8010    930 	ldmfd	[sp]!,{r4,pc}

                     931 	.endf	getBerStringLength

                     932 	.align	4

                     933 ;length	[sp]	local

                     934 ;newPos	r0	local

                     935 

                     936 ;berStringPos	r4	param

                     937 

                     938 	.section ".bss","awb"

                     939 .L246:

                     940 	.data

                     941 	.text

                     942 

                     943 

                     944 	.align	4

                     945 	.align	4

                     946 getIEDObjectNameString::

000000d4 e92d40f0    947 	stmfd	[sp]!,{r4-r7,lr}

000000d8 e24dd004    948 	sub	sp,sp,4

000000dc e1a06000    949 	mov	r6,r0

000000e0 e5960000    950 	ldr	r0,[r6]

000000e4 e1a07001    951 	mov	r7,r1

000000e8 e2802001    952 	add	r2,r0,1

000000ec e59f0788*   953 	ldr	r0,.L102

000000f0 e59fc780*   954 	ldr	r12,.L101

000000f4 e5903000    955 	ldr	r3,[r0]

000000f8 e59c0000    956 	ldr	r0,[r12]

000000fc e1a0100d    957 	mov	r1,sp

00000100 eb000000*   958 	bl	BerDecoder_decodeLength

00000104 e2505000    959 	subs	r5,r0,0

00000108 da000002    960 	ble	.L265

0000010c ebffffde*   961 	bl	getBerStringLength

00000110 e1b04000    962 	movs	r4,r0

00000114 1a000001    963 	bne	.L264

                     964 .L265:

00000118 e3a00000    965 	mov	r0,0


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
0000011c ea00000b    966 	b	.L259

                     967 .L264:

00000120 e3570000    968 	cmp	r7,0

00000124 0a000005    969 	beq	.L267

00000128 e59f1748*   970 	ldr	r1,.L101

0000012c e5910000    971 	ldr	r0,[r1]

00000130 e1a02004    972 	mov	r2,r4

00000134 e0851000    973 	add	r1,r5,r0

00000138 e1a00007    974 	mov	r0,r7

0000013c eb000000*   975 	bl	memcpy

                     976 .L267:

00000140 e59d0000    977 	ldr	r0,[sp]

00000144 e0800005    978 	add	r0,r0,r5

00000148 e5860000    979 	str	r0,[r6]

0000014c e1a00004    980 	mov	r0,r4

                     981 .L259:

00000150 e28dd004    982 	add	sp,sp,4

00000154 e8bd80f0    983 	ldmfd	[sp]!,{r4-r7,pc}

                     984 	.endf	getIEDObjectNameString

                     985 	.align	4

                     986 ;objLen	[sp]	local

                     987 ;nameLen	r4	local

                     988 ;namePos	r5	local

                     989 ;pos	r0	local

                     990 

                     991 ;pObjPos	r6	param

                     992 ;buf	r7	param

                     993 

                     994 	.section ".bss","awb"

                     995 .L338:

                     996 	.data

                     997 	.text

                     998 

                     999 

                    1000 	.align	4

                    1001 	.align	4

                    1002 getObjectName::

00000158 e92d4010   1003 	stmfd	[sp]!,{r4,lr}

0000015c e24dd00c   1004 	sub	sp,sp,12

00000160 e28d2008   1005 	add	r2,sp,8

00000164 e1a04001   1006 	mov	r4,r1

00000168 e3a03000   1007 	mov	r3,0

0000016c e1a01003   1008 	mov	r1,r3

00000170 ebffffa7*  1009 	bl	readTL

00000174 e3500000   1010 	cmp	r0,0

00000178 159d1008   1011 	ldrne	r1,[sp,8]

0000017c 13510000   1012 	cmpne	r1,0

00000180 0a000008   1013 	beq	.L364

00000184 e28d2004   1014 	add	r2,sp,4

00000188 e28d1003   1015 	add	r1,sp,3

0000018c e3a03000   1016 	mov	r3,0

00000190 ebffff9f*  1017 	bl	readTL

00000194 e3500000   1018 	cmp	r0,0

00000198 0a000002   1019 	beq	.L364

0000019c e5dd1003   1020 	ldrb	r1,[sp,3]

000001a0 e351001a   1021 	cmp	r1,26

000001a4 0a000001   1022 	beq	.L363

                    1023 .L364:

000001a8 e3a00000   1024 	mov	r0,0

000001ac ea000006   1025 	b	.L355

                    1026 .L363:


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
000001b0 e59f36c0*  1027 	ldr	r3,.L101

000001b4 e5931000   1028 	ldr	r1,[r3]

000001b8 e59d2004   1029 	ldr	r2,[sp,4]

000001bc e0801001   1030 	add	r1,r0,r1

000001c0 e1a00004   1031 	mov	r0,r4

000001c4 eb000000*  1032 	bl	StringView_init

000001c8 e3a00001   1033 	mov	r0,1

                    1034 .L355:

000001cc e28dd00c   1035 	add	sp,sp,12

000001d0 e8bd8010   1036 	ldmfd	[sp]!,{r4,pc}

                    1037 	.endf	getObjectName

                    1038 	.align	4

                    1039 ;nameTag	[sp,3]	local

                    1040 ;nameLen	[sp,4]	local

                    1041 ;namePos	r0	local

                    1042 ;objectLen	[sp,8]	local

                    1043 ;pos	r0	local

                    1044 

                    1045 ;objPos	none	param

                    1046 ;result	r4	param

                    1047 

                    1048 	.section ".bss","awb"

                    1049 .L430:

                    1050 	.data

                    1051 	.text

                    1052 

                    1053 

                    1054 	.align	4

                    1055 	.align	4

                    1056 IEDModel_isServiceInfo::

000001d4 e350001a   1057 	cmp	r0,26

000001d8 135000f0   1058 	cmpne	r0,240

000001dc 135000f1   1059 	cmpne	r0,241

000001e0 135000c0   1060 	cmpne	r0,192

000001e4 e3a00001   1061 	mov	r0,1

000001e8 13a00000   1062 	movne	r0,0

000001ec e20000ff   1063 	and	r0,r0,255

000001f0 e12fff1e*  1064 	ret	

                    1065 	.endf	IEDModel_isServiceInfo

                    1066 	.align	4

                    1067 

                    1068 ;tag	r1	param

                    1069 

                    1070 	.section ".bss","awb"

                    1071 .L522:

                    1072 	.data

                    1073 	.text

                    1074 

                    1075 

                    1076 	.align	4

                    1077 	.align	4

                    1078 getSubObjectsPos::

000001f4 e92d4070   1079 	stmfd	[sp]!,{r4-r6,lr}

000001f8 e1a06001   1080 	mov	r6,r1

000001fc e24dd004   1081 	sub	sp,sp,4

00000200 e2802001   1082 	add	r2,r0,1

00000204 e59f0670*  1083 	ldr	r0,.L102

00000208 e59fc668*  1084 	ldr	r12,.L101

0000020c e5903000   1085 	ldr	r3,[r0]

00000210 e59c0000   1086 	ldr	r0,[r12]

00000214 e1a0100d   1087 	mov	r1,sp


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
00000218 eb000000*  1088 	bl	BerDecoder_decodeLength

0000021c e2504000   1089 	subs	r4,r0,0

00000220 da000005   1090 	ble	.L544

00000224 e59f1650*  1091 	ldr	r1,.L102

00000228 e59d5000   1092 	ldr	r5,[sp]

0000022c e5910000   1093 	ldr	r0,[r1]

00000230 e0855004   1094 	add	r5,r5,r4

00000234 e1550000   1095 	cmp	r5,r0

00000238 da000001   1096 	ble	.L543

                    1097 .L544:

0000023c e3a00000   1098 	mov	r0,0

00000240 ea00000f   1099 	b	.L538

                    1100 .L543:

00000244 e5865000   1101 	str	r5,[r6]

00000248 e1540005   1102 	cmp	r4,r5

0000024c aa00000b   1103 	bge	.L550

00000250 e59f6620*  1104 	ldr	r6,.L101

                    1105 .L551:

00000254 e5960000   1106 	ldr	r0,[r6]

00000258 e7d00004   1107 	ldrb	r0,[r0,r4]

0000025c ebffffdc*  1108 	bl	IEDModel_isServiceInfo

00000260 e3500000   1109 	cmp	r0,0

00000264 0a000005   1110 	beq	.L550

00000268 e1a00004   1111 	mov	r0,r4

0000026c ebffff77*  1112 	bl	skipObject

00000270 e1b04000   1113 	movs	r4,r0

00000274 0afffff0   1114 	beq	.L544

00000278 e1540005   1115 	cmp	r4,r5

0000027c bafffff4   1116 	blt	.L551

                    1117 .L550:

00000280 e1a00004   1118 	mov	r0,r4

                    1119 .L538:

00000284 e28dd004   1120 	add	sp,sp,4

00000288 e8bd8070   1121 	ldmfd	[sp]!,{r4-r6,pc}

                    1122 	.endf	getSubObjectsPos

                    1123 	.align	4

                    1124 ;endPos	r5	local

                    1125 ;rootObjLen	[sp]	local

                    1126 ;pos	r4	local

                    1127 

                    1128 ;rootObjPos	r0	param

                    1129 ;pEndPos	r6	param

                    1130 

                    1131 	.section ".bss","awb"

                    1132 .L705:

                    1133 	.data

                    1134 	.text

                    1135 

                    1136 

                    1137 	.align	4

                    1138 	.align	4

                    1139 findObjectBySimpleName::

0000028c e92d4cf0   1140 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

00000290 e1a0a002   1141 	mov	r10,r2

00000294 e24dd00c   1142 	sub	sp,sp,12

00000298 e1a0b001   1143 	mov	fp,r1

0000029c e1a0100d   1144 	mov	r1,sp

000002a0 ebffffd3*  1145 	bl	getSubObjectsPos

000002a4 e1b04000   1146 	movs	r4,r0

000002a8 0a000026   1147 	beq	.L743

000002ac e59f65c4*  1148 	ldr	r6,.L101


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
000002b0 e59d0000   1149 	ldr	r0,[sp]

000002b4 e59f75c0*  1150 	ldr	r7,.L102

000002b8 e1540000   1151 	cmp	r4,r0

000002bc aa000021   1152 	bge	.L743

                    1153 .L744:

000002c0 e2842001   1154 	add	r2,r4,1

000002c4 e5973000   1155 	ldr	r3,[r7]

000002c8 e5960000   1156 	ldr	r0,[r6]

000002cc e28d1008   1157 	add	r1,sp,8

000002d0 eb000000*  1158 	bl	BerDecoder_decodeLength

000002d4 e3500000   1159 	cmp	r0,0

000002d8 da00001a   1160 	ble	.L743

000002dc e596c000   1161 	ldr	r12,[r6]

000002e0 e7dc1000   1162 	ldrb	r1,[r12,r0]

000002e4 e351001a   1163 	cmp	r1,26

000002e8 1a000016   1164 	bne	.L743

000002ec e5973000   1165 	ldr	r3,[r7]

000002f0 e59d5008   1166 	ldr	r5,[sp,8]

000002f4 e28d1004   1167 	add	r1,sp,4

000002f8 e0855000   1168 	add	r5,r5,r0

000002fc e2802001   1169 	add	r2,r0,1

00000300 e1a0000c   1170 	mov	r0,r12

00000304 eb000000*  1171 	bl	BerDecoder_decodeLength

00000308 e3500000   1172 	cmp	r0,0

0000030c da00000d   1173 	ble	.L743

00000310 e5961000   1174 	ldr	r1,[r6]

00000314 e59d2004   1175 	ldr	r2,[sp,4]

00000318 e0801001   1176 	add	r1,r0,r1

0000031c e1a0000b   1177 	mov	r0,fp

00000320 eb000000*  1178 	bl	memcmp

00000324 e59d1004   1179 	ldr	r1,[sp,4]

00000328 e151000a   1180 	cmp	r1,r10

0000032c 03500000   1181 	cmpeq	r0,0

00000330 01a00004   1182 	moveq	r0,r4

00000334 0a000004   1183 	beq	.L737

00000338 e59d0000   1184 	ldr	r0,[sp]

0000033c e1a04005   1185 	mov	r4,r5

00000340 e1540000   1186 	cmp	r4,r0

00000344 baffffdd   1187 	blt	.L744

                    1188 .L743:

00000348 e3a00000   1189 	mov	r0,0

                    1190 .L737:

0000034c e28dd00c   1191 	add	sp,sp,12

00000350 e8bd8cf0   1192 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                    1193 	.endf	findObjectBySimpleName

                    1194 	.align	4

                    1195 ;objPos	r4	local

                    1196 ;endPos	[sp]	local

                    1197 ;nameCompareResult	r0	local

                    1198 ;nameLen	[sp,4]	local

                    1199 ;objLen	[sp,8]	local

                    1200 ;namePos	r0	local

                    1201 ;nextObjPos	r5	local

                    1202 

                    1203 ;rootObjPos	none	param

                    1204 ;name	fp	param

                    1205 ;argNameLen	r10	param

                    1206 

                    1207 	.section ".bss","awb"

                    1208 .L900:

                    1209 	.data


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                    1210 	.text

                    1211 

                    1212 

                    1213 	.align	4

                    1214 	.align	4

                    1215 findObjectByTag::

00000354 e92d40f0   1216 	stmfd	[sp]!,{r4-r7,lr}

00000358 e24dd008   1217 	sub	sp,sp,8

0000035c e1a07001   1218 	mov	r7,r1

00000360 e1a0100d   1219 	mov	r1,sp

00000364 ebffffa2*  1220 	bl	getSubObjectsPos

00000368 e1b01000   1221 	movs	r1,r0

0000036c 0a000014   1222 	beq	.L940

00000370 e59f4500*  1223 	ldr	r4,.L101

00000374 e59f5500*  1224 	ldr	r5,.L102

00000378 e59d0000   1225 	ldr	r0,[sp]

0000037c e28d6004   1226 	add	r6,sp,4

00000380 e1510000   1227 	cmp	r1,r0

00000384 aa00000e   1228 	bge	.L940

                    1229 .L941:

00000388 e594c000   1230 	ldr	r12,[r4]

0000038c e1a00001   1231 	mov	r0,r1

00000390 e7dc1000   1232 	ldrb	r1,[r12,r0]

00000394 e1510007   1233 	cmp	r1,r7

00000398 0a00000a   1234 	beq	.L934

0000039c e5953000   1235 	ldr	r3,[r5]

000003a0 e1a01006   1236 	mov	r1,r6

000003a4 e2802001   1237 	add	r2,r0,1

000003a8 e1a0000c   1238 	mov	r0,r12

000003ac eb000000*  1239 	bl	BerDecoder_decodeLength

000003b0 e3500000   1240 	cmp	r0,0

000003b4 c89d000c   1241 	ldmgtfd	[sp],{r2-r3}

000003b8 c0831000   1242 	addgt	r1,r3,r0

000003bc c0710002   1243 	rsbgts	r0,r1,r2

000003c0 cafffff0   1244 	bgt	.L941

                    1245 .L940:

000003c4 e3a00000   1246 	mov	r0,0

                    1247 .L934:

000003c8 e28dd008   1248 	add	sp,sp,8

000003cc e8bd80f0   1249 	ldmfd	[sp]!,{r4-r7,pc}

                    1250 	.endf	findObjectByTag

                    1251 	.align	4

                    1252 ;objPos	r1	local

                    1253 ;endPos	[sp]	local

                    1254 ;objLen	[sp,4]	local

                    1255 ;pos	r0	local

                    1256 ;tag	r1	local

                    1257 

                    1258 ;rootObjPos	none	param

                    1259 ;tagToFind	r7	param

                    1260 

                    1261 	.section ".bss","awb"

                    1262 .L1044:

                    1263 	.data

                    1264 	.text

                    1265 

                    1266 

                    1267 	.align	4

                    1268 	.align	4

                    1269 findDomainSection::

000003d0 e92d4010   1270 	stmfd	[sp]!,{r4,lr}


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
000003d4 e1a04000   1271 	mov	r4,r0

000003d8 e3a00000   1272 	mov	r0,0

000003dc ebffffaa*  1273 	bl	findObjectBySimpleName

000003e0 e1b01000   1274 	movs	r1,r0

000003e4 120410ff   1275 	andne	r1,r4,255

000003e8 18bd4010   1276 	ldmnefd	[sp]!,{r4,lr}

000003ec 1affffd8*  1277 	bne	findObjectByTag

000003f0 e8bd8010   1278 	ldmfd	[sp]!,{r4,pc}

                    1279 	.endf	findDomainSection

                    1280 	.align	4

                    1281 ;vmdPos	r1	local

                    1282 

                    1283 ;section	r4	param

                    1284 ;domainId	none	param

                    1285 ;domainIdLen	none	param

                    1286 

                    1287 	.section ".bss","awb"

                    1288 .L1125:

                    1289 	.data

                    1290 	.text

                    1291 

                    1292 

                    1293 	.align	4

                    1294 	.align	4

                    1295 getSimpleNameLen::

000003f4 e92d0030   1296 	stmfd	[sp]!,{r4-r5}

000003f8 e3a04001   1297 	mov	r4,1

000003fc e3a03000   1298 	mov	r3,0

00000400 e5c23000   1299 	strb	r3,[r2]

00000404 e3510000   1300 	cmp	r1,0

00000408 a1a0c001   1301 	movge	r12,r1

0000040c b3a0c000   1302 	movlt	r12,0

00000410 e1b011ac   1303 	movs	r1,r12 lsr 3

00000414 0a00001a   1304 	beq	.L1179

                    1305 .L1180:

00000418 e7d05003   1306 	ldrb	r5,[r0,r3]

0000041c e3550024   1307 	cmp	r5,36

00000420 12833001   1308 	addne	r3,r3,1

00000424 17d05003   1309 	ldrneb	r5,[r0,r3]

00000428 13550024   1310 	cmpne	r5,36

0000042c 12833001   1311 	addne	r3,r3,1

00000430 17d05003   1312 	ldrneb	r5,[r0,r3]

00000434 13550024   1313 	cmpne	r5,36

00000438 12833001   1314 	addne	r3,r3,1

0000043c 17d05003   1315 	ldrneb	r5,[r0,r3]

00000440 13550024   1316 	cmpne	r5,36

00000444 12833001   1317 	addne	r3,r3,1

00000448 17d05003   1318 	ldrneb	r5,[r0,r3]

0000044c 13550024   1319 	cmpne	r5,36

00000450 12833001   1320 	addne	r3,r3,1

00000454 17d05003   1321 	ldrneb	r5,[r0,r3]

00000458 13550024   1322 	cmpne	r5,36

0000045c 12833001   1323 	addne	r3,r3,1

00000460 17d05003   1324 	ldrneb	r5,[r0,r3]

00000464 13550024   1325 	cmpne	r5,36

00000468 12833001   1326 	addne	r3,r3,1

0000046c 17d05003   1327 	ldrneb	r5,[r0,r3]

00000470 13550024   1328 	cmpne	r5,36

00000474 0a000006   1329 	beq	.L1215

00000478 e2833001   1330 	add	r3,r3,1

0000047c e2511001   1331 	subs	r1,r1,1


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
00000480 1affffe4   1332 	bne	.L1180

                    1333 .L1179:

00000484 e21c1007   1334 	ands	r1,r12,7

00000488 0a000006   1335 	beq	.L1139

                    1336 .L1214:

0000048c e7d0c003   1337 	ldrb	r12,[r0,r3]

00000490 e35c0024   1338 	cmp	r12,36

                    1339 .L1215:

00000494 05c24000   1340 	streqb	r4,[r2]

00000498 0a000002   1341 	beq	.L1139

                    1342 .L1217:

0000049c e2833001   1343 	add	r3,r3,1

000004a0 e2511001   1344 	subs	r1,r1,1

000004a4 1afffff8   1345 	bne	.L1214

                    1346 .L1139:

000004a8 e1a00003   1347 	mov	r0,r3

000004ac e8bd0030   1348 	ldmfd	[sp]!,{r4-r5}

000004b0 e12fff1e*  1349 	ret	

                    1350 	.endf	getSimpleNameLen

                    1351 	.align	4

                    1352 ;len	r3	local

                    1353 

                    1354 ;name	r0	param

                    1355 ;fullNameLen	r1	param

                    1356 ;delimiterFound	r2	param

                    1357 

                    1358 	.section ".bss","awb"

                    1359 .L1464:

                    1360 	.data

                    1361 	.text

                    1362 

                    1363 

                    1364 	.align	4

                    1365 	.align	4

                    1366 findObjectByPath::

000004b4 e92d44f0   1367 	stmfd	[sp]!,{r4-r7,r10,lr}

000004b8 e1a05001   1368 	mov	r5,r1

000004bc e1b06002   1369 	movs	r6,r2

000004c0 e1a04000   1370 	mov	r4,r0

000004c4 e24dd004   1371 	sub	sp,sp,4

000004c8 e28da003   1372 	add	r10,sp,3

000004cc 0a000010   1373 	beq	.L1508

                    1374 .L1509:

000004d0 e1a0200a   1375 	mov	r2,r10

000004d4 e1a01006   1376 	mov	r1,r6

000004d8 e1a00005   1377 	mov	r0,r5

000004dc ebffffc4*  1378 	bl	getSimpleNameLen

000004e0 e1a07000   1379 	mov	r7,r0

000004e4 e1a02007   1380 	mov	r2,r7

000004e8 e1a01005   1381 	mov	r1,r5

000004ec e1a00004   1382 	mov	r0,r4

000004f0 ebffff65*  1383 	bl	findObjectBySimpleName

000004f4 e1b04000   1384 	movs	r4,r0

000004f8 0a000005   1385 	beq	.L1508

000004fc e5dd0003   1386 	ldrb	r0,[sp,3]

00000500 e3500000   1387 	cmp	r0,0

00000504 12877001   1388 	addne	r7,r7,1

00000508 e0855007   1389 	add	r5,r5,r7

0000050c e0566007   1390 	subs	r6,r6,r7

00000510 1affffee   1391 	bne	.L1509

                    1392 .L1508:


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
00000514 e1a00004   1393 	mov	r0,r4

00000518 e28dd004   1394 	add	sp,sp,4

0000051c e8bd84f0   1395 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    1396 	.endf	findObjectByPath

                    1397 	.align	4

                    1398 ;objPos	r4	local

                    1399 ;delimiterfound	[sp,3]	local

                    1400 ;simpleNameLen	r7	local

                    1401 

                    1402 ;rootObjPos	r0	param

                    1403 ;name	r5	param

                    1404 ;fullNameLen	r6	param

                    1405 

                    1406 	.section ".bss","awb"

                    1407 .L1594:

                    1408 	.data

                    1409 	.text

                    1410 

                    1411 

                    1412 	.align	4

                    1413 	.align	4

                    1414 findObjectByFullName::

00000520 e92d4010   1415 	stmfd	[sp]!,{r4,lr}

00000524 e1a04002   1416 	mov	r4,r2

00000528 e891000c   1417 	ldmfd	[r1],{r2-r3}

0000052c e1a01003   1418 	mov	r1,r3

00000530 ebffffa6*  1419 	bl	findDomainSection

00000534 e1b03000   1420 	movs	r3,r0

00000538 0a000004   1421 	beq	.L1617

0000053c e894000c   1422 	ldmfd	[r4],{r2-r3}

00000540 e1a01003   1423 	mov	r1,r3

00000544 ebffffda*  1424 	bl	findObjectByPath

00000548 e3500000   1425 	cmp	r0,0

0000054c 03a00000   1426 	moveq	r0,0

                    1427 .L1617:

00000550 e8bd8010   1428 	ldmfd	[sp]!,{r4,pc}

                    1429 	.endf	findObjectByFullName

                    1430 	.align	4

                    1431 ;sectionPos	r3	local

                    1432 

                    1433 ;section	none	param

                    1434 ;domainName	r1	param

                    1435 ;objectName	r4	param

                    1436 

                    1437 	.section ".bss","awb"

                    1438 .L1670:

                    1439 	.data

                    1440 	.text

                    1441 

                    1442 

                    1443 	.align	4

                    1444 	.align	4

                    1445 getDataSetByPath::

00000554 e92d4000   1446 	stmfd	[sp]!,{lr}

00000558 e24dd010   1447 	sub	sp,sp,16

0000055c e1a0300d   1448 	mov	r3,sp

00000560 e28d2008   1449 	add	r2,sp,8

00000564 e3a0102f   1450 	mov	r1,47

00000568 eb000000*  1451 	bl	StringView_splitChar

0000056c e3500000   1452 	cmp	r0,0

00000570 0a000005   1453 	beq	.L1684


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
00000574 e1a0200d   1454 	mov	r2,sp

00000578 e28d1008   1455 	add	r1,sp,8

0000057c e3a000ee   1456 	mov	r0,238

00000580 ebffffe6*  1457 	bl	findObjectByFullName

00000584 e3500000   1458 	cmp	r0,0

00000588 03a00000   1459 	moveq	r0,0

                    1460 .L1684:

0000058c e28dd010   1461 	add	sp,sp,16

00000590 e8bd8000   1462 	ldmfd	[sp]!,{pc}

                    1463 	.endf	getDataSetByPath

                    1464 	.align	4

                    1465 ;domainName	[sp,8]	local

                    1466 ;objectName	[sp]	local

                    1467 

                    1468 ;pDatasetPath	none	param

                    1469 

                    1470 	.section ".bss","awb"

                    1471 .L1734:

                    1472 	.data

                    1473 	.text

                    1474 

                    1475 

                    1476 	.align	4

                    1477 	.align	4

                    1478 writeChildrenNames::

00000594 e92d4df0   1479 	stmfd	[sp]!,{r4-r8,r10-fp,lr}

00000598 e24dd014   1480 	sub	sp,sp,20

0000059c e5cd2003   1481 	strb	r2,[sp,3]

000005a0 e1a07003   1482 	mov	r7,r3

000005a4 e1a05001   1483 	mov	r5,r1

000005a8 e28d1004   1484 	add	r1,sp,4

000005ac ebffff10*  1485 	bl	getSubObjectsPos

000005b0 e1b04000   1486 	movs	r4,r0

000005b4 0a000033   1487 	beq	.L1748

000005b8 e59f62b8*  1488 	ldr	r6,.L101

000005bc e59fa2b8*  1489 	ldr	r10,.L102

000005c0 e2971001   1490 	adds	r1,r7,1

000005c4 13a01001   1491 	movne	r1,1

000005c8 e59d0004   1492 	ldr	r0,[sp,4]

000005cc e1a08001   1493 	mov	r8,r1

000005d0 e1540000   1494 	cmp	r4,r0

000005d4 aa00002b   1495 	bge	.L1748

                    1496 .L1755:

000005d8 e5960000   1497 	ldr	r0,[r6]

000005dc e2842001   1498 	add	r2,r4,1

000005e0 e7d01004   1499 	ldrb	r1,[r0,r4]

000005e4 e59a3000   1500 	ldr	r3,[r10]

000005e8 e5cd1002   1501 	strb	r1,[sp,2]

000005ec e28d1008   1502 	add	r1,sp,8

000005f0 eb000000*  1503 	bl	BerDecoder_decodeLength

000005f4 e2501000   1504 	subs	r1,r0,0

000005f8 e1a0b001   1505 	mov	fp,r1

000005fc da000021   1506 	ble	.L1748

00000600 e5960000   1507 	ldr	r0,[r6]

00000604 e7d03001   1508 	ldrb	r3,[r0,r1]

00000608 e2811001   1509 	add	r1,r1,1

0000060c e353001a   1510 	cmp	r3,26

00000610 1a00001c   1511 	bne	.L1748

00000614 e59a3000   1512 	ldr	r3,[r10]

00000618 e1a02001   1513 	mov	r2,r1

0000061c e28d100c   1514 	add	r1,sp,12


                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
00000620 eb000000*  1515 	bl	BerDecoder_decodeLength

00000624 e2501000   1516 	subs	r1,r0,0

00000628 da000016   1517 	ble	.L1748

0000062c e5960000   1518 	ldr	r0,[r6]

00000630 e59d200c   1519 	ldr	r2,[sp,12]

00000634 e0811000   1520 	add	r1,r1,r0

00000638 e1a00005   1521 	mov	r0,r5

0000063c eb000000*  1522 	bl	DomainNameWriter_pushName

00000640 e2581000   1523 	subs	r1,r8,0

00000644 15dd1002   1524 	ldrneb	r1,[sp,2]

00000648 11570001   1525 	cmpne	r7,r1

0000064c 01a00005   1526 	moveq	r0,r5

00000650 0b000000*  1527 	bleq	DomainNameWriter_encode

00000654 e5dd1003   1528 	ldrb	r1,[sp,3]

00000658 e3510000   1529 	cmp	r1,0

0000065c 11a03007   1530 	movne	r3,r7

00000660 11a01005   1531 	movne	r1,r5

00000664 11a00004   1532 	movne	r0,r4

00000668 13a02001   1533 	movne	r2,1

0000066c 1bffffc8*  1534 	blne	writeChildrenNames

00000670 e1a00005   1535 	mov	r0,r5

00000674 eb000000*  1536 	bl	DomainNameWriter_discardName

00000678 e99d0011   1537 	ldmed	[sp],{r0,r4}

0000067c e084400b   1538 	add	r4,r4,fp

00000680 e1540000   1539 	cmp	r4,r0

00000684 baffffd3   1540 	blt	.L1755

                    1541 .L1748:

00000688 e28dd014   1542 	add	sp,sp,20

0000068c e8bd8df0   1543 	ldmfd	[sp]!,{r4-r8,r10-fp,pc}

                    1544 	.endf	writeChildrenNames

                    1545 	.align	4

                    1546 ;objEndPos	[sp,4]	local

                    1547 ;childObjectPos	r4	local

                    1548 ;subObjLen	[sp,8]	local

                    1549 ;nameLen	[sp,12]	local

                    1550 ;namePos	r1	local

                    1551 ;objContentPos	fp	local

                    1552 ;tag	[sp,2]	local

                    1553 

                    1554 ;rootObjPos	none	param

                    1555 ;writer	r5	param

                    1556 ;recursive	[sp,3]	param

                    1557 ;objectsTagToWrite	r7	param

                    1558 

                    1559 	.section ".bss","awb"

                    1560 .L1880:

                    1561 	.data

                    1562 	.text

                    1563 

                    1564 

                    1565 	.align	4

                    1566 	.align	4

                    1567 encodeReadConst::

00000690 e92d44f0   1568 	stmfd	[sp]!,{r4-r7,r10,lr}

00000694 e1a07002   1569 	mov	r7,r2

00000698 e2872001   1570 	add	r2,r7,1

0000069c e1a05001   1571 	mov	r5,r1

000006a0 e24dd004   1572 	sub	sp,sp,4

000006a4 e1a0a003   1573 	mov	r10,r3

000006a8 e1a06000   1574 	mov	r6,r0

000006ac e59f01c8*  1575 	ldr	r0,.L102


                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
000006b0 e59fc1c0*  1576 	ldr	r12,.L101

000006b4 e5903000   1577 	ldr	r3,[r0]

000006b8 e59c0000   1578 	ldr	r0,[r12]

000006bc e1a0100d   1579 	mov	r1,sp

000006c0 eb000000*  1580 	bl	BerDecoder_decodeLength

000006c4 e59d4000   1581 	ldr	r4,[sp]

000006c8 e0400007   1582 	sub	r0,r0,r7

000006cc e0844000   1583 	add	r4,r4,r0

000006d0 e35a0000   1584 	cmp	r10,0

000006d4 11a00004   1585 	movne	r0,r4

000006d8 1a000040   1586 	bne	.L1914

000006dc e59f1194*  1587 	ldr	r1,.L101

000006e0 e5910000   1588 	ldr	r0,[r1]

000006e4 e1a02004   1589 	mov	r2,r4

000006e8 e0801007   1590 	add	r1,r0,r7

000006ec e0850006   1591 	add	r0,r5,r6

000006f0 eb000000*  1592 	bl	memcpy

000006f4 e7d6c005   1593 	ldrb	r12,[r6,r5]

000006f8 e20c00c0   1594 	and	r0,r12,192

000006fc e3500080   1595 	cmp	r0,128

00000700 13a00000   1596 	movne	r0,0

00000704 1a000035   1597 	bne	.L1914

00000708 e20cc03f   1598 	and	r12,r12,63

0000070c e25cc00d   1599 	subs	r12,r12,13

00000710 2a00000f   1600 	bhs	.L2037

00000714 e29cc001   1601 	adds	r12,r12,1

00000718 0a000021   1602 	beq	.L1925

0000071c e29cc003   1603 	adds	r12,r12,3

00000720 0a000023   1604 	beq	.L1926

00000724 e29cc001   1605 	adds	r12,r12,1

00000728 e29cc002   1606 	adds	r12,r12,2

0000072c 2a000020   1607 	bhs	.L1926

00000730 e29cc001   1608 	adds	r12,r12,1

00000734 e29cc002   1609 	adds	r12,r12,2

00000738 2a000019   1610 	bhs	.L1925

0000073c e29cc003   1611 	adds	r12,r12,3

00000740 03a00083   1612 	moveq	r0,131

00000744 07c60005   1613 	streqb	r0,[r6,r5]

00000748 00840005   1614 	addeq	r0,r4,r5

0000074c 0a000023   1615 	beq	.L1914

00000750 ea00001f   1616 	b	.L1932

                    1617 .L2037:

                    1618 

00000754 e25cc001   1619 	subs	r12,r12,1

00000758 93a00089   1620 	movls	r0,137

0000075c 97c60005   1621 	strlsb	r0,[r6,r5]

00000760 90840005   1622 	addls	r0,r4,r5

00000764 9a00001d   1623 	bls	.L1914

00000768 e25cc007   1624 	subs	r12,r12,7

0000076c 03a00090   1625 	moveq	r0,144

00000770 07c60005   1626 	streqb	r0,[r6,r5]

00000774 00840005   1627 	addeq	r0,r4,r5

00000778 0a000018   1628 	beq	.L1914

0000077c e25cc002   1629 	subs	r12,r12,2

00000780 0a00000f   1630 	beq	.L1930

00000784 e25cc003   1631 	subs	r12,r12,3

00000788 0a00000d   1632 	beq	.L1930

0000078c e35c0002   1633 	cmp	r12,2

00000790 03a0008c   1634 	moveq	r0,140

00000794 07c60005   1635 	streqb	r0,[r6,r5]

00000798 00840005   1636 	addeq	r0,r4,r5


                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
0000079c 0a00000f   1637 	beq	.L1914

000007a0 ea00000b   1638 	b	.L1932

                    1639 .L1925:

000007a4 e3a00085   1640 	mov	r0,133

000007a8 e7c60005   1641 	strb	r0,[r6,r5]

000007ac e0840005   1642 	add	r0,r4,r5

000007b0 ea00000a   1643 	b	.L1914

                    1644 .L1926:

000007b4 e3a00086   1645 	mov	r0,134

000007b8 e7c60005   1646 	strb	r0,[r6,r5]

000007bc e0840005   1647 	add	r0,r4,r5

000007c0 ea000006   1648 	b	.L1914

                    1649 .L1930:

000007c4 e3a00084   1650 	mov	r0,132

000007c8 e7c60005   1651 	strb	r0,[r6,r5]

000007cc e0840005   1652 	add	r0,r4,r5

000007d0 ea000002   1653 	b	.L1914

                    1654 .L1932:

000007d4 e3a0008a   1655 	mov	r0,138

000007d8 e7c60005   1656 	strb	r0,[r6,r5]

000007dc e0840005   1657 	add	r0,r4,r5

                    1658 .L1914:

000007e0 e28dd004   1659 	add	sp,sp,4

000007e4 e8bd84f0   1660 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    1661 	.endf	encodeReadConst

                    1662 	.align	4

                    1663 ;sizeOfTL	r0	local

                    1664 ;fullSize	r4	local

                    1665 ;objectSize	[sp]	local

                    1666 ;constTypeTag	r12	local

                    1667 ;mmsTag	r0	local

                    1668 

                    1669 ;outBuf	r6	param

                    1670 ;bufPos	r5	param

                    1671 ;constObjPos	r7	param

                    1672 ;determineSize	r10	param

                    1673 

                    1674 	.section ".bss","awb"

                    1675 .L2036:

                    1676 	.data

                    1677 	.text

                    1678 

                    1679 

                    1680 	.align	4

                    1681 	.align	4

                    1682 getAlignedDescrStruct::

000007e8 e92d4000   1683 	stmfd	[sp]!,{lr}

000007ec e59f1084*  1684 	ldr	r1,.L101

000007f0 e24dd004   1685 	sub	sp,sp,4

000007f4 e591c000   1686 	ldr	r12,[r1]

000007f8 e2802001   1687 	add	r2,r0,1

000007fc e7dc0000   1688 	ldrb	r0,[r12,r0]

00000800 e3500004   1689 	cmp	r0,4

00000804 1a00000e   1690 	bne	.L2085

00000808 e59f006c*  1691 	ldr	r0,.L102

0000080c e1a0100d   1692 	mov	r1,sp

00000810 e5903000   1693 	ldr	r3,[r0]

00000814 e1a0000c   1694 	mov	r0,r12

00000818 eb000000*  1695 	bl	BerDecoder_decodeLength

0000081c e1a02000   1696 	mov	r2,r0

00000820 e3720001   1697 	cmn	r2,1


                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
00000824 0a000006   1698 	beq	.L2085

00000828 e59f1048*  1699 	ldr	r1,.L101

0000082c e5910000   1700 	ldr	r0,[r1]

00000830 e7f01002   1701 	ldrb	r1,[r0,r2]!

00000834 e3510004   1702 	cmp	r1,4

00000838 30810000   1703 	addlo	r0,r1,r0

0000083c 32800001   1704 	addlo	r0,r0,1

00000840 3a000000   1705 	blo	.L2076

                    1706 .L2085:

00000844 e3a00000   1707 	mov	r0,0

                    1708 .L2076:

00000848 e28dd004   1709 	add	sp,sp,4

0000084c e8bd8000   1710 	ldmfd	[sp]!,{pc}

                    1711 	.endf	getAlignedDescrStruct

                    1712 	.align	4

                    1713 ;pDescrStructAlignOffset	r0	local

                    1714 ;descrTag	r0	local

                    1715 ;descrLen	[sp]	local

                    1716 

                    1717 ;pos	r2	param

                    1718 

                    1719 	.section ".bss","awb"

                    1720 .L2150:

                    1721 	.data

                    1722 	.text

                    1723 

                    1724 

                    1725 	.align	4

                    1726 	.align	4

                    1727 encodeTypeAccessAttrs::

00000850 e92d4010   1728 	stmfd	[sp]!,{r4,lr}

00000854 e5ddc008   1729 	ldrb	r12,[sp,8]

00000858 e1a04003   1730 	mov	r4,r3

0000085c e59f301c*  1731 	ldr	r3,.L2316

00000860 e252201f   1732 	subs	r2,r2,31

00000864 2a000014   1733 	bhs	.L2279

00000868 e292201f   1734 	adds	r2,r2,31

0000086c e352000b   1735 	cmp	r2,11

00000870 8a000050   1736 	bhi	.L2200

00000874 ea000002   1737 	b	.L2317

                    1738 	.align	4

                    1739 .L101:

00000878 00000000*  1740 	.data.w	iedModel

                    1741 	.type	.L101,$object

                    1742 	.size	.L101,4

                    1743 

                    1744 .L102:

0000087c 00000000*  1745 	.data.w	iedModelSize

                    1746 	.type	.L102,$object

                    1747 	.size	.L102,4

                    1748 

                    1749 .L2316:

00000880 00000000*  1750 	.data.w	encodeAccessAttrUInt

                    1751 	.type	.L2316,$object

                    1752 	.size	.L2316,4

                    1753 

                    1754 .L2317:

                    1755 

00000884 e08ff102   1756 	add	pc,pc,r2 lsl 2

                    1757 .L2280:

                    1758 


                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
00000888 e1a00000   1759 	nop	

0000088c ea000020   1760 	b	.L2178

00000890 ea000048   1761 	b	.L2200

00000894 ea00001b   1762 	b	.L2176

00000898 ea000020   1763 	b	.L2180

0000089c ea000022   1764 	b	.L2182

000008a0 ea000044   1765 	b	.L2200

000008a4 ea000034   1766 	b	.L2192

000008a8 ea000023   1767 	b	.L2184

000008ac ea000026   1768 	b	.L2186

000008b0 ea00003c   1769 	b	.L2198

000008b4 ea00003f   1770 	b	.L2200

000008b8 ea000027   1771 	b	.L2188

                    1772 	.align	4

                    1773 .L2279:

                    1774 

000008bc e352000a   1775 	cmp	r2,10

000008c0 8a00003c   1776 	bhi	.L2200

000008c4 e08ff102   1777 	add	pc,pc,r2 lsl 2

                    1778 .L2281:

                    1779 

000008c8 e1a00000   1780 	nop	

000008cc ea000009   1781 	b	.L2174

000008d0 ea00002c   1782 	b	.L2194

000008d4 ea00000b   1783 	b	.L2176

000008d8 ea000013   1784 	b	.L2182

000008dc ea00001a   1785 	b	.L2186

000008e0 ea000034   1786 	b	.L2200

000008e4 ea000014   1787 	b	.L2184

000008e8 ea00002a   1788 	b	.L2196

000008ec ea000005   1789 	b	.L2176

000008f0 ea000004   1790 	b	.L2176

000008f4 ea00001c   1791 	b	.L2190

                    1792 .L2174:

000008f8 e1a0300c   1793 	mov	r3,r12

000008fc e1a02004   1794 	mov	r2,r4

00000900 e8bd4010   1795 	ldmfd	[sp]!,{r4,lr}

00000904 ea000000*  1796 	b	encodeAccessAttrConst

                    1797 .L2176:

00000908 e1a0200c   1798 	mov	r2,r12

0000090c e8bd4010   1799 	ldmfd	[sp]!,{r4,lr}

00000910 ea000000*  1800 	b	encodeAccessAttrFloat

                    1801 .L2178:

00000914 e1a0200c   1802 	mov	r2,r12

00000918 e8bd4010   1803 	ldmfd	[sp]!,{r4,lr}

0000091c ea000000*  1804 	b	encodeAccessAttrQuality

                    1805 .L2180:

00000920 e1a0200c   1806 	mov	r2,r12

00000924 e8bd4010   1807 	ldmfd	[sp]!,{r4,lr}

00000928 ea000000*  1808 	b	encodeAccessAttrTimeStamp

                    1809 .L2182:

0000092c e1a0300c   1810 	mov	r3,r12

00000930 e3a02020   1811 	mov	r2,32

00000934 eb000000*  1812 	bl	encodeAccessAttrInt

00000938 ea00001f   1813 	b	.L2170

                    1814 .L2184:

0000093c e1a0300c   1815 	mov	r3,r12

00000940 e3a02008   1816 	mov	r2,8

00000944 eb000000*  1817 	bl	encodeAccessAttrInt

00000948 ea00001b   1818 	b	.L2170

                    1819 .L2186:


                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
0000094c e1a0300c   1820 	mov	r3,r12

00000950 e3a02020   1821 	mov	r2,32

00000954 eb000000*  1822 	bl	encodeAccessAttrUInt

00000958 ea000017   1823 	b	.L2170

                    1824 .L2188:

0000095c e1a0300c   1825 	mov	r3,r12

00000960 e3a02008   1826 	mov	r2,8

00000964 eb000000*  1827 	bl	encodeAccessAttrUInt

00000968 ea000013   1828 	b	.L2170

                    1829 .L2190:

0000096c e1a0300c   1830 	mov	r3,r12

00000970 e3a02040   1831 	mov	r2,64

00000974 eb000000*  1832 	bl	encodeAccessAttrInt

00000978 ea00000f   1833 	b	.L2170

                    1834 .L2192:

0000097c e1a0200c   1835 	mov	r2,r12

00000980 eb000000*  1836 	bl	encodeAccessAttrBoolean

00000984 ea00000c   1837 	b	.L2170

                    1838 .L2194:

00000988 e1a0300c   1839 	mov	r3,r12

0000098c e1a02004   1840 	mov	r2,r4

00000990 eb000000*  1841 	bl	encodeAccessAttrRCB

00000994 ea000008   1842 	b	.L2170

                    1843 .L2196:

00000998 e1a0300c   1844 	mov	r3,r12

0000099c e1a02004   1845 	mov	r2,r4

000009a0 eb000000*  1846 	bl	encodeAccessAttrGoCB

000009a4 ea000004   1847 	b	.L2170

                    1848 .L2198:

000009a8 e1a0300c   1849 	mov	r3,r12

000009ac e1a02004   1850 	mov	r2,r4

000009b0 eb000000*  1851 	bl	encodeAccessAttrCodedEnum

000009b4 ea000000   1852 	b	.L2170

                    1853 .L2200:

000009b8 e3a00000   1854 	mov	r0,0

                    1855 .L2170:

000009bc e8bd8010   1856 	ldmfd	[sp]!,{r4,pc}

                    1857 	.endf	encodeTypeAccessAttrs

                    1858 	.align	4

                    1859 

                    1860 ;outBuf	r0	param

                    1861 ;bufPos	r1	param

                    1862 ;type	r2	param

                    1863 ;attrDataPos	r4	param

                    1864 ;determineSize	r12	param

                    1865 

                    1866 	.section ".bss","awb"

                    1867 .L2278:

                    1868 	.data

                    1869 	.ghsnote jtable,5,.L2280,.L2280,.L2280,13

                    1870 	.ghsnote jtable,5,.L2281,.L2281,.L2281,12

                    1871 	.text

                    1872 

                    1873 

                    1874 	.align	4

                    1875 	.align	4

                    1876 encodeNameAttr:

000009c0 e92d44f0   1877 	stmfd	[sp]!,{r4-r7,r10,lr}

000009c4 e1a05002   1878 	mov	r5,r2

000009c8 e2852001   1879 	add	r2,r5,1

000009cc e1a04001   1880 	mov	r4,r1


                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
000009d0 e24dd004   1881 	sub	sp,sp,4

000009d4 e1a07003   1882 	mov	r7,r3

000009d8 e1a06000   1883 	mov	r6,r0

000009dc e59f084c*  1884 	ldr	r0,.L2392

000009e0 e59fc84c*  1885 	ldr	r12,.L2393

000009e4 e5903000   1886 	ldr	r3,[r0]

000009e8 e59c0000   1887 	ldr	r0,[r12]

000009ec e1a0100d   1888 	mov	r1,sp

000009f0 eb000000*  1889 	bl	BerDecoder_decodeLength

000009f4 e3500000   1890 	cmp	r0,0

000009f8 d3a00000   1891 	movle	r0,0

000009fc da00000e   1892 	ble	.L2318

00000a00 e59d1000   1893 	ldr	r1,[sp]

00000a04 e3570000   1894 	cmp	r7,0

00000a08 e0800001   1895 	add	r0,r0,r1

00000a0c e040a005   1896 	sub	r10,r0,r5

00000a10 11a0000a   1897 	movne	r0,r10

00000a14 1a000008   1898 	bne	.L2318

00000a18 e59f1814*  1899 	ldr	r1,.L2393

00000a1c e5910000   1900 	ldr	r0,[r1]

00000a20 e1a0200a   1901 	mov	r2,r10

00000a24 e0801005   1902 	add	r1,r0,r5

00000a28 e0840006   1903 	add	r0,r4,r6

00000a2c eb000000*  1904 	bl	memcpy

00000a30 e3a00080   1905 	mov	r0,128

00000a34 e7c60004   1906 	strb	r0,[r6,r4]

00000a38 e08a0004   1907 	add	r0,r10,r4

                    1908 .L2318:

00000a3c e28dd004   1909 	add	sp,sp,4

00000a40 e8bd44f0   1910 	ldmfd	[sp]!,{r4-r7,r10,lr}

00000a44 e12fff1e*  1911 	ret	

                    1912 	.endf	encodeNameAttr

                    1913 	.align	4

                    1914 ;totalLen	r10	local

                    1915 ;len	[sp]	local

                    1916 ;pos	r0	local

                    1917 

                    1918 ;outBuf	r6	param

                    1919 ;bufPos	r4	param

                    1920 ;namePos	r5	param

                    1921 ;determineSize	r7	param

                    1922 

                    1923 	.section ".bss","awb"

                    1924 .L2373:

                    1925 	.data

                    1926 	.text

                    1927 

                    1928 

                    1929 	.align	4

                    1930 	.align	4

                    1931 encodeSimpleDataAccessAttrs::

00000a48 e92d4ff0   1932 	stmfd	[sp]!,{r4-fp,lr}

00000a4c e24dd014   1933 	sub	sp,sp,20

00000a50 e5dda038   1934 	ldrb	r10,[sp,56]

00000a54 e2822001   1935 	add	r2,r2,1

00000a58 e1a06001   1936 	mov	r6,r1

00000a5c e5cd3007   1937 	strb	r3,[sp,7]

00000a60 e1a05000   1938 	mov	r5,r0

00000a64 e59f07c4*  1939 	ldr	r0,.L2392

00000a68 e59fc7c4*  1940 	ldr	r12,.L2393

00000a6c e5903000   1941 	ldr	r3,[r0]


                                                                      Page 33
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
00000a70 e59c0000   1942 	ldr	r0,[r12]

00000a74 e28d1008   1943 	add	r1,sp,8

00000a78 eb000000*  1944 	bl	BerDecoder_decodeLength

00000a7c e1a04000   1945 	mov	r4,r0

00000a80 e3740001   1946 	cmn	r4,1

00000a84 0a000037   1947 	beq	.L2412

00000a88 e1a08004   1948 	mov	r8,r4

00000a8c ebfffd6f*  1949 	bl	skipObject

00000a90 e1b04000   1950 	movs	r4,r0

00000a94 0a000033   1951 	beq	.L2412

00000a98 e59f7794*  1952 	ldr	r7,.L2393

00000a9c e5970000   1953 	ldr	r0,[r7]

00000aa0 e7d00004   1954 	ldrb	r0,[r0,r4]

00000aa4 ebfffdca*  1955 	bl	IEDModel_isServiceInfo

00000aa8 e3500000   1956 	cmp	r0,0

00000aac 0a000007   1957 	beq	.L2403

                    1958 .L2404:

00000ab0 e1a00004   1959 	mov	r0,r4

00000ab4 ebfffd65*  1960 	bl	skipObject

00000ab8 e1a04000   1961 	mov	r4,r0

00000abc e5970000   1962 	ldr	r0,[r7]

00000ac0 e7d00004   1963 	ldrb	r0,[r0,r4]

00000ac4 ebfffdc2*  1964 	bl	IEDModel_isServiceInfo

00000ac8 e3500000   1965 	cmp	r0,0

00000acc 1afffff7   1966 	bne	.L2404

                    1967 .L2403:

00000ad0 e59f375c*  1968 	ldr	r3,.L2393

00000ad4 e5930000   1969 	ldr	r0,[r3]

00000ad8 e7d01004   1970 	ldrb	r1,[r0,r4]

00000adc e2844001   1971 	add	r4,r4,1

00000ae0 e3510002   1972 	cmp	r1,2

00000ae4 1a00001f   1973 	bne	.L2412

00000ae8 e7d07004   1974 	ldrb	r7,[r0,r4]

00000aec e2844001   1975 	add	r4,r4,1

00000af0 e084b007   1976 	add	fp,r4,r7

00000af4 e1a02004   1977 	mov	r2,r4

00000af8 e1a01007   1978 	mov	r1,r7

00000afc eb000000*  1979 	bl	BerDecoder_decodeUint32

00000b00 e1a0300b   1980 	mov	r3,fp

00000b04 e1a01006   1981 	mov	r1,r6

00000b08 e1a09000   1982 	mov	r9,r0

00000b0c e1a02000   1983 	mov	r2,r0

00000b10 e3a00001   1984 	mov	r0,1

00000b14 e58d0000   1985 	str	r0,[sp]

00000b18 e1a00005   1986 	mov	r0,r5

00000b1c ebffff4b*  1987 	bl	encodeTypeAccessAttrs

00000b20 e1a07000   1988 	mov	r7,r0

00000b24 e35a0000   1989 	cmp	r10,0

00000b28 0a000004   1990 	beq	.L2408

00000b2c e5dd0007   1991 	ldrb	r0,[sp,7]

00000b30 e2874003   1992 	add	r4,r7,3

00000b34 e3500000   1993 	cmp	r0,0

00000b38 0a000015   1994 	beq	.L2414

00000b3c ea00000f   1995 	b	.L2415

                    1996 .L2408:

00000b40 eb000000*  1997 	bl	BerEncoder_determineLengthSize

00000b44 e1a02008   1998 	mov	r2,r8

00000b48 e3a03001   1999 	mov	r3,1

00000b4c e0870000   2000 	add	r0,r7,r0

00000b50 e2804001   2001 	add	r4,r0,1

00000b54 e3a01000   2002 	mov	r1,0


                                                                      Page 34
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
00000b58 e1a00001   2003 	mov	r0,r1

00000b5c ebffff97*  2004 	bl	encodeNameAttr

00000b60 e3500000   2005 	cmp	r0,0

00000b64 ca000001   2006 	bgt	.L2411

                    2007 .L2412:

00000b68 e3a00000   2008 	mov	r0,0

00000b6c ea000030   2009 	b	.L2394

                    2010 .L2411:

00000b70 e0844000   2011 	add	r4,r4,r0

00000b74 e5dd0007   2012 	ldrb	r0,[sp,7]

00000b78 e3500000   2013 	cmp	r0,0

00000b7c 0a000004   2014 	beq	.L2414

                    2015 .L2415:

00000b80 e1a00004   2016 	mov	r0,r4

00000b84 eb000000*  2017 	bl	BerEncoder_determineLengthSize

00000b88 e0840000   2018 	add	r0,r4,r0

00000b8c e2800001   2019 	add	r0,r0,1

00000b90 ea000027   2020 	b	.L2394

                    2021 .L2414:

00000b94 e1a03006   2022 	mov	r3,r6

00000b98 e1a02005   2023 	mov	r2,r5

00000b9c e35a0000   2024 	cmp	r10,0

00000ba0 1a000014   2025 	bne	.L2417

00000ba4 e1a01004   2026 	mov	r1,r4

00000ba8 e3a00030   2027 	mov	r0,48

00000bac eb000000*  2028 	bl	BerEncoder_encodeTL

00000bb0 e1a02008   2029 	mov	r2,r8

00000bb4 e1a01000   2030 	mov	r1,r0

00000bb8 e1a00005   2031 	mov	r0,r5

00000bbc e3a03000   2032 	mov	r3,0

00000bc0 ebffff7e*  2033 	bl	encodeNameAttr

00000bc4 e1a03000   2034 	mov	r3,r0

00000bc8 e3a000a1   2035 	mov	r0,161

00000bcc e1a02005   2036 	mov	r2,r5

00000bd0 e1a01007   2037 	mov	r1,r7

00000bd4 eb000000*  2038 	bl	BerEncoder_encodeTL

00000bd8 e1a0300b   2039 	mov	r3,fp

00000bdc e1a02009   2040 	mov	r2,r9

00000be0 e1a01000   2041 	mov	r1,r0

00000be4 e3a00000   2042 	mov	r0,0

00000be8 e58d0000   2043 	str	r0,[sp]

00000bec e1a00005   2044 	mov	r0,r5

00000bf0 ebffff16*  2045 	bl	encodeTypeAccessAttrs

00000bf4 ea00000e   2046 	b	.L2394

                    2047 .L2417:

00000bf8 e3a01000   2048 	mov	r1,0

00000bfc e3a00080   2049 	mov	r0,128

00000c00 eb000000*  2050 	bl	BerEncoder_encodeBoolean

00000c04 e1a03000   2051 	mov	r3,r0

00000c08 e3a000a2   2052 	mov	r0,162

00000c0c e1a02005   2053 	mov	r2,r5

00000c10 e1a01007   2054 	mov	r1,r7

00000c14 eb000000*  2055 	bl	BerEncoder_encodeTL

00000c18 e1a0300b   2056 	mov	r3,fp

00000c1c e1a02009   2057 	mov	r2,r9

00000c20 e1a01000   2058 	mov	r1,r0

00000c24 e3a00000   2059 	mov	r0,0

00000c28 e58d0000   2060 	str	r0,[sp]

00000c2c e1a00005   2061 	mov	r0,r5

00000c30 ebffff06*  2062 	bl	encodeTypeAccessAttrs

                    2063 .L2394:


                                                                      Page 35
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
00000c34 e28dd014   2064 	add	sp,sp,20

00000c38 e8bd8ff0   2065 	ldmfd	[sp]!,{r4-fp,pc}

                    2066 	.endf	encodeSimpleDataAccessAttrs

                    2067 	.align	4

                    2068 ;typeIdTag	r1	local

                    2069 ;typeIdLen	r7	local

                    2070 ;typeId	r9	local

                    2071 ;typeDescrSize	r7	local

                    2072 ;pos	r4	local

                    2073 ;objectSize	[sp,8]	local

                    2074 ;sequenceSize	r4	local

                    2075 ;objectNamePos	r8	local

                    2076 ;nameLen	r0	local

                    2077 ;daDataPos	fp	local

                    2078 

                    2079 ;outBuf	r5	param

                    2080 ;bufPos	r6	param

                    2081 ;objectPos	r2	param

                    2082 ;determineSize	[sp,7]	param

                    2083 ;topStruct	r10	param

                    2084 

                    2085 	.section ".bss","awb"

                    2086 .L2603:

                    2087 	.data

                    2088 	.text

                    2089 

                    2090 

                    2091 	.align	4

                    2092 	.align	4

                    2093 encodeObjectAccessAttrs::

00000c3c e92d4ff0   2094 	stmfd	[sp]!,{r4-fp,lr}

00000c40 e24dd018   2095 	sub	sp,sp,24

00000c44 e59fc5e8*  2096 	ldr	r12,.L2393

00000c48 e1a06000   2097 	mov	r6,r0

00000c4c e59c0000   2098 	ldr	r0,[r12]

00000c50 e7d0c002   2099 	ldrb	r12,[r0,r2]

00000c54 e5dd503c   2100 	ldrb	r5,[sp,60]

00000c58 e35c00e9   2101 	cmp	r12,233

00000c5c 1a000003   2102 	bne	.L2640

00000c60 e58d5000   2103 	str	r5,[sp]

00000c64 e1a00006   2104 	mov	r0,r6

00000c68 ebffff76*  2105 	bl	encodeSimpleDataAccessAttrs

00000c6c ea00005f   2106 	b	.L2638

                    2107 .L2640:

00000c70 e58d200c   2108 	str	r2,[sp,12]

00000c74 e1a07002   2109 	mov	r7,r2

00000c78 e2822001   2110 	add	r2,r2,1

                    2111 ;814:     int objectSize;


                    2112 ;815: 


                    2113 ;816: 


                    2114 ;817:     //Пропускаем тэг


                    2115 ;818:     pos++;


                    2116 

                    2117 ;819: 


                    2118 ;820:     //Пропускаем длину


                    2119 ;821:     pos = BerDecoder_decodeLength(iedModel, &objectSize, pos, iedModelSize);


                    2120 

00000c7c e1a04001   2121 	mov	r4,r1

00000c80 e59f15a8*  2122 	ldr	r1,.L2392

00000c84 e5cd3007   2123 	strb	r3,[sp,7]

                    2124 ;804:                           bool topStruct)



                                                                      Page 36
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                    2125 ;805: {


                    2126 

                    2127 ;806:     int totalSize;


                    2128 ;807:     int sequenceSize;


                    2129 ;808:     int descrTypeSize;


                    2130 ;809:     int structureSize;


                    2131 ;810:     int fieldListSize;


                    2132 ;811:     int nameLen;


                    2133 ;812:     int objectNamePos;


                    2134 ;813:     int pos = objectPos;


                    2135 

00000c88 e5913000   2136 	ldr	r3,[r1]

00000c8c e28d1008   2137 	add	r1,sp,8

00000c90 eb000000*  2138 	bl	BerDecoder_decodeLength

                    2139 ;822:     if(pos == -1)


                    2140 

00000c94 e3700001   2141 	cmn	r0,1

00000c98 0a00001f   2142 	beq	.L2651

                    2143 ;823:     {


                    2144 

                    2145 ;824:         return 0;


                    2146 

                    2147 ;825:     }


                    2148 ;826: 


                    2149 ;827:     objectNamePos = pos;


                    2150 

00000c9c e1a02007   2151 	mov	r2,r7

00000ca0 e1a01004   2152 	mov	r1,r4

00000ca4 e1a08000   2153 	mov	r8,r0

                    2154 ;828: 


                    2155 ;829: 


                    2156 ;830:     //================= Определяем размеры ===================


                    2157 ;831:     fieldListSize = encodeChildrenAccessAttrs(outBuf, bufPos,  objectPos, TRUE);


                    2158 

00000ca8 e1a00006   2159 	mov	r0,r6

00000cac e3a03001   2160 	mov	r3,1

00000cb0 eb000050*  2161 	bl	encodeChildrenAccessAttrs

                    2162 ;832:     structureSize = 1


                    2163 

00000cb4 e1a09000   2164 	mov	r9,r0

00000cb8 eb000000*  2165 	bl	BerEncoder_determineLengthSize

00000cbc e0890000   2166 	add	r0,r9,r0

00000cc0 e280a001   2167 	add	r10,r0,1

                    2168 ;833:             + BerEncoder_determineLengthSize(fieldListSize)


                    2169 ;834:             + fieldListSize;


                    2170 ;835: 


                    2171 ;836:     descrTypeSize = 1


                    2172 

00000cc4 e1a0000a   2173 	mov	r0,r10

00000cc8 eb000000*  2174 	bl	BerEncoder_determineLengthSize

00000ccc e08a0000   2175 	add	r0,r10,r0

00000cd0 e280b001   2176 	add	fp,r0,1

                    2177 ;837:             + BerEncoder_determineLengthSize(structureSize)


                    2178 ;838:             + structureSize;


                    2179 ;839: 


                    2180 ;840:     sequenceSize = 1


                    2181 

00000cd4 e1a0000b   2182 	mov	r0,fp

00000cd8 eb000000*  2183 	bl	BerEncoder_determineLengthSize

00000cdc e08b0000   2184 	add	r0,fp,r0

00000ce0 e2807001   2185 	add	r7,r0,1


                                                                      Page 37
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                    2186 ;841:         + BerEncoder_determineLengthSize(descrTypeSize)


                    2187 ;842:         + descrTypeSize;


                    2188 ;843: 


                    2189 ;844:     if(topStruct)


                    2190 

00000ce4 e3550000   2191 	cmp	r5,0

00000ce8 0a000004   2192 	beq	.L2650

                    2193 ;845:     {


                    2194 

                    2195 ;846:         //+Deletable size


                    2196 ;847:         totalSize = sequenceSize + 3;


                    2197 

00000cec e5dd1007   2198 	ldrb	r1,[sp,7]

00000cf0 e2800004   2199 	add	r0,r0,4

                    2200 ;861:             + BerEncoder_determineLengthSize(sequenceSize)


                    2201 ;862:             + sequenceSize;


                    2202 ;863:     }


                    2203 ;864: 


                    2204 ;865:     if(determineSize)


                    2205 

00000cf4 e3510000   2206 	cmp	r1,0

00000cf8 1a000010   2207 	bne	.L2654

00000cfc ea000010   2208 	b	.L2655

                    2209 .L2650:

                    2210 ;848:     }


                    2211 ;849:     else


                    2212 ;850:     {


                    2213 

                    2214 ;851: 


                    2215 ;852:         //+Name size


                    2216 ;853:         nameLen = encodeNameAttr(NULL, 0, objectNamePos, TRUE);


                    2217 

00000d00 e1a02008   2218 	mov	r2,r8

00000d04 e3a03001   2219 	mov	r3,1

00000d08 e3a01000   2220 	mov	r1,0

00000d0c e1a00001   2221 	mov	r0,r1

00000d10 ebffff2a*  2222 	bl	encodeNameAttr

                    2223 ;854:         if(nameLen < 1)


                    2224 

00000d14 e3500000   2225 	cmp	r0,0

00000d18 ca000001   2226 	bgt	.L2652

                    2227 .L2651:

                    2228 ;855:         {


                    2229 

                    2230 ;856:             return 0;


                    2231 

00000d1c e3a00000   2232 	mov	r0,0

00000d20 ea000032   2233 	b	.L2638

                    2234 .L2652:

                    2235 ;857:         }


                    2236 ;858:         sequenceSize += nameLen;


                    2237 

00000d24 e0877000   2238 	add	r7,r7,r0

                    2239 ;859: 


                    2240 ;860:         totalSize = 1


                    2241 

00000d28 e1a00007   2242 	mov	r0,r7

00000d2c eb000000*  2243 	bl	BerEncoder_determineLengthSize

00000d30 e0870000   2244 	add	r0,r7,r0

00000d34 e5dd1007   2245 	ldrb	r1,[sp,7]

00000d38 e2800001   2246 	add	r0,r0,1


                                                                      Page 38
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                    2247 ;861:             + BerEncoder_determineLengthSize(sequenceSize)


                    2248 ;862:             + sequenceSize;


                    2249 ;863:     }


                    2250 ;864: 


                    2251 ;865:     if(determineSize)


                    2252 

00000d3c e3510000   2253 	cmp	r1,0

                    2254 .L2654:

                    2255 ;866:     {


                    2256 

                    2257 ;867:         return totalSize;


                    2258 

00000d40 1a00002a   2259 	bne	.L2638

                    2260 .L2655:

                    2261 ;868:     }


                    2262 ;869: 


                    2263 ;870:     //=================== Пишем ==============================


                    2264 ;871:     if(!topStruct)


                    2265 

00000d44 e1a03004   2266 	mov	r3,r4

00000d48 e1a02006   2267 	mov	r2,r6

00000d4c e3550000   2268 	cmp	r5,0

00000d50 1a00000c   2269 	bne	.L2657

                    2270 ;872:     {


                    2271 

                    2272 ;873:         //Если не topStruct пишем sequence


                    2273 ;874:         bufPos = BerEncoder_encodeTL(ASN_SEQUENCE, sequenceSize,


                    2274 

00000d54 e1a01007   2275 	mov	r1,r7

00000d58 e3a00030   2276 	mov	r0,48

00000d5c eb000000*  2277 	bl	BerEncoder_encodeTL

                    2278 ;875:                                      outBuf, bufPos);


                    2279 ;876:         //Имя объекта


                    2280 ;877:         bufPos = encodeNameAttr(outBuf, bufPos, objectNamePos, FALSE);


                    2281 

00000d60 e1a02008   2282 	mov	r2,r8

00000d64 e1a01000   2283 	mov	r1,r0

00000d68 e1a00006   2284 	mov	r0,r6

00000d6c e3a03000   2285 	mov	r3,0

00000d70 ebffff12*  2286 	bl	encodeNameAttr

00000d74 e1a04000   2287 	mov	r4,r0

                    2288 ;883:     }


                    2289 ;884: 


                    2290 ;885:     //Описание типа


                    2291 ;886:     bufPos = BerEncoder_encodeTL(


                    2292 

00000d78 e3a000a1   2293 	mov	r0,161

00000d7c e3550000   2294 	cmp	r5,0

00000d80 1a000006   2295 	bne	.L2659

00000d84 ea000006   2296 	b	.L2660

                    2297 .L2657:

                    2298 ;878:     }


                    2299 ;879:     else


                    2300 ;880:     {


                    2301 

                    2302 ;881:         //Deletable


                    2303 ;882:         bufPos = BerEncoder_encodeBoolean(VAR_DELETABLE, FALSE, outBuf, bufPos);


                    2304 

00000d88 e3a01000   2305 	mov	r1,0

00000d8c e3a00080   2306 	mov	r0,128

00000d90 eb000000*  2307 	bl	BerEncoder_encodeBoolean


                                                                      Page 39
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
00000d94 e1a04000   2308 	mov	r4,r0

                    2309 ;883:     }


                    2310 ;884: 


                    2311 ;885:     //Описание типа


                    2312 ;886:     bufPos = BerEncoder_encodeTL(


                    2313 

00000d98 e3a000a1   2314 	mov	r0,161

00000d9c e3550000   2315 	cmp	r5,0

                    2316 .L2659:

00000da0 13a000a2   2317 	movne	r0,162

                    2318 .L2660:

00000da4 e1a03004   2319 	mov	r3,r4

00000da8 e1a02006   2320 	mov	r2,r6

00000dac e1a0100b   2321 	mov	r1,fp

00000db0 eb000000*  2322 	bl	BerEncoder_encodeTL

                    2323 ;887: 


                    2324 ;888:                 //Дурацкий хак


                    2325 ;889:                 topStruct? ASN_TYPEDESCRIPTION_STRUCTURE:


                    2326 ;890:                            ASN_TYPEDESCRIPTION_COMPONENT_TYPE,


                    2327 ;891: 


                    2328 ;892:                                  descrTypeSize, outBuf, bufPos);


                    2329 ;893: 


                    2330 ;894:     //Structure


                    2331 ;895:     bufPos = BerEncoder_encodeTL(ASN_TYPEDESCRIPTION_STRUCTURE,


                    2332 

00000db4 e1a02006   2333 	mov	r2,r6

00000db8 e1a0100a   2334 	mov	r1,r10

00000dbc e1a03000   2335 	mov	r3,r0

00000dc0 e3a000a2   2336 	mov	r0,162

00000dc4 eb000000*  2337 	bl	BerEncoder_encodeTL

                    2338 ;896:                                  structureSize, outBuf, bufPos);


                    2339 ;897:     //Тэг списка полей tag=0xa1


                    2340 ;898:     bufPos = BerEncoder_encodeTL(0xA1, fieldListSize, outBuf, bufPos);


                    2341 

00000dc8 e1a02006   2342 	mov	r2,r6

00000dcc e1a01009   2343 	mov	r1,r9

00000dd0 e1a03000   2344 	mov	r3,r0

00000dd4 e3a000a1   2345 	mov	r0,161

00000dd8 eb000000*  2346 	bl	BerEncoder_encodeTL

                    2347 ;899: 


                    2348 ;900:     //Сами поля


                    2349 ;901:     bufPos = encodeChildrenAccessAttrs(outBuf, bufPos,  objectPos, FALSE);


                    2350 

00000ddc e59d200c   2351 	ldr	r2,[sp,12]

00000de0 e1a01000   2352 	mov	r1,r0

00000de4 e1a00006   2353 	mov	r0,r6

00000de8 e3a03000   2354 	mov	r3,0

00000dec eb000001*  2355 	bl	encodeChildrenAccessAttrs

                    2356 ;902:     return bufPos;


                    2357 

                    2358 .L2638:

00000df0 e28dd018   2359 	add	sp,sp,24

00000df4 e8bd8ff0   2360 	ldmfd	[sp]!,{r4-fp,pc}

                    2361 	.endf	encodeObjectAccessAttrs

                    2362 	.align	4

                    2363 ;tag	r12	local

                    2364 ;bufPos	r4	local

                    2365 ;objectPos	[sp,12]	local

                    2366 ;determineSize	[sp,7]	local

                    2367 ;topStruct	r5	local

                    2368 ;totalSize	r0	local


                                                                      Page 40
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                    2369 ;sequenceSize	r7	local

                    2370 ;descrTypeSize	fp	local

                    2371 ;structureSize	r10	local

                    2372 ;fieldListSize	r9	local

                    2373 ;nameLen	r0	local

                    2374 ;objectNamePos	r8	local

                    2375 ;pos	r0	local

                    2376 ;objectSize	[sp,8]	local

                    2377 

                    2378 ;outBuf	r6	param

                    2379 ;bufPos	r1	param

                    2380 ;objectPos	r2	param

                    2381 ;determineSize	r3	param

                    2382 ;topStruct	r5	param

                    2383 

                    2384 	.section ".bss","awb"

                    2385 .L2815:

                    2386 	.data

                    2387 	.text

                    2388 

                    2389 

                    2390 	.align	4

                    2391 	.align	4

                    2392 encodeChildrenAccessAttrs::

00000df8 e92d4df0   2393 	stmfd	[sp]!,{r4-r8,r10-fp,lr}

00000dfc e3a05000   2394 	mov	r5,0

00000e00 e1a0a003   2395 	mov	r10,r3

00000e04 e24dd010   2396 	sub	sp,sp,16

00000e08 e1a04001   2397 	mov	r4,r1

00000e0c e28d1004   2398 	add	r1,sp,4

00000e10 e1a08000   2399 	mov	r8,r0

00000e14 e1a00002   2400 	mov	r0,r2

00000e18 ebfffcf5*  2401 	bl	getSubObjectsPos

00000e1c e1b06000   2402 	movs	r6,r0

00000e20 0a000004   2403 	beq	.L2851

00000e24 e59d1004   2404 	ldr	r1,[sp,4]

00000e28 e59fb404*  2405 	ldr	fp,.L2393

00000e2c e1560001   2406 	cmp	r6,r1

00000e30 aa000018   2407 	bge	.L2854

00000e34 ea000001   2408 	b	.L2855

                    2409 .L2851:

00000e38 e3a00000   2410 	mov	r0,0

00000e3c ea000018   2411 	b	.L2848

                    2412 .L2855:

00000e40 e59f03e8*  2413 	ldr	r0,.L2392

00000e44 e2862001   2414 	add	r2,r6,1

00000e48 e5903000   2415 	ldr	r3,[r0]

00000e4c e59b0000   2416 	ldr	r0,[fp]

00000e50 e28d1008   2417 	add	r1,sp,8

00000e54 eb000000*  2418 	bl	BerDecoder_decodeLength

00000e58 e2507000   2419 	subs	r7,r0,0

00000e5c dafffff5   2420 	ble	.L2851

00000e60 e1a0300a   2421 	mov	r3,r10

00000e64 e1a02006   2422 	mov	r2,r6

00000e68 e3a01000   2423 	mov	r1,0

00000e6c e58d1000   2424 	str	r1,[sp]

00000e70 e1a01004   2425 	mov	r1,r4

00000e74 e1a00008   2426 	mov	r0,r8

00000e78 ebffff6f*  2427 	bl	encodeObjectAccessAttrs

00000e7c e1a04000   2428 	mov	r4,r0

00000e80 e35a0000   2429 	cmp	r10,0


                                                                      Page 41
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
00000e84 10855004   2430 	addne	r5,r5,r4

00000e88 e99d0042   2431 	ldmed	[sp],{r1,r6}

00000e8c e0866007   2432 	add	r6,r6,r7

00000e90 e1560001   2433 	cmp	r6,r1

00000e94 baffffe9   2434 	blt	.L2855

                    2435 .L2854:

00000e98 e35a0000   2436 	cmp	r10,0

00000e9c 11a00005   2437 	movne	r0,r5

00000ea0 01a00004   2438 	moveq	r0,r4

                    2439 .L2848:

00000ea4 e28dd010   2440 	add	sp,sp,16

00000ea8 e8bd8df0   2441 	ldmfd	[sp]!,{r4-r8,r10-fp,pc}

                    2442 	.endf	encodeChildrenAccessAttrs

                    2443 	.align	4

                    2444 ;totalSize	r5	local

                    2445 ;objEndPos	[sp,4]	local

                    2446 ;childObjectPos	r6	local

                    2447 ;subObjLen	[sp,8]	local

                    2448 ;objContentPos	r7	local

                    2449 

                    2450 ;outBuf	r8	param

                    2451 ;bufPos	r4	param

                    2452 ;rootObjPos	r2	param

                    2453 ;determineSize	r10	param

                    2454 

                    2455 	.section ".bss","awb"

                    2456 .L2995:

                    2457 	.data

                    2458 	.text

                    2459 

                    2460 

                    2461 ;904: 


                    2462 ;905: void* IEDModel_ptrFromPos(size_t pos)


                    2463 	.align	4

                    2464 	.align	4

                    2465 IEDModel_ptrFromPos::

                    2466 ;906: {


                    2467 

                    2468 ;907:     if (pos >= (size_t)iedModelSize)


                    2469 

00000eac e59f237c*  2470 	ldr	r2,.L2392

00000eb0 e1a01000   2471 	mov	r1,r0

00000eb4 e5920000   2472 	ldr	r0,[r2]

00000eb8 e1510000   2473 	cmp	r1,r0

                    2474 ;908:     {


                    2475 

                    2476 ;909:         return NULL;


                    2477 

00000ebc 359f2370*  2478 	ldrlo	r2,.L2393

00000ec0 23a00000   2479 	movhs	r0,0

                    2480 ;910:     }


                    2481 ;911:     return iedModel + pos;


                    2482 

00000ec4 35920000   2483 	ldrlo	r0,[r2]

00000ec8 30800001   2484 	addlo	r0,r0,r1

00000ecc e12fff1e*  2485 	ret	

                    2486 	.endf	IEDModel_ptrFromPos

                    2487 	.align	4

                    2488 

                    2489 ;pos	r1	param

                    2490 


                                                                      Page 42
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                    2491 	.section ".bss","awb"

                    2492 .L3062:

                    2493 	.data

                    2494 	.text

                    2495 

                    2496 ;912: }


                    2497 

                    2498 ;913: 


                    2499 ;914: void processSubobjects(int parentPos, void(*func)(int))


                    2500 	.align	4

                    2501 	.align	4

                    2502 processSubobjects::

00000ed0 e92d4cf0   2503 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

00000ed4 e24dd008   2504 	sub	sp,sp,8

00000ed8 e1a06001   2505 	mov	r6,r1

                    2506 ;915: {


                    2507 

                    2508 ;916:     int objPos;


                    2509 ;917:     int endPos;


                    2510 ;918: 


                    2511 ;919:     objPos = getSubObjectsPos(parentPos, &endPos);


                    2512 

00000edc e1a0100d   2513 	mov	r1,sp

00000ee0 ebfffcc3*  2514 	bl	getSubObjectsPos

00000ee4 e1b04000   2515 	movs	r4,r0

                    2516 ;920:     if (objPos == 0)


                    2517 

00000ee8 0a000013   2518 	beq	.L3075

00000eec e59f733c*  2519 	ldr	r7,.L2392

00000ef0 e59fb33c*  2520 	ldr	fp,.L2393

00000ef4 e59d0000   2521 	ldr	r0,[sp]

00000ef8 e28da004   2522 	add	r10,sp,4

00000efc e1540000   2523 	cmp	r4,r0

00000f00 aa00000d   2524 	bge	.L3075

                    2525 .L3082:

                    2526 ;921:     {


                    2527 

                    2528 ;922:         ERROR_REPORT("Error reading objects at pos = %d", parentPos);


                    2529 ;923:         return;


                    2530 

                    2531 ;924:     }


                    2532 ;925:     while (objPos < endPos)


                    2533 

                    2534 ;926:     {


                    2535 

                    2536 ;927:         int objLen;


                    2537 ;928:         int pos = objPos;


                    2538 

00000f04 e2842001   2539 	add	r2,r4,1

                    2540 ;929:         //Skip tag


                    2541 ;930:         pos++;


                    2542 

                    2543 ;931:         pos = BerDecoder_decodeLength(iedModel, &objLen, pos, iedModelSize);


                    2544 

00000f08 e5973000   2545 	ldr	r3,[r7]

00000f0c e59b0000   2546 	ldr	r0,[fp]

00000f10 e1a0100a   2547 	mov	r1,r10

00000f14 eb000000*  2548 	bl	BerDecoder_decodeLength

00000f18 e2505000   2549 	subs	r5,r0,0

                    2550 ;932:         if (pos < 1)


                    2551 


                                                                      Page 43
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
00000f1c da000006   2552 	ble	.L3075

                    2553 ;933:         {


                    2554 

                    2555 ;934:             ERROR_REPORT("Error reading object length");


                    2556 ;935:             return;


                    2557 

                    2558 ;936:         }


                    2559 ;937:         func(objPos);


                    2560 

00000f20 e1a00004   2561 	mov	r0,r4

00000f24 e1a0e00f   2562 	mov	lr,pc

00000f28 e12fff16*  2563 	bx	r6

                    2564 ;938:         objPos = pos + objLen;


                    2565 

00000f2c e89d0011   2566 	ldmfd	[sp],{r0,r4}

00000f30 e0844005   2567 	add	r4,r4,r5

00000f34 e1540000   2568 	cmp	r4,r0

00000f38 bafffff1   2569 	blt	.L3082

                    2570 .L3075:

00000f3c e28dd008   2571 	add	sp,sp,8

00000f40 e8bd8cf0   2572 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                    2573 	.endf	processSubobjects

                    2574 	.align	4

                    2575 ;objPos	r4	local

                    2576 ;endPos	[sp]	local

                    2577 ;objLen	[sp,4]	local

                    2578 ;pos	r5	local

                    2579 

                    2580 ;parentPos	none	param

                    2581 ;func	r6	param

                    2582 

                    2583 	.section ".bss","awb"

                    2584 .L3145:

                    2585 	.data

                    2586 	.text

                    2587 

                    2588 ;939:     }


                    2589 ;940: }


                    2590 

                    2591 ;941: 


                    2592 ;942: int getDAValuePos(int rootObjPos, uint8_t* name, int nameLen,


                    2593 	.align	4

                    2594 	.align	4

                    2595 getDAValuePos::

00000f44 e92d4070   2596 	stmfd	[sp]!,{r4-r6,lr}

00000f48 e24dd004   2597 	sub	sp,sp,4

00000f4c e1a04003   2598 	mov	r4,r3

                    2599 ;943:     enum InnerAttributeType* attrType)


                    2600 ;944: {


                    2601 

                    2602 ;945:     int pos;


                    2603 ;946:     uint8_t tag;


                    2604 ;947:     uint8_t typeIdTag;


                    2605 ;948:     int typeIdLen;


                    2606 ;949:     int daPos = findObjectBySimpleName(rootObjPos, name, nameLen);


                    2607 

00000f50 ebfffccd*  2608 	bl	findObjectBySimpleName

                    2609 ;950:     RET_IF_NOT(daPos, "Unable to find DA by name");


                    2610 

00000f54 e3500000   2611 	cmp	r0,0

00000f58 0a00000e   2612 	beq	.L3171


                                                                      Page 44
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                    2613 ;951:     pos = readTL(daPos, &tag, NULL, NULL);


                    2614 

00000f5c e28d1003   2615 	add	r1,sp,3

00000f60 e3a03000   2616 	mov	r3,0

00000f64 e1a02003   2617 	mov	r2,r3

00000f68 ebfffc29*  2618 	bl	readTL

                    2619 ;952:     RET_IF_NOT(pos, "Error reading DA at %d", daPos);


                    2620 

00000f6c e3500000   2621 	cmp	r0,0

00000f70 0a000008   2622 	beq	.L3171

                    2623 ;953:     //Skip name


                    2624 ;954:     pos = skipObject(pos);


                    2625 

00000f74 ebfffc35*  2626 	bl	skipObject

00000f78 e1b05000   2627 	movs	r5,r0

                    2628 ;955:     RET_IF_NOT(pos, "Error reading DA at %d", daPos);


                    2629 

00000f7c 0a000005   2630 	beq	.L3171

                    2631 ;956: 


                    2632 ;957:     //Получаем идентификатор типа


                    2633 ;958:     typeIdTag = iedModel[pos++];


                    2634 

00000f80 e59f32ac*  2635 	ldr	r3,.L2393

00000f84 e5930000   2636 	ldr	r0,[r3]

00000f88 e7d01005   2637 	ldrb	r1,[r0,r5]

00000f8c e2855001   2638 	add	r5,r5,1

                    2639 ;959:     RET_IF_NOT(typeIdTag == ASN_INTEGER, "Error reading DA at %d", daPos);


                    2640 

00000f90 e3510002   2641 	cmp	r1,2

00000f94 0a000001   2642 	beq	.L3170

                    2643 .L3171:

00000f98 e3a00000   2644 	mov	r0,0

00000f9c ea000006   2645 	b	.L3159

                    2646 .L3170:

                    2647 ;960:     typeIdLen = iedModel[pos++];


                    2648 

00000fa0 e7d06005   2649 	ldrb	r6,[r0,r5]

00000fa4 e2855001   2650 	add	r5,r5,1

                    2651 ;961:     //Получаем идентификатор типа


                    2652 ;962:     *attrType = (enum InnerAttributeType)


                    2653 

00000fa8 e1a02005   2654 	mov	r2,r5

00000fac e1a01006   2655 	mov	r1,r6

00000fb0 eb000000*  2656 	bl	BerDecoder_decodeUint32

00000fb4 e5840000   2657 	str	r0,[r4]

                    2658 ;963:         BerDecoder_decodeUint32(iedModel, typeIdLen, pos);


                    2659 ;964:     pos += typeIdLen;


                    2660 

00000fb8 e0850006   2661 	add	r0,r5,r6

                    2662 ;965:     //pos указывает на константу или OCTET_STRING со структурой


                    2663 ;966:     return pos;


                    2664 

                    2665 .L3159:

00000fbc e28dd004   2666 	add	sp,sp,4

00000fc0 e8bd8070   2667 	ldmfd	[sp]!,{r4-r6,pc}

                    2668 	.endf	getDAValuePos

                    2669 	.align	4

                    2670 ;pos	r5	local

                    2671 ;tag	[sp,3]	local

                    2672 ;typeIdTag	r1	local

                    2673 ;typeIdLen	r6	local


                                                                      Page 45
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                    2674 ;daPos	r1	local

                    2675 

                    2676 ;rootObjPos	none	param

                    2677 ;name	none	param

                    2678 ;nameLen	none	param

                    2679 ;attrType	r4	param

                    2680 

                    2681 	.section ".bss","awb"

                    2682 .L3248:

                    2683 	.data

                    2684 	.text

                    2685 

                    2686 ;967: }


                    2687 

                    2688 ;968: 


                    2689 ;969: //Возвращает значение константного DA в виде StringView


                    2690 ;970: bool getConstDAString(size_t parentPos, const char* attrName,


                    2691 	.align	4

                    2692 	.align	4

                    2693 getConstDAString::

00000fc4 e92d4070   2694 	stmfd	[sp]!,{r4-r6,lr}

                    2695 ;971:     StringView* result)


                    2696 ;972: {


                    2697 

                    2698 ;973:     uint8_t tag;


                    2699 ;974:     int len;


                    2700 ;975:     enum InnerAttributeType attrType;


                    2701 ;976:     int daPos = getDAValuePos(parentPos, (uint8_t*) attrName, strlen(attrName),


                    2702 

00000fc8 e24dd00c   2703 	sub	sp,sp,12

00000fcc e1a06002   2704 	mov	r6,r2

00000fd0 e1a05000   2705 	mov	r5,r0

00000fd4 e1a04001   2706 	mov	r4,r1

00000fd8 e1a00004   2707 	mov	r0,r4

00000fdc eb000000*  2708 	bl	strlen

00000fe0 e28d3008   2709 	add	r3,sp,8

00000fe4 e1a01004   2710 	mov	r1,r4

00000fe8 e1a02000   2711 	mov	r2,r0

00000fec e1a00005   2712 	mov	r0,r5

00000ff0 ebffffd3*  2713 	bl	getDAValuePos

                    2714 ;977:         &attrType);


                    2715 ;978:     RET_IF_NOT(daPos, "Error reading %s", attrName);


                    2716 

00000ff4 e3500000   2717 	cmp	r0,0

00000ff8 0a000004   2718 	beq	.L3271

                    2719 ;979:     //pos указывает на константу


                    2720 ;980:     daPos = readTL(daPos, &tag, &len, NULL);


                    2721 

00000ffc e28d2004   2722 	add	r2,sp,4

00001000 e28d1003   2723 	add	r1,sp,3

00001004 e3a03000   2724 	mov	r3,0

00001008 ebfffc01*  2725 	bl	readTL

0000100c e1b03000   2726 	movs	r3,r0

                    2727 ;981:     RET_IF_NOT(daPos, "Error reading value at %d", daPos);


                    2728 

                    2729 .L3271:

00001010 03a00000   2730 	moveq	r0,0

00001014 0a000006   2731 	beq	.L3265

                    2732 .L3270:

                    2733 ;982:     StringView_init(result, (char*)iedModel + daPos, len);


                    2734 


                                                                      Page 46
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
00001018 e59f1214*  2735 	ldr	r1,.L2393

0000101c e5910000   2736 	ldr	r0,[r1]

00001020 e59d2004   2737 	ldr	r2,[sp,4]

00001024 e0831000   2738 	add	r1,r3,r0

00001028 e1a00006   2739 	mov	r0,r6

0000102c eb000000*  2740 	bl	StringView_init

                    2741 ;983:     return TRUE;


                    2742 

00001030 e3a00001   2743 	mov	r0,1

                    2744 .L3265:

00001034 e28dd00c   2745 	add	sp,sp,12

00001038 e8bd8070   2746 	ldmfd	[sp]!,{r4-r6,pc}

                    2747 	.endf	getConstDAString

                    2748 	.align	4

                    2749 ;tag	[sp,3]	local

                    2750 ;len	[sp,4]	local

                    2751 ;attrType	[sp,8]	local

                    2752 ;daPos	r3	local

                    2753 

                    2754 ;parentPos	r5	param

                    2755 ;attrName	r4	param

                    2756 ;result	r6	param

                    2757 

                    2758 	.section ".bss","awb"

                    2759 .L3324:

                    2760 	.data

                    2761 	.text

                    2762 

                    2763 ;984: }


                    2764 

                    2765 ;985: 


                    2766 ;986: //Возвращает значение константного DA в виде uint32_t


                    2767 ;987: bool getConstDAULong(size_t parentPos, const char* attrName,


                    2768 	.align	4

                    2769 	.align	4

                    2770 getConstDAULong::

0000103c e92d4010   2771 	stmfd	[sp]!,{r4,lr}

                    2772 ;988:     uint32_t* result)


                    2773 ;989: {


                    2774 

                    2775 ;990:     uint8_t tag;


                    2776 ;991:     int len;


                    2777 ;992:     enum InnerAttributeType attrType;


                    2778 ;993:     int daPos = getDAValuePos(parentPos, (uint8_t*) attrName, 7, &attrType);


                    2779 

00001040 e24dd00c   2780 	sub	sp,sp,12

00001044 e28d3008   2781 	add	r3,sp,8

00001048 e1a04002   2782 	mov	r4,r2

0000104c e3a02007   2783 	mov	r2,7

00001050 ebffffbb*  2784 	bl	getDAValuePos

                    2785 ;994:     RET_IF_NOT(daPos, "Error reading %s", attrName);


                    2786 

00001054 e3500000   2787 	cmp	r0,0

00001058 0a000004   2788 	beq	.L3343

                    2789 ;995:     daPos = readTL(daPos, &tag, &len, NULL);


                    2790 

0000105c e28d2004   2791 	add	r2,sp,4

00001060 e28d1003   2792 	add	r1,sp,3

00001064 e3a03000   2793 	mov	r3,0

00001068 ebfffbe9*  2794 	bl	readTL

0000106c e1b02000   2795 	movs	r2,r0


                                                                      Page 47
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                    2796 ;996:     RET_IF_NOT(daPos, "Error reading %s", attrName);


                    2797 

                    2798 .L3343:

00001070 03a00000   2799 	moveq	r0,0

00001074 0a000005   2800 	beq	.L3337

                    2801 .L3342:

                    2802 ;997:     *result = BerDecoder_decodeUint32(iedModel, len, daPos);


                    2803 

00001078 e59f31b4*  2804 	ldr	r3,.L2393

0000107c e59d1004   2805 	ldr	r1,[sp,4]

00001080 e5930000   2806 	ldr	r0,[r3]

00001084 eb000000*  2807 	bl	BerDecoder_decodeUint32

00001088 e5840000   2808 	str	r0,[r4]

                    2809 ;998:     return TRUE;


                    2810 

0000108c e3a00001   2811 	mov	r0,1

                    2812 .L3337:

00001090 e28dd00c   2813 	add	sp,sp,12

00001094 e8bd8010   2814 	ldmfd	[sp]!,{r4,pc}

                    2815 	.endf	getConstDAULong

                    2816 	.align	4

                    2817 ;tag	[sp,3]	local

                    2818 ;len	[sp,4]	local

                    2819 ;attrType	[sp,8]	local

                    2820 ;daPos	r2	local

                    2821 

                    2822 ;parentPos	none	param

                    2823 ;attrName	none	param

                    2824 ;result	r4	param

                    2825 

                    2826 	.section ".bss","awb"

                    2827 .L3404:

                    2828 	.data

                    2829 	.text

                    2830 

                    2831 ;999: }


                    2832 

                    2833 ;1000: 


                    2834 ;1001: bool IEDModel_skipServiceInfo(BufferView* subObjects)


                    2835 	.align	4

                    2836 	.align	4

                    2837 IEDModel_skipServiceInfo::

00001098 e92d4030   2838 	stmfd	[sp]!,{r4-r5,lr}

0000109c e24dd004   2839 	sub	sp,sp,4

000010a0 e1a04000   2840 	mov	r4,r0

                    2841 ;1002: {


                    2842 

                    2843 ;1003:     while(!BufferView_endOfBuf(subObjects))


                    2844 

000010a4 e9940003   2845 	ldmed	[r4],{r0-r1}

000010a8 e1500001   2846 	cmp	r0,r1

000010ac 0a000010   2847 	beq	.L3427

                    2848 .L3421:

000010b0 e28d1003   2849 	add	r1,sp,3

000010b4 e1a00004   2850 	mov	r0,r4

000010b8 eb000000*  2851 	bl	BufferView_peekTag

                    2852 ;1004:     {


                    2853 

                    2854 ;1005:         uint8_t objInfoTag;


                    2855 ;1006:         if(!BufferView_peekTag(subObjects, &objInfoTag))


                    2856 


                                                                      Page 48
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
000010bc e3500000   2857 	cmp	r0,0

                    2858 ;1007:         {


                    2859 

                    2860 ;1008:             return false;


                    2861 

000010c0 0a00000c   2862 	beq	.L3417

                    2863 ;1009:         }


                    2864 ;1010: 


                    2865 ;1011:         if(!IEDModel_isServiceInfo(objInfoTag))


                    2866 

000010c4 e5dd5003   2867 	ldrb	r5,[sp,3]

000010c8 e1a00005   2868 	mov	r0,r5

000010cc ebfffc40*  2869 	bl	IEDModel_isServiceInfo

000010d0 e3500000   2870 	cmp	r0,0

000010d4 0a000006   2871 	beq	.L3427

                    2872 ;1012:         {


                    2873 

                    2874 ;1013:             break;


                    2875 

                    2876 ;1014:         }


                    2877 ;1015:         BufferView_skipObject(subObjects, objInfoTag, false);


                    2878 

000010d8 e1a01005   2879 	mov	r1,r5

000010dc e1a00004   2880 	mov	r0,r4

000010e0 e3a02000   2881 	mov	r2,0

000010e4 eb000000*  2882 	bl	BufferView_skipObject

000010e8 e9940003   2883 	ldmed	[r4],{r0-r1}

000010ec e1500001   2884 	cmp	r0,r1

000010f0 1affffee   2885 	bne	.L3421

                    2886 .L3427:

                    2887 ;1016:     }


                    2888 ;1017:     return true;


                    2889 

000010f4 e3a00001   2890 	mov	r0,1

                    2891 .L3417:

000010f8 e28dd004   2892 	add	sp,sp,4

000010fc e8bd8030   2893 	ldmfd	[sp]!,{r4-r5,pc}

                    2894 	.endf	IEDModel_skipServiceInfo

                    2895 	.align	4

                    2896 ;objInfoTag	[sp,3]	local

                    2897 

                    2898 ;subObjects	r4	param

                    2899 

                    2900 	.section ".bss","awb"

                    2901 .L3518:

                    2902 	.data

                    2903 	.text

                    2904 

                    2905 ;1018: }


                    2906 

                    2907 ;1019: 


                    2908 ;1020: 


                    2909 ;1021: bool IEDModel_getChildren(const BufferView* berObject,  BufferView* children)


                    2910 	.align	4

                    2911 	.align	4

                    2912 IEDModel_getChildren::

00001100 e92d4010   2913 	stmfd	[sp]!,{r4,lr}

00001104 e24dd004   2914 	sub	sp,sp,4

00001108 e1a04001   2915 	mov	r4,r1

                    2916 ;1022: {


                    2917 


                                                                      Page 49
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                    2918 ;1023:     size_t len;


                    2919 ;1024:     *children = *berObject;


                    2920 

0000110c e8900007   2921 	ldmfd	[r0],{r0-r2}

00001110 e8840007   2922 	stmea	[r4],{r0-r2}

                    2923 ;1025:     if(!BufferView_decodeTL(children, NULL, &len, NULL))


                    2924 

00001114 e1a0200d   2925 	mov	r2,sp

00001118 e1a00004   2926 	mov	r0,r4

0000111c e3a03000   2927 	mov	r3,0

00001120 e1a01003   2928 	mov	r1,r3

00001124 eb000000*  2929 	bl	BufferView_decodeTL

00001128 e3500000   2930 	cmp	r0,0

0000112c 0a000009   2931 	beq	.L3548

                    2932 ;1026:     {


                    2933 

                    2934 ;1027:         return false;


                    2935 

                    2936 ;1028:     }


                    2937 ;1029: 


                    2938 ;1030:     BufferView_init(children, children->p + children->pos, len, 0);


                    2939 

00001130 e59d2000   2940 	ldr	r2,[sp]

00001134 e894000a   2941 	ldmfd	[r4],{r1,r3}

00001138 e1a00004   2942 	mov	r0,r4

0000113c e0831001   2943 	add	r1,r3,r1

00001140 e3a03000   2944 	mov	r3,0

00001144 eb000000*  2945 	bl	BufferView_init

                    2946 ;1031: 


                    2947 ;1032:     if(!IEDModel_skipServiceInfo(children))


                    2948 

00001148 e1a00004   2949 	mov	r0,r4

0000114c ebffffd1*  2950 	bl	IEDModel_skipServiceInfo

00001150 e3500000   2951 	cmp	r0,0

                    2952 ;1035:     }


                    2953 ;1036:     return true;


                    2954 

00001154 13a00001   2955 	movne	r0,1

                    2956 .L3548:

                    2957 ;1033:     {


                    2958 

                    2959 ;1034:         return false;


                    2960 

00001158 03a00000   2961 	moveq	r0,0

                    2962 .L3542:

0000115c e28dd004   2963 	add	sp,sp,4

00001160 e8bd8010   2964 	ldmfd	[sp]!,{r4,pc}

                    2965 	.endf	IEDModel_getChildren

                    2966 	.align	4

                    2967 ;len	[sp]	local

                    2968 

                    2969 ;berObject	r0	param

                    2970 ;children	r4	param

                    2971 

                    2972 	.section ".bss","awb"

                    2973 .L3596:

                    2974 	.data

                    2975 	.text

                    2976 

                    2977 ;1037: }


                    2978 


                                                                      Page 50
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                    2979 ;1038: 


                    2980 ;1039: bool IEDModel_getTermItemDescrStruct(BufferView* descrObject,


                    2981 	.align	4

                    2982 	.align	4

                    2983 IEDModel_getTermItemDescrStruct::

00001164 e92d4070   2984 	stmfd	[sp]!,{r4-r6,lr}

00001168 e24dd008   2985 	sub	sp,sp,8

0000116c e1a04000   2986 	mov	r4,r0

00001170 e1a06001   2987 	mov	r6,r1

                    2988 ;1040:                                     void** pDescrStruct)


                    2989 ;1041: {


                    2990 

                    2991 ;1042:     //Какое смещение использовано для выравнивания структуры описания


                    2992 ;1043:     uint8_t* pDescrStructAlignOffset;


                    2993 ;1044:     uint8_t descrTag;


                    2994 ;1045:     size_t descrLen;


                    2995 ;1046:     //Получаем указатель на структуру описания


                    2996 ;1047:     if(BufferView_endOfBuf(descrObject))


                    2997 

00001174 e9940003   2998 	ldmed	[r4],{r0-r1}

00001178 e1500001   2999 	cmp	r0,r1

0000117c 0a00001a   3000 	beq	.L3632

                    3001 ;1048:     {


                    3002 

                    3003 ;1049:         return false;


                    3004 

                    3005 ;1050:     }


                    3006 ;1051:     if(!BufferView_decodeTL(descrObject, &descrTag, &descrLen, NULL))


                    3007 

00001180 e28d2004   3008 	add	r2,sp,4

00001184 e28d1003   3009 	add	r1,sp,3

00001188 e1a00004   3010 	mov	r0,r4

0000118c e3a03000   3011 	mov	r3,0

00001190 eb000000*  3012 	bl	BufferView_decodeTL

00001194 e3500000   3013 	cmp	r0,0

00001198 0a000013   3014 	beq	.L3632

                    3015 ;1052:     {


                    3016 

                    3017 ;1053:         return false;


                    3018 

                    3019 ;1054:     }


                    3020 ;1055:     if (descrTag != ASN_OCTET_STRING)


                    3021 

0000119c e5dd0003   3022 	ldrb	r0,[sp,3]

000011a0 e3500004   3023 	cmp	r0,4

000011a4 1a000010   3024 	bne	.L3632

                    3025 ;1056:     {


                    3026 

                    3027 ;1057:         ERROR_REPORT("Invalid tag");


                    3028 ;1058:         return false;


                    3029 

                    3030 ;1059:     };


                    3031 ;1060:     //Получаем структуру описания с учётом выравнивания


                    3032 ;1061:     pDescrStructAlignOffset = descrObject->p + descrObject->pos;


                    3033 

000011a8 e8940021   3034 	ldmfd	[r4],{r0,r5}

                    3035 ;1062:     if(*pDescrStructAlignOffset >= 4)


                    3036 

000011ac e7f51000   3037 	ldrb	r1,[r5,r0]!

000011b0 e3510004   3038 	cmp	r1,4

000011b4 2a00000c   3039 	bhs	.L3632


                                                                      Page 51
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                    3040 ;1063:     {


                    3041 

                    3042 ;1064:         ERROR_REPORT("Invalid alignment");


                    3043 ;1065:         return false;


                    3044 

                    3045 ;1066:     }


                    3046 ;1067: 


                    3047 ;1068:     if(!BufferView_advance(descrObject, descrLen))


                    3048 

000011b8 e59d1004   3049 	ldr	r1,[sp,4]

000011bc e1a00004   3050 	mov	r0,r4

000011c0 eb000000*  3051 	bl	BufferView_advance

000011c4 e3500000   3052 	cmp	r0,0

000011c8 0a000007   3053 	beq	.L3632

                    3054 ;1069:     {


                    3055 

                    3056 ;1070:         ERROR_REPORT("Invalid object length");


                    3057 ;1071:         return false;


                    3058 

                    3059 ;1072:     }


                    3060 ;1073: 


                    3061 ;1074:     *pDescrStruct = pDescrStructAlignOffset + *pDescrStructAlignOffset + 1;


                    3062 

000011cc e5d50000   3063 	ldrb	r0,[r5]

000011d0 e0800005   3064 	add	r0,r0,r5

000011d4 e2800001   3065 	add	r0,r0,1

000011d8 e5860000   3066 	str	r0,[r6]

                    3067 ;1075:     if(( ((uint32_t)(*pDescrStruct)) & 3) != 0)


                    3068 

                    3069 

                    3070 

                    3071 

000011dc e3100003   3072 	tst	r0,3

000011e0 03a00001   3073 	moveq	r0,1

000011e4 13a00000   3074 	movne	r0,0

000011e8 ea000000   3075 	b	.L3613

                    3076 .L3632:

                    3077 ;1076:     {


                    3078 

                    3079 ;1077:         ERROR_REPORT("Invalid alignment");


                    3080 ;1078:         return false;


                    3081 

000011ec e3a00000   3082 	mov	r0,0

                    3083 .L3613:

000011f0 e28dd008   3084 	add	sp,sp,8

000011f4 e8bd8070   3085 	ldmfd	[sp]!,{r4-r6,pc}

                    3086 	.endf	IEDModel_getTermItemDescrStruct

                    3087 	.align	4

                    3088 ;pDescrStructAlignOffset	r5	local

                    3089 ;descrTag	[sp,3]	local

                    3090 ;descrLen	[sp,4]	local

                    3091 

                    3092 ;descrObject	r4	param

                    3093 ;pDescrStruct	r6	param

                    3094 

                    3095 	.section ".bss","awb"

                    3096 .L3728:

                    3097 	.data

                    3098 	.text

                    3099 

                    3100 ;1081: }



                                                                      Page 52
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                    3101 

                    3102 ;1082: 


                    3103 ;1083: bool IEDModel_getBufferView(size_t pos, BufferView* bv)


                    3104 	.align	4

                    3105 	.align	4

                    3106 IEDModel_getBufferView::

000011f8 e92d4000   3107 	stmfd	[sp]!,{lr}

000011fc e1a0c001   3108 	mov	r12,r1

                    3109 ;1084: {


                    3110 

                    3111 ;1085:     if(pos > (size_t)iedModelSize)


                    3112 

00001200 e59f1028*  3113 	ldr	r1,.L2392

00001204 e5912000   3114 	ldr	r2,[r1]

00001208 e1500002   3115 	cmp	r0,r2

                    3116 ;1086:     {


                    3117 

                    3118 ;1087:         return false;


                    3119 

0000120c 83a00000   3120 	movhi	r0,0

00001210 8a000005   3121 	bhi	.L3747

                    3122 ;1088:     }


                    3123 ;1089:     BufferView_init(bv, iedModel, iedModelSize, pos);


                    3124 

00001214 e1a03000   3125 	mov	r3,r0

00001218 e59f0014*  3126 	ldr	r0,.L2393

0000121c e5901000   3127 	ldr	r1,[r0]

00001220 e1a0000c   3128 	mov	r0,r12

00001224 eb000000*  3129 	bl	BufferView_init

                    3130 ;1090:     return true;


                    3131 

00001228 e3a00001   3132 	mov	r0,1

                    3133 .L3747:

0000122c e8bd8000   3134 	ldmfd	[sp]!,{pc}

                    3135 	.endf	IEDModel_getBufferView

                    3136 	.align	4

                    3137 

                    3138 ;pos	r0	param

                    3139 ;bv	r12	param

                    3140 

                    3141 	.section ".bss","awb"

                    3142 .L3782:

                    3143 	.data

                    3144 	.text

                    3145 

                    3146 ;1091: }


                    3147 	.align	4

                    3148 .L2392:

00001230 00000000*  3149 	.data.w	iedModelSize

                    3150 	.type	.L2392,$object

                    3151 	.size	.L2392,4

                    3152 

                    3153 .L2393:

00001234 00000000*  3154 	.data.w	iedModel

                    3155 	.type	.L2393,$object

                    3156 	.size	.L2393,4

                    3157 

                    3158 	.align	4

                    3159 

                    3160 	.data

                    3161 .L3812:


                                                                      Page 53
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_3lk1.s
                    3162 	.globl	defaultIed

00000000 031a3aea   3163 defaultIed:	.data.b	234,58,26,3

00000004 e2444549   3164 	.data.b	73,69,68,226

00000008 4c071a33   3165 	.data.b	51,26,7,76

0000000c 69766544   3166 	.data.b	68,101,118,105

00000010 28e46563   3167 	.data.b	99,101,228,40

00000014 4d4d051a   3168 	.data.b	26,5,77,77

00000018 e6305558   3169 	.data.b	88,85,48,230

0000001c 54041a1f   3170 	.data.b	31,26,4,84

00000020 e857746f   3171 	.data.b	111,116,87,232

00000024 6d031a17   3172 	.data.b	23,26,3,109

00000028 10e96761   3173 	.data.b	97,103,233,16

0000002c 0266011a   3174 	.data.b	26,1,102,2

00000030 08040201   3175 	.data.b	1,2,4,8

00000034 c0        3176 	.data.b	192

00000035 00        3177 	.space	1

00000036 00        3178 	.space	1

00000037 00        3179 	.space	1

00000038 35311acf   3180 	.data.b	207,26,49,53

                    3181 	.type	defaultIed,$object

                    3182 	.size	defaultIed,60

                    3183 .L3813:

                    3184 	.globl	iedModel

0000003c 00000000*  3185 iedModel:	.data.w	.L3812

                    3186 	.type	iedModel,$object

                    3187 	.size	iedModel,4

                    3188 .L3814:

                    3189 	.globl	iedModelSize

00000040 0000003c   3190 iedModelSize:	.data.b	60,0,0,0

                    3191 	.type	iedModelSize,$object

                    3192 	.size	iedModelSize,4

                    3193 	.ghsnote version,6

                    3194 	.ghsnote tools,3

                    3195 	.ghsnote options,0

                    3196 	.text

                    3197 	.align	4

                    3198 	.data

                    3199 	.align	4

                    3200 	.text

