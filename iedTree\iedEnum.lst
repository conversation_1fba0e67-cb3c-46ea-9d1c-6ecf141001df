                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gc1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=iedEnum.c -o iedTree\gh_gc1.o -list=iedTree/iedEnum.lst C:\Users\<USER>\AppData\Local\Temp\gh_gc1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_gc1.s
Source File: iedEnum.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		iedTree/iedEnum.c -o iedTree/iedEnum.o

                      12 ;Source File:   iedTree/iedEnum.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:51 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "iedInt.h"


                      23 ;2: 


                      24 ;3: #include "iedTree.h"


                      25 ;4: #include "iedFinalDA.h"


                      26 ;5: 


                      27 ;6: #include "../DataSlice.h"


                      28 ;7: #include "../AsnEncoding.h"


                      29 ;8: #include "../mms_data.h"


                      30 ;9: 


                      31 ;10: #include "debug.h"


                      32 ;11: 


                      33 ;12: #include "IEDCompile/AccessInfo.h"


                      34 ;13: 


                      35 ;14: 


                      36 ;15: 


                      37 ;16: static void updateFromDataSlice(IEDEntity entity)


                      38 	.text

                      39 	.align	4

                      40 updateFromDataSlice:

00000000 e92d4010     41 	stmfd	[sp]!,{r4,lr}

00000004 e1a04000     42 	mov	r4,r0

                      43 ;17: {


                      44 

                      45 ;18:     int offset  = entity->dataSliceOffset;


                      46 

00000008 e594002c     47 	ldr	r0,[r4,44]

                      48 ;19:     int32_t value;


                      49 ;20: 


                      50 ;21:     if(offset == -1)



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gc1.s
                      51 

0000000c e3700001     52 	cmn	r0,1

00000010 0a000011     53 	beq	.L2

                      54 ;22:     {


                      55 

                      56 ;23:         return;


                      57 

                      58 ;24:     }


                      59 ;25: 


                      60 ;26:     value = DataSlice_getInt32FastCurrDS(offset);


                      61 

00000014 e1a00800     62 	mov	r0,r0 lsl 16

00000018 e1a00820     63 	mov	r0,r0 lsr 16

0000001c eb000000*    64 	bl	DataSlice_getInt32FastCurrDS

                      65 ;27: 


                      66 ;28: 


                      67 ;29:     if(entity->intValue == value)


                      68 

00000020 e5941030     69 	ldr	r1,[r4,48]

00000024 e1510000     70 	cmp	r1,r0

                      71 ;30:     {


                      72 

                      73 ;31:         entity->changed = TRGOP_NONE;


                      74 

00000028 03a00000     75 	moveq	r0,0

0000002c 05840028     76 	streq	r0,[r4,40]

00000030 0a000009     77 	beq	.L2

                      78 ;32:     }


                      79 ;33:     else


                      80 ;34:     {


                      81 

                      82 ;35:         entity->changed = entity->trgOps;


                      83 

00000034 e5941024     84 	ldr	r1,[r4,36]

00000038 e5840030     85 	str	r0,[r4,48]

                      86 ;37:         entity->cached = false;


                      87 

0000003c e5841028     88 	str	r1,[r4,40]

                      89 ;36:         entity->intValue = value;


                      90 

00000040 e3a00000     91 	mov	r0,0

00000044 e5c40034     92 	strb	r0,[r4,52]

                      93 ;38:         IEDEntity_setTimeStamp(entity, dataSliceGetTimeStamp());


                      94 

00000048 eb000000*    95 	bl	dataSliceGetTimeStamp

0000004c e1a02001     96 	mov	r2,r1

00000050 e1a01000     97 	mov	r1,r0

00000054 e1a00004     98 	mov	r0,r4

00000058 eb000000*    99 	bl	IEDEntity_setTimeStamp

                     100 .L2:

0000005c e8bd4010    101 	ldmfd	[sp]!,{r4,lr}

00000060 e12fff1e*   102 	ret	

                     103 	.endf	updateFromDataSlice

                     104 	.align	4

                     105 ;offset	r0	local

                     106 ;value	r0	local

                     107 

                     108 ;entity	r4	param

                     109 

                     110 	.section ".bss","awb"

                     111 .L56:


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gc1.s
                     112 	.data

                     113 	.text

                     114 

                     115 ;39:     }


                     116 ;40: }


                     117 

                     118 ;41: 


                     119 ;42: 


                     120 ;43: // Вычисляет и записывает в кэш длину BER и значение


                     121 ;44: static void calcAndCacheLenAndVal(IEDEntity entity)


                     122 	.align	4

                     123 	.align	4

                     124 calcAndCacheLenAndVal:

00000064 e92d4010    125 	stmfd	[sp]!,{r4,lr}

00000068 e1a04000    126 	mov	r4,r0

                     127 ;45: {


                     128 

                     129 ;46:     TerminalItem* extInfo = entity->extInfo;


                     130 

0000006c e5940058    131 	ldr	r0,[r4,88]

                     132 ;47:     IntBoolAccessInfo* accessInfo = extInfo->accessInfo;


                     133 

00000070 e5900000    134 	ldr	r0,[r0]

                     135 ;48:     int32_t enumValue;


                     136 ;49: 


                     137 ;50:     enumValue = getEnumValue(entity->intValue, accessInfo->enumTable,


                     138 

00000074 e280100c    139 	add	r1,r0,12

00000078 e5902008    140 	ldr	r2,[r0,8]

0000007c e5940030    141 	ldr	r0,[r4,48]

00000080 eb000000*   142 	bl	getEnumValue

                     143 ;51:                          accessInfo->enumTableSize);


                     144 ;52: 


                     145 ;53:     entity->cache.enumValue = enumValue;


                     146 

00000084 e584003c    147 	str	r0,[r4,60]

                     148 ;54: 


                     149 ;55: 


                     150 ;56:     entity->cachedBERLen = BerEncoder_Int32DetermineEncodedSize(enumValue) + 2;


                     151 

00000088 eb000000*   152 	bl	BerEncoder_Int32DetermineEncodedSize

0000008c e2800002    153 	add	r0,r0,2

00000090 e5840038    154 	str	r0,[r4,56]

                     155 ;57:     entity->cached = true;


                     156 

00000094 e3a00001    157 	mov	r0,1

00000098 e5c40034    158 	strb	r0,[r4,52]

0000009c e8bd4010    159 	ldmfd	[sp]!,{r4,lr}

000000a0 e12fff1e*   160 	ret	

                     161 	.endf	calcAndCacheLenAndVal

                     162 	.align	4

                     163 ;extInfo	r0	local

                     164 ;accessInfo	r0	local

                     165 

                     166 ;entity	r4	param

                     167 

                     168 	.section ".bss","awb"

                     169 .L97:

                     170 	.data

                     171 	.text

                     172 


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gc1.s
                     173 ;58: 


                     174 ;59: }


                     175 

                     176 ;60: 


                     177 ;61: static bool calcReadLen(IEDEntity entity, size_t* pLen )


                     178 	.align	4

                     179 	.align	4

                     180 calcReadLen:

000000a4 e92d4030    181 	stmfd	[sp]!,{r4-r5,lr}

000000a8 e1a04000    182 	mov	r4,r0

                     183 ;62: {


                     184 

                     185 ;63:     if(entity->cached)


                     186 

000000ac e5d40034    187 	ldrb	r0,[r4,52]

000000b0 e1a05001    188 	mov	r5,r1

000000b4 e3500000    189 	cmp	r0,0

                     190 ;64:     {


                     191 

                     192 ;65:         *pLen = entity->cachedBERLen;


                     193 

                     194 ;66: 


                     195 ;67:         return true;


                     196 

                     197 ;68:     }


                     198 ;69: 


                     199 ;70:     calcAndCacheLenAndVal(entity);


                     200 

000000b8 01a00004    201 	moveq	r0,r4

000000bc 0bffffe8*   202 	bleq	calcAndCacheLenAndVal

                     203 ;71: 


                     204 ;72:     *pLen = entity->cachedBERLen;


                     205 

                     206 ;73:     return true;


                     207 

000000c0 e5940038    208 	ldr	r0,[r4,56]

000000c4 e5850000    209 	str	r0,[r5]

000000c8 e3a00001    210 	mov	r0,1

000000cc e8bd4030    211 	ldmfd	[sp]!,{r4-r5,lr}

000000d0 e12fff1e*   212 	ret	

                     213 	.endf	calcReadLen

                     214 	.align	4

                     215 

                     216 ;entity	r4	param

                     217 ;pLen	r5	param

                     218 

                     219 	.section ".bss","awb"

                     220 .L147:

                     221 	.data

                     222 	.text

                     223 

                     224 ;74: }


                     225 

                     226 ;75: 


                     227 ;76: static bool encodeRead(IEDEntity entity, BufferView* outBuf)


                     228 	.align	4

                     229 	.align	4

                     230 encodeRead:

000000d4 e92d4030    231 	stmfd	[sp]!,{r4-r5,lr}

000000d8 e24dd004    232 	sub	sp,sp,4

000000dc e1a04000    233 	mov	r4,r0


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gc1.s
                     234 ;77: {


                     235 

                     236 ;78: 


                     237 ;79:     uint8_t* encodeBuf;


                     238 ;80:     int fullEncodedLen;


                     239 ;81: 


                     240 ;82: 


                     241 ;83:     if(!entity->cached)


                     242 

000000e0 e5d40034    243 	ldrb	r0,[r4,52]

000000e4 e1a05001    244 	mov	r5,r1

000000e8 e3500000    245 	cmp	r0,0

                     246 ;84:     {


                     247 

                     248 ;85:         ERROR_REPORT("Length and value are not cached");


                     249 ;86:         calcAndCacheLenAndVal(entity);


                     250 

000000ec 01a00004    251 	moveq	r0,r4

000000f0 0bffffdb*   252 	bleq	calcAndCacheLenAndVal

000000f4 e1a0200d    253 	mov	r2,sp

000000f8 e5941038    254 	ldr	r1,[r4,56]

000000fc e1a00005    255 	mov	r0,r5

00000100 eb000000*   256 	bl	BufferView_alloc

                     257 ;87:     }


                     258 ;88: 


                     259 ;89:     if(!BufferView_alloc(outBuf,entity->cachedBERLen, &encodeBuf))


                     260 

00000104 e3500000    261 	cmp	r0,0

                     262 ;90:     {


                     263 

                     264 ;91:         ERROR_REPORT("Unable to allocate buffer");


                     265 ;92:         return false;


                     266 

00000108 0a000008    267 	beq	.L160

                     268 ;93:     }


                     269 ;94: 


                     270 ;95:     //Функция возвращает новое смещение в буфере, но поскольку начальное


                     271 ;96:     //смещение 0, можно считать это размером.


                     272 ;97:     fullEncodedLen = BerEncoder_EncodeInt32WithTL(


                     273 

0000010c e59d2000    274 	ldr	r2,[sp]

00000110 e594103c    275 	ldr	r1,[r4,60]

00000114 e3a03000    276 	mov	r3,0

00000118 e3a00085    277 	mov	r0,133

0000011c eb000000*   278 	bl	BerEncoder_EncodeInt32WithTL

                     279 ;98:                 IEC61850_BER_INTEGER, entity->cache.enumValue, encodeBuf, 0);


                     280 ;99: 


                     281 ;100:     outBuf->pos += fullEncodedLen;


                     282 

00000120 e5951004    283 	ldr	r1,[r5,4]

00000124 e0811000    284 	add	r1,r1,r0

00000128 e5851004    285 	str	r1,[r5,4]

                     286 ;101:     return true;


                     287 

0000012c e3a00001    288 	mov	r0,1

                     289 .L160:

00000130 e28dd004    290 	add	sp,sp,4

00000134 e8bd4030    291 	ldmfd	[sp]!,{r4-r5,lr}

00000138 e12fff1e*   292 	ret	

                     293 	.endf	encodeRead

                     294 	.align	4


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gc1.s
                     295 ;encodeBuf	[sp]	local

                     296 

                     297 ;entity	r4	param

                     298 ;outBuf	r5	param

                     299 

                     300 	.section ".bss","awb"

                     301 .L243:

                     302 	.data

                     303 	.text

                     304 

                     305 ;102: }


                     306 

                     307 ;103: 


                     308 ;104: 


                     309 ;105: void IEDEnum_init(IEDEntity entity)


                     310 	.align	4

                     311 	.align	4

                     312 IEDEnum_init::

0000013c e92d4070    313 	stmfd	[sp]!,{r4-r6,lr}

00000140 e280405c    314 	add	r4,r0,92

00000144 e5140004    315 	ldr	r0,[r4,-4]

                     316 ;108:     IntBoolAccessInfo* accessInfo = extInfo->accessInfo;


                     317 

00000148 e5900000    318 	ldr	r0,[r0]

                     319 ;109: 


                     320 ;110:     //Если будет ошибка, то запишется -1;


                     321 ;111:     entity->dataSliceOffset = DataSlice_getIntOffset(accessInfo->valueOffset);


                     322 

0000014c e59f5028*   323 	ldr	r5,.L293

00000150 e5900004    324 	ldr	r0,[r0,4]

00000154 e59f6024*   325 	ldr	r6,.L294

                     326 ;106: {


                     327 

                     328 ;107:     TerminalItem* extInfo = entity->extInfo;


                     329 

00000158 eb000000*   330 	bl	DataSlice_getIntOffset

0000015c e5040030    331 	str	r0,[r4,-48]

                     332 ;112: 


                     333 ;113: 


                     334 ;114:     entity->calcReadLen = calcReadLen;


                     335 

                     336 ;115:     entity->encodeRead = encodeRead;


                     337 

00000160 e1a00006    338 	mov	r0,r6

00000164 e8840021    339 	stmea	[r4],{r0,r5}

                     340 ;116:     entity->updateFromDataSlice = updateFromDataSlice;


                     341 

00000168 e59f0014*   342 	ldr	r0,.L295

0000016c e584000c    343 	str	r0,[r4,12]

                     344 ;117: 


                     345 ;118:     IEDTree_addToCmpList(entity);


                     346 

00000170 e244005c    347 	sub	r0,r4,92

00000174 e8bd4070    348 	ldmfd	[sp]!,{r4-r6,lr}

00000178 ea000000*   349 	b	IEDTree_addToCmpList

                     350 	.endf	IEDEnum_init

                     351 	.align	4

                     352 ;extInfo	r0	local

                     353 ;accessInfo	r0	local

                     354 

                     355 ;entity	r4	param


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_gc1.s
                     356 

                     357 	.section ".bss","awb"

                     358 .L286:

                     359 	.data

                     360 	.text

                     361 

                     362 ;119: }


                     363 	.align	4

                     364 .L293:

0000017c 00000000*   365 	.data.w	calcReadLen

                     366 	.type	.L293,$object

                     367 	.size	.L293,4

                     368 

                     369 .L294:

00000180 00000000*   370 	.data.w	encodeRead

                     371 	.type	.L294,$object

                     372 	.size	.L294,4

                     373 

                     374 .L295:

00000184 00000000*   375 	.data.w	updateFromDataSlice

                     376 	.type	.L295,$object

                     377 	.size	.L295,4

                     378 

                     379 	.align	4

                     380 

                     381 	.data

                     382 	.ghsnote version,6

                     383 	.ghsnote tools,3

                     384 	.ghsnote options,0

                     385 	.text

                     386 	.align	4

