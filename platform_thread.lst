                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_33k1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=platform_thread.c -o gh_33k1.o -list=platform_thread.lst C:\Users\<USER>\AppData\Local\Temp\gh_33k1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_33k1.s
Source File: platform_thread.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		platform_thread.c -o platform_thread.o

                      12 ;Source File:   platform_thread.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:57 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "platform_thread.h"


                      23 ;2: 


                      24 ;3: #include <process.h>


                      25 ;4: 


                      26 ;5: void createThread(void *pProcAddr, void* pParam)


                      27 	.text

                      28 	.align	4

                      29 createThread::

                      30 ;6: {


                      31 

                      32 ;7:     _CreateThread( pProcAddr, pParam, 0x1000,


                      33 

00000000 e3a03f40     34 	mov	r3,256

00000004 e2833001     35 	add	r3,r3,1

00000008 e3a02d40     36 	mov	r2,1<<12

0000000c ea000000*    37 	b	_CreateThread

                      38 	.endf	createThread

                      39 	.align	4

                      40 

                      41 ;pProcAddr	none	param

                      42 ;pParam	none	param

                      43 

                      44 	.section ".bss","awb"

                      45 .L30:

                      46 	.data

                      47 	.text

                      48 

                      49 ;8:                                 THREAD_PRIORITY_NORMAL | THREAD_SELF_TERMINATED);


                      50 ;9: }



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_33k1.s
                      51 	.align	4

                      52 

                      53 	.data

                      54 	.ghsnote version,6

                      55 	.ghsnote tools,3

                      56 	.ghsnote options,0

                      57 	.text

                      58 	.align	4

