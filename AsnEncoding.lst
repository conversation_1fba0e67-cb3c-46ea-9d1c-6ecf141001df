                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=AsnEncoding.c -o gh_9ik1.o -list=AsnEncoding.lst C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
Source File: AsnEncoding.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile AsnEncoding.c

                      11 ;		-o AsnEncoding.o

                      12 ;Source File:   AsnEncoding.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:01 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "AsnEncoding.h"


                      23 ;2: 


                      24 ;3: #include "bufViewBER.h"


                      25 ;4: #include <string.h>


                      26 ;5: #include <stdlib.h>


                      27 ;6: 


                      28 ;7: 


                      29 ;8: int BerEncoder_encodeLength( unsigned int iLength, unsigned char* pBuffer, int iBufPos )


                      30 	.text

                      31 	.align	4

                      32 BerEncoder_encodeLength::

                      33 ;9: {


                      34 

                      35 ;10:     if ( iLength < 128 )


                      36 

00000000 e3500080     37 	cmp	r0,128

                      38 ;11:     {


                      39 

                      40 ;12:         pBuffer[iBufPos++] = (unsigned char)iLength;


                      41 

                      42 ;24: 


                      43 ;25:     }


                      44 ;26: 


                      45 ;27:     return iBufPos;


                      46 

00000004 3a00000a     47 	blo	.L21

                      48 ;13:     }


                      49 ;14:     else if ( iLength < 256 )


                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
00000008 e3500f40     51 	cmp	r0,256

                      52 ;15:     {


                      53 

                      54 ;16:         pBuffer[iBufPos++] = 0x81;


                      55 

0000000c 33a03081     56 	movlo	r3,129

00000010 37c13002     57 	strlob	r3,[r1,r2]

00000014 32822001     58 	addlo	r2,r2,1

                      59 ;17:         pBuffer[iBufPos++] = (unsigned char)iLength;


                      60 

                      61 ;24: 


                      62 ;25:     }


                      63 ;26: 


                      64 ;27:     return iBufPos;


                      65 

00000018 3a000005     66 	blo	.L21

                      67 ;18:     }


                      68 ;19:     else


                      69 ;20:     {


                      70 

                      71 ;21:         pBuffer[iBufPos++] = 0x82;


                      72 

0000001c e3a03082     73 	mov	r3,130

00000020 e7c13002     74 	strb	r3,[r1,r2]

00000024 e2822001     75 	add	r2,r2,1

                      76 ;22:         pBuffer[iBufPos++] = iLength / 256;


                      77 

00000028 e1a03420     78 	mov	r3,r0 lsr 8

0000002c e7c13002     79 	strb	r3,[r1,r2]

00000030 e2822001     80 	add	r2,r2,1

                      81 ;23:         pBuffer[iBufPos++] = iLength % 256;


                      82 

                      83 ;24: 


                      84 ;25:     }


                      85 ;26: 


                      86 ;27:     return iBufPos;


                      87 

                      88 .L21:

00000034 e7c10002     89 	strb	r0,[r1,r2]

00000038 e2820001     90 	add	r0,r2,1

0000003c e12fff1e*    91 	ret	

                      92 	.endf	BerEncoder_encodeLength

                      93 	.align	4

                      94 

                      95 ;iLength	r0	param

                      96 ;pBuffer	r1	param

                      97 ;iBufPos	r2	param

                      98 

                      99 	.section ".bss","awb"

                     100 .L74:

                     101 	.data

                     102 	.text

                     103 

                     104 ;28: }


                     105 

                     106 ;29: 


                     107 ;30: int BerDecoder_decodeLength(unsigned char* buffer, int* length, int bufPos, int maxBufPos)


                     108 	.align	4

                     109 	.align	4

                     110 BerDecoder_decodeLength::

00000040 e92d0070    111 	stmfd	[sp]!,{r4-r6}


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                     112 ;31: {


                     113 

                     114 ;32:     unsigned char len1;


                     115 ;33:     int i;


                     116 ;34: 


                     117 ;35:     if (bufPos >= maxBufPos)


                     118 

00000044 e1520003    119 	cmp	r2,r3

00000048 aa000064    120 	bge	.L112

                     121 ;36:         return -1;


                     122 

                     123 ;37: 


                     124 ;38:      len1 = buffer[bufPos++];


                     125 

0000004c e7d0c002    126 	ldrb	r12,[r0,r2]

00000050 e2822001    127 	add	r2,r2,1

                     128 ;39: 


                     129 ;40:     if (len1 & 0x80) {


                     130 

00000054 e31c0080    131 	tst	r12,128

                     132 ;56:             }


                     133 ;57:         }


                     134 ;58: 


                     135 ;59:     }


                     136 ;60:     else {


                     137 

                     138 ;61:         *length = len1;


                     139 

00000058 0581c000    140 	streq	r12,[r1]

0000005c 0a00005b    141 	beq	.L110

                     142 ;41:         int lenLength = len1 & 0x7f;


                     143 

00000060 e21c407f    144 	ands	r4,r12,127

                     145 ;42: 


                     146 ;43:         if (lenLength == 0) { /* indefinite length form */


                     147 

00000064 1a000004    148 	bne	.L100

                     149 ;44:             *length = -1;


                     150 

00000068 e3f0c000    151 	mvns	r12,0

0000006c e581c000    152 	str	r12,[r1]

                     153 ;62:     }


                     154 ;63: 


                     155 ;64:     if (*length < 0)


                     156 

                     157 

                     158 

00000070 51a00002    159 	movpl	r0,r2

00000074 43e00000    160 	mvnmi	r0,0

00000078 ea000059    161 	b	.L93

                     162 .L100:

                     163 ;45:         }


                     164 ;46:         else {


                     165 

                     166 ;47:             *length = 0;


                     167 

0000007c e3a0c000    168 	mov	r12,0

00000080 e581c000    169 	str	r12,[r1]

                     170 ;48: 


                     171 ;49: 


                     172 ;50:             for (i = 0; i < lenLength; i++) {



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                     173 

00000084 e3540000    174 	cmp	r4,0

00000088 a1a05004    175 	movge	r5,r4

0000008c b3a05000    176 	movlt	r5,0

00000090 e1b041a5    177 	movs	r4,r5 lsr 3

00000094 0a000041    178 	beq	.L178

                     179 .L179:

00000098 e1520003    180 	cmp	r2,r3

0000009c aa00004f    181 	bge	.L112

000000a0 e1a0c40c    182 	mov	r12,r12 lsl 8

000000a4 e581c000    183 	str	r12,[r1]

000000a8 e7d06002    184 	ldrb	r6,[r0,r2]

000000ac e2822001    185 	add	r2,r2,1

000000b0 e08cc006    186 	add	r12,r12,r6

000000b4 e581c000    187 	str	r12,[r1]

000000b8 e1520003    188 	cmp	r2,r3

000000bc aa000047    189 	bge	.L112

000000c0 e1a0c40c    190 	mov	r12,r12 lsl 8

000000c4 e581c000    191 	str	r12,[r1]

000000c8 e7d06002    192 	ldrb	r6,[r0,r2]

000000cc e2822001    193 	add	r2,r2,1

000000d0 e08cc006    194 	add	r12,r12,r6

000000d4 e581c000    195 	str	r12,[r1]

000000d8 e1520003    196 	cmp	r2,r3

000000dc aa00003f    197 	bge	.L112

000000e0 e1a0c40c    198 	mov	r12,r12 lsl 8

000000e4 e581c000    199 	str	r12,[r1]

000000e8 e7d06002    200 	ldrb	r6,[r0,r2]

000000ec e2822001    201 	add	r2,r2,1

000000f0 e08cc006    202 	add	r12,r12,r6

000000f4 e581c000    203 	str	r12,[r1]

000000f8 e1520003    204 	cmp	r2,r3

000000fc aa000037    205 	bge	.L112

00000100 e1a0c40c    206 	mov	r12,r12 lsl 8

00000104 e581c000    207 	str	r12,[r1]

00000108 e7d06002    208 	ldrb	r6,[r0,r2]

0000010c e2822001    209 	add	r2,r2,1

00000110 e08cc006    210 	add	r12,r12,r6

00000114 e581c000    211 	str	r12,[r1]

00000118 e1520003    212 	cmp	r2,r3

0000011c aa00002f    213 	bge	.L112

00000120 e1a0c40c    214 	mov	r12,r12 lsl 8

00000124 e581c000    215 	str	r12,[r1]

00000128 e7d06002    216 	ldrb	r6,[r0,r2]

0000012c e2822001    217 	add	r2,r2,1

00000130 e08cc006    218 	add	r12,r12,r6

00000134 e581c000    219 	str	r12,[r1]

00000138 e1520003    220 	cmp	r2,r3

0000013c aa000027    221 	bge	.L112

00000140 e1a0c40c    222 	mov	r12,r12 lsl 8

00000144 e581c000    223 	str	r12,[r1]

00000148 e7d06002    224 	ldrb	r6,[r0,r2]

0000014c e2822001    225 	add	r2,r2,1

00000150 e08cc006    226 	add	r12,r12,r6

00000154 e581c000    227 	str	r12,[r1]

00000158 e1520003    228 	cmp	r2,r3

0000015c aa00001f    229 	bge	.L112

00000160 e1a0c40c    230 	mov	r12,r12 lsl 8

00000164 e581c000    231 	str	r12,[r1]

00000168 e7d06002    232 	ldrb	r6,[r0,r2]

0000016c e2822001    233 	add	r2,r2,1


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
00000170 e08cc006    234 	add	r12,r12,r6

00000174 e581c000    235 	str	r12,[r1]

00000178 e1520003    236 	cmp	r2,r3

0000017c aa000017    237 	bge	.L112

00000180 e1a0c40c    238 	mov	r12,r12 lsl 8

00000184 e581c000    239 	str	r12,[r1]

00000188 e7d06002    240 	ldrb	r6,[r0,r2]

0000018c e2822001    241 	add	r2,r2,1

00000190 e08cc006    242 	add	r12,r12,r6

00000194 e581c000    243 	str	r12,[r1]

00000198 e2544001    244 	subs	r4,r4,1

0000019c 1affffbd    245 	bne	.L179

                     246 .L178:

000001a0 e2154007    247 	ands	r4,r5,7

000001a4 0a000009    248 	beq	.L110

                     249 .L205:

000001a8 e1520003    250 	cmp	r2,r3

000001ac aa00000b    251 	bge	.L112

000001b0 e1a0c40c    252 	mov	r12,r12 lsl 8

000001b4 e581c000    253 	str	r12,[r1]

000001b8 e7d05002    254 	ldrb	r5,[r0,r2]

000001bc e2822001    255 	add	r2,r2,1

000001c0 e08cc005    256 	add	r12,r12,r5

000001c4 e581c000    257 	str	r12,[r1]

000001c8 e2544001    258 	subs	r4,r4,1

000001cc 1afffff5    259 	bne	.L205

                     260 .L110:

                     261 ;62:     }


                     262 ;63: 


                     263 ;64:     if (*length < 0)


                     264 

                     265 

                     266 

000001d0 e35c0000    267 	cmp	r12,0

000001d4 a1a00002    268 	movge	r0,r2

000001d8 b3e00000    269 	mvnlt	r0,0

000001dc ea000000    270 	b	.L93

                     271 .L112:

                     272 ;65:         return -1;


                     273 

000001e0 e3e00000    274 	mvn	r0,0

                     275 .L93:

000001e4 e8bd0070    276 	ldmfd	[sp]!,{r4-r6}

000001e8 e12fff1e*   277 	ret	

                     278 	.endf	BerDecoder_decodeLength

                     279 	.align	4

                     280 ;len1	r12	local

                     281 ;lenLength	r4	local

                     282 

                     283 ;buffer	r0	param

                     284 ;length	r1	param

                     285 ;bufPos	r2	param

                     286 ;maxBufPos	r3	param

                     287 

                     288 	.section ".bss","awb"

                     289 .L484:

                     290 	.data

                     291 	.text

                     292 

                     293 ;74: }


                     294 


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                     295 ;75: 


                     296 ;76: 


                     297 ;77: 


                     298 ;78: 


                     299 ;79: bool BerDecoder_decodeTLFromBufferView(BufferView* buf, uint8_t* pTag, int* pLen, int* pFullLen)


                     300 	.align	4

                     301 	.align	4

                     302 BerDecoder_decodeTLFromBufferView::

000001ec e92d4cf0    303 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

000001f0 e24dd004    304 	sub	sp,sp,4

000001f4 e1a04000    305 	mov	r4,r0

000001f8 e1a06001    306 	mov	r6,r1

000001fc e1a07002    307 	mov	r7,r2

00000200 e1a0a003    308 	mov	r10,r3

                     309 ;80: {


                     310 

                     311 ;81:     uint8_t tag;


                     312 ;82:     int len;


                     313 ;83:     int objPos = buf->pos;


                     314 

00000204 e8941021    315 	ldmfd	[r4],{r0,r5,r12}

00000208 e2852001    316 	add	r2,r5,1

0000020c e1a0300c    317 	mov	r3,r12

                     318 ;84: 


                     319 ;85:     tag = BufferView_readTag(buf);


                     320 

00000210 e5842004    321 	str	r2,[r4,4]

00000214 e7d0b005    322 	ldrb	fp,[r0,r5]

                     323 ;86:     buf->pos = BerDecoder_decodeLength(buf->p, &len, buf->pos, buf->len);


                     324 

00000218 e1a0100d    325 	mov	r1,sp

0000021c ebffff87*   326 	bl	BerDecoder_decodeLength

00000220 e5840004    327 	str	r0,[r4,4]

                     328 ;87:     if (buf->pos <= 0)


                     329 

00000224 e3500000    330 	cmp	r0,0

                     331 ;88:     {


                     332 

                     333 ;89:         return FALSE;


                     334 

00000228 020000ff    335 	andeq	r0,r0,255

0000022c 0a00000b    336 	beq	.L553

                     337 ;90:     }


                     338 ;91: 


                     339 ;92:     if (pTag != NULL)


                     340 

00000230 e3560000    341 	cmp	r6,0

                     342 ;93:     {


                     343 

                     344 ;94:         *pTag = tag;


                     345 

00000234 15c6b000    346 	strneb	fp,[r6]

                     347 ;95:     }


                     348 ;96: 


                     349 ;97:     if (pLen != NULL)


                     350 

00000238 e3570000    351 	cmp	r7,0

                     352 ;98:     {


                     353 

                     354 ;99:         *pLen = len;


                     355 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
0000023c 159d0000    356 	ldrne	r0,[sp]

00000240 15870000    357 	strne	r0,[r7]

                     358 ;100:     }


                     359 ;101: 


                     360 ;102:     if (pFullLen != NULL)


                     361 

00000244 e35a0000    362 	cmp	r10,0

                     363 ;103:     {


                     364 

                     365 ;104:         *pFullLen = buf->pos - objPos + len;


                     366 

00000248 15940004    367 	ldrne	r0,[r4,4]

0000024c 159d1000    368 	ldrne	r1,[sp]

00000250 10400005    369 	subne	r0,r0,r5

00000254 10810000    370 	addne	r0,r1,r0

00000258 158a0000    371 	strne	r0,[r10]

                     372 ;105:     }


                     373 ;106: 


                     374 ;107:     return TRUE;


                     375 

0000025c e3a00001    376 	mov	r0,1

                     377 .L553:

00000260 e28dd004    378 	add	sp,sp,4

00000264 e8bd8cf0    379 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                     380 	.endf	BerDecoder_decodeTLFromBufferView

                     381 	.align	4

                     382 ;tag	fp	local

                     383 ;len	[sp]	local

                     384 ;objPos	r5	local

                     385 

                     386 ;buf	r4	param

                     387 ;pTag	r6	param

                     388 ;pLen	r7	param

                     389 ;pFullLen	r10	param

                     390 

                     391 	.section ".bss","awb"

                     392 .L650:

                     393 	.data

                     394 	.text

                     395 

                     396 ;108: }


                     397 

                     398 ;109: 


                     399 ;110: int32_t BerDecoder_decodeInt32(uint8_t* buffer, int intlen, int bufPos)


                     400 	.align	4

                     401 	.align	4

                     402 BerDecoder_decodeInt32::

00000268 e92d0070    403 	stmfd	[sp]!,{r4-r6}

0000026c e1a03000    404 	mov	r3,r0

                     405 ;111: {


                     406 

                     407 ;112:     int32_t value;


                     408 ;113:     int i;


                     409 ;114: 


                     410 ;115:     bool isNegative = ((buffer[bufPos] & 0x80) == 0x80);


                     411 

00000270 e7d30002    412 	ldrb	r0,[r3,r2]

00000274 e3a04000    413 	mov	r4,0

00000278 e1a00c00    414 	mov	r0,r0 lsl 24

0000027c e1b00fa0    415 	movs	r0,r0 lsr 31

                     416 ;116: 



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                     417 ;117:     if (isNegative)


                     418 

                     419 ;118:         value = -1;


                     420 

00000280 13e00000    421 	mvnne	r0,0

                     422 ;121: 


                     423 ;122:     for (i = 0; i < intlen; i++) {


                     424 

                     425 ;119:     else


                     426 ;120:         value = 0;


                     427 

                     428 ;121: 


                     429 ;122:     for (i = 0; i < intlen; i++) {


                     430 

00000284 e3510000    431 	cmp	r1,0

00000288 a1a05001    432 	movge	r5,r1

0000028c b3a05000    433 	movlt	r5,0

00000290 e1b0c1a5    434 	movs	r12,r5 lsr 3

00000294 0a000013    435 	beq	.L726

00000298 e0821003    436 	add	r1,r2,r3

0000029c e1a0418c    437 	mov	r4,r12 lsl 3

                     438 .L742:

000002a0 e4d16001    439 	ldrb	r6,[r1],1

000002a4 e0860400    440 	add	r0,r6,r0 lsl 8

000002a8 e4d16001    441 	ldrb	r6,[r1],1

000002ac e0860400    442 	add	r0,r6,r0 lsl 8

000002b0 e4d16001    443 	ldrb	r6,[r1],1

000002b4 e0860400    444 	add	r0,r6,r0 lsl 8

000002b8 e4d16001    445 	ldrb	r6,[r1],1

000002bc e0860400    446 	add	r0,r6,r0 lsl 8

000002c0 e4d16001    447 	ldrb	r6,[r1],1

000002c4 e0860400    448 	add	r0,r6,r0 lsl 8

000002c8 e4d16001    449 	ldrb	r6,[r1],1

000002cc e0860400    450 	add	r0,r6,r0 lsl 8

000002d0 e4d16001    451 	ldrb	r6,[r1],1

000002d4 e0860400    452 	add	r0,r6,r0 lsl 8

000002d8 e4d16001    453 	ldrb	r6,[r1],1

000002dc e25cc001    454 	subs	r12,r12,1

000002e0 e0860400    455 	add	r0,r6,r0 lsl 8

000002e4 1affffed    456 	bne	.L742

                     457 .L726:

000002e8 e215c007    458 	ands	r12,r5,7

000002ec 10831002    459 	addne	r1,r3,r2

000002f0 10841001    460 	addne	r1,r4,r1

                     461 .L746:

000002f4 14d12001    462 	ldrneb	r2,[r1],1

000002f8 10820400    463 	addne	r0,r2,r0 lsl 8

000002fc 125cc001    464 	subnes	r12,r12,1

00000300 1afffffb    465 	bne	.L746

                     466 .L681:

                     467 ;125:     }


                     468 ;126: 


                     469 ;127:     return value;


                     470 

00000304 e8bd0070    471 	ldmfd	[sp]!,{r4-r6}

00000308 e12fff1e*   472 	ret	

                     473 	.endf	BerDecoder_decodeInt32

                     474 	.align	4

                     475 ;value	r0	local

                     476 ;i	r4	local

                     477 ;isNegative	r0	local


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                     478 

                     479 ;buffer	r3	param

                     480 ;intlen	r1	param

                     481 ;bufPos	r2	param

                     482 

                     483 	.section ".bss","awb"

                     484 .L923:

                     485 	.data

                     486 	.text

                     487 

                     488 ;128: }


                     489 

                     490 ;129: 


                     491 ;130: unsigned int BerDecoder_decodeUint32(unsigned char* buffer, int intLen, int bufPos)


                     492 	.align	4

                     493 	.align	4

                     494 BerDecoder_decodeUint32::

0000030c e92d0070    495 	stmfd	[sp]!,{r4-r6}

                     496 ;131: {


                     497 

00000310 e3a03000    498 	mov	r3,0

                     499 ;132:     unsigned int value = 0;


                     500 

                     501 ;133: 


                     502 ;134:     int i;


                     503 ;135:     for (i = 0; i < intLen; i++) {


                     504 

00000314 e1a04003    505 	mov	r4,r3

00000318 e3510000    506 	cmp	r1,0

0000031c a1a05001    507 	movge	r5,r1

00000320 b3a05000    508 	movlt	r5,0

00000324 e1b0c1a5    509 	movs	r12,r5 lsr 3

00000328 0a000013    510 	beq	.L974

0000032c e0821000    511 	add	r1,r2,r0

00000330 e1a0418c    512 	mov	r4,r12 lsl 3

                     513 .L990:

00000334 e4d16001    514 	ldrb	r6,[r1],1

00000338 e0863403    515 	add	r3,r6,r3 lsl 8

0000033c e4d16001    516 	ldrb	r6,[r1],1

00000340 e0863403    517 	add	r3,r6,r3 lsl 8

00000344 e4d16001    518 	ldrb	r6,[r1],1

00000348 e0863403    519 	add	r3,r6,r3 lsl 8

0000034c e4d16001    520 	ldrb	r6,[r1],1

00000350 e0863403    521 	add	r3,r6,r3 lsl 8

00000354 e4d16001    522 	ldrb	r6,[r1],1

00000358 e0863403    523 	add	r3,r6,r3 lsl 8

0000035c e4d16001    524 	ldrb	r6,[r1],1

00000360 e0863403    525 	add	r3,r6,r3 lsl 8

00000364 e4d16001    526 	ldrb	r6,[r1],1

00000368 e0863403    527 	add	r3,r6,r3 lsl 8

0000036c e4d16001    528 	ldrb	r6,[r1],1

00000370 e25cc001    529 	subs	r12,r12,1

00000374 e0863403    530 	add	r3,r6,r3 lsl 8

00000378 1affffed    531 	bne	.L990

                     532 .L974:

0000037c e215c007    533 	ands	r12,r5,7

00000380 10800002    534 	addne	r0,r0,r2

00000384 10840000    535 	addne	r0,r4,r0

                     536 .L994:

00000388 14d01001    537 	ldrneb	r1,[r0],1

0000038c 10813403    538 	addne	r3,r1,r3 lsl 8


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
00000390 125cc001    539 	subnes	r12,r12,1

00000394 1afffffb    540 	bne	.L994

                     541 .L954:

                     542 ;138:     }


                     543 ;139: 


                     544 ;140:     return value;


                     545 

00000398 e1a00003    546 	mov	r0,r3

0000039c e8bd0070    547 	ldmfd	[sp]!,{r4-r6}

000003a0 e12fff1e*   548 	ret	

                     549 	.endf	BerDecoder_decodeUint32

                     550 	.align	4

                     551 ;value	r3	local

                     552 ;i	r4	local

                     553 

                     554 ;buffer	r0	param

                     555 ;intLen	r1	param

                     556 ;bufPos	r2	param

                     557 

                     558 	.section ".bss","awb"

                     559 .L1153:

                     560 	.data

                     561 	.text

                     562 

                     563 ;141: }


                     564 

                     565 ;142: 


                     566 ;143: int BerEncoder_encodeTL( unsigned char ucTag, unsigned int iLength,


                     567 	.align	4

                     568 	.align	4

                     569 BerEncoder_encodeTL::

000003a4 e1a0c001    570 	mov	r12,r1

000003a8 e1a01002    571 	mov	r1,r2

                     572 ;144:                          unsigned char* pBuffer, int iBufPos )


                     573 ;145: {


                     574 

                     575 ;146:     pBuffer[iBufPos++] = ucTag;


                     576 

000003ac e2832001    577 	add	r2,r3,1

000003b0 e7c10003    578 	strb	r0,[r1,r3]

                     579 ;147:     iBufPos = BerEncoder_encodeLength( iLength, pBuffer, iBufPos );


                     580 

000003b4 e1a0000c    581 	mov	r0,r12

000003b8 eaffff10*   582 	b	BerEncoder_encodeLength

                     583 	.endf	BerEncoder_encodeTL

                     584 	.align	4

                     585 

                     586 ;ucTag	r0	param

                     587 ;iLength	r4	param

                     588 ;pBuffer	r12	param

                     589 ;iBufPos	none	param

                     590 

                     591 	.section ".bss","awb"

                     592 .L1198:

                     593 	.data

                     594 	.text

                     595 

                     596 ;149: }


                     597 

                     598 ;150: 


                     599 ;151: int BerDecoder_decodeString(uint8_t* buf, int bufPos, int maxPos, uint8_t** str, int* strLen)



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                     600 	.align	4

                     601 	.align	4

                     602 BerDecoder_decodeString::

000003bc e92d4070    603 	stmfd	[sp]!,{r4-r6,lr}

                     604 ;152: {


                     605 

                     606 ;153:     uint8_t tag = buf[bufPos++];


                     607 

000003c0 e1a05003    608 	mov	r5,r3

000003c4 e1a0c002    609 	mov	r12,r2

000003c8 e1a0300c    610 	mov	r3,r12

000003cc e59d4010    611 	ldr	r4,[sp,16]

000003d0 e2812001    612 	add	r2,r1,1

                     613 ;154:     bufPos = BerDecoder_decodeLength(buf, strLen, bufPos,  maxPos);


                     614 

000003d4 e1a01004    615 	mov	r1,r4

000003d8 e1a06000    616 	mov	r6,r0

000003dc ebffff17*   617 	bl	BerDecoder_decodeLength

000003e0 e1a01000    618 	mov	r1,r0

                     619 ;155:     if(bufPos == -1)


                     620 

000003e4 e3710001    621 	cmn	r1,1

                     622 ;156:     {


                     623 

                     624 ;157:         return -1;


                     625 

                     626 ;158:     }


                     627 ;159:     *str = buf + bufPos;


                     628 

000003e8 10810006    629 	addne	r0,r1,r6

000003ec 15850000    630 	strne	r0,[r5]

                     631 ;160:     return bufPos + *strLen;


                     632 

000003f0 15940000    633 	ldrne	r0,[r4]

000003f4 10800001    634 	addne	r0,r0,r1

000003f8 e8bd8070    635 	ldmfd	[sp]!,{r4-r6,pc}

                     636 	.endf	BerDecoder_decodeString

                     637 	.align	4

                     638 

                     639 ;buf	r6	param

                     640 ;bufPos	r1	param

                     641 ;maxPos	r12	param

                     642 ;str	r5	param

                     643 ;strLen	r4	param

                     644 

                     645 	.section ".bss","awb"

                     646 .L1253:

                     647 	.data

                     648 	.text

                     649 

                     650 ;161: }


                     651 

                     652 ;162: 


                     653 ;163: int EncodeBOOLEAN( unsigned char* pBuffer, unsigned char bValue )


                     654 	.align	4

                     655 	.align	4

                     656 EncodeBOOLEAN::

                     657 ;164: {


                     658 

                     659 ;165:     pBuffer[0] = ASN_BOOLEAN;	//идентификатор


                     660 


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
000003fc e3a02001    661 	mov	r2,1

00000400 e5c02000    662 	strb	r2,[r0]

                     663 ;166:     pBuffer[1] = 0x01;			//длина


                     664 

00000404 e5c02001    665 	strb	r2,[r0,1]

                     666 ;167:     pBuffer[2] = 0x00;			//значение


                     667 

00000408 e3a02000    668 	mov	r2,0

0000040c e5c02002    669 	strb	r2,[r0,2]

                     670 ;168:     if( bValue > 0 )


                     671 

00000410 e3510000    672 	cmp	r1,0

                     673 ;169:     {


                     674 

                     675 ;170:         pBuffer[2] = 0x01;


                     676 

00000414 13a01001    677 	movne	r1,1

00000418 15c01002    678 	strneb	r1,[r0,2]

                     679 ;171:     }


                     680 ;172:     return ASN_BOOLEANTYPE_SIZE;


                     681 

0000041c e3a00003    682 	mov	r0,3

00000420 e12fff1e*   683 	ret	

                     684 	.endf	EncodeBOOLEAN

                     685 	.align	4

                     686 

                     687 ;pBuffer	r0	param

                     688 ;bValue	r1	param

                     689 

                     690 	.section ".bss","awb"

                     691 .L1309:

                     692 	.data

                     693 	.text

                     694 

                     695 ;173: }


                     696 

                     697 ;174: 


                     698 ;175: int DecodeBOOLEAN( unsigned char* pBuffer, int iBufPos, unsigned char* pValueOut )


                     699 	.align	4

                     700 	.align	4

                     701 DecodeBOOLEAN::

                     702 ;176: {


                     703 

                     704 ;177: 


                     705 ;178:     if( pBuffer[iBufPos] != ASN_BOOLEAN )


                     706 

00000424 e7d03001    707 	ldrb	r3,[r0,r1]

00000428 e3530001    708 	cmp	r3,1

                     709 ;179:     {//не тип BOOLEAN


                     710 

                     711 ;180:         return -1;


                     712 

                     713 ;181:     }


                     714 ;182:     if( pBuffer[iBufPos+1] != 0x01 )


                     715 

0000042c 00803001    716 	addeq	r3,r0,r1

00000430 05d33001    717 	ldreqb	r3,[r3,1]

00000434 03530001    718 	cmpeq	r3,1

                     719 ;185:     }


                     720 ;186: 


                     721 ;187: 



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                     722 ;188:     *pValueOut = pBuffer[iBufPos+2];


                     723 

00000438 00800001    724 	addeq	r0,r0,r1

0000043c 05d00002    725 	ldreqb	r0,[r0,2]

00000440 05c20000    726 	streqb	r0,[r2]

                     727 ;189: 


                     728 ;190:     return iBufPos+3;


                     729 

00000444 02810003    730 	addeq	r0,r1,3

                     731 ;183:     {//размер не 0x01


                     732 

                     733 ;184:         return -1;


                     734 

00000448 13e00000    735 	mvnne	r0,0

0000044c e12fff1e*   736 	ret	

                     737 	.endf	DecodeBOOLEAN

                     738 	.align	4

                     739 

                     740 ;pBuffer	r0	param

                     741 ;iBufPos	r1	param

                     742 ;pValueOut	r2	param

                     743 

                     744 	.section ".bss","awb"

                     745 .L1388:

                     746 	.data

                     747 	.text

                     748 

                     749 ;191: }


                     750 

                     751 ;192: 


                     752 ;193: int EncodeBIT_STRINGPrimitive( unsigned char* pBuffer, unsigned char* pBitString, int iLengthInBit )


                     753 	.align	4

                     754 	.align	4

                     755 EncodeBIT_STRINGPrimitive::

00000450 e92d4010    756 	stmfd	[sp]!,{r4,lr}

                     757 ;194: {


                     758 

00000454 e3a03000    759 	mov	r3,0

                     760 ;195:     unsigned char ucRemn = (unsigned char)( iLengthInBit & 0x07 );	//остаток от деления на 8


                     761 

00000458 e212c007    762 	ands	r12,r2,7

                     763 ;196:     unsigned char ucUnusedBitsQuant = 0;							//количество незначащих бит в конце


                     764 

                     765 ;197:     int			  iSize;											//длина всей посылки ASN


                     766 ;198: 


                     767 ;199:     if( ucRemn > 0 )


                     768 

                     769 ;200:     {


                     770 

                     771 ;201:         ucUnusedBitsQuant = 8 - ucRemn;


                     772 

0000045c 126c3008    773 	rsbne	r3,r12,8

00000460 120330ff    774 	andne	r3,r3,255

                     775 ;202:     }


                     776 ;203: 


                     777 ;204:     iSize = ( iLengthInBit + ucUnusedBitsQuant ) / 8 + 1;	//общая длина в байтах ( +1 это головной байт для незначащих бит )


                     778 

00000464 e0832002    779 	add	r2,r3,r2

00000468 e1a0cfc2    780 	mov	r12,r2 asr 31

0000046c e0822eac    781 	add	r2,r2,r12 lsr 29

00000470 e1a021c2    782 	mov	r2,r2 asr 3


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
00000474 e2824001    783 	add	r4,r2,1

                     784 ;205: 


                     785 ;206: 


                     786 ;207:     pBuffer[0] = ASN_BIT_STRING;			//идентификатор BIT_STRING


                     787 

00000478 e3a02003    788 	mov	r2,3

0000047c e5c02000    789 	strb	r2,[r0]

                     790 ;208:     if( iSize > 127 )


                     791 

00000480 e354007f    792 	cmp	r4,127

                     793 ;209:     {


                     794 

                     795 ;210:      //BIT_STRING такого размера не используются


                     796 ;211:         return 0;


                     797 

00000484 c3a00000    798 	movgt	r0,0

00000488 ca000005    799 	bgt	.L1402

                     800 ;212:     }


                     801 ;213:     else


                     802 ;214:     {


                     803 

                     804 ;215:         pBuffer[1] = (unsigned char)iSize;				//длина


                     805 

0000048c e2442001    806 	sub	r2,r4,1

00000490 e5c04001    807 	strb	r4,[r0,1]

                     808 ;216:         pBuffer[2] = ucUnusedBitsQuant;					//количество незначащих бит в конце посылки


                     809 

00000494 e5c03002    810 	strb	r3,[r0,2]

                     811 ;217:         memcpy( &pBuffer[3], pBitString, iSize - 1 );	//битовая строка


                     812 

00000498 e2800003    813 	add	r0,r0,3

0000049c eb000000*   814 	bl	memcpy

                     815 ;218:         return iSize + 2;


                     816 

000004a0 e2840002    817 	add	r0,r4,2

                     818 .L1402:

000004a4 e8bd8010    819 	ldmfd	[sp]!,{r4,pc}

                     820 	.endf	EncodeBIT_STRINGPrimitive

                     821 	.align	4

                     822 ;ucRemn	r12	local

                     823 ;ucUnusedBitsQuant	r3	local

                     824 ;iSize	r4	local

                     825 

                     826 ;pBuffer	r0	param

                     827 ;pBitString	r1	param

                     828 ;iLengthInBit	r2	param

                     829 

                     830 	.section ".bss","awb"

                     831 .L1463:

                     832 	.data

                     833 	.text

                     834 

                     835 ;219:     }


                     836 ;220: }


                     837 

                     838 ;221: int DecodeBIT_STRINGPrimitive( unsigned char* pBuffer, unsigned char* pBitStringOut,


                     839 	.align	4

                     840 	.align	4

                     841 DecodeBIT_STRINGPrimitive::

000004a8 e92d4010    842 	stmfd	[sp]!,{r4,lr}

000004ac e1a0c001    843 	mov	r12,r1


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                     844 ;222:                                unsigned char* pBitStringSizeOut, unsigned char* pUnusedBitsQuantOut )


                     845 ;223: {


                     846 

                     847 ;224: 


                     848 ;225:     if( pBuffer[0] != ASN_BIT_STRING )


                     849 

000004b0 e5d01000    850 	ldrb	r1,[r0]

000004b4 e3510003    851 	cmp	r1,3

000004b8 1a000002    852 	bne	.L1487

                     853 ;226:     {//не тип BIT_STRING


                     854 

                     855 ;227:         return -1;


                     856 

                     857 ;228:     }


                     858 ;229: 


                     859 ;230:     if( pBuffer[1] > 127 )


                     860 

000004bc e5d01001    861 	ldrb	r1,[r0,1]

000004c0 e351007f    862 	cmp	r1,127

000004c4 9a000001    863 	bls	.L1486

                     864 .L1487:

                     865 ;231:     {//неверный размер


                     866 

                     867 ;232:         return -1;


                     868 

000004c8 e3e04000    869 	mvn	r4,0

000004cc ea00000b    870 	b	.L1481

                     871 .L1486:

                     872 ;233:     }


                     873 ;234: 


                     874 ;235:     if( pBuffer[1] == 1 )


                     875 

000004d0 e3a04000    876 	mov	r4,0

000004d4 e3510001    877 	cmp	r1,1

                     878 ;236:     {//пустая битовая строка


                     879 

                     880 ;237:         *pBitStringSizeOut = 0;


                     881 

000004d8 05c24000    882 	streqb	r4,[r2]

                     883 ;238:         return 0;


                     884 

000004dc 0a000007    885 	beq	.L1481

                     886 ;239:     }


                     887 ;240: 


                     888 ;241:     *pBitStringSizeOut = pBuffer[1] - 1;


                     889 

000004e0 e2411001    890 	sub	r1,r1,1

000004e4 e5c21000    891 	strb	r1,[r2]

                     892 ;242:     *pUnusedBitsQuantOut = pBuffer[2];


                     893 

000004e8 e5d01002    894 	ldrb	r1,[r0,2]

000004ec e5c31000    895 	strb	r1,[r3]

                     896 ;243:     memcpy( pBitStringOut, &pBuffer[3], (int)(*pBitStringSizeOut) );


                     897 

000004f0 e5d22000    898 	ldrb	r2,[r2]

000004f4 e2801003    899 	add	r1,r0,3

000004f8 e1a0000c    900 	mov	r0,r12

000004fc eb000000*   901 	bl	memcpy

                     902 ;244:     return 0;


                     903 

                     904 .L1481:


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
00000500 e1a00004    905 	mov	r0,r4

00000504 e8bd8010    906 	ldmfd	[sp]!,{r4,pc}

                     907 	.endf	DecodeBIT_STRINGPrimitive

                     908 	.align	4

                     909 

                     910 ;pBuffer	r0	param

                     911 ;pBitStringOut	r12	param

                     912 ;pBitStringSizeOut	r2	param

                     913 ;pUnusedBitsQuantOut	r3	param

                     914 

                     915 	.section ".bss","awb"

                     916 .L1560:

                     917 	.data

                     918 	.text

                     919 

                     920 ;245: }


                     921 

                     922 ;246: 


                     923 ;247: int BerDecoder_DecodeBitStringTLToInt(uint8_t* inBuf, int inBufPos)


                     924 	.align	4

                     925 	.align	4

                     926 BerDecoder_DecodeBitStringTLToInt::

00000508 e92d0030    927 	stmfd	[sp]!,{r4-r5}

                     928 ;248: {


                     929 

0000050c e2811001    930 	add	r1,r1,1

                     931 ;255:     len = inBuf[inBufPos++];


                     932 

00000510 e7d03001    933 	ldrb	r3,[r0,r1]

00000514 e3a02000    934 	mov	r2,0

                     935 ;249:     int result = 0;


                     936 

                     937 ;250:     int len;


                     938 ;251:     int padding;


                     939 ;252:     int i;


                     940 ;253:     //Skip tag


                     941 ;254:     inBufPos++;


                     942 

00000518 e3530005    943 	cmp	r3,5

                     944 ;257:     {


                     945 

                     946 ;258:         return -1;


                     947 

                     948 ;259:     }


                     949 ;260:     len--;//padding не считаем


                     950 

0000051c d2433001    951 	suble	r3,r3,1

                     952 ;261:     padding = inBuf[inBufPos++];


                     953 

00000520 e2811001    954 	add	r1,r1,1

                     955 ;256:     if (len > 5)


                     956 

00000524 d7d0c001    957 	ldrleb	r12,[r0,r1]

00000528 d2811001    958 	addle	r1,r1,1

                     959 ;262:     if (padding > 7)


                     960 

0000052c d35c0007    961 	cmple	r12,7

                     962 ;263:     {


                     963 

                     964 ;264:         return -1;


                     965 


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
00000530 c3e00000    966 	mvngt	r0,0

00000534 ca000026    967 	bgt	.L1583

                     968 ;265:     }


                     969 ;266:     //Байт который лежит последним, должен быть младшим


                     970 ;267:     for (i = 0; i < len; ++i)


                     971 

00000538 e3530000    972 	cmp	r3,0

0000053c a1a04003    973 	movge	r4,r3

00000540 b3a04000    974 	movlt	r4,0

00000544 e1b031a4    975 	movs	r3,r4 lsr 3

00000548 0a000019    976 	beq	.L1637

                     977 .L1653:

0000054c e7d05001    978 	ldrb	r5,[r0,r1]

00000550 e2811001    979 	add	r1,r1,1

00000554 e1852402    980 	orr	r2,r5,r2 lsl 8

00000558 e7d05001    981 	ldrb	r5,[r0,r1]

0000055c e2811001    982 	add	r1,r1,1

00000560 e1852402    983 	orr	r2,r5,r2 lsl 8

00000564 e7d05001    984 	ldrb	r5,[r0,r1]

00000568 e2811001    985 	add	r1,r1,1

0000056c e1852402    986 	orr	r2,r5,r2 lsl 8

00000570 e7d05001    987 	ldrb	r5,[r0,r1]

00000574 e2811001    988 	add	r1,r1,1

00000578 e1852402    989 	orr	r2,r5,r2 lsl 8

0000057c e7d05001    990 	ldrb	r5,[r0,r1]

00000580 e2811001    991 	add	r1,r1,1

00000584 e1852402    992 	orr	r2,r5,r2 lsl 8

00000588 e7d05001    993 	ldrb	r5,[r0,r1]

0000058c e2811001    994 	add	r1,r1,1

00000590 e1852402    995 	orr	r2,r5,r2 lsl 8

00000594 e7d05001    996 	ldrb	r5,[r0,r1]

00000598 e2811001    997 	add	r1,r1,1

0000059c e1852402    998 	orr	r2,r5,r2 lsl 8

000005a0 e7d05001    999 	ldrb	r5,[r0,r1]

000005a4 e2811001   1000 	add	r1,r1,1

000005a8 e1852402   1001 	orr	r2,r5,r2 lsl 8

000005ac e2533001   1002 	subs	r3,r3,1

000005b0 1affffe5   1003 	bne	.L1653

                    1004 .L1637:

000005b4 e2143007   1005 	ands	r3,r4,7

                    1006 .L1657:

000005b8 17d04001   1007 	ldrneb	r4,[r0,r1]

000005bc 12811001   1008 	addne	r1,r1,1

000005c0 11842402   1009 	orrne	r2,r4,r2 lsl 8

000005c4 12533001   1010 	subnes	r3,r3,1

000005c8 1afffffa   1011 	bne	.L1657

                    1012 .L1595:

000005cc e1b00c52   1013 	movs	r0,r2 asr r12

000005d0 43e00000   1014 	mvnmi	r0,0

                    1015 ;271:     }


                    1016 ;272:     result >>= padding;


                    1017 

                    1018 ;273:     if (result < 0)


                    1019 

                    1020 

                    1021 

                    1022 ;276:     }


                    1023 ;277:     return result;


                    1024 

                    1025 .L1583:

000005d4 e8bd0030   1026 	ldmfd	[sp]!,{r4-r5}


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
000005d8 e12fff1e*  1027 	ret	

                    1028 	.endf	BerDecoder_DecodeBitStringTLToInt

                    1029 	.align	4

                    1030 ;result	r2	local

                    1031 ;len	r3	local

                    1032 ;padding	r12	local

                    1033 

                    1034 ;inBuf	r0	param

                    1035 ;inBufPos	r1	param

                    1036 

                    1037 	.section ".bss","awb"

                    1038 .L1846:

                    1039 	.data

                    1040 	.text

                    1041 

                    1042 ;278: }


                    1043 

                    1044 ;279: 


                    1045 ;280: int BerDecoder_DecodeLengthOld(unsigned char* pBuffer, int iBufPos, int iMaxBufPos, int* pLength)


                    1046 	.align	4

                    1047 	.align	4

                    1048 BerDecoder_DecodeLengthOld::

000005dc e92d0070   1049 	stmfd	[sp]!,{r4-r6}

                    1050 ;281: {


                    1051 

                    1052 ;282: 


                    1053 ;283:     unsigned char	ucLength;


                    1054 ;284:     int				iLenLength;


                    1055 ;285: 


                    1056 ;286: 


                    1057 ;287:     if( iBufPos >= iMaxBufPos )


                    1058 

000005e0 e1510002   1059 	cmp	r1,r2

                    1060 .L1881:

                    1061 ;288:     {


                    1062 

                    1063 ;289: 


                    1064 ;290:         return -1;


                    1065 

000005e4 a3e00000   1066 	mvnge	r0,0

000005e8 aa00005f   1067 	bge	.L1878

                    1068 .L1880:

                    1069 ;291: 


                    1070 ;292:     }//if( iBufPos > iMaxBufPos )


                    1071 ;293: 


                    1072 ;294: 


                    1073 ;295:     ucLength = pBuffer[iBufPos++];


                    1074 

000005ec e7d0c001   1075 	ldrb	r12,[r0,r1]

000005f0 e2811001   1076 	add	r1,r1,1

                    1077 ;296: 


                    1078 ;297:     if ( ucLength & 0x80 )


                    1079 

000005f4 e31c0080   1080 	tst	r12,128

                    1081 ;322: 


                    1082 ;323:             }//for( i = 0; i < iLenLength; i++ )


                    1083 ;324: 


                    1084 ;325:         }//else if( iLenLength == 0 )


                    1085 ;326: 


                    1086 ;327:     }//if ( ucLength & 0x80 )


                    1087 ;328:     else



                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                    1088 ;329:     {


                    1089 

                    1090 ;330: 


                    1091 ;331:         *pLength = ucLength;


                    1092 

000005f8 0583c000   1093 	streq	r12,[r3]

000005fc 0a000059   1094 	beq	.L1895

                    1095 ;298:     {//кодирование длины занимает больше одного байта


                    1096 

                    1097 ;299: 


                    1098 ;300:         iLenLength = ucLength & 0x7f;


                    1099 

00000600 e21cc07f   1100 	ands	r12,r12,127

                    1101 ;301:         if( iLenLength == 0 )


                    1102 

                    1103 ;302:         {//неопределенная форма длины


                    1104 

                    1105 ;303: 


                    1106 ;304:             *pLength = -1;


                    1107 

00000604 03e0c000   1108 	mvneq	r12,0

00000608 0583c000   1109 	streq	r12,[r3]

                    1110 ;332: 


                    1111 ;333:     }//else if ( ucLength & 0x80 )


                    1112 ;334: 


                    1113 ;335:     return iBufPos;


                    1114 

0000060c 01a00001   1115 	moveq	r0,r1

00000610 0a000055   1116 	beq	.L1878

                    1117 ;305: 


                    1118 ;306:         }//if( iLenLength == 0 )


                    1119 ;307:         else


                    1120 ;308:         {


                    1121 

                    1122 ;309:             int i;


                    1123 ;310:             *pLength = 0;


                    1124 

00000614 e3a05000   1125 	mov	r5,0

00000618 e5835000   1126 	str	r5,[r3]

                    1127 ;311:             for( i = 0; i < iLenLength; i++ )


                    1128 

0000061c e35c0000   1129 	cmp	r12,0

00000620 a1a0400c   1130 	movge	r4,r12

00000624 b3a04000   1131 	movlt	r4,0

00000628 e1b0c1a4   1132 	movs	r12,r4 lsr 3

0000062c 0a000041   1133 	beq	.L1949

                    1134 .L1950:

00000630 e1510002   1135 	cmp	r1,r2

00000634 aaffffea   1136 	bge	.L1881

00000638 e1a05405   1137 	mov	r5,r5 lsl 8

0000063c e5835000   1138 	str	r5,[r3]

00000640 e7d06001   1139 	ldrb	r6,[r0,r1]

00000644 e2811001   1140 	add	r1,r1,1

00000648 e0855006   1141 	add	r5,r5,r6

0000064c e5835000   1142 	str	r5,[r3]

00000650 e1510002   1143 	cmp	r1,r2

00000654 aaffffe2   1144 	bge	.L1881

00000658 e1a05405   1145 	mov	r5,r5 lsl 8

0000065c e5835000   1146 	str	r5,[r3]

00000660 e7d06001   1147 	ldrb	r6,[r0,r1]

00000664 e2811001   1148 	add	r1,r1,1


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
00000668 e0855006   1149 	add	r5,r5,r6

0000066c e5835000   1150 	str	r5,[r3]

00000670 e1510002   1151 	cmp	r1,r2

00000674 aaffffda   1152 	bge	.L1881

00000678 e1a05405   1153 	mov	r5,r5 lsl 8

0000067c e5835000   1154 	str	r5,[r3]

00000680 e7d06001   1155 	ldrb	r6,[r0,r1]

00000684 e2811001   1156 	add	r1,r1,1

00000688 e0855006   1157 	add	r5,r5,r6

0000068c e5835000   1158 	str	r5,[r3]

00000690 e1510002   1159 	cmp	r1,r2

00000694 aaffffd2   1160 	bge	.L1881

00000698 e1a05405   1161 	mov	r5,r5 lsl 8

0000069c e5835000   1162 	str	r5,[r3]

000006a0 e7d06001   1163 	ldrb	r6,[r0,r1]

000006a4 e2811001   1164 	add	r1,r1,1

000006a8 e0855006   1165 	add	r5,r5,r6

000006ac e5835000   1166 	str	r5,[r3]

000006b0 e1510002   1167 	cmp	r1,r2

000006b4 aaffffca   1168 	bge	.L1881

000006b8 e1a05405   1169 	mov	r5,r5 lsl 8

000006bc e5835000   1170 	str	r5,[r3]

000006c0 e7d06001   1171 	ldrb	r6,[r0,r1]

000006c4 e2811001   1172 	add	r1,r1,1

000006c8 e0855006   1173 	add	r5,r5,r6

000006cc e5835000   1174 	str	r5,[r3]

000006d0 e1510002   1175 	cmp	r1,r2

000006d4 aaffffc2   1176 	bge	.L1881

000006d8 e1a05405   1177 	mov	r5,r5 lsl 8

000006dc e5835000   1178 	str	r5,[r3]

000006e0 e7d06001   1179 	ldrb	r6,[r0,r1]

000006e4 e2811001   1180 	add	r1,r1,1

000006e8 e0855006   1181 	add	r5,r5,r6

000006ec e5835000   1182 	str	r5,[r3]

000006f0 e1510002   1183 	cmp	r1,r2

000006f4 aaffffba   1184 	bge	.L1881

000006f8 e1a05405   1185 	mov	r5,r5 lsl 8

000006fc e5835000   1186 	str	r5,[r3]

00000700 e7d06001   1187 	ldrb	r6,[r0,r1]

00000704 e2811001   1188 	add	r1,r1,1

00000708 e0855006   1189 	add	r5,r5,r6

0000070c e5835000   1190 	str	r5,[r3]

00000710 e1510002   1191 	cmp	r1,r2

00000714 aaffffb2   1192 	bge	.L1881

00000718 e1a05405   1193 	mov	r5,r5 lsl 8

0000071c e5835000   1194 	str	r5,[r3]

00000720 e7d06001   1195 	ldrb	r6,[r0,r1]

00000724 e2811001   1196 	add	r1,r1,1

00000728 e0855006   1197 	add	r5,r5,r6

0000072c e5835000   1198 	str	r5,[r3]

00000730 e25cc001   1199 	subs	r12,r12,1

00000734 1affffbd   1200 	bne	.L1950

                    1201 .L1949:

00000738 e214c007   1202 	ands	r12,r4,7

0000073c 0a000009   1203 	beq	.L1895

                    1204 .L1976:

00000740 e1510002   1205 	cmp	r1,r2

00000744 aaffffa6   1206 	bge	.L1881

00000748 e1a05405   1207 	mov	r5,r5 lsl 8

0000074c e5835000   1208 	str	r5,[r3]

00000750 e7d04001   1209 	ldrb	r4,[r0,r1]


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
00000754 e2811001   1210 	add	r1,r1,1

00000758 e0855004   1211 	add	r5,r5,r4

0000075c e5835000   1212 	str	r5,[r3]

00000760 e25cc001   1213 	subs	r12,r12,1

00000764 1afffff5   1214 	bne	.L1976

                    1215 .L1895:

                    1216 ;332: 


                    1217 ;333:     }//else if ( ucLength & 0x80 )


                    1218 ;334: 


                    1219 ;335:     return iBufPos;


                    1220 

00000768 e1a00001   1221 	mov	r0,r1

                    1222 .L1878:

0000076c e8bd0070   1223 	ldmfd	[sp]!,{r4-r6}

00000770 e12fff1e*  1224 	ret	

                    1225 	.endf	BerDecoder_DecodeLengthOld

                    1226 	.align	4

                    1227 ;ucLength	r12	local

                    1228 ;iLenLength	r12	local

                    1229 

                    1230 ;pBuffer	r0	param

                    1231 ;iBufPos	r1	param

                    1232 ;iMaxBufPos	r2	param

                    1233 ;pLength	r3	param

                    1234 

                    1235 	.section ".bss","awb"

                    1236 .L2253:

                    1237 	.data

                    1238 	.text

                    1239 

                    1240 ;336: }


                    1241 

                    1242 ;337: 


                    1243 ;338: unsigned int BerDecoder_DecodeUint32Old( unsigned char* pBuffer, int iBufPos, int iLength )


                    1244 	.align	4

                    1245 	.align	4

                    1246 BerDecoder_DecodeUint32Old::

00000774 e92d0070   1247 	stmfd	[sp]!,{r4-r6}

                    1248 ;339: {


                    1249 

00000778 e3a03000   1250 	mov	r3,0

                    1251 ;340: 


                    1252 ;341:     unsigned int Value = 0;


                    1253 

                    1254 ;342:     int i;


                    1255 ;343: 


                    1256 ;344:     for( i = 0; i < iLength; i++ )


                    1257 

0000077c e1a04003   1258 	mov	r4,r3

00000780 e3520000   1259 	cmp	r2,0

00000784 a1a05002   1260 	movge	r5,r2

00000788 b3a05000   1261 	movlt	r5,0

0000078c e1b0c1a5   1262 	movs	r12,r5 lsr 3

00000790 0a000013   1263 	beq	.L2350

00000794 e0812000   1264 	add	r2,r1,r0

00000798 e1a0418c   1265 	mov	r4,r12 lsl 3

                    1266 .L2366:

0000079c e4d26001   1267 	ldrb	r6,[r2],1

000007a0 e0863403   1268 	add	r3,r6,r3 lsl 8

000007a4 e4d26001   1269 	ldrb	r6,[r2],1

000007a8 e0863403   1270 	add	r3,r6,r3 lsl 8


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
000007ac e4d26001   1271 	ldrb	r6,[r2],1

000007b0 e0863403   1272 	add	r3,r6,r3 lsl 8

000007b4 e4d26001   1273 	ldrb	r6,[r2],1

000007b8 e0863403   1274 	add	r3,r6,r3 lsl 8

000007bc e4d26001   1275 	ldrb	r6,[r2],1

000007c0 e0863403   1276 	add	r3,r6,r3 lsl 8

000007c4 e4d26001   1277 	ldrb	r6,[r2],1

000007c8 e0863403   1278 	add	r3,r6,r3 lsl 8

000007cc e4d26001   1279 	ldrb	r6,[r2],1

000007d0 e0863403   1280 	add	r3,r6,r3 lsl 8

000007d4 e4d26001   1281 	ldrb	r6,[r2],1

000007d8 e25cc001   1282 	subs	r12,r12,1

000007dc e0863403   1283 	add	r3,r6,r3 lsl 8

000007e0 1affffed   1284 	bne	.L2366

                    1285 .L2350:

000007e4 e215c007   1286 	ands	r12,r5,7

000007e8 10800001   1287 	addne	r0,r0,r1

000007ec 10840000   1288 	addne	r0,r4,r0

                    1289 .L2370:

000007f0 14d01001   1290 	ldrneb	r1,[r0],1

000007f4 10813403   1291 	addne	r3,r1,r3 lsl 8

000007f8 125cc001   1292 	subnes	r12,r12,1

000007fc 1afffffb   1293 	bne	.L2370

                    1294 .L2323:

                    1295 ;349: 


                    1296 ;350:     }//for( i = 0; i < iLength; i++ )


                    1297 ;351: 


                    1298 ;352:     return Value;


                    1299 

00000800 e1a00003   1300 	mov	r0,r3

00000804 e8bd0070   1301 	ldmfd	[sp]!,{r4-r6}

00000808 e12fff1e*  1302 	ret	

                    1303 	.endf	BerDecoder_DecodeUint32Old

                    1304 	.align	4

                    1305 ;Value	r3	local

                    1306 ;i	r4	local

                    1307 

                    1308 ;pBuffer	r0	param

                    1309 ;iBufPos	r1	param

                    1310 ;iLength	r2	param

                    1311 

                    1312 	.section ".bss","awb"

                    1313 .L2529:

                    1314 	.data

                    1315 	.text

                    1316 

                    1317 ;353: }


                    1318 

                    1319 ;354: 


                    1320 ;355: int BerDecoder_DecodeObjectName(unsigned char* pBuffer, int bufPos, int iTotalLength,


                    1321 	.align	4

                    1322 	.align	4

                    1323 BerDecoder_DecodeObjectName::

0000080c e92d4cf0   1324 	stmfd	[sp]!,{r4-r7,r10-fp,lr}

                    1325 ;356:     unsigned char** pItemId, int* itemIdLen, unsigned char** pDomainId, int* domainLen)


                    1326 ;357: {


                    1327 

                    1328 ;358:     unsigned char   tag=0xff;


                    1329 

                    1330 ;359:     int             totalNameLength;


                    1331 ;360: 



                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                    1332 ;361:     tag = pBuffer[bufPos++];


                    1333 

00000810 e24dd00c   1334 	sub	sp,sp,12

00000814 e59d6028   1335 	ldr	r6,[sp,40]

00000818 e59d702c   1336 	ldr	r7,[sp,44]

0000081c e59da030   1337 	ldr	r10,[sp,48]

00000820 e1a0b003   1338 	mov	fp,r3

00000824 e1a04002   1339 	mov	r4,r2

00000828 e2812001   1340 	add	r2,r1,1

0000082c e7d01001   1341 	ldrb	r1,[r0,r1]

00000830 e1a03004   1342 	mov	r3,r4

00000834 e5cd1007   1343 	strb	r1,[sp,7]

                    1344 ;362:     bufPos = BerDecoder_decodeLength(pBuffer, &totalNameLength, bufPos, iTotalLength);


                    1345 

00000838 e28d1008   1346 	add	r1,sp,8

0000083c e1a05000   1347 	mov	r5,r0

00000840 ebfffdfe*  1348 	bl	BerDecoder_decodeLength

00000844 e1a01000   1349 	mov	r1,r0

                    1350 ;363: 


                    1351 ;364:     if (bufPos == -1)


                    1352 

00000848 e3710001   1353 	cmn	r1,1

0000084c 0a000012   1354 	beq	.L2565

                    1355 ;365:     {


                    1356 

                    1357 ;366:         return -1;


                    1358 

                    1359 ;367:     }


                    1360 ;368: 


                    1361 ;369:     switch(tag)


                    1362 

00000850 e5dd0007   1363 	ldrb	r0,[sp,7]

00000854 e35000a1   1364 	cmp	r0,161

00000858 1a00000f   1365 	bne	.L2565

                    1366 ;370:     {


                    1367 ;371:     case ASN_OBJECT_NAME_VMD_SPECIFIC:


                    1368 ;372:         break;


                    1369 ;373:     case ASN_OBJECT_NAME_DOMAIN_SPECIFIC:


                    1370 ;374:         //Домен


                    1371 ;375:         bufPos = BerDecoder_decodeString(pBuffer, bufPos, iTotalLength, pDomainId, domainLen);


                    1372 

0000085c e58da000   1373 	str	r10,[sp]

00000860 e1a03007   1374 	mov	r3,r7

00000864 e1a02004   1375 	mov	r2,r4

00000868 e1a00005   1376 	mov	r0,r5

0000086c ebfffed2*  1377 	bl	BerDecoder_decodeString

00000870 e1a01000   1378 	mov	r1,r0

                    1379 ;376:         if (bufPos == -1)


                    1380 

00000874 e3710001   1381 	cmn	r1,1

00000878 0a000007   1382 	beq	.L2565

                    1383 ;377:         {


                    1384 

                    1385 ;378:             break;


                    1386 

                    1387 ;379:         }


                    1388 ;380:         bufPos = BerDecoder_decodeString(pBuffer, bufPos, iTotalLength, pItemId, itemIdLen);


                    1389 

0000087c e58d6000   1390 	str	r6,[sp]

00000880 e1a0300b   1391 	mov	r3,fp

00000884 e1a02004   1392 	mov	r2,r4


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
00000888 e1a00005   1393 	mov	r0,r5

0000088c ebfffeca*  1394 	bl	BerDecoder_decodeString

                    1395 ;381:         if (bufPos == -1)


                    1396 

                    1397 

                    1398 

                    1399 

                    1400 

00000890 e3700001   1401 	cmn	r0,1

00000894 03e00000   1402 	mvneq	r0,0

00000898 ea000000   1403 	b	.L2554

                    1404 .L2565:

                    1405 ;386:     case ASN_OBJECT_NAME_AA_SPECIFIC:


                    1406 ;387:         break;


                    1407 ;388:     default:


                    1408 ;389:         break;


                    1409 ;390:     }


                    1410 ;391:     return -1;


                    1411 

0000089c e3e00000   1412 	mvn	r0,0

                    1413 .L2554:

000008a0 e28dd00c   1414 	add	sp,sp,12

000008a4 e8bd8cf0   1415 	ldmfd	[sp]!,{r4-r7,r10-fp,pc}

                    1416 	.endf	BerDecoder_DecodeObjectName

                    1417 	.align	4

                    1418 ;tag	[sp,7]	local

                    1419 ;totalNameLength	[sp,8]	local

                    1420 

                    1421 ;pBuffer	r5	param

                    1422 ;bufPos	r1	param

                    1423 ;iTotalLength	r4	param

                    1424 ;pItemId	fp	param

                    1425 ;itemIdLen	r6	param

                    1426 ;pDomainId	r7	param

                    1427 ;domainLen	r10	param

                    1428 

                    1429 	.section ".bss","awb"

                    1430 .L2640:

                    1431 	.data

                    1432 	.text

                    1433 

                    1434 ;392: }


                    1435 

                    1436 ;393: 


                    1437 ;394: int BerDecoder_DecodeObjectNameToStringView(unsigned char* pBuffer, int bufPos,


                    1438 	.align	4

                    1439 	.align	4

                    1440 BerDecoder_DecodeObjectNameToStringView::

000008a8 e92d4000   1441 	stmfd	[sp]!,{lr}

                    1442 ;395:     int iTotalLength, StringView* domainId, StringView* itemId)


                    1443 ;396: {


                    1444 

                    1445 ;397:     return BerDecoder_DecodeObjectName(pBuffer, bufPos, iTotalLength,


                    1446 

000008ac e24dd00c   1447 	sub	sp,sp,12

000008b0 e59dc010   1448 	ldr	r12,[sp,16]

000008b4 e1a0e003   1449 	mov	lr,r3

000008b8 e2833004   1450 	add	r3,r3,4

000008bc e98d4008   1451 	stmfa	[sp],{r3,lr}

000008c0 e58dc000   1452 	str	r12,[sp]

000008c4 e28c3004   1453 	add	r3,r12,4


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
000008c8 ebffffcf*  1454 	bl	BerDecoder_DecodeObjectName

000008cc e28dd00c   1455 	add	sp,sp,12

000008d0 e8bd8000   1456 	ldmfd	[sp]!,{pc}

                    1457 	.endf	BerDecoder_DecodeObjectNameToStringView

                    1458 	.align	4

                    1459 

                    1460 ;pBuffer	none	param

                    1461 ;bufPos	none	param

                    1462 ;iTotalLength	none	param

                    1463 ;domainId	r3	param

                    1464 ;itemId	r12	param

                    1465 

                    1466 	.section ".bss","awb"

                    1467 .L2689:

                    1468 	.data

                    1469 	.text

                    1470 

                    1471 ;398:         (uint8_t**)&itemId->p, (int*)&itemId->len,


                    1472 ;399:         (uint8_t**)&domainId->p, (int*)&domainId->len);


                    1473 ;400: }


                    1474 

                    1475 ;401: 


                    1476 ;402: 


                    1477 ;403: int BerEncoder_determineLengthSize( unsigned int uLength )


                    1478 	.align	4

                    1479 	.align	4

                    1480 BerEncoder_determineLengthSize::

                    1481 ;404: {


                    1482 

                    1483 ;405:     if( uLength < 128 )


                    1484 

000008d4 e3500080   1485 	cmp	r0,128

                    1486 ;406:     {


                    1487 

                    1488 ;407:         return 1;


                    1489 

000008d8 33a00001   1490 	movlo	r0,1

000008dc 3a000002   1491 	blo	.L2696

                    1492 ;408:     }


                    1493 ;409: 


                    1494 ;410:     if( uLength < 256 )


                    1495 

                    1496 

                    1497 

                    1498 

                    1499 

000008e0 e3500f40   1500 	cmp	r0,256

000008e4 23a00003   1501 	movhs	r0,3

000008e8 33a00002   1502 	movlo	r0,2

                    1503 .L2696:

000008ec e12fff1e*  1504 	ret	

                    1505 	.endf	BerEncoder_determineLengthSize

                    1506 	.align	4

                    1507 

                    1508 ;uLength	r0	param

                    1509 

                    1510 	.section ".bss","awb"

                    1511 .L2748:

                    1512 	.data

                    1513 	.text

                    1514 


                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                    1515 ;417:     }


                    1516 ;418: }


                    1517 

                    1518 ;419: 


                    1519 ;420: int BerEncoder_determineFullObjectSize(unsigned int uLength)


                    1520 	.align	4

                    1521 	.align	4

                    1522 BerEncoder_determineFullObjectSize::

000008f0 e92d4010   1523 	stmfd	[sp]!,{r4,lr}

                    1524 ;421: {


                    1525 

                    1526 ;422:     return 1 + BerEncoder_determineLengthSize(uLength)


                    1527 

000008f4 e1a04000   1528 	mov	r4,r0

000008f8 ebfffff5*  1529 	bl	BerEncoder_determineLengthSize

000008fc e2800001   1530 	add	r0,r0,1

00000900 e0840000   1531 	add	r0,r4,r0

00000904 e8bd8010   1532 	ldmfd	[sp]!,{r4,pc}

                    1533 	.endf	BerEncoder_determineFullObjectSize

                    1534 	.align	4

                    1535 

                    1536 ;uLength	r4	param

                    1537 

                    1538 	.section ".bss","awb"

                    1539 .L2782:

                    1540 	.data

                    1541 	.text

                    1542 

                    1543 ;423:         + uLength;


                    1544 ;424: }


                    1545 

                    1546 ;425: 


                    1547 ;426: 


                    1548 ;427: int BerEncoder_UInt32determineEncodedSize( unsigned int iValue )


                    1549 	.align	4

                    1550 	.align	4

                    1551 BerEncoder_UInt32determineEncodedSize::

00000908 e92d4000   1552 	stmfd	[sp]!,{lr}

0000090c e24dd00c   1553 	sub	sp,sp,12

00000910 e58d0000   1554 	str	r0,[sp]

00000914 e3a01000   1555 	mov	r1,0

00000918 e5cd1004   1556 	strb	r1,[sp,4]

0000091c e5dd1001   1557 	ldrb	r1,[sp,1]

00000920 e5cd1006   1558 	strb	r1,[sp,6]

00000924 e5dd1002   1559 	ldrb	r1,[sp,2]

00000928 e5cd0005   1560 	strb	r0,[sp,5]

0000092c e5cd1007   1561 	strb	r1,[sp,7]

00000930 e5dd1003   1562 	ldrb	r1,[sp,3]

00000934 e28d0005   1563 	add	r0,sp,5

00000938 e5cd1008   1564 	strb	r1,[sp,8]

                    1565 ;428: {


                    1566 

                    1567 ;429: 


                    1568 ;430:     unsigned char* pValueArray = (unsigned char*)&iValue;


                    1569 

                    1570 ;431:     unsigned char ValueBuffer[5];


                    1571 ;432: 


                    1572 ;433:     int i;


                    1573 ;434:     int iSize;


                    1574 ;435: 


                    1575 ;436:     ValueBuffer[0] = 0;



                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                    1576 

                    1577 ;437: 


                    1578 ;438: 


                    1579 ;439:     for( i = 0; i < 4; i++ )


                    1580 

                    1581 ;443: 


                    1582 ;444:     }//for( i = 0; i < 4; i++ )


                    1583 ;445: 


                    1584 ;446:     //если ORDER_LITTLE_ENDIAN


                    1585 ;447:     BerEncoder_RevertByteOrder( ValueBuffer + 1, 4 );


                    1586 

0000093c e3a01004   1587 	mov	r1,4

00000940 eb000319*  1588 	bl	BerEncoder_RevertByteOrder

                    1589 ;448:     //если ORDER_LITTLE_ENDIAN


                    1590 ;449:     iSize = BerEncoder_CompressInteger( ValueBuffer, 5 );


                    1591 

00000944 e28d0004   1592 	add	r0,sp,4

00000948 e3a01005   1593 	mov	r1,5

0000094c eb000017*  1594 	bl	BerEncoder_CompressInteger

                    1595 ;450:     return iSize;


                    1596 

00000950 e28dd00c   1597 	add	sp,sp,12

00000954 e8bd8000   1598 	ldmfd	[sp]!,{pc}

                    1599 	.endf	BerEncoder_UInt32determineEncodedSize

                    1600 	.align	4

                    1601 ;ValueBuffer	[sp,4]	local

                    1602 

                    1603 ;iValue	[sp]	param

                    1604 

                    1605 	.section ".bss","awb"

                    1606 .L2840:

                    1607 	.data

                    1608 	.text

                    1609 

                    1610 ;451: }


                    1611 

                    1612 ;452: size_t BerEncoder_uint32determineEncodedSizeTL(uint32_t value)


                    1613 	.align	4

                    1614 	.align	4

                    1615 BerEncoder_uint32determineEncodedSizeTL::

00000958 e92d4000   1616 	stmfd	[sp]!,{lr}

                    1617 ;453: {


                    1618 

                    1619 ;454:     return BerEncoder_UInt32determineEncodedSize(value) + 2;//+ размер и тэг


                    1620 

0000095c ebffffe9*  1621 	bl	BerEncoder_UInt32determineEncodedSize

00000960 e2800002   1622 	add	r0,r0,2

00000964 e8bd8000   1623 	ldmfd	[sp]!,{pc}

                    1624 	.endf	BerEncoder_uint32determineEncodedSizeTL

                    1625 	.align	4

                    1626 

                    1627 ;value	none	param

                    1628 

                    1629 	.section ".bss","awb"

                    1630 .L2878:

                    1631 	.data

                    1632 	.text

                    1633 

                    1634 ;455: }


                    1635 

                    1636 ;456: 



                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                    1637 ;457: 


                    1638 ;458: int BerEncoder_Int32DetermineEncodedSize(int iValue)


                    1639 	.align	4

                    1640 	.align	4

                    1641 BerEncoder_Int32DetermineEncodedSize::

00000968 e92d4000   1642 	stmfd	[sp]!,{lr}

0000096c e24dd008   1643 	sub	sp,sp,8

00000970 e58d0004   1644 	str	r0,[sp,4]

00000974 e5dd1005   1645 	ldrb	r1,[sp,5]

00000978 e5cd1001   1646 	strb	r1,[sp,1]

0000097c e5dd1006   1647 	ldrb	r1,[sp,6]

00000980 e5cd0000   1648 	strb	r0,[sp]

00000984 e5cd1002   1649 	strb	r1,[sp,2]

00000988 e5dd1007   1650 	ldrb	r1,[sp,7]

0000098c e1a0000d   1651 	mov	r0,sp

00000990 e5cd1003   1652 	strb	r1,[sp,3]

                    1653 ;459: {


                    1654 

                    1655 ;460:     unsigned char* pValueArray = (unsigned char*)&iValue;


                    1656 

                    1657 ;461:     unsigned char ValueBuffer[4];


                    1658 ;462: 


                    1659 ;463:     int i;


                    1660 ;464:     int iSize;


                    1661 ;465: 


                    1662 ;466: 


                    1663 ;467:     for (i = 0; i < 4; i++)


                    1664 

                    1665 ;470:     }


                    1666 ;471:      //если ORDER_LITTLE_ENDIAN


                    1667 ;472:     BerEncoder_RevertByteOrder(ValueBuffer, 4);


                    1668 

00000994 e3a01004   1669 	mov	r1,4

00000998 eb000303*  1670 	bl	BerEncoder_RevertByteOrder

                    1671 ;473:     //если ORDER_LITTLE_ENDIAN


                    1672 ;474:     iSize = BerEncoder_CompressInteger(ValueBuffer, 4);


                    1673 

0000099c e1a0000d   1674 	mov	r0,sp

000009a0 e3a01004   1675 	mov	r1,4

000009a4 eb000001*  1676 	bl	BerEncoder_CompressInteger

                    1677 ;475:     return iSize;


                    1678 

000009a8 e28dd008   1679 	add	sp,sp,8

000009ac e8bd8000   1680 	ldmfd	[sp]!,{pc}

                    1681 	.endf	BerEncoder_Int32DetermineEncodedSize

                    1682 	.align	4

                    1683 ;ValueBuffer	[sp]	local

                    1684 

                    1685 ;iValue	[sp,4]	param

                    1686 

                    1687 	.section ".bss","awb"

                    1688 .L2943:

                    1689 	.data

                    1690 	.text

                    1691 

                    1692 ;476: }


                    1693 

                    1694 ;477: 


                    1695 ;478: int BerEncoder_CompressInteger( unsigned char* pInteger, int iOriginalSize )


                    1696 	.align	4

                    1697 	.align	4


                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                    1698 BerEncoder_CompressInteger::

000009b0 e92d0010   1699 	stmfd	[sp]!,{r4}

000009b4 e1a02000   1700 	mov	r2,r0

                    1701 ;479: {


                    1702 

                    1703 ;480: 


                    1704 ;481:     unsigned char* pIntegerEnd = pInteger + iOriginalSize - 1;


                    1705 

000009b8 e1a03002   1706 	mov	r3,r2

000009bc e0810002   1707 	add	r0,r1,r2

000009c0 e2400001   1708 	sub	r0,r0,1

                    1709 ;482:     unsigned char* pBytePosition;


                    1710 ;483: 


                    1711 ;484:     int iBytesToDelete;


                    1712 ;485:     int iNewSize;


                    1713 ;486: 


                    1714 ;487:     for( pBytePosition = pInteger; pBytePosition < pIntegerEnd; pBytePosition++ )


                    1715 

000009c4 e050c002   1716 	subs	r12,r0,r2

000009c8 43a0c000   1717 	movmi	r12,0

000009cc e1b001ac   1718 	movs	r0,r12 lsr 3

000009d0 0a000062   1719 	beq	.L3006

                    1720 .L3007:

000009d4 e5d34000   1721 	ldrb	r4,[r3]

000009d8 e3540000   1722 	cmp	r4,0

000009dc 1a000003   1723 	bne	.L3009

000009e0 e5d34001   1724 	ldrb	r4,[r3,1]

000009e4 e3140080   1725 	tst	r4,128

000009e8 1a00006d   1726 	bne	.L2968

000009ec ea000004   1727 	b	.L3013

                    1728 .L3009:

000009f0 e35400ff   1729 	cmp	r4,255

000009f4 1a00006a   1730 	bne	.L2968

000009f8 e5d34001   1731 	ldrb	r4,[r3,1]

000009fc e3140080   1732 	tst	r4,128

00000a00 0a000067   1733 	beq	.L2968

                    1734 .L3013:

00000a04 e5f34001   1735 	ldrb	r4,[r3,1]!

00000a08 e3540000   1736 	cmp	r4,0

00000a0c 1a000003   1737 	bne	.L3015

00000a10 e5d34001   1738 	ldrb	r4,[r3,1]

00000a14 e3140080   1739 	tst	r4,128

00000a18 1a000061   1740 	bne	.L2968

00000a1c ea000004   1741 	b	.L3019

                    1742 .L3015:

00000a20 e35400ff   1743 	cmp	r4,255

00000a24 1a00005e   1744 	bne	.L2968

00000a28 e5d34001   1745 	ldrb	r4,[r3,1]

00000a2c e3140080   1746 	tst	r4,128

00000a30 0a00005b   1747 	beq	.L2968

                    1748 .L3019:

00000a34 e5f34001   1749 	ldrb	r4,[r3,1]!

00000a38 e3540000   1750 	cmp	r4,0

00000a3c 1a000003   1751 	bne	.L3021

00000a40 e5d34001   1752 	ldrb	r4,[r3,1]

00000a44 e3140080   1753 	tst	r4,128

00000a48 1a000055   1754 	bne	.L2968

00000a4c ea000004   1755 	b	.L3025

                    1756 .L3021:

00000a50 e35400ff   1757 	cmp	r4,255

00000a54 1a000052   1758 	bne	.L2968


                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
00000a58 e5d34001   1759 	ldrb	r4,[r3,1]

00000a5c e3140080   1760 	tst	r4,128

00000a60 0a00004f   1761 	beq	.L2968

                    1762 .L3025:

00000a64 e5f34001   1763 	ldrb	r4,[r3,1]!

00000a68 e3540000   1764 	cmp	r4,0

00000a6c 1a000003   1765 	bne	.L3027

00000a70 e5d34001   1766 	ldrb	r4,[r3,1]

00000a74 e3140080   1767 	tst	r4,128

00000a78 1a000049   1768 	bne	.L2968

00000a7c ea000004   1769 	b	.L3031

                    1770 .L3027:

00000a80 e35400ff   1771 	cmp	r4,255

00000a84 1a000046   1772 	bne	.L2968

00000a88 e5d34001   1773 	ldrb	r4,[r3,1]

00000a8c e3140080   1774 	tst	r4,128

00000a90 0a000043   1775 	beq	.L2968

                    1776 .L3031:

00000a94 e5f34001   1777 	ldrb	r4,[r3,1]!

00000a98 e3540000   1778 	cmp	r4,0

00000a9c 1a000003   1779 	bne	.L3033

00000aa0 e5d34001   1780 	ldrb	r4,[r3,1]

00000aa4 e3140080   1781 	tst	r4,128

00000aa8 1a00003d   1782 	bne	.L2968

00000aac ea000004   1783 	b	.L3037

                    1784 .L3033:

00000ab0 e35400ff   1785 	cmp	r4,255

00000ab4 1a00003a   1786 	bne	.L2968

00000ab8 e5d34001   1787 	ldrb	r4,[r3,1]

00000abc e3140080   1788 	tst	r4,128

00000ac0 0a000037   1789 	beq	.L2968

                    1790 .L3037:

00000ac4 e5f34001   1791 	ldrb	r4,[r3,1]!

00000ac8 e3540000   1792 	cmp	r4,0

00000acc 1a000003   1793 	bne	.L3039

00000ad0 e5d34001   1794 	ldrb	r4,[r3,1]

00000ad4 e3140080   1795 	tst	r4,128

00000ad8 1a000031   1796 	bne	.L2968

00000adc ea000004   1797 	b	.L3043

                    1798 .L3039:

00000ae0 e35400ff   1799 	cmp	r4,255

00000ae4 1a00002e   1800 	bne	.L2968

00000ae8 e5d34001   1801 	ldrb	r4,[r3,1]

00000aec e3140080   1802 	tst	r4,128

00000af0 0a00002b   1803 	beq	.L2968

                    1804 .L3043:

00000af4 e5f34001   1805 	ldrb	r4,[r3,1]!

00000af8 e3540000   1806 	cmp	r4,0

00000afc 1a000003   1807 	bne	.L3045

00000b00 e5d34001   1808 	ldrb	r4,[r3,1]

00000b04 e3140080   1809 	tst	r4,128

00000b08 1a000025   1810 	bne	.L2968

00000b0c ea000004   1811 	b	.L3049

                    1812 .L3045:

00000b10 e35400ff   1813 	cmp	r4,255

00000b14 1a000022   1814 	bne	.L2968

00000b18 e5d34001   1815 	ldrb	r4,[r3,1]

00000b1c e3140080   1816 	tst	r4,128

00000b20 0a00001f   1817 	beq	.L2968

                    1818 .L3049:

00000b24 e5f34001   1819 	ldrb	r4,[r3,1]!


                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
00000b28 e3540000   1820 	cmp	r4,0

00000b2c 1a000003   1821 	bne	.L3051

00000b30 e5d34001   1822 	ldrb	r4,[r3,1]

00000b34 e3140080   1823 	tst	r4,128

00000b38 1a000019   1824 	bne	.L2968

00000b3c ea000004   1825 	b	.L3054

                    1826 .L3051:

00000b40 e35400ff   1827 	cmp	r4,255

00000b44 1a000016   1828 	bne	.L2968

00000b48 e5d34001   1829 	ldrb	r4,[r3,1]

00000b4c e3140080   1830 	tst	r4,128

00000b50 0a000013   1831 	beq	.L2968

                    1832 .L3054:

00000b54 e2833001   1833 	add	r3,r3,1

00000b58 e2500001   1834 	subs	r0,r0,1

00000b5c 1affff9c   1835 	bne	.L3007

                    1836 .L3006:

00000b60 e21c0007   1837 	ands	r0,r12,7

00000b64 0a00000e   1838 	beq	.L2968

                    1839 .L3057:

00000b68 e5d34000   1840 	ldrb	r4,[r3]

00000b6c e3540000   1841 	cmp	r4,0

00000b70 1a000003   1842 	bne	.L3059

00000b74 e5d3c001   1843 	ldrb	r12,[r3,1]

00000b78 e31c0080   1844 	tst	r12,128

00000b7c 1a000008   1845 	bne	.L2968

00000b80 ea000004   1846 	b	.L3062

                    1847 .L3059:

00000b84 e35400ff   1848 	cmp	r4,255

00000b88 1a000005   1849 	bne	.L2968

00000b8c e5d3c001   1850 	ldrb	r12,[r3,1]

00000b90 e31c0080   1851 	tst	r12,128

00000b94 0a000002   1852 	beq	.L2968

                    1853 .L3062:

00000b98 e2833001   1854 	add	r3,r3,1

00000b9c e2500001   1855 	subs	r0,r0,1

00000ba0 1afffff0   1856 	bne	.L3057

                    1857 .L2968:

                    1858 ;510: 


                    1859 ;511:     }


                    1860 ;512: 


                    1861 ;513:     iBytesToDelete = pBytePosition - pInteger;


                    1862 

00000ba4 e053c002   1863 	subs	r12,r3,r2

                    1864 ;514:     iNewSize = iOriginalSize;


                    1865 

00000ba8 e1a00001   1866 	mov	r0,r1

                    1867 ;515: 


                    1868 ;516: 


                    1869 ;517:     if( iBytesToDelete )


                    1870 

00000bac 0a00001d   1871 	beq	.L2950

                    1872 ;518:     {


                    1873 

                    1874 ;519:         unsigned char* pNewEnd;


                    1875 ;520:         unsigned char* pNewBytePosition;


                    1876 ;521:         iNewSize -= iBytesToDelete;


                    1877 

00000bb0 e1a01002   1878 	mov	r1,r2

00000bb4 e040000c   1879 	sub	r0,r0,r12

                    1880 ;522:         pNewEnd = pInteger + iNewSize;



                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                    1881 

00000bb8 e080c002   1882 	add	r12,r0,r2

                    1883 ;523: 


                    1884 ;524:         for( pNewBytePosition = pInteger; pNewBytePosition < pNewEnd; pNewBytePosition++ )


                    1885 

00000bbc e05cc002   1886 	subs	r12,r12,r2

00000bc0 43a0c000   1887 	movmi	r12,0

00000bc4 e1b021ac   1888 	movs	r2,r12 lsr 3

00000bc8 0a000011   1889 	beq	.L3065

                    1890 .L3081:

00000bcc e4d34001   1891 	ldrb	r4,[r3],1

00000bd0 e4c14001   1892 	strb	r4,[r1],1

00000bd4 e4d34001   1893 	ldrb	r4,[r3],1

00000bd8 e4c14001   1894 	strb	r4,[r1],1

00000bdc e4d34001   1895 	ldrb	r4,[r3],1

00000be0 e4c14001   1896 	strb	r4,[r1],1

00000be4 e4d34001   1897 	ldrb	r4,[r3],1

00000be8 e4c14001   1898 	strb	r4,[r1],1

00000bec e4d34001   1899 	ldrb	r4,[r3],1

00000bf0 e4c14001   1900 	strb	r4,[r1],1

00000bf4 e4d34001   1901 	ldrb	r4,[r3],1

00000bf8 e4c14001   1902 	strb	r4,[r1],1

00000bfc e4d34001   1903 	ldrb	r4,[r3],1

00000c00 e4c14001   1904 	strb	r4,[r1],1

00000c04 e4d34001   1905 	ldrb	r4,[r3],1

00000c08 e2522001   1906 	subs	r2,r2,1

00000c0c e4c14001   1907 	strb	r4,[r1],1

00000c10 1affffed   1908 	bne	.L3081

                    1909 .L3065:

00000c14 e21c2007   1910 	ands	r2,r12,7

                    1911 .L3085:

00000c18 14d3c001   1912 	ldrneb	r12,[r3],1

00000c1c 14c1c001   1913 	strneb	r12,[r1],1

00000c20 12522001   1914 	subnes	r2,r2,1

00000c24 1afffffb   1915 	bne	.L3085

                    1916 .L2970:

                    1917 ;529: 


                    1918 ;530:         }//for( pNewBytePosition = pInteger; pNewBytePosition < pNewEnd; pNewBytePosition++ )


                    1919 ;531: 


                    1920 ;532: 


                    1921 ;533:     }//if( iBytesToDelete )


                    1922 ;534: 


                    1923 ;535:     return iNewSize;


                    1924 

                    1925 .L2950:

00000c28 e8bd0010   1926 	ldmfd	[sp]!,{r4}

00000c2c e12fff1e*  1927 	ret	

                    1928 	.endf	BerEncoder_CompressInteger

                    1929 	.align	4

                    1930 ;pIntegerEnd	r0	local

                    1931 ;pBytePosition	r3	local

                    1932 ;iBytesToDelete	r12	local

                    1933 ;iNewSize	r0	local

                    1934 ;pNewEnd	r12	local

                    1935 ;pNewBytePosition	r1	local

                    1936 

                    1937 ;pInteger	r2	param

                    1938 ;iOriginalSize	r1	param

                    1939 

                    1940 	.section ".bss","awb"

                    1941 .L3692:


                                                                      Page 33
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                    1942 	.data

                    1943 	.text

                    1944 

                    1945 ;536: 


                    1946 ;537: }


                    1947 

                    1948 ;538: 


                    1949 ;539: int BerEncoder_encodeUInt32( unsigned int iValue, unsigned char* pBuffer, int iBufPos )


                    1950 	.align	4

                    1951 	.align	4

                    1952 BerEncoder_encodeUInt32::

00000c30 e92d4070   1953 	stmfd	[sp]!,{r4-r6,lr}

00000c34 e1a04002   1954 	mov	r4,r2

00000c38 e3a06000   1955 	mov	r6,0

00000c3c e24dd00c   1956 	sub	sp,sp,12

00000c40 e58d0000   1957 	str	r0,[sp]

00000c44 e1a05001   1958 	mov	r5,r1

00000c48 e5dd1001   1959 	ldrb	r1,[sp,1]

00000c4c e5cd6004   1960 	strb	r6,[sp,4]

00000c50 e5cd1006   1961 	strb	r1,[sp,6]

00000c54 e5dd1002   1962 	ldrb	r1,[sp,2]

00000c58 e5cd0005   1963 	strb	r0,[sp,5]

00000c5c e5cd1007   1964 	strb	r1,[sp,7]

00000c60 e5dd1003   1965 	ldrb	r1,[sp,3]

00000c64 e28d0005   1966 	add	r0,sp,5

00000c68 e5cd1008   1967 	strb	r1,[sp,8]

                    1968 ;540: {


                    1969 

                    1970 ;541: 


                    1971 ;542:     unsigned char* pValueArray = (unsigned char*)&iValue;


                    1972 

                    1973 ;543:     unsigned char ValueBuffer[5];


                    1974 ;544: 


                    1975 ;545:     int i;


                    1976 ;546:     int iSize;


                    1977 ;547: 


                    1978 ;548:     ValueBuffer[0] = 0;


                    1979 

                    1980 ;549: 


                    1981 ;550: 


                    1982 ;551:     for( i = 0; i < 4; i++ )


                    1983 

                    1984 ;555: 


                    1985 ;556:     }//for( i = 0; i < 4; i++ )


                    1986 ;557: 


                    1987 ;558:     //если ORDER_LITTLE_ENDIAN


                    1988 ;559:     BerEncoder_RevertByteOrder( ValueBuffer + 1, 4 );


                    1989 

00000c6c e3a01004   1990 	mov	r1,4

00000c70 eb00024d*  1991 	bl	BerEncoder_RevertByteOrder

                    1992 ;560:     //если ORDER_LITTLE_ENDIAN


                    1993 ;561: 


                    1994 ;562:     iSize = BerEncoder_CompressInteger( ValueBuffer, 5 );


                    1995 

00000c74 e28d0004   1996 	add	r0,sp,4

00000c78 e3a01005   1997 	mov	r1,5

00000c7c ebffff4b*  1998 	bl	BerEncoder_CompressInteger

                    1999 ;563: 


                    2000 ;564:     for( i = 0; i < iSize; i++ )


                    2001 

00000c80 e3500000   2002 	cmp	r0,0


                                                                      Page 34
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
00000c84 a1a03000   2003 	movge	r3,r0

00000c88 b3a03000   2004 	movlt	r3,0

00000c8c e1b021a3   2005 	movs	r2,r3 lsr 3

00000c90 0a00001b   2006 	beq	.L3874

00000c94 e28d1004   2007 	add	r1,sp,4

00000c98 e1a06182   2008 	mov	r6,r2 lsl 3

                    2009 .L3890:

00000c9c e4d1c001   2010 	ldrb	r12,[r1],1

00000ca0 e2522001   2011 	subs	r2,r2,1

00000ca4 e7c5c004   2012 	strb	r12,[r5,r4]

00000ca8 e4d1c001   2013 	ldrb	r12,[r1],1

00000cac e2844001   2014 	add	r4,r4,1

00000cb0 e7c5c004   2015 	strb	r12,[r5,r4]

00000cb4 e4d1c001   2016 	ldrb	r12,[r1],1

00000cb8 e2844001   2017 	add	r4,r4,1

00000cbc e7c5c004   2018 	strb	r12,[r5,r4]

00000cc0 e4d1c001   2019 	ldrb	r12,[r1],1

00000cc4 e2844001   2020 	add	r4,r4,1

00000cc8 e7c5c004   2021 	strb	r12,[r5,r4]

00000ccc e4d1c001   2022 	ldrb	r12,[r1],1

00000cd0 e2844001   2023 	add	r4,r4,1

00000cd4 e7c5c004   2024 	strb	r12,[r5,r4]

00000cd8 e4d1c001   2025 	ldrb	r12,[r1],1

00000cdc e2844001   2026 	add	r4,r4,1

00000ce0 e7c5c004   2027 	strb	r12,[r5,r4]

00000ce4 e4d1c001   2028 	ldrb	r12,[r1],1

00000ce8 e2844001   2029 	add	r4,r4,1

00000cec e7c5c004   2030 	strb	r12,[r5,r4]

00000cf0 e4d1c001   2031 	ldrb	r12,[r1],1

00000cf4 e2844001   2032 	add	r4,r4,1

00000cf8 e7c5c004   2033 	strb	r12,[r5,r4]

00000cfc e2844001   2034 	add	r4,r4,1

00000d00 1affffe5   2035 	bne	.L3890

                    2036 .L3874:

00000d04 e2132007   2037 	ands	r2,r3,7

00000d08 108d1006   2038 	addne	r1,sp,r6

00000d0c 12811004   2039 	addne	r1,r1,4

                    2040 .L3894:

00000d10 14d13001   2041 	ldrneb	r3,[r1],1

00000d14 17c53004   2042 	strneb	r3,[r5,r4]

00000d18 12844001   2043 	addne	r4,r4,1

00000d1c 12522001   2044 	subnes	r2,r2,1

00000d20 1afffffa   2045 	bne	.L3894

                    2046 .L3849:

                    2047 ;568: 


                    2048 ;569:     }


                    2049 ;570:     return iBufPos;


                    2050 

00000d24 e1a00004   2051 	mov	r0,r4

00000d28 e28dd00c   2052 	add	sp,sp,12

00000d2c e8bd8070   2053 	ldmfd	[sp]!,{r4-r6,pc}

                    2054 	.endf	BerEncoder_encodeUInt32

                    2055 	.align	4

                    2056 ;ValueBuffer	[sp,4]	local

                    2057 ;i	r6	local

                    2058 

                    2059 ;iValue	[sp]	param

                    2060 ;pBuffer	r5	param

                    2061 ;iBufPos	r4	param

                    2062 

                    2063 	.section ".bss","awb"


                                                                      Page 35
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                    2064 .L4087:

                    2065 	.data

                    2066 	.text

                    2067 

                    2068 ;571: }


                    2069 

                    2070 ;572: 


                    2071 ;573: int BerEncoder_EncodeInt32(int iValue, unsigned char* pBuffer, int iBufPos)


                    2072 	.align	4

                    2073 	.align	4

                    2074 BerEncoder_EncodeInt32::

00000d30 e92d4030   2075 	stmfd	[sp]!,{r4-r5,lr}

00000d34 e24dd008   2076 	sub	sp,sp,8

00000d38 e58d0004   2077 	str	r0,[sp,4]

00000d3c e1a05001   2078 	mov	r5,r1

00000d40 e5dd1005   2079 	ldrb	r1,[sp,5]

00000d44 e1a04002   2080 	mov	r4,r2

00000d48 e5cd1001   2081 	strb	r1,[sp,1]

00000d4c e5dd1006   2082 	ldrb	r1,[sp,6]

00000d50 e5cd0000   2083 	strb	r0,[sp]

00000d54 e5cd1002   2084 	strb	r1,[sp,2]

00000d58 e5dd1007   2085 	ldrb	r1,[sp,7]

00000d5c e1a0000d   2086 	mov	r0,sp

00000d60 e5cd1003   2087 	strb	r1,[sp,3]

                    2088 ;574: {


                    2089 

                    2090 ;575:     unsigned char* pValueArray = (unsigned char*)&iValue;


                    2091 

                    2092 ;576:     unsigned char ValueBuffer[4];


                    2093 ;577: 


                    2094 ;578:     int i;


                    2095 ;579:     int iSize;


                    2096 ;580: 


                    2097 ;581:     for (i = 0; i < 4; i++)


                    2098 

                    2099 ;584:     }


                    2100 ;585:      //если ORDER_LITTLE_ENDIAN


                    2101 ;586:     BerEncoder_RevertByteOrder(ValueBuffer, 4);


                    2102 

00000d64 e3a01004   2103 	mov	r1,4

00000d68 eb00020f*  2104 	bl	BerEncoder_RevertByteOrder

                    2105 ;587:     //если ORDER_LITTLE_ENDIAN


                    2106 ;588:     iSize = BerEncoder_CompressInteger(ValueBuffer, 4);


                    2107 

00000d6c e1a0000d   2108 	mov	r0,sp

00000d70 e3a01004   2109 	mov	r1,4

00000d74 ebffff0d*  2110 	bl	BerEncoder_CompressInteger

                    2111 ;589:     for (i = 0; i < iSize; i++)


                    2112 

00000d78 e3a03000   2113 	mov	r3,0

00000d7c e3500000   2114 	cmp	r0,0

00000d80 a1a0c000   2115 	movge	r12,r0

00000d84 b3a0c000   2116 	movlt	r12,0

00000d88 e1b021ac   2117 	movs	r2,r12 lsr 3

00000d8c 0a00001b   2118 	beq	.L4146

00000d90 e1a0100d   2119 	mov	r1,sp

00000d94 e1a03182   2120 	mov	r3,r2 lsl 3

                    2121 .L4162:

00000d98 e4d10001   2122 	ldrb	r0,[r1],1

00000d9c e2522001   2123 	subs	r2,r2,1

00000da0 e7c50004   2124 	strb	r0,[r5,r4]


                                                                      Page 36
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
00000da4 e4d10001   2125 	ldrb	r0,[r1],1

00000da8 e2844001   2126 	add	r4,r4,1

00000dac e7c50004   2127 	strb	r0,[r5,r4]

00000db0 e4d10001   2128 	ldrb	r0,[r1],1

00000db4 e2844001   2129 	add	r4,r4,1

00000db8 e7c50004   2130 	strb	r0,[r5,r4]

00000dbc e4d10001   2131 	ldrb	r0,[r1],1

00000dc0 e2844001   2132 	add	r4,r4,1

00000dc4 e7c50004   2133 	strb	r0,[r5,r4]

00000dc8 e4d10001   2134 	ldrb	r0,[r1],1

00000dcc e2844001   2135 	add	r4,r4,1

00000dd0 e7c50004   2136 	strb	r0,[r5,r4]

00000dd4 e4d10001   2137 	ldrb	r0,[r1],1

00000dd8 e2844001   2138 	add	r4,r4,1

00000ddc e7c50004   2139 	strb	r0,[r5,r4]

00000de0 e4d10001   2140 	ldrb	r0,[r1],1

00000de4 e2844001   2141 	add	r4,r4,1

00000de8 e7c50004   2142 	strb	r0,[r5,r4]

00000dec e4d10001   2143 	ldrb	r0,[r1],1

00000df0 e2844001   2144 	add	r4,r4,1

00000df4 e7c50004   2145 	strb	r0,[r5,r4]

00000df8 e2844001   2146 	add	r4,r4,1

00000dfc 1affffe5   2147 	bne	.L4162

                    2148 .L4146:

00000e00 e21c2007   2149 	ands	r2,r12,7

00000e04 1083100d   2150 	addne	r1,r3,sp

                    2151 .L4166:

00000e08 14d13001   2152 	ldrneb	r3,[r1],1

00000e0c 17c53004   2153 	strneb	r3,[r5,r4]

00000e10 12844001   2154 	addne	r4,r4,1

00000e14 12522001   2155 	subnes	r2,r2,1

00000e18 1afffffa   2156 	bne	.L4166

                    2157 .L4117:

                    2158 ;592:     }


                    2159 ;593:     return iBufPos;


                    2160 

00000e1c e1a00004   2161 	mov	r0,r4

00000e20 e28dd008   2162 	add	sp,sp,8

00000e24 e8bd8030   2163 	ldmfd	[sp]!,{r4-r5,pc}

                    2164 	.endf	BerEncoder_EncodeInt32

                    2165 	.align	4

                    2166 ;ValueBuffer	[sp]	local

                    2167 ;i	r3	local

                    2168 

                    2169 ;iValue	[sp,4]	param

                    2170 ;pBuffer	r5	param

                    2171 ;iBufPos	r4	param

                    2172 

                    2173 	.section ".bss","awb"

                    2174 .L4357:

                    2175 	.data

                    2176 	.text

                    2177 

                    2178 ;594: }


                    2179 

                    2180 ;595: 


                    2181 ;596: int BerEncoder_EncodeFloatWithTL(unsigned char ucTag, float Value,


                    2182 	.align	4

                    2183 	.align	4

                    2184 BerEncoder_EncodeFloatWithTL::

00000e28 e92d4cf2   2185 	stmfd	[sp]!,{r1,r4-r7,r10-fp,lr}


                                                                      Page 37
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                    2186 ;597:     unsigned char formatWidth, unsigned char exponentWidth,


                    2187 ;598:     unsigned char* pBuffer, int iBufPos)


                    2188 ;599: {


                    2189 

                    2190 ;600:     unsigned char* pValueArray = (unsigned char*)&Value;


                    2191 

                    2192 ;601:     unsigned char valueBuffer[9];


                    2193 ;602:     int i;


                    2194 ;603: 


                    2195 ;604:     int byteSize = formatWidth / 8;


                    2196 

00000e2c e24dd00c   2197 	sub	sp,sp,12

00000e30 e58d100c   2198 	str	r1,[sp,12]

00000e34 e59d502c   2199 	ldr	r5,[sp,44]

00000e38 e59d4030   2200 	ldr	r4,[sp,48]

00000e3c e5cd3000   2201 	strb	r3,[sp]

                    2202 ;607: 


                    2203 ;608:     for (i = 0; i < byteSize; i++) {


                    2204 

00000e40 e1a06000   2205 	mov	r6,r0

00000e44 e28d0001   2206 	add	r0,sp,1

00000e48 e1b071a2   2207 	movs	r7,r2 lsr 3

                    2208 ;605: 


                    2209 ;606:     valueBuffer[0] = exponentWidth;


                    2210 

00000e4c 51a0b007   2211 	movpl	fp,r7

00000e50 43a0b000   2212 	movmi	fp,0

00000e54 e1b0a1ab   2213 	movs	r10,fp lsr 3

00000e58 11a03000   2214 	movne	r3,r0

00000e5c 128dc00c   2215 	addne	r12,sp,12

00000e60 11a0218a   2216 	movne	r2,r10 lsl 3

00000e64 1a000003   2217 	bne	.L4436

00000e68 e3a02000   2218 	mov	r2,0

00000e6c e21ba007   2219 	ands	r10,fp,7

00000e70 1a000014   2220 	bne	.L4441

00000e74 ea00001a   2221 	b	.L4384

                    2222 .L4436:

00000e78 e4dc1001   2223 	ldrb	r1,[r12],1

00000e7c e4c31001   2224 	strb	r1,[r3],1

00000e80 e4dc1001   2225 	ldrb	r1,[r12],1

00000e84 e4c31001   2226 	strb	r1,[r3],1

00000e88 e4dc1001   2227 	ldrb	r1,[r12],1

00000e8c e4c31001   2228 	strb	r1,[r3],1

00000e90 e4dc1001   2229 	ldrb	r1,[r12],1

00000e94 e4c31001   2230 	strb	r1,[r3],1

00000e98 e4dc1001   2231 	ldrb	r1,[r12],1

00000e9c e4c31001   2232 	strb	r1,[r3],1

00000ea0 e4dc1001   2233 	ldrb	r1,[r12],1

00000ea4 e4c31001   2234 	strb	r1,[r3],1

00000ea8 e4dc1001   2235 	ldrb	r1,[r12],1

00000eac e4c31001   2236 	strb	r1,[r3],1

00000eb0 e4dc1001   2237 	ldrb	r1,[r12],1

00000eb4 e25aa001   2238 	subs	r10,r10,1

00000eb8 e4c31001   2239 	strb	r1,[r3],1

00000ebc 1affffed   2240 	bne	.L4436

00000ec0 e21ba007   2241 	ands	r10,fp,7

00000ec4 0a000006   2242 	beq	.L4384

                    2243 .L4441:

00000ec8 e0823000   2244 	add	r3,r2,r0

00000ecc e08dc002   2245 	add	r12,sp,r2

00000ed0 e28c200c   2246 	add	r2,r12,12


                                                                      Page 38
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                    2247 .L4440:

00000ed4 e4d2c001   2248 	ldrb	r12,[r2],1

00000ed8 e25aa001   2249 	subs	r10,r10,1

00000edc e4c3c001   2250 	strb	r12,[r3],1

00000ee0 1afffffb   2251 	bne	.L4440

                    2252 .L4384:

                    2253 ;610:     }


                    2254 ;611: 


                    2255 ;612:     //если ORDER_LITTLE_ENDIAN


                    2256 ;613:     BerEncoder_RevertByteOrder(valueBuffer + 1, byteSize);


                    2257 

00000ee4 e1a01007   2258 	mov	r1,r7

00000ee8 eb0001af*  2259 	bl	BerEncoder_RevertByteOrder

                    2260 ;614:     //если ORDER_LITTLE_ENDIAN


                    2261 ;615: 


                    2262 ;616:     pBuffer[iBufPos++] = ucTag;


                    2263 

00000eec e7c56004   2264 	strb	r6,[r5,r4]

00000ef0 e2844001   2265 	add	r4,r4,1

                    2266 ;617:     pBuffer[iBufPos++] = (unsigned char)byteSize+1;


                    2267 

00000ef4 e2970001   2268 	adds	r0,r7,1

00000ef8 e7c50004   2269 	strb	r0,[r5,r4]

00000efc e2844001   2270 	add	r4,r4,1

                    2271 ;618: 


                    2272 ;619:     for (i = 0; i < byteSize + 1; i++) {


                    2273 

00000f00 e3a02000   2274 	mov	r2,0

00000f04 51a0c000   2275 	movpl	r12,r0

00000f08 43a0c000   2276 	movmi	r12,0

00000f0c e1b031ac   2277 	movs	r3,r12 lsr 3

00000f10 0a00001b   2278 	beq	.L4443

00000f14 e1a0000d   2279 	mov	r0,sp

00000f18 e1a02183   2280 	mov	r2,r3 lsl 3

                    2281 .L4459:

00000f1c e4d01001   2282 	ldrb	r1,[r0],1

00000f20 e2533001   2283 	subs	r3,r3,1

00000f24 e7c51004   2284 	strb	r1,[r5,r4]

00000f28 e4d01001   2285 	ldrb	r1,[r0],1

00000f2c e2844001   2286 	add	r4,r4,1

00000f30 e7c51004   2287 	strb	r1,[r5,r4]

00000f34 e4d01001   2288 	ldrb	r1,[r0],1

00000f38 e2844001   2289 	add	r4,r4,1

00000f3c e7c51004   2290 	strb	r1,[r5,r4]

00000f40 e4d01001   2291 	ldrb	r1,[r0],1

00000f44 e2844001   2292 	add	r4,r4,1

00000f48 e7c51004   2293 	strb	r1,[r5,r4]

00000f4c e4d01001   2294 	ldrb	r1,[r0],1

00000f50 e2844001   2295 	add	r4,r4,1

00000f54 e7c51004   2296 	strb	r1,[r5,r4]

00000f58 e4d01001   2297 	ldrb	r1,[r0],1

00000f5c e2844001   2298 	add	r4,r4,1

00000f60 e7c51004   2299 	strb	r1,[r5,r4]

00000f64 e4d01001   2300 	ldrb	r1,[r0],1

00000f68 e2844001   2301 	add	r4,r4,1

00000f6c e7c51004   2302 	strb	r1,[r5,r4]

00000f70 e4d01001   2303 	ldrb	r1,[r0],1

00000f74 e2844001   2304 	add	r4,r4,1

00000f78 e7c51004   2305 	strb	r1,[r5,r4]

00000f7c e2844001   2306 	add	r4,r4,1

00000f80 1affffe5   2307 	bne	.L4459


                                                                      Page 39
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                    2308 .L4443:

00000f84 e21c3007   2309 	ands	r3,r12,7

00000f88 1082000d   2310 	addne	r0,r2,sp

                    2311 .L4463:

00000f8c 14d02001   2312 	ldrneb	r2,[r0],1

00000f90 17c52004   2313 	strneb	r2,[r5,r4]

00000f94 12844001   2314 	addne	r4,r4,1

00000f98 12533001   2315 	subnes	r3,r3,1

00000f9c 1afffffa   2316 	bne	.L4463

                    2317 .L4388:

                    2318 ;621:     }


                    2319 ;622: 


                    2320 ;623:     return iBufPos;


                    2321 

00000fa0 e1a00004   2322 	mov	r0,r4

00000fa4 e28dd00c   2323 	add	sp,sp,12

00000fa8 e8bd8cf2   2324 	ldmfd	[sp]!,{r1,r4-r7,r10-fp,pc}

                    2325 	.endf	BerEncoder_EncodeFloatWithTL

                    2326 	.align	4

                    2327 ;valueBuffer	[sp]	local

                    2328 ;i	r2	local

                    2329 ;byteSize	r7	local

                    2330 

                    2331 ;ucTag	r6	param

                    2332 ;Value	[sp,12]	param

                    2333 ;formatWidth	r2	param

                    2334 ;exponentWidth	r3	param

                    2335 ;pBuffer	r5	param

                    2336 ;iBufPos	r4	param

                    2337 

                    2338 	.section ".bss","awb"

                    2339 .L4763:

                    2340 	.data

                    2341 	.text

                    2342 

                    2343 ;624: }


                    2344 

                    2345 ;625: 


                    2346 ;626: int BerEncoder_encodeUInt32WithTL(unsigned char ucTag, unsigned int iValue,


                    2347 	.align	4

                    2348 	.align	4

                    2349 BerEncoder_encodeUInt32WithTL::

00000fac e92d40f2   2350 	stmfd	[sp]!,{r1,r4-r7,lr}

00000fb0 e1a05002   2351 	mov	r5,r2

00000fb4 e1a07000   2352 	mov	r7,r0

00000fb8 e24dd008   2353 	sub	sp,sp,8

00000fbc e58d1008   2354 	str	r1,[sp,8]

00000fc0 e5dd0009   2355 	ldrb	r0,[sp,9]

00000fc4 e1a04003   2356 	mov	r4,r3

00000fc8 e5cd0002   2357 	strb	r0,[sp,2]

00000fcc e5dd000a   2358 	ldrb	r0,[sp,10]

00000fd0 e3a06000   2359 	mov	r6,0

00000fd4 e5cd0003   2360 	strb	r0,[sp,3]

00000fd8 e5dd000b   2361 	ldrb	r0,[sp,11]

00000fdc e5cd6000   2362 	strb	r6,[sp]

00000fe0 e5cd0004   2363 	strb	r0,[sp,4]

                    2364 ;627:         unsigned char* pBuffer, int iBufPos)


                    2365 ;628: {


                    2366 

                    2367 ;629: 


                    2368 ;630:     unsigned char* pValueArray = (unsigned char*)&iValue;



                                                                      Page 40
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                    2369 

                    2370 ;631:     unsigned char ValueBuffer[5];


                    2371 ;632: 


                    2372 ;633:     int i;


                    2373 ;634:     int iSize;


                    2374 ;635: 


                    2375 ;636:     ValueBuffer[0] = 0;


                    2376 

                    2377 ;637: 


                    2378 ;638: 


                    2379 ;639:     for( i = 0; i < 4; i++ )


                    2380 

                    2381 ;643: 


                    2382 ;644:     }//for( i = 0; i < 4; i++ )


                    2383 ;645: 


                    2384 ;646:     //если ORDER_LITTLE_ENDIAN


                    2385 ;647:     BerEncoder_RevertByteOrder( ValueBuffer + 1, 4 );


                    2386 

00000fe4 e28d0001   2387 	add	r0,sp,1

00000fe8 e5cd1001   2388 	strb	r1,[sp,1]

00000fec e3a01004   2389 	mov	r1,4

00000ff0 eb00016d*  2390 	bl	BerEncoder_RevertByteOrder

                    2391 ;648:     //если ORDER_LITTLE_ENDIAN


                    2392 ;649: 


                    2393 ;650:     iSize = BerEncoder_CompressInteger( ValueBuffer, 5 );


                    2394 

00000ff4 e1a0000d   2395 	mov	r0,sp

00000ff8 e3a01005   2396 	mov	r1,5

00000ffc ebfffe6b*  2397 	bl	BerEncoder_CompressInteger

                    2398 ;651: 


                    2399 ;652:     pBuffer[iBufPos++] = ucTag;


                    2400 

00001000 e7c57004   2401 	strb	r7,[r5,r4]

00001004 e2844001   2402 	add	r4,r4,1

                    2403 ;653:     pBuffer[iBufPos++] = (unsigned char)iSize;


                    2404 

00001008 e7c50004   2405 	strb	r0,[r5,r4]

0000100c e2844001   2406 	add	r4,r4,1

                    2407 ;654: 


                    2408 ;655:     for( i = 0; i < iSize; i++ )


                    2409 

00001010 e3500000   2410 	cmp	r0,0

00001014 a1a0c000   2411 	movge	r12,r0

00001018 b3a0c000   2412 	movlt	r12,0

0000101c e1b031ac   2413 	movs	r3,r12 lsr 3

00001020 0a00001b   2414 	beq	.L4850

00001024 e1a0200d   2415 	mov	r2,sp

00001028 e1a06183   2416 	mov	r6,r3 lsl 3

                    2417 .L4866:

0000102c e4d20001   2418 	ldrb	r0,[r2],1

00001030 e2533001   2419 	subs	r3,r3,1

00001034 e7c50004   2420 	strb	r0,[r5,r4]

00001038 e4d20001   2421 	ldrb	r0,[r2],1

0000103c e2844001   2422 	add	r4,r4,1

00001040 e7c50004   2423 	strb	r0,[r5,r4]

00001044 e4d20001   2424 	ldrb	r0,[r2],1

00001048 e2844001   2425 	add	r4,r4,1

0000104c e7c50004   2426 	strb	r0,[r5,r4]

00001050 e4d20001   2427 	ldrb	r0,[r2],1

00001054 e2844001   2428 	add	r4,r4,1

00001058 e7c50004   2429 	strb	r0,[r5,r4]


                                                                      Page 41
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
0000105c e4d20001   2430 	ldrb	r0,[r2],1

00001060 e2844001   2431 	add	r4,r4,1

00001064 e7c50004   2432 	strb	r0,[r5,r4]

00001068 e4d20001   2433 	ldrb	r0,[r2],1

0000106c e2844001   2434 	add	r4,r4,1

00001070 e7c50004   2435 	strb	r0,[r5,r4]

00001074 e4d20001   2436 	ldrb	r0,[r2],1

00001078 e2844001   2437 	add	r4,r4,1

0000107c e7c50004   2438 	strb	r0,[r5,r4]

00001080 e4d20001   2439 	ldrb	r0,[r2],1

00001084 e2844001   2440 	add	r4,r4,1

00001088 e7c50004   2441 	strb	r0,[r5,r4]

0000108c e2844001   2442 	add	r4,r4,1

00001090 1affffe5   2443 	bne	.L4866

                    2444 .L4850:

00001094 e21c3007   2445 	ands	r3,r12,7

00001098 1086000d   2446 	addne	r0,r6,sp

                    2447 .L4870:

0000109c 14d02001   2448 	ldrneb	r2,[r0],1

000010a0 17c52004   2449 	strneb	r2,[r5,r4]

000010a4 12844001   2450 	addne	r4,r4,1

000010a8 12533001   2451 	subnes	r3,r3,1

000010ac 1afffffa   2452 	bne	.L4870

                    2453 .L4813:

                    2454 ;659: 


                    2455 ;660:     }//    for( i = 0; i < iSize; i++ )


                    2456 ;661: 


                    2457 ;662:     return iBufPos;


                    2458 

000010b0 e1a00004   2459 	mov	r0,r4

000010b4 e28dd008   2460 	add	sp,sp,8

000010b8 e8bd80f2   2461 	ldmfd	[sp]!,{r1,r4-r7,pc}

                    2462 	.endf	BerEncoder_encodeUInt32WithTL

                    2463 	.align	4

                    2464 ;ValueBuffer	[sp]	local

                    2465 ;i	r6	local

                    2466 

                    2467 ;ucTag	r7	param

                    2468 ;iValue	[sp,8]	param

                    2469 ;pBuffer	r5	param

                    2470 ;iBufPos	r4	param

                    2471 

                    2472 	.section ".bss","awb"

                    2473 .L5063:

                    2474 	.data

                    2475 	.text

                    2476 

                    2477 ;663: 


                    2478 ;664: }


                    2479 

                    2480 ;665: 


                    2481 ;666: int BerEncoder_EncodeInt32WithTL(unsigned char ucTag, int iValue,


                    2482 	.align	4

                    2483 	.align	4

                    2484 BerEncoder_EncodeInt32WithTL::

000010bc e92d4076   2485 	stmfd	[sp]!,{r1-r2,r4-r6,lr}

000010c0 e1a06000   2486 	mov	r6,r0

000010c4 e5dd0000   2487 	ldrb	r0,[sp]

000010c8 e5cd0004   2488 	strb	r0,[sp,4]

000010cc e5dd0001   2489 	ldrb	r0,[sp,1]

000010d0 e1a05002   2490 	mov	r5,r2


                                                                      Page 42
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
000010d4 e5cd0005   2491 	strb	r0,[sp,5]

000010d8 e5dd0002   2492 	ldrb	r0,[sp,2]

000010dc e1a04003   2493 	mov	r4,r3

000010e0 e5cd0006   2494 	strb	r0,[sp,6]

000010e4 e5dd0003   2495 	ldrb	r0,[sp,3]

000010e8 e3a01004   2496 	mov	r1,4

000010ec e5cd0007   2497 	strb	r0,[sp,7]

                    2498 ;667:         unsigned char* pBuffer, int iBufPos)


                    2499 ;668: {


                    2500 

                    2501 ;669:     unsigned char* pValueArray = (unsigned char*)&iValue;


                    2502 

                    2503 ;670:     unsigned char ValueBuffer[4];


                    2504 ;671: 


                    2505 ;672:     int i;


                    2506 ;673:     int iSize;


                    2507 ;674: 


                    2508 ;675:     for (i = 0; i < 4; i++)


                    2509 

                    2510 ;679: 


                    2511 ;680:     }//for( i = 0; i < 4; i++ )


                    2512 ;681: 


                    2513 ;682:      //если ORDER_LITTLE_ENDIAN


                    2514 ;683:     BerEncoder_RevertByteOrder(ValueBuffer, 4);


                    2515 

000010f0 e28d0004   2516 	add	r0,sp,4

000010f4 eb00012c*  2517 	bl	BerEncoder_RevertByteOrder

                    2518 ;684:     //если ORDER_LITTLE_ENDIAN


                    2519 ;685: 


                    2520 ;686:     iSize = BerEncoder_CompressInteger(ValueBuffer, 4);


                    2521 

000010f8 e28d0004   2522 	add	r0,sp,4

000010fc e3a01004   2523 	mov	r1,4

00001100 ebfffe2a*  2524 	bl	BerEncoder_CompressInteger

                    2525 ;687: 


                    2526 ;688:     pBuffer[iBufPos++] = ucTag;


                    2527 

00001104 e7c56004   2528 	strb	r6,[r5,r4]

00001108 e2844001   2529 	add	r4,r4,1

                    2530 ;689:     pBuffer[iBufPos++] = (unsigned char)iSize;


                    2531 

0000110c e7c50004   2532 	strb	r0,[r5,r4]

00001110 e2844001   2533 	add	r4,r4,1

                    2534 ;690: 


                    2535 ;691:     for (i = 0; i < iSize; i++)


                    2536 

00001114 e3a0c000   2537 	mov	r12,0

00001118 e3500000   2538 	cmp	r0,0

0000111c a1a06000   2539 	movge	r6,r0

00001120 b3a06000   2540 	movlt	r6,0

00001124 e1b031a6   2541 	movs	r3,r6 lsr 3

00001128 0a00001b   2542 	beq	.L5122

0000112c e28d2004   2543 	add	r2,sp,4

00001130 e1a0c183   2544 	mov	r12,r3 lsl 3

                    2545 .L5138:

00001134 e4d20001   2546 	ldrb	r0,[r2],1

00001138 e2533001   2547 	subs	r3,r3,1

0000113c e7c50004   2548 	strb	r0,[r5,r4]

00001140 e4d20001   2549 	ldrb	r0,[r2],1

00001144 e2844001   2550 	add	r4,r4,1

00001148 e7c50004   2551 	strb	r0,[r5,r4]


                                                                      Page 43
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
0000114c e4d20001   2552 	ldrb	r0,[r2],1

00001150 e2844001   2553 	add	r4,r4,1

00001154 e7c50004   2554 	strb	r0,[r5,r4]

00001158 e4d20001   2555 	ldrb	r0,[r2],1

0000115c e2844001   2556 	add	r4,r4,1

00001160 e7c50004   2557 	strb	r0,[r5,r4]

00001164 e4d20001   2558 	ldrb	r0,[r2],1

00001168 e2844001   2559 	add	r4,r4,1

0000116c e7c50004   2560 	strb	r0,[r5,r4]

00001170 e4d20001   2561 	ldrb	r0,[r2],1

00001174 e2844001   2562 	add	r4,r4,1

00001178 e7c50004   2563 	strb	r0,[r5,r4]

0000117c e4d20001   2564 	ldrb	r0,[r2],1

00001180 e2844001   2565 	add	r4,r4,1

00001184 e7c50004   2566 	strb	r0,[r5,r4]

00001188 e4d20001   2567 	ldrb	r0,[r2],1

0000118c e2844001   2568 	add	r4,r4,1

00001190 e7c50004   2569 	strb	r0,[r5,r4]

00001194 e2844001   2570 	add	r4,r4,1

00001198 1affffe5   2571 	bne	.L5138

                    2572 .L5122:

0000119c e2163007   2573 	ands	r3,r6,7

000011a0 108d000c   2574 	addne	r0,sp,r12

000011a4 12800004   2575 	addne	r0,r0,4

                    2576 .L5142:

000011a8 14d02001   2577 	ldrneb	r2,[r0],1

000011ac 17c52004   2578 	strneb	r2,[r5,r4]

000011b0 12844001   2579 	addne	r4,r4,1

000011b4 12533001   2580 	subnes	r3,r3,1

000011b8 1afffffa   2581 	bne	.L5142

                    2582 .L5093:

                    2583 ;695: 


                    2584 ;696:     }//    for( i = 0; i < iSize; i++ )


                    2585 ;697: 


                    2586 ;698:     return iBufPos;


                    2587 

000011bc e1a00004   2588 	mov	r0,r4

000011c0 e8bd8076   2589 	ldmfd	[sp]!,{r1-r2,r4-r6,pc}

                    2590 	.endf	BerEncoder_EncodeInt32WithTL

                    2591 	.align	4

                    2592 ;ValueBuffer	[sp,4]	local

                    2593 ;i	r12	local

                    2594 

                    2595 ;ucTag	r6	param

                    2596 ;iValue	[sp]	param

                    2597 ;pBuffer	r5	param

                    2598 ;iBufPos	r4	param

                    2599 

                    2600 	.section ".bss","awb"

                    2601 .L5333:

                    2602 	.data

                    2603 	.text

                    2604 

                    2605 ;699: 


                    2606 ;700: }


                    2607 

                    2608 ;701: 


                    2609 ;702: int BerEncoder_encodeOctetString(uint8_t tag, const uint8_t *octetString, uint32_t octetStringSize,


                    2610 	.align	4

                    2611 	.align	4

                    2612 BerEncoder_encodeOctetString::


                                                                      Page 44
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
000011c4 e92d40f0   2613 	stmfd	[sp]!,{r4-r7,lr}

000011c8 e1a07001   2614 	mov	r7,r1

000011cc e59d1014   2615 	ldr	r1,[sp,20]

                    2616 ;703:                                  uint8_t *buffer, int bufPos)


                    2617 ;704: {


                    2618 

                    2619 ;705:     buffer[bufPos++] = tag;


                    2620 

000011d0 e1a05002   2621 	mov	r5,r2

000011d4 e2812001   2622 	add	r2,r1,1

                    2623 ;706: 


                    2624 ;707:     bufPos = BerEncoder_encodeLength(octetStringSize, buffer, bufPos);


                    2625 

000011d8 e1a06003   2626 	mov	r6,r3

000011dc e7c60001   2627 	strb	r0,[r6,r1]

000011e0 e1a01006   2628 	mov	r1,r6

000011e4 e1a00005   2629 	mov	r0,r5

000011e8 ebfffb84*  2630 	bl	BerEncoder_encodeLength

000011ec e1a02005   2631 	mov	r2,r5

000011f0 e1a01007   2632 	mov	r1,r7

000011f4 e1a04000   2633 	mov	r4,r0

                    2634 ;708: 


                    2635 ;709:     memcpy(buffer + bufPos, octetString, octetStringSize);


                    2636 

000011f8 e0840006   2637 	add	r0,r4,r6

000011fc eb000000*  2638 	bl	memcpy

                    2639 ;710:     bufPos += octetStringSize;


                    2640 

                    2641 ;711: 


                    2642 ;712:     return bufPos;


                    2643 

00001200 e0840005   2644 	add	r0,r4,r5

00001204 e8bd80f0   2645 	ldmfd	[sp]!,{r4-r7,pc}

                    2646 	.endf	BerEncoder_encodeOctetString

                    2647 	.align	4

                    2648 

                    2649 ;tag	r0	param

                    2650 ;octetString	r7	param

                    2651 ;octetStringSize	r5	param

                    2652 ;buffer	r6	param

                    2653 ;bufPos	r4	param

                    2654 

                    2655 	.section ".bss","awb"

                    2656 .L5374:

                    2657 	.data

                    2658 	.text

                    2659 

                    2660 ;713: }


                    2661 

                    2662 ;714: 


                    2663 ;715: int BerEncoder_encodeBitString( unsigned char ucTag, int iBitStringSize, unsigned char* pBitString,


                    2664 	.align	4

                    2665 	.align	4

                    2666 BerEncoder_encodeBitString::

00001208 e92d44f0   2667 	stmfd	[sp]!,{r4-r7,r10,lr}

                    2668 ;716:                                 unsigned char* pBuffer, int iBufPos )


                    2669 ;717: {


                    2670 

0000120c e3a04000   2671 	mov	r4,0

                    2672 ;718:     int iByteSize = iBitStringSize / 8;


                    2673 


                                                                      Page 45
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
00001210 e1a06002   2674 	mov	r6,r2

00001214 e1a02fc1   2675 	mov	r2,r1 asr 31

00001218 e1a05003   2676 	mov	r5,r3

0000121c e59dc018   2677 	ldr	r12,[sp,24]

00001220 e0813ea2   2678 	add	r3,r1,r2 lsr 29

00001224 e7c5000c   2679 	strb	r0,[r5,r12]

                    2680 ;724: 


                    2681 ;725:     if ( iBitStringSize % 8 )


                    2682 

00001228 e2020007   2683 	and	r0,r2,7

0000122c e28c2001   2684 	add	r2,r12,1

00001230 e1a071c3   2685 	mov	r7,r3 asr 3

                    2686 ;719:     int i;


                    2687 ;720:     int iPadding;


                    2688 ;721:     unsigned char ucPaddingMask = 0;


                    2689 

                    2690 ;722: 


                    2691 ;723:     pBuffer[iBufPos++] = ucTag;


                    2692 

00001234 e0810000   2693 	add	r0,r1,r0

00001238 e3c00007   2694 	bic	r0,r0,7

0000123c e1510000   2695 	cmp	r1,r0

                    2696 ;726:     {


                    2697 

                    2698 ;727: 


                    2699 ;728:         iByteSize++;


                    2700 

00001240 12877001   2701 	addne	r7,r7,1

                    2702 ;729: 


                    2703 ;730:     }//if ( iBitStringSize % 8 )


                    2704 ;731: 


                    2705 ;732:     iPadding = ( iByteSize * 8 ) - iBitStringSize;


                    2706 

00001244 e061a187   2707 	rsb	r10,r1,r7 lsl 3

                    2708 ;733: 


                    2709 ;734:     iBufPos = BerEncoder_encodeLength( iByteSize + 1, pBuffer, iBufPos );


                    2710 

00001248 e1a01005   2711 	mov	r1,r5

0000124c e2870001   2712 	add	r0,r7,1

00001250 ebfffb6a*  2713 	bl	BerEncoder_encodeLength

                    2714 ;735: 


                    2715 ;736:     pBuffer[iBufPos++] = iPadding;


                    2716 

00001254 e280c001   2717 	add	r12,r0,1

00001258 e7c5a000   2718 	strb	r10,[r5,r0]

                    2719 ;737: 


                    2720 ;738: 


                    2721 ;739: 


                    2722 ;740:     for ( i = 0; i < iByteSize; i++ )


                    2723 

0000125c e1a00004   2724 	mov	r0,r4

00001260 e3570000   2725 	cmp	r7,0

00001264 a1a03007   2726 	movge	r3,r7

00001268 b3a03000   2727 	movlt	r3,0

0000126c e1b021a3   2728 	movs	r2,r3 lsr 3

00001270 0a00001b   2729 	beq	.L5432

00001274 e2861001   2730 	add	r1,r6,1

                    2731 .L5448:

00001278 e7d67000   2732 	ldrb	r7,[r6,r0]

0000127c e2800008   2733 	add	r0,r0,8

00001280 e7c5700c   2734 	strb	r7,[r5,r12]


                                                                      Page 46
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
00001284 e4d17008   2735 	ldrb	r7,[r1],8

00001288 e28cc001   2736 	add	r12,r12,1

0000128c e7c5700c   2737 	strb	r7,[r5,r12]

00001290 e5517007   2738 	ldrb	r7,[r1,-7]

00001294 e28cc001   2739 	add	r12,r12,1

00001298 e7c5700c   2740 	strb	r7,[r5,r12]

0000129c e5517006   2741 	ldrb	r7,[r1,-6]

000012a0 e28cc001   2742 	add	r12,r12,1

000012a4 e7c5700c   2743 	strb	r7,[r5,r12]

000012a8 e5517005   2744 	ldrb	r7,[r1,-5]

000012ac e28cc001   2745 	add	r12,r12,1

000012b0 e7c5700c   2746 	strb	r7,[r5,r12]

000012b4 e5517004   2747 	ldrb	r7,[r1,-4]

000012b8 e28cc001   2748 	add	r12,r12,1

000012bc e7c5700c   2749 	strb	r7,[r5,r12]

000012c0 e5517003   2750 	ldrb	r7,[r1,-3]

000012c4 e28cc001   2751 	add	r12,r12,1

000012c8 e7c5700c   2752 	strb	r7,[r5,r12]

000012cc e5517002   2753 	ldrb	r7,[r1,-2]

000012d0 e28cc001   2754 	add	r12,r12,1

000012d4 e7c5700c   2755 	strb	r7,[r5,r12]

000012d8 e28cc001   2756 	add	r12,r12,1

000012dc e2522001   2757 	subs	r2,r2,1

000012e0 1affffe4   2758 	bne	.L5448

                    2759 .L5432:

000012e4 e2132007   2760 	ands	r2,r3,7

000012e8 0a000005   2761 	beq	.L5385

                    2762 .L5452:

000012ec e7d61000   2763 	ldrb	r1,[r6,r0]

000012f0 e2800001   2764 	add	r0,r0,1

000012f4 e7c5100c   2765 	strb	r1,[r5,r12]

000012f8 e28cc001   2766 	add	r12,r12,1

000012fc e2522001   2767 	subs	r2,r2,1

00001300 1afffff9   2768 	bne	.L5452

                    2769 .L5385:

                    2770 ;744: 


                    2771 ;745:     }//for ( i = 0; i < iByteSize; i++ )


                    2772 ;746: 


                    2773 ;747: 


                    2774 ;748: 


                    2775 ;749:     for( i = 0; i < iPadding; i++ )


                    2776 

00001304 e3a00000   2777 	mov	r0,0

00001308 e35a0000   2778 	cmp	r10,0

0000130c a1a0300a   2779 	movge	r3,r10

00001310 b3a03000   2780 	movlt	r3,0

00001314 e1b021a3   2781 	movs	r2,r3 lsr 3

00001318 0a000013   2782 	beq	.L5455

0000131c e3a01001   2783 	mov	r1,1

                    2784 .L5471:

00001320 e0844011   2785 	add	r4,r4,r1 lsl r0

00001324 e2806001   2786 	add	r6,r0,1

00001328 e0844611   2787 	add	r4,r4,r1 lsl r6

0000132c e2806002   2788 	add	r6,r0,2

00001330 e0844611   2789 	add	r4,r4,r1 lsl r6

00001334 e2806003   2790 	add	r6,r0,3

00001338 e0844611   2791 	add	r4,r4,r1 lsl r6

0000133c e2806004   2792 	add	r6,r0,4

00001340 e0844611   2793 	add	r4,r4,r1 lsl r6

00001344 e2806005   2794 	add	r6,r0,5

00001348 e0844611   2795 	add	r4,r4,r1 lsl r6


                                                                      Page 47
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
0000134c e2806006   2796 	add	r6,r0,6

00001350 e0844611   2797 	add	r4,r4,r1 lsl r6

00001354 e2806007   2798 	add	r6,r0,7

00001358 e0844611   2799 	add	r4,r4,r1 lsl r6

0000135c e20440ff   2800 	and	r4,r4,255

00001360 e2800008   2801 	add	r0,r0,8

00001364 e2522001   2802 	subs	r2,r2,1

00001368 1affffec   2803 	bne	.L5471

                    2804 .L5455:

0000136c e2132007   2805 	ands	r2,r3,7

00001370 13a03001   2806 	movne	r3,1

                    2807 .L5475:

00001374 10844013   2808 	addne	r4,r4,r3 lsl r0

00001378 120440ff   2809 	andne	r4,r4,255

0000137c 12800001   2810 	addne	r0,r0,1

00001380 12522001   2811 	subnes	r2,r2,1

00001384 1afffffa   2812 	bne	.L5475

                    2813 .L5389:

                    2814 ;753: 


                    2815 ;754:     }//for( i = 0; i < iPadding; i++ )


                    2816 ;755: 


                    2817 ;756:     ucPaddingMask = ~ucPaddingMask;


                    2818 

                    2819 ;757: 


                    2820 ;758:     pBuffer[iBufPos -1] = pBuffer[iBufPos -1] & ucPaddingMask;


                    2821 

00001388 e08c0005   2822 	add	r0,r12,r5

0000138c e5501001   2823 	ldrb	r1,[r0,-1]

00001390 e1c11004   2824 	bic	r1,r1,r4

00001394 e5401001   2825 	strb	r1,[r0,-1]

                    2826 ;759: 


                    2827 ;760:     return iBufPos;


                    2828 

00001398 e1a0000c   2829 	mov	r0,r12

0000139c e8bd84f0   2830 	ldmfd	[sp]!,{r4-r7,r10,pc}

                    2831 	.endf	BerEncoder_encodeBitString

                    2832 	.align	4

                    2833 ;iByteSize	r7	local

                    2834 ;i	r0	local

                    2835 ;iPadding	r10	local

                    2836 ;ucPaddingMask	r4	local

                    2837 

                    2838 ;ucTag	r0	param

                    2839 ;iBitStringSize	r1	param

                    2840 ;pBitString	r6	param

                    2841 ;pBuffer	r5	param

                    2842 ;iBufPos	r12	param

                    2843 

                    2844 	.section ".bss","awb"

                    2845 .L5786:

                    2846 	.data

                    2847 	.text

                    2848 

                    2849 ;761: }


                    2850 

                    2851 ;762: 


                    2852 ;763: int BerEncoder_encodeBitStringUshortBuf(uint8_t tag, int bitCount, uint16_t data,


                    2853 	.align	4

                    2854 	.align	4

                    2855 BerEncoder_encodeBitStringUshortBuf::

000013a0 e59dc000   2856 	ldr	r12,[sp]


                                                                      Page 48
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                    2857 ;764:     uint8_t* outBuf, int outBufPos)


                    2858 ;765: {


                    2859 

                    2860 ;766:     uint8_t padding = sizeof(uint16_t) * 8 - bitCount;


                    2861 

000013a4 e2611010   2862 	rsb	r1,r1,16

                    2863 ;767:     outBuf[outBufPos++] = tag;


                    2864 

000013a8 e7c3000c   2865 	strb	r0,[r3,r12]

000013ac e28cc001   2866 	add	r12,r12,1

                    2867 ;768:     outBuf[outBufPos++] = 3;//length


                    2868 

000013b0 e3a00003   2869 	mov	r0,3

000013b4 e7c3000c   2870 	strb	r0,[r3,r12]

000013b8 e28cc001   2871 	add	r12,r12,1

                    2872 ;769:     outBuf[outBufPos++] = padding;


                    2873 

000013bc e7c3100c   2874 	strb	r1,[r3,r12]

000013c0 e28cc001   2875 	add	r12,r12,1

                    2876 ;770:     outBuf[outBufPos++] = data & 0xFF;


                    2877 

000013c4 e7c3200c   2878 	strb	r2,[r3,r12]

000013c8 e28cc001   2879 	add	r12,r12,1

                    2880 ;771:     outBuf[outBufPos++] = data >> 8;


                    2881 

000013cc e1a00442   2882 	mov	r0,r2 asr 8

000013d0 e7c3000c   2883 	strb	r0,[r3,r12]

                    2884 ;772:     return outBufPos;


                    2885 

000013d4 e28c0001   2886 	add	r0,r12,1

000013d8 e12fff1e*  2887 	ret	

                    2888 	.endf	BerEncoder_encodeBitStringUshortBuf

                    2889 	.align	4

                    2890 

                    2891 ;tag	r0	param

                    2892 ;bitCount	r1	param

                    2893 ;data	r2	param

                    2894 ;outBuf	r3	param

                    2895 ;outBufPos	r12	param

                    2896 

                    2897 	.section ".bss","awb"

                    2898 .L5854:

                    2899 	.data

                    2900 	.text

                    2901 

                    2902 ;773: }


                    2903 

                    2904 ;774: 


                    2905 ;775: int BerEncoder_encodeUshortBitString(uint8_t tag, int bitCount, uint16_t data,


                    2906 	.align	4

                    2907 	.align	4

                    2908 BerEncoder_encodeUshortBitString::

000013dc e92d0010   2909 	stmfd	[sp]!,{r4}

000013e0 e59d4004   2910 	ldr	r4,[sp,4]

                    2911 ;776:     uint8_t* outBuf, int outBufPos)


                    2912 ;777: {


                    2913 

                    2914 ;778:     uint8_t padding = sizeof(uint16_t) * 8 - bitCount;


                    2915 

000013e4 e261c010   2916 	rsb	r12,r1,16

                    2917 ;779:     outBuf[outBufPos++] = tag;



                                                                      Page 49
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                    2918 

000013e8 e7c30004   2919 	strb	r0,[r3,r4]

000013ec e2844001   2920 	add	r4,r4,1

                    2921 ;780:     outBuf[outBufPos++] = 3;//length


                    2922 

000013f0 e3a00003   2923 	mov	r0,3

000013f4 e7c30004   2924 	strb	r0,[r3,r4]

000013f8 e2844001   2925 	add	r4,r4,1

                    2926 ;781:     outBuf[outBufPos++] = padding;


                    2927 

000013fc e7c3c004   2928 	strb	r12,[r3,r4]

00001400 e2844001   2929 	add	r4,r4,1

                    2930 ;782:     //Сдвигаем чтобы неиспользуемые биты были младшими


                    2931 ;783:     data <<= padding;


                    2932 

00001404 e20cc0ff   2933 	and	r12,r12,255

00001408 e1a02c12   2934 	mov	r2,r2 lsl r12

0000140c e1a02802   2935 	mov	r2,r2 lsl 16

00001410 e1a02822   2936 	mov	r2,r2 lsr 16

                    2937 ;784:     outBuf[outBufPos++] = data >> 8;


                    2938 

00001414 e1a00442   2939 	mov	r0,r2 asr 8

00001418 e7c30004   2940 	strb	r0,[r3,r4]

0000141c e2844001   2941 	add	r4,r4,1

                    2942 ;785:     outBuf[outBufPos++] = data & 0xFF;


                    2943 

00001420 e7c32004   2944 	strb	r2,[r3,r4]

                    2945 ;786:     return outBufPos;


                    2946 

00001424 e2840001   2947 	add	r0,r4,1

00001428 e8bd0010   2948 	ldmfd	[sp]!,{r4}

0000142c e12fff1e*  2949 	ret	

                    2950 	.endf	BerEncoder_encodeUshortBitString

                    2951 	.align	4

                    2952 ;padding	r12	local

                    2953 

                    2954 ;tag	r0	param

                    2955 ;bitCount	r1	param

                    2956 ;data	r2	param

                    2957 ;outBuf	r3	param

                    2958 ;outBufPos	r4	param

                    2959 

                    2960 	.section ".bss","awb"

                    2961 .L5886:

                    2962 	.data

                    2963 	.text

                    2964 

                    2965 ;787: }


                    2966 

                    2967 ;788: 


                    2968 ;789: int BerEncoder_encodeUcharBitString(uint8_t tag, int bitCount, uint8_t data,


                    2969 	.align	4

                    2970 	.align	4

                    2971 BerEncoder_encodeUcharBitString::

00001430 e92d0010   2972 	stmfd	[sp]!,{r4}

00001434 e59d4004   2973 	ldr	r4,[sp,4]

                    2974 ;790:     uint8_t* outBuf, int outBufPos)


                    2975 ;791: {


                    2976 

                    2977 ;792:     uint8_t padding = sizeof(uint8_t) * 8 - bitCount;


                    2978 


                                                                      Page 50
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
00001438 e261c008   2979 	rsb	r12,r1,8

                    2980 ;793:     outBuf[outBufPos++] = tag;


                    2981 

0000143c e7c30004   2982 	strb	r0,[r3,r4]

00001440 e2844001   2983 	add	r4,r4,1

                    2984 ;794:     outBuf[outBufPos++] = 2;//length


                    2985 

00001444 e3a00002   2986 	mov	r0,2

00001448 e7c30004   2987 	strb	r0,[r3,r4]

0000144c e2844001   2988 	add	r4,r4,1

                    2989 ;795:     outBuf[outBufPos++] = padding;


                    2990 

00001450 e7c3c004   2991 	strb	r12,[r3,r4]

00001454 e2844001   2992 	add	r4,r4,1

                    2993 ;796:     //Сдвигаем чтобы неиспользуемые биты были младшими


                    2994 ;797:     data <<= padding;


                    2995 

00001458 e20cc0ff   2996 	and	r12,r12,255

0000145c e1a02c12   2997 	mov	r2,r2 lsl r12

                    2998 ;798:     outBuf[outBufPos++] = data;


                    2999 

00001460 e7c32004   3000 	strb	r2,[r3,r4]

                    3001 ;799:     return outBufPos;


                    3002 

00001464 e2840001   3003 	add	r0,r4,1

00001468 e8bd0010   3004 	ldmfd	[sp]!,{r4}

0000146c e12fff1e*  3005 	ret	

                    3006 	.endf	BerEncoder_encodeUcharBitString

                    3007 	.align	4

                    3008 ;padding	r12	local

                    3009 

                    3010 ;tag	r0	param

                    3011 ;bitCount	r1	param

                    3012 ;data	r2	param

                    3013 ;outBuf	r3	param

                    3014 ;outBufPos	r4	param

                    3015 

                    3016 	.section ".bss","awb"

                    3017 .L5918:

                    3018 	.data

                    3019 	.text

                    3020 

                    3021 ;800: }


                    3022 

                    3023 ;801: 


                    3024 ;802: 


                    3025 ;803: int BerEncoder_determineEncodedStringSize( const char* String )


                    3026 	.align	4

                    3027 	.align	4

                    3028 BerEncoder_determineEncodedStringSize::

00001470 e92d4010   3029 	stmfd	[sp]!,{r4,lr}

                    3030 ;804: {


                    3031 

                    3032 ;805: 


                    3033 ;806:      int iSize;


                    3034 ;807:      int iLength;


                    3035 ;808: 


                    3036 ;809:      if( String != NULL )


                    3037 

00001474 e3500000   3038 	cmp	r0,0

                    3039 ;820: 



                                                                      Page 51
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                    3040 ;821:     }//if( String != NULL )


                    3041 ;822:     else


                    3042 ;823:     {


                    3043 

                    3044 ;824:         return 2;


                    3045 

00001478 03a00002   3046 	moveq	r0,2

0000147c 0a000004   3047 	beq	.L5925

                    3048 ;810:      {


                    3049 

                    3050 ;811:         iSize = 1; //размер тэга


                    3051 

                    3052 ;812: 


                    3053 ;813:         iLength = strlen( String );


                    3054 

00001480 eb000000*  3055 	bl	strlen

                    3056 ;814: 


                    3057 ;815:         iSize += BerEncoder_determineLengthSize( iLength );


                    3058 

00001484 e1a04000   3059 	mov	r4,r0

00001488 ebfffd11*  3060 	bl	BerEncoder_determineLengthSize

0000148c e0800004   3061 	add	r0,r0,r4

                    3062 ;816: 


                    3063 ;817:         iSize += iLength;


                    3064 

00001490 e2800001   3065 	add	r0,r0,1

                    3066 ;818: 


                    3067 ;819:         return iSize;


                    3068 

                    3069 .L5925:

00001494 e8bd8010   3070 	ldmfd	[sp]!,{r4,pc}

                    3071 	.endf	BerEncoder_determineEncodedStringSize

                    3072 	.align	4

                    3073 ;iSize	r0	local

                    3074 ;iLength	r4	local

                    3075 

                    3076 ;String	r0	param

                    3077 

                    3078 	.section ".bss","awb"

                    3079 .L5958:

                    3080 	.data

                    3081 	.text

                    3082 

                    3083 ;825: 


                    3084 ;826:      }


                    3085 ;827: 


                    3086 ;828: }


                    3087 

                    3088 ;829: 


                    3089 ;830: int BerEncoder_encodeStringWithTL( unsigned char ucTag, const char* pString,


                    3090 	.align	4

                    3091 	.align	4

                    3092 BerEncoder_encodeStringWithTL::

00001498 e92d40f0   3093 	stmfd	[sp]!,{r4-r7,lr}

                    3094 ;831:                                     unsigned char* pBuffer, int iBufPos )


                    3095 ;832: {


                    3096 

                    3097 ;833: 


                    3098 ;834:     int iLength;


                    3099 ;835:     int i;


                    3100 ;836: 



                                                                      Page 52
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                    3101 ;837:     pBuffer[iBufPos++] = ucTag;


                    3102 

0000149c e1b06001   3103 	movs	r6,r1

000014a0 e2834001   3104 	add	r4,r3,1

000014a4 e1a05002   3105 	mov	r5,r2

000014a8 e7c50003   3106 	strb	r0,[r5,r3]

                    3107 ;838: 


                    3108 ;839: 


                    3109 ;840:     if( pString != NULL )


                    3110 

                    3111 ;852: 


                    3112 ;853:         }//for( i = 0; i < iLength; i++ )


                    3113 ;854: 


                    3114 ;855:     }//if( pString != NULL )


                    3115 ;856:     else


                    3116 ;857:     {


                    3117 

                    3118 ;858: 


                    3119 ;859:         pBuffer[iBufPos++] = 0;


                    3120 

000014ac 07c56004   3121 	streqb	r6,[r5,r4]

000014b0 02844001   3122 	addeq	r4,r4,1

000014b4 0a000030   3123 	beq	.L5980

                    3124 ;841:     {


                    3125 

                    3126 ;842: 


                    3127 ;843:         iLength = strlen( pString );


                    3128 

000014b8 e1a00006   3129 	mov	r0,r6

000014bc eb000000*  3130 	bl	strlen

                    3131 ;844: 


                    3132 ;845:         iBufPos = BerEncoder_encodeLength( iLength, pBuffer, iBufPos );


                    3133 

000014c0 e1a02004   3134 	mov	r2,r4

000014c4 e1a01005   3135 	mov	r1,r5

000014c8 e1a07000   3136 	mov	r7,r0

000014cc ebfffacb*  3137 	bl	BerEncoder_encodeLength

000014d0 e1a04000   3138 	mov	r4,r0

                    3139 ;846: 


                    3140 ;847: 


                    3141 ;848:         for( i = 0; i < iLength; i++ )


                    3142 

000014d4 e3a02000   3143 	mov	r2,0

000014d8 e3570000   3144 	cmp	r7,0

000014dc a1a03007   3145 	movge	r3,r7

000014e0 b3a03000   3146 	movlt	r3,0

000014e4 e1b011a3   3147 	movs	r1,r3 lsr 3

000014e8 0a00001b   3148 	beq	.L6018

000014ec e2860001   3149 	add	r0,r6,1

                    3150 .L6034:

000014f0 e7d6c002   3151 	ldrb	r12,[r6,r2]

000014f4 e2822008   3152 	add	r2,r2,8

000014f8 e7c5c004   3153 	strb	r12,[r5,r4]

000014fc e4d0c008   3154 	ldrb	r12,[r0],8

00001500 e2844001   3155 	add	r4,r4,1

00001504 e7c5c004   3156 	strb	r12,[r5,r4]

00001508 e550c007   3157 	ldrb	r12,[r0,-7]

0000150c e2844001   3158 	add	r4,r4,1

00001510 e7c5c004   3159 	strb	r12,[r5,r4]

00001514 e550c006   3160 	ldrb	r12,[r0,-6]

00001518 e2844001   3161 	add	r4,r4,1


                                                                      Page 53
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
0000151c e7c5c004   3162 	strb	r12,[r5,r4]

00001520 e550c005   3163 	ldrb	r12,[r0,-5]

00001524 e2844001   3164 	add	r4,r4,1

00001528 e7c5c004   3165 	strb	r12,[r5,r4]

0000152c e550c004   3166 	ldrb	r12,[r0,-4]

00001530 e2844001   3167 	add	r4,r4,1

00001534 e7c5c004   3168 	strb	r12,[r5,r4]

00001538 e550c003   3169 	ldrb	r12,[r0,-3]

0000153c e2844001   3170 	add	r4,r4,1

00001540 e7c5c004   3171 	strb	r12,[r5,r4]

00001544 e550c002   3172 	ldrb	r12,[r0,-2]

00001548 e2844001   3173 	add	r4,r4,1

0000154c e7c5c004   3174 	strb	r12,[r5,r4]

00001550 e2844001   3175 	add	r4,r4,1

00001554 e2511001   3176 	subs	r1,r1,1

00001558 1affffe4   3177 	bne	.L6034

                    3178 .L6018:

0000155c e2131007   3179 	ands	r1,r3,7

00001560 0a000005   3180 	beq	.L5980

                    3181 .L6038:

00001564 e7d60002   3182 	ldrb	r0,[r6,r2]

00001568 e2822001   3183 	add	r2,r2,1

0000156c e7c50004   3184 	strb	r0,[r5,r4]

00001570 e2844001   3185 	add	r4,r4,1

00001574 e2511001   3186 	subs	r1,r1,1

00001578 1afffff9   3187 	bne	.L6038

                    3188 .L5980:

                    3189 ;860: 


                    3190 ;861:     }//else if( pString != NULL )


                    3191 ;862: 


                    3192 ;863:     return iBufPos;


                    3193 

0000157c e1a00004   3194 	mov	r0,r4

00001580 e8bd80f0   3195 	ldmfd	[sp]!,{r4-r7,pc}

                    3196 	.endf	BerEncoder_encodeStringWithTL

                    3197 	.align	4

                    3198 ;iLength	r7	local

                    3199 ;i	r2	local

                    3200 

                    3201 ;ucTag	r0	param

                    3202 ;pString	r6	param

                    3203 ;pBuffer	r5	param

                    3204 ;iBufPos	r4	param

                    3205 

                    3206 	.section ".bss","awb"

                    3207 .L6209:

                    3208 	.data

                    3209 	.text

                    3210 

                    3211 ;864: 


                    3212 ;865: }


                    3213 

                    3214 ;866: 


                    3215 ;867: int BerEncoder_encodeBoolean( unsigned char ucTag, unsigned char ucValue,


                    3216 	.align	4

                    3217 	.align	4

                    3218 BerEncoder_encodeBoolean::

00001584 e283c001   3219 	add	r12,r3,1

00001588 e7c20003   3220 	strb	r0,[r2,r3]

0000158c e3a00001   3221 	mov	r0,1

00001590 e7c2000c   3222 	strb	r0,[r2,r12]


                                                                      Page 54
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
00001594 e28cc001   3223 	add	r12,r12,1

00001598 e1b00001   3224 	movs	r0,r1

0000159c 13a00001   3225 	movne	r0,1

                    3226 ;868:                               unsigned char* pBuffer, int iBufPos )


                    3227 ;869: {


                    3228 

                    3229 ;870: 


                    3230 ;871:     pBuffer[iBufPos++] = ucTag;


                    3231 

                    3232 ;872:     pBuffer[iBufPos++] = 1;


                    3233 

                    3234 ;873: 


                    3235 ;874:     if( ucValue )


                    3236 

                    3237 ;875:     {


                    3238 

                    3239 ;876:         pBuffer[iBufPos++] = 0x01;


                    3240 

                    3241 ;877:     }


                    3242 ;878:     else


                    3243 ;879:     {


                    3244 

                    3245 ;880:         pBuffer[iBufPos++] = 0x00;


                    3246 

                    3247 ;881:     }


                    3248 ;882:     return iBufPos;


                    3249 

000015a0 e7c2000c   3250 	strb	r0,[r2,r12]

000015a4 e28c0001   3251 	add	r0,r12,1

000015a8 e12fff1e*  3252 	ret	

                    3253 	.endf	BerEncoder_encodeBoolean

                    3254 	.align	4

                    3255 

                    3256 ;ucTag	r0	param

                    3257 ;ucValue	r1	param

                    3258 ;pBuffer	r2	param

                    3259 ;iBufPos	r12	param

                    3260 

                    3261 	.section ".bss","awb"

                    3262 .L6288:

                    3263 	.data

                    3264 	.text

                    3265 

                    3266 ;883: }


                    3267 

                    3268 ;884: 


                    3269 ;885: void BerEncoder_RevertByteOrder( unsigned char* pOctets, const int iSize )


                    3270 	.align	4

                    3271 	.align	4

                    3272 BerEncoder_RevertByteOrder::

000015ac e92d00f0   3273 	stmfd	[sp]!,{r4-r7}

                    3274 ;886: {


                    3275 

                    3276 ;887:     int i;


                    3277 ;888:     unsigned char ucTemp;


                    3278 ;889: 


                    3279 ;890:     for( i = 0; i < iSize / 2; i++ )


                    3280 

000015b0 e3a04000   3281 	mov	r4,0

000015b4 e0812fa1   3282 	add	r2,r1,r1 lsr 31

000015b8 e1b060c2   3283 	movs	r6,r2 asr 1


                                                                      Page 55
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
000015bc 43a06000   3284 	movmi	r6,0

000015c0 e1b051a6   3285 	movs	r5,r6 lsr 3

000015c4 0a000024   3286 	beq	.L6316

000015c8 e0802001   3287 	add	r2,r0,r1

000015cc e2422008   3288 	sub	r2,r2,8

000015d0 e280c001   3289 	add	r12,r0,1

                    3290 .L6332:

000015d4 e5d27007   3291 	ldrb	r7,[r2,7]

000015d8 e7d03004   3292 	ldrb	r3,[r0,r4]

000015dc e7c07004   3293 	strb	r7,[r0,r4]

000015e0 e5d27006   3294 	ldrb	r7,[r2,6]

000015e4 e5c23007   3295 	strb	r3,[r2,7]

000015e8 e14c3097   3296 	swpb	r3,r7,[r12]

000015ec e2844008   3297 	add	r4,r4,8

000015f0 e5d27005   3298 	ldrb	r7,[r2,5]

000015f4 e5c23006   3299 	strb	r3,[r2,6]

000015f8 e5dc3001   3300 	ldrb	r3,[r12,1]

000015fc e5cc7001   3301 	strb	r7,[r12,1]

00001600 e5c23005   3302 	strb	r3,[r2,5]

00001604 e5d27004   3303 	ldrb	r7,[r2,4]

00001608 e5dc3002   3304 	ldrb	r3,[r12,2]

0000160c e5cc7002   3305 	strb	r7,[r12,2]

00001610 e5c23004   3306 	strb	r3,[r2,4]

00001614 e5d27003   3307 	ldrb	r7,[r2,3]

00001618 e5dc3003   3308 	ldrb	r3,[r12,3]

0000161c e5cc7003   3309 	strb	r7,[r12,3]

00001620 e5c23003   3310 	strb	r3,[r2,3]

00001624 e5d27002   3311 	ldrb	r7,[r2,2]

00001628 e5dc3004   3312 	ldrb	r3,[r12,4]

0000162c e5cc7004   3313 	strb	r7,[r12,4]

00001630 e5c23002   3314 	strb	r3,[r2,2]

00001634 e5d27001   3315 	ldrb	r7,[r2,1]

00001638 e5dc3005   3316 	ldrb	r3,[r12,5]

0000163c e5cc7005   3317 	strb	r7,[r12,5]

00001640 e5c23001   3318 	strb	r3,[r2,1]

00001644 e5d27000   3319 	ldrb	r7,[r2]

00001648 e5fc3006   3320 	ldrb	r3,[r12,6]!

0000164c e4cc7002   3321 	strb	r7,[r12],2

00001650 e4423008   3322 	strb	r3,[r2],-8

00001654 e2555001   3323 	subs	r5,r5,1

00001658 1affffdd   3324 	bne	.L6332

                    3325 .L6316:

0000165c e2165007   3326 	ands	r5,r6,7

00001660 0a000009   3327 	beq	.L6295

00001664 e0411004   3328 	sub	r1,r1,r4

00001668 e0811000   3329 	add	r1,r1,r0

0000166c e2411001   3330 	sub	r1,r1,1

                    3331 .L6336:

00001670 e5d12000   3332 	ldrb	r2,[r1]

00001674 e7d03004   3333 	ldrb	r3,[r0,r4]

00001678 e7c02004   3334 	strb	r2,[r0,r4]

0000167c e4413001   3335 	strb	r3,[r1],-1

00001680 e2844001   3336 	add	r4,r4,1

00001684 e2555001   3337 	subs	r5,r5,1

00001688 1afffff8   3338 	bne	.L6336

                    3339 .L6295:

0000168c e8bd00f0   3340 	ldmfd	[sp]!,{r4-r7}

00001690 e12fff1e*  3341 	ret	

                    3342 	.endf	BerEncoder_RevertByteOrder

                    3343 	.align	4

                    3344 ;i	r4	local


                                                                      Page 56
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                    3345 ;ucTemp	r3	local

                    3346 

                    3347 ;pOctets	r0	param

                    3348 ;iSize	r1	param

                    3349 

                    3350 	.section ".bss","awb"

                    3351 .L6489:

                    3352 	.data

                    3353 	.text

                    3354 

                    3355 ;895: 


                    3356 ;896:     }


                    3357 ;897: }


                    3358 

                    3359 ;898: 


                    3360 ;899: int IsIncluded(unsigned char* str1, unsigned char* str2)


                    3361 	.align	4

                    3362 	.align	4

                    3363 IsIncluded::

00001694 e92d40f0   3364 	stmfd	[sp]!,{r4-r7,lr}

                    3365 ;900: {


                    3366 

                    3367 ;901:     int min_len;


                    3368 ;902:     int len1, len2;


                    3369 ;903:     int i;


                    3370 ;904: 


                    3371 ;905:     len1 = strlen((char*)str1);


                    3372 

00001698 e1a04001   3373 	mov	r4,r1

0000169c e1a05000   3374 	mov	r5,r0

000016a0 eb000000*  3375 	bl	strlen

000016a4 e1a06000   3376 	mov	r6,r0

                    3377 ;906:     len2 = strlen((char*)str2);


                    3378 

000016a8 e1a00004   3379 	mov	r0,r4

000016ac eb000000*  3380 	bl	strlen

                    3381 ;907: 


                    3382 ;908:     min_len = len1 < len2 ? len1 : len2;


                    3383 

000016b0 e3a02000   3384 	mov	r2,0

000016b4 e1500006   3385 	cmp	r0,r6

000016b8 c1a00006   3386 	movgt	r0,r6

                    3387 ;909: 


                    3388 ;910:     for ( i = 0; i < min_len; i++) {


                    3389 

000016bc e3500000   3390 	cmp	r0,0

000016c0 a1a0c000   3391 	movge	r12,r0

000016c4 b3a0c000   3392 	movlt	r12,0

000016c8 e1b031ac   3393 	movs	r3,r12 lsr 3

000016cc 0a00001f   3394 	beq	.L6546

000016d0 e2840001   3395 	add	r0,r4,1

000016d4 e2851001   3396 	add	r1,r5,1

                    3397 .L6547:

000016d8 e7d47002   3398 	ldrb	r7,[r4,r2]

000016dc e7d56002   3399 	ldrb	r6,[r5,r2]

000016e0 e1560007   3400 	cmp	r6,r7

000016e4 05d07000   3401 	ldreqb	r7,[r0]

000016e8 05d16000   3402 	ldreqb	r6,[r1]

000016ec 01560007   3403 	cmpeq	r6,r7

000016f0 05d07001   3404 	ldreqb	r7,[r0,1]

000016f4 05d16001   3405 	ldreqb	r6,[r1,1]


                                                                      Page 57
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
000016f8 01560007   3406 	cmpeq	r6,r7

000016fc 05d07002   3407 	ldreqb	r7,[r0,2]

00001700 05d16002   3408 	ldreqb	r6,[r1,2]

00001704 01560007   3409 	cmpeq	r6,r7

00001708 05d07003   3410 	ldreqb	r7,[r0,3]

0000170c 05d16003   3411 	ldreqb	r6,[r1,3]

00001710 01560007   3412 	cmpeq	r6,r7

00001714 05d07004   3413 	ldreqb	r7,[r0,4]

00001718 05d16004   3414 	ldreqb	r6,[r1,4]

0000171c 01560007   3415 	cmpeq	r6,r7

00001720 05d07005   3416 	ldreqb	r7,[r0,5]

00001724 05d16005   3417 	ldreqb	r6,[r1,5]

00001728 01560007   3418 	cmpeq	r6,r7

0000172c 05d07006   3419 	ldreqb	r7,[r0,6]

00001730 05d16006   3420 	ldreqb	r6,[r1,6]

00001734 01560007   3421 	cmpeq	r6,r7

00001738 1a000009   3422 	bne	.L6582

0000173c e2800008   3423 	add	r0,r0,8

00001740 e2811008   3424 	add	r1,r1,8

00001744 e2822008   3425 	add	r2,r2,8

00001748 e2533001   3426 	subs	r3,r3,1

0000174c 1affffe1   3427 	bne	.L6547

                    3428 .L6546:

00001750 e21c3007   3429 	ands	r3,r12,7

00001754 0a000007   3430 	beq	.L6515

                    3431 .L6581:

00001758 e7d41002   3432 	ldrb	r1,[r4,r2]

0000175c e7d50002   3433 	ldrb	r0,[r5,r2]

00001760 e1500001   3434 	cmp	r0,r1

                    3435 .L6582:

00001764 13a00000   3436 	movne	r0,0

00001768 1a000003   3437 	bne	.L6513

                    3438 .L6584:

0000176c e2822001   3439 	add	r2,r2,1

00001770 e2533001   3440 	subs	r3,r3,1

00001774 1afffff7   3441 	bne	.L6581

                    3442 .L6515:

                    3443 ;913:         }


                    3444 ;914:     }


                    3445 ;915:     return 1;


                    3446 

00001778 e3a00001   3447 	mov	r0,1

                    3448 .L6513:

0000177c e8bd80f0   3449 	ldmfd	[sp]!,{r4-r7,pc}

                    3450 	.endf	IsIncluded

                    3451 	.align	4

                    3452 ;min_len	r0	local

                    3453 ;len1	r6	local

                    3454 ;i	r2	local

                    3455 

                    3456 ;str1	r5	param

                    3457 ;str2	r4	param

                    3458 

                    3459 	.section ".bss","awb"

                    3460 .L6817:

                    3461 	.data

                    3462 	.text

                    3463 

                    3464 ;916: }


                    3465 

                    3466 ;917: 



                                                                      Page 58
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_9ik1.s
                    3467 ;918: float BerDecoder_decodeFloat(unsigned char* buffer, int bufPos)


                    3468 	.align	4

                    3469 	.align	4

                    3470 BerDecoder_decodeFloat::

00001780 e24dd004   3471 	sub	sp,sp,4

00001784 e2811001   3472 	add	r1,r1,1

00001788 e7d02001   3473 	ldrb	r2,[r0,r1]

0000178c e2811001   3474 	add	r1,r1,1

00001790 e5cd2003   3475 	strb	r2,[sp,3]

00001794 e7d02001   3476 	ldrb	r2,[r0,r1]

00001798 e2811001   3477 	add	r1,r1,1

0000179c e5cd2002   3478 	strb	r2,[sp,2]

000017a0 e7d02001   3479 	ldrb	r2,[r0,r1]

000017a4 e2811001   3480 	add	r1,r1,1

000017a8 e5cd2001   3481 	strb	r2,[sp,1]

000017ac e7d00001   3482 	ldrb	r0,[r0,r1]

000017b0 e5cd0000   3483 	strb	r0,[sp]

                    3484 ;919: {


                    3485 

                    3486 ;920:     float value;


                    3487 ;921:     unsigned char* valueBuf = (unsigned char*)&value;


                    3488 

                    3489 ;922: 


                    3490 ;923:     int i;


                    3491 ;924: 


                    3492 ;925:     bufPos += 1; /* skip exponentWidth field */


                    3493 

                    3494 ;926: 


                    3495 ;927:     for (i = 3; i >= 0; i--) {


                    3496 

                    3497 ;929:     }


                    3498 ;930: 


                    3499 ;931:     return value;


                    3500 

000017b4 e59d0000   3501 	ldr	r0,[sp]

000017b8 e28dd004   3502 	add	sp,sp,4

000017bc e12fff1e*  3503 	ret	

                    3504 	.endf	BerDecoder_decodeFloat

                    3505 	.align	4

                    3506 ;value	[sp]	local

                    3507 

                    3508 ;buffer	r0	param

                    3509 ;bufPos	r1	param

                    3510 

                    3511 	.section ".bss","awb"

                    3512 .L6904:

                    3513 	.data

                    3514 	.text

                    3515 

                    3516 ;932: }


                    3517 	.align	4

                    3518 

                    3519 	.data

                    3520 	.ghsnote version,6

                    3521 	.ghsnote tools,3

                    3522 	.ghsnote options,0

                    3523 	.text

                    3524 	.align	4

