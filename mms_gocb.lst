                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_av01.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_gocb.c -o gh_av01.o -list=mms_gocb.lst C:\Users\<USER>\AppData\Local\Temp\gh_av01.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_av01.s
Source File: mms_gocb.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile mms_gocb.c -o

                      11 ;		mms_gocb.o

                      12 ;Source File:   mms_gocb.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:56 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "mms_gocb.h"


                      23 ;2: #include "IEDCompile/AccessInfo.h"


                      24 ;3: #include "IEDCompile/InnerAttributeTypes.h"


                      25 ;4: #include "iedmodel.h"


                      26 ;5: #include "mms_data.h"


                      27 ;6: #include "goose.h"


                      28 ;7: #include "BaseAsnTypes.h"


                      29 ;8: #include <stddef.h>


                      30 ;9: 


                      31 ;10: //Комментарий для проверки кодировки


                      32 ;11: 


                      33 ;12: int encodeAccessAttrGoCB(uint8_t* outBuf, int bufPos, int accessDataPos,


                      34 ;13: 	bool determineSize)


                      35 ;14: {


                      36 ;15: 	CBAttrAccessInfo* pAccessInfo =


                      37 ;16: 		(CBAttrAccessInfo*)getAlignedDescrStruct(accessDataPos);


                      38 ;17: 	if (pAccessInfo == NULL)


                      39 ;18: 	{


                      40 ;19: 		ERROR_REPORT("Unable to get access info struct");


                      41 ;20: 		return 0;


                      42 ;21: 	}


                      43 ;22: 


                      44 ;23: 	switch (pAccessInfo->attrCode)


                      45 ;24: 	{


                      46 ;25: 	case GoEna:	


                      47 ;26: 	case NdsCom:


                      48 ;27: 		return encodeAccessAttrBoolean(outBuf, bufPos, determineSize);	


                      49 ;28: 	default:


                      50 ;29: 		ERROR_REPORT("Invalid GoCB DA code");



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_av01.s
                      51 ;30: 		return 0;


                      52 ;31: 	}


                      53 ;32: }


                      54 ;33: 


                      55 ;34: static int encodeReadGoEna(uint8_t* outBuf, int bufPos,


                      56 

                      57 ;42: }


                      58 

                      59 ;43: 


                      60 ;44: static int encodeReadNdsCom(uint8_t* outBuf, int bufPos,


                      61 

                      62 ;51: }


                      63 

                      64 ;52: 


                      65 ;53: int encodeReadAttrGoCB(uint8_t* outBuf, int bufPos, void* descrStruct, bool determineSize)


                      66 ;54: {


                      67 ;55: 	CBAttrAccessInfo* pAccessInfo = descrStruct;


                      68 ;56: 


                      69 ;57: 	switch (pAccessInfo->attrCode) {


                      70 ;58: 	case GoEna:


                      71 ;59: 		return encodeReadGoEna(outBuf, bufPos, pAccessInfo, determineSize);


                      72 ;60: 	case NdsCom:


                      73 ;61: 		return encodeReadNdsCom(outBuf, bufPos, pAccessInfo, determineSize);


                      74 ;62: 	default:


                      75 ;63: 		ERROR_REPORT("Invalid  GoCB DA code");


                      76 ;64: 		return 0;


                      77 ;65: 	}


                      78 ;66: }


                      79 ;67: 


                      80 ;68: static void writeGoEna(int cbIndex, uint8_t* dataToWrite)


                      81 

                      82 ;79: }


                      83 

                      84 	.text

                      85 	.align	4

                      86 encodeAccessAttrGoCB::

00000000 e92d4070     87 	stmfd	[sp]!,{r4-r6,lr}

00000004 e1a05001     88 	mov	r5,r1

00000008 e1a06003     89 	mov	r6,r3

0000000c e1a04000     90 	mov	r4,r0

00000010 e1a00002     91 	mov	r0,r2

00000014 eb000000*    92 	bl	getAlignedDescrStruct

00000018 e3500000     93 	cmp	r0,0

0000001c 0a000007     94 	beq	.L64

00000020 e5900004     95 	ldr	r0,[r0,4]

00000024 e2500001     96 	subs	r0,r0,1

00000028 e3500001     97 	cmp	r0,1

0000002c 91a02006     98 	movls	r2,r6

00000030 91a01005     99 	movls	r1,r5

00000034 91a00004    100 	movls	r0,r4

00000038 98bd4070    101 	ldmlsfd	[sp]!,{r4-r6,lr}

0000003c 9a000000*   102 	bls	encodeAccessAttrBoolean

                     103 .L64:

00000040 e3a00000    104 	mov	r0,0

00000044 e8bd8070    105 	ldmfd	[sp]!,{r4-r6,pc}

                     106 	.endf	encodeAccessAttrGoCB

                     107 	.align	4

                     108 ;pAccessInfo	r0	local

                     109 

                     110 ;outBuf	r4	param

                     111 ;bufPos	r5	param


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_av01.s
                     112 ;accessDataPos	r2	param

                     113 ;determineSize	r6	param

                     114 

                     115 	.section ".bss","awb"

                     116 .L124:

                     117 	.data

                     118 	.text

                     119 

                     120 

                     121 	.align	4

                     122 	.align	4

                     123 encodeReadAttrGoCB::

00000048 e92d4070    124 	stmfd	[sp]!,{r4-r6,lr}

0000004c e24dd004    125 	sub	sp,sp,4

00000050 e1a05000    126 	mov	r5,r0

00000054 e5920004    127 	ldr	r0,[r2,4]

00000058 e1a06001    128 	mov	r6,r1

0000005c e2500001    129 	subs	r0,r0,1

00000060 0a000003    130 	beq	.L145

00000064 e3500001    131 	cmp	r0,1

00000068 13a00000    132 	movne	r0,0

0000006c 1a000013    133 	bne	.L141

00000070 ea000009    134 	b	.L151

                     135 .L145:

00000074 e1a04003    136 	mov	r4,r3

                     137 ;35: 	CBAttrAccessInfo* descrStruct, bool determineSize)


                     138 ;36: {


                     139 

                     140 ;37: 	size_t cbIndex = descrStruct->rcbIndex;


                     141 

00000078 e5920008    142 	ldr	r0,[r2,8]

                     143 ;38: 	bool value;


                     144 ;39: 	GOOSE_getGoEna(cbIndex, &value);


                     145 

0000007c e28d1002    146 	add	r1,sp,2

00000080 eb000000*   147 	bl	GOOSE_getGoEna

                     148 ;40: 


                     149 ;41: 	return encodeBoolValue(outBuf, bufPos, value, determineSize);


                     150 

00000084 e1a03004    151 	mov	r3,r4

00000088 e5dd2002    152 	ldrb	r2,[sp,2]

0000008c e1a01006    153 	mov	r1,r6

00000090 e1a00005    154 	mov	r0,r5

00000094 eb000000*   155 	bl	encodeBoolValue

00000098 ea000008    156 	b	.L141

                     157 .L151:

0000009c e1a04003    158 	mov	r4,r3

                     159 ;45: 	CBAttrAccessInfo* descrStruct, bool determineSize)


                     160 ;46: {


                     161 

                     162 ;47: 	size_t cbIndex = descrStruct->rcbIndex;


                     163 

000000a0 e5920008    164 	ldr	r0,[r2,8]

                     165 ;48: 	bool value;


                     166 ;49: 	GOOSE_getNdsCom(cbIndex, &value);


                     167 

000000a4 e28d1003    168 	add	r1,sp,3

000000a8 eb000000*   169 	bl	GOOSE_getNdsCom

                     170 ;50: 	return encodeBoolValue(outBuf, bufPos, value, determineSize);


                     171 

000000ac e1a03004    172 	mov	r3,r4


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_av01.s
000000b0 e5dd2003    173 	ldrb	r2,[sp,3]

000000b4 e1a01006    174 	mov	r1,r6

000000b8 e1a00005    175 	mov	r0,r5

000000bc eb000000*   176 	bl	encodeBoolValue

                     177 .L141:

000000c0 e28dd004    178 	add	sp,sp,4

000000c4 e8bd8070    179 	ldmfd	[sp]!,{r4-r6,pc}

                     180 	.endf	encodeReadAttrGoCB

                     181 	.align	4

                     182 ;pAccessInfo	r2	local

                     183 ;determineSize	r4	local

                     184 ;value	[sp,2]	local

                     185 ;determineSize	r4	local

                     186 ;value	[sp,3]	local

                     187 

                     188 ;outBuf	r5	param

                     189 ;bufPos	r6	param

                     190 ;descrStruct	r2	param

                     191 ;determineSize	r3	param

                     192 

                     193 	.section ".bss","awb"

                     194 .L192:

                     195 	.data

                     196 	.text

                     197 

                     198 

                     199 ;80: 


                     200 ;81: void writeAttrGoCB(void* descrStruct, uint8_t* dataToWrite)


                     201 	.align	4

                     202 	.align	4

                     203 writeAttrGoCB::

                     204 ;82: {


                     205 

                     206 ;83: 	CBAttrAccessInfo* pAccessInfo = descrStruct;


                     207 

                     208 ;84: 


                     209 ;85: 	switch (pAccessInfo->attrCode)


                     210 

000000c8 e5902004    211 	ldr	r2,[r0,4]

000000cc e3520001    212 	cmp	r2,1

                     213 ;86: 	{


                     214 ;87: 	case GoEna:


                     215 ;88: 		writeGoEna(pAccessInfo->rcbIndex, dataToWrite);


                     216 

000000d0 05d12000    217 	ldreqb	r2,[r1]

000000d4 05900008    218 	ldreq	r0,[r0,8]

                     219 ;69: {


                     220 

                     221 ;70: 	bool value;


                     222 ;71: 	


                     223 ;72: 	if (dataToWrite[0] != IEC61850_BER_BOOLEAN || dataToWrite[1] != 1)


                     224 

000000d8 03520083    225 	cmpeq	r2,131

000000dc 05d12001    226 	ldreqb	r2,[r1,1]

000000e0 03520001    227 	cmpeq	r2,1

000000e4 1a000003    228 	bne	.L209

                     229 ;73: 	{


                     230 

                     231 ;74: 		return;


                     232 

                     233 ;75: 	}



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_av01.s
                     234 ;76: 	value = dataToWrite[2] != 0;


                     235 

000000e8 e5d11002    236 	ldrb	r1,[r1,2]

000000ec e3510000    237 	cmp	r1,0

000000f0 13a01001    238 	movne	r1,1

                     239 ;77: 	


                     240 ;78: 	GOOSE_setGoEna(cbIndex, value);


                     241 

000000f4 ea000000*   242 	b	GOOSE_setGoEna

                     243 .L209:

000000f8 e12fff1e*   244 	ret	

                     245 	.endf	writeAttrGoCB

                     246 	.align	4

                     247 ;pAccessInfo	r0	local

                     248 ;cbIndex	r0	local

                     249 ;value	r1	local

                     250 

                     251 ;descrStruct	r0	param

                     252 ;dataToWrite	r1	param

                     253 

                     254 	.section ".bss","awb"

                     255 .L262:

                     256 	.data

                     257 	.text

                     258 

                     259 ;89: 		break;


                     260 ;90: 	default:


                     261 ;91: 		ERROR_REPORT("Unsupported GoCB attribute");


                     262 ;92: 	}


                     263 ;93: 


                     264 ;94: }


                     265 	.align	4

                     266 

                     267 	.data

                     268 	.ghsnote version,6

                     269 	.ghsnote tools,3

                     270 	.ghsnote options,0

                     271 	.text

                     272 	.align	4

