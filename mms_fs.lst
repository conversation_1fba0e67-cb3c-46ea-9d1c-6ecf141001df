                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_fs.c -o gh_adg1.o -list=mms_fs.lst C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
Source File: mms_fs.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile mms_fs.c -o

                      11 ;		mms_fs.o

                      12 ;Source File:   mms_fs.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:05 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "mms_fs.h"


                      23 ;2: #include "stringView.h"


                      24 ;3: 


                      25 ;4: #include "BaseAsnTypes.h"


                      26 ;5: #include "AsnEncoding.h"


                      27 ;6: #include "file_system.h"


                      28 ;7: #include "tools.h"


                      29 ;8: #include "local_types.h"


                      30 ;9: #include "bufViewBER.h"


                      31 ;10: #include <stddef.h>


                      32 ;11: #include <stdio.h>


                      33 ;12: #include "timetools.h"


                      34 ;13: //Файловые операции MMS


                      35 ;14: 


                      36 ;15: #define MMS_DIRECTORY_NAME 0xA0


                      37 ;16: #define MMS_FILE_NAME 0xA1


                      38 ;17: 


                      39 ;18: //GeneralizedTime


                      40 ;19: // год, месяц(1 - 12), число(1 - 32), часы, минуты, cекунды, точка, доли секунды, Z


                      41 ;20: // strlen("19851106210627.123Z") + 1 = 20


                      42 ;21: #define GENERZLISED_TIME_SIZE	(sizeof(GeneralizedTime_t) -1 ) // размер без '0'


                      43 ;22: typedef char GeneralizedTime_t[20];


                      44 ;23: 


                      45 ;24: 


                      46 ;25: // Информация о длине закодированных атрибутов файла


                      47 ;26: typedef struct {


                      48 ;27:     size_t timeLen;


                      49 ;28:     size_t sizeLen;


                      50 ;29:     //Полный размер закодированных атрибутов файла включая тэг и длину



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
                      51 ;30:     size_t fullAttrLen;//A1


                      52 ;31: }  EncodedAttrLen;


                      53 ;32: 


                      54 ;33: // Информация для записи заголовка ответа на файловую операцию


                      55 ;34: typedef struct {


                      56 ;35:     uint32_t invokeID;


                      57 ;36:     //Двухбайтный тэг big endian


                      58 ;37:     uint16_t extTag;


                      59 ;38:     //Размер всего ответа внутри тэга A1, включая invokeID


                      60 ;39:     size_t responseLen;


                      61 ;40:     //Размер данных внутри расширенного тэга (BF 4X)


                      62 ;41:     size_t dataLen;


                      63 ;42: } FileRespHeadInfo;


                      64 ;43: 


                      65 ;44: 


                      66 ;45: 


                      67 ;46: static void determineEncodedAttrLen(EncodedAttrLen* encAttrLen, size_t fileSize)


                      68 ;47: {


                      69 ;48:     encAttrLen->timeLen = 2 //тэг + длина


                      70 ;49:         + GENERZLISED_TIME_SIZE;


                      71 ;50:     encAttrLen->sizeLen = 2 //тэг + длина


                      72 ;51:         + BerEncoder_UInt32determineEncodedSize(fileSize);


                      73 ;52:     encAttrLen->fullAttrLen = BerEncoder_determineFullObjectSize(


                      74 ;53:         encAttrLen->sizeLen + encAttrLen->timeLen);


                      75 ;54: }


                      76 ;55: 


                      77 ;56: // Определяет размер ответа на операцию с файлом,


                      78 ;57: // включая размер данных, двухбайтный тэг, invokeID.


                      79 ;58: // Исключая корневой тэг(A1) и его размер.


                      80 ;59: static size_t determineResponseLen(uint32_t invokeID, size_t responseDataLen)


                      81 ;60: {


                      82 ;61:     return BerEncoder_UInt32determineEncodedSize(invokeID)


                      83 ;62:         + 2 //Тэг и длина InvokeID


                      84 ;63:         + BerEncoder_determineFullObjectSize(responseDataLen)


                      85 ;64:         + 1; //Ещё один байт поскольку тэг двухбайтовый (BF 4D)


                      86 ;65: }


                      87 ;66: 


                      88 ;67: static bool makeGeneralizedTime(FSFileAttr* attr, GeneralizedTime_t generalizedTime)


                      89 

                      90 ;85: }


                      91 

                      92 ;86: static bool encodeFileAttr(BufferView* outBuf, EncodedAttrLen* encAttrLen,


                      93 ;87:     FSFileAttr* attr)


                      94 ;88: {


                      95 ;89:     GeneralizedTime_t generalizedTime;


                      96 ;90:     //Атрибуты


                      97 ;91:     if (!BufferView_encodeTL(outBuf, 0xA1,


                      98 ;92:         encAttrLen->sizeLen + encAttrLen->timeLen))


                      99 ;93:     {


                     100 ;94:         return FALSE;


                     101 ;95:     }


                     102 ;96:     //Размер


                     103 ;97:     if (!BufferView_encodeUInt32(outBuf, 0x80, attr->fileSize))


                     104 ;98:     {


                     105 ;99:         return FALSE;


                     106 ;100:     }


                     107 ;101: 


                     108 ;102:     //Время


                     109 ;103:     if (!makeGeneralizedTime(attr, generalizedTime))


                     110 ;104:     {


                     111 ;105:         return FALSE;



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
                     112 ;106:     }


                     113 ;107: 


                     114 ;108:     if (!BufferView_encodeOctetString(outBuf, 0x81, generalizedTime,


                     115 ;109:         GENERZLISED_TIME_SIZE))


                     116 ;110:     {


                     117 ;111:         return FALSE;


                     118 ;112:     }


                     119 ;113:     return TRUE;


                     120 ;114: }


                     121 ;115: 


                     122 ;116: static bool encodeFileInfo(FSFindData* fileInfo, BufferView* outBuf)


                     123 

                     124 ;164: }


                     125 

                     126 ;165: 


                     127 ;166: static bool encodeFileResponseHead(BufferView* outBuf,


                     128 ;167:     FileRespHeadInfo* headerInfo)


                     129 ;168: {


                     130 ;169:     if (!BufferView_encodeTL(outBuf, 0xA1, headerInfo->responseLen))


                     131 ;170:     {


                     132 ;171:         return FALSE;


                     133 ;172:     }


                     134 ;173:     //InvokeID


                     135 ;174:     if (!BufferView_encodeUInt32(outBuf, ASN_INTEGER, headerInfo->invokeID))


                     136 ;175:     {


                     137 ;176:         return FALSE;


                     138 ;177:     }


                     139 ;178:     if (!BufferView_encodeExtTL(outBuf, headerInfo->extTag,


                     140 ;179:         headerInfo->dataLen))


                     141 ;180:     {


                     142 ;181:         return FALSE;


                     143 ;182:     }


                     144 ;183:     return TRUE;


                     145 ;184: }


                     146 ;185: 


                     147 ;186: //


                     148 ;187: static bool fileList(MmsConnection* mmsConn, StringView* dirName,


                     149 

                     150 ;235: }


                     151 

                     152 ;236: 


                     153 ;237: // Создаёт ответ на запрос списка файлов


                     154 ;238: static bool encodeFileDirRequest(MmsConnection* mmsConn, uint32_t invokeID,


                     155 

                     156 ;303: }


                     157 

                     158 ;304: 


                     159 ;305: bool mms_handleFileDirRequest(MmsConnection* mmsConn, BufferView* inBuf,


                     160 ;306:     unsigned int invokeId, BufferView* outBuf)


                     161 ;307: {


                     162 ;308:     uint8_t tag;


                     163 ;309:     int length;


                     164 ;310:     StringView dir;


                     165 ;311:     StringView startFile;


                     166 ;312:     bool thereIsStartFile = FALSE;


                     167 ;313:     StringView_fromCStr(&dir, "/");


                     168 ;314: 


                     169 ;315:     while (inBuf->pos < inBuf->len)


                     170 ;316:     {


                     171 ;317:         bool result = BerDecoder_decodeTLFromBufferView(inBuf, &tag, &length, NULL);


                     172 ;318:         RET_IF_NOT(result, "Error reading tag and length");



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
                     173 ;319:         switch (tag) {


                     174 ;320:         case MMS_DIRECTORY_NAME:


                     175 ;321:             result = BerDecoder_decodeTLFromBufferView(inBuf, &tag, &length, NULL);


                     176 ;322:             RET_IF_NOT(result, "Error reading tag and length");


                     177 ;323:             RET_IF_NOT(tag == ASN_GRAPHIC_STRING, "Unexpected tag");


                     178 ;324:             StringView_init(&dir, (char*)inBuf->p + inBuf->pos, length);


                     179 ;325:             inBuf->pos += length;


                     180 ;326:             break;


                     181 ;327:         case MMS_FILE_NAME:


                     182 ;328:             result = BerDecoder_decodeTLFromBufferView(inBuf, &tag, &length, NULL);


                     183 ;329:             RET_IF_NOT(result, "Error reading tag and length");


                     184 ;330:             RET_IF_NOT(tag == ASN_GRAPHIC_STRING, "Unexpected tag");


                     185 ;331:             StringView_init(&startFile, (char*)inBuf->p + inBuf->pos, length);


                     186 ;332:             inBuf->pos += length;


                     187 ;333:             thereIsStartFile = TRUE;


                     188 ;334:             break;


                     189 ;335:         default:


                     190 ;336:             ERROR_REPORT("Unexpectded tag");


                     191 ;337:             return FALSE;


                     192 ;338:         }


                     193 ;339:     }


                     194 ;340:     RET_IF_NOT(dir.p != NULL, "Directory name is not found");


                     195 ;341: 


                     196 ;342:     //dir содержит путь к запрошенному директорию


                     197 ;343:     return encodeFileDirRequest(mmsConn, invokeId, &dir,


                     198 ;344:         thereIsStartFile? &startFile: NULL,


                     199 ;345:         outBuf);


                     200 ;346: }


                     201 ;347: 


                     202 ;348: 


                     203 ;349: static bool encodeFileOpenRequest(MmsConnection* mmsConn, uint32_t invokeID,


                     204 

                     205 ;402: }


                     206 

                     207 ;403: 


                     208 ;404: bool mms_handleFileOpenRequest(MmsConnection* mmsConn, BufferView* inBuf,


                     209 ;405:     unsigned int invokeId, BufferView* outBuf)


                     210 ;406: {


                     211 ;407:     /*


                     212 ;408:     A0


                     213 ;409:         19 Имя файла


                     214 ;410:     81 Начальная позиция


                     215 ;411:     */


                     216 ;412:     uint8_t tag;


                     217 ;413:     int length;


                     218 ;414:     StringView fileName;


                     219 ;415:     size_t startPos;


                     220 ;416: 


                     221 ;417:     bool result = BerDecoder_decodeTLFromBufferView(inBuf, &tag, &length, NULL);


                     222 ;418:     RET_IF_NOT(result, "Error reading tag and length");


                     223 ;419:     RET_IF_NOT(tag == 0xA0, "Unexpected tag");


                     224 ;420:     //Имя файла


                     225 ;421:     result = BerDecoder_decodeTLFromBufferView(inBuf, &tag, &length, NULL);


                     226 ;422:     RET_IF_NOT(result, "Error reading file name tag and length");


                     227 ;423:     RET_IF_NOT(tag == ASN_GRAPHIC_STRING, "Unexpected tag");


                     228 ;424:     //Создаём StringView на имя файла


                     229 ;425:     result = BufferView_readStringView(inBuf, length, &fileName);


                     230 ;426:     RET_IF_NOT(result, "Error reading file Name");


                     231 ;427:     //Начальная позиция


                     232 ;428:     result = BerDecoder_decodeTLFromBufferView(inBuf, &tag, &length, NULL);


                     233 ;429:     RET_IF_NOT(result, "Error reading position tag and length");



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
                     234 ;430:     result = BufferView_decodeUInt32(inBuf, length, (uint32_t*)&startPos);


                     235 ;431:     RET_IF_NOT(result, "Error reading start position");


                     236 ;432: 


                     237 ;433:     return encodeFileOpenRequest(mmsConn, invokeId,


                     238 ;434:         &fileName, startPos, outBuf);


                     239 ;435: }


                     240 ;436: 


                     241 ;437: 


                     242 ;438: static bool encodeFileReadRequest(MmsConnection* mmsConn, uint32_t invokeID,


                     243 

                     244 ;483: }


                     245 

                     246 ;484: 


                     247 ;485: bool mms_handleFileReadRequest(MmsConnection* mmsConn, BufferView* inBuf,


                     248 ;486:     unsigned int invokeID, BufferView* outBuf)


                     249 ;487: {


                     250 ;488:     /*


                     251 ;489:         inBuf содержит только frsmID


                     252 ;490:     */


                     253 ;491:     uint32_t frsmID;


                     254 ;492:     bool result = BufferView_decodeUInt32(inBuf, inBuf->len, &frsmID);


                     255 ;493:     RET_IF_NOT(result, "Error reading FRSM ID");


                     256 ;494:     return encodeFileReadRequest(mmsConn, invokeID, frsmID, outBuf);


                     257 ;495: }


                     258 ;496: 


                     259 ;497: static bool encodeFileCloseRequest(MmsConnection* mmsConn, uint32_t invokeID,


                     260 

                     261 ;519: }


                     262 

                     263 	.text

                     264 	.align	4

                     265 determineEncodedAttrLen:

00000000 e92d4010    266 	stmfd	[sp]!,{r4,lr}

00000004 e1a04000    267 	mov	r4,r0

00000008 e3a00015    268 	mov	r0,21

0000000c e5840000    269 	str	r0,[r4]

00000010 e1a00001    270 	mov	r0,r1

00000014 eb000000*   271 	bl	BerEncoder_UInt32determineEncodedSize

00000018 e2800002    272 	add	r0,r0,2

0000001c e5941000    273 	ldr	r1,[r4]

00000020 e5840004    274 	str	r0,[r4,4]

00000024 e0800001    275 	add	r0,r0,r1

00000028 eb000000*   276 	bl	BerEncoder_determineFullObjectSize

0000002c e5840008    277 	str	r0,[r4,8]

00000030 e8bd4010    278 	ldmfd	[sp]!,{r4,lr}

00000034 e12fff1e*   279 	ret	

                     280 	.endf	determineEncodedAttrLen

                     281 	.align	4

                     282 

                     283 ;encAttrLen	r4	param

                     284 ;fileSize	r1	param

                     285 

                     286 	.section ".bss","awb"

                     287 .L334:

                     288 	.data

                     289 	.text

                     290 

                     291 

                     292 	.align	4

                     293 	.align	4

                     294 determineResponseLen:


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
00000038 e92d4030    295 	stmfd	[sp]!,{r4-r5,lr}

0000003c e1a04001    296 	mov	r4,r1

00000040 eb000000*   297 	bl	BerEncoder_UInt32determineEncodedSize

00000044 e1a05000    298 	mov	r5,r0

00000048 e1a00004    299 	mov	r0,r4

0000004c eb000000*   300 	bl	BerEncoder_determineFullObjectSize

00000050 e0800005    301 	add	r0,r0,r5

00000054 e2800003    302 	add	r0,r0,3

00000058 e8bd4030    303 	ldmfd	[sp]!,{r4-r5,lr}

0000005c e12fff1e*   304 	ret	

                     305 	.endf	determineResponseLen

                     306 	.align	4

                     307 

                     308 ;invokeID	none	param

                     309 ;responseDataLen	r4	param

                     310 

                     311 	.section ".bss","awb"

                     312 .L366:

                     313 	.data

                     314 	.text

                     315 

                     316 

                     317 	.align	4

                     318 	.align	4

                     319 	.align	4

                     320 encodeFileAttr:

00000060 e92d4030    321 	stmfd	[sp]!,{r4-r5,lr}

00000064 e24dd054    322 	sub	sp,sp,84

00000068 e1a04002    323 	mov	r4,r2

0000006c e891000c    324 	ldmfd	[r1],{r2-r3}

00000070 e1a05000    325 	mov	r5,r0

00000074 e0822003    326 	add	r2,r2,r3

00000078 e3a010a1    327 	mov	r1,161

0000007c eb000000*   328 	bl	BufferView_encodeTL

00000080 e3500000    329 	cmp	r0,0

00000084 0a000025    330 	beq	.L391

00000088 e5942000    331 	ldr	r2,[r4]

0000008c e1a00005    332 	mov	r0,r5

00000090 e3a01080    333 	mov	r1,128

00000094 eb000000*   334 	bl	BufferView_encodeUInt32

00000098 e3500000    335 	cmp	r0,0

0000009c 0a00001f    336 	beq	.L391

                     337 ;68: {


                     338 

                     339 ;69:     struct tm tmTime;


                     340 ;70:     __time32_t t = attr->time;


                     341 

000000a0 e5940004    342 	ldr	r0,[r4,4]

000000a4 e28d1018    343 	add	r1,sp,24

000000a8 e58d0018    344 	str	r0,[sp,24]

000000ac e28d001c    345 	add	r0,sp,28

000000b0 eb000000*   346 	bl	TimeTools_gmtime32

                     347 ;71: 


                     348 ;72:     if (!TimeTools_gmtime32(&tmTime, &t))


                     349 

000000b4 e3500000    350 	cmp	r0,0

000000b8 0a000018    351 	beq	.L391

000000bc e28d0028    352 	add	r0,sp,40

000000c0 e8905004    353 	ldmfd	[r0],{r2,r12,lr}

000000c4 e28c0001    354 	add	r0,r12,1

000000c8 e28e1e70    355 	add	r1,lr,7<<8


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
000000cc e281306c    356 	add	r3,r1,108

000000d0 e58d3030    357 	str	r3,[sp,48]

000000d4 e58d002c    358 	str	r0,[sp,44]

000000d8 e594c008    359 	ldr	r12,[r4,8]

000000dc e59d401c    360 	ldr	r4,[sp,28]

000000e0 e59d1020    361 	ldr	r1,[sp,32]

000000e4 e28de00c    362 	add	lr,sp,12

000000e8 e88e1012    363 	stmea	[lr],{r1,r4,r12}

000000ec e59d4024    364 	ldr	r4,[sp,36]

000000f0 e88d0015    365 	stmea	[sp],{r0,r2,r4}

000000f4 e59f2320*   366 	ldr	r2,.L530

000000f8 e28d0040    367 	add	r0,sp,64

000000fc e3a01014    368 	mov	r1,20

00000100 eb000000*   369 	bl	snprintf

                     370 ;73:     {


                     371 

                     372 ;74:         return false;


                     373 

                     374 ;75:     }


                     375 ;76: 


                     376 ;77:     tmTime.tm_year += 1900;


                     377 

                     378 ;78:     tmTime.tm_mon += 1;


                     379 

                     380 ;79:     snprintf(generalizedTime, sizeof(GeneralizedTime_t),


                     381 

                     382 ;80:         "%d%02d%02d%02d%02d%02d.%03dZ", // 19851106210627.300Z


                     383 ;81:         tmTime.tm_year, tmTime.tm_mon, tmTime.tm_mday,


                     384 ;82:         tmTime.tm_hour, tmTime.tm_min, tmTime.tm_sec, attr->ms);


                     385 ;83: 


                     386 ;84:     return true;


                     387 

00000104 e28d2040    388 	add	r2,sp,64

00000108 e1a00005    389 	mov	r0,r5

0000010c e3a03013    390 	mov	r3,19

00000110 e3a01081    391 	mov	r1,129

00000114 eb000000*   392 	bl	BufferView_encodeOctetString

00000118 e3500000    393 	cmp	r0,0

0000011c 13a00001    394 	movne	r0,1

                     395 .L391:

00000120 03a00000    396 	moveq	r0,0

                     397 .L373:

00000124 e28dd054    398 	add	sp,sp,84

00000128 e8bd4030    399 	ldmfd	[sp]!,{r4-r5,lr}

0000012c e12fff1e*   400 	ret	

                     401 	.endf	encodeFileAttr

                     402 	.align	4

                     403 ;generalizedTime	[sp,64]	local

                     404 ;tmTime	[sp,28]	local

                     405 ;t	[sp,24]	local

                     406 

                     407 ;outBuf	r5	param

                     408 ;encAttrLen	r1	param

                     409 ;attr	r4	param

                     410 

                     411 	.section ".bss","awb"

                     412 .L506:

                     413 	.section ".rodata","a"

                     414 .L507:

                     415 __UNNAMED_1_static_in_makeGeneralizedTime:;	"%d%02d%02d%02d%02d%02d.%03dZ\000"

00000000 30256425    416 	.data.b	37,100,37,48


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
00000004 30256432    417 	.data.b	50,100,37,48

00000008 30256432    418 	.data.b	50,100,37,48

0000000c 30256432    419 	.data.b	50,100,37,48

00000010 30256432    420 	.data.b	50,100,37,48

00000014 252e6432    421 	.data.b	50,100,46,37

00000018 5a643330    422 	.data.b	48,51,100,90

0000001c 00         423 	.data.b	0

0000001d 000000     424 	.space	3

                     425 	.type	__UNNAMED_1_static_in_makeGeneralizedTime,$object

                     426 	.size	__UNNAMED_1_static_in_makeGeneralizedTime,32

                     427 	.data

                     428 	.text

                     429 

                     430 

                     431 	.align	4

                     432 	.align	4

                     433 encodeFileResponseHead:

00000130 e92d4030    434 	stmfd	[sp]!,{r4-r5,lr}

00000134 e1a04001    435 	mov	r4,r1

00000138 e5942008    436 	ldr	r2,[r4,8]

0000013c e1a05000    437 	mov	r5,r0

00000140 e3a010a1    438 	mov	r1,161

00000144 eb000000*   439 	bl	BufferView_encodeTL

00000148 e3500000    440 	cmp	r0,0

0000014c 0a00000b    441 	beq	.L540

00000150 e5942000    442 	ldr	r2,[r4]

00000154 e1a00005    443 	mov	r0,r5

00000158 e3a01002    444 	mov	r1,2

0000015c eb000000*   445 	bl	BufferView_encodeUInt32

00000160 e3500000    446 	cmp	r0,0

00000164 0a000005    447 	beq	.L540

00000168 e594200c    448 	ldr	r2,[r4,12]

0000016c e1d410b4    449 	ldrh	r1,[r4,4]

00000170 e1a00005    450 	mov	r0,r5

00000174 eb000000*   451 	bl	BufferView_encodeExtTL

00000178 e3500000    452 	cmp	r0,0

0000017c 13a00001    453 	movne	r0,1

                     454 .L540:

00000180 03a00000    455 	moveq	r0,0

                     456 .L531:

00000184 e8bd4030    457 	ldmfd	[sp]!,{r4-r5,lr}

00000188 e12fff1e*   458 	ret	

                     459 	.endf	encodeFileResponseHead

                     460 	.align	4

                     461 

                     462 ;outBuf	r5	param

                     463 ;headerInfo	r4	param

                     464 

                     465 	.section ".bss","awb"

                     466 .L598:

                     467 	.data

                     468 	.text

                     469 

                     470 

                     471 	.align	4

                     472 	.align	4

                     473 mms_handleFileDirRequest::

0000018c e92d4cf4    474 	stmfd	[sp]!,{r2,r4-r7,r10-fp,lr}

00000190 e3a06000    475 	mov	r6,0

00000194 e24dd06c    476 	sub	sp,sp,108

00000198 e58d206c    477 	str	r2,[sp,108]


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
0000019c e1a0b003    478 	mov	fp,r3

000001a0 e1a07000    479 	mov	r7,r0

000001a4 e1a04001    480 	mov	r4,r1

000001a8 e28f1000*   481 	adr	r1,.L1287

000001ac e28d0064    482 	add	r0,sp,100

000001b0 eb000000*   483 	bl	StringView_fromCStr

000001b4 e28d5004    484 	add	r5,sp,4

000001b8 e9940003    485 	ldmed	[r4],{r0-r1}

000001bc e1500001    486 	cmp	r0,r1

000001c0 2a000038    487 	bhs	.L621

                     488 .L622:

000001c4 e1a02005    489 	mov	r2,r5

000001c8 e28d1003    490 	add	r1,sp,3

000001cc e1a00004    491 	mov	r0,r4

000001d0 e3a03000    492 	mov	r3,0

000001d4 eb000000*   493 	bl	BerDecoder_decodeTLFromBufferView

000001d8 e3500000    494 	cmp	r0,0

000001dc 0a000034    495 	beq	.L645

000001e0 e5dd0003    496 	ldrb	r0,[sp,3]

000001e4 e25000a0    497 	subs	r0,r0,160

000001e8 0a000002    498 	beq	.L628

000001ec e3500001    499 	cmp	r0,1

000001f0 0a000016    500 	beq	.L635

000001f4 ea00002e    501 	b	.L645

                     502 .L628:

000001f8 e1a02005    503 	mov	r2,r5

000001fc e28d1003    504 	add	r1,sp,3

00000200 e1a00004    505 	mov	r0,r4

00000204 e3a03000    506 	mov	r3,0

00000208 eb000000*   507 	bl	BerDecoder_decodeTLFromBufferView

0000020c e3500000    508 	cmp	r0,0

00000210 0a000027    509 	beq	.L645

00000214 e5dd0003    510 	ldrb	r0,[sp,3]

00000218 e3500019    511 	cmp	r0,25

0000021c 1a000024    512 	bne	.L645

00000220 e59d2004    513 	ldr	r2,[sp,4]

00000224 e894000a    514 	ldmfd	[r4],{r1,r3}

00000228 e28d0064    515 	add	r0,sp,100

0000022c e0831001    516 	add	r1,r3,r1

00000230 eb000000*   517 	bl	StringView_init

00000234 e9940005    518 	ldmed	[r4],{r0,r2}

00000238 e59d1004    519 	ldr	r1,[sp,4]

0000023c e0800001    520 	add	r0,r0,r1

00000240 e5840004    521 	str	r0,[r4,4]

00000244 e1500002    522 	cmp	r0,r2

00000248 3affffdd    523 	blo	.L622

0000024c ea000015    524 	b	.L621

                     525 .L635:

00000250 e1a02005    526 	mov	r2,r5

00000254 e28d1003    527 	add	r1,sp,3

00000258 e1a00004    528 	mov	r0,r4

0000025c e3a03000    529 	mov	r3,0

00000260 eb000000*   530 	bl	BerDecoder_decodeTLFromBufferView

00000264 e3500000    531 	cmp	r0,0

00000268 0a000011    532 	beq	.L645

0000026c e5dd0003    533 	ldrb	r0,[sp,3]

00000270 e3500019    534 	cmp	r0,25

00000274 1a00000e    535 	bne	.L645

00000278 e59d2004    536 	ldr	r2,[sp,4]

0000027c e894000a    537 	ldmfd	[r4],{r1,r3}

00000280 e28d005c    538 	add	r0,sp,92


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
00000284 e0831001    539 	add	r1,r3,r1

00000288 eb000000*   540 	bl	StringView_init

0000028c e9940005    541 	ldmed	[r4],{r0,r2}

00000290 e59d1004    542 	ldr	r1,[sp,4]

00000294 e3a06001    543 	mov	r6,1

00000298 e0800001    544 	add	r0,r0,r1

0000029c e5840004    545 	str	r0,[r4,4]

000002a0 e1500002    546 	cmp	r0,r2

000002a4 3affffc6    547 	blo	.L622

                     548 .L621:

000002a8 e59d0068    549 	ldr	r0,[sp,104]

000002ac e3500000    550 	cmp	r0,0

000002b0 1a000001    551 	bne	.L644

                     552 .L645:

000002b4 e3a00000    553 	mov	r0,0

000002b8 ea00008b    554 	b	.L618

                     555 .L644:

000002bc e3560000    556 	cmp	r6,0

000002c0 e2870c64    557 	add	r0,r7,25<<10

000002c4 e28010f8    558 	add	r1,r0,248

000002c8 e28d0050    559 	add	r0,sp,80

000002cc e1a06000    560 	mov	r6,r0

000002d0 e1a05006    561 	mov	r5,r6

000002d4 e3a04000    562 	mov	r4,0

000002d8 128d405c    563 	addne	r4,sp,92

                     564 ;239:     StringView* dirName, StringView* startFileName, BufferView* outBuf)


                     565 ;240: {


                     566 

                     567 ;241:     /*


                     568 ;242:         A1


                     569 ;243:             02 InvokeID


                     570 ;244:             BF 4D


                     571 ;245:                 A0


                     572 ;246:                     30


                     573 ;247:                         Список


                     574 ;248:                 More follows


                     575 ;249:     */


                     576 ;250: 


                     577 ;251:     bool moreFollows;


                     578 ;252:     BufferView fileListBuf;


                     579 ;253:     size_t fileListSeqSize;


                     580 ;254:     FileRespHeadInfo headInfo;


                     581 ;255: 


                     582 ;256:     //Получаем закодированый список файлов с атрибутами


                     583 ;257:     BufferView_init(&fileListBuf, mmsConn->fileBuf, FILE_BUF_SIZE, 0);


                     584 

000002dc e3a02ef0    585 	mov	r2,15<<8

000002e0 e282209c    586 	add	r2,r2,156

000002e4 e3a03000    587 	mov	r3,0

000002e8 eb000000*   588 	bl	BufferView_init

                     589 ;258:     if (!fileList(mmsConn, dirName, startFileName, &fileListBuf, &moreFollows))


                     590 

                     591 ;188:     StringView* startFileName, BufferView* outBuf, bool* moreFollows)


                     592 ;189: {


                     593 

                     594 ;190:     FSFindData fileInfo;


                     595 ;191:     BufferView nameBuf;


                     596 ;192:     FNameErrCode findResult;


                     597 ;193:     size_t oldOutBufPos;


                     598 ;194:     //Инициализируем буфер имени


                     599 ;195:     BufferView_init(&nameBuf, mmsConn->fileName, FILE_NAME_BUF_SIZE, 0);



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
                     600 

000002ec e2870c74    601 	add	r0,r7,29<<10

000002f0 e2801094    602 	add	r1,r0,148

000002f4 e28d0014    603 	add	r0,sp,20

000002f8 e3a03000    604 	mov	r3,0

000002fc e3a020ff    605 	mov	r2,255

00000300 eb000000*   606 	bl	BufferView_init

                     607 ;196: 


                     608 ;197:     findResult = fs_findFirst(dirName, startFileName, &fileInfo, &nameBuf);


                     609 

00000304 e28d3014    610 	add	r3,sp,20

00000308 e28d2020    611 	add	r2,sp,32

0000030c e1a01004    612 	mov	r1,r4

00000310 e1a04002    613 	mov	r4,r2

00000314 e28d0064    614 	add	r0,sp,100

00000318 eb000000*   615 	bl	fs_findFirst

                     616 ;198:     while (findResult != FNAME_NOT_FOUND)


                     617 

0000031c e1a07000    618 	mov	r7,r0

00000320 e3570001    619 	cmp	r7,1

00000324 0a000034    620 	beq	.L669

                     621 .L652:

                     622 ;199:     {


                     623 

                     624 ;200:         if (findResult == FNAME_BUF_ERROR)


                     625 

00000328 e3570002    626 	cmp	r7,2

                     627 ;201:         {


                     628 

                     629 ;202:             ERROR_REPORT("The file name does not fit in the buffer");


                     630 ;203:             fs_findClose(&fileInfo);


                     631 

                     632 ;204:             return findResult;


                     633 

                     634 ;205:         }


                     635 ;206: 


                     636 ;207:         if (findResult == FNAME_ERROR)


                     637 

0000032c 13570003    638 	cmpne	r7,3

00000330 0a00003b    639 	beq	.L670

                     640 ;208:         {


                     641 

                     642 ;209:             ERROR_REPORT("Unknown error");


                     643 ;210:             fs_findClose(&fileInfo);


                     644 

                     645 ;211:             return findResult;


                     646 

                     647 ;212:         }


                     648 ;213: 


                     649 ;214:         //encodeFileInfo пишет инфу в буфер. Если инфа не влезла,


                     650 ;215:         //то надо откатить буфер к состоянию до вызова, и вернуть More Follows


                     651 ;216:         oldOutBufPos = outBuf->pos;


                     652 

00000334 e5967004    653 	ldr	r7,[r6,4]

                     654 ;217:         if (!encodeFileInfo(&fileInfo, outBuf))


                     655 

                     656 ;117: {


                     657 

                     658 ;118:     /*


                     659 ;119:         30


                     660 ;120:             A0



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
                     661 ;121:                 19 Имя файла


                     662 ;122:             A1


                     663 ;123:                 80 Размер


                     664 ;124:                 81 Время


                     665 ;125:     */


                     666 ;126: 


                     667 ;127:     EncodedAttrLen encAttrLen;


                     668 ;128:     size_t encodedNameStringLen;//19


                     669 ;129:     size_t encodedNameLen;//A0


                     670 ;130: 


                     671 ;131: 


                     672 ;132:     //Определяем размеры


                     673 ;133:     determineEncodedAttrLen(&encAttrLen, fileInfo->attr.fileSize);


                     674 

00000338 e5941014    675 	ldr	r1,[r4,20]

0000033c e28d0008    676 	add	r0,sp,8

00000340 ebffff2e*   677 	bl	determineEncodedAttrLen

                     678 ;134:     encodedNameStringLen = BerEncoder_determineFullObjectSize(


                     679 

00000344 e594000c    680 	ldr	r0,[r4,12]

00000348 eb000000*   681 	bl	BerEncoder_determineFullObjectSize

                     682 ;135:         fileInfo->fileName.len);


                     683 ;136:     encodedNameLen = BerEncoder_determineFullObjectSize(encodedNameStringLen);


                     684 

0000034c e1a0a000    685 	mov	r10,r0

00000350 eb000000*   686 	bl	BerEncoder_determineFullObjectSize

                     687 ;137: 


                     688 ;138:     //Кодируем


                     689 ;139:     //Sequence


                     690 ;140:     if (!BufferView_encodeTL(


                     691 

00000354 e59d1010    692 	ldr	r1,[sp,16]

00000358 e0812000    693 	add	r2,r1,r0

0000035c e1a00005    694 	mov	r0,r5

00000360 e3a01030    695 	mov	r1,48

00000364 eb000000*   696 	bl	BufferView_encodeTL

00000368 e3500000    697 	cmp	r0,0

0000036c 0a000011    698 	beq	.L666

                     699 ;141:         outBuf, ASN_SEQUENCE, encodedNameLen + encAttrLen.fullAttrLen))


                     700 ;142:     {


                     701 

                     702 ;143:         return FALSE;


                     703 

                     704 ;144:     }


                     705 ;145:     //Имя файла


                     706 ;146: 


                     707 ;147:     if (!BufferView_encodeTL(outBuf, 0xA0, encodedNameStringLen))


                     708 

00000370 e1a0200a    709 	mov	r2,r10

00000374 e1a00005    710 	mov	r0,r5

00000378 e3a010a0    711 	mov	r1,160

0000037c eb000000*   712 	bl	BufferView_encodeTL

00000380 e3500000    713 	cmp	r0,0

00000384 0a00000b    714 	beq	.L666

                     715 ;148:     {


                     716 

                     717 ;149:         return FALSE;


                     718 

                     719 ;150:     }


                     720 ;151: 


                     721 ;152:     if (!BufferView_encodeStringView(outBuf, ASN_GRAPHIC_STRING,



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
                     722 

00000388 e284200c    723 	add	r2,r4,12

0000038c e1a00005    724 	mov	r0,r5

00000390 e3a01019    725 	mov	r1,25

00000394 eb000000*   726 	bl	BufferView_encodeStringView

00000398 e3500000    727 	cmp	r0,0

0000039c 0a000005    728 	beq	.L666

                     729 ;153:         &fileInfo->fileName))


                     730 ;154:     {


                     731 

                     732 ;155:         return FALSE;


                     733 

                     734 ;156:     }


                     735 ;157: 


                     736 ;158:     //Атрибуты


                     737 ;159:     if (!encodeFileAttr(outBuf, &encAttrLen, &fileInfo->attr))


                     738 

000003a0 e2842014    739 	add	r2,r4,20

000003a4 e28d1008    740 	add	r1,sp,8

000003a8 e1a00005    741 	mov	r0,r5

000003ac ebffff2b*   742 	bl	encodeFileAttr

000003b0 e3500000    743 	cmp	r0,0

000003b4 1a000008    744 	bne	.L667

                     745 .L666:

                     746 ;160:     {


                     747 

                     748 ;161:         return FALSE;


                     749 

                     750 ;162:     }


                     751 ;163:     return TRUE;


                     752 

                     753 ;218:         {


                     754 

                     755 ;219:             fs_findClose(&fileInfo);


                     756 

000003b8 e28d0020    757 	add	r0,sp,32

000003bc eb000000*   758 	bl	fs_findClose

                     759 ;220:             //Восстанавливаем позицию буфера до вызова


                     760 ;221:             outBuf->pos = oldOutBufPos;


                     761 

000003c0 e5867004    762 	str	r7,[r6,4]

                     763 ;222:             *moreFollows = TRUE;


                     764 

000003c4 e59d0054    765 	ldr	r0,[sp,84]

000003c8 e3a04001    766 	mov	r4,1

                     767 ;223:             return TRUE;


                     768 

                     769 ;259:     {


                     770 

                     771 ;260:         return FALSE;


                     772 

                     773 ;261:     }


                     774 ;262: 


                     775 ;263:     //===================Определяем размеры=====================


                     776 ;264:     // Размер списка файлов с размером и тэгом SEQUENCE(0x30) для тэга 0xA0


                     777 ;265:     fileListSeqSize = BerEncoder_determineFullObjectSize(fileListBuf.pos);


                     778 

000003cc eb000000*   779 	bl	BerEncoder_determineFullObjectSize

000003d0 e1a05000    780 	mov	r5,r0

                     781 ;266: 


                     782 ;267:     // Полный размер списка с размером и тэгом A0 и флагом More Follows



                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
                     783 ;268:     // для тэга BF 4D


                     784 ;269:     headInfo.dataLen = BerEncoder_determineFullObjectSize(fileListSeqSize)


                     785 

000003d4 e3a06000    786 	mov	r6,0

000003d8 ea00001b    787 	b	.L673

                     788 .L667:

                     789 ;224:         }


                     790 ;225: 


                     791 ;226:         //Освобождаем буфер имени


                     792 ;227:         nameBuf.pos = 0;


                     793 

000003dc e28d1014    794 	add	r1,sp,20

000003e0 e3a00000    795 	mov	r0,0

000003e4 e58d0018    796 	str	r0,[sp,24]

                     797 ;228: 


                     798 ;229:         findResult = fs_findNext(&fileInfo, &nameBuf);


                     799 

000003e8 e28d0020    800 	add	r0,sp,32

000003ec eb000000*   801 	bl	fs_findNext

000003f0 e1a07000    802 	mov	r7,r0

000003f4 e3570001    803 	cmp	r7,1

000003f8 1affffca    804 	bne	.L652

                     805 .L669:

                     806 ;230:     }


                     807 ;231: 


                     808 ;232:     fs_findClose(&fileInfo);


                     809 

000003fc e28d0020    810 	add	r0,sp,32

00000400 eb000000*   811 	bl	fs_findClose

                     812 ;233:     *moreFollows = FALSE;


                     813 

00000404 e59d0054    814 	ldr	r0,[sp,84]

00000408 e3a04000    815 	mov	r4,0

                     816 ;234:     return TRUE;


                     817 

                     818 ;259:     {


                     819 

                     820 ;260:         return FALSE;


                     821 

                     822 ;261:     }


                     823 ;262: 


                     824 ;263:     //===================Определяем размеры=====================


                     825 ;264:     // Размер списка файлов с размером и тэгом SEQUENCE(0x30) для тэга 0xA0


                     826 ;265:     fileListSeqSize = BerEncoder_determineFullObjectSize(fileListBuf.pos);


                     827 

0000040c eb000000*   828 	bl	BerEncoder_determineFullObjectSize

00000410 e1a05000    829 	mov	r5,r0

                     830 ;266: 


                     831 ;267:     // Полный размер списка с размером и тэгом A0 и флагом More Follows


                     832 ;268:     // для тэга BF 4D


                     833 ;269:     headInfo.dataLen = BerEncoder_determineFullObjectSize(fileListSeqSize)


                     834 

00000414 e1a06004    835 	mov	r6,r4

00000418 ea00000c    836 	b	.L674

                     837 	.align	4

                     838 .L530:

0000041c 00000000*   839 	.data.w	.L507

                     840 	.type	.L530,$object

                     841 	.size	.L530,4

                     842 

                     843 .L1287:


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
                     844 ;	"/\000"

00000420 002f       845 	.data.b	47,0

00000422 0000       846 	.align 4

                     847 

                     848 	.type	.L1287,$object

                     849 	.size	.L1287,4

                     850 

                     851 .L670:

00000424 e28d0020    852 	add	r0,sp,32

00000428 eb000000*   853 	bl	fs_findClose

0000042c e31700ff    854 	tst	r7,255

00000430 0a00002a    855 	beq	.L682

                     856 ;259:     {


                     857 

                     858 ;260:         return FALSE;


                     859 

                     860 ;261:     }


                     861 ;262: 


                     862 ;263:     //===================Определяем размеры=====================


                     863 ;264:     // Размер списка файлов с размером и тэгом SEQUENCE(0x30) для тэга 0xA0


                     864 ;265:     fileListSeqSize = BerEncoder_determineFullObjectSize(fileListBuf.pos);


                     865 

00000434 e59d0054    866 	ldr	r0,[sp,84]

00000438 e3a06000    867 	mov	r6,0

0000043c eb000000*   868 	bl	BerEncoder_determineFullObjectSize

00000440 e1a05000    869 	mov	r5,r0

                     870 ;266: 


                     871 ;267:     // Полный размер списка с размером и тэгом A0 и флагом More Follows


                     872 ;268:     // для тэга BF 4D


                     873 ;269:     headInfo.dataLen = BerEncoder_determineFullObjectSize(fileListSeqSize)


                     874 

00000444 e3540000    875 	cmp	r4,0

00000448 0a000000    876 	beq	.L674

                     877 .L673:

0000044c e3a06003    878 	mov	r6,3

                     879 .L674:

00000450 e1a00005    880 	mov	r0,r5

00000454 eb000000*   881 	bl	BerEncoder_determineFullObjectSize

00000458 e0861000    882 	add	r1,r6,r0

0000045c e59d006c    883 	ldr	r0,[sp,108]

00000460 e58d104c    884 	str	r1,[sp,76]

                     885 ;270:         + (moreFollows ? 3 : 0);


                     886 ;271: 


                     887 ;272:     //Размер ответа, включая InvokeID для тэга A1


                     888 ;273:     headInfo.responseLen = determineResponseLen(invokeID, headInfo.dataLen);


                     889 

00000464 ebfffef3*   890 	bl	determineResponseLen

00000468 e58d0048    891 	str	r0,[sp,72]

                     892 ;274: 


                     893 ;275:     //====================Кодируем================================


                     894 ;276:     headInfo.extTag = 0xBF4D;


                     895 

0000046c e3a00cbf    896 	mov	r0,191<<8

00000470 e280004d    897 	add	r0,r0,77

00000474 e1cd04b4    898 	strh	r0,[sp,68]

                     899 ;277:     headInfo.invokeID = invokeID;


                     900 

00000478 e59d006c    901 	ldr	r0,[sp,108]

0000047c e28d1040    902 	add	r1,sp,64

00000480 e58d0040    903 	str	r0,[sp,64]

                     904 ;278: 



                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
                     905 ;279:     if (!encodeFileResponseHead(outBuf, &headInfo))


                     906 

00000484 e1a0000b    907 	mov	r0,fp

00000488 ebffff28*   908 	bl	encodeFileResponseHead

0000048c e3500000    909 	cmp	r0,0

00000490 0a000012    910 	beq	.L682

                     911 ;280:     {


                     912 

                     913 ;281:         return FALSE;


                     914 

                     915 ;282:     }


                     916 ;283: 


                     917 ;284:     if (!BufferView_encodeTL(outBuf, 0xA0, fileListSeqSize))


                     918 

00000494 e1a02005    919 	mov	r2,r5

00000498 e1a0000b    920 	mov	r0,fp

0000049c e3a010a0    921 	mov	r1,160

000004a0 eb000000*   922 	bl	BufferView_encodeTL

000004a4 e3500000    923 	cmp	r0,0

000004a8 0a00000c    924 	beq	.L682

                     925 ;285:     {


                     926 

                     927 ;286:         return FALSE;


                     928 

                     929 ;287:     }


                     930 ;288:     // Cписок файлов из временного буфера в исходящий


                     931 ;289:     if (!BufferView_encodeBufferView(outBuf, ASN_SEQUENCE,&fileListBuf))


                     932 

000004ac e28d2050    933 	add	r2,sp,80

000004b0 e1a0000b    934 	mov	r0,fp

000004b4 e3a01030    935 	mov	r1,48

000004b8 eb000000*   936 	bl	BufferView_encodeBufferView

000004bc e3500000    937 	cmp	r0,0

000004c0 0a000006    938 	beq	.L682

                     939 ;290:     {


                     940 

                     941 ;291:         return FALSE;


                     942 

                     943 ;292:     }


                     944 ;293:     //More Follows


                     945 ;294:     if (moreFollows)


                     946 

000004c4 e3540000    947 	cmp	r4,0

000004c8 0a000006    948 	beq	.L683

                     949 ;295:     {


                     950 

                     951 ;296:         if (!BufferView_encodeBoolean(outBuf, 0x81, moreFollows))


                     952 

000004cc e1a02004    953 	mov	r2,r4

000004d0 e1a0000b    954 	mov	r0,fp

000004d4 e3a01081    955 	mov	r1,129

000004d8 eb000000*   956 	bl	BufferView_encodeBoolean

000004dc e3500000    957 	cmp	r0,0

                     958 .L682:

                     959 ;297:         {


                     960 

                     961 ;298:             return FALSE;


                     962 

000004e0 03a00000    963 	moveq	r0,0

000004e4 0a000000    964 	beq	.L618

                     965 .L683:


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
                     966 ;299:         }


                     967 ;300: 


                     968 ;301:     }


                     969 ;302:     return TRUE;


                     970 

000004e8 e3a00001    971 	mov	r0,1

                     972 .L618:

000004ec e28dd06c    973 	add	sp,sp,108

000004f0 e8bd8cf4    974 	ldmfd	[sp]!,{r2,r4-r7,r10-fp,pc}

                     975 	.endf	mms_handleFileDirRequest

                     976 	.align	4

                     977 ;tag	[sp,3]	local

                     978 ;length	[sp,4]	local

                     979 ;dir	[sp,100]	local

                     980 ;startFile	[sp,92]	local

                     981 ;thereIsStartFile	r6	local

                     982 ;.L1183	.L1186	static

                     983 ;moreFollows	r4	local

                     984 ;fileListBuf	[sp,80]	local

                     985 ;fileListSeqSize	r5	local

                     986 ;headInfo	[sp,64]	local

                     987 ;fileInfo	[sp,32]	local

                     988 ;nameBuf	[sp,20]	local

                     989 ;findResult	r7	local

                     990 ;oldOutBufPos	r7	local

                     991 ;encAttrLen	[sp,8]	local

                     992 ;encodedNameStringLen	r10	local

                     993 

                     994 ;mmsConn	r7	param

                     995 ;inBuf	r4	param

                     996 ;invokeId	[sp,108]	param

                     997 ;outBuf	fp	param

                     998 

                     999 	.section ".bss","awb"

                    1000 .L1182:

                    1001 	.data

                    1002 	.text

                    1003 

                    1004 

                    1005 	.align	4

                    1006 	.align	4

                    1007 mms_handleFileOpenRequest::

000004f4 e92d40f0   1008 	stmfd	[sp]!,{r4-r7,lr}

000004f8 e1a06002   1009 	mov	r6,r2

000004fc e24dd040   1010 	sub	sp,sp,64

00000500 e28d2004   1011 	add	r2,sp,4

00000504 e1a04001   1012 	mov	r4,r1

00000508 e28d1003   1013 	add	r1,sp,3

0000050c e1a07000   1014 	mov	r7,r0

00000510 e1a00004   1015 	mov	r0,r4

00000514 e1a05003   1016 	mov	r5,r3

00000518 e3a03000   1017 	mov	r3,0

0000051c eb000000*  1018 	bl	BerDecoder_decodeTLFromBufferView

00000520 e3500000   1019 	cmp	r0,0

00000524 0a00001f   1020 	beq	.L1309

00000528 e5dd0003   1021 	ldrb	r0,[sp,3]

0000052c e35000a0   1022 	cmp	r0,160

00000530 1a00001c   1023 	bne	.L1309

00000534 e28d2004   1024 	add	r2,sp,4

00000538 e28d1003   1025 	add	r1,sp,3

0000053c e1a00004   1026 	mov	r0,r4


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
00000540 e3a03000   1027 	mov	r3,0

00000544 eb000000*  1028 	bl	BerDecoder_decodeTLFromBufferView

00000548 e3500000   1029 	cmp	r0,0

0000054c 0a000015   1030 	beq	.L1309

00000550 e5dd0003   1031 	ldrb	r0,[sp,3]

00000554 e3500019   1032 	cmp	r0,25

00000558 1a000012   1033 	bne	.L1309

0000055c e28d2038   1034 	add	r2,sp,56

00000560 e59d1004   1035 	ldr	r1,[sp,4]

00000564 e1a00004   1036 	mov	r0,r4

00000568 eb000000*  1037 	bl	BufferView_readStringView

0000056c e3500000   1038 	cmp	r0,0

00000570 0a00000c   1039 	beq	.L1309

00000574 e28d2004   1040 	add	r2,sp,4

00000578 e28d1003   1041 	add	r1,sp,3

0000057c e1a00004   1042 	mov	r0,r4

00000580 e3a03000   1043 	mov	r3,0

00000584 eb000000*  1044 	bl	BerDecoder_decodeTLFromBufferView

00000588 e3500000   1045 	cmp	r0,0

0000058c 0a000005   1046 	beq	.L1309

00000590 e28d2008   1047 	add	r2,sp,8

00000594 e59d1004   1048 	ldr	r1,[sp,4]

00000598 e1a00004   1049 	mov	r0,r4

0000059c eb000000*  1050 	bl	BufferView_decodeUInt32

000005a0 e3500000   1051 	cmp	r0,0

000005a4 1a000001   1052 	bne	.L1313

                    1053 .L1309:

000005a8 e3a00000   1054 	mov	r0,0

000005ac ea00002d   1055 	b	.L1288

                    1056 .L1313:

                    1057 ;350:     StringView* fileName, size_t startPos, BufferView* outBuf)


                    1058 ;351: {


                    1059 

                    1060 ;352:     /*


                    1061 ;353:         A1


                    1062 ;354:             02 InvokeID


                    1063 ;355:             BF 48


                    1064 ;356:                 80 FRSM ID


                    1065 ;357:                 A1 (содержит атрибуты)


                    1066 ;358:                     80 Размер


                    1067 ;359:                     81 Время


                    1068 ;360:     */


                    1069 ;361:     EncodedAttrLen encAttrLen;


                    1070 ;362:     size_t encodedFRSMIDlen;


                    1071 ;363:     FileRespHeadInfo respHeadInfo;


                    1072 ;364:     FSFileAttr attr;


                    1073 ;365:     uint32_t frsmID;


                    1074 ;366: 


                    1075 ;367:     if (!fs_fileOpen(fileName, startPos, &frsmID, &attr))


                    1076 

000005b0 e28d3010   1077 	add	r3,sp,16

000005b4 e28d200c   1078 	add	r2,sp,12

000005b8 e59d1008   1079 	ldr	r1,[sp,8]

000005bc e28d0038   1080 	add	r0,sp,56

000005c0 eb000000*  1081 	bl	fs_fileOpen

000005c4 e3500000   1082 	cmp	r0,0

000005c8 0a000025   1083 	beq	.L1320

                    1084 ;368:     {


                    1085 

                    1086 ;369:         return FALSE;


                    1087 


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
                    1088 ;370:     }


                    1089 ;371:     mmsConn->isFileOpen = TRUE;


                    1090 

000005cc e2870c75   1091 	add	r0,r7,117<<8

000005d0 e3a01001   1092 	mov	r1,1

000005d4 e5c01093   1093 	strb	r1,[r0,147]

                    1094 ;372:     mmsConn->frsmID = frsmID;


                    1095 

000005d8 e59d100c   1096 	ldr	r1,[sp,12]

000005dc e5801094   1097 	str	r1,[r0,148]

                    1098 ;373: 


                    1099 ;374:     //===================Определяем размеры=====================


                    1100 ;375:     determineEncodedAttrLen(&encAttrLen, attr.fileSize);


                    1101 

000005e0 e59d1010   1102 	ldr	r1,[sp,16]

000005e4 e28d002c   1103 	add	r0,sp,44

000005e8 ebfffe84*  1104 	bl	determineEncodedAttrLen

                    1105 ;376:     encodedFRSMIDlen = 2 //тэг + длина


                    1106 

000005ec e59d000c   1107 	ldr	r0,[sp,12]

000005f0 eb000000*  1108 	bl	BerEncoder_UInt32determineEncodedSize

000005f4 e59d1034   1109 	ldr	r1,[sp,52]

000005f8 e2800002   1110 	add	r0,r0,2

                    1111 ;377:         + BerEncoder_UInt32determineEncodedSize(frsmID);


                    1112 ;378:     //данные для тэга BF 48


                    1113 ;379:     respHeadInfo.dataLen = encAttrLen.fullAttrLen + encodedFRSMIDlen;


                    1114 

000005fc e0801001   1115 	add	r1,r0,r1

00000600 e58d1028   1116 	str	r1,[sp,40]

                    1117 ;380:     respHeadInfo.responseLen = determineResponseLen(invokeID,


                    1118 

00000604 e1a00006   1119 	mov	r0,r6

00000608 ebfffe8a*  1120 	bl	determineResponseLen

0000060c e58d601c   1121 	str	r6,[sp,28]

                    1122 ;386:     if (!encodeFileResponseHead(outBuf, &respHeadInfo))


                    1123 

00000610 e28d101c   1124 	add	r1,sp,28

00000614 e58d0024   1125 	str	r0,[sp,36]

                    1126 ;381:         respHeadInfo.dataLen);


                    1127 ;382: 


                    1128 ;383:     //===================Кодируем=====================


                    1129 ;384:     respHeadInfo.extTag = 0xBF48;


                    1130 

00000618 e3a00cbf   1131 	mov	r0,191<<8

0000061c e2800048   1132 	add	r0,r0,72

00000620 e1cd02b0   1133 	strh	r0,[sp,32]

                    1134 ;385:     respHeadInfo.invokeID = invokeID;


                    1135 

00000624 e1a00005   1136 	mov	r0,r5

00000628 ebfffec0*  1137 	bl	encodeFileResponseHead

0000062c e3500000   1138 	cmp	r0,0

00000630 0a00000b   1139 	beq	.L1320

                    1140 ;387:     {


                    1141 

                    1142 ;388:         return FALSE;


                    1143 

                    1144 ;389:     }


                    1145 ;390:     //FRSM ID


                    1146 ;391:     if (!BufferView_encodeUInt32(outBuf, 0x80, frsmID))


                    1147 

00000634 e59d200c   1148 	ldr	r2,[sp,12]


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
00000638 e1a00005   1149 	mov	r0,r5

0000063c e3a01080   1150 	mov	r1,128

00000640 eb000000*  1151 	bl	BufferView_encodeUInt32

00000644 e3500000   1152 	cmp	r0,0

00000648 0a000005   1153 	beq	.L1320

                    1154 ;392:     {


                    1155 

                    1156 ;393:         return FALSE;


                    1157 

                    1158 ;394:     }


                    1159 ;395: 


                    1160 ;396:     //Атрибуты


                    1161 ;397:     if (!encodeFileAttr(outBuf, &encAttrLen, &attr))


                    1162 

0000064c e28d2010   1163 	add	r2,sp,16

00000650 e28d102c   1164 	add	r1,sp,44

00000654 e1a00005   1165 	mov	r0,r5

00000658 ebfffe80*  1166 	bl	encodeFileAttr

0000065c e3500000   1167 	cmp	r0,0

                    1168 ;400:     }


                    1169 ;401:     return TRUE;


                    1170 

00000660 13a00001   1171 	movne	r0,1

                    1172 .L1320:

                    1173 ;398:     {


                    1174 

                    1175 ;399:         return FALSE;


                    1176 

00000664 03a00000   1177 	moveq	r0,0

                    1178 .L1288:

00000668 e28dd040   1179 	add	sp,sp,64

0000066c e8bd80f0   1180 	ldmfd	[sp]!,{r4-r7,pc}

                    1181 	.endf	mms_handleFileOpenRequest

                    1182 	.align	4

                    1183 ;tag	[sp,3]	local

                    1184 ;length	[sp,4]	local

                    1185 ;fileName	[sp,56]	local

                    1186 ;startPos	[sp,8]	local

                    1187 ;encAttrLen	[sp,44]	local

                    1188 ;encodedFRSMIDlen	r0	local

                    1189 ;respHeadInfo	[sp,28]	local

                    1190 ;attr	[sp,16]	local

                    1191 ;frsmID	[sp,12]	local

                    1192 

                    1193 ;mmsConn	r7	param

                    1194 ;inBuf	r4	param

                    1195 ;invokeId	r6	param

                    1196 ;outBuf	r5	param

                    1197 

                    1198 	.section ".bss","awb"

                    1199 .L1489:

                    1200 	.data

                    1201 	.text

                    1202 

                    1203 

                    1204 	.align	4

                    1205 	.align	4

                    1206 mms_handleFileReadRequest::

00000670 e92d4070   1207 	stmfd	[sp]!,{r4-r6,lr}

00000674 e1a04003   1208 	mov	r4,r3

00000678 e1a05002   1209 	mov	r5,r2


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
0000067c e24dd024   1210 	sub	sp,sp,36

00000680 e1a06000   1211 	mov	r6,r0

00000684 e1a00001   1212 	mov	r0,r1

00000688 e5901008   1213 	ldr	r1,[r0,8]

0000068c e28d2004   1214 	add	r2,sp,4

00000690 eb000000*  1215 	bl	BufferView_decodeUInt32

00000694 e3500000   1216 	cmp	r0,0

00000698 0a000029   1217 	beq	.L1536

                    1218 ;439:     uint32_t frsmID, BufferView* outBuf)


                    1219 ;440: {


                    1220 

                    1221 ;441:     /*


                    1222 ;442:         A1


                    1223 ;443:             02 InvokeID


                    1224 ;444:             BF 49


                    1225 ;445:                 80 file data


                    1226 ;446:                 81 more follows


                    1227 ;447:     */


                    1228 ;448:     BufferView fileReadBuf;


                    1229 ;449:     bool moreFollows;


                    1230 ;450:     size_t moreFollowsLen = 3;


                    1231 

                    1232 ;451:     size_t encodedFileDataLen;


                    1233 ;452:     FileRespHeadInfo respHeadInfo;


                    1234 ;453: 


                    1235 ;454:     BufferView_init(&fileReadBuf, mmsConn->fileBuf, FILE_BUF_SIZE, 0);


                    1236 

0000069c e3a02ef0   1237 	mov	r2,15<<8

000006a0 e282209c   1238 	add	r2,r2,156

000006a4 e2860c64   1239 	add	r0,r6,25<<10

000006a8 e28010f8   1240 	add	r1,r0,248

000006ac e28d0018   1241 	add	r0,sp,24

000006b0 e3a03000   1242 	mov	r3,0

000006b4 eb000000*  1243 	bl	BufferView_init

                    1244 ;455:     if (!fs_fileRead(frsmID, &fileReadBuf, &moreFollows))


                    1245 

000006b8 e28d2003   1246 	add	r2,sp,3

000006bc e59d0004   1247 	ldr	r0,[sp,4]

000006c0 e28d1018   1248 	add	r1,sp,24

000006c4 eb000000*  1249 	bl	fs_fileRead

000006c8 e3500000   1250 	cmp	r0,0

000006cc 0a00001b   1251 	beq	.L1550

                    1252 ;456:     {


                    1253 

                    1254 ;457:         return FALSE;


                    1255 

                    1256 ;458:     }


                    1257 ;459:     //===================Определяем размеры=====================


                    1258 ;460:     encodedFileDataLen = BerEncoder_determineFullObjectSize(fileReadBuf.pos);


                    1259 

000006d0 e59d001c   1260 	ldr	r0,[sp,28]

000006d4 eb000000*  1261 	bl	BerEncoder_determineFullObjectSize

                    1262 ;461:     respHeadInfo.dataLen = encodedFileDataLen + moreFollowsLen;


                    1263 

000006d8 e2801003   1264 	add	r1,r0,3

000006dc e58d1014   1265 	str	r1,[sp,20]

                    1266 ;462:     respHeadInfo.responseLen = determineResponseLen(invokeID,


                    1267 

000006e0 e1a00005   1268 	mov	r0,r5

000006e4 ebfffe53*  1269 	bl	determineResponseLen

000006e8 e58d5008   1270 	str	r5,[sp,8]


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
                    1271 ;468:     if (!encodeFileResponseHead(outBuf, &respHeadInfo))


                    1272 

000006ec e28d1008   1273 	add	r1,sp,8

000006f0 e58d0010   1274 	str	r0,[sp,16]

                    1275 ;463:         respHeadInfo.dataLen);


                    1276 ;464: 


                    1277 ;465:     //===================Кодируем=====================


                    1278 ;466:     respHeadInfo.extTag = 0xBF49;


                    1279 

000006f4 e3a00cbf   1280 	mov	r0,191<<8

000006f8 e2800049   1281 	add	r0,r0,73

000006fc e1cd00bc   1282 	strh	r0,[sp,12]

                    1283 ;467:     respHeadInfo.invokeID = invokeID;


                    1284 

00000700 e1a00004   1285 	mov	r0,r4

00000704 ebfffe89*  1286 	bl	encodeFileResponseHead

00000708 e3500000   1287 	cmp	r0,0

0000070c 0a00000b   1288 	beq	.L1550

                    1289 ;469:     {


                    1290 

                    1291 ;470:         return FALSE;


                    1292 

                    1293 ;471:     }


                    1294 ;472:     //Данные


                    1295 ;473:     if (!BufferView_encodeBufferView(outBuf, 0x80, &fileReadBuf))


                    1296 

00000710 e28d2018   1297 	add	r2,sp,24

00000714 e1a00004   1298 	mov	r0,r4

00000718 e3a01080   1299 	mov	r1,128

0000071c eb000000*  1300 	bl	BufferView_encodeBufferView

00000720 e3500000   1301 	cmp	r0,0

00000724 0a000005   1302 	beq	.L1550

                    1303 ;474:     {


                    1304 

                    1305 ;475:         return FALSE;


                    1306 

                    1307 ;476:     }


                    1308 ;477:     //More follows


                    1309 ;478:     if (!BufferView_encodeBoolean(outBuf, 0x81, moreFollows))


                    1310 

00000728 e5dd2003   1311 	ldrb	r2,[sp,3]

0000072c e1a00004   1312 	mov	r0,r4

00000730 e3a01081   1313 	mov	r1,129

00000734 eb000000*  1314 	bl	BufferView_encodeBoolean

00000738 e3500000   1315 	cmp	r0,0

                    1316 ;481:     }


                    1317 ;482:     return TRUE;


                    1318 

0000073c 13a00001   1319 	movne	r0,1

                    1320 .L1550:

                    1321 ;479:     {


                    1322 

                    1323 ;480:         return FALSE;


                    1324 

00000740 03a00000   1325 	moveq	r0,0

                    1326 .L1536:

00000744 e28dd024   1327 	add	sp,sp,36

00000748 e8bd8070   1328 	ldmfd	[sp]!,{r4-r6,pc}

                    1329 	.endf	mms_handleFileReadRequest

                    1330 	.align	4

                    1331 ;frsmID	[sp,4]	local


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
                    1332 ;result	r0	local

                    1333 ;fileReadBuf	[sp,24]	local

                    1334 ;moreFollows	[sp,3]	local

                    1335 ;respHeadInfo	[sp,8]	local

                    1336 

                    1337 ;mmsConn	r6	param

                    1338 ;inBuf	r12	param

                    1339 ;invokeID	r5	param

                    1340 ;outBuf	r4	param

                    1341 

                    1342 	.section ".bss","awb"

                    1343 .L1683:

                    1344 	.data

                    1345 	.text

                    1346 

                    1347 

                    1348 ;520: 


                    1349 ;521: bool mms_handleFileCloseRequest(MmsConnection* mmsConn, BufferView* inBuf,


                    1350 	.align	4

                    1351 	.align	4

                    1352 mms_handleFileCloseRequest::

0000074c e92d4070   1353 	stmfd	[sp]!,{r4-r6,lr}

                    1354 ;522:     unsigned int invokeID, BufferView* outBuf)


                    1355 ;523: {


                    1356 

                    1357 ;524:     /*


                    1358 ;525:         inBuf содержит только frsmID


                    1359 ;526:     */


                    1360 ;527:     uint32_t frsmID;


                    1361 ;528:     bool result = BufferView_decodeUInt32(inBuf, inBuf->len, &frsmID);


                    1362 

00000750 e1a06003   1363 	mov	r6,r3

00000754 e1a04002   1364 	mov	r4,r2

00000758 e24dd014   1365 	sub	sp,sp,20

0000075c e1a05000   1366 	mov	r5,r0

00000760 e1a00001   1367 	mov	r0,r1

00000764 e5901008   1368 	ldr	r1,[r0,8]

00000768 e1a0200d   1369 	mov	r2,sp

0000076c eb000000*  1370 	bl	BufferView_decodeUInt32

                    1371 ;529:     RET_IF_NOT(result, "Error reading FRSM ID");


                    1372 

00000770 e3500000   1373 	cmp	r0,0

00000774 0a000012   1374 	beq	.L1711

00000778 e59d0000   1375 	ldr	r0,[sp]

0000077c eb000000*  1376 	bl	fs_fileClose

                    1377 ;530:     return encodeFileCloseRequest(mmsConn, invokeID, frsmID, outBuf);


                    1378 

                    1379 ;498:     uint32_t frsmID, BufferView* outBuf)


                    1380 ;499: {


                    1381 

                    1382 ;500:     /*


                    1383 ;501:         A1


                    1384 ;502:             02 InvokeID


                    1385 ;503:             9F 4A пустой


                    1386 ;504:     */


                    1387 ;505:     FileRespHeadInfo respHeadInfo;


                    1388 ;506: 


                    1389 ;507:     if (!fs_fileClose(frsmID))


                    1390 

00000780 e3500000   1391 	cmp	r0,0

                    1392 ;508:     {



                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
                    1393 

                    1394 ;509:         return FALSE;


                    1395 

00000784 0a00000e   1396 	beq	.L1711

                    1397 ;510:     }


                    1398 ;511:     mmsConn->isFileOpen = FALSE;


                    1399 

00000788 e3a01000   1400 	mov	r1,0

0000078c e58d1010   1401 	str	r1,[sp,16]

                    1402 ;514:     respHeadInfo.responseLen = determineResponseLen(invokeID,


                    1403 

00000790 e3a00c75   1404 	mov	r0,117<<8

00000794 e2800093   1405 	add	r0,r0,147

00000798 e7c51000   1406 	strb	r1,[r5,r0]

                    1407 ;512: 


                    1408 ;513:     respHeadInfo.dataLen = 0;


                    1409 

0000079c e1a00004   1410 	mov	r0,r4

000007a0 ebfffe24*  1411 	bl	determineResponseLen

000007a4 e58d4004   1412 	str	r4,[sp,4]

                    1413 ;517:     respHeadInfo.extTag = 0x9F4A;


                    1414 

000007a8 e28d1004   1415 	add	r1,sp,4

000007ac e58d000c   1416 	str	r0,[sp,12]

                    1417 ;515:         respHeadInfo.dataLen);


                    1418 ;516:     respHeadInfo.invokeID = invokeID;


                    1419 

000007b0 e3a00c9f   1420 	mov	r0,159<<8

000007b4 e280004a   1421 	add	r0,r0,74

000007b8 e1cd00b8   1422 	strh	r0,[sp,8]

                    1423 ;518:     return encodeFileResponseHead(outBuf, &respHeadInfo);


                    1424 

000007bc e1a00006   1425 	mov	r0,r6

000007c0 ebfffe5a*  1426 	bl	encodeFileResponseHead

                    1427 .L1711:

000007c4 e28dd014   1428 	add	sp,sp,20

000007c8 e8bd8070   1429 	ldmfd	[sp]!,{r4-r6,pc}

                    1430 	.endf	mms_handleFileCloseRequest

                    1431 	.align	4

                    1432 ;frsmID	[sp]	local

                    1433 ;result	r0	local

                    1434 ;respHeadInfo	[sp,4]	local

                    1435 

                    1436 ;mmsConn	r5	param

                    1437 ;inBuf	r12	param

                    1438 ;invokeID	r4	param

                    1439 ;outBuf	r6	param

                    1440 

                    1441 	.section ".bss","awb"

                    1442 .L1794:

                    1443 	.data

                    1444 	.text

                    1445 

                    1446 ;531: }


                    1447 	.align	4

                    1448 ;__UNNAMED_1_static_in_makeGeneralizedTime	.L507	static

                    1449 

                    1450 	.data

                    1451 	.ghsnote version,6

                    1452 	.ghsnote tools,3

                    1453 	.ghsnote options,0


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_adg1.s
                    1454 	.text

                    1455 	.align	4

                    1456 	.section ".rodata","a"

                    1457 	.align	4

                    1458 	.text

