                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_20c1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=mms_get_name_list.c -o gh_20c1.o -list=mms_get_name_list.lst C:\Users\<USER>\AppData\Local\Temp\gh_20c1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_20c1.s
Source File: mms_get_name_list.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		mms_get_name_list.c -o mms_get_name_list.o

                      12 ;Source File:   mms_get_name_list.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:03 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "mms_get_name_list.h"


                      23 ;2: 


                      24 ;3: #include "AsnEncoding.h"


                      25 ;4: #include "MmsConst.h"


                      26 ;5: #include "mms_error.h"


                      27 ;6: #include "mmsconnection.h"


                      28 ;7: #include "iedmodel.h"


                      29 ;8: #include <debug.h>


                      30 ;9: 


                      31 ;10: #include <types.h>


                      32 ;11: #include <stddef.h>


                      33 ;12: 


                      34 ;13: 


                      35 ;14: #define TAG_CONTINUE_AFTER 0x82


                      36 ;15: 


                      37 ;16: 


                      38 ;17: #define OBJECT_SCOPE_VMD					0


                      39 ;18: #define OBJECT_SCOPE_DOMAIN					1


                      40 ;19: #define OBJECT_SCOPE_ASSOCIATION			2


                      41 ;20: 


                      42 ;21: // 5 - максимуальная шапка списка (тэг + размер)


                      43 ;22: // 3 - "more follows"


                      44 ;23: // 6 - Invoke ID


                      45 ;24: // 5 - тэг и размер всего ответа


                      46 ;25: #define MAX_NAME_LIST_HEADER (5 + 3 + 6 + 5)


                      47 ;26: 


                      48 ;27: /*


                      49 ;28: int mms_createEmptyNameListResponse( unsigned int invokeId, unsigned char* response)


                      50 ;29: {



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_20c1.s
                      51 ;30:     int bufPos = 0;


                      52 ;31:     unsigned int identifierListSize;


                      53 ;32:     unsigned int  invokeIdSize;


                      54 ;33:     unsigned int listOfIdentifierSize;


                      55 ;34:     unsigned int getNameListSize;


                      56 ;35:     unsigned int confirmedServiceResponseSize;


                      57 ;36:     unsigned int confirmedResponsePDUSize;


                      58 ;37:     int moreFollows;


                      59 ;38: 


                      60 ;39: 


                      61 ;40:     //debugSendText("mms_createNameListResponse");


                      62 ;41: 


                      63 ;42:     //============== Определяем размеры =================


                      64 ;43:     //Размер списка идентификаторов


                      65 ;44:     //Настраиваем DomainNameWriter на определение размера


                      66 ;45: 


                      67 ;46:     identifierListSize = 0;


                      68 ;47: 


                      69 ;48:     moreFollows = FALSE;


                      70 ;49: 


                      71 ;50:     //Размер ответа на список имён


                      72 ;51:     listOfIdentifierSize = 1 + BerEncoder_determineLengthSize(identifierListSize)


                      73 ;52:             + identifierListSize;


                      74 ;53: 


                      75 ;54: 


                      76 ;55:     getNameListSize = listOfIdentifierSize;


                      77 ;56:     if (moreFollows == FALSE)


                      78 ;57:     {


                      79 ;58:         //3 байта


                      80 ;59:         getNameListSize += 3;


                      81 ;60:     }


                      82 ;61: 


                      83 ;62:     //Размер Invoke ID


                      84 ;63:     invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;


                      85 ;64: 


                      86 ;65:     //Размер всего ответа


                      87 ;66:     confirmedServiceResponseSize = 1 + BerEncoder_determineLengthSize(getNameListSize)


                      88 ;67:             + getNameListSize;


                      89 ;68:     confirmedResponsePDUSize = confirmedServiceResponseSize + invokeIdSize;


                      90 ;69: 


                      91 ;70:     //=================== Кодируем ответ ==================


                      92 ;71: 


                      93 ;72:     bufPos = BerEncoder_encodeTL(0xa1, confirmedResponsePDUSize, response, bufPos);


                      94 ;73: 


                      95 ;74:     bufPos = BerEncoder_encodeTL(ASN_INTEGER, invokeIdSize - 2, response, bufPos);


                      96 ;75:     bufPos = BerEncoder_encodeUInt32(invokeId, response, bufPos);


                      97 ;76: 


                      98 ;77:     bufPos = BerEncoder_encodeTL(0xa1, getNameListSize, response, bufPos);


                      99 ;78:     bufPos = BerEncoder_encodeTL(0xa0, identifierListSize, response, bufPos);


                     100 ;79: 


                     101 ;80: 


                     102 ;81:     identifierListSize =  0;


                     103 ;82:     bufPos += identifierListSize;


                     104 ;83: 


                     105 ;84:     if (!moreFollows)


                     106 ;85:     {        


                     107 ;86:         bufPos = BerEncoder_encodeBoolean(0x81, moreFollows, response, bufPos);


                     108 ;87:     }


                     109 ;88:     return bufPos;


                     110 ;89: }


                     111 ;90: */



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_20c1.s
                     112 ;91: 


                     113 ;92: int mms_createNameListResponse(MmsConnection* mmsConn,


                     114 	.text

                     115 	.align	4

                     116 mms_createNameListResponse::

00000000 e92d4ff8    117 	stmfd	[sp]!,{r3-fp,lr}

                     118 ;93:         unsigned int invokeId, unsigned char* response, int rootObjPos,


                     119 ;94:                                uint8_t* continueAfter, int continueAfterLen,


                     120 ;95:                                bool recursive, int objectsTagTowrite)


                     121 ;96: {


                     122 

                     123 ;97:     int bufPos = 0;


                     124 

                     125 ;98:     unsigned int identifierListSize;


                     126 ;99:     unsigned int  invokeIdSize;


                     127 ;100:     unsigned int listOfIdentifierSize;


                     128 ;101:     unsigned int getNameListSize;


                     129 ;102:     unsigned int confirmedServiceResponseSize;


                     130 ;103:     unsigned int confirmedResponsePDUSize;    


                     131 ;104:     int moreFollows;


                     132 ;105:     DomainNameWriter* nameWriter = &mmsConn->nameWriter;


                     133 

                     134 ;106: 


                     135 ;107:     //debugSendText("mms_createNameListResponse");


                     136 ;108: 


                     137 ;109:     //============== Определяем размеры =================


                     138 ;110:     //Размер списка идентификаторов


                     139 ;111:     //Настраиваем DomainNameWriter на определение размера


                     140 ;112:     DomainNameWriter_init(nameWriter,NULL,


                     141 

00000004 e24dd008    142 	sub	sp,sp,8

00000008 e58d3008    143 	str	r3,[sp,8]

0000000c e59d7030    144 	ldr	r7,[sp,48]

00000010 e1a05002    145 	mov	r5,r2

00000014 e3a02ef0    146 	mov	r2,15<<8

00000018 e28220ed    147 	add	r2,r2,237

0000001c e1a04000    148 	mov	r4,r0

00000020 e1a0b001    149 	mov	fp,r1

00000024 e3a01000    150 	mov	r1,0

00000028 eb000000*   151 	bl	DomainNameWriter_init

                     152 ;113:                           MAX_MMS_RESPONSE_SIZE - MAX_NAME_LIST_HEADER);


                     153 ;114:     if(continueAfter != NULL)


                     154 

0000002c e3570000    155 	cmp	r7,0

                     156 ;115:     {


                     157 

                     158 ;116:         DomainNameWriter_setStartName(nameWriter,continueAfter, continueAfterLen);


                     159 

00000030 159d2034    160 	ldrne	r2,[sp,52]

00000034 11a01007    161 	movne	r1,r7

00000038 11a00004    162 	movne	r0,r4

0000003c 1b000000*   163 	blne	DomainNameWriter_setStartName

                     164 ;117:     }


                     165 ;118: 


                     166 ;119:     writeChildrenNames(rootObjPos, nameWriter, recursive, objectsTagTowrite);


                     167 

00000040 e59d303c    168 	ldr	r3,[sp,60]

00000044 e5dd2038    169 	ldrb	r2,[sp,56]

00000048 e59d0008    170 	ldr	r0,[sp,8]

0000004c e1a01004    171 	mov	r1,r4

00000050 eb000000*   172 	bl	writeChildrenNames


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_20c1.s
                     173 ;120:     identifierListSize = nameWriter->totalSize;


                     174 

00000054 e5946004    175 	ldr	r6,[r4,4]

                     176 ;121: 


                     177 ;122:     moreFollows = nameWriter->bufferFull;    


                     178 

00000058 e5d49008    179 	ldrb	r9,[r4,8]

                     180 ;123: 


                     181 ;124:     //Размер ответа на список имён


                     182 ;125:     listOfIdentifierSize = 1 + BerEncoder_determineLengthSize(identifierListSize)


                     183 

0000005c e1a00006    184 	mov	r0,r6

00000060 eb000000*   185 	bl	BerEncoder_determineLengthSize

00000064 e0800006    186 	add	r0,r0,r6

00000068 e280a004    187 	add	r10,r0,4

                     188 ;126:             + identifierListSize;


                     189 ;127: 


                     190 ;128: 


                     191 ;129:     getNameListSize = listOfIdentifierSize;


                     192 

                     193 ;130:     


                     194 ;131:     


                     195 ;132:     //размер moreFollows


                     196 ;133:     getNameListSize += 3;


                     197 

                     198 ;134:     


                     199 ;135: 


                     200 ;136:     //Размер Invoke ID


                     201 ;137:     invokeIdSize = BerEncoder_UInt32determineEncodedSize(invokeId) + 2;


                     202 

0000006c e1a0000b    203 	mov	r0,fp

00000070 eb000000*   204 	bl	BerEncoder_UInt32determineEncodedSize

00000074 e2808002    205 	add	r8,r0,2

                     206 ;138: 


                     207 ;139:     //Размер всего ответа


                     208 ;140:     confirmedServiceResponseSize = 1 + BerEncoder_determineLengthSize(getNameListSize)


                     209 

00000078 e1a0000a    210 	mov	r0,r10

0000007c eb000000*   211 	bl	BerEncoder_determineLengthSize

00000080 e080100a    212 	add	r1,r0,r10

00000084 e0811008    213 	add	r1,r1,r8

                     214 ;141:             + getNameListSize;


                     215 ;142:     confirmedResponsePDUSize = confirmedServiceResponseSize + invokeIdSize;


                     216 

00000088 e2811001    217 	add	r1,r1,1

                     218 ;143: 


                     219 ;144:     //=================== Кодируем ответ ==================


                     220 ;145: 


                     221 ;146:     bufPos = BerEncoder_encodeTL(0xa1, confirmedResponsePDUSize, response, bufPos);


                     222 

0000008c e1a02005    223 	mov	r2,r5

00000090 e3a03000    224 	mov	r3,0

00000094 e3a000a1    225 	mov	r0,161

00000098 eb000000*   226 	bl	BerEncoder_encodeTL

                     227 ;147: 


                     228 ;148:     bufPos = BerEncoder_encodeTL(ASN_INTEGER, invokeIdSize - 2, response, bufPos);


                     229 

0000009c e1a02005    230 	mov	r2,r5

000000a0 e2481002    231 	sub	r1,r8,2

000000a4 e1a03000    232 	mov	r3,r0

000000a8 e3a00002    233 	mov	r0,2


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_20c1.s
000000ac eb000000*   234 	bl	BerEncoder_encodeTL

                     235 ;149:     bufPos = BerEncoder_encodeUInt32(invokeId, response, bufPos);


                     236 

000000b0 e1a01005    237 	mov	r1,r5

000000b4 e1a02000    238 	mov	r2,r0

000000b8 e1a0000b    239 	mov	r0,fp

000000bc eb000000*   240 	bl	BerEncoder_encodeUInt32

                     241 ;150: 


                     242 ;151:     bufPos = BerEncoder_encodeTL(0xa1, getNameListSize, response, bufPos);


                     243 

000000c0 e1a02005    244 	mov	r2,r5

000000c4 e1a0100a    245 	mov	r1,r10

000000c8 e1a03000    246 	mov	r3,r0

000000cc e3a000a1    247 	mov	r0,161

000000d0 eb000000*   248 	bl	BerEncoder_encodeTL

                     249 ;152:     bufPos = BerEncoder_encodeTL(0xa0, identifierListSize, response, bufPos);


                     250 

000000d4 e1a02005    251 	mov	r2,r5

000000d8 e1a01006    252 	mov	r1,r6

000000dc e1a03000    253 	mov	r3,r0

000000e0 e3a000a0    254 	mov	r0,160

000000e4 eb000000*   255 	bl	BerEncoder_encodeTL

000000e8 e3a02ef0    256 	mov	r2,15<<8

000000ec e28220ed    257 	add	r2,r2,237

000000f0 e1a0a000    258 	mov	r10,r0

                     259 ;153: 


                     260 ;154:     //Настраиваем DomainNameWriter на фактическую запись


                     261 ;155:     DomainNameWriter_init(nameWriter, response + bufPos,


                     262 

000000f4 e08a1005    263 	add	r1,r10,r5

000000f8 e1a00004    264 	mov	r0,r4

000000fc eb000000*   265 	bl	DomainNameWriter_init

                     266 ;156:                           MAX_MMS_RESPONSE_SIZE - MAX_NAME_LIST_HEADER);


                     267 ;157:     if(continueAfter != NULL)


                     268 

00000100 e3570000    269 	cmp	r7,0

                     270 ;158:     {


                     271 

                     272 ;159:         DomainNameWriter_setStartName(nameWriter,continueAfter, continueAfterLen);


                     273 

00000104 159d2034    274 	ldrne	r2,[sp,52]

00000108 11a01007    275 	movne	r1,r7

0000010c 11a00004    276 	movne	r0,r4

00000110 1b000000*   277 	blne	DomainNameWriter_setStartName

                     278 ;160:     }


                     279 ;161: 


                     280 ;162:     writeChildrenNames(rootObjPos, nameWriter, recursive, objectsTagTowrite);


                     281 

00000114 e59d303c    282 	ldr	r3,[sp,60]

00000118 e5dd2038    283 	ldrb	r2,[sp,56]

0000011c e59d0008    284 	ldr	r0,[sp,8]

00000120 e1a01004    285 	mov	r1,r4

00000124 eb000000*   286 	bl	writeChildrenNames

                     287 ;163: 


                     288 ;164:     identifierListSize =  nameWriter->totalSize;


                     289 

00000128 e1a02005    290 	mov	r2,r5

0000012c e5940004    291 	ldr	r0,[r4,4]

                     292 ;165:     //debugSendUshort("nameWriter returned:", identifierListSize);


                     293 ;166: 


                     294 ;167:     bufPos += identifierListSize;



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_20c1.s
                     295 

00000130 e1a01009    296 	mov	r1,r9

00000134 e08a3000    297 	add	r3,r10,r0

                     298 ;168:     bufPos = BerEncoder_encodeBoolean(0x81, moreFollows, response, bufPos);


                     299 

00000138 e3a00081    300 	mov	r0,129

0000013c eb000000*   301 	bl	BerEncoder_encodeBoolean

                     302 ;169: 


                     303 ;170:     return bufPos;


                     304 

00000140 e28dd008    305 	add	sp,sp,8

00000144 e8bd8ff8    306 	ldmfd	[sp]!,{r3-fp,pc}

                     307 	.endf	mms_createNameListResponse

                     308 	.align	4

                     309 ;bufPos	r10	local

                     310 ;identifierListSize	r6	local

                     311 ;invokeIdSize	r8	local

                     312 ;listOfIdentifierSize	r0	local

                     313 ;getNameListSize	r10	local

                     314 ;confirmedServiceResponseSize	r1	local

                     315 ;moreFollows	r9	local

                     316 ;nameWriter	r4	local

                     317 

                     318 ;mmsConn	r0	param

                     319 ;invokeId	fp	param

                     320 ;response	r5	param

                     321 ;rootObjPos	[sp,8]	param

                     322 ;continueAfter	r7	param

                     323 ;continueAfterLen	[sp,52]	param

                     324 ;recursive	[sp,56]	param

                     325 ;objectsTagTowrite	[sp,60]	param

                     326 

                     327 	.section ".bss","awb"

                     328 .L64:

                     329 	.data

                     330 	.text

                     331 

                     332 ;171: }


                     333 

                     334 ;172: 


                     335 ;173: int mms_handleGetNameListRequest(MmsConnection* mmsConn,


                     336 	.align	4

                     337 	.align	4

                     338 mms_handleGetNameListRequest::

00000148 e92d4ff0    339 	stmfd	[sp]!,{r4-fp,lr}

                     340 ;174:                                  unsigned char* inBuf, int bufPos, int maxBufPos,


                     341 ;175:                                   unsigned int invokeId, unsigned char* response)


                     342 ;176: {


                     343 

0000014c e1a06003    344 	mov	r6,r3

00000150 e24dd02c    345 	sub	sp,sp,44

00000154 e59da054    346 	ldr	r10,[sp,84]

00000158 e3e0b000    347 	mvn	fp,0

0000015c e1a04002    348 	mov	r4,r2

00000160 e58d0028    349 	str	r0,[sp,40]

00000164 e3a00000    350 	mov	r0,0

00000168 e1a02000    351 	mov	r2,r0

0000016c e1a0c000    352 	mov	r12,r0

00000170 e1a05001    353 	mov	r5,r1

00000174 e28d1014    354 	add	r1,sp,20

00000178 e8811805    355 	stmea	[r1],{r0,r2,fp-r12}


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_20c1.s
                     356 ;177:     int responseSize = 0;


                     357 

                     358 ;178:     int rootObj;


                     359 ;179:     int objectClass = -1;


                     360 

                     361 ;180: 


                     362 ;181:     int objectScope = -1;


                     363 

                     364 ;182: 


                     365 ;183:     uint8_t* domainId = NULL;


                     366 

                     367 ;184:     int domainIdLength;


                     368 ;185: 


                     369 ;186:     uint8_t* continueAfter = NULL;


                     370 

                     371 ;187:     int continueAfterLength = 0;


                     372 

                     373 ;188: 


                     374 ;189:     //debugSendDump("\tdata:", inBuf + bufPos, maxBufPos - bufPos);


                     375 ;190: 


                     376 ;191:     while (bufPos < maxBufPos) {


                     377 

0000017c e1540006    378 	cmp	r4,r6

00000180 aa00004d    379 	bge	.L84

                     380 .L85:

                     381 ;192:         unsigned char tag = inBuf[bufPos++];


                     382 

00000184 e7d57004    383 	ldrb	r7,[r5,r4]

00000188 e2842001    384 	add	r2,r4,1

                     385 ;193:         int length;


                     386 ;194: 


                     387 ;195:         bufPos = BerDecoder_decodeLength(inBuf, &length, bufPos, maxBufPos);


                     388 

0000018c e1a03006    389 	mov	r3,r6

00000190 e28d1010    390 	add	r1,sp,16

00000194 e1a00005    391 	mov	r0,r5

00000198 eb000000*   392 	bl	BerDecoder_decodeLength

0000019c e1b04000    393 	movs	r4,r0

                     394 ;196: 


                     395 ;197:         if (bufPos < 0)  {


                     396 

000001a0 4a00006b    397 	bmi	.L116

                     398 ;198:             //mmsMsg_createMmsRejectPdu(&invokeId, MMS_ERROR_REJECT_INVALID_PDU, response);			


                     399 ;199:             return 0;


                     400 

                     401 ;200:         }


                     402 ;201: 


                     403 ;202:         //debugSendUshort("Name List request tag:", tag);


                     404 ;203: 


                     405 ;204: 


                     406 ;205:         switch (tag) {


                     407 

000001a4 e2571082    408 	subs	r1,r7,130

000001a8 0a00003c    409 	beq	.L101

000001ac e251101e    410 	subs	r1,r1,30

000001b0 0a000002    411 	beq	.L91

000001b4 e3510001    412 	cmp	r1,1

000001b8 0a00000d    413 	beq	.L92

000001bc ea000064    414 	b	.L116

                     415 .L91:

                     416 ;206:         case 0xa0: // objectClass



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_20c1.s
                     417 ;207:             bufPos++;


                     418 

000001c0 e2844001    419 	add	r4,r4,1

                     420 ;208:             length = inBuf[bufPos++];


                     421 

000001c4 e7d51004    422 	ldrb	r1,[r5,r4]

000001c8 e2844001    423 	add	r4,r4,1

000001cc e58d1010    424 	str	r1,[sp,16]

                     425 ;209:             objectClass = BerDecoder_decodeUint32(inBuf, length, bufPos);            


                     426 

000001d0 e1a02004    427 	mov	r2,r4

000001d4 e1a00005    428 	mov	r0,r5

000001d8 eb000000*   429 	bl	BerDecoder_decodeUint32

000001dc e59d1010    430 	ldr	r1,[sp,16]

                     431 ;245:             break;


                     432 ;246:         default:        


                     433 ;247:             return 0;


                     434 

                     435 ;248:             /*


                     436 ;249:             return CreateMmsConfirmedErrorPdu( invokeId, response,


                     437 ;250:                                                           MMS_ERROR_ACCESS_OBJECT_ACCESS_UNSUPPORTED );


                     438 ;251:                                                           */


                     439 ;252:         }


                     440 ;253: 


                     441 ;254:         bufPos += length;


                     442 

000001e0 e58d001c    443 	str	r0,[sp,28]

000001e4 e0844001    444 	add	r4,r4,r1

000001e8 e1540006    445 	cmp	r4,r6

000001ec baffffe4    446 	blt	.L85

000001f0 ea000031    447 	b	.L84

                     448 .L92:

                     449 ;210:             break;


                     450 ;211: 


                     451 ;212:         case 0xa1: // objectScope


                     452 ;213:             {


                     453 

                     454 ;214: 


                     455 ;215:                 unsigned char objectScopeTag = inBuf[bufPos++];


                     456 

000001f4 e7d5b004    457 	ldrb	fp,[r5,r4]

000001f8 e2842001    458 	add	r2,r4,1

                     459 ;216:                 bufPos = BerDecoder_decodeLength(inBuf, &length, bufPos, maxBufPos);


                     460 

000001fc e1a03006    461 	mov	r3,r6

00000200 e28d1010    462 	add	r1,sp,16

00000204 e1a00005    463 	mov	r0,r5

00000208 eb000000*   464 	bl	BerDecoder_decodeLength

0000020c e1a04000    465 	mov	r4,r0

                     466 ;217: 


                     467 ;218:                 //debugSendUshort("\tobjectScopeTag:", objectScopeTag);


                     468 ;219:                 switch (objectScopeTag) {


                     469 

00000210 e25bb080    470 	subs	fp,fp,128

00000214 3a000018    471 	blo	.L99

00000218 0a000003    472 	beq	.L95

0000021c e35b0002    473 	cmp	fp,2

00000220 3a000007    474 	blo	.L96

00000224 0a00000f    475 	beq	.L97

00000228 ea000013    476 	b	.L99

                     477 .L95:


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_20c1.s
                     478 ;220:                 case 0x80: // vmd-specific


                     479 ;221:                     objectScope = OBJECT_SCOPE_VMD;


                     480 

0000022c e59d1010    481 	ldr	r1,[sp,16]

                     482 ;245:             break;


                     483 ;246:         default:        


                     484 ;247:             return 0;


                     485 

                     486 ;248:             /*


                     487 ;249:             return CreateMmsConfirmedErrorPdu( invokeId, response,


                     488 ;250:                                                           MMS_ERROR_ACCESS_OBJECT_ACCESS_UNSUPPORTED );


                     489 ;251:                                                           */


                     490 ;252:         }


                     491 ;253: 


                     492 ;254:         bufPos += length;


                     493 

00000230 e3a0b000    494 	mov	fp,0

00000234 e0844001    495 	add	r4,r4,r1

00000238 e1540006    496 	cmp	r4,r6

0000023c baffffd0    497 	blt	.L85

00000240 ea00001d    498 	b	.L84

                     499 .L96:

                     500 ;222:                     break;


                     501 ;223:                 case 0x81: // domain-specific


                     502 ;224:                     domainIdLength = length;


                     503 

00000244 e59d1010    504 	ldr	r1,[sp,16]

00000248 e0840005    505 	add	r0,r4,r5

0000024c e58d1024    506 	str	r1,[sp,36]

                     507 ;225:                     domainId = inBuf + bufPos;


                     508 

00000250 e58d0014    509 	str	r0,[sp,20]

                     510 ;226:                     //debugSendUshort("\tdomainIdLength:", domainIdLength);


                     511 ;227:                     //debugSendStrL("\tdomainId:", domainId, domainIdLength);


                     512 ;228:                     objectScope = OBJECT_SCOPE_DOMAIN;


                     513 

00000254 e3a0b001    514 	mov	fp,1

                     515 ;245:             break;


                     516 ;246:         default:        


                     517 ;247:             return 0;


                     518 

                     519 ;248:             /*


                     520 ;249:             return CreateMmsConfirmedErrorPdu( invokeId, response,


                     521 ;250:                                                           MMS_ERROR_ACCESS_OBJECT_ACCESS_UNSUPPORTED );


                     522 ;251:                                                           */


                     523 ;252:         }


                     524 ;253: 


                     525 ;254:         bufPos += length;


                     526 

00000258 e0844001    527 	add	r4,r4,r1

0000025c e1540006    528 	cmp	r4,r6

00000260 baffffc7    529 	blt	.L85

00000264 ea000014    530 	b	.L84

                     531 .L97:

                     532 ;229:                     break;


                     533 ;230:                 case 0x82: // association-specific


                     534 ;231:                     objectScope = OBJECT_SCOPE_ASSOCIATION;                    


                     535 

                     536 ;232:                     return CreateMmsConfirmedErrorPdu( invokeId, response,


                     537 

00000268 e1a0100a    538 	mov	r1,r10


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_20c1.s
0000026c e59d0050    539 	ldr	r0,[sp,80]

00000270 e3a02052    540 	mov	r2,82

00000274 eb000000*   541 	bl	CreateMmsConfirmedErrorPdu

00000278 ea000036    542 	b	.L81

                     543 .L99:

                     544 ;233:                                                    MMS_ERROR_ACCESS_OBJECT_ACCESS_UNSUPPORTED );


                     545 ;234:                 default:


                     546 ;235:                     debugSendUshort("!!!!! Unsupported objectScope:",tag);


                     547 

0000027c e28f0000*   548 	adr	r0,.L339

00000280 e1a01007    549 	mov	r1,r7

00000284 eb000000*   550 	bl	debugSendUshort

                     551 ;236:                     mms_createMmsRejectPdu(&invokeId, MMS_ERROR_REJECT_UNRECOGNIZED_MODIFIER, response);


                     552 

00000288 e1a0200a    553 	mov	r2,r10

0000028c e28d0050    554 	add	r0,sp,80

00000290 e3a01068    555 	mov	r1,104

00000294 eb000000*   556 	bl	mms_createMmsRejectPdu

                     557 ;237:                     return 0;


                     558 

00000298 e3a00000    559 	mov	r0,0

0000029c ea00002d    560 	b	.L81

                     561 .L101:

                     562 ;238:                 }                


                     563 ;239:             }


                     564 ;240:             break;


                     565 ;241: 


                     566 ;242:         case TAG_CONTINUE_AFTER:


                     567 ;243:             continueAfter = inBuf + bufPos;


                     568 

000002a0 e0840005    569 	add	r0,r4,r5

000002a4 e59d1010    570 	ldr	r1,[sp,16]

000002a8 e58d0018    571 	str	r0,[sp,24]

                     572 ;244:             continueAfterLength = length;


                     573 

000002ac e58d1020    574 	str	r1,[sp,32]

                     575 ;245:             break;


                     576 ;246:         default:        


                     577 ;247:             return 0;


                     578 

                     579 ;248:             /*


                     580 ;249:             return CreateMmsConfirmedErrorPdu( invokeId, response,


                     581 ;250:                                                           MMS_ERROR_ACCESS_OBJECT_ACCESS_UNSUPPORTED );


                     582 ;251:                                                           */


                     583 ;252:         }


                     584 ;253: 


                     585 ;254:         bufPos += length;


                     586 

000002b0 e0844001    587 	add	r4,r4,r1

000002b4 e1540006    588 	cmp	r4,r6

000002b8 baffffb1    589 	blt	.L85

                     590 .L84:

                     591 ;255:     }           


                     592 ;256: 


                     593 ;257:     switch(objectScope)


                     594 

000002bc e35b0001    595 	cmp	fp,1

000002c0 0a00000a    596 	beq	.L108

000002c4 2a000022    597 	bhs	.L116

                     598 ;258:     {


                     599 ;259:     case OBJECT_SCOPE_VMD:



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_20c1.s
                     600 ;260:         //debugSendText("Process OBJECT_SCOPE_VMD");


                     601 ;261:         responseSize =


                     602 

000002c8 e3e04000    603 	mvn	r4,0

000002cc e59d0018    604 	ldr	r0,[sp,24]

000002d0 e59d1020    605 	ldr	r1,[sp,32]

000002d4 e3a03000    606 	mov	r3,0

000002d8 e88d001b    607 	stmea	[sp],{r0-r1,r3-r4}

000002dc e59d0028    608 	ldr	r0,[sp,40]

000002e0 e59d1050    609 	ldr	r1,[sp,80]

000002e4 e1a0200a    610 	mov	r2,r10

000002e8 ebffff44*   611 	bl	mms_createNameListResponse

                     612 ;262:                 mms_createNameListResponse(mmsConn, invokeId,  response, 0,


                     613 ;263: 					continueAfter, continueAfterLength, FALSE, IED_ANY_TAG);


                     614 ;264:         return responseSize;


                     615 

000002ec ea000019    616 	b	.L81

                     617 .L108:

                     618 ;265:     case OBJECT_SCOPE_DOMAIN:


                     619 ;266: 		{


                     620 

                     621 ;267: 			int ldSection;


                     622 ;268: 			int objectsTagToWrite;


                     623 ;269: 			if (objectClass == 2 /*OBJECT_CLASS_NAMED_VARIABLE_LIST*/)


                     624 

000002f0 e59d2024    625 	ldr	r2,[sp,36]

000002f4 e59d001c    626 	ldr	r0,[sp,28]

000002f8 e59d1014    627 	ldr	r1,[sp,20]

000002fc e3500002    628 	cmp	r0,2

                     629 ;270: 			{


                     630 

                     631 ;271: 				//Для Data sets


                     632 ;272: 				ldSection = IED_VMD_DATA_SET_SECTION;


                     633 

00000300 03a000ee    634 	moveq	r0,238

                     635 ;273: 				objectsTagToWrite = IED_DATA_SET;


                     636 

00000304 13a000ec    637 	movne	r0,236

                     638 ;279: 				objectsTagToWrite = IED_ANY_TAG;


                     639 

00000308 03a040e7    640 	moveq	r4,231

                     641 ;280: 			}


                     642 ;281: 			


                     643 ;282: 			rootObj = findDomainSection(ldSection,


                     644 

                     645 ;274: 			}


                     646 ;275: 			else


                     647 ;276: 			{


                     648 

                     649 ;277:                 //Для обычных данных


                     650 ;278: 				ldSection = IED_VMD_DATA_SECTION;


                     651 

0000030c 13e04000    652 	mvnne	r4,0

                     653 ;280: 			}


                     654 ;281: 			


                     655 ;282: 			rootObj = findDomainSection(ldSection,


                     656 

00000310 eb000000*   657 	bl	findDomainSection

00000314 e1b03000    658 	movs	r3,r0

                     659 ;283: 				domainId, domainIdLength);


                     660 ;284: 			if (rootObj == 0)



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_20c1.s
                     661 

00000318 1a000004    662 	bne	.L112

                     663 ;285: 			{


                     664 

                     665 ;286: 				ERROR_REPORT("Unable to find domain cection");


                     666 ;287: 				return CreateMmsConfirmedErrorPdu(invokeId, response,


                     667 

0000031c e1a0100a    668 	mov	r1,r10

00000320 e59d0050    669 	ldr	r0,[sp,80]

00000324 e3a02051    670 	mov	r2,81

00000328 eb000000*   671 	bl	CreateMmsConfirmedErrorPdu

0000032c ea000009    672 	b	.L81

                     673 .L112:

                     674 ;288: 					MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);


                     675 ;289: 			}


                     676 ;290: 			return mms_createNameListResponse(mmsConn, invokeId, response,


                     677 

00000330 e59d0018    678 	ldr	r0,[sp,24]

00000334 e59d1020    679 	ldr	r1,[sp,32]

00000338 e3a02001    680 	mov	r2,1

0000033c e88d0017    681 	stmea	[sp],{r0-r2,r4}

00000340 e59d0028    682 	ldr	r0,[sp,40]

00000344 e59d1050    683 	ldr	r1,[sp,80]

00000348 e1a0200a    684 	mov	r2,r10

0000034c ebffff2b*   685 	bl	mms_createNameListResponse

00000350 ea000000    686 	b	.L81

                     687 .L116:

                     688 ;291: 				rootObj, continueAfter, continueAfterLength, TRUE,


                     689 ;292: 				objectsTagToWrite);


                     690 ;293: 		}                


                     691 ;294:     default:


                     692 ;295:         return 0;


                     693 

00000354 e3a00000    694 	mov	r0,0

                     695 .L81:

00000358 e28dd02c    696 	add	sp,sp,44

0000035c e8bd8ff0    697 	ldmfd	[sp]!,{r4-fp,pc}

                     698 	.endf	mms_handleGetNameListRequest

                     699 	.align	4

                     700 ;rootObj	r3	local

                     701 ;objectClass	[sp,28]	local

                     702 ;objectScope	fp	local

                     703 ;domainId	[sp,20]	local

                     704 ;domainIdLength	[sp,36]	local

                     705 ;continueAfter	[sp,24]	local

                     706 ;continueAfterLength	[sp,32]	local

                     707 ;tag	r7	local

                     708 ;length	[sp,16]	local

                     709 ;objectScopeTag	fp	local

                     710 ;.L279	.L282	static

                     711 ;objectsTagToWrite	r4	local

                     712 

                     713 ;mmsConn	[sp,40]	param

                     714 ;inBuf	r5	param

                     715 ;bufPos	r4	param

                     716 ;maxBufPos	r6	param

                     717 ;invokeId	[sp,80]	param

                     718 ;response	r10	param

                     719 

                     720 	.section ".bss","awb"

                     721 .L278:


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_20c1.s
                     722 	.data

                     723 	.text

                     724 

                     725 ;296:     }


                     726 ;297: 


                     727 ;298: }


                     728 	.align	4

                     729 .L339:

                     730 ;	"!!!!! Unsupported objectScope:\000"

00000360 21212121    731 	.data.b	33,33,33,33

00000364 6e552021    732 	.data.b	33,32,85,110

00000368 70707573    733 	.data.b	115,117,112,112

0000036c 6574726f    734 	.data.b	111,114,116,101

00000370 626f2064    735 	.data.b	100,32,111,98

00000374 7463656a    736 	.data.b	106,101,99,116

00000378 706f6353    737 	.data.b	83,99,111,112

0000037c 3a65       738 	.data.b	101,58

0000037e 00         739 	.data.b	0

0000037f 00         740 	.align 4

                     741 

                     742 	.type	.L339,$object

                     743 	.size	.L339,4

                     744 

                     745 	.align	4

                     746 

                     747 	.data

                     748 	.ghsnote version,6

                     749 	.ghsnote tools,3

                     750 	.ghsnote options,0

                     751 	.text

                     752 	.align	4

