                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a2o1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=ReportQueue.c -o gh_a2o1.o -list=ReportQueue.lst C:\Users\<USER>\AppData\Local\Temp\gh_a2o1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_a2o1.s
Source File: ReportQueue.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile ReportQueue.c

                      11 ;		-o ReportQueue.o

                      12 ;Source File:   ReportQueue.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:08 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "ReportQueue.h"


                      23 ;2: #include <debug.h>


                      24 ;3: #include <string.h>


                      25 ;4: 


                      26 ;5: 


                      27 ;6: 


                      28 ;7: static void lockQueue(ReportQueue *queue)


                      29 ;8: {


                      30 ;9: 	CriticalSection_Lock(&(queue->criticalSection));


                      31 ;10: }


                      32 ;11: static void unlockQueue(ReportQueue *queue)


                      33 ;12: {


                      34 ;13: 	CriticalSection_Unlock(&(queue->criticalSection));


                      35 ;14: }


                      36 ;15: 	


                      37 ;16: 


                      38 ;17: void ReportQueue_init(ReportQueue *queue)


                      39 ;18: {


                      40 ;19: 	queue->chunkCount = REPORT_MEM_SIZE / REPORT_CHUNK_SIZE;


                      41 ;20: 	queue->criticalErrorCount = 0;


                      42 ;21: 	queue->lastOverflowCount = queue->overflowCount = 0;


                      43 ;22: 	queue->head = 0;


                      44 ;23: 	queue->tail = 0;


                      45 ;24: 	CriticalSection_Init(&queue->criticalSection);


                      46 ;25: }


                      47 ;26: 


                      48 ;27: static int getFreeChunkCount(ReportQueue *queue)


                      49 ;28: {


                      50 ;29: 	int freeChunkCount;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a2o1.s
                      51 ;30: 	// очередь переполнена


                      52 ;31: 	if (queue->head == -1)


                      53 ;32: 	{


                      54 ;33: 		return 0;


                      55 ;34: 	}


                      56 ;35: 	// очередь пустая


                      57 ;36: 	if (queue->head == queue->tail)


                      58 ;37: 	{


                      59 ;38: 		return queue->chunkCount;


                      60 ;39: 	}


                      61 ;40: 	// смотрим сколько осталось


                      62 ;41: 	if (queue->head > queue->tail)


                      63 ;42: 	{


                      64 ;43: 		


                      65 ;44: 		freeChunkCount = (queue->chunkCount - queue->head) + queue->tail;


                      66 ;45: 	}


                      67 ;46: 	else


                      68 ;47: 	{	


                      69 ;48: 		freeChunkCount = queue->tail - queue->head;


                      70 ;49: 	}


                      71 ;50: 


                      72 ;51: 	return freeChunkCount;


                      73 ;52: }


                      74 ;53: 


                      75 ;54: // пустое чтение, для осбождения памяти


                      76 ;55: static void dummyRead(ReportQueue *queue)


                      77 

                      78 ;89: }


                      79 

                      80 ;90: 


                      81 ;91: static void freeChunk(ReportQueue *queue, int chunkCount)


                      82 

                      83 ;96: 	} while (getFreeChunkCount(queue) < chunkCount);


                      84 ;97: }


                      85 

                      86 	.text

                      87 	.align	4

                      88 lockQueue:

00000000 e92d4000     89 	stmfd	[sp]!,{lr}

00000004 e2801b42     90 	add	r1,r0,66<<10

00000008 e2810018     91 	add	r0,r1,24

0000000c eb000000*    92 	bl	CriticalSection_Lock

00000010 e8bd4000     93 	ldmfd	[sp]!,{lr}

00000014 e12fff1e*    94 	ret	

                      95 	.endf	lockQueue

                      96 	.align	4

                      97 

                      98 ;queue	r0	param

                      99 

                     100 	.section ".bss","awb"

                     101 .L126:

                     102 	.data

                     103 	.text

                     104 

                     105 

                     106 	.align	4

                     107 	.align	4

                     108 unlockQueue:

00000018 e92d4000    109 	stmfd	[sp]!,{lr}

0000001c e2801b42    110 	add	r1,r0,66<<10

00000020 e2810018    111 	add	r0,r1,24


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a2o1.s
00000024 eb000000*   112 	bl	CriticalSection_Unlock

00000028 e8bd4000    113 	ldmfd	[sp]!,{lr}

0000002c e12fff1e*   114 	ret	

                     115 	.endf	unlockQueue

                     116 	.align	4

                     117 

                     118 ;queue	r0	param

                     119 

                     120 	.section ".bss","awb"

                     121 .L158:

                     122 	.data

                     123 	.text

                     124 

                     125 

                     126 	.align	4

                     127 	.align	4

                     128 ReportQueue_init::

00000030 e3a01b42    129 	mov	r1,66<<10

00000034 e2811008    130 	add	r1,r1,8

00000038 e3a02f80    131 	mov	r2,1<<9

0000003c e7812000    132 	str	r2,[r1,r0]

00000040 e2802b42    133 	add	r2,r0,66<<10

00000044 e3a01000    134 	mov	r1,0

00000048 e5821014    135 	str	r1,[r2,20]

0000004c e3a02b42    136 	mov	r2,66<<10

00000050 e282200c    137 	add	r2,r2,12

00000054 e7821000    138 	str	r1,[r2,r0]

00000058 e3a02b42    139 	mov	r2,66<<10

0000005c e2822010    140 	add	r2,r2,16

00000060 e7821000    141 	str	r1,[r2,r0]

00000064 e2802b42    142 	add	r2,r0,66<<10

00000068 e5821000    143 	str	r1,[r2]

0000006c e3a02b42    144 	mov	r2,66<<10

00000070 e2822004    145 	add	r2,r2,4

00000074 e7821000    146 	str	r1,[r2,r0]

00000078 e2801b42    147 	add	r1,r0,66<<10

0000007c e2810018    148 	add	r0,r1,24

00000080 ea000000*   149 	b	CriticalSection_Init

                     150 	.endf	ReportQueue_init

                     151 	.align	4

                     152 

                     153 ;queue	r0	param

                     154 

                     155 	.section ".bss","awb"

                     156 .L190:

                     157 	.data

                     158 	.text

                     159 

                     160 

                     161 	.align	4

                     162 	.align	4

                     163 getFreeChunkCount:

00000084 e2801b42    164 	add	r1,r0,66<<10

00000088 e5911000    165 	ldr	r1,[r1]

0000008c e3710001    166 	cmn	r1,1

00000090 03a00000    167 	moveq	r0,0

00000094 0a00000e    168 	beq	.L197

00000098 e3a02b42    169 	mov	r2,66<<10

0000009c e2822004    170 	add	r2,r2,4

000000a0 e7922000    171 	ldr	r2,[r2,r0]

000000a4 e1510002    172 	cmp	r1,r2


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a2o1.s
000000a8 03a01b42    173 	moveq	r1,66<<10

000000ac 02811008    174 	addeq	r1,r1,8

000000b0 07900001    175 	ldreq	r0,[r0,r1]

000000b4 0a000006    176 	beq	.L197

000000b8 e1510002    177 	cmp	r1,r2

000000bc c3a03b42    178 	movgt	r3,66<<10

000000c0 c2833008    179 	addgt	r3,r3,8

000000c4 c7900003    180 	ldrgt	r0,[r0,r3]

000000c8 c0400001    181 	subgt	r0,r0,r1

000000cc c0820000    182 	addgt	r0,r2,r0

000000d0 d0420001    183 	suble	r0,r2,r1

                     184 .L197:

000000d4 e12fff1e*   185 	ret	

                     186 	.endf	getFreeChunkCount

                     187 	.align	4

                     188 

                     189 ;queue	r0	param

                     190 

                     191 	.section ".bss","awb"

                     192 .L250:

                     193 	.data

                     194 	.text

                     195 

                     196 

                     197 ;98: 


                     198 ;99: int	 ReportQueue_write(ReportQueue *queue, unsigned char *data, int size)


                     199 	.align	4

                     200 	.align	4

                     201 ReportQueue_write::

000000d8 e92d4ff6    202 	stmfd	[sp]!,{r1-r2,r4-fp,lr}

                     203 ;100: {


                     204 

                     205 ;101: 	ReportChunk *chunk = NULL;


                     206 

                     207 ;102: 	int takeChunkCount;


                     208 ;103: 	int freeChunkCount;


                     209 ;104: 	int head;


                     210 ;105: 	int copyCount;


                     211 ;106: 	int sizeToWrite = size;


                     212 

                     213 ;107: 			


                     214 ;108: 	// 


                     215 ;109: 	if (size <= 0)


                     216 

000000dc e24dd008    217 	sub	sp,sp,8

000000e0 e58d1008    218 	str	r1,[sp,8]

000000e4 e58d200c    219 	str	r2,[sp,12]

000000e8 e3520000    220 	cmp	r2,0

                     221 ;110: 	{


                     222 

                     223 ;111: 		return 0;


                     224 

000000ec d3a00000    225 	movle	r0,0

000000f0 da000068    226 	ble	.L273

000000f4 e3a0b000    227 	mov	fp,0

000000f8 e1a06000    228 	mov	r6,r0

000000fc e2867b42    229 	add	r7,r6,66<<10

00000100 e1a0a002    230 	mov	r10,r2

                     231 ;112: 	}


                     232 ;113: 


                     233 ;114: 	// количество занимаемых кусков



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a2o1.s
                     234 ;115: 	takeChunkCount = (size / REPORT_CHUNK_SIZE) + (size % REPORT_CHUNK_SIZE != 0);


                     235 

00000104 e1a00fc2    236 	mov	r0,r2 asr 31

00000108 e200307f    237 	and	r3,r0,127

0000010c e0823003    238 	add	r3,r2,r3

00000110 e3c3307f    239 	bic	r3,r3,127

00000114 e1520003    240 	cmp	r2,r3

00000118 e0820ca0    241 	add	r0,r2,r0 lsr 25

0000011c e1a003c0    242 	mov	r0,r0 asr 7

00000120 12802001    243 	addne	r2,r0,1

00000124 11a08002    244 	movne	r8,r2

00000128 01a08000    245 	moveq	r8,r0

                     246 ;116: 	// пытаемся положить данных больше, чем общая память


                     247 ;117: 	if (takeChunkCount > queue->chunkCount)


                     248 

0000012c e5970008    249 	ldr	r0,[r7,8]

00000130 e1a01008    250 	mov	r1,r8

00000134 e1510000    251 	cmp	r1,r0

00000138 da000007    252 	ble	.L278

                     253 ;118: 	{


                     254 

                     255 ;119: 		queue->criticalErrorCount++;


                     256 

0000013c e5970014    257 	ldr	r0,[r7,20]

00000140 e2800001    258 	add	r0,r0,1

00000144 e5870014    259 	str	r0,[r7,20]

                     260 ;120: 		queue->overflowCount++;


                     261 

00000148 e597000c    262 	ldr	r0,[r7,12]

0000014c e2800001    263 	add	r0,r0,1

00000150 e587000c    264 	str	r0,[r7,12]

                     265 ;121: 		return 0;


                     266 

00000154 e3a00000    267 	mov	r0,0

00000158 ea00004e    268 	b	.L273

                     269 .L278:

                     270 ;122: 	}


                     271 ;123: 


                     272 ;124: 	// блокировка, т.к. может вызваться purge


                     273 ;125: 	lockQueue(queue);


                     274 

0000015c e1a00006    275 	mov	r0,r6

00000160 ebffffa6*   276 	bl	lockQueue

                     277 ;126: 	freeChunkCount = getFreeChunkCount(queue);


                     278 

00000164 e1a00006    279 	mov	r0,r6

00000168 ebffffc5*   280 	bl	getFreeChunkCount

                     281 ;127: 	if (freeChunkCount < takeChunkCount)


                     282 

0000016c e1500008    283 	cmp	r0,r8

00000170 ba000003    284 	blt	.L282

00000174 e5974000    285 	ldr	r4,[r7]

                     286 ;132: 	}


                     287 ;133: 


                     288 ;134: 	head = queue->head;


                     289 

                     290 ;135: 


                     291 ;136: 	while (sizeToWrite > 0)


                     292 

00000178 e35a0000    293 	cmp	r10,0

0000017c da00003b    294 	ble	.L305


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a2o1.s
00000180 ea000024    295 	b	.L302

                     296 .L282:

                     297 ;128: 	{


                     298 

                     299 ;129: 		queue->overflowCount++;


                     300 

00000184 e8970234    301 	ldmfd	[r7],{r2,r4-r5,r9}

00000188 e2890001    302 	add	r0,r9,1

0000018c e587000c    303 	str	r0,[r7,12]

                     304 ;130: 		// освобождается нужное количество


                     305 ;131: 		freeChunk(queue, takeChunkCount);


                     306 

00000190 e1a09005    307 	mov	r9,r5

00000194 e1a05002    308 	mov	r5,r2

                     309 .L286:

                     310 ;92: {	


                     311 

                     312 ;93: 	do


                     313 

                     314 ;94: 	{


                     315 

                     316 ;95: 		dummyRead(queue);


                     317 

                     318 ;56: {


                     319 

                     320 ;57: 	ReportChunk *chunk;


                     321 ;58: 	int tail;


                     322 ;59: 	


                     323 ;60: 	if (queue->head == -1)


                     324 

00000198 e3750001    325 	cmn	r5,1

                     326 ;61: 	{


                     327 

                     328 ;62: 		queue->head = queue->tail;


                     329 

0000019c 01a05004    330 	moveq	r5,r4

000001a0 05875000    331 	streq	r5,[r7]

                     332 ;63: 	}


                     333 ;64: 


                     334 ;65: 	tail = queue->tail;


                     335 

                     336 ;66: 	while (1)


                     337 

000001a4 e1a0c009    338 	mov	r12,r9

000001a8 e1a0e005    339 	mov	lr,r5

000001ac e3a02000    340 	mov	r2,0

                     341 .L291:

000001b0 e0840284    342 	add	r0,r4,r4 lsl 5

000001b4 e2844001    343 	add	r4,r4,1

000001b8 e154000c    344 	cmp	r4,r12

000001bc e0860100    345 	add	r0,r6,r0 lsl 2

000001c0 e5d03002    346 	ldrb	r3,[r0,2]

000001c4 a1a04002    347 	movge	r4,r2

                     348 ;67: 	{


                     349 

                     350 ;68: 		chunk = &queue->chunk[tail];


                     351 

                     352 ;69: 		tail++;


                     353 

                     354 ;70: 		if (tail >= queue->chunkCount)


                     355 


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a2o1.s
                     356 

                     357 

                     358 ;73: 		}


                     359 ;74: 


                     360 ;75: 		if (chunk->lastFlag)


                     361 

000001c8 e3530000    362 	cmp	r3,0

000001cc 0a000007    363 	beq	.L293

                     364 ;76: 		{


                     365 

                     366 ;77: 			chunk->lastFlag = 0;


                     367 

000001d0 e3a03000    368 	mov	r3,0

000001d4 e5c03002    369 	strb	r3,[r0,2]

                     370 ;78: 			break;


                     371 

                     372 ;83: 		{


                     373 

                     374 ;84: 			break;


                     375 

                     376 ;85: 		}


                     377 ;86: 	}


                     378 ;87: 	


                     379 ;88: 	queue->tail = tail;


                     380 

000001d8 e5874004    381 	str	r4,[r7,4]

000001dc e1a00006    382 	mov	r0,r6

000001e0 ebffffa7*   383 	bl	getFreeChunkCount

000001e4 e1500008    384 	cmp	r0,r8

000001e8 baffffea    385 	blt	.L286

000001ec ea000006    386 	b	.L281

                     387 .L293:

                     388 ;79: 		}


                     389 ;80: 


                     390 ;81: 		// очередь полностью освободилась


                     391 ;82: 		if (tail == queue->head)


                     392 

000001f0 e154000e    393 	cmp	r4,lr

000001f4 1affffed    394 	bne	.L291

                     395 ;83: 		{


                     396 

                     397 ;84: 			break;


                     398 

                     399 ;85: 		}


                     400 ;86: 	}


                     401 ;87: 	


                     402 ;88: 	queue->tail = tail;


                     403 

000001f8 e5874004    404 	str	r4,[r7,4]

000001fc e1a00006    405 	mov	r0,r6

00000200 ebffff9f*   406 	bl	getFreeChunkCount

00000204 e1500008    407 	cmp	r0,r8

00000208 baffffe2    408 	blt	.L286

                     409 .L281:

                     410 ;132: 	}


                     411 ;133: 


                     412 ;134: 	head = queue->head;


                     413 

0000020c e1a04005    414 	mov	r4,r5

                     415 ;135: 


                     416 ;136: 	while (sizeToWrite > 0)



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a2o1.s
                     417 

00000210 e35a0000    418 	cmp	r10,0

00000214 da000015    419 	ble	.L305

                     420 .L302:

00000218 e35a0080    421 	cmp	r10,128

0000021c d1a0500a    422 	movle	r5,r10

00000220 c3a05080    423 	movgt	r5,128

00000224 e1a02005    424 	mov	r2,r5

00000228 e0840284    425 	add	r0,r4,r4 lsl 5

0000022c e086b100    426 	add	fp,r6,r0 lsl 2

00000230 e59d1008    427 	ldr	r1,[sp,8]

00000234 e28b0004    428 	add	r0,fp,4

00000238 eb000000*   429 	bl	memcpy

                     430 ;137: 	{


                     431 

                     432 ;138: 		copyCount = sizeToWrite > REPORT_CHUNK_SIZE ? REPORT_CHUNK_SIZE : sizeToWrite;


                     433 

                     434 ;139: 		chunk = &queue->chunk[head];


                     435 

                     436 ;140: 


                     437 ;141: 		if (copyCount == REPORT_CHUNK_SIZE)


                     438 

                     439 ;142: 		{


                     440 

                     441 ;143: 			//Закомментировано потому что выравнивание на 4 не гарантировано


                     442 ;144: 			//copyChunk(chunk->payload, data);


                     443 ;145: 			memcpy(chunk->payload, data, copyCount);


                     444 

                     445 ;146: 		}


                     446 ;147: 		else


                     447 ;148: 		{


                     448 

                     449 ;149: 			memcpy(chunk->payload, data, copyCount);


                     450 

                     451 ;150: 		}


                     452 ;151: 


                     453 ;152: 		sizeToWrite -= copyCount;


                     454 

0000023c e1cb50b0    455 	strh	r5,[fp]

                     456 ;154: 		chunk->lastFlag = 0;


                     457 

00000240 e3a00000    458 	mov	r0,0

00000244 e5cb0002    459 	strb	r0,[fp,2]

                     460 ;155: 		data += copyCount;


                     461 

00000248 e59d0008    462 	ldr	r0,[sp,8]

0000024c e04aa005    463 	sub	r10,r10,r5

                     464 ;153: 		chunk->len = copyCount;


                     465 

00000250 e0800005    466 	add	r0,r0,r5

00000254 e58d0008    467 	str	r0,[sp,8]

                     468 ;156: 		head++;


                     469 

00000258 e5970008    470 	ldr	r0,[r7,8]

0000025c e2844001    471 	add	r4,r4,1

                     472 ;157: 		if (head >= queue->chunkCount)


                     473 

                     474 

                     475 

00000260 e1540000    476 	cmp	r4,r0

00000264 a3a04000    477 	movge	r4,0


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a2o1.s
00000268 e35a0000    478 	cmp	r10,0

0000026c caffffe9    479 	bgt	.L302

                     480 .L305:

00000270 e3a00001    481 	mov	r0,1

00000274 e5cb0002    482 	strb	r0,[fp,2]

00000278 e5970004    483 	ldr	r0,[r7,4]

0000027c e1500004    484 	cmp	r0,r4

00000280 11a00004    485 	movne	r0,r4

00000284 03e00000    486 	mvneq	r0,0

                     487 ;160: 		}


                     488 ;161: 	}


                     489 ;162: 	


                     490 ;163: 	// тут chunk уже не может быть NULL


                     491 ;164: 	chunk->lastFlag = 1;


                     492 

                     493 ;165: 


                     494 ;166: 	// последний элемент


                     495 ;167: 	if (head == queue->tail) // переполнение


                     496 

                     497 

                     498 

                     499 ;170: 	}


                     500 ;171: 


                     501 ;172: 	queue->head = head;


                     502 

00000288 e5870000    503 	str	r0,[r7]

                     504 ;173: 	unlockQueue(queue);


                     505 

0000028c e1a00006    506 	mov	r0,r6

00000290 ebffff60*   507 	bl	unlockQueue

                     508 ;174: 	return size;


                     509 

00000294 e59d000c    510 	ldr	r0,[sp,12]

                     511 .L273:

00000298 e28dd008    512 	add	sp,sp,8

0000029c e8bd8ff6    513 	ldmfd	[sp]!,{r1-r2,r4-fp,pc}

                     514 	.endf	ReportQueue_write

                     515 	.align	4

                     516 ;chunk	fp	local

                     517 ;takeChunkCount	r8	local

                     518 ;head	r4	local

                     519 ;copyCount	r5	local

                     520 ;sizeToWrite	r10	local

                     521 ;chunk	r0	local

                     522 ;tail	r4	local

                     523 

                     524 ;queue	r6	param

                     525 ;data	[sp,8]	param

                     526 ;size	[sp,12]	param

                     527 

                     528 	.section ".bss","awb"

                     529 .L550:

                     530 	.data

                     531 	.text

                     532 

                     533 ;175: }


                     534 

                     535 ;176: 


                     536 ;177: int ReportQueue_read(ReportQueue *queue, unsigned char *data, int maxDataSize)


                     537 	.align	4

                     538 	.align	4


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a2o1.s
                     539 ReportQueue_read::

000002a0 e92d4ff0    540 	stmfd	[sp]!,{r4-fp,lr}

                     541 ;178: {


                     542 

                     543 ;179: 	int copyCount = 0;


                     544 

                     545 ;180: 	int tail;


                     546 ;181: 	ReportChunk *chunk;


                     547 ;182: 


                     548 ;183: 	if (ReportQueue_isEmpty(queue))


                     549 

000002a4 e24dd008    550 	sub	sp,sp,8

000002a8 e1a06001    551 	mov	r6,r1

000002ac e1a07002    552 	mov	r7,r2

000002b0 e1a0a000    553 	mov	r10,r0

000002b4 eb000041*   554 	bl	ReportQueue_isEmpty

000002b8 e3500000    555 	cmp	r0,0

                     556 ;184: 	{


                     557 

                     558 ;185: 		return 0;


                     559 

000002bc 13a00000    560 	movne	r0,0

000002c0 1a00002c    561 	bne	.L605

000002c4 e3a0b000    562 	mov	fp,0

                     563 ;186: 	}


                     564 ;187: 	


                     565 ;188: 	lockQueue(queue);


                     566 

000002c8 e1a0000a    567 	mov	r0,r10

000002cc ebffff4b*   568 	bl	lockQueue

                     569 ;189: 


                     570 ;190: 	if (queue->head == -1)


                     571 

000002d0 e28a0b42    572 	add	r0,r10,66<<10

000002d4 e5900000    573 	ldr	r0,[r0]

000002d8 e3a05b42    574 	mov	r5,66<<10

000002dc e2855004    575 	add	r5,r5,4

000002e0 e79a4005    576 	ldr	r4,[r10,r5]

000002e4 e3700001    577 	cmn	r0,1

                     578 ;191: 	{


                     579 

                     580 ;192: 		queue->head = queue->tail;


                     581 

000002e8 028a0b42    582 	addeq	r0,r10,66<<10

000002ec 05804000    583 	streq	r4,[r0]

                     584 ;193: 	}


                     585 ;194: 


                     586 ;195: 	tail = queue->tail;


                     587 

                     588 ;196: 	while (1)


                     589 

000002f0 e08a9005    590 	add	r9,r10,r5

000002f4 e2898004    591 	add	r8,r9,4

                     592 .L614:

                     593 ;197: 	{


                     594 

                     595 ;198: 		chunk = &queue->chunk[tail];


                     596 

000002f8 e0840284    597 	add	r0,r4,r4 lsl 5

000002fc e1a05100    598 	mov	r5,r0 lsl 2

                     599 ;199: 		if (maxDataSize > chunk->len)



                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a2o1.s
                     600 

00000300 e1b520ba    601 	ldrh	r2,[r5,r10]!

00000304 e1570002    602 	cmp	r7,r2

00000308 da000005    603 	ble	.L620

0000030c e2851004    604 	add	r1,r5,4

00000310 e1a00006    605 	mov	r0,r6

00000314 eb000000*   606 	bl	memcpy

                     607 ;200: 		{


                     608 

                     609 ;201: 			if (chunk->len == REPORT_CHUNK_SIZE)


                     610 

                     611 ;202: 			{


                     612 

                     613 ;203: 				//Закомментировано потому что выравнивание на 4 не гарантировано


                     614 ;204: 				//copyChunk(data, chunk->payload);


                     615 ;205: 				memcpy(data, chunk->payload, chunk->len);


                     616 

                     617 ;206: 			}


                     618 ;207: 			else


                     619 ;208: 			{


                     620 

                     621 ;209: 				memcpy(data, chunk->payload, chunk->len);


                     622 

                     623 ;210: 			}


                     624 ;211: 			copyCount += chunk->len;


                     625 

00000318 e1d520b0    626 	ldrh	r2,[r5]

0000031c e0866002    627 	add	r6,r6,r2

00000320 e08bb002    628 	add	fp,fp,r2

                     629 ;212: 			data += chunk->len;


                     630 

                     631 .L620:

00000324 e0477002    632 	sub	r7,r7,r2

00000328 e1a01008    633 	mov	r1,r8

0000032c e5910000    634 	ldr	r0,[r1]

00000330 e2844001    635 	add	r4,r4,1

00000334 e1540000    636 	cmp	r4,r0

00000338 e5d50002    637 	ldrb	r0,[r5,2]

0000033c a3a04000    638 	movge	r4,0

                     639 ;213: 		}


                     640 ;214: 


                     641 ;215: 		maxDataSize -= chunk->len;


                     642 

                     643 ;216: 		tail++;


                     644 

                     645 ;217: 		if (tail >= queue->chunkCount)


                     646 

                     647 

                     648 

                     649 ;220: 		}


                     650 ;221: 		if (chunk->lastFlag)


                     651 

00000340 e3500000    652 	cmp	r0,0

00000344 0affffeb    653 	beq	.L614

                     654 ;222: 		{


                     655 

                     656 ;223: 			chunk->lastFlag = 0;


                     657 

00000348 e3a00000    658 	mov	r0,0

0000034c e5c50002    659 	strb	r0,[r5,2]

                     660 ;224: 			break;



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a2o1.s
                     661 

                     662 ;225: 		}


                     663 ;226: 		


                     664 ;227: 	}


                     665 ;228: 	queue->lastOverflowCount = queue->overflowCount;


                     666 

00000350 e3a00b42    667 	mov	r0,66<<10

00000354 e3a01b42    668 	mov	r1,66<<10

00000358 e281100c    669 	add	r1,r1,12

0000035c e791100a    670 	ldr	r1,[r1,r10]

00000360 e2800010    671 	add	r0,r0,16

00000364 e780100a    672 	str	r1,[r0,r10]

                     673 ;229: 	queue->tail = tail;


                     674 

00000368 e5894000    675 	str	r4,[r9]

                     676 ;230: 	unlockQueue(queue);


                     677 

0000036c e1a0000a    678 	mov	r0,r10

00000370 ebffff28*   679 	bl	unlockQueue

                     680 ;231: 	return copyCount;


                     681 

00000374 e1a0000b    682 	mov	r0,fp

                     683 .L605:

00000378 e28dd008    684 	add	sp,sp,8

0000037c e8bd8ff0    685 	ldmfd	[sp]!,{r4-fp,pc}

                     686 	.endf	ReportQueue_read

                     687 	.align	4

                     688 ;copyCount	fp	local

                     689 ;tail	r4	local

                     690 ;chunk	r5	local

                     691 

                     692 ;queue	r10	param

                     693 ;data	r6	param

                     694 ;maxDataSize	r7	param

                     695 

                     696 	.section ".bss","awb"

                     697 .L775:

                     698 	.data

                     699 	.text

                     700 

                     701 ;232: }


                     702 

                     703 ;233: 


                     704 ;234: void ReportQueue_purge(ReportQueue *queue)


                     705 	.align	4

                     706 	.align	4

                     707 ReportQueue_purge::

00000380 e92d4010    708 	stmfd	[sp]!,{r4,lr}

                     709 ;235: {


                     710 

                     711 ;236: 	lockQueue(queue);


                     712 

00000384 e1a04000    713 	mov	r4,r0

00000388 ebffff1c*   714 	bl	lockQueue

                     715 ;237: 	queue->head = queue->tail = 0;


                     716 

0000038c e2841b42    717 	add	r1,r4,66<<10

00000390 e3a00000    718 	mov	r0,0

00000394 e1a02000    719 	mov	r2,r0

00000398 e8810005    720 	stmea	[r1],{r0,r2}

                     721 ;238: 	queue->lastOverflowCount = queue->overflowCount = 0;



                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a2o1.s
                     722 

0000039c e3a01b42    723 	mov	r1,66<<10

000003a0 e281100c    724 	add	r1,r1,12

000003a4 e7810004    725 	str	r0,[r1,r4]

000003a8 e3a01b42    726 	mov	r1,66<<10

000003ac e2811010    727 	add	r1,r1,16

000003b0 e7810004    728 	str	r0,[r1,r4]

                     729 ;239: 	unlockQueue(queue);


                     730 

000003b4 e1a00004    731 	mov	r0,r4

000003b8 e8bd4010    732 	ldmfd	[sp]!,{r4,lr}

000003bc eaffff15*   733 	b	unlockQueue

                     734 	.endf	ReportQueue_purge

                     735 	.align	4

                     736 

                     737 ;queue	r4	param

                     738 

                     739 	.section ".bss","awb"

                     740 .L830:

                     741 	.data

                     742 	.text

                     743 

                     744 ;240: }


                     745 

                     746 ;241: 


                     747 ;242: int ReportQueue_isEmpty(ReportQueue *queue)


                     748 	.align	4

                     749 	.align	4

                     750 ReportQueue_isEmpty::

                     751 ;243: {


                     752 

                     753 ;244: 	return queue->head == queue->tail;


                     754 

000003c0 e3a02b42    755 	mov	r2,66<<10

000003c4 e2822004    756 	add	r2,r2,4

000003c8 e2801b42    757 	add	r1,r0,66<<10

000003cc e7900002    758 	ldr	r0,[r0,r2]

000003d0 e5911000    759 	ldr	r1,[r1]

000003d4 e1500001    760 	cmp	r0,r1

000003d8 03a00001    761 	moveq	r0,1

000003dc 13a00000    762 	movne	r0,0

000003e0 e12fff1e*   763 	ret	

                     764 	.endf	ReportQueue_isEmpty

                     765 	.align	4

                     766 

                     767 ;queue	r0	param

                     768 

                     769 	.section ".bss","awb"

                     770 .L862:

                     771 	.data

                     772 	.text

                     773 

                     774 ;245: }


                     775 

                     776 ;246: 


                     777 ;247: int ReportQueue_isOverflow(ReportQueue *queue)


                     778 	.align	4

                     779 	.align	4

                     780 ReportQueue_isOverflow::

                     781 ;248: {


                     782 


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_a2o1.s
                     783 ;249: 	return queue->lastOverflowCount != queue->overflowCount;


                     784 

000003e4 e2800b42    785 	add	r0,r0,66<<10

000003e8 e5901010    786 	ldr	r1,[r0,16]

000003ec e590000c    787 	ldr	r0,[r0,12]

000003f0 e0500001    788 	subs	r0,r0,r1

000003f4 13a00001    789 	movne	r0,1

000003f8 e12fff1e*   790 	ret	

                     791 	.endf	ReportQueue_isOverflow

                     792 	.align	4

                     793 

                     794 ;queue	r0	param

                     795 

                     796 	.section ".bss","awb"

                     797 .L894:

                     798 	.data

                     799 	.text

                     800 

                     801 ;250: }


                     802 	.align	4

                     803 

                     804 	.data

                     805 	.ghsnote version,6

                     806 	.ghsnote tools,3

                     807 	.ghsnote options,0

                     808 	.text

                     809 	.align	4

