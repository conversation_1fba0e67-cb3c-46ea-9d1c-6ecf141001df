                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b9g1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=OscDescr.c -o fs\gh_b9g1.o -list=fs/OscDescr.lst C:\Users\<USER>\AppData\Local\Temp\gh_b9g1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_b9g1.s
Source File: OscDescr.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile fs/OscDescr.c

                      11 ;		-o fs/OscDescr.o

                      12 ;Source File:   fs/OscDescr.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:58 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include <stdlib.h>


                      23 ;2: #include "OscFiles.h"


                      24 ;3: #include "OscDescr.h"


                      25 ;4: #include "../pwin_access.h"


                      26 ;5: #include "platform_critical_section.h"


                      27 ;6: 


                      28 ;7: static OscDescrHeader *oscDescr = NULL;


                      29 ;8: 


                      30 ;9: 


                      31 ;10: static OscDescrAnalog *getAnalogPtr(void)


                      32 ;11: {


                      33 ;12: 	return (OscDescrAnalog*)(oscDescr + 1);


                      34 ;13: }


                      35 ;14: 


                      36 ;15: static OscDescrBool *getBoolPtr(void)


                      37 

                      38 ;22: }


                      39 

                      40 ;23: 


                      41 ;24: 


                      42 ;25: // ищет информацию об аналоговом канале по индексу


                      43 ;26: OscDescrAnalog *OSCDescr_findDescrAnalogItem(unsigned int itemIdex)


                      44 ;27: {


                      45 ;28: 	OscDescrAnalog *pAnalog = getAnalogPtr();


                      46 ;29: 	int firstIndex = 0;


                      47 ;30: 	int lastIndex = oscDescr->analogCount;


                      48 ;31: 	int averageIndex = 0;


                      49 ;32: 	unsigned int curIndex;


                      50 ;33: 	while (firstIndex < lastIndex)



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b9g1.s
                      51 ;34: 	{


                      52 ;35: 		averageIndex = (firstIndex + lastIndex) / 2;


                      53 ;36: 		curIndex = pAnalog[averageIndex].index;


                      54 ;37: 		if (itemIdex < curIndex)


                      55 ;38: 		{


                      56 ;39: 			lastIndex = averageIndex;


                      57 ;40: 		}


                      58 ;41: 		else


                      59 ;42: 			if (itemIdex > curIndex)


                      60 ;43: 			{


                      61 ;44: 				firstIndex = averageIndex + 1;


                      62 ;45: 			}


                      63 ;46: 			else


                      64 ;47: 			{


                      65 ;48: 


                      66 ;49: 				return &pAnalog[averageIndex];


                      67 ;50: 			}


                      68 ;51: 	}


                      69 ;52: 


                      70 ;53: 	return NULL;


                      71 ;54: }


                      72 ;55: 


                      73 ;56: // ищет информацию о дискретном канале по смещению


                      74 ;57: OscDescrBool *OSCDescr_findDescrBoolItem(unsigned int itemOffset)


                      75 ;58: {


                      76 ;59: 	// смещение дискретных


                      77 ;60: 	OscDescrBool *pBool = getBoolPtr();


                      78 ;61: 


                      79 ;62: 	int firstIndex = 0;


                      80 ;63: 	int lastIndex = oscDescr->boolCount;


                      81 ;64: 	int averageIndex = 0;


                      82 ;65: 	unsigned int curOffset;


                      83 ;66: 	while (firstIndex < lastIndex)


                      84 ;67: 	{


                      85 ;68: 		averageIndex = (firstIndex + lastIndex) / 2;


                      86 ;69: 		curOffset = pBool[averageIndex].offset;


                      87 ;70: 		if (itemOffset < curOffset)


                      88 ;71: 		{


                      89 ;72: 			lastIndex = averageIndex;


                      90 ;73: 		}


                      91 ;74: 		else


                      92 ;75: 			if (itemOffset > curOffset)


                      93 ;76: 			{


                      94 ;77: 				firstIndex = averageIndex + 1;


                      95 ;78: 			}


                      96 ;79: 			else


                      97 ;80: 			{


                      98 ;81: 


                      99 ;82: 				return &pBool[averageIndex];


                     100 ;83: 			}


                     101 ;84: 	}


                     102 ;85: 


                     103 ;86: 	return NULL;


                     104 ;87: }


                     105 ;88: 


                     106 ;89: 


                     107 ;90: // загрузка ром модуля с описанием


                     108 ;91: static bool initOscDescr(void)


                     109 

                     110 ;114: } 


                     111 


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b9g1.s
                     112 	.text

                     113 	.align	4

                     114 getAnalogPtr:

00000000 e59f11a8*   115 	ldr	r1,.L70

00000004 e5910000    116 	ldr	r0,[r1]

00000008 e2800094    117 	add	r0,r0,148

0000000c e12fff1e*   118 	ret	

                     119 	.endf	getAnalogPtr

                     120 	.align	4

                     121 

                     122 	.section ".bss","awb"

                     123 .L62:

                     124 	.data

                     125 .L63:

00000000 00000000    126 oscDescr:	.data.b	0,0,0,0

                     127 	.type	oscDescr,$object

                     128 	.size	oscDescr,4

                     129 	.text

                     130 

                     131 

                     132 	.align	4

                     133 	.align	4

                     134 OSCDescr_findDescrAnalogItem::

00000010 e92d4030    135 	stmfd	[sp]!,{r4-r5,lr}

00000014 e1a05000    136 	mov	r5,r0

00000018 ebfffff8*   137 	bl	getAnalogPtr

0000001c e59f218c*   138 	ldr	r2,.L70

00000020 e5921000    139 	ldr	r1,[r2]

00000024 e1d130bc    140 	ldrh	r3,[r1,12]

00000028 e3a04000    141 	mov	r4,0

0000002c e1540003    142 	cmp	r4,r3

00000030 aa000010    143 	bge	.L74

                     144 .L75:

00000034 e0831004    145 	add	r1,r3,r4

00000038 e0811fa1    146 	add	r1,r1,r1 lsr 31

0000003c e1a020c1    147 	mov	r2,r1 asr 1

00000040 e0801202    148 	add	r1,r0,r2 lsl 4

00000044 e5d1c005    149 	ldrb	r12,[r1,5]

00000048 e155000c    150 	cmp	r5,r12

0000004c 2a000003    151 	bhs	.L76

00000050 e1a03002    152 	mov	r3,r2

00000054 e1540003    153 	cmp	r4,r3

00000058 bafffff5    154 	blt	.L75

0000005c ea000005    155 	b	.L74

                     156 .L76:

00000060 e155000c    157 	cmp	r5,r12

00000064 91a00001    158 	movls	r0,r1

00000068 9a000003    159 	bls	.L71

0000006c e2824001    160 	add	r4,r2,1

00000070 e1540003    161 	cmp	r4,r3

00000074 baffffee    162 	blt	.L75

                     163 .L74:

00000078 e3a00000    164 	mov	r0,0

                     165 .L71:

0000007c e8bd8030    166 	ldmfd	[sp]!,{r4-r5,pc}

                     167 	.endf	OSCDescr_findDescrAnalogItem

                     168 	.align	4

                     169 ;pAnalog	r0	local

                     170 ;firstIndex	r4	local

                     171 ;lastIndex	r3	local

                     172 ;averageIndex	r2	local


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b9g1.s
                     173 ;curIndex	r12	local

                     174 

                     175 ;itemIdex	r5	param

                     176 

                     177 	.section ".bss","awb"

                     178 .L138:

                     179 	.data

                     180 	.text

                     181 

                     182 

                     183 	.align	4

                     184 	.align	4

                     185 OSCDescr_findDescrBoolItem::

00000080 e92d4030    186 	stmfd	[sp]!,{r4-r5,lr}

00000084 e1a05000    187 	mov	r5,r0

00000088 ebffffdc*   188 	bl	getAnalogPtr

                     189 ;19: 	pAnalog += oscDescr->analogCount;


                     190 

0000008c e59f211c*   191 	ldr	r2,.L70

00000090 e5921000    192 	ldr	r1,[r2]

00000094 e1d120bc    193 	ldrh	r2,[r1,12]

00000098 e080c202    194 	add	r12,r0,r2 lsl 4

                     195 ;20: 


                     196 ;21: 	return (OscDescrBool*)pAnalog;


                     197 

0000009c e1d120be    198 	ldrh	r2,[r1,14]

000000a0 e3a04000    199 	mov	r4,0

                     200 ;16: {


                     201 

                     202 ;17: 	// дискретные входы находятся после аналоговых


                     203 ;18: 	OscDescrAnalog *pAnalog = getAnalogPtr();


                     204 

000000a4 e1540002    205 	cmp	r4,r2

000000a8 aa00000f    206 	bge	.L172

                     207 .L173:

000000ac e0820004    208 	add	r0,r2,r4

000000b0 e0800fa0    209 	add	r0,r0,r0 lsr 31

000000b4 e1a010c0    210 	mov	r1,r0 asr 1

000000b8 e1a00181    211 	mov	r0,r1 lsl 3

000000bc e1b030bc    212 	ldrh	r3,[r0,r12]!

000000c0 e1550003    213 	cmp	r5,r3

000000c4 2a000003    214 	bhs	.L174

000000c8 e1a02001    215 	mov	r2,r1

000000cc e1540002    216 	cmp	r4,r2

000000d0 bafffff5    217 	blt	.L173

000000d4 ea000004    218 	b	.L172

                     219 .L174:

000000d8 e1550003    220 	cmp	r5,r3

000000dc 9a000003    221 	bls	.L165

000000e0 e2814001    222 	add	r4,r1,1

000000e4 e1540002    223 	cmp	r4,r2

000000e8 baffffef    224 	blt	.L173

                     225 .L172:

000000ec e3a00000    226 	mov	r0,0

                     227 .L165:

000000f0 e8bd8030    228 	ldmfd	[sp]!,{r4-r5,pc}

                     229 	.endf	OSCDescr_findDescrBoolItem

                     230 	.align	4

                     231 ;pBool	r12	local

                     232 ;firstIndex	r4	local

                     233 ;lastIndex	r2	local


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b9g1.s
                     234 ;averageIndex	r1	local

                     235 ;curOffset	r3	local

                     236 

                     237 ;itemOffset	r5	param

                     238 

                     239 	.section ".bss","awb"

                     240 .L244:

                     241 	.data

                     242 	.text

                     243 

                     244 

                     245 ;115: 


                     246 ;116: bool OSCDescr_init(void)


                     247 	.align	4

                     248 	.align	4

                     249 OSCDescr_init::

000000f4 e92d4000    250 	stmfd	[sp]!,{lr}

000000f8 e3a00000    251 	mov	r0,0

000000fc e52d0010    252 	str	r0,[sp,-16]!

00000100 e28d300c    253 	add	r3,sp,12

00000104 e28d2008    254 	add	r2,sp,8

00000108 e59f00a4*   255 	ldr	r0,.L365

0000010c e28d1004    256 	add	r1,sp,4

00000110 eb000000*   257 	bl	loadRomModule

                     258 ;117: {


                     259 

                     260 ;118: 	if (!initOscDescr())


                     261 

                     262 ;92: {


                     263 

                     264 ;93: 	void *pRommModule;


                     265 ;94: 	unsigned char * pRommModuleData;


                     266 ;95: 	size_t moduleDataSize;


                     267 ;96: 	OscDescrHeader *descr;


                     268 ;97: 


                     269 ;98: 	if (!loadRomModule('OSCD', &pRommModule, &pRommModuleData, &moduleDataSize,NULL))


                     270 

00000114 e3500000    271 	cmp	r0,0

00000118 1a000001    272 	bne	.L279

                     273 ;99: 	{


                     274 

                     275 ;100: 		return false;


                     276 

0000011c 13a00001    277 	movne	r0,1

00000120 ea00000b    278 	b	.L271

                     279 .L279:

                     280 ;101: 	}


                     281 ;102: 


                     282 ;103: 


                     283 ;104: 	descr = (OscDescrHeader *)pRommModuleData;


                     284 

00000124 e59d0008    285 	ldr	r0,[sp,8]

                     286 ;105: 	// неверная версия


                     287 ;106: 	if (descr->version != OSC_DESCR_VERSION)


                     288 

00000128 e5901000    289 	ldr	r1,[r0]

0000012c e3510000    290 	cmp	r1,0

00000130 0a000003    291 	beq	.L281

                     292 ;107: 	{


                     293 

                     294 ;108: 		free(descr);



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b9g1.s
                     295 

00000134 eb000000*   296 	bl	free

                     297 ;109: 		return false;


                     298 

00000138 e3b00000    299 	movs	r0,0

0000013c 13a00001    300 	movne	r0,1

00000140 ea000003    301 	b	.L271

                     302 .L281:

                     303 ;110: 	}


                     304 ;111: 	oscDescr = descr;


                     305 

00000144 e59f1064*   306 	ldr	r1,.L70

00000148 e5810000    307 	str	r0,[r1]

                     308 ;112: 	


                     309 ;113: 	return true;


                     310 

0000014c e3b00001    311 	movs	r0,1

00000150 13a00001    312 	movne	r0,1

                     313 .L271:

                     314 ;119: 	{


                     315 

                     316 ;120: 		return false;


                     317 

                     318 ;121: 	}


                     319 ;122: 


                     320 ;123: 


                     321 ;124: 	return true;


                     322 

00000154 e28dd010    323 	add	sp,sp,16

00000158 e8bd8000    324 	ldmfd	[sp]!,{pc}

                     325 	.endf	OSCDescr_init

                     326 	.align	4

                     327 ;pRommModule	[sp,4]	local

                     328 ;pRommModuleData	[sp,8]	local

                     329 ;moduleDataSize	[sp,12]	local

                     330 ;descr	r0	local

                     331 

                     332 	.section ".bss","awb"

                     333 .L350:

                     334 	.data

                     335 	.text

                     336 

                     337 ;125: }


                     338 

                     339 ;126: 


                     340 ;127: const char * OSCDescr_getTerminalName(void)


                     341 	.align	4

                     342 	.align	4

                     343 OSCDescr_getTerminalName::

                     344 ;128: {


                     345 

                     346 ;129: 	return oscDescr->terminalName;


                     347 

0000015c e59fc04c*   348 	ldr	r12,.L70

00000160 e59c0000    349 	ldr	r0,[r12]

00000164 e2800014    350 	add	r0,r0,20

00000168 e12fff1e*   351 	ret	

                     352 	.endf	OSCDescr_getTerminalName

                     353 	.align	4

                     354 

                     355 	.data


                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b9g1.s
                     356 	.text

                     357 

                     358 ;130: }


                     359 

                     360 ;131: 


                     361 ;132: unsigned int OSCDescr_getTerminalVersion(void)


                     362 	.align	4

                     363 	.align	4

                     364 OSCDescr_getTerminalVersion::

                     365 ;133: {


                     366 

                     367 ;134: 	return oscDescr->terminalVersion;


                     368 

0000016c e59fc03c*   369 	ldr	r12,.L70

00000170 e59c0000    370 	ldr	r0,[r12]

00000174 e5900010    371 	ldr	r0,[r0,16]

00000178 e12fff1e*   372 	ret	

                     373 	.endf	OSCDescr_getTerminalVersion

                     374 	.align	4

                     375 

                     376 	.data

                     377 	.text

                     378 

                     379 ;135: }


                     380 

                     381 ;136: 


                     382 ;137: 


                     383 ;138: const char * OSCDescr_analogName(OscDescrAnalog *analog)


                     384 	.align	4

                     385 	.align	4

                     386 OSCDescr_analogName::

                     387 ;139: {


                     388 

                     389 ;140: 	return OSCDESCR_ANALOG_NAME(analog);


                     390 

0000017c e5901008    391 	ldr	r1,[r0,8]

00000180 e0810000    392 	add	r0,r1,r0

00000184 e12fff1e*   393 	ret	

                     394 	.endf	OSCDescr_analogName

                     395 	.align	4

                     396 

                     397 ;analog	r0	param

                     398 

                     399 	.section ".bss","awb"

                     400 .L446:

                     401 	.data

                     402 	.text

                     403 

                     404 ;141: }


                     405 

                     406 ;142: const char * OSCDescr_analogUnits(OscDescrAnalog *analog)


                     407 	.align	4

                     408 	.align	4

                     409 OSCDescr_analogUnits::

                     410 ;143: {


                     411 

                     412 ;144: 	return OSCDESCR_ANALOG_UNIT(analog);


                     413 

00000188 e590100c    414 	ldr	r1,[r0,12]

0000018c e0810000    415 	add	r0,r1,r0

00000190 e12fff1e*   416 	ret	


                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b9g1.s
                     417 	.endf	OSCDescr_analogUnits

                     418 	.align	4

                     419 

                     420 ;analog	r0	param

                     421 

                     422 	.section ".bss","awb"

                     423 .L478:

                     424 	.data

                     425 	.text

                     426 

                     427 ;145: }


                     428 

                     429 ;146: 


                     430 ;147: const char * OSCDescr_boolName(OscDescrBool *pBool)


                     431 	.align	4

                     432 	.align	4

                     433 OSCDescr_boolName::

                     434 ;148: {


                     435 

                     436 ;149: 	return OSCDESCR_BOOL_NAME(pBool);


                     437 

00000194 e5901004    438 	ldr	r1,[r0,4]

00000198 e0810000    439 	add	r0,r1,r0

0000019c e12fff1e*   440 	ret	

                     441 	.endf	OSCDescr_boolName

                     442 	.align	4

                     443 

                     444 ;pBool	r0	param

                     445 

                     446 	.section ".bss","awb"

                     447 .L510:

                     448 	.data

                     449 	.text

                     450 

                     451 ;150: }


                     452 

                     453 ;151: 


                     454 ;152: float OSCDescr_getFreq(void)


                     455 	.align	4

                     456 	.align	4

                     457 OSCDescr_getFreq::

                     458 ;153: {


                     459 

                     460 ;154: 	return oscDescr->freq;


                     461 

000001a0 e59fc008*   462 	ldr	r12,.L70

000001a4 e59c0000    463 	ldr	r0,[r12]

000001a8 e5900004    464 	ldr	r0,[r0,4]

000001ac e12fff1e*   465 	ret	

                     466 	.endf	OSCDescr_getFreq

                     467 	.align	4

                     468 

                     469 	.data

                     470 	.text

                     471 

                     472 ;155: }


                     473 	.align	4

                     474 .L70:

000001b0 00000000*   475 	.data.w	.L63

                     476 	.type	.L70,$object

                     477 	.size	.L70,4


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_b9g1.s
                     478 

                     479 .L365:

000001b4 4f534344    480 	.data.w	0x4f534344

                     481 	.type	.L365,$object

                     482 	.size	.L365,4

                     483 

                     484 	.align	4

                     485 ;oscDescr	.L63	static

                     486 

                     487 	.data

                     488 	.ghsnote version,6

                     489 	.ghsnote tools,3

                     490 	.ghsnote options,0

                     491 	.text

                     492 	.align	4

                     493 	.data

                     494 	.align	4

                     495 	.text

