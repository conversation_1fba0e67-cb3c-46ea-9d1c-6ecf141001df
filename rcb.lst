                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=rcb.c -o gh_6uo1.o -list=rcb.lst C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s
Source File: rcb.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile rcb.c -o rcb.o

                      11 ;Source File:   rcb.c

                      12 ;Directory:     

                      13 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      14 ;Compile Date:  Mon Jul 28 12:31:08 2025

                      15 ;Host OS:       Win32

                      16 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      17 ;Release:       MULTI v4.2.3

                      18 ;Revision Date: Wed Mar 29 05:25:47 2006

                      19 ;Release Date:  Fri Mar 31 10:02:14 2006

                      20 

                      21 ;1: #include "rcb.h"


                      22 ;2: #include "AsnEncoding.h"


                      23 ;3: #include "IEDCompile/InnerAttributeTypes.h"


                      24 ;4: #include "iedmodel.h"


                      25 ;5: #include "iedTree/iedTree.h"


                      26 ;6: #include "reporter.h"


                      27 ;7: #include "reports.h"


                      28 ;8: #include <debug.h>


                      29 ;9: #include <string.h>


                      30 ;10: 


                      31 ;11: //Получение имени DataSet из атрибута RCB DatSet.


                      32 ;12: static bool readDatSetAttr(int datSetDAPos, StringView* dataSetFullName)


                      33 

                      34 ;54: }


                      35 

                      36 ;55: 


                      37 ;56: 


                      38 ;57: static bool registerRCBDataset(int rcbPos, PReporter report)


                      39 

                      40 ;101: }


                      41 

                      42 ;102: 


                      43 ;103: bool registerRptID(int rcbPos, RCB* report)


                      44 	.text

                      45 	.align	4

                      46 registerRptID::

00000000 e92d4010     47 	stmfd	[sp]!,{r4,lr}

                      48 ;104: {


                      49 

                      50 ;105:     int rptIDLen;



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s
                      51 ;106:     int rptIDvalPos;


                      52 ;107:     int pos;


                      53 ;108:     uint8_t tag;


                      54 ;109:     int rptIDDAPos = findObjectBySimpleName(rcbPos, "RptID", 5);


                      55 

00000004 e24dd008     56 	sub	sp,sp,8

00000008 e1a04001     57 	mov	r4,r1

0000000c e28f1000*    58 	adr	r1,.L220

00000010 e3a02005     59 	mov	r2,5

00000014 eb000000*    60 	bl	findObjectBySimpleName

                      61 ;110:     RET_IF_NOT(rptIDDAPos, "RptIDPos attr is not found in RCB at pos %04X", rcbPos);


                      62 

00000018 e3500000     63 	cmp	r0,0

0000001c 0a00000a     64 	beq	.L120

                      65 ;111:     pos = readTL(rptIDDAPos, &tag, NULL, NULL);


                      66 

00000020 e28d1003     67 	add	r1,sp,3

00000024 e3a03000     68 	mov	r3,0

00000028 e1a02003     69 	mov	r2,r3

0000002c eb000000*    70 	bl	readTL

                      71 ;112:     RET_IF_NOT(pos, "Error reading RptDI at %d", rptIDDAPos);


                      72 

00000030 e3500000     73 	cmp	r0,0

00000034 0a000004     74 	beq	.L120

                      75 ;113:     //Skip name


                      76 ;114:     pos = skipObject(pos);


                      77 

00000038 eb000000*    78 	bl	skipObject

                      79 ;115:     RET_IF_NOT(pos, "Error reading RptDI at %d", rptIDDAPos);


                      80 

0000003c e3500000     81 	cmp	r0,0

00000040 0a000001     82 	beq	.L120

                      83 ;116:     //Skip Inner Attribute Type


                      84 ;117:     pos = skipObject(pos);


                      85 

00000044 eb000000*    86 	bl	skipObject

                      87 ;118:     RET_IF_NOT(pos, "Error reading RptDI at %d", rptIDDAPos);


                      88 

00000048 e3500000     89 	cmp	r0,0

                      90 .L120:

                      91 ;119:     //pos указывает на константу RptID


                      92 ;120:     rptIDvalPos = readTL(pos, &tag, &rptIDLen, NULL);


                      93 

                      94 ;121:     RET_IF_NOT(pos, "Error reading value RptDI at %d", pos);


                      95 

0000004c 03a00000     96 	moveq	r0,0

00000050 0a00000a     97 	beq	.L105

                      98 .L119:

00000054 e28d2004     99 	add	r2,sp,4

00000058 e28d1003    100 	add	r1,sp,3

0000005c e3a03000    101 	mov	r3,0

00000060 eb000000*   102 	bl	readTL

                     103 ;122: 


                     104 ;123: 


                     105 ;124:     StringView_init(&report->rptID,


                     106 

00000064 e59f3308*   107 	ldr	r3,.L221

00000068 e5931000    108 	ldr	r1,[r3]

0000006c e59d2004    109 	ldr	r2,[sp,4]

00000070 e0801001    110 	add	r1,r0,r1

00000074 e2840004    111 	add	r0,r4,4


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s
00000078 eb000000*   112 	bl	StringView_init

                     113 ;125:         (const char*)iedModel + rptIDvalPos, rptIDLen);    


                     114 ;126:     return TRUE;


                     115 

0000007c e3a00001    116 	mov	r0,1

                     117 .L105:

00000080 e28dd008    118 	add	sp,sp,8

00000084 e8bd8010    119 	ldmfd	[sp]!,{r4,pc}

                     120 	.endf	registerRptID

                     121 	.align	4

                     122 ;rptIDLen	[sp,4]	local

                     123 ;pos	r2	local

                     124 ;tag	[sp,3]	local

                     125 ;rptIDDAPos	r2	local

                     126 ;.L201	.L205	static

                     127 

                     128 ;rcbPos	none	param

                     129 ;report	r4	param

                     130 

                     131 	.section ".bss","awb"

                     132 .L200:

                     133 	.section ".rodata","a"

                     134 .L202:

                     135 __UNNAMED_1_static_in_registerRCBDataset:;	"DatSet\000"

00000000 53746144    136 	.data.b	68,97,116,83

00000004 7465       137 	.data.b	101,116

00000006 00         138 	.data.b	0

00000007 00         139 	.space	1

                     140 	.type	__UNNAMED_1_static_in_registerRCBDataset,$object

                     141 	.size	__UNNAMED_1_static_in_registerRCBDataset,8

                     142 	.data

                     143 	.text

                     144 

                     145 ;127: }


                     146 

                     147 ;128: 


                     148 ;129: bool registerTrgOps(int rcbPos, RCB* rcb)


                     149 	.align	4

                     150 	.align	4

                     151 registerTrgOps::

00000088 e92d4030    152 	stmfd	[sp]!,{r4-r5,lr}

                     153 ;130: {


                     154 

                     155 ;131:     int trgOps;


                     156 ;132:     enum InnerAttributeType attrType;


                     157 ;133:     //По 7.2 этот атрибут должен называться TrgOp, но по факту везде TrgOps


                     158 ;134:     int trgOpsPos = getDAValuePos(rcbPos, "TrgOps", 6, &attrType);


                     159 

0000008c e24dd004    160 	sub	sp,sp,4

00000090 e1a0300d    161 	mov	r3,sp

00000094 e1a05001    162 	mov	r5,r1

00000098 e28f1000*   163 	adr	r1,.L303

0000009c e3a02006    164 	mov	r2,6

000000a0 eb000000*   165 	bl	getDAValuePos

000000a4 e1b04000    166 	movs	r4,r0

                     167 ;135:     RET_IF_NOT(trgOpsPos, "Error reading TrgOps");


                     168 

000000a8 0a000007    169 	beq	.L228

                     170 ;136:     trgOps = BerDecoder_DecodeBitStringTLToInt(iedModel, trgOpsPos);


                     171 

000000ac e59f22c0*   172 	ldr	r2,.L221


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s
000000b0 e5920000    173 	ldr	r0,[r2]

000000b4 e1a01004    174 	mov	r1,r4

000000b8 eb000000*   175 	bl	BerDecoder_DecodeBitStringTLToInt

                     176 ;137:     RET_IF_NOT(trgOpsPos >= 0, "Error decoding TrgOps");


                     177 

000000bc e3540000    178 	cmp	r4,0

                     179 ;138:     rcb->trgOps = trgOps;


                     180 

000000c0 a5c5001a    181 	strgeb	r0,[r5,26]

                     182 ;139:     return TRUE;


                     183 

000000c4 a3a00001    184 	movge	r0,1

000000c8 aa000000    185 	bge	.L222

                     186 .L228:

000000cc e3a00000    187 	mov	r0,0

                     188 .L222:

000000d0 e28dd004    189 	add	sp,sp,4

000000d4 e8bd8030    190 	ldmfd	[sp]!,{r4-r5,pc}

                     191 	.endf	registerTrgOps

                     192 	.align	4

                     193 ;trgOps	r0	local

                     194 ;attrType	[sp]	local

                     195 ;trgOpsPos	r4	local

                     196 ;.L285	.L288	static

                     197 

                     198 ;rcbPos	none	param

                     199 ;rcb	r5	param

                     200 

                     201 	.section ".bss","awb"

                     202 .L284:

                     203 	.data

                     204 	.text

                     205 

                     206 ;140: }


                     207 

                     208 ;141: 


                     209 ;142: bool registerConfRev(int rcbPos, RCB* pRCB)


                     210 	.align	4

                     211 	.align	4

                     212 registerConfRev::

000000d8 e92d4010    213 	stmfd	[sp]!,{r4,lr}

                     214 ;143: {


                     215 

                     216 ;144:     uint8_t tag;


                     217 ;145:     int len;


                     218 ;146:     int confRev;


                     219 ;147:     enum InnerAttributeType attrType;


                     220 ;148:     int confRevPos = getDAValuePos(rcbPos, "ConfRev", 7, &attrType);


                     221 

000000dc e24dd00c    222 	sub	sp,sp,12

000000e0 e28d3008    223 	add	r3,sp,8

000000e4 e1a04001    224 	mov	r4,r1

000000e8 e28f1000*   225 	adr	r1,.L379

000000ec e3a02007    226 	mov	r2,7

000000f0 eb000000*   227 	bl	getDAValuePos

                     228 ;149:     RET_IF_NOT(confRevPos, "Error reading ConfRev");


                     229 

000000f4 e3500000    230 	cmp	r0,0

000000f8 0a000004    231 	beq	.L310

                     232 ;150:     confRevPos = readTL(confRevPos, &tag, &len, NULL);


                     233 


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s
000000fc e28d2004    234 	add	r2,sp,4

00000100 e28d1003    235 	add	r1,sp,3

00000104 e3a03000    236 	mov	r3,0

00000108 eb000000*   237 	bl	readTL

0000010c e1b02000    238 	movs	r2,r0

                     239 ;151:     RET_IF_NOT(confRevPos, "Error reading ConfRev");


                     240 

                     241 .L310:

00000110 03a00000    242 	moveq	r0,0

00000114 0a000005    243 	beq	.L304

                     244 .L309:

                     245 ;152:     confRev = BerDecoder_decodeUint32(iedModel, len, confRevPos);


                     246 

00000118 e59f3254*   247 	ldr	r3,.L221

0000011c e59d1004    248 	ldr	r1,[sp,4]

00000120 e5930000    249 	ldr	r0,[r3]

00000124 eb000000*   250 	bl	BerDecoder_decodeUint32

                     251 ;153:     pRCB->confRev = confRev;


                     252 

00000128 e5840014    253 	str	r0,[r4,20]

                     254 ;154:     return TRUE;


                     255 

0000012c e3a00001    256 	mov	r0,1

                     257 .L304:

00000130 e28dd00c    258 	add	sp,sp,12

00000134 e8bd8010    259 	ldmfd	[sp]!,{r4,pc}

                     260 	.endf	registerConfRev

                     261 	.align	4

                     262 ;tag	[sp,3]	local

                     263 ;len	[sp,4]	local

                     264 ;attrType	[sp,8]	local

                     265 ;confRevPos	r2	local

                     266 ;.L365	.L368	static

                     267 

                     268 ;rcbPos	none	param

                     269 ;pRCB	r4	param

                     270 

                     271 	.section ".bss","awb"

                     272 .L364:

                     273 	.data

                     274 	.text

                     275 

                     276 ;155: }


                     277 

                     278 ;156: 


                     279 ;157: 


                     280 ;158: bool registerOptFlds(int rcbPos, RCB* report)


                     281 	.align	4

                     282 	.align	4

                     283 registerOptFlds::

00000138 e92d4030    284 	stmfd	[sp]!,{r4-r5,lr}

                     285 ;159: {


                     286 

                     287 ;160:     int optFlds;


                     288 ;161:     enum InnerAttributeType attrType;


                     289 ;162:     int optFldsPos = getDAValuePos(rcbPos, "OptFlds", 7, &attrType);


                     290 

0000013c e24dd004    291 	sub	sp,sp,4

00000140 e1a0300d    292 	mov	r3,sp

00000144 e1a05001    293 	mov	r5,r1

00000148 e28f1000*   294 	adr	r1,.L463


                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s
0000014c e3a02007    295 	mov	r2,7

00000150 eb000000*   296 	bl	getDAValuePos

00000154 e1b04000    297 	movs	r4,r0

                     298 ;163:     RET_IF_NOT(optFldsPos, "Error reading OptFlds");


                     299 

00000158 0a000008    300 	beq	.L386

                     301 ;164:     optFlds = BerDecoder_DecodeBitStringTLToInt(iedModel, optFldsPos);


                     302 

0000015c e59f2210*   303 	ldr	r2,.L221

00000160 e5920000    304 	ldr	r0,[r2]

00000164 e1a01004    305 	mov	r1,r4

00000168 eb000000*   306 	bl	BerDecoder_DecodeBitStringTLToInt

                     307 ;165:     RET_IF_NOT(optFldsPos >= 0, "Error decoding OptFlds");


                     308 

0000016c e3540000    309 	cmp	r4,0

                     310 ;166:     report->optFlds = optFlds & SUPPORTED_OPTFLDS;


                     311 

00000170 a20000ee    312 	andge	r0,r0,238

00000174 a1c502b0    313 	strgeh	r0,[r5,32]

                     314 ;167:     return TRUE;


                     315 

00000178 a3a00001    316 	movge	r0,1

0000017c aa000000    317 	bge	.L380

                     318 .L386:

00000180 e3a00000    319 	mov	r0,0

                     320 .L380:

00000184 e28dd004    321 	add	sp,sp,4

00000188 e8bd8030    322 	ldmfd	[sp]!,{r4-r5,pc}

                     323 	.endf	registerOptFlds

                     324 	.align	4

                     325 ;optFlds	r0	local

                     326 ;attrType	[sp]	local

                     327 ;optFldsPos	r4	local

                     328 ;.L445	.L448	static

                     329 

                     330 ;rcbPos	none	param

                     331 ;report	r5	param

                     332 

                     333 	.section ".bss","awb"

                     334 .L444:

                     335 	.data

                     336 	.text

                     337 

                     338 ;168: }


                     339 

                     340 ;169: 


                     341 ;170: void registerReport(int rcbPos, bool buffered)


                     342 	.align	4

                     343 	.align	4

                     344 registerReport::

0000018c e92d48f0    345 	stmfd	[sp]!,{r4-r7,fp,lr}

00000190 e24dd014    346 	sub	sp,sp,20

00000194 e1a07000    347 	mov	r7,r0

00000198 e1a0b001    348 	mov	fp,r1

                     349 ;171: {


                     350 

                     351 ;172:     PReporter pReporter = getFreeReport();


                     352 

0000019c eb000000*   353 	bl	getFreeReport

000001a0 e1b04000    354 	movs	r4,r0

                     355 ;173:     RCB* pRCB = &pReporter->rcb;



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s
                     356 

000001a4 e1a05004    357 	mov	r5,r4

                     358 ;174:     if (pReporter == NULL)


                     359 

000001a8 0a00006d    360 	beq	.L464

                     361 ;175:     {


                     362 

                     363 ;176:         ERROR_REPORT("Unable to register report: too many reports");


                     364 ;177:         return;


                     365 

                     366 ;178:     }


                     367 ;179: 


                     368 ;180:     pReporter->rcb.buffered = buffered;


                     369 

000001ac e5c4b000    370 	strb	fp,[r4]

                     371 ;181:     pReporter->rcb.rptEna = FALSE;


                     372 

000001b0 e3a01000    373 	mov	r1,0

000001b4 e5c41001    374 	strb	r1,[r4,1]

                     375 ;182:     pReporter->rcb.resv = false;


                     376 

000001b8 e5c41002    377 	strb	r1,[r4,2]

                     378 ;183:     pReporter->sessionOutBuffer.busy = FALSE;


                     379 

000001bc e3a00b42    380 	mov	r0,66<<10

000001c0 e280005c    381 	add	r0,r0,92

000001c4 e7c01004    382 	strb	r1,[r0,r4]

                     383 ;184:     pReporter->connection = NULL;


                     384 

000001c8 e584102c    385 	str	r1,[r4,44]

                     386 ;186:     pReporter->intgTimerAlam = false;


                     387 

000001cc e5c41030    388 	strb	r1,[r4,48]

                     389 ;187:     pRCB->confRev = 0;


                     390 

000001d0 e5851014    391 	str	r1,[r5,20]

                     392 ;188:     pRCB->sqNum = 1;


                     393 

000001d4 e584103c    394 	str	r1,[r4,60]

                     395 ;185:     pReporter->intgPdCounter = 0;


                     396 

000001d8 e5851024    397 	str	r1,[r5,36]

000001dc e5851028    398 	str	r1,[r5,40]

                     399 ;190:     pRCB->intgPd = 0;


                     400 

000001e0 e585101c    401 	str	r1,[r5,28]

                     402 ;191: 


                     403 ;192:     if (!registerRptID(rcbPos, pRCB))


                     404 

000001e4 e1a01005    405 	mov	r1,r5

000001e8 e3a00001    406 	mov	r0,1

000001ec e1c501b8    407 	strh	r0,[r5,24]

                     408 ;189:     pRCB->entryID = 0;


                     409 

000001f0 e1a00007    410 	mov	r0,r7

000001f4 ebffff81*   411 	bl	registerRptID

000001f8 e3500000    412 	cmp	r0,0

000001fc 0a000058    413 	beq	.L464

                     414 ;193:     {


                     415 

                     416 ;194:         return;



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s
                     417 

                     418 ;195:     }


                     419 ;196: 


                     420 ;197:     if (!registerRCBDataset(rcbPos, pReporter))


                     421 

                     422 ;58: {


                     423 

                     424 ;59:     IEDEntity dataSetEntity;


                     425 ;60: 


                     426 ;61:     StringView dataSetFullName;


                     427 ;62:     int dataSetPos;


                     428 ;63:     int datSetDAPos = findObjectBySimpleName(rcbPos, "DatSet", 6);


                     429 

00000200 e59f1188*   430 	ldr	r1,.L839

00000204 e1a00007    431 	mov	r0,r7

00000208 e3a02006    432 	mov	r2,6

0000020c eb000000*   433 	bl	findObjectBySimpleName

                     434 ;64:     if (!datSetDAPos)


                     435 

00000210 e3500000    436 	cmp	r0,0

00000214 0a000052    437 	beq	.L464

                     438 ;65:     {


                     439 

                     440 ;66:         ERROR_REPORT("DatSet attr is not found in RCB at pos %04X", rcbPos);


                     441 ;67:         return FALSE;


                     442 

                     443 ;68:     }


                     444 ;69: 


                     445 ;70:     if (!readDatSetAttr(datSetDAPos, &dataSetFullName))


                     446 

                     447 ;13: {


                     448 

                     449 ;14:     int innerTypeLen;


                     450 ;15:     enum InnerAttributeType innerAttrType;


                     451 ;16:     int namePos;


                     452 ;17:     uint8_t nameTag;


                     453 ;18:     int nameLen;


                     454 ;19:     //Пропускем TL


                     455 ;20:     int pos = readTL(datSetDAPos, NULL, NULL, NULL);


                     456 

00000218 e3a03000    457 	mov	r3,0

0000021c e1a02003    458 	mov	r2,r3

00000220 e1a01003    459 	mov	r1,r3

00000224 eb000000*   460 	bl	readTL

                     461 ;21:     //Пропускаем имя


                     462 ;22:     pos = skipObject(pos);


                     463 

00000228 eb000000*   464 	bl	skipObject

                     465 ;23: 


                     466 ;24:     //Inner TYPE


                     467 ;25:     if (iedModel[pos++] != ASN_INTEGER)


                     468 

0000022c e59f1140*   469 	ldr	r1,.L221

00000230 e591c000    470 	ldr	r12,[r1]

00000234 e2806001    471 	add	r6,r0,1

00000238 e7dc0000    472 	ldrb	r0,[r12,r0]

0000023c e3500002    473 	cmp	r0,2

00000240 1a000047    474 	bne	.L464

                     475 ;26:     {


                     476 

                     477 ;27:         return FALSE;



                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s
                     478 

                     479 ;28:     }


                     480 ;29:     pos = BerDecoder_decodeLength(iedModel, &innerTypeLen, pos, iedModelSize);


                     481 

00000244 e1a02006    482 	mov	r2,r6

00000248 e59f0144*   483 	ldr	r0,.L840

0000024c e28d1004    484 	add	r1,sp,4

00000250 e5903000    485 	ldr	r3,[r0]

00000254 e1a0000c    486 	mov	r0,r12

00000258 eb000000*   487 	bl	BerDecoder_decodeLength

0000025c e2506000    488 	subs	r6,r0,0

                     489 ;30:     if (pos < 1)


                     490 

00000260 da00003f    491 	ble	.L464

                     492 ;31:     {


                     493 

                     494 ;32:         return FALSE;


                     495 

                     496 ;33:     }


                     497 ;34:     innerAttrType = (enum InnerAttributeType)


                     498 

00000264 e59f3108*   499 	ldr	r3,.L221

00000268 e59d1004    500 	ldr	r1,[sp,4]

0000026c e5930000    501 	ldr	r0,[r3]

00000270 e1a02006    502 	mov	r2,r6

00000274 eb000000*   503 	bl	BerDecoder_decodeUint32

                     504 ;35:             BerDecoder_decodeUint32(iedModel, innerTypeLen, pos);


                     505 ;36:     pos += innerTypeLen;


                     506 

00000278 e59d1004    507 	ldr	r1,[sp,4]

0000027c e350001f    508 	cmp	r0,31

00000280 e0866001    509 	add	r6,r6,r1

                     510 ;37:     if (innerAttrType != INNER_TYPE_CONST)


                     511 

00000284 1a000036    512 	bne	.L464

                     513 ;38:     {


                     514 

                     515 ;39:         return FALSE;


                     516 

                     517 ;40:     }


                     518 ;41: 


                     519 ;42:     //Читаем имя DataSet


                     520 ;43:     //Неплохо бы сделать это через готовую функцию


                     521 ;44:     namePos = readTL(pos, &nameTag, &nameLen, NULL);


                     522 

00000288 e28d2008    523 	add	r2,sp,8

0000028c e28d1003    524 	add	r1,sp,3

00000290 e1a00006    525 	mov	r0,r6

00000294 e3a03000    526 	mov	r3,0

00000298 eb000000*   527 	bl	readTL

                     528 ;45: 


                     529 ;46:     if (!namePos || nameTag !=(BER_CONTEXT_SPECIFIC | IEC61850_VISIBLE_STRING_64))


                     530 

0000029c e3500000    531 	cmp	r0,0

000002a0 0a00002f    532 	beq	.L464

000002a4 e5dd1003    533 	ldrb	r1,[sp,3]

000002a8 e3510091    534 	cmp	r1,145

000002ac 1a00002c    535 	bne	.L464

000002b0 e59f30bc*   536 	ldr	r3,.L221

000002b4 e5931000    537 	ldr	r1,[r3]

000002b8 e59d2008    538 	ldr	r2,[sp,8]


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s
000002bc e0801001    539 	add	r1,r0,r1

000002c0 e28d000c    540 	add	r0,sp,12

000002c4 eb000000*   541 	bl	StringView_init

                     542 ;47:     {


                     543 

                     544 ;48:         return FALSE;


                     545 

                     546 ;49:     }


                     547 ;50: 


                     548 ;51:     StringView_init(dataSetFullName,(const char*)iedModel + namePos, nameLen);


                     549 

                     550 ;52: 


                     551 ;53:     return TRUE;


                     552 

                     553 ;71:     {


                     554 

                     555 ;72:         ERROR_REPORT("Unable to read DatSet attr at pos %04X", datSetDAPos);


                     556 ;73:         return FALSE;


                     557 

                     558 ;74:     }


                     559 ;75: 


                     560 ;76:     dataSetPos = getDataSetByPath(&dataSetFullName);


                     561 

000002c8 e28d000c    562 	add	r0,sp,12

000002cc eb000000*   563 	bl	getDataSetByPath

                     564 ;77:     if (!dataSetPos)


                     565 

000002d0 e3500000    566 	cmp	r0,0

000002d4 0a000022    567 	beq	.L464

                     568 ;78:     {


                     569 

                     570 ;79:         ERROR_REPORT("DataSet is not found");


                     571 ;80:         return FALSE;


                     572 

                     573 ;81:     }


                     574 ;82: 


                     575 ;83:     Reporter_setDataSetName(report, &dataSetFullName);


                     576 

000002d8 e28d100c    577 	add	r1,sp,12

000002dc e1a00004    578 	mov	r0,r4

000002e0 eb000000*   579 	bl	Reporter_setDataSetName

                     580 ;84: 


                     581 ;85:     //Получаем DataSet в IEDTree


                     582 ;86:     dataSetEntity = IEDTree_findDataSetBySingleName(&dataSetFullName);


                     583 

000002e4 e28d000c    584 	add	r0,sp,12

000002e8 eb000000*   585 	bl	IEDTree_findDataSetBySingleName

000002ec e1b01000    586 	movs	r1,r0

                     587 ;87:     if(dataSetEntity == NULL)


                     588 

000002f0 0a00001b    589 	beq	.L464

                     590 ;88:     {


                     591 

                     592 ;89:         return false;


                     593 

                     594 ;90:     }


                     595 ;91:     report->dataSetEntity = dataSetEntity;


                     596 

000002f4 e5841034    597 	str	r1,[r4,52]

                     598 ;92:     report->dataSet = DataSet_getDataSetObj(dataSetEntity);


                     599 


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s
000002f8 eb000000*   600 	bl	DataSet_getDataSetObj

000002fc e5840038    601 	str	r0,[r4,56]

                     602 ;93: 


                     603 ;94:     if(report->dataSet == NULL)


                     604 

00000300 e3500000    605 	cmp	r0,0

00000304 0a000016    606 	beq	.L464

                     607 ;95:     {


                     608 

                     609 ;96:         ERROR_REPORT("Invalid DataSet");


                     610 ;97:         return false;


                     611 

                     612 ;98:     }


                     613 ;99: 


                     614 ;100:     return TRUE;


                     615 

                     616 ;198:     {


                     617 

                     618 ;199:         return;


                     619 

                     620 ;200:     }


                     621 ;201: 


                     622 ;202:     if (!registerConfRev(rcbPos, pRCB))


                     623 

00000308 e1a01005    624 	mov	r1,r5

0000030c e1a00007    625 	mov	r0,r7

00000310 ebffff70*   626 	bl	registerConfRev

00000314 e3500000    627 	cmp	r0,0

00000318 0a000011    628 	beq	.L464

                     629 ;203:     {


                     630 

                     631 ;204:         return;


                     632 

                     633 ;205:     }


                     634 ;206: 


                     635 ;207:     if (!registerTrgOps(rcbPos, pRCB))


                     636 

0000031c e1a01005    637 	mov	r1,r5

00000320 e1a00007    638 	mov	r0,r7

00000324 ebffff57*   639 	bl	registerTrgOps

00000328 e3500000    640 	cmp	r0,0

0000032c 0a00000c    641 	beq	.L464

                     642 ;208:     {


                     643 

                     644 ;209:         return;


                     645 

                     646 ;210:     }


                     647 ;211: 


                     648 ;212:     if (!registerOptFlds(rcbPos, pRCB))


                     649 

00000330 e1a01005    650 	mov	r1,r5

00000334 e1a00007    651 	mov	r0,r7

00000338 ebffff7e*   652 	bl	registerOptFlds

0000033c e3500000    653 	cmp	r0,0

00000340 0a000007    654 	beq	.L464

                     655 ;213:     {


                     656 

                     657 ;214:         return;


                     658 

                     659 ;215:     }


                     660 ;216: 



                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s
                     661 ;217:     if(!initReportCompareDataset(pReporter))


                     662 

00000344 e1a00004    663 	mov	r0,r4

00000348 eb000000*   664 	bl	initReportCompareDataset

0000034c e3500000    665 	cmp	r0,0

00000350 0a000003    666 	beq	.L464

                     667 ;218:     {


                     668 

                     669 ;219:         return;


                     670 

                     671 ;220:     }


                     672 ;221: 


                     673 ;222:     finalizeReportRegistration();


                     674 

00000354 eb000000*   675 	bl	finalizeReportRegistration

                     676 ;223:     if (buffered)


                     677 

00000358 e35b0000    678 	cmp	fp,0

                     679 ;224:     {


                     680 

                     681 ;225:         ReportQueue_init(&(pReporter->buffer));


                     682 

0000035c 12840040    683 	addne	r0,r4,64

00000360 1b000000*   684 	blne	ReportQueue_init

                     685 .L464:

00000364 e28dd014    686 	add	sp,sp,20

00000368 e8bd88f0    687 	ldmfd	[sp]!,{r4-r7,fp,pc}

                     688 	.endf	registerReport

                     689 	.align	4

                     690 .L220:

                     691 ;	"RptID\000"

0000036c 49747052    692 	.data.b	82,112,116,73

00000370 0044       693 	.data.b	68,0

00000372 0000       694 	.align 4

                     695 

                     696 	.type	.L220,$object

                     697 	.size	.L220,4

                     698 

                     699 .L221:

00000374 00000000*   700 	.data.w	iedModel

                     701 	.type	.L221,$object

                     702 	.size	.L221,4

                     703 

                     704 .L303:

                     705 ;	"TrgOps\000"

00000378 4f677254    706 	.data.b	84,114,103,79

0000037c 7370       707 	.data.b	112,115

0000037e 00         708 	.data.b	0

0000037f 00         709 	.align 4

                     710 

                     711 	.type	.L303,$object

                     712 	.size	.L303,4

                     713 

                     714 .L379:

                     715 ;	"ConfRev\000"

00000380 666e6f43    716 	.data.b	67,111,110,102

00000384 00766552    717 	.data.b	82,101,118,0

                     718 	.align 4

                     719 

                     720 	.type	.L379,$object

                     721 	.size	.L379,4


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s
                     722 

                     723 .L463:

                     724 ;	"OptFlds\000"

00000388 4674704f    725 	.data.b	79,112,116,70

0000038c 0073646c    726 	.data.b	108,100,115,0

                     727 	.align 4

                     728 

                     729 	.type	.L463,$object

                     730 	.size	.L463,4

                     731 

                     732 .L839:

00000390 00000000*   733 	.data.w	.L202

                     734 	.type	.L839,$object

                     735 	.size	.L839,4

                     736 

                     737 .L840:

00000394 00000000*   738 	.data.w	iedModelSize

                     739 	.type	.L840,$object

                     740 	.size	.L840,4

                     741 

                     742 	.align	4

                     743 ;pReporter	r4	local

                     744 ;pRCB	r5	local

                     745 ;dataSetEntity	r1	local

                     746 ;dataSetFullName	[sp,12]	local

                     747 ;datSetDAPos	r1	local

                     748 ;innerTypeLen	[sp,4]	local

                     749 ;namePos	r0	local

                     750 ;nameTag	[sp,3]	local

                     751 ;nameLen	[sp,8]	local

                     752 ;pos	r6	local

                     753 

                     754 ;rcbPos	r7	param

                     755 ;buffered	fp	param

                     756 

                     757 	.section ".bss","awb"

                     758 .L783:

                     759 	.data

                     760 	.text

                     761 

                     762 ;226:     }


                     763 ;227: }


                     764 

                     765 ;228: 


                     766 ;229: void registerBufferedReport(int rcbPos)


                     767 	.align	4

                     768 	.align	4

                     769 registerBufferedReport::

                     770 ;230: {


                     771 

                     772 ;231:     TRACE("Buffered report. Pos: %04X", rcbPos);


                     773 ;232:     registerReport(rcbPos, TRUE);


                     774 

00000398 e3a01001    775 	mov	r1,1

0000039c eaffff7a*   776 	b	registerReport

                     777 	.endf	registerBufferedReport

                     778 	.align	4

                     779 

                     780 ;rcbPos	none	param

                     781 

                     782 	.section ".bss","awb"


                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s
                     783 .L862:

                     784 	.data

                     785 	.text

                     786 

                     787 ;233: }


                     788 

                     789 ;234: 


                     790 ;235: void registerUnbufferedReport(int rcbPos)


                     791 	.align	4

                     792 	.align	4

                     793 registerUnbufferedReport::

                     794 ;236: {


                     795 

                     796 ;237:     TRACE("Unbuffered report. Pos: %04X", rcbPos);


                     797 ;238:     registerReport(rcbPos, FALSE);


                     798 

000003a0 e3a01000    799 	mov	r1,0

000003a4 eaffff78*   800 	b	registerReport

                     801 	.endf	registerUnbufferedReport

                     802 	.align	4

                     803 

                     804 ;rcbPos	none	param

                     805 

                     806 	.section ".bss","awb"

                     807 .L894:

                     808 	.data

                     809 	.text

                     810 

                     811 ;239: }


                     812 

                     813 ;240: 


                     814 ;241: // Регистрирует все RCB, которые найдёт в указанном объекте FC


                     815 ;242: // В зависимости от имени FC регистрирует buffered, unbuffered,


                     816 ;243: // или вообще никакие RCB (если имя FC не "BR" и не "RP")


                     817 ;244: void registerRCBsGivenFC(int fcPos)


                     818 	.align	4

                     819 	.align	4

                     820 registerRCBsGivenFC::

000003a8 e92d4010    821 	stmfd	[sp]!,{r4,lr}

                     822 ;245: {


                     823 

                     824 ;246:     StringView fcName;


                     825 ;247: 


                     826 ;248:     if(!getObjectName(fcPos, &fcName))


                     827 

000003ac e24dd008    828 	sub	sp,sp,8

000003b0 e1a0100d    829 	mov	r1,sp

000003b4 e1a04000    830 	mov	r4,r0

000003b8 eb000000*   831 	bl	getObjectName

000003bc e3500000    832 	cmp	r0,0

000003c0 0a000014    833 	beq	.L901

                     834 ;249:     {


                     835 

                     836 ;250:         ERROR_REPORT("Unable to read FC name");


                     837 ;251:         return;


                     838 

                     839 ;252:     }


                     840 ;253:     if(fcName.len != 2)


                     841 

000003c4 e59d0000    842 	ldr	r0,[sp]

000003c8 e3500002    843 	cmp	r0,2


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s
000003cc 1a000011    844 	bne	.L901

                     845 ;254:     {


                     846 

                     847 ;255:         ERROR_REPORT("Invalid FC name");


                     848 ;256:         return;


                     849 

                     850 ;257:     }


                     851 ;258:     if ( memcmp("RP", fcName.p, 2) == 0)


                     852 

000003d0 e59d1004    853 	ldr	r1,[sp,4]

000003d4 e59f0078*   854 	ldr	r0,.L1016

000003d8 e3a02002    855 	mov	r2,2

000003dc eb000000*   856 	bl	memcmp

000003e0 e3500000    857 	cmp	r0,0

000003e4 1a000003    858 	bne	.L909

000003e8 e59f1068*   859 	ldr	r1,.L1017

                     860 ;259:     {


                     861 

                     862 ;260:         //Unbuffered reports


                     863 ;261:         processSubobjects(fcPos, registerUnbufferedReport);


                     864 

000003ec e1a00004    865 	mov	r0,r4

000003f0 eb000000*   866 	bl	processSubobjects

000003f4 ea000007    867 	b	.L901

                     868 .L909:

                     869 ;262:     }


                     870 ;263:     else if( memcmp("BR", fcName.p, 2) == 0)


                     871 

000003f8 e59d1004    872 	ldr	r1,[sp,4]

000003fc e59f0058*   873 	ldr	r0,.L1018

00000400 e3a02002    874 	mov	r2,2

00000404 eb000000*   875 	bl	memcmp

00000408 e3500000    876 	cmp	r0,0

                     877 ;264:     {


                     878 

                     879 ;265:         //Buffered reports


                     880 ;266:         processSubobjects(fcPos, registerBufferedReport);


                     881 

0000040c 059f104c*   882 	ldreq	r1,.L1019

00000410 01a00004    883 	moveq	r0,r4

00000414 0b000000*   884 	bleq	processSubobjects

                     885 .L901:

00000418 e28dd008    886 	add	sp,sp,8

0000041c e8bd8010    887 	ldmfd	[sp]!,{r4,pc}

                     888 	.endf	registerRCBsGivenFC

                     889 	.align	4

                     890 ;fcName	[sp]	local

                     891 ;.L990	.L994	static

                     892 ;.L991	.L995	static

                     893 

                     894 ;fcPos	r4	param

                     895 

                     896 	.section ".bss","awb"

                     897 .L989:

                     898 	.section ".rodata","a"

                     899 .L994:;	"RP\000"

00000008 5052       900 	.data.b	82,80

0000000a 00         901 	.data.b	0

                     902 	.type	.L994,$object

                     903 	.size	.L994,3

                     904 .L995:;	"BR\000"


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s
0000000b 5242       905 	.data.b	66,82

0000000d 00         906 	.data.b	0

                     907 	.type	.L995,$object

                     908 	.size	.L995,3

                     909 	.data

                     910 	.text

                     911 

                     912 ;267:     }


                     913 ;268: }


                     914 

                     915 ;269: 


                     916 ;270: void registerAllLogicalNodeRCB(int lnPos)


                     917 	.align	4

                     918 	.align	4

                     919 registerAllLogicalNodeRCB::

00000420 e59f103c*   920 	ldr	r1,.L1045

                     921 ;271: {


                     922 

                     923 ;272:     processSubobjects(lnPos, registerRCBsGivenFC);


                     924 

00000424 ea000000*   925 	b	processSubobjects

                     926 	.endf	registerAllLogicalNodeRCB

                     927 	.align	4

                     928 

                     929 ;lnPos	none	param

                     930 

                     931 	.section ".bss","awb"

                     932 .L1038:

                     933 	.data

                     934 	.text

                     935 

                     936 ;273: }


                     937 

                     938 ;274: 


                     939 ;275: void registerAllLogicalDeviceRCB(int ldPos)


                     940 	.align	4

                     941 	.align	4

                     942 registerAllLogicalDeviceRCB::

00000428 e92d4000    943 	stmfd	[sp]!,{lr}

                     944 ;276: {


                     945 

                     946 ;277:     int dataSectionPos;


                     947 ;278: 


                     948 ;279:     dataSectionPos = findObjectByTag(ldPos, IED_VMD_DATA_SECTION);


                     949 

0000042c e3a010ec    950 	mov	r1,236

00000430 eb000000*   951 	bl	findObjectByTag

                     952 ;280:     if(!dataSectionPos)


                     953 

00000434 e3500000    954 	cmp	r0,0

00000438 159f1028*   955 	ldrne	r1,.L1091

                     956 ;281:     {


                     957 

                     958 ;282:         ERROR_REPORT("Data section is not found");


                     959 ;283:         return;


                     960 

                     961 ;284:     }


                     962 ;285: 


                     963 ;286:     processSubobjects(dataSectionPos, registerAllLogicalNodeRCB);


                     964 

0000043c 18bd4000    965 	ldmnefd	[sp]!,{lr}


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s
00000440 1a000000*   966 	bne	processSubobjects

00000444 e8bd8000    967 	ldmfd	[sp]!,{pc}

                     968 	.endf	registerAllLogicalDeviceRCB

                     969 	.align	4

                     970 ;dataSectionPos	r1	local

                     971 

                     972 ;ldPos	none	param

                     973 

                     974 	.section ".bss","awb"

                     975 .L1080:

                     976 	.data

                     977 	.text

                     978 

                     979 ;287: }


                     980 

                     981 ;288: 


                     982 ;289: void registerAllRCB(void)


                     983 	.align	4

                     984 	.align	4

                     985 registerAllRCB::

00000448 e59f101c*   986 	ldr	r1,.L1125

                     987 ;290: {


                     988 

                     989 ;291:     processSubobjects(0, registerAllLogicalDeviceRCB);


                     990 

0000044c e3a00000    991 	mov	r0,0

00000450 ea000000*   992 	b	processSubobjects

                     993 	.endf	registerAllRCB

                     994 	.align	4

                     995 

                     996 	.section ".bss","awb"

                     997 .L1118:

                     998 	.data

                     999 	.text

                    1000 

                    1001 ;292: }


                    1002 	.align	4

                    1003 .L1016:

00000454 00000000*  1004 	.data.w	.L994

                    1005 	.type	.L1016,$object

                    1006 	.size	.L1016,4

                    1007 

                    1008 .L1017:

00000458 00000000*  1009 	.data.w	registerUnbufferedReport

                    1010 	.type	.L1017,$object

                    1011 	.size	.L1017,4

                    1012 

                    1013 .L1018:

0000045c 00000000*  1014 	.data.w	.L995

                    1015 	.type	.L1018,$object

                    1016 	.size	.L1018,4

                    1017 

                    1018 .L1019:

00000460 00000000*  1019 	.data.w	registerBufferedReport

                    1020 	.type	.L1019,$object

                    1021 	.size	.L1019,4

                    1022 

                    1023 .L1045:

00000464 00000000*  1024 	.data.w	registerRCBsGivenFC

                    1025 	.type	.L1045,$object

                    1026 	.size	.L1045,4


                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_6uo1.s
                    1027 

                    1028 .L1091:

00000468 00000000*  1029 	.data.w	registerAllLogicalNodeRCB

                    1030 	.type	.L1091,$object

                    1031 	.size	.L1091,4

                    1032 

                    1033 .L1125:

0000046c 00000000*  1034 	.data.w	registerAllLogicalDeviceRCB

                    1035 	.type	.L1125,$object

                    1036 	.size	.L1125,4

                    1037 

                    1038 	.align	4

                    1039 ;iedModel	iedModel	import

                    1040 ;iedModelSize	iedModelSize	import

                    1041 ;__UNNAMED_1_static_in_registerRCBDataset	.L202	static

                    1042 

                    1043 	.data

                    1044 	.ghsnote version,6

                    1045 	.ghsnote tools,3

                    1046 	.ghsnote options,0

                    1047 	.text

                    1048 	.align	4

                    1049 	.section ".rodata","a"

0000000e 0000      1050 	.align	4

                    1051 	.text

