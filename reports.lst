                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=reports.c -o gh_aus1.o -list=reports.lst C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
Source File: reports.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile reports.c -o

                      11 ;		reports.o

                      12 ;Source File:   reports.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:08 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "reports.h"


                      23 ;2: #include "rcb.h"


                      24 ;3: #include "reporter.h"


                      25 ;4: #include "mms.h"


                      26 ;5: #include "platform_thread.h"


                      27 ;6: #include "DataSlice.h"


                      28 ;7: #include "IEDCompile/InnerAttributeTypes.h"


                      29 ;8: #include "iedmodel.h"


                      30 ;9: #include"iedTree/iedTree.h"


                      31 ;10: #include "BaseAsnTypes.h"


                      32 ;11: #include "AsnEncoding.h"


                      33 ;12: #include "mms_data.h"


                      34 ;13: #include "control.h"


                      35 ;14: #include "timers.h"


                      36 ;15: #include "BusError.h"


                      37 ;16: #include <Clib.h>


                      38 ;17: #include <stddef.h>


                      39 ;18: #include <string.h>


                      40 ;19: #include <debug.h>


                      41 ;20: 


                      42 ;21: 


                      43 ;22: #define TRGOP_INTEGRITY (1 << 1)


                      44 ;23: #define MIN_INTG_PD 100


                      45 ;24: 


                      46 ;25: 


                      47 ;26: // Сколько байт занимает inclusion-bitstring.


                      48 ;27: // Нежелательно делать очень большим, потому что переменная создаётся


                      49 ;28: // на стеке. Желательно делать кратным 4.


                      50 ;29: #define BYTES_IN_INCLUSION_BITSTING 40



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                      51 ;30: #define MAX_DATASET_OBJECT_COUNT (BYTES_IN_INCLUSION_BITSTING * 8)


                      52 ;31: 


                      53 ;32: #define TRG_OPS_BIT_COUNT 6


                      54 ;33: 


                      55 ;34: //Reason for Inclusion


                      56 ;35: #define REASON_DCHG DCHG


                      57 ;36: #define REASON_QCHG QCHG


                      58 ;37: #define REASON_GI 1


                      59 ;38: #define REASON_INTEGRITY (1 << 1)


                      60 ;39: 


                      61 ;40: 


                      62 ;41: #pragma alignvar (4)


                      63 ;42: 


                      64 ;43: //Fixed report "RPT" variable


                      65 ;44: uint8_t reportVarNameSequence[7] = { 0xA1, 0x05, 0x80, 0x03, 0x52, 0x50, 0x54 };


                      66 ;45: 


                      67 ;46: size_t g_reportCount = 0;


                      68 ;47: 


                      69 ;48: 


                      70 ;49: Reporter g_reports[MAX_REPORT_COUNT];


                      71 ;50: 


                      72 ;51: //Причина включения для каждого значения в отчёте


                      73 ;52: //Используется для генерации Reason-For-Inclusion


                      74 ;53: //и для генерации Inclusion-bitstring.


                      75 ;54: //Каждый элемент соответствует элементу DataSet.


                      76 ;55: //При подготовке отчёта записавается:


                      77 ;56: // 0 если элемент не включен в отчёт


                      78 ;57: // причина включения если элемент включен в отчёт


                      79 ;58: static uint8_t reportInclusionReasons[MAX_DATASET_OBJECT_COUNT];


                      80 ;59: 


                      81 ;60: //Здесь лежат закодированные значения, попавшие в отчёт.


                      82 ;61: static uint8_t reportValuesBuf[DEFAULT_REPORT_BUFFER_SIZE];


                      83 ;62: 


                      84 ;63: //Здесь лежат закодированные значения, попавшие в отчёт вместе с


                      85 ;64: //обязательными и необязательными служебными данными


                      86 ;65: static uint8_t reportAccessResultsBuf[DEFAULT_REPORT_BUFFER_SIZE];


                      87 ;66: //Здесь лежит отчёт в виде пакета MMS


                      88 ;67: static uint8_t reportMmsBuf[DEFAULT_REPORT_BUFFER_SIZE];


                      89 ;68: //Здесь лежит отчёт в виде пакета Presentation


                      90 ;69: static uint8_t reportPresentationBuf[DEFAULT_REPORT_BUFFER_SIZE];


                      91 ;70: 


                      92 ;71: // Режим обработки набора данных в отчёте:


                      93 ;72: // инициализация начальных значений


                      94 ;73: // General Interrorgation


                      95 ;74: // Режим сравнения на data change или quality change


                      96 ;75: typedef enum {RPT_GI, RPT_INTG, RPT_INIT, RPT_CMP} RptProcessMode;


                      97 ;76: 


                      98 ;77: 


                      99 ;78: // Структура для параметров обработки отчёта (проверки на изменения, GI, 


                     100 ;79: // инициализации начальных значений)


                     101 ;80: typedef struct {


                     102 ;81: 	RptProcessMode mode;	


                     103 ;82: 	//Индекс в массиве значений для сравнения


                     104 ;83: 	int valIdx;	


                     105 ;84: 	//Индекс элемета DataSet


                     106 ;85: 	size_t itemIdx;


                     107 ;86: 	BufferView* outBuf;


                     108 ;87: 	Reporter* reporter;


                     109 ;88: } RptProcContext;


                     110 ;89: 


                     111 ;90: /* 61850 - 8 - 1



                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                     112 ;91: 	The MMS Btime6 (TimeOfDay) type shall be an OCTET STRING. A value of the TimeOfDay


                     113 ;92: type may contain either 4 or 6 octets. The first form specifies the time as the number of


                     114 ;93: milliseconds since midnight on the current date (the date is not contained in the value), while


                     115 ;94: the second form contains the time and a date, expressed as the relative day since 1 January


                     116 ;95: 1984. The first four octets shall contain a value indicating the number of milliseconds since


                     117 ;96: midnight for the current date in both forms.


                     118 ;97: 


                     119 ;98: https://www.kepware.com/getattachment/be373908-e367-4779-b8ae-033c73a99b1e/iec-61850-mms-client-manual.pdf


                     120 ;99: 


                     121 ;100: 	6 byte structure containing days since Jan 1, 1984 and milliseconds since


                     122 ;101: 	midnight.It uses the format "MM/DD/YYYY_HH:MM:SS.mmm".


                     123 ;102: */


                     124 ;103: 


                     125 ;104: 


                     126 ;105: #define TIME3232_TO_TIME(t) ((unsigned long)(t/0x100000000L))


                     127 ;106: #define TIME3232FRACT_TO_MS(t)  ((unsigned long)((t&0xFFFFFFFF)*1000/0x100000000L))


                     128 ;107: 


                     129 ;108: #define msPerDay 86400000LL


                     130 ;109: #define Years14_msCount 441763200000LL


                     131 ;110: 


                     132 ;111: 


                     133 ;112: bool getRCB(size_t index, RCB** pRCB)


                     134 ;113: {


                     135 ;114: 	if (index >= g_reportCount)


                     136 ;115: 	{


                     137 ;116: 		return false;


                     138 ;117: 	}


                     139 ;118: 	*pRCB = &g_reports[index].rcb;


                     140 ;119: 	return true;


                     141 ;120: }


                     142 ;121: 


                     143 ;122: PReporter getReporterByIndex(size_t index)


                     144 ;123: {


                     145 ;124: 	if (index >= g_reportCount) 


                     146 ;125: 	{


                     147 ;126: 		return NULL;


                     148 ;127: 	}


                     149 ;128: 	return g_reports + index;


                     150 ;129: }


                     151 ;130: 


                     152 ;131: static void encodeBinaryTime(uint8_t* outBuf, unsigned long long preсiseTime)


                     153 

                     154 ;169: 


                     155 ;170: 


                     156 ;171: }


                     157 

                     158 ;172: 


                     159 ;173: 


                     160 ;174: bool isRCBConnected(PReporter pReport)


                     161 ;175: {


                     162 ;176: 	IsoConnection* conn = pReport->connection;


                     163 ;177: 	return conn != NULL && conn->connected;	


                     164 ;178: }


                     165 ;179: 


                     166 ;180: //=====Сравнение данных DataSet и создание отчёта при наличии изменений======


                     167 ;181: 


                     168 ;182: //Готовит данные отчёта для Integrity или General Interrogation


                     169 ;183: static bool processDataSetIntgGi(DataSetItem* firstDSItem, RptProcContext* context)


                     170 

                     171 ;211: }


                     172 


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                     173 ;212: 


                     174 ;213: //Готовит данные отчёта при изменении данных


                     175 ;214: static bool processDataSetChange(DataSetItem* firstDSItem, RptProcContext* context)


                     176 

                     177 ;254: }


                     178 

                     179 ;255: 


                     180 ;256: //Записывает в буфер значения для отчёта с учтётом trgOps 


                     181 ;257: //mode. Одновременно заполняет массив с причинами передачи (Reason-For-Inclusion)


                     182 ;258: static bool writeReportValues(Reporter* pRCB, RptProcessMode mode, BufferView* outBuf)


                     183 

                     184 ;282: 	}


                     185 ;283: }


                     186 

                     187 ;284: 


                     188 ;285: //=======Инициализация текущих значений отчёта для дальнейшего сравнения==


                     189 ;286: 


                     190 ;287: bool initReportCompareDataset(PReporter reporter)


                     191 ;288: {


                     192 ;289: 


                     193 ;290: 	DataSet* dataSet = reporter->dataSet;


                     194 ;291: 


                     195 ;292: 	if (dataSet->itemCount > MAX_DATASET_OBJECT_COUNT)


                     196 ;293: 	{


                     197 ;294: 		ERROR_REPORT("Too many dataset objects");


                     198 ;295: 		return false;


                     199 ;296: 	}


                     200 ;297: 


                     201 ;298: 	return true;


                     202 ;299: }


                     203 ;300: 


                     204 ;301: //===========================================================================


                     205 ;302: 


                     206 ;303: static int writeRptID(RCB* pRCB, uint8_t* outBuf, int outBufPos)


                     207 

                     208 ;307: 		(const uint8_t*)pRCB->rptID.p, pRCB->rptID.len, outBuf, outBufPos);


                     209 ;308: }


                     210 

                     211 ;309: 


                     212 ;310: static int writeOptFlds(uint16_t optFlds, uint8_t* outBuf, int outBufPos)


                     213 

                     214 ;316: }


                     215 

                     216 ;317: 


                     217 ;318: static int writeTimeOfEntry(RCB* pRCB, uint8_t* outBuf, int outBufPos)


                     218 

                     219 ;333: }


                     220 

                     221 ;334: 


                     222 ;335: static int writeDatSetRef(RCB* pRCB, uint8_t* outBuf, int outBufPos)


                     223 

                     224 ;339: 		pRCB->dataSetName, pRCB->dataSetNameLength, outBuf, outBufPos);


                     225 ;340: }


                     226 

                     227 ;341: 


                     228 ;342: static int writeBufferOverflow(Reporter* pRCB, uint8_t* outBuf, int outBufPos)


                     229 

                     230 ;346: 		outBuf, outBufPos);


                     231 ;347: }


                     232 

                     233 ;348: 



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                     234 ;349: static int writeEntryID(RCB* pRCB, uint8_t* outBuf, int outBufPos)


                     235 

                     236 ;352: }


                     237 

                     238 ;353: 


                     239 ;354: static int writeConfRev(RCB* pRCB, uint8_t* outBuf, int outBufPos)


                     240 

                     241 ;357: 		pRCB->confRev, outBuf, outBufPos);	


                     242 ;358: }


                     243 

                     244 ;359: 


                     245 ;360: static int writeInclusionBitstring(Reporter* pRCB, uint8_t* outBuf, int outBufPos)


                     246 

                     247 ;383: }


                     248 

                     249 ;384: 


                     250 ;385: static int writeReasonForInclusion(Reporter* pRCB, uint8_t* outBuf, int outBufPos)


                     251 

                     252 ;402: }


                     253 

                     254 ;403: 


                     255 ;404: static int writeReportAccessResults(Reporter* pReporter, uint8_t* outBuf, 


                     256 

                     257 ;464: }


                     258 

                     259 ;465: 


                     260 ;466: //Собирает данные для отчёта и пишет в буфер


                     261 ;467: static int writeReport(Reporter* pReporter, RptProcessMode mode)


                     262 ;468: {		


                     263 ;469: 	BufferView reportValBufView;


                     264 ;470: 	BufferView_init(&reportValBufView, reportValuesBuf, sizeof(reportValuesBuf),0);


                     265 ;471: 		


                     266 ;472: 	if(! writeReportValues(pReporter, mode, &reportValBufView))


                     267 ;473: 	{


                     268 ;474: 		ERROR_REPORT("Error processing dataset");


                     269 ;475: 		return 0;


                     270 ;476: 	}


                     271 ;477: 


                     272 ;478: 	if (reportValBufView.pos == 0)


                     273 ;479: 	{


                     274 ;480: 		//Никаких данных не собрано, отчёт не посылаем.


                     275 ;481: 		return 0;


                     276 ;482: 	}


                     277 ;483: 


                     278 ;484: 	return writeReportAccessResults(pReporter,


                     279 ;485: 		reportAccessResultsBuf, reportValuesBuf, reportValBufView.pos);


                     280 ;486: }


                     281 ;487: 


                     282 ;488: //Оформляет и отправляет отчёт


                     283 ;489: static void sendReport(IsoConnection* isoConn, uint8_t* mmsReport, int byteCount,


                     284 ;490:                 SessionOutBuffer* sessionOutBuf)


                     285 ;491: {


                     286 ;492: 	int sessionDataLen;


                     287 ;493: 	int presentationDataLen;


                     288 ;494: 	int bufPos = 0;


                     289 ;495:     int reportLen;


                     290 ;496:     int varNameLen;


                     291 ;497:     int reportVarLen;


                     292 ;498:     int reportContentLen;


                     293 ;499: 


                     294 ;500: 	if (isoConn == NULL)



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                     295 ;501: 	{


                     296 ;502: 		return;


                     297 ;503: 	}


                     298 ;504: 


                     299 ;505: 	//Определяем длины


                     300 ;506: 


                     301 ;507: 	//A0


                     302 ;508:     reportLen = 1 +


                     303 ;509: 		BerEncoder_determineLengthSize(byteCount) + byteCount;


                     304 ;510: 


                     305 ;511: 	//A1


                     306 ;512:     varNameLen = sizeof(reportVarNameSequence);


                     307 ;513: 	


                     308 ;514: 	//A0 без тэга


                     309 ;515:     reportVarLen = reportLen + varNameLen;


                     310 ;516: 


                     311 ;517: 	//A0 с тегом и длиной


                     312 ;518:     reportContentLen = 1 +


                     313 ;519: 		BerEncoder_determineLengthSize(reportVarLen) + reportVarLen;


                     314 ;520: 


                     315 ;521: 	// Кодируем


                     316 ;522:     //Unconfirmed PDU


                     317 ;523:     bufPos = BerEncoder_encodeTL(0xA3, reportContentLen, reportMmsBuf, bufPos);


                     318 ;524: 


                     319 ;525: 	bufPos = BerEncoder_encodeTL(0xA0, reportVarLen, reportMmsBuf, bufPos);


                     320 ;526: 	memcpy(reportMmsBuf + bufPos, reportVarNameSequence, varNameLen);


                     321 ;527: 	bufPos += varNameLen;


                     322 ;528: 	bufPos = BerEncoder_encodeTL(0xA0, byteCount, reportMmsBuf, bufPos);


                     323 ;529: 	memcpy(reportMmsBuf + bufPos, mmsReport, byteCount);


                     324 ;530: 	bufPos += byteCount;


                     325 ;531: 	byteCount = bufPos;


                     326 ;532: 	


                     327 ;533: 	presentationDataLen = IsoPresentation_createUserData(&isoConn->presentation,


                     328 ;534: 		reportPresentationBuf, reportMmsBuf, byteCount);


                     329 ;535: 


                     330 ;536:     sessionDataLen = isoSession_createDataSpdu(sessionOutBuf->cotpOutBuf,


                     331 ;537:         SESSION_OUT_BUF_SIZE, reportPresentationBuf, presentationDataLen);


                     332 ;538:     sessionOutBuf->byteCount = sessionDataLen;


                     333 ;539:     if (!OutQueue_insert(&isoConn->outQueue, sessionOutBuf))


                     334 ;540: 	{


                     335 ;541: 		ERROR_REPORT("Out queue overflow");


                     336 ;542: 		return;


                     337 ;543: 	}


                     338 ;544: }


                     339 ;545: 


                     340 ;546: static void writeReportToBuffer(Reporter* pRCB, uint8_t* pData, int dataLen)


                     341 ;547: {


                     342 ;548: 	ReportQueue_write(&pRCB->buffer, pData, dataLen);


                     343 ;549: }


                     344 ;550: 


                     345 ;551: //Выделяет SessionOutBuf и посылает данные отчёта


                     346 ;552: static void allocateBufAndSendReport(Reporter* pRCB,


                     347 ;553: 	uint8_t* reportData, int reportDataSize)


                     348 ;554: {


                     349 ;555:     SessionOutBuffer* outBuf;


                     350 ;556: 	IsoConnection* pConn = pRCB->connection;


                     351 ;557: 	if (pConn == NULL)


                     352 ;558: 	{


                     353 ;559: 		return;


                     354 ;560: 	}


                     355 ;561:     outBuf = allocSessionOutBuffer(&pConn->outBuffers, SESSION_OUT_BUF_SIZE);



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                     356 ;562: 	if (outBuf)


                     357 ;563: 	{


                     358 ;564: 		sendReport(pConn, reportData, reportDataSize, outBuf);


                     359 ;565: 	}


                     360 ;566: 	else


                     361 ;567: 	{


                     362 ;568: 		ERROR_REPORT("Unable to allocate buffer for the report");


                     363 ;569: 	}


                     364 ;570: }


                     365 ;571: 


                     366 ;572: //Вызывается каждую миллисекунду для таймера integrity


                     367 ;573: static void integrityTimerProc(void)


                     368 ;574: {


                     369 ;575: 	size_t rcbIdx;


                     370 ;576: 	for (rcbIdx = 0; rcbIdx < g_reportCount; ++rcbIdx)


                     371 ;577: 	{


                     372 ;578: 		Reporter* pReporter = g_reports + rcbIdx;


                     373 ;579: 		RCB* pRCB = &pReporter->rcb;


                     374 ;580: 		if ((pRCB->trgOps & TRGOP_INTEGRITY) == 0 || pRCB->intgPd == 0)


                     375 ;581: 		{


                     376 ;582: 			continue;


                     377 ;583: 		}


                     378 ;584: 		if (pReporter->intgPdCounter < pRCB->intgPd)


                     379 ;585: 		{


                     380 ;586: 			pReporter->intgPdCounter++;


                     381 ;587: 		}


                     382 ;588: 		else


                     383 ;589: 		{


                     384 ;590: 			pReporter->intgPdCounter = 0;


                     385 ;591: 			pReporter->intgTimerAlam = true;


                     386 ;592: 		}


                     387 ;593: 	}


                     388 ;594: }


                     389 ;595: 


                     390 ;596: //Обнаруживает изменения в наборах данных, зарегистрированных в отчётах,


                     391 ;597: //и при наличии посылает соответствующий отчёт(небуферизированные) 


                     392 ;598: //или помещает его в буфер (буферизированные)


                     393 ;599: static void processAllReportsData()


                     394 

                     395 ;625: 						reportDataSize);


                     396 ;626: 				}


                     397 ;627: 			}


                     398 ;628: 		}


                     399 ;629: 	}


                     400 ;630: }


                     401 

                     402 ;631: 


                     403 ;632: 


                     404 ;633: static bool bufferedReportHasData(Reporter* pCurrReport)


                     405 

                     406 ;636: }


                     407 

                     408 ;637: 


                     409 ;638: static int readReportFromBuffer(Reporter* pRCB, uint8_t* bufferToRead, int bufSize)


                     410 

                     411 ;641: }


                     412 

                     413 ;642: 


                     414 ;643: static void sendFromReportBuf(Reporter* pRCB)


                     415 

                     416 ;654: 				&pRCB->sessionOutBuffer);



                                                                      Page 8
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                     417 ;655: 		}


                     418 ;656: 	}		


                     419 ;657: }


                     420 

                     421 ;658: 


                     422 ;659: 


                     423 ;660: //Посылает GI если установлены соответствующие флаги в RCB.


                     424 ;661: //Сбрасывает GI после посылки.


                     425 ;662: //Возвращает FALSE если GI не нужно.


                     426 ;663: static bool processGI(Reporter* pReporter)


                     427 ;664: {


                     428 ;665: 	int reportDataSize;


                     429 ;666: 	if (!pReporter->rcb.gi)


                     430 ;667: 	{


                     431 ;668: 		return false;


                     432 ;669: 	}	


                     433 ;670: 	reportDataSize = writeReport(pReporter, RPT_GI);


                     434 ;671: 	if (reportDataSize > 0)


                     435 ;672: 	{


                     436 ;673: 		allocateBufAndSendReport(pReporter, reportAccessResultsBuf, reportDataSize);


                     437 ;674: 		pReporter->rcb.gi = false;


                     438 ;675: 	}	


                     439 ;676: 	return true;


                     440 ;677: }


                     441 ;678: 


                     442 ;679: //Посылает Integrity report если установлены соответствующие флаги в RCB


                     443 ;680: //и время пришло.


                     444 ;681: //Возвращает false если Intregrity report не послан.


                     445 ;682: static bool processIntegrity(Reporter* pReporter)


                     446 

                     447 ;717: }


                     448 

                     449 	.text

                     450 	.align	4

                     451 getRCB::

00000000 e59f3d8c*   452 	ldr	r3,.L649

00000004 e5932000    453 	ldr	r2,[r3]

00000008 e1500002    454 	cmp	r0,r2

0000000c 23a00000    455 	movhs	r0,0

00000010 2a000008    456 	bhs	.L590

00000014 e0800100    457 	add	r0,r0,r0 lsl 2

00000018 e1a02100    458 	mov	r2,r0 lsl 2

0000001c e0800002    459 	add	r0,r0,r2

00000020 e0800382    460 	add	r0,r0,r2 lsl 7

00000024 e0800502    461 	add	r0,r0,r2 lsl 10

00000028 e59f2d68*   462 	ldr	r2,.L650

0000002c e0820100    463 	add	r0,r2,r0 lsl 2

00000030 e5810000    464 	str	r0,[r1]

00000034 e3a00001    465 	mov	r0,1

                     466 .L590:

00000038 e12fff1e*   467 	ret	

                     468 	.endf	getRCB

                     469 	.align	4

                     470 

                     471 ;index	r0	param

                     472 ;pRCB	r1	param

                     473 

                     474 	.section ".bss","awb"

                     475 .L635:

                     476 	.data

                     477 	.text


                                                                      Page 9
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                     478 

                     479 

                     480 	.align	4

                     481 	.align	4

                     482 getReporterByIndex::

0000003c e59f2d50*   483 	ldr	r2,.L649

00000040 e5921000    484 	ldr	r1,[r2]

00000044 e1500001    485 	cmp	r0,r1

00000048 23a00000    486 	movhs	r0,0

0000004c 2a000006    487 	bhs	.L651

00000050 e0800100    488 	add	r0,r0,r0 lsl 2

00000054 e1a01100    489 	mov	r1,r0 lsl 2

00000058 e0800001    490 	add	r0,r0,r1

0000005c e0800381    491 	add	r0,r0,r1 lsl 7

00000060 e0800501    492 	add	r0,r0,r1 lsl 10

00000064 e59f1d2c*   493 	ldr	r1,.L650

00000068 e0810100    494 	add	r0,r1,r0 lsl 2

                     495 .L651:

0000006c e12fff1e*   496 	ret	

                     497 	.endf	getReporterByIndex

                     498 	.align	4

                     499 

                     500 ;index	r0	param

                     501 

                     502 	.section ".bss","awb"

                     503 .L678:

                     504 	.data

                     505 	.text

                     506 

                     507 

                     508 	.align	4

                     509 	.align	4

                     510 isRCBConnected::

00000070 e590103c    511 	ldr	r1,[r0,60]

00000074 e3510000    512 	cmp	r1,0

00000078 13a02bf2    513 	movne	r2,242<<10

0000007c 12822098    514 	addne	r2,r2,152

00000080 17d21001    515 	ldrneb	r1,[r2,r1]

00000084 e3a00000    516 	mov	r0,0

00000088 13510000    517 	cmpne	r1,0

0000008c 13a00001    518 	movne	r0,1

00000090 e20000ff    519 	and	r0,r0,255

00000094 e12fff1e*   520 	ret	

                     521 	.endf	isRCBConnected

                     522 	.align	4

                     523 ;conn	r1	local

                     524 

                     525 ;pReport	r0	param

                     526 

                     527 	.section ".bss","awb"

                     528 .L746:

                     529 	.data

                     530 	.text

                     531 

                     532 

                     533 	.align	4

                     534 	.align	4

                     535 initReportCompareDataset::

00000098 e5900038    536 	ldr	r0,[r0,56]

0000009c e5900004    537 	ldr	r0,[r0,4]

000000a0 e3500f50    538 	cmp	r0,0x0140


                                                                      Page 10
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
000000a4 93a00001    539 	movls	r0,1

000000a8 83a00000    540 	movhi	r0,0

000000ac e12fff1e*   541 	ret	

                     542 	.endf	initReportCompareDataset

                     543 	.align	4

                     544 ;dataSet	r0	local

                     545 

                     546 ;reporter	r0	param

                     547 

                     548 	.section ".bss","awb"

                     549 .L789:

                     550 	.data

                     551 	.text

                     552 

                     553 

                     554 	.align	4

                     555 	.align	4

                     556 	.align	4

                     557 writeReport:

000000b0 e92d4ff0    558 	stmfd	[sp]!,{r4-fp,lr}

000000b4 e59fbce0*   559 	ldr	fp,.L2600

000000b8 e24dd084    560 	sub	sp,sp,132

000000bc e1a08000    561 	mov	r8,r0

000000c0 e1a06000    562 	mov	r6,r0

000000c4 e1a04001    563 	mov	r4,r1

000000c8 e59f1cd0*   564 	ldr	r1,.L2601

000000cc e1a05000    565 	mov	r5,r0

000000d0 e28d0078    566 	add	r0,sp,120

000000d4 e3a03000    567 	mov	r3,0

000000d8 e3a02d80    568 	mov	r2,1<<13

000000dc eb000000*   569 	bl	BufferView_init

                     570 ;259: {


                     571 

                     572 ;260: 	RptProcContext rptProcContext;


                     573 ;261: 	DataSet* dataSet = pRCB->dataSet;


                     574 

000000e0 e5951038    575 	ldr	r1,[r5,56]

                     576 ;262: 


                     577 ;263: 	if (dataSet->itemCount > MAX_DATASET_OBJECT_COUNT)


                     578 

000000e4 e591c004    579 	ldr	r12,[r1,4]

000000e8 e3a00000    580 	mov	r0,0

000000ec e35c0f50    581 	cmp	r12,0x0140

                     582 ;264: 	{


                     583 

                     584 ;265: 		ERROR_REPORT("Too many dataset objects");


                     585 ;266: 		return false;


                     586 

000000f0 8a0001e2    587 	bhi	.L796

                     588 ;267: 	}


                     589 ;268: 


                     590 ;269: 	rptProcContext.mode = mode;


                     591 

000000f4 e1a0c004    592 	mov	r12,r4

000000f8 e58dc064    593 	str	r12,[sp,100]

                     594 ;270: 	rptProcContext.valIdx = 0;	


                     595 

000000fc e58d0068    596 	str	r0,[sp,104]

                     597 ;271: 	rptProcContext.outBuf = outBuf;	


                     598 

00000100 e28d0078    599 	add	r0,sp,120


                                                                      Page 11
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
00000104 e58d0070    600 	str	r0,[sp,112]

                     601 ;272: 	rptProcContext.reporter = pRCB;


                     602 

00000108 e1a05006    603 	mov	r5,r6

0000010c e58d5074    604 	str	r5,[sp,116]

                     605 ;273: 


                     606 ;274: 


                     607 ;275: 	if(mode == RPT_CMP)


                     608 

00000110 e5911000    609 	ldr	r1,[r1]

00000114 e3540003    610 	cmp	r4,3

00000118 1a000028    611 	bne	.L817

                     612 ;276: 	{


                     613 

                     614 ;277: 		return processDataSetChange(dataSet->firstItem, &rptProcContext);


                     615 

                     616 ;215: {


                     617 

                     618 ;216: 	DataSetItem* dsItem = firstDSItem;		


                     619 

0000011c e5d5001a    620 	ldrb	r0,[r5,26]

00000120 e1b04001    621 	movs	r4,r1

                     622 ;217: 	TrgOps changed;


                     623 ;218: 	TrgOps trgOps = (TrgOps)context->reporter->rcb.trgOps;


                     624 

00000124 e58d0014    625 	str	r0,[sp,20]

                     626 ;219: 


                     627 ;220: 	context->itemIdx = 0;


                     628 

00000128 e3a01000    629 	mov	r1,0

0000012c e58d106c    630 	str	r1,[sp,108]

                     631 ;221: 


                     632 ;222: 	while (dsItem != NULL)


                     633 

00000130 e28d7064    634 	add	r7,sp,100

00000134 0a000020    635 	beq	.L815

                     636 .L806:

                     637 ;223: 	{


                     638 

                     639 ;224: 		IEDEntity iedObj = dsItem->obj;


                     640 

00000138 e5946014    641 	ldr	r6,[r4,20]

                     642 ;225: 		if(iedObj == NULL)


                     643 

0000013c e3560000    644 	cmp	r6,0

00000140 0a00000b    645 	beq	.L810

                     646 ;226: 		{


                     647 

                     648 ;227: 			ERROR_REPORT("Invalid pointer to IEDEntity");


                     649 ;228: 			return false;


                     650 

                     651 ;229: 		}		


                     652 ;230: 


                     653 ;231: 		//Для объекта данных проверяем наличие изменений


                     654 ;232: 		changed =  IEDEntity_findChanges(iedObj, trgOps);


                     655 

00000144 e59d1014    656 	ldr	r1,[sp,20]

00000148 e1a00006    657 	mov	r0,r6

0000014c eb000000*   658 	bl	IEDEntity_findChanges

00000150 e1a05000    659 	mov	r5,r0

00000154 e1b0c005    660 	movs	r12,r5


                                                                      Page 12
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                     661 ;233: 


                     662 ;234: 		if(changed != TRGOP_NONE)


                     663 

00000158 0a00000f    664 	beq	.L812

                     665 ;235: 		{


                     666 

                     667 ;236: 			//Добавляем в отчёт


                     668 ;237: 			if(!iedObj->encodeRead(iedObj, context->outBuf ))


                     669 

0000015c e596c05c    670 	ldr	r12,[r6,92]

00000160 e28d1078    671 	add	r1,sp,120

00000164 e1a00006    672 	mov	r0,r6

00000168 e1a0e00f    673 	mov	lr,pc

0000016c e12fff1c*   674 	bx	r12

00000170 e3500000    675 	cmp	r0,0

                     676 .L810:

                     677 ;238: 			{


                     678 

                     679 ;239: 				ERROR_REPORT("Report data reading error");


                     680 ;240: 				return false;


                     681 

00000174 0a00002e    682 	beq	.L831

                     683 .L811:

                     684 ;241: 			}


                     685 ;242: 


                     686 ;243: 			reportInclusionReasons[context->itemIdx] = changed;


                     687 

                     688 ;249: 		}


                     689 ;250: 		context->itemIdx++;


                     690 

00000178 e59f0c24*   691 	ldr	r0,.L2602

0000017c e5971008    692 	ldr	r1,[r7,8]

00000180 e7c05001    693 	strb	r5,[r0,r1]

00000184 e2811001    694 	add	r1,r1,1

00000188 e5944000    695 	ldr	r4,[r4]

0000018c e5871008    696 	str	r1,[r7,8]

                     697 ;251: 		dsItem = dsItem->next;


                     698 

00000190 e3540000    699 	cmp	r4,0

00000194 1affffe7    700 	bne	.L806

00000198 ea000007    701 	b	.L815

                     702 .L812:

                     703 ;244: 		}


                     704 ;245: 		else


                     705 ;246: 		{


                     706 

                     707 ;247: 			// Элемент не изменился, причины добавлять нет


                     708 ;248: 			reportInclusionReasons[context->itemIdx] = 0;


                     709 

                     710 ;249: 		}


                     711 ;250: 		context->itemIdx++;


                     712 

0000019c e59f0c00*   713 	ldr	r0,.L2602

000001a0 e5971008    714 	ldr	r1,[r7,8]

000001a4 e7c0c001    715 	strb	r12,[r0,r1]

000001a8 e2811001    716 	add	r1,r1,1

000001ac e5944000    717 	ldr	r4,[r4]

000001b0 e5871008    718 	str	r1,[r7,8]

                     719 ;251: 		dsItem = dsItem->next;


                     720 

000001b4 e3540000    721 	cmp	r4,0


                                                                      Page 13
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
000001b8 1affffde    722 	bne	.L806

                     723 .L815:

                     724 ;252: 	}


                     725 ;253: 	return true;


                     726 

000001bc ea00001a    727 	b	.L798

                     728 .L817:

                     729 ;278: 	}


                     730 ;279: 	else


                     731 ;280: 	{


                     732 

                     733 ;281: 		return processDataSetIntgGi(dataSet->firstItem, &rptProcContext);


                     734 

                     735 ;184: {


                     736 

                     737 ;185: 	DataSetItem* dsItem = firstDSItem;	


                     738 

000001c0 e1a04001    739 	mov	r4,r1

                     740 ;186: 	uint8_t reason = context->mode == RPT_GI ? REASON_GI : REASON_INTEGRITY;


                     741 

000001c4 e3a01000    742 	mov	r1,0

000001c8 e58d106c    743 	str	r1,[sp,108]

                     744 ;189: 


                     745 ;190: 	while (dsItem != NULL)


                     746 

000001cc e28d6064    747 	add	r6,sp,100

000001d0 e1a07000    748 	mov	r7,r0

000001d4 e3a05002    749 	mov	r5,2

000001d8 e35c0000    750 	cmp	r12,0

000001dc 03a05001    751 	moveq	r5,1

                     752 ;187: 


                     753 ;188: 	context->itemIdx = 0;


                     754 

000001e0 e3540000    755 	cmp	r4,0

000001e4 0a000010    756 	beq	.L798

                     757 .L820:

                     758 ;191: 	{


                     759 

                     760 ;192: 		IEDEntity iedObj = dsItem->obj;


                     761 

000001e8 e5940014    762 	ldr	r0,[r4,20]

                     763 ;193: 		if(iedObj == NULL)


                     764 

000001ec e3500000    765 	cmp	r0,0

000001f0 0a000004    766 	beq	.L823

                     767 ;194: 		{


                     768 

                     769 ;195: 			ERROR_REPORT("Invalid poiter to IEDEntity");


                     770 ;196: 			return false;


                     771 

                     772 ;197: 		}


                     773 ;198: 


                     774 ;199: 		if(!iedObj->encodeRead(iedObj, context->outBuf ))


                     775 

000001f4 e590c05c    776 	ldr	r12,[r0,92]

000001f8 e1a01007    777 	mov	r1,r7

000001fc e1a0e00f    778 	mov	lr,pc

00000200 e12fff1c*   779 	bx	r12

00000204 e3500000    780 	cmp	r0,0

                     781 .L823:

                     782 ;200: 		{



                                                                      Page 14
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                     783 

                     784 ;201: 			ERROR_REPORT("Report data reading error");


                     785 ;202: 			return false;


                     786 

00000208 0a000009    787 	beq	.L831

                     788 .L824:

                     789 ;203: 		}


                     790 ;204: 		reportInclusionReasons[context->itemIdx] = reason;


                     791 

0000020c e59f0b90*   792 	ldr	r0,.L2602

00000210 e5961008    793 	ldr	r1,[r6,8]

00000214 e7c05001    794 	strb	r5,[r0,r1]

                     795 ;205: 


                     796 ;206: 		context->itemIdx++;


                     797 

00000218 e2811001    798 	add	r1,r1,1

0000021c e5944000    799 	ldr	r4,[r4]

00000220 e5861008    800 	str	r1,[r6,8]

                     801 ;207: 


                     802 ;208: 		dsItem = dsItem->next;


                     803 

00000224 e3540000    804 	cmp	r4,0

00000228 1affffee    805 	bne	.L820

                     806 ;209: 	}


                     807 ;210: 	return true;


                     808 

                     809 .L798:

0000022c e59d107c    810 	ldr	r1,[sp,124]

00000230 e3510000    811 	cmp	r1,0

                     812 .L831:

00000234 03a00000    813 	moveq	r0,0

00000238 0a000190    814 	beq	.L796

                     815 .L830:

0000023c e1d862b0    816 	ldrh	r6,[r8,32]

00000240 e1a09008    817 	mov	r9,r8

                     818 ;409: 	uint16_t optFlds = pRCB->optFlds;


                     819 

00000244 e1cd60ba    820 	strh	r6,[sp,10]

                     821 ;410: 	


                     822 ;411: 


                     823 ;412: 	outBufPos = writeRptID(pRCB, outBuf, outBufPos);


                     824 

                     825 ;304: {		


                     826 

                     827 ;305: 	//Используется OctetString потому что она позволяет указать длину строки


                     828 ;306: 	return BerEncoder_encodeOctetString(IEC61850_BER_VISIBLE_STRING,


                     829 

00000248 e58d1014    830 	str	r1,[sp,20]

                     831 ;405: 	uint8_t* encodedValues, int encodedValuesSize)


                     832 ;406: {


                     833 

                     834 ;407: 	int outBufPos = 0;


                     835 

                     836 ;408: 	RCB* pRCB = &pReporter->rcb;


                     837 

0000024c e3a01000    838 	mov	r1,0

00000250 e59f5b50*   839 	ldr	r5,.L2603

00000254 e58d1000    840 	str	r1,[sp]

00000258 e1a03005    841 	mov	r3,r5

0000025c e9980003    842 	ldmed	[r8],{r0-r1}

00000260 e1a02000    843 	mov	r2,r0


                                                                      Page 15
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
00000264 e3a0008a    844 	mov	r0,138

00000268 eb000000*   845 	bl	BerEncoder_encodeOctetString

0000026c e2504000    846 	subs	r4,r0,0

                     847 ;413: 	RET_IF_NOT(outBufPos > 0, "Error writing RptID");


                     848 

00000270 da000181    849 	ble	.L873

                     850 ;414: 	


                     851 ;415: 	outBufPos = writeOptFlds(optFlds, outBuf, outBufPos);


                     852 

                     853 ;311: {	


                     854 

                     855 ;312: 	outBufPos = BerEncoder_encodeUshortBitString(IEC61850_BER_BIT_STRING, 10, 


                     856 

00000274 e58d4000    857 	str	r4,[sp]

00000278 e1a03005    858 	mov	r3,r5

0000027c e1a02006    859 	mov	r2,r6

00000280 e3a0100a    860 	mov	r1,10

00000284 e3a00084    861 	mov	r0,132

00000288 eb000000*   862 	bl	BerEncoder_encodeUshortBitString

                     863 ;313: 		optFlds, outBuf, outBufPos);


                     864 ;314: 


                     865 ;315: 	return outBufPos;		


                     866 

0000028c e2504000    867 	subs	r4,r0,0

                     868 ;416: 	RET_IF_NOT(outBufPos > 0, "Error writing OptFlds");


                     869 

00000290 da000179    870 	ble	.L873

                     871 ;417: 		


                     872 ;418: 	if (optFlds & OPTFLDS_TIME_STAMP)


                     873 

00000294 e1dd00ba    874 	ldrh	r0,[sp,10]

00000298 e3100080    875 	tst	r0,128

0000029c 0a00004d    876 	beq	.L845

                     877 ;419: 	{


                     878 

                     879 ;420: 		outBufPos = writeTimeOfEntry(pRCB, outBuf, outBufPos);


                     880 

                     881 ;319: {	


                     882 

                     883 ;320: 	//0x8C, 0x06, 0x01, 0x90, 0xE4, 0x3A, 0x32, 0x13


                     884 ;321: 	/*


                     885 ;322: 	uint8_t timeStampBuf[8] = { 0x01, 0x90, 0xE4, 0x3A, 0x32, 0x13 };


                     886 ;323: 	RCB* pReport = g_reports + reportIndex;


                     887 ;324: 	return BerEncoder_encodeOctetString( 0x8C, timeStampBuf, 6, outBuf,


                     888 ;325: 		outBufPos);


                     889 ;326: 	*/


                     890 ;327: 	unsigned long long timestamp;	


                     891 ;328: 	timestamp = dataSliceGetTimeStamp();


                     892 

000002a0 eb000000*   893 	bl	dataSliceGetTimeStamp

000002a4 e58d1028    894 	str	r1,[sp,40]

                     895 ;329: 	outBuf[outBufPos++] = 0x8C;


                     896 

000002a8 e2846001    897 	add	r6,r4,1

000002ac e58d0024    898 	str	r0,[sp,36]

000002b0 e3a0208c    899 	mov	r2,140

000002b4 e7c52004    900 	strb	r2,[r5,r4]

                     901 ;330: 	outBuf[outBufPos++] = 0x06;


                     902 

000002b8 e3a02006    903 	mov	r2,6

000002bc e7c52006    904 	strb	r2,[r5,r6]


                                                                      Page 16
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
000002c0 e3a02000    905 	mov	r2,0

000002c4 e3a03001    906 	mov	r3,1

000002c8 eb000000*   907 	bl	__gh_udiv64

000002cc e59d2024    908 	ldr	r2,[sp,36]

000002d0 e3a0affa    909 	mov	r10,0x03e8

000002d4 e08ec29a    910 	umull	r12,lr,r10,r2

000002d8 e2866001    911 	add	r6,r6,1

                     912 ;331: 	encodeBinaryTime(outBuf + outBufPos, timestamp);


                     913 

000002dc e58d001c    914 	str	r0,[sp,28]

000002e0 e58de038    915 	str	lr,[sp,56]

000002e4 e1a03102    916 	mov	r3,r2 lsl 2

000002e8 e0422003    917 	sub	r2,r2,r3

000002ec e0822283    918 	add	r2,r2,r3 lsl 5

000002f0 e1a00182    919 	mov	r0,r2 lsl 3

000002f4 e58d0034    920 	str	r0,[sp,52]

000002f8 e3a03001    921 	mov	r3,1

000002fc e3a01000    922 	mov	r1,0

00000300 e58d1020    923 	str	r1,[sp,32]

                     924 ;142: 	uint32_t mSecCount = TIME3232FRACT_TO_MS(preсiseTime);


                     925 

00000304 e1a02001    926 	mov	r2,r1

00000308 e1a0100e    927 	mov	r1,lr

0000030c eb000000*   928 	bl	__gh_udiv64

                     929 ;143: 	timestamp = secCount * 1000 + mSecCount;


                     930 

00000310 e59d301c    931 	ldr	r3,[sp,28]

00000314 e1a07003    932 	mov	r7,r3

00000318 e082339a    933 	umull	r3,r2,r10,r3

0000031c e59d3020    934 	ldr	r3,[sp,32]

00000320 e0864005    935 	add	r4,r6,r5

                     936 ;132: {


                     937 

                     938 ;133: 	


                     939 ;134: 	unsigned long long mmsTime;


                     940 ;135: 	unsigned long long timestamp;


                     941 ;136:     uint8_t* binaryTimeBuf;


                     942 ;137:     uint16_t daysDiff;


                     943 ;138:     uint8_t* daysDiffBuf;


                     944 ;139:     uint32_t msSinceMidnight;


                     945 ;140:     uint8_t* msSinceMidnightBuf;


                     946 ;141: 	unsigned long long secCount = TIME3232_TO_TIME(preсiseTime);


                     947 

00000324 e1a0c103    948 	mov	r12,r3 lsl 2

00000328 e043300c    949 	sub	r3,r3,r12

0000032c e083328c    950 	add	r3,r3,r12 lsl 5

00000330 e0821183    951 	add	r1,r2,r3 lsl 3

00000334 e0472107    952 	sub	r2,r7,r7 lsl 2

00000338 e0822387    953 	add	r2,r2,r7 lsl 7

0000033c e0900182    954 	adds	r0,r0,r2 lsl 3

00000340 e2a11000    955 	adc	r1,r1,0

                     956 ;144: 


                     957 ;145: 	if (timestamp > Years14_msCount)


                     958 

00000344 e59f2a60*   959 	ldr	r2,.L2604

00000348 e3510066    960 	cmp	r1,102

0000034c 01500002    961 	cmpeq	r0,r2

                     962 ;147: 	else


                     963 ;148: 		mmsTime = 0;


                     964 

00000350 93a00000    965 	movls	r0,0


                                                                      Page 17
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
00000354 958d0034    966 	strls	r0,[sp,52]

00000358 9a000002    967 	bls	.L843

                     968 ;146: 		mmsTime = timestamp - (Years14_msCount);


                     969 

0000035c e0502002    970 	subs	r2,r0,r2

00000360 e58d2034    971 	str	r2,[sp,52]

00000364 e2c10066    972 	sbc	r0,r1,102

                     973 .L843:

00000368 e59f2a40*   974 	ldr	r2,.L2605

0000036c e58d0038    975 	str	r0,[sp,56]

00000370 e1a01000    976 	mov	r1,r0

                     977 ;149: 


                     978 ;150:     binaryTimeBuf = outBuf;


                     979 

                     980 ;151: 


                     981 ;152: 


                     982 ;153:     daysDiff = (uint16_t)(mmsTime / (msPerDay));


                     983 

00000374 e59d0034    984 	ldr	r0,[sp,52]

00000378 e3a03000    985 	mov	r3,0

0000037c eb000000*   986 	bl	__gh_udiv64

00000380 e1cd00b8    987 	strh	r0,[sp,8]

                     988 ;154:     daysDiffBuf = (uint8_t*)&daysDiff;


                     989 

                     990 ;155: 


                     991 ;156: 


                     992 ;157: 	binaryTimeBuf[4] = daysDiffBuf[1];


                     993 

00000384 e5dd2009    994 	ldrb	r2,[sp,9]

00000388 e5c42004    995 	strb	r2,[r4,4]

                     996 ;158: 	binaryTimeBuf[5] = daysDiffBuf[0];


                     997 

0000038c e5dd2008    998 	ldrb	r2,[sp,8]

00000390 e59d1038    999 	ldr	r1,[sp,56]

00000394 e5c42005   1000 	strb	r2,[r4,5]

                    1001 ;159: 


                    1002 ;160: 


                    1003 ;161:     msSinceMidnight = mmsTime % (msPerDay);


                    1004 

00000398 e59f2a10*  1005 	ldr	r2,.L2605

0000039c e59d0034   1006 	ldr	r0,[sp,52]

000003a0 e3a03000   1007 	mov	r3,0

000003a4 eb000000*  1008 	bl	__gh_urem64

000003a8 e58d0004   1009 	str	r0,[sp,4]

                    1010 ;162:     msSinceMidnightBuf = (uint8_t*)&msSinceMidnight;


                    1011 

                    1012 ;163: 


                    1013 ;164: 


                    1014 ;165: 	binaryTimeBuf[0] = msSinceMidnightBuf[3];


                    1015 

000003ac e5dd1007   1016 	ldrb	r1,[sp,7]

000003b0 e5c41000   1017 	strb	r1,[r4]

                    1018 ;166: 	binaryTimeBuf[1] = msSinceMidnightBuf[2];


                    1019 

000003b4 e5dd1006   1020 	ldrb	r1,[sp,6]

000003b8 e5c41001   1021 	strb	r1,[r4,1]

                    1022 ;167: 	binaryTimeBuf[2] = msSinceMidnightBuf[1];


                    1023 

000003bc e5dd1005   1024 	ldrb	r1,[sp,5]

000003c0 e5c41002   1025 	strb	r1,[r4,2]

                    1026 ;168: 	binaryTimeBuf[3] = msSinceMidnightBuf[0];



                                                                      Page 18
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    1027 

000003c4 e5dd1004   1028 	ldrb	r1,[sp,4]

000003c8 e5c41003   1029 	strb	r1,[r4,3]

                    1030 ;332: 	return outBufPos + 6;


                    1031 

000003cc e2864006   1032 	add	r4,r6,6

                    1033 ;421: 		RET_IF_NOT(outBufPos > 0, "Error writing TimeOfEntry");


                    1034 

000003d0 e3540000   1035 	cmp	r4,0

000003d4 da000128   1036 	ble	.L873

                    1037 .L845:

                    1038 ;422: 	}


                    1039 ;423: 	


                    1040 ;424: 	if (optFlds & OPTFLDS_DATA_SET)


                    1041 

000003d8 e1dd00ba   1042 	ldrh	r0,[sp,10]

000003dc e3100020   1043 	tst	r0,32

000003e0 0a000007   1044 	beq	.L848

                    1045 ;425: 	{


                    1046 

                    1047 ;426: 		outBufPos = writeDatSetRef(pRCB, outBuf, outBufPos);


                    1048 

                    1049 ;336: {	


                    1050 

                    1051 ;337: 	//Используется OctetString потому что она позволяет указать длину строки


                    1052 ;338: 	return BerEncoder_encodeOctetString(IEC61850_BER_VISIBLE_STRING,


                    1053 

000003e4 e1a03005   1054 	mov	r3,r5

000003e8 e58d4000   1055 	str	r4,[sp]

000003ec e5992010   1056 	ldr	r2,[r9,16]

000003f0 e599100c   1057 	ldr	r1,[r9,12]

000003f4 e3a0008a   1058 	mov	r0,138

000003f8 eb000000*  1059 	bl	BerEncoder_encodeOctetString

000003fc e2504000   1060 	subs	r4,r0,0

                    1061 ;427: 		RET_IF_NOT(outBufPos > 0, "Error writing DatSet");


                    1062 

00000400 da00011d   1063 	ble	.L873

                    1064 .L848:

                    1065 ;428: 	}


                    1066 ;429: 


                    1067 ;430: 	if (optFlds & OPTFLDS_BUFFER_OVERFLOW)


                    1068 

00000404 e1dd00ba   1069 	ldrh	r0,[sp,10]

00000408 e3100008   1070 	tst	r0,8

0000040c 0a000008   1071 	beq	.L851

                    1072 ;431: 	{


                    1073 

                    1074 ;432: 		outBufPos = writeBufferOverflow(pReporter, outBuf, outBufPos);


                    1075 

                    1076 ;343: {


                    1077 

                    1078 ;344: 	bool overflow = ReportQueue_isOverflow(&pRCB->buffer);


                    1079 

00000410 e2880040   1080 	add	r0,r8,64

00000414 eb000000*  1081 	bl	ReportQueue_isOverflow

00000418 e1a03004   1082 	mov	r3,r4

0000041c e1a02005   1083 	mov	r2,r5

00000420 e20010ff   1084 	and	r1,r0,255

                    1085 ;345: 	return BerEncoder_encodeBoolean(IEC61850_BER_BOOLEAN, overflow,


                    1086 

00000424 e3a00083   1087 	mov	r0,131


                                                                      Page 19
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
00000428 eb000000*  1088 	bl	BerEncoder_encodeBoolean

0000042c e2504000   1089 	subs	r4,r0,0

                    1090 ;433: 		RET_IF_NOT(outBufPos > 0, "Error writing <buffer overflow> field");


                    1091 

00000430 da000111   1092 	ble	.L873

                    1093 .L851:

                    1094 ;434: 	}


                    1095 ;435: 


                    1096 ;436: 


                    1097 ;437: 	if (optFlds & OPTFLDS_ENTRY_ID)


                    1098 

00000434 e1dd00ba   1099 	ldrh	r0,[sp,10]

00000438 e3100004   1100 	tst	r0,4

0000043c 0a000006   1101 	beq	.L854

                    1102 ;438: 	{


                    1103 

                    1104 ;439: 		outBufPos = writeEntryID(pRCB, outBuf, outBufPos);


                    1105 

                    1106 ;350: {	


                    1107 

                    1108 ;351: 	return encodeOctetString8Value(outBuf, outBufPos, &pRCB->entryID, FALSE);


                    1109 

00000440 e2892024   1110 	add	r2,r9,36

00000444 e1a01004   1111 	mov	r1,r4

00000448 e1a00005   1112 	mov	r0,r5

0000044c e3a03000   1113 	mov	r3,0

00000450 eb000000*  1114 	bl	encodeOctetString8Value

00000454 e2504000   1115 	subs	r4,r0,0

                    1116 ;440: 		RET_IF_NOT(outBufPos > 0, "Error writing EntryID");


                    1117 

00000458 da000107   1118 	ble	.L873

                    1119 .L854:

                    1120 ;441: 	}


                    1121 ;442: 	pRCB->entryID++;


                    1122 

0000045c e1a07009   1123 	mov	r7,r9

00000460 e5971024   1124 	ldr	r1,[r7,36]

00000464 e597c028   1125 	ldr	r12,[r7,40]

00000468 e2912001   1126 	adds	r2,r1,1

0000046c e5872024   1127 	str	r2,[r7,36]

00000470 e2ac3000   1128 	adc	r3,r12,0

00000474 e1dd00ba   1129 	ldrh	r0,[sp,10]

00000478 e5873028   1130 	str	r3,[r7,40]

                    1131 ;443: 


                    1132 ;444: 	if (optFlds & OPTFLDS_CONF_REV)


                    1133 

0000047c e3100002   1134 	tst	r0,2

00000480 0a000006   1135 	beq	.L857

                    1136 ;445: 	{


                    1137 

                    1138 ;446: 		outBufPos = writeConfRev(pRCB, outBuf, outBufPos);


                    1139 

                    1140 ;355: {


                    1141 

                    1142 ;356: 	return BerEncoder_encodeUInt32WithTL(IEC61850_BER_UNSIGNED_INTEGER,


                    1143 

00000484 e1a03004   1144 	mov	r3,r4

00000488 e1a02005   1145 	mov	r2,r5

0000048c e5971014   1146 	ldr	r1,[r7,20]

00000490 e3a00086   1147 	mov	r0,134

00000494 eb000000*  1148 	bl	BerEncoder_encodeUInt32WithTL


                                                                      Page 20
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
00000498 e2504000   1149 	subs	r4,r0,0

                    1150 ;447: 		RET_IF_NOT(outBufPos > 0, "Error writing ConfRev");


                    1151 

0000049c da0000f6   1152 	ble	.L873

                    1153 .L857:

                    1154 ;448: 	}


                    1155 ;449: 	


                    1156 ;450: 	outBufPos = writeInclusionBitstring(pReporter, outBuf, outBufPos);


                    1157 

000004a0 e58d4010   1158 	str	r4,[sp,16]

                    1159 ;361: {    


                    1160 

                    1161 ;362:     //inclusion-bitstring


                    1162 ;363:     uint8_t inclusionBitString[BYTES_IN_INCLUSION_BITSTING];


                    1163 ;364: 	int inclusionFlagNum;


                    1164 ;365: 	int dataSetObjectCount = pRCB->dataSet->itemCount;


                    1165 

000004a4 e28d003c   1166 	add	r0,sp,60

000004a8 e5981038   1167 	ldr	r1,[r8,56]

000004ac e3a02028   1168 	mov	r2,40

000004b0 e5919004   1169 	ldr	r9,[r1,4]

                    1170 ;366: 


                    1171 ;367:     memset(inclusionBitString, 0, BYTES_IN_INCLUSION_BITSTING);


                    1172 

000004b4 e3a01000   1173 	mov	r1,0

000004b8 eb000000*  1174 	bl	memset

                    1175 ;368: 	


                    1176 ;369: 	for (inclusionFlagNum = 0; inclusionFlagNum < dataSetObjectCount; ++inclusionFlagNum)


                    1177 

000004bc e3a0c000   1178 	mov	r12,0

000004c0 e3590000   1179 	cmp	r9,0

000004c4 a1a00009   1180 	movge	r0,r9

000004c8 b3a00000   1181 	movlt	r0,0

000004cc e1b041a0   1182 	movs	r4,r0 lsr 3

000004d0 0a00004c   1183 	beq	.L1214

000004d4 e59f78c8*  1184 	ldr	r7,.L2602

000004d8 e28d603c   1185 	add	r6,sp,60

000004dc e3a0e001   1186 	mov	lr,1

                    1187 .L1215:

000004e0 e5d72000   1188 	ldrb	r2,[r7]

000004e4 e3520000   1189 	cmp	r2,0

000004e8 120c3007   1190 	andne	r3,r12,7

000004ec 17d611cc   1191 	ldrneb	r1,[r6,r12 asr 3]

000004f0 12633007   1192 	rsbne	r3,r3,7

000004f4 1181131e   1193 	orrne	r1,r1,lr lsl r3

000004f8 e5d72001   1194 	ldrb	r2,[r7,1]

000004fc 17c611cc   1195 	strneb	r1,[r6,r12 asr 3]

00000500 e3520000   1196 	cmp	r2,0

00000504 0a000005   1197 	beq	.L1223

00000508 e28c2001   1198 	add	r2,r12,1

0000050c e2023007   1199 	and	r3,r2,7

00000510 e7d611c2   1200 	ldrb	r1,[r6,r2 asr 3]

00000514 e2633007   1201 	rsb	r3,r3,7

00000518 e181131e   1202 	orr	r1,r1,lr lsl r3

0000051c e7c611c2   1203 	strb	r1,[r6,r2 asr 3]

                    1204 .L1223:

00000520 e5d72002   1205 	ldrb	r2,[r7,2]

00000524 e3520000   1206 	cmp	r2,0

00000528 0a000005   1207 	beq	.L1227

0000052c e28c2002   1208 	add	r2,r12,2

00000530 e2023007   1209 	and	r3,r2,7


                                                                      Page 21
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
00000534 e7d611c2   1210 	ldrb	r1,[r6,r2 asr 3]

00000538 e2633007   1211 	rsb	r3,r3,7

0000053c e181131e   1212 	orr	r1,r1,lr lsl r3

00000540 e7c611c2   1213 	strb	r1,[r6,r2 asr 3]

                    1214 .L1227:

00000544 e5d72003   1215 	ldrb	r2,[r7,3]

00000548 e3520000   1216 	cmp	r2,0

0000054c 0a000005   1217 	beq	.L1231

00000550 e28c2003   1218 	add	r2,r12,3

00000554 e2023007   1219 	and	r3,r2,7

00000558 e7d611c2   1220 	ldrb	r1,[r6,r2 asr 3]

0000055c e2633007   1221 	rsb	r3,r3,7

00000560 e181131e   1222 	orr	r1,r1,lr lsl r3

00000564 e7c611c2   1223 	strb	r1,[r6,r2 asr 3]

                    1224 .L1231:

00000568 e5d72004   1225 	ldrb	r2,[r7,4]

0000056c e3520000   1226 	cmp	r2,0

00000570 0a000005   1227 	beq	.L1235

00000574 e28c2004   1228 	add	r2,r12,4

00000578 e2023007   1229 	and	r3,r2,7

0000057c e7d611c2   1230 	ldrb	r1,[r6,r2 asr 3]

00000580 e2633007   1231 	rsb	r3,r3,7

00000584 e181131e   1232 	orr	r1,r1,lr lsl r3

00000588 e7c611c2   1233 	strb	r1,[r6,r2 asr 3]

                    1234 .L1235:

0000058c e5d72005   1235 	ldrb	r2,[r7,5]

00000590 e3520000   1236 	cmp	r2,0

00000594 0a000005   1237 	beq	.L1239

00000598 e28c2005   1238 	add	r2,r12,5

0000059c e2023007   1239 	and	r3,r2,7

000005a0 e7d611c2   1240 	ldrb	r1,[r6,r2 asr 3]

000005a4 e2633007   1241 	rsb	r3,r3,7

000005a8 e181131e   1242 	orr	r1,r1,lr lsl r3

000005ac e7c611c2   1243 	strb	r1,[r6,r2 asr 3]

                    1244 .L1239:

000005b0 e5d72006   1245 	ldrb	r2,[r7,6]

000005b4 e3520000   1246 	cmp	r2,0

000005b8 0a000005   1247 	beq	.L1243

000005bc e28c2006   1248 	add	r2,r12,6

000005c0 e2023007   1249 	and	r3,r2,7

000005c4 e7d611c2   1250 	ldrb	r1,[r6,r2 asr 3]

000005c8 e2633007   1251 	rsb	r3,r3,7

000005cc e181131e   1252 	orr	r1,r1,lr lsl r3

000005d0 e7c611c2   1253 	strb	r1,[r6,r2 asr 3]

                    1254 .L1243:

000005d4 e5d72007   1255 	ldrb	r2,[r7,7]

000005d8 e3520000   1256 	cmp	r2,0

000005dc 0a000005   1257 	beq	.L1246

000005e0 e28c2007   1258 	add	r2,r12,7

000005e4 e2023007   1259 	and	r3,r2,7

000005e8 e7d611c2   1260 	ldrb	r1,[r6,r2 asr 3]

000005ec e2633007   1261 	rsb	r3,r3,7

000005f0 e181131e   1262 	orr	r1,r1,lr lsl r3

000005f4 e7c611c2   1263 	strb	r1,[r6,r2 asr 3]

                    1264 .L1246:

000005f8 e2877008   1265 	add	r7,r7,8

000005fc e28cc008   1266 	add	r12,r12,8

00000600 e2544001   1267 	subs	r4,r4,1

00000604 1affffb5   1268 	bne	.L1215

                    1269 .L1214:

00000608 e2104007   1270 	ands	r4,r0,7


                                                                      Page 22
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
0000060c 0a00000d   1271 	beq	.L862

00000610 e59f078c*  1272 	ldr	r0,.L2602

00000614 e28d703c   1273 	add	r7,sp,60

00000618 e08c6000   1274 	add	r6,r12,r0

0000061c e3a0e001   1275 	mov	lr,1

                    1276 .L1249:

00000620 e4d62001   1277 	ldrb	r2,[r6],1

00000624 e3520000   1278 	cmp	r2,0

00000628 120c3007   1279 	andne	r3,r12,7

0000062c 17d711cc   1280 	ldrneb	r1,[r7,r12 asr 3]

00000630 12633007   1281 	rsbne	r3,r3,7

00000634 1181131e   1282 	orrne	r1,r1,lr lsl r3

00000638 17c711cc   1283 	strneb	r1,[r7,r12 asr 3]

0000063c e28cc001   1284 	add	r12,r12,1

00000640 e2544001   1285 	subs	r4,r4,1

00000644 1afffff5   1286 	bne	.L1249

                    1287 .L862:

                    1288 ;377: 		}


                    1289 ;378: 	}		


                    1290 ;379: 


                    1291 ;380: 	outBufPos = BerEncoder_encodeBitString(IEC61850_BER_BIT_STRING,


                    1292 

00000648 e1a03005   1293 	mov	r3,r5

0000064c e28d203c   1294 	add	r2,sp,60

00000650 e59d0010   1295 	ldr	r0,[sp,16]

00000654 e1a01009   1296 	mov	r1,r9

00000658 e58d0000   1297 	str	r0,[sp]

0000065c e3a00084   1298 	mov	r0,132

00000660 eb000000*  1299 	bl	BerEncoder_encodeBitString

                    1300 ;381:         dataSetObjectCount, inclusionBitString, outBuf, outBufPos);


                    1301 ;382: 	return outBufPos;


                    1302 

00000664 e2504000   1303 	subs	r4,r0,0

                    1304 ;451: 	RET_IF_NOT(outBufPos > 0, "Error writing Inclusion Bit string");


                    1305 

00000668 da000083   1306 	ble	.L873

                    1307 ;452: 	


                    1308 ;453: 	//Write values


                    1309 ;454: 	memcpy(outBuf + outBufPos, encodedValues, encodedValuesSize);


                    1310 

0000066c e59d2014   1311 	ldr	r2,[sp,20]

00000670 e59f1728*  1312 	ldr	r1,.L2601

00000674 e0840005   1313 	add	r0,r4,r5

00000678 eb000000*  1314 	bl	memcpy

                    1315 ;455: 	outBufPos += encodedValuesSize;


                    1316 

0000067c e59d0014   1317 	ldr	r0,[sp,20]

00000680 e0844000   1318 	add	r4,r4,r0

                    1319 ;456: 


                    1320 ;457: 	if (optFlds & OPTFLDS_REASON_FOR_INCLUSION)


                    1321 

00000684 e1dd00ba   1322 	ldrh	r0,[sp,10]

00000688 e3100040   1323 	tst	r0,64

                    1324 ;461: 	}


                    1325 ;462: 


                    1326 ;463: 	return outBufPos;


                    1327 

0000068c 01a00004   1328 	moveq	r0,r4

00000690 0a00007a   1329 	beq	.L796

                    1330 ;458: 	{


                    1331 


                                                                      Page 23
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    1332 ;459: 		outBufPos = writeReasonForInclusion(pReporter, outBuf, outBufPos);


                    1333 

00000694 e5981038   1334 	ldr	r1,[r8,56]

00000698 e1a00004   1335 	mov	r0,r4

                    1336 ;386: {	


                    1337 

                    1338 ;387: 	int objIdx;


                    1339 ;388: 	int dataSetObjectCount = pRCB->dataSet->itemCount;


                    1340 

0000069c e5911004   1341 	ldr	r1,[r1,4]

                    1342 ;389: 


                    1343 ;390: 	for (objIdx = 0; objIdx < dataSetObjectCount; ++objIdx)


                    1344 

000006a0 e3a06000   1345 	mov	r6,0

000006a4 e3510000   1346 	cmp	r1,0

000006a8 a1a08001   1347 	movge	r8,r1

000006ac b3a08000   1348 	movlt	r8,0

000006b0 e1b071a8   1349 	movs	r7,r8 lsr 3

000006b4 0a00005a   1350 	beq	.L1255

000006b8 e59f46e4*  1351 	ldr	r4,.L2602

                    1352 .L1256:

000006bc e7d42006   1353 	ldrb	r2,[r4,r6]

000006c0 e3520000   1354 	cmp	r2,0

000006c4 0a000006   1355 	beq	.L1261

000006c8 e1a03005   1356 	mov	r3,r5

000006cc e3a01006   1357 	mov	r1,6

000006d0 e58d0000   1358 	str	r0,[sp]

000006d4 e3a00084   1359 	mov	r0,132

000006d8 eb000000*  1360 	bl	BerEncoder_encodeUcharBitString

000006dc e3500000   1361 	cmp	r0,0

000006e0 da00005c   1362 	ble	.L1300

                    1363 .L1261:

000006e4 e2866001   1364 	add	r6,r6,1

000006e8 e7d42006   1365 	ldrb	r2,[r4,r6]

000006ec e3520000   1366 	cmp	r2,0

000006f0 0a000006   1367 	beq	.L1266

000006f4 e1a03005   1368 	mov	r3,r5

000006f8 e3a01006   1369 	mov	r1,6

000006fc e58d0000   1370 	str	r0,[sp]

00000700 e3a00084   1371 	mov	r0,132

00000704 eb000000*  1372 	bl	BerEncoder_encodeUcharBitString

00000708 e3500000   1373 	cmp	r0,0

0000070c da000051   1374 	ble	.L1300

                    1375 .L1266:

00000710 e2866001   1376 	add	r6,r6,1

00000714 e7d42006   1377 	ldrb	r2,[r4,r6]

00000718 e3520000   1378 	cmp	r2,0

0000071c 0a000006   1379 	beq	.L1271

00000720 e1a03005   1380 	mov	r3,r5

00000724 e3a01006   1381 	mov	r1,6

00000728 e58d0000   1382 	str	r0,[sp]

0000072c e3a00084   1383 	mov	r0,132

00000730 eb000000*  1384 	bl	BerEncoder_encodeUcharBitString

00000734 e3500000   1385 	cmp	r0,0

00000738 da000046   1386 	ble	.L1300

                    1387 .L1271:

0000073c e2866001   1388 	add	r6,r6,1

00000740 e7d42006   1389 	ldrb	r2,[r4,r6]

00000744 e3520000   1390 	cmp	r2,0

00000748 0a000006   1391 	beq	.L1276

0000074c e1a03005   1392 	mov	r3,r5


                                                                      Page 24
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
00000750 e3a01006   1393 	mov	r1,6

00000754 e58d0000   1394 	str	r0,[sp]

00000758 e3a00084   1395 	mov	r0,132

0000075c eb000000*  1396 	bl	BerEncoder_encodeUcharBitString

00000760 e3500000   1397 	cmp	r0,0

00000764 da00003b   1398 	ble	.L1300

                    1399 .L1276:

00000768 e2866001   1400 	add	r6,r6,1

0000076c e7d42006   1401 	ldrb	r2,[r4,r6]

00000770 e3520000   1402 	cmp	r2,0

00000774 0a000006   1403 	beq	.L1281

00000778 e1a03005   1404 	mov	r3,r5

0000077c e3a01006   1405 	mov	r1,6

00000780 e58d0000   1406 	str	r0,[sp]

00000784 e3a00084   1407 	mov	r0,132

00000788 eb000000*  1408 	bl	BerEncoder_encodeUcharBitString

0000078c e3500000   1409 	cmp	r0,0

00000790 da000030   1410 	ble	.L1300

                    1411 .L1281:

00000794 e2866001   1412 	add	r6,r6,1

00000798 e7d42006   1413 	ldrb	r2,[r4,r6]

0000079c e3520000   1414 	cmp	r2,0

000007a0 0a000006   1415 	beq	.L1286

000007a4 e1a03005   1416 	mov	r3,r5

000007a8 e3a01006   1417 	mov	r1,6

000007ac e58d0000   1418 	str	r0,[sp]

000007b0 e3a00084   1419 	mov	r0,132

000007b4 eb000000*  1420 	bl	BerEncoder_encodeUcharBitString

000007b8 e3500000   1421 	cmp	r0,0

000007bc da000025   1422 	ble	.L1300

                    1423 .L1286:

000007c0 e2866001   1424 	add	r6,r6,1

000007c4 e7d42006   1425 	ldrb	r2,[r4,r6]

000007c8 e3520000   1426 	cmp	r2,0

000007cc 0a000006   1427 	beq	.L1291

000007d0 e1a03005   1428 	mov	r3,r5

000007d4 e3a01006   1429 	mov	r1,6

000007d8 e58d0000   1430 	str	r0,[sp]

000007dc e3a00084   1431 	mov	r0,132

000007e0 eb000000*  1432 	bl	BerEncoder_encodeUcharBitString

000007e4 e3500000   1433 	cmp	r0,0

000007e8 da00001a   1434 	ble	.L1300

                    1435 .L1291:

000007ec e2866001   1436 	add	r6,r6,1

000007f0 e7d42006   1437 	ldrb	r2,[r4,r6]

000007f4 e3520000   1438 	cmp	r2,0

000007f8 0a000006   1439 	beq	.L1295

000007fc e1a03005   1440 	mov	r3,r5

00000800 e3a01006   1441 	mov	r1,6

00000804 e58d0000   1442 	str	r0,[sp]

00000808 e3a00084   1443 	mov	r0,132

0000080c eb000000*  1444 	bl	BerEncoder_encodeUcharBitString

00000810 e3500000   1445 	cmp	r0,0

00000814 da00000f   1446 	ble	.L1300

                    1447 .L1295:

00000818 e2866001   1448 	add	r6,r6,1

0000081c e2577001   1449 	subs	r7,r7,1

00000820 1affffa5   1450 	bne	.L1256

                    1451 .L1255:

00000824 e2187007   1452 	ands	r7,r8,7

00000828 0a000010   1453 	beq	.L871


                                                                      Page 25
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
0000082c e59f4570*  1454 	ldr	r4,.L2602

                    1455 .L1298:

00000830 e7d42006   1456 	ldrb	r2,[r4,r6]

00000834 e3520000   1457 	cmp	r2,0

00000838 0a000009   1458 	beq	.L1302

0000083c e1a03005   1459 	mov	r3,r5

00000840 e3a01006   1460 	mov	r1,6

00000844 e58d0000   1461 	str	r0,[sp]

00000848 e3a00084   1462 	mov	r0,132

0000084c eb000000*  1463 	bl	BerEncoder_encodeUcharBitString

00000850 e3500000   1464 	cmp	r0,0

00000854 ca000002   1465 	bgt	.L1302

                    1466 .L1300:

00000858 e3b00000   1467 	movs	r0,0

                    1468 ;460: 		RET_IF_NOT(outBufPos > 0, "Error writing Reason-For-Inclusion");


                    1469 

                    1470 

0000085c 43a00000   1471 	movmi	r0,0

00000860 ea000006   1472 	b	.L796

                    1473 .L1302:

00000864 e2866001   1474 	add	r6,r6,1

00000868 e2577001   1475 	subs	r7,r7,1

0000086c 1affffef   1476 	bne	.L1298

                    1477 .L871:

                    1478 ;398: 		}


                    1479 ;399: 	}


                    1480 ;400: 


                    1481 ;401: 	return outBufPos;


                    1482 

                    1483 ;460: 		RET_IF_NOT(outBufPos > 0, "Error writing Reason-For-Inclusion");


                    1484 

                    1485 

00000870 e3500000   1486 	cmp	r0,0

00000874 b3a00000   1487 	movlt	r0,0

00000878 ea000000   1488 	b	.L796

                    1489 .L873:

0000087c e3a00000   1490 	mov	r0,0

                    1491 .L796:

00000880 e28dd084   1492 	add	sp,sp,132

00000884 e8bd4ff0   1493 	ldmfd	[sp]!,{r4-fp,lr}

00000888 e12fff1e*  1494 	ret	

                    1495 	.endf	writeReport

                    1496 	.align	4

                    1497 ;reportValBufView	[sp,120]	local

                    1498 ;rptProcContext	[sp,100]	local

                    1499 ;dataSet	r1	local

                    1500 ;dsItem	r4	local

                    1501 ;changed	r5	local

                    1502 ;trgOps	[sp,20]	local

                    1503 ;iedObj	r6	local

                    1504 ;dsItem	r4	local

                    1505 ;reason	r5	local

                    1506 ;iedObj	r0	local

                    1507 ;encodedValuesSize	[sp,20]	local

                    1508 ;outBufPos	r4	local

                    1509 ;pRCB	r9	local

                    1510 ;optFlds	[sp,10]	local

                    1511 ;outBufPos	r6	local

                    1512 ;timestamp	[sp,36]	local

                    1513 ;outBuf	r4	local

                    1514 ;mmsTime	[sp,52]	local


                                                                      Page 26
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    1515 ;timestamp	r0	local

                    1516 ;daysDiff	[sp,8]	local

                    1517 ;msSinceMidnight	[sp,4]	local

                    1518 ;secCount	[sp,28]	local

                    1519 ;overflow	r0	local

                    1520 ;outBufPos	[sp,16]	local

                    1521 ;inclusionBitString	[sp,60]	local

                    1522 ;inclusionFlagNum	r12	local

                    1523 ;dataSetObjectCount	r9	local

                    1524 ;byteNum	r2	local

                    1525 ;bitNum	r3	local

                    1526 ;bit	r3	local

                    1527 ;outBufPos	r0	local

                    1528 ;objIdx	r6	local

                    1529 ;dataSetObjectCount	r1	local

                    1530 ;reason	r2	local

                    1531 

                    1532 ;pReporter	r8	param

                    1533 ;mode	r4	param

                    1534 

                    1535 	.section ".bss","awb"

                    1536 .L2334:

00000000 00000000   1537 reportInclusionReasons:	.space	320

00000004 00000000 
00000008 00000000 
0000000c 00000000 
00000010 00000000 
00000014 00000000 
00000018 00000000 
0000001c 00000000 
00000020 00000000 
00000024 00000000 
00000028 00000000 
0000002c 00000000 
00000030 00000000 
00000034 00000000 
00000038 00000000 
0000003c 00000000 
00000040 00000000 
00000044 00000000 
00000048 00000000 
0000004c 00000000 
00000050 00000000 
00000054 00000000 
00000058 00000000 
0000005c 00000000 
00000060 00000000 
00000064 00000000 
00000068 00000000 
0000006c 00000000 
00000070 00000000 
00000074 00000000 
00000078 00000000 
0000007c 00000000 
00000080 00000000 
00000084 00000000 
00000088 00000000 
0000008c 00000000 
00000090 00000000 
00000094 00000000 
00000098 00000000 

                                                                      Page 27
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
0000009c 00000000 
000000a0 00000000 
000000a4 00000000 
000000a8 00000000 
000000ac 00000000 
000000b0 00000000 
000000b4 00000000 
000000b8 00000000 
000000bc 00000000 
000000c0 00000000 
000000c4 00000000 
000000c8 00000000 
000000cc 00000000 
000000d0 00000000 
000000d4 00000000 
000000d8 00000000 
000000dc 00000000 
000000e0 00000000 
000000e4 00000000 
000000e8 00000000 
000000ec 00000000 
000000f0 00000000 
000000f4 00000000 
000000f8 00000000 
000000fc 00000000 
00000140 00000000   1538 reportValuesBuf:	.space	8192

00000144 00000000 
00000148 00000000 
0000014c 00000000 
00000150 00000000 
00000154 00000000 
00000158 00000000 
0000015c 00000000 
00000160 00000000 
00000164 00000000 
00000168 00000000 
0000016c 00000000 
00000170 00000000 
00000174 00000000 
00000178 00000000 
0000017c 00000000 
00000180 00000000 
00000184 00000000 
00000188 00000000 
0000018c 00000000 
00000190 00000000 
00000194 00000000 
00000198 00000000 
0000019c 00000000 
000001a0 00000000 
000001a4 00000000 
000001a8 00000000 
000001ac 00000000 
000001b0 00000000 
000001b4 00000000 
000001b8 00000000 
000001bc 00000000 
000001c0 00000000 
000001c4 00000000 
000001c8 00000000 
000001cc 00000000 

                                                                      Page 28
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
000001d0 00000000 
000001d4 00000000 
000001d8 00000000 
000001dc 00000000 
000001e0 00000000 
000001e4 00000000 
000001e8 00000000 
000001ec 00000000 
000001f0 00000000 
000001f4 00000000 
000001f8 00000000 
000001fc 00000000 
00000200 00000000 
00000204 00000000 
00000208 00000000 
0000020c 00000000 
00000210 00000000 
00000214 00000000 
00000218 00000000 
0000021c 00000000 
00000220 00000000 
00000224 00000000 
00000228 00000000 
0000022c 00000000 
00000230 00000000 
00000234 00000000 
00000238 00000000 
0000023c 00000000 
00002140 00000000   1539 reportAccessResultsBuf:	.space	8192

00002144 00000000 
00002148 00000000 
0000214c 00000000 
00002150 00000000 
00002154 00000000 
00002158 00000000 
0000215c 00000000 
00002160 00000000 
00002164 00000000 
00002168 00000000 
0000216c 00000000 
00002170 00000000 
00002174 00000000 
00002178 00000000 
0000217c 00000000 
00002180 00000000 
00002184 00000000 
00002188 00000000 
0000218c 00000000 
00002190 00000000 
00002194 00000000 
00002198 00000000 
0000219c 00000000 
000021a0 00000000 
000021a4 00000000 
000021a8 00000000 
000021ac 00000000 
000021b0 00000000 
000021b4 00000000 
000021b8 00000000 
000021bc 00000000 
000021c0 00000000 

                                                                      Page 29
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
000021c4 00000000 
000021c8 00000000 
000021cc 00000000 
000021d0 00000000 
000021d4 00000000 
000021d8 00000000 
000021dc 00000000 
000021e0 00000000 
000021e4 00000000 
000021e8 00000000 
000021ec 00000000 
000021f0 00000000 
000021f4 00000000 
000021f8 00000000 
000021fc 00000000 
00002200 00000000 
00002204 00000000 
00002208 00000000 
0000220c 00000000 
00002210 00000000 
00002214 00000000 
00002218 00000000 
0000221c 00000000 
00002220 00000000 
00002224 00000000 
00002228 00000000 
0000222c 00000000 
00002230 00000000 
00002234 00000000 
00002238 00000000 
0000223c 00000000 
00004140 00000000   1540 reportMmsBuf:	.space	8192

00004144 00000000 
00004148 00000000 
0000414c 00000000 
00004150 00000000 
00004154 00000000 
00004158 00000000 
0000415c 00000000 
00004160 00000000 
00004164 00000000 
00004168 00000000 
0000416c 00000000 
00004170 00000000 
00004174 00000000 
00004178 00000000 
0000417c 00000000 
00004180 00000000 
00004184 00000000 
00004188 00000000 
0000418c 00000000 
00004190 00000000 
00004194 00000000 
00004198 00000000 
0000419c 00000000 
000041a0 00000000 
000041a4 00000000 
000041a8 00000000 
000041ac 00000000 
000041b0 00000000 
000041b4 00000000 

                                                                      Page 30
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
000041b8 00000000 
000041bc 00000000 
000041c0 00000000 
000041c4 00000000 
000041c8 00000000 
000041cc 00000000 
000041d0 00000000 
000041d4 00000000 
000041d8 00000000 
000041dc 00000000 
000041e0 00000000 
000041e4 00000000 
000041e8 00000000 
000041ec 00000000 
000041f0 00000000 
000041f4 00000000 
000041f8 00000000 
000041fc 00000000 
00004200 00000000 
00004204 00000000 
00004208 00000000 
0000420c 00000000 
00004210 00000000 
00004214 00000000 
00004218 00000000 
0000421c 00000000 
00004220 00000000 
00004224 00000000 
00004228 00000000 
0000422c 00000000 
00004230 00000000 
00004234 00000000 
00004238 00000000 
0000423c 00000000 
00006140 00000000   1541 reportPresentationBuf:	.space	8192

00006144 00000000 
00006148 00000000 
0000614c 00000000 
00006150 00000000 
00006154 00000000 
00006158 00000000 
0000615c 00000000 
00006160 00000000 
00006164 00000000 
00006168 00000000 
0000616c 00000000 
00006170 00000000 
00006174 00000000 
00006178 00000000 
0000617c 00000000 
00006180 00000000 
00006184 00000000 
00006188 00000000 
0000618c 00000000 
00006190 00000000 
00006194 00000000 
00006198 00000000 
0000619c 00000000 
000061a0 00000000 
000061a4 00000000 
000061a8 00000000 

                                                                      Page 31
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
000061ac 00000000 
000061b0 00000000 
000061b4 00000000 
000061b8 00000000 
000061bc 00000000 
000061c0 00000000 
000061c4 00000000 
000061c8 00000000 
000061cc 00000000 
000061d0 00000000 
000061d4 00000000 
000061d8 00000000 
000061dc 00000000 
000061e0 00000000 
000061e4 00000000 
000061e8 00000000 
000061ec 00000000 
000061f0 00000000 
000061f4 00000000 
000061f8 00000000 
000061fc 00000000 
00006200 00000000 
00006204 00000000 
00006208 00000000 
0000620c 00000000 
00006210 00000000 
00006214 00000000 
00006218 00000000 
0000621c 00000000 
00006220 00000000 
00006224 00000000 
00006228 00000000 
0000622c 00000000 
00006230 00000000 
00006234 00000000 
00006238 00000000 
0000623c 00000000 
                    1542 	.data

                    1543 	.text

                    1544 

                    1545 

                    1546 	.align	4

                    1547 	.align	4

                    1548 sendReport:

0000088c e92d4ce2   1549 	stmfd	[sp]!,{r1,r5-r7,r10-fp,lr}

00000890 e1b06000   1550 	movs	r6,r0

00000894 e1a05002   1551 	mov	r5,r2

00000898 e1a07003   1552 	mov	r7,r3

0000089c e59fb510*  1553 	ldr	fp,.L2656

000008a0 0a000030   1554 	beq	.L2606

000008a4 e1a00005   1555 	mov	r0,r5

000008a8 eb000000*  1556 	bl	BerEncoder_determineLengthSize

000008ac e0850000   1557 	add	r0,r5,r0

000008b0 e280a008   1558 	add	r10,r0,8

000008b4 e1a0000a   1559 	mov	r0,r10

000008b8 eb000000*  1560 	bl	BerEncoder_determineLengthSize

000008bc e1a0200b   1561 	mov	r2,fp

000008c0 e3a03000   1562 	mov	r3,0

000008c4 e08a0000   1563 	add	r0,r10,r0

000008c8 e2801001   1564 	add	r1,r0,1

000008cc e3a000a3   1565 	mov	r0,163


                                                                      Page 32
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
000008d0 eb000000*  1566 	bl	BerEncoder_encodeTL

000008d4 e1a0200b   1567 	mov	r2,fp

000008d8 e1a0100a   1568 	mov	r1,r10

000008dc e1a03000   1569 	mov	r3,r0

000008e0 e3a000a0   1570 	mov	r0,160

000008e4 eb000000*  1571 	bl	BerEncoder_encodeTL

000008e8 e1a0a000   1572 	mov	r10,r0

000008ec e59f14c4*  1573 	ldr	r1,.L2657

000008f0 e08a000b   1574 	add	r0,r10,fp

000008f4 e3a02007   1575 	mov	r2,7

000008f8 eb000000*  1576 	bl	memcpy

000008fc e28a3007   1577 	add	r3,r10,7

00000900 e1a0200b   1578 	mov	r2,fp

00000904 e1a01005   1579 	mov	r1,r5

00000908 e3a000a0   1580 	mov	r0,160

0000090c eb000000*  1581 	bl	BerEncoder_encodeTL

00000910 e1a02005   1582 	mov	r2,r5

00000914 e1a0a000   1583 	mov	r10,r0

00000918 e59d1000   1584 	ldr	r1,[sp]

0000091c e08a000b   1585 	add	r0,r10,fp

00000920 eb000000*  1586 	bl	memcpy

00000924 e08a3005   1587 	add	r3,r10,r5

00000928 e59f548c*  1588 	ldr	r5,.L2658

0000092c e1a0200b   1589 	mov	r2,fp

00000930 e1a01005   1590 	mov	r1,r5

00000934 e2860edb   1591 	add	r0,r6,0x0db0

00000938 e2800bf0   1592 	add	r0,r0,15<<14

0000093c eb000000*  1593 	bl	IsoPresentation_createUserData

00000940 e1a02005   1594 	mov	r2,r5

00000944 e1a03000   1595 	mov	r3,r0

00000948 e2870008   1596 	add	r0,r7,8

0000094c e3a01c60   1597 	mov	r1,3<<13

00000950 eb000000*  1598 	bl	isoSession_createDataSpdu

00000954 e1a01007   1599 	mov	r1,r7

00000958 e5870004   1600 	str	r0,[r7,4]

0000095c e2860bf2   1601 	add	r0,r6,242<<10

00000960 e280006c   1602 	add	r0,r0,108

00000964 eb000000*  1603 	bl	OutQueue_insert

                    1604 .L2606:

00000968 e8bd4ce2   1605 	ldmfd	[sp]!,{r1,r5-r7,r10-fp,lr}

0000096c e12fff1e*  1606 	ret	

                    1607 	.endf	sendReport

                    1608 	.align	4

                    1609 ;bufPos	r10	local

                    1610 ;reportVarLen	r10	local

                    1611 

                    1612 ;isoConn	r6	param

                    1613 ;mmsReport	[sp]	param

                    1614 ;byteCount	r5	param

                    1615 ;sessionOutBuf	r7	param

                    1616 

                    1617 	.data

                    1618 	.text

                    1619 

                    1620 

                    1621 	.align	4

                    1622 	.align	4

                    1623 writeReportToBuffer:

00000970 e92d4000   1624 	stmfd	[sp]!,{lr}

00000974 e2800040   1625 	add	r0,r0,64

00000978 eb000000*  1626 	bl	ReportQueue_write


                                                                      Page 33
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
0000097c e8bd4000   1627 	ldmfd	[sp]!,{lr}

00000980 e12fff1e*  1628 	ret	

                    1629 	.endf	writeReportToBuffer

                    1630 	.align	4

                    1631 

                    1632 ;pRCB	r0	param

                    1633 ;pData	none	param

                    1634 ;dataLen	none	param

                    1635 

                    1636 	.section ".bss","awb"

                    1637 .L2686:

                    1638 	.data

                    1639 	.text

                    1640 

                    1641 

                    1642 	.align	4

                    1643 	.align	4

                    1644 allocateBufAndSendReport:

00000984 e92d4070   1645 	stmfd	[sp]!,{r4-r6,lr}

00000988 e1a04001   1646 	mov	r4,r1

0000098c e590603c   1647 	ldr	r6,[r0,60]

00000990 e1a05002   1648 	mov	r5,r2

00000994 e3560000   1649 	cmp	r6,0

00000998 0a000008   1650 	beq	.L2693

0000099c e2860e80   1651 	add	r0,r6,1<<11

000009a0 e2800018   1652 	add	r0,r0,24

000009a4 e3a01c60   1653 	mov	r1,3<<13

000009a8 eb000000*  1654 	bl	allocSessionOutBuffer

000009ac e1b03000   1655 	movs	r3,r0

000009b0 11a02005   1656 	movne	r2,r5

000009b4 11a01004   1657 	movne	r1,r4

000009b8 11a00006   1658 	movne	r0,r6

000009bc 1bffffb2*  1659 	blne	sendReport

                    1660 .L2693:

000009c0 e8bd4070   1661 	ldmfd	[sp]!,{r4-r6,lr}

000009c4 e12fff1e*  1662 	ret	

                    1663 	.endf	allocateBufAndSendReport

                    1664 	.align	4

                    1665 ;outBuf	r3	local

                    1666 ;pConn	r6	local

                    1667 

                    1668 ;pRCB	r0	param

                    1669 ;reportData	r4	param

                    1670 ;reportDataSize	r5	param

                    1671 

                    1672 	.section ".bss","awb"

                    1673 .L2735:

                    1674 	.data

                    1675 	.text

                    1676 

                    1677 

                    1678 	.align	4

                    1679 	.align	4

                    1680 integrityTimerProc:

000009c8 e92d4ff0   1681 	stmfd	[sp]!,{r4-fp,lr}

000009cc e24dd010   1682 	sub	sp,sp,16

000009d0 e3a00000   1683 	mov	r0,0

000009d4 e59f13b8*  1684 	ldr	r1,.L649

000009d8 e58d0008   1685 	str	r0,[sp,8]

000009dc e5910000   1686 	ldr	r0,[r1]

000009e0 e3500000   1687 	cmp	r0,0


                                                                      Page 34
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
000009e4 b3a00000   1688 	movlt	r0,0

000009e8 e58d000c   1689 	str	r0,[sp,12]

000009ec e1b0c1a0   1690 	movs	r12,r0 lsr 3

000009f0 0a0000c0   1691 	beq	.L2790

000009f4 e59f03c4*  1692 	ldr	r0,.L3446

000009f8 e59f4398*  1693 	ldr	r4,.L650

000009fc e3a02001   1694 	mov	r2,1

00000a00 e0849000   1695 	add	r9,r4,r0

00000a04 e2840f96   1696 	add	r0,r4,0x0258

00000a08 e2805a87   1697 	add	r5,r0,135<<12

00000a0c e59f03b0*  1698 	ldr	r0,.L3447

00000a10 e3a03000   1699 	mov	r3,0

00000a14 e0841000   1700 	add	r1,r4,r0

00000a18 e2840f64   1701 	add	r0,r4,0x0190

00000a1c e2806a5a   1702 	add	r6,r0,90<<12

00000a20 e59f03a0*  1703 	ldr	r0,.L3448

00000a24 e1a0818c   1704 	mov	r8,r12 lsl 3

00000a28 e0847000   1705 	add	r7,r4,r0

00000a2c e2840bb4   1706 	add	r0,r4,45<<12

00000a30 e280a0c8   1707 	add	r10,r0,200

00000a34 e2840b5a   1708 	add	r0,r4,90<<10

00000a38 e280b064   1709 	add	fp,r0,100

00000a3c e98d0102   1710 	stmfa	[sp],{r1,r8}

                    1711 .L2791:

00000a40 e1a00004   1712 	mov	r0,r4

00000a44 e1a01000   1713 	mov	r1,r0

00000a48 e5d1e01a   1714 	ldrb	lr,[r1,26]

00000a4c e31e0002   1715 	tst	lr,2

00000a50 1591e01c   1716 	ldrne	lr,[r1,28]

00000a54 135e0000   1717 	cmpne	lr,0

00000a58 0a00000c   1718 	beq	.L2798

00000a5c e590102c   1719 	ldr	r1,[r0,44]

00000a60 e151000e   1720 	cmp	r1,lr

00000a64 2580302c   1721 	strhs	r3,[r0,44]

00000a68 25c02030   1722 	strhsb	r2,[r0,48]

00000a6c 2a000007   1723 	bhs	.L2798

00000a70 e2811001   1724 	add	r1,r1,1

00000a74 e580102c   1725 	str	r1,[r0,44]

00000a78 e1a0000b   1726 	mov	r0,fp

00000a7c e1a01000   1727 	mov	r1,r0

00000a80 e5d1e01a   1728 	ldrb	lr,[r1,26]

00000a84 e31e0002   1729 	tst	lr,2

00000a88 1a000005   1730 	bne	.L2799

00000a8c ea000014   1731 	b	.L2805

                    1732 .L2798:

00000a90 e1a0000b   1733 	mov	r0,fp

00000a94 e1a01000   1734 	mov	r1,r0

00000a98 e5d1e01a   1735 	ldrb	lr,[r1,26]

00000a9c e31e0002   1736 	tst	lr,2

00000aa0 0a00000f   1737 	beq	.L2805

                    1738 .L2799:

00000aa4 e591e01c   1739 	ldr	lr,[r1,28]

00000aa8 e35e0000   1740 	cmp	lr,0

00000aac 0a00000c   1741 	beq	.L2805

00000ab0 e590102c   1742 	ldr	r1,[r0,44]

00000ab4 e151000e   1743 	cmp	r1,lr

00000ab8 2580302c   1744 	strhs	r3,[r0,44]

00000abc 25c02030   1745 	strhsb	r2,[r0,48]

00000ac0 2a000007   1746 	bhs	.L2805

00000ac4 e2811001   1747 	add	r1,r1,1

00000ac8 e580102c   1748 	str	r1,[r0,44]


                                                                      Page 35
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
00000acc e1a0000a   1749 	mov	r0,r10

00000ad0 e1a01000   1750 	mov	r1,r0

00000ad4 e5d1e01a   1751 	ldrb	lr,[r1,26]

00000ad8 e31e0002   1752 	tst	lr,2

00000adc 1a000005   1753 	bne	.L2806

00000ae0 ea000014   1754 	b	.L2812

                    1755 .L2805:

00000ae4 e1a0000a   1756 	mov	r0,r10

00000ae8 e1a01000   1757 	mov	r1,r0

00000aec e5d1e01a   1758 	ldrb	lr,[r1,26]

00000af0 e31e0002   1759 	tst	lr,2

00000af4 0a00000f   1760 	beq	.L2812

                    1761 .L2806:

00000af8 e591e01c   1762 	ldr	lr,[r1,28]

00000afc e35e0000   1763 	cmp	lr,0

00000b00 0a00000c   1764 	beq	.L2812

00000b04 e590102c   1765 	ldr	r1,[r0,44]

00000b08 e151000e   1766 	cmp	r1,lr

00000b0c 2580302c   1767 	strhs	r3,[r0,44]

00000b10 25c02030   1768 	strhsb	r2,[r0,48]

00000b14 2a000007   1769 	bhs	.L2812

00000b18 e2811001   1770 	add	r1,r1,1

00000b1c e580102c   1771 	str	r1,[r0,44]

00000b20 e1a00007   1772 	mov	r0,r7

00000b24 e1a01000   1773 	mov	r1,r0

00000b28 e5d1e01a   1774 	ldrb	lr,[r1,26]

00000b2c e31e0002   1775 	tst	lr,2

00000b30 1a000005   1776 	bne	.L2813

00000b34 ea000014   1777 	b	.L2819

                    1778 .L2812:

00000b38 e1a00007   1779 	mov	r0,r7

00000b3c e1a01000   1780 	mov	r1,r0

00000b40 e5d1e01a   1781 	ldrb	lr,[r1,26]

00000b44 e31e0002   1782 	tst	lr,2

00000b48 0a00000f   1783 	beq	.L2819

                    1784 .L2813:

00000b4c e591e01c   1785 	ldr	lr,[r1,28]

00000b50 e35e0000   1786 	cmp	lr,0

00000b54 0a00000c   1787 	beq	.L2819

00000b58 e590102c   1788 	ldr	r1,[r0,44]

00000b5c e151000e   1789 	cmp	r1,lr

00000b60 2580302c   1790 	strhs	r3,[r0,44]

00000b64 25c02030   1791 	strhsb	r2,[r0,48]

00000b68 2a000007   1792 	bhs	.L2819

00000b6c e2811001   1793 	add	r1,r1,1

00000b70 e580102c   1794 	str	r1,[r0,44]

00000b74 e1a00006   1795 	mov	r0,r6

00000b78 e1a01000   1796 	mov	r1,r0

00000b7c e5d1e01a   1797 	ldrb	lr,[r1,26]

00000b80 e31e0002   1798 	tst	lr,2

00000b84 1a000005   1799 	bne	.L2820

00000b88 ea000014   1800 	b	.L2826

                    1801 .L2819:

00000b8c e1a00006   1802 	mov	r0,r6

00000b90 e1a01000   1803 	mov	r1,r0

00000b94 e5d1e01a   1804 	ldrb	lr,[r1,26]

00000b98 e31e0002   1805 	tst	lr,2

00000b9c 0a00000f   1806 	beq	.L2826

                    1807 .L2820:

00000ba0 e591e01c   1808 	ldr	lr,[r1,28]

00000ba4 e35e0000   1809 	cmp	lr,0


                                                                      Page 36
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
00000ba8 0a00000c   1810 	beq	.L2826

00000bac e590102c   1811 	ldr	r1,[r0,44]

00000bb0 e151000e   1812 	cmp	r1,lr

00000bb4 2580302c   1813 	strhs	r3,[r0,44]

00000bb8 25c02030   1814 	strhsb	r2,[r0,48]

00000bbc 2a000007   1815 	bhs	.L2826

00000bc0 e2811001   1816 	add	r1,r1,1

00000bc4 e580102c   1817 	str	r1,[r0,44]

00000bc8 e59d0004   1818 	ldr	r0,[sp,4]

00000bcc e1a01000   1819 	mov	r1,r0

00000bd0 e5d1e01a   1820 	ldrb	lr,[r1,26]

00000bd4 e31e0002   1821 	tst	lr,2

00000bd8 1a000005   1822 	bne	.L2827

00000bdc ea000014   1823 	b	.L2833

                    1824 .L2826:

00000be0 e59d0004   1825 	ldr	r0,[sp,4]

00000be4 e1a01000   1826 	mov	r1,r0

00000be8 e5d1e01a   1827 	ldrb	lr,[r1,26]

00000bec e31e0002   1828 	tst	lr,2

00000bf0 0a00000f   1829 	beq	.L2833

                    1830 .L2827:

00000bf4 e591e01c   1831 	ldr	lr,[r1,28]

00000bf8 e35e0000   1832 	cmp	lr,0

00000bfc 0a00000c   1833 	beq	.L2833

00000c00 e590102c   1834 	ldr	r1,[r0,44]

00000c04 e151000e   1835 	cmp	r1,lr

00000c08 2580302c   1836 	strhs	r3,[r0,44]

00000c0c 25c02030   1837 	strhsb	r2,[r0,48]

00000c10 2a000007   1838 	bhs	.L2833

00000c14 e2811001   1839 	add	r1,r1,1

00000c18 e580102c   1840 	str	r1,[r0,44]

00000c1c e1a00005   1841 	mov	r0,r5

00000c20 e1a01000   1842 	mov	r1,r0

00000c24 e5d1e01a   1843 	ldrb	lr,[r1,26]

00000c28 e31e0002   1844 	tst	lr,2

00000c2c 1a000005   1845 	bne	.L2834

00000c30 ea000014   1846 	b	.L2840

                    1847 .L2833:

00000c34 e1a00005   1848 	mov	r0,r5

00000c38 e1a01000   1849 	mov	r1,r0

00000c3c e5d1e01a   1850 	ldrb	lr,[r1,26]

00000c40 e31e0002   1851 	tst	lr,2

00000c44 0a00000f   1852 	beq	.L2840

                    1853 .L2834:

00000c48 e591e01c   1854 	ldr	lr,[r1,28]

00000c4c e35e0000   1855 	cmp	lr,0

00000c50 0a00000c   1856 	beq	.L2840

00000c54 e590102c   1857 	ldr	r1,[r0,44]

00000c58 e151000e   1858 	cmp	r1,lr

00000c5c 2580302c   1859 	strhs	r3,[r0,44]

00000c60 25c02030   1860 	strhsb	r2,[r0,48]

00000c64 2a000007   1861 	bhs	.L2840

00000c68 e2811001   1862 	add	r1,r1,1

00000c6c e580102c   1863 	str	r1,[r0,44]

00000c70 e1a00009   1864 	mov	r0,r9

00000c74 e1a01000   1865 	mov	r1,r0

00000c78 e5d1e01a   1866 	ldrb	lr,[r1,26]

00000c7c e31e0002   1867 	tst	lr,2

00000c80 1a000005   1868 	bne	.L2841

00000c84 ea00000d   1869 	b	.L2846

                    1870 .L2840:


                                                                      Page 37
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
00000c88 e1a00009   1871 	mov	r0,r9

00000c8c e1a01000   1872 	mov	r1,r0

00000c90 e5d1e01a   1873 	ldrb	lr,[r1,26]

00000c94 e31e0002   1874 	tst	lr,2

00000c98 0a000008   1875 	beq	.L2846

                    1876 .L2841:

00000c9c e591e01c   1877 	ldr	lr,[r1,28]

00000ca0 e35e0000   1878 	cmp	lr,0

00000ca4 0a000005   1879 	beq	.L2846

00000ca8 e590102c   1880 	ldr	r1,[r0,44]

00000cac e151000e   1881 	cmp	r1,lr

00000cb0 32811001   1882 	addlo	r1,r1,1

00000cb4 3580102c   1883 	strlo	r1,[r0,44]

00000cb8 2580302c   1884 	strhs	r3,[r0,44]

00000cbc 25c02030   1885 	strhsb	r2,[r0,48]

                    1886 .L2846:

00000cc0 e3a00fc8   1887 	mov	r0,0x0320

00000cc4 e2800ab4   1888 	add	r0,r0,45<<14

00000cc8 e0899000   1889 	add	r9,r9,r0

00000ccc e59d8004   1890 	ldr	r8,[sp,4]

00000cd0 e0855000   1891 	add	r5,r5,r0

00000cd4 e0888000   1892 	add	r8,r8,r0

00000cd8 e58d8004   1893 	str	r8,[sp,4]

00000cdc e0866000   1894 	add	r6,r6,r0

00000ce0 e0877000   1895 	add	r7,r7,r0

00000ce4 e08aa000   1896 	add	r10,r10,r0

00000ce8 e08bb000   1897 	add	fp,fp,r0

00000cec e0844000   1898 	add	r4,r4,r0

00000cf0 e25cc001   1899 	subs	r12,r12,1

00000cf4 1affff51   1900 	bne	.L2791

                    1901 .L2790:

00000cf8 e59d000c   1902 	ldr	r0,[sp,12]

00000cfc e210c007   1903 	ands	r12,r0,7

00000d00 0a000020   1904 	beq	.L2749

00000d04 e59d2008   1905 	ldr	r2,[sp,8]

00000d08 e3a04b5a   1906 	mov	r4,90<<10

00000d0c e0820102   1907 	add	r0,r2,r2 lsl 2

00000d10 e1a01100   1908 	mov	r1,r0 lsl 2

00000d14 e0800001   1909 	add	r0,r0,r1

00000d18 e0800381   1910 	add	r0,r0,r1 lsl 7

00000d1c e0800501   1911 	add	r0,r0,r1 lsl 10

00000d20 e59f1070*  1912 	ldr	r1,.L650

00000d24 e2844064   1913 	add	r4,r4,100

00000d28 e0812100   1914 	add	r2,r1,r0 lsl 2

00000d2c e3a05001   1915 	mov	r5,1

00000d30 e3a06000   1916 	mov	r6,0

                    1917 .L2849:

00000d34 e1a00002   1918 	mov	r0,r2

00000d38 e1a01000   1919 	mov	r1,r0

00000d3c e5d1301a   1920 	ldrb	r3,[r1,26]

00000d40 e3130002   1921 	tst	r3,2

00000d44 1591e01c   1922 	ldrne	lr,[r1,28]

00000d48 135e0000   1923 	cmpne	lr,0

00000d4c 0a00000a   1924 	beq	.L2855

00000d50 e590102c   1925 	ldr	r1,[r0,44]

00000d54 e151000e   1926 	cmp	r1,lr

00000d58 2580602c   1927 	strhs	r6,[r0,44]

00000d5c 25c05030   1928 	strhsb	r5,[r0,48]

00000d60 2a000005   1929 	bhs	.L2855

00000d64 e2811001   1930 	add	r1,r1,1

00000d68 e580102c   1931 	str	r1,[r0,44]


                                                                      Page 38
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
00000d6c e0822004   1932 	add	r2,r2,r4

00000d70 e25cc001   1933 	subs	r12,r12,1

00000d74 1affffee   1934 	bne	.L2849

00000d78 ea000002   1935 	b	.L2749

                    1936 .L2855:

00000d7c e0822004   1937 	add	r2,r2,r4

00000d80 e25cc001   1938 	subs	r12,r12,1

00000d84 1affffea   1939 	bne	.L2849

                    1940 .L2749:

00000d88 e28dd010   1941 	add	sp,sp,16

00000d8c e8bd4ff0   1942 	ldmfd	[sp]!,{r4-fp,lr}

00000d90 e12fff1e*  1943 	ret	

                    1944 	.endf	integrityTimerProc

                    1945 	.align	4

                    1946 .L649:

00000d94 00000000*  1947 	.data.w	g_reportCount

                    1948 	.type	.L649,$object

                    1949 	.size	.L649,4

                    1950 

                    1951 .L650:

00000d98 00000000*  1952 	.data.w	g_reports

                    1953 	.type	.L650,$object

                    1954 	.size	.L650,4

                    1955 

                    1956 .L2600:

00000d9c 00000000*  1957 	.data.w	.L2334

                    1958 	.type	.L2600,$object

                    1959 	.size	.L2600,4

                    1960 

                    1961 .L2601:

00000da0 00000000*  1962 	.data.w	reportValuesBuf

                    1963 	.type	.L2601,$object

                    1964 	.size	.L2601,4

                    1965 

                    1966 .L2602:

00000da4 00000000*  1967 	.data.w	reportInclusionReasons

                    1968 	.type	.L2602,$object

                    1969 	.size	.L2602,4

                    1970 

                    1971 .L2603:

00000da8 00000000*  1972 	.data.w	reportAccessResultsBuf

                    1973 	.type	.L2603,$object

                    1974 	.size	.L2603,4

                    1975 

                    1976 .L2604:

00000dac db237c00   1977 	.data.w	0xdb237c00

                    1978 	.type	.L2604,$object

                    1979 	.size	.L2604,4

                    1980 

                    1981 .L2605:

00000db0 05265c00   1982 	.data.w	0x05265c00

                    1983 	.type	.L2605,$object

                    1984 	.size	.L2605,4

                    1985 

                    1986 .L2656:

00000db4 00000000*  1987 	.data.w	reportMmsBuf

                    1988 	.type	.L2656,$object

                    1989 	.size	.L2656,4

                    1990 

                    1991 .L2657:

00000db8 00000000*  1992 	.data.w	reportVarNameSequence


                                                                      Page 39
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    1993 	.type	.L2657,$object

                    1994 	.size	.L2657,4

                    1995 

                    1996 .L2658:

00000dbc 00000000*  1997 	.data.w	reportPresentationBuf

                    1998 	.type	.L2658,$object

                    1999 	.size	.L2658,4

                    2000 

                    2001 .L3446:

00000dc0 0009dabc   2002 	.data.w	0x0009dabc

                    2003 	.type	.L3446,$object

                    2004 	.size	.L3446,4

                    2005 

                    2006 .L3447:

00000dc4 000709f4   2007 	.data.w	0x000709f4

                    2008 	.type	.L3447,$object

                    2009 	.size	.L3447,4

                    2010 

                    2011 .L3448:

00000dc8 0004392c   2012 	.data.w	0x0004392c

                    2013 	.type	.L3448,$object

                    2014 	.size	.L3448,4

                    2015 

                    2016 	.align	4

                    2017 ;rcbIdx	[sp,8]	local

                    2018 ;pReporter	r0	local

                    2019 ;pRCB	r1	local

                    2020 

                    2021 	.section ".bss","awb"

                    2022 .L3296:

                    2023 	.data

                    2024 	.text

                    2025 

                    2026 

                    2027 	.align	4

                    2028 	.align	4

                    2029 processGI:

00000dcc e92d4010   2030 	stmfd	[sp]!,{r4,lr}

00000dd0 e1a04000   2031 	mov	r4,r0

00000dd4 e5d40022   2032 	ldrb	r0,[r4,34]

00000dd8 e3500000   2033 	cmp	r0,0

00000ddc 0a00000a   2034 	beq	.L3449

00000de0 e1a00004   2035 	mov	r0,r4

00000de4 e3a01000   2036 	mov	r1,0

00000de8 ebfffcb0*  2037 	bl	writeReport

00000dec e2502000   2038 	subs	r2,r0,0

00000df0 da000004   2039 	ble	.L3454

00000df4 e51f1054*  2040 	ldr	r1,.L2603

00000df8 e1a00004   2041 	mov	r0,r4

00000dfc ebfffee0*  2042 	bl	allocateBufAndSendReport

00000e00 e3a00000   2043 	mov	r0,0

00000e04 e5c40022   2044 	strb	r0,[r4,34]

                    2045 .L3454:

00000e08 e3a00001   2046 	mov	r0,1

                    2047 .L3449:

00000e0c e8bd4010   2048 	ldmfd	[sp]!,{r4,lr}

00000e10 e12fff1e*  2049 	ret	

                    2050 	.endf	processGI

                    2051 	.align	4

                    2052 ;reportDataSize	r2	local

                    2053 


                                                                      Page 40
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    2054 ;pReporter	r4	param

                    2055 

                    2056 	.data

                    2057 	.text

                    2058 

                    2059 

                    2060 ;718: 


                    2061 ;719: static void reportsThread(void* data)


                    2062 	.align	4

                    2063 	.align	4

                    2064 reportsThread:

00000e14 e92d48f0   2065 	stmfd	[sp]!,{r4-r7,fp,lr}

                    2066 ;720: {


                    2067 

00000e18 e24dd00c   2068 	sub	sp,sp,12

00000e1c e3a00000   2069 	mov	r0,0

00000e20 e1a01000   2070 	mov	r1,r0

00000e24 e98d0003   2071 	stmfa	[sp],{r0-r1}

                    2072 ;721:     unsigned long long timeStamp = 0;


                    2073 

                    2074 ;722:     unsigned long long newTimeStamp;    


                    2075 ;723: 	static bool newBusOK = true;


                    2076 ;724: 	static bool oldBusOK = true;


                    2077 ;725: 


                    2078 ;726: 	//Для цикла по отчётам


                    2079 ;727: 	size_t rptIdx;


                    2080 ;728: 	bool reportsProcessed;


                    2081 ;729:     while(1)


                    2082 

                    2083 .L3539:

                    2084 ;730:     {


                    2085 

                    2086 ;731: 		reportsProcessed = FALSE;


                    2087 

00000e28 e3a06000   2088 	mov	r6,0

                    2089 ;732:         


                    2090 ;733: 		newTimeStamp = getCurrentDataSliceTime();


                    2091 

00000e2c eb000000*  2092 	bl	getCurrentDataSliceTime

00000e30 e1a04000   2093 	mov	r4,r0

00000e34 e1a05001   2094 	mov	r5,r1

                    2095 ;734: 		newBusOK = BusError_check();


                    2096 

00000e38 eb000000*  2097 	bl	BusError_check

                    2098 ;735: 


                    2099 ;736: 		//Если изменилось время или возникла ошибка шины


                    2100 ;737: 		if (timeStamp != newTimeStamp || (!newBusOK && oldBusOK))


                    2101 

00000e3c e99d0006   2102 	ldmed	[sp],{r1-r2}

00000e40 e1520005   2103 	cmp	r2,r5

00000e44 01510004   2104 	cmpeq	r1,r4

00000e48 1a000005   2105 	bne	.L3541

00000e4c e3500000   2106 	cmp	r0,0

00000e50 1a000037   2107 	bne	.L3540

00000e54 e59f2280*  2108 	ldr	r2,.L4150

00000e58 e5d21000   2109 	ldrb	r1,[r2]

00000e5c e3510000   2110 	cmp	r1,0

00000e60 0a000033   2111 	beq	.L3540

                    2112 .L3541:

                    2113 ;738: 		{			


                    2114 


                                                                      Page 41
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    2115 ;739: 			oldBusOK = newBusOK;


                    2116 

00000e64 e59f1270*  2117 	ldr	r1,.L4150

00000e68 e5c10000   2118 	strb	r0,[r1]

                    2119 ;740: 			timeStamp = newTimeStamp;


                    2120 

00000e6c e98d0030   2121 	stmfa	[sp],{r4-r5}

                    2122 ;741:             // Изменилось время, значит могли измениться данные


                    2123 ;742: 			dataSliceCapture();


                    2124 

00000e70 eb000000*  2125 	bl	dataSliceCapture

                    2126 ;743: 


                    2127 ;744: 			//Устанавливам/снимаем флаги изменения в DA, содержащих


                    2128 ;745: 			//данные из DataSlice


                    2129 ;746: 


                    2130 ;747: 


                    2131 ;748: 			IEDTree_lock();


                    2132 

00000e74 eb000000*  2133 	bl	IEDTree_lock

                    2134 ;749: 


                    2135 ;750: 			IEDTree_updateFromDataSlice();


                    2136 

00000e78 eb000000*  2137 	bl	IEDTree_updateFromDataSlice

                    2138 ;751: 


                    2139 ;752:             // Здесь, при наличии изменений, происходит отправка небуферизированных


                    2140 ;753:             // отчётов или помещение в буфер буферизированных			


                    2141 ;754: 			processAllReportsData();			


                    2142 

                    2143 ;600: {	


                    2144 

                    2145 ;601: 	size_t rcbIdx;


                    2146 ;602: 	int reportDataSize;


                    2147 ;603: 	 


                    2148 ;604: 	for(rcbIdx = 0; rcbIdx < g_reportCount; ++rcbIdx)


                    2149 

00000e7c e51fb0f0*  2150 	ldr	fp,.L649

00000e80 e51f70f0*  2151 	ldr	r7,.L650

00000e84 e59b0000   2152 	ldr	r0,[fp]

00000e88 e3a06000   2153 	mov	r6,0

00000e8c e1560000   2154 	cmp	r6,r0

00000e90 2a000023   2155 	bhs	.L3544

                    2156 .L3547:

                    2157 ;605: 	{


                    2158 

                    2159 ;606: 		Reporter* pReporter = g_reports + rcbIdx;


                    2160 

00000e94 e1a04007   2161 	mov	r4,r7

                    2162 ;607: 		RCB* pRCB = &pReporter->rcb;


                    2163 

00000e98 e1a05004   2164 	mov	r5,r4

                    2165 ;608: 


                    2166 ;609: 		if ((pRCB->rptEna && isRCBConnected(pReporter))


                    2167 

00000e9c e5d50001   2168 	ldrb	r0,[r5,1]

00000ea0 e3500000   2169 	cmp	r0,0

00000ea4 0a000002   2170 	beq	.L3549

00000ea8 e1a00004   2171 	mov	r0,r4

00000eac ebfffc6f*  2172 	bl	isRCBConnected

00000eb0 e3500000   2173 	cmp	r0,0

                    2174 .L3549:

00000eb4 05d50000   2175 	ldreqb	r0,[r5]


                                                                      Page 42
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
00000eb8 03500000   2176 	cmpeq	r0,0

00000ebc 0a000012   2177 	beq	.L3554

                    2178 .L3550:

                    2179 ;610: 			|| pRCB->buffered)


                    2180 ;611: 		{			


                    2181 

                    2182 ;612: 			//Готовим отчёт для буферизированного или небуферизированного


                    2183 ;613: 			reportDataSize = writeReport(pReporter, RPT_CMP);


                    2184 

00000ec0 e1a00004   2185 	mov	r0,r4

00000ec4 e3a01003   2186 	mov	r1,3

00000ec8 ebfffc78*  2187 	bl	writeReport

00000ecc e2502000   2188 	subs	r2,r0,0

                    2189 ;614: 			if (reportDataSize > 0)


                    2190 

00000ed0 da00000d   2191 	ble	.L3554

                    2192 ;615: 			{


                    2193 

                    2194 ;616: 				if (pRCB->buffered)


                    2195 

00000ed4 e51f1134*  2196 	ldr	r1,.L2603

00000ed8 e5d53000   2197 	ldrb	r3,[r5]

00000edc e1a00004   2198 	mov	r0,r4

00000ee0 e3530000   2199 	cmp	r3,0

00000ee4 0a000007   2200 	beq	.L3553

                    2201 ;617: 				{


                    2202 

                    2203 ;618: 					//Пишем в буфер буферизированного отчёта


                    2204 ;619: 					writeReportToBuffer(pReporter, reportAccessResultsBuf,


                    2205 

00000ee8 ebfffea0*  2206 	bl	writeReportToBuffer

00000eec e2870b5a   2207 	add	r0,r7,90<<10

00000ef0 e2807064   2208 	add	r7,r0,100

00000ef4 e59b0000   2209 	ldr	r0,[fp]

00000ef8 e2866001   2210 	add	r6,r6,1

00000efc e1560000   2211 	cmp	r6,r0

00000f00 3affffe3   2212 	blo	.L3547

00000f04 ea000006   2213 	b	.L3544

                    2214 .L3553:

                    2215 ;620: 						reportDataSize);


                    2216 ;621: 				}


                    2217 ;622: 				else


                    2218 ;623: 				{


                    2219 

                    2220 ;624: 					allocateBufAndSendReport(pReporter, reportAccessResultsBuf,


                    2221 

00000f08 ebfffe9d*  2222 	bl	allocateBufAndSendReport

                    2223 .L3554:

00000f0c e2870b5a   2224 	add	r0,r7,90<<10

00000f10 e2807064   2225 	add	r7,r0,100

00000f14 e59b0000   2226 	ldr	r0,[fp]

00000f18 e2866001   2227 	add	r6,r6,1

00000f1c e1560000   2228 	cmp	r6,r0

00000f20 3affffdb   2229 	blo	.L3547

                    2230 .L3544:

                    2231 ;755: 			IEDTree_unlock();


                    2232 

00000f24 eb000000*  2233 	bl	IEDTree_unlock

                    2234 ;756: 


                    2235 ;757:             Control_processCtrlObjects();


                    2236 


                                                                      Page 43
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
00000f28 eb000000*  2237 	bl	Control_processCtrlObjects

                    2238 ;758: 			dataSliceRelease();


                    2239 

00000f2c eb000000*  2240 	bl	dataSliceRelease

                    2241 ;759: 			reportsProcessed = TRUE;


                    2242 

00000f30 e3a06001   2243 	mov	r6,1

                    2244 .L3540:

                    2245 ;760: 		} 


                    2246 ;761: 		


                    2247 ;762: 		//Отправка Intergrity


                    2248 ;763: 		for (rptIdx = 0; rptIdx < g_reportCount; rptIdx++)


                    2249 

00000f34 e51f11a8*  2250 	ldr	r1,.L649

00000f38 e51f71a8*  2251 	ldr	r7,.L650

00000f3c e5910000   2252 	ldr	r0,[r1]

00000f40 e3a05000   2253 	mov	r5,0

00000f44 e1550000   2254 	cmp	r5,r0

00000f48 2a00002c   2255 	bhs	.L3557

                    2256 .L3559:

                    2257 ;764: 		{


                    2258 

                    2259 ;765: 			Reporter* pReporter = g_reports + rptIdx;			


                    2260 

00000f4c e1a04007   2261 	mov	r4,r7

                    2262 ;766: 			if (processIntegrity(pReporter))


                    2263 

                    2264 ;683: {


                    2265 

00000f50 e594101c   2266 	ldr	r1,[r4,28]

00000f54 e3510000   2267 	cmp	r1,0

00000f58 15d41030   2268 	ldrneb	r1,[r4,48]

00000f5c e3a0b000   2269 	mov	fp,0

                    2270 ;684: 	int reportDataSize;


                    2271 ;685:     bool sent = false;


                    2272 

                    2273 ;686: 	


                    2274 ;687: 	if (pReporter->rcb.intgPd == 0 || !pReporter->intgTimerAlam)


                    2275 

00000f60 13510000   2276 	cmpne	r1,0

00000f64 0a00001e   2277 	beq	.L3558

                    2278 ;688: 	{


                    2279 

                    2280 ;689:         return sent;


                    2281 

                    2282 ;690: 	}


                    2283 ;691: 		


                    2284 ;692:     if (pReporter->rcb.buffered)


                    2285 

00000f68 e5d41000   2286 	ldrb	r1,[r4]

00000f6c e3510000   2287 	cmp	r1,0

00000f70 0a00000b   2288 	beq	.L3571

                    2289 ;693:     {


                    2290 

                    2291 ;694:         reportDataSize = writeReport(pReporter, RPT_INTG);


                    2292 

00000f74 e1a00004   2293 	mov	r0,r4

00000f78 e3a01001   2294 	mov	r1,1

00000f7c ebfffc4b*  2295 	bl	writeReport

00000f80 e2502000   2296 	subs	r2,r0,0

                    2297 ;695:         if (reportDataSize > 0)



                                                                      Page 44
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    2298 

00000f84 da000012   2299 	ble	.L3972

                    2300 ;696:         {


                    2301 

                    2302 ;697:             //Пишем в буфер буферизированного отчёта


                    2303 ;698:             writeReportToBuffer(pReporter, reportAccessResultsBuf,


                    2304 

00000f88 e51f11e8*  2305 	ldr	r1,.L2603

00000f8c e1a00004   2306 	mov	r0,r4

00000f90 ebfffe76*  2307 	bl	writeReportToBuffer

                    2308 ;699:                 reportDataSize);


                    2309 ;700:             sent = true;


                    2310 

00000f94 e51f1208*  2311 	ldr	r1,.L649

00000f98 e3a0b001   2312 	mov	fp,1

00000f9c e5910000   2313 	ldr	r0,[r1]

00000fa0 ea00000d   2314 	b	.L3574

                    2315 .L3571:

                    2316 ;701:         }


                    2317 ;702:     }


                    2318 ;703:     else if(pReporter->rcb.rptEna)


                    2319 

00000fa4 e5d41001   2320 	ldrb	r1,[r4,1]

00000fa8 e3510000   2321 	cmp	r1,0

00000fac 0a00000a   2322 	beq	.L3574

                    2323 ;704:     {


                    2324 

                    2325 ;705:         reportDataSize = writeReport(pReporter, RPT_INTG);


                    2326 

00000fb0 e1a00004   2327 	mov	r0,r4

00000fb4 e3a01001   2328 	mov	r1,1

00000fb8 ebfffc3c*  2329 	bl	writeReport

00000fbc e2502000   2330 	subs	r2,r0,0

                    2331 ;706:         if (reportDataSize > 0)


                    2332 

00000fc0 da000003   2333 	ble	.L3972

                    2334 ;707:         {


                    2335 

                    2336 ;708:             allocateBufAndSendReport(pReporter, reportAccessResultsBuf,


                    2337 

00000fc4 e51f1224*  2338 	ldr	r1,.L2603

00000fc8 e1a00004   2339 	mov	r0,r4

00000fcc ebfffe6c*  2340 	bl	allocateBufAndSendReport

                    2341 ;709:                 reportDataSize);


                    2342 ;710:             sent = true;


                    2343 

00000fd0 e3a0b001   2344 	mov	fp,1

                    2345 .L3972:

00000fd4 e51f1248*  2346 	ldr	r1,.L649

00000fd8 e5910000   2347 	ldr	r0,[r1]

                    2348 .L3574:

                    2349 ;711:         }


                    2350 ;712:     }


                    2351 ;713: 


                    2352 ;714:     pReporter->intgTimerAlam = false;


                    2353 

00000fdc e3a01000   2354 	mov	r1,0

00000fe0 e5c41030   2355 	strb	r1,[r4,48]

                    2356 ;715: 


                    2357 ;716:     return sent;


                    2358 


                                                                      Page 45
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    2359 .L3558:

00000fe4 e2871b5a   2360 	add	r1,r7,90<<10

00000fe8 e2817064   2361 	add	r7,r1,100

00000fec e35b0000   2362 	cmp	fp,0

00000ff0 13a06001   2363 	movne	r6,1

                    2364 

                    2365 

00000ff4 e2855001   2366 	add	r5,r5,1

00000ff8 e1550000   2367 	cmp	r5,r0

00000ffc 3affffd2   2368 	blo	.L3559

                    2369 .L3557:

                    2370 ;769: 			}


                    2371 ;770: 		}


                    2372 ;771: 


                    2373 ;772:         // В этом цикле отправляются буфера буфериризированных отчётов


                    2374 ;773:         // и General Interrogation.


                    2375 ;774:         // Для небуферизированных отчётов GI просто посылается.


                    2376 ;775:         // Для буферизированных GI посылается только если буфер пуст.


                    2377 ;776: 		for (rptIdx = 0; rptIdx < g_reportCount; rptIdx++)


                    2378 

00001000 e51f0274*  2379 	ldr	r0,.L649

00001004 e51fb274*  2380 	ldr	fp,.L650

00001008 e5901000   2381 	ldr	r1,[r0]

0000100c e3a05000   2382 	mov	r5,0

00001010 e1550001   2383 	cmp	r5,r1

00001014 2a00002d   2384 	bhs	.L3576

                    2385 .L3578:

                    2386 ;777: 		{


                    2387 

                    2388 ;778: 			Reporter* pReporter = g_reports + rptIdx;


                    2389 

00001018 e1a0400b   2390 	mov	r4,fp

                    2391 ;779: 			RCB* pRCB = &pReporter->rcb;


                    2392 

0000101c e1a00004   2393 	mov	r0,r4

                    2394 ;780: 			if (!pRCB->rptEna)


                    2395 

00001020 e5d02001   2396 	ldrb	r2,[r0,1]

00001024 e3520000   2397 	cmp	r2,0

00001028 0a000023   2398 	beq	.L3577

                    2399 ;781: 			{


                    2400 

                    2401 ;782: 				continue;


                    2402 

                    2403 ;783: 			}


                    2404 ;784: 			if (pRCB->buffered)


                    2405 

0000102c e5d00000   2406 	ldrb	r0,[r0]

00001030 e3500000   2407 	cmp	r0,0

00001034 0a00001b   2408 	beq	.L3584

                    2409 ;785: 			{


                    2410 

                    2411 ;786: 				//Для буферизированных отчётов посылается либо содержимое буфера


                    2412 ;787: 				//либо, если буфер пустой, можно послать GI


                    2413 ;788: 				if (bufferedReportHasData(pReporter))


                    2414 

                    2415 ;634: {	


                    2416 

                    2417 ;635: 	return !ReportQueue_isEmpty(&pCurrReport->buffer);


                    2418 

00001038 e2840040   2419 	add	r0,r4,64


                                                                      Page 46
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
0000103c eb000000*  2420 	bl	ReportQueue_isEmpty

00001040 e3500000   2421 	cmp	r0,0

00001044 1a000017   2422 	bne	.L3584

                    2423 ;789: 				{


                    2424 

                    2425 ;790: 					sendFromReportBuf(pReporter);


                    2426 

                    2427 ;644: {		


                    2428 

                    2429 ;645: 	//Буферизированный отчёт и в буфере есть данные


                    2430 ;646: 	if (!pRCB->sessionOutBuffer.busy)


                    2431 

00001048 e2846b42   2432 	add	r6,r4,66<<10

0000104c e5f6005c   2433 	ldrb	r0,[r6,92]!

00001050 e3500000   2434 	cmp	r0,0

00001054 1a00000c   2435 	bne	.L3592

                    2436 ;647: 	{


                    2437 

                    2438 ;648: 		int reportSize = readReportFromBuffer(pRCB, reportAccessResultsBuf,


                    2439 

                    2440 ;639: {


                    2441 

                    2442 ;640: 	return ReportQueue_read(&pRCB->buffer, bufferToRead, bufSize);


                    2443 

00001058 e51f72b8*  2444 	ldr	r7,.L2603

0000105c e2840040   2445 	add	r0,r4,64

00001060 e1a01007   2446 	mov	r1,r7

00001064 e3a02d80   2447 	mov	r2,1<<13

00001068 eb000000*  2448 	bl	ReportQueue_read

0000106c e1b02000   2449 	movs	r2,r0

                    2450 ;649: 			sizeof(reportAccessResultsBuf));


                    2451 ;650: 		if (reportSize != 0)


                    2452 

00001070 0a000005   2453 	beq	.L3592

                    2454 ;651: 		{


                    2455 

                    2456 ;652: 			pRCB->sessionOutBuffer.busy = TRUE;


                    2457 

00001074 e1a03006   2458 	mov	r3,r6

00001078 e3a00001   2459 	mov	r0,1

0000107c e5c60000   2460 	strb	r0,[r6]

                    2461 ;653: 			sendReport(pRCB->connection, reportAccessResultsBuf, reportSize,


                    2462 

00001080 e594003c   2463 	ldr	r0,[r4,60]

00001084 e1a01007   2464 	mov	r1,r7

00001088 ebfffdff*  2465 	bl	sendReport

                    2466 .L3592:

                    2467 ;791: 					reportsProcessed = TRUE;


                    2468 

0000108c e51f0300*  2469 	ldr	r0,.L649

00001090 e3a06001   2470 	mov	r6,1

00001094 e5901000   2471 	ldr	r1,[r0]

00001098 e28b0b5a   2472 	add	r0,fp,90<<10

0000109c e280b064   2473 	add	fp,r0,100

000010a0 e2855001   2474 	add	r5,r5,1

000010a4 ea000007   2475 	b	.L3579

                    2476 .L3584:

                    2477 ;792: 				}


                    2478 ;793: 				else


                    2479 ;794: 				{


                    2480 


                                                                      Page 47
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    2481 ;795: 					reportsProcessed = processGI(pReporter);


                    2482 

                    2483 ;796: 				}


                    2484 ;797: 			}


                    2485 ;798: 			else


                    2486 ;799: 			{


                    2487 

                    2488 ;800: 				reportsProcessed = processGI(pReporter);


                    2489 

000010a8 e1a00004   2490 	mov	r0,r4

000010ac ebffff46*  2491 	bl	processGI

000010b0 e1a06000   2492 	mov	r6,r0

000010b4 e51f0328*  2493 	ldr	r0,.L649

000010b8 e5901000   2494 	ldr	r1,[r0]

                    2495 .L3577:

000010bc e28b0b5a   2496 	add	r0,fp,90<<10

000010c0 e280b064   2497 	add	fp,r0,100

000010c4 e2855001   2498 	add	r5,r5,1

                    2499 .L3579:

000010c8 e1550001   2500 	cmp	r5,r1

000010cc 3affffd1   2501 	blo	.L3578

                    2502 .L3576:

                    2503 ;801: 			}


                    2504 ;802: 			


                    2505 ;803: 		}			


                    2506 ;804: 		if(!reportsProcessed)


                    2507 

000010d0 e3560000   2508 	cmp	r6,0

000010d4 1affff53   2509 	bne	.L3539

                    2510 ;805:         {         


                    2511 

                    2512 ;806:             Idle();            


                    2513 

000010d8 ea000000   2514 	b	.L4151

                    2515 	.align	4

                    2516 .L4150:

000010dc 00000000*  2517 	.data.w	.L4068

                    2518 	.type	.L4150,$object

                    2519 	.size	.L4150,4

                    2520 

                    2521 .L4151:

                    2522 

000010e0 e6000010   2523 	.word	0xE6000010

                    2524 

000010e4 eaffff4f   2525 	b	.L3539

                    2526 	.endf	reportsThread

                    2527 	.align	4

                    2528 ;newBusOK	r0	local

                    2529 ;oldBusOK	.L4068	static

                    2530 ;timeStamp	[sp,4]	local

                    2531 ;newTimeStamp	r4	local

                    2532 ;rptIdx	r5	local

                    2533 ;reportsProcessed	r6	local

                    2534 ;rcbIdx	r6	local

                    2535 ;reportDataSize	r2	local

                    2536 ;pReporter	r4	local

                    2537 ;pRCB	r5	local

                    2538 ;pReporter	r4	local

                    2539 ;reportDataSize	r2	local

                    2540 ;sent	fp	local

                    2541 ;pReporter	r4	local


                                                                      Page 48
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    2542 ;pRCB	r0	local

                    2543 ;reportSize	r2	local

                    2544 

                    2545 ;data	none	param

                    2546 

                    2547 	.data

00000000 01        2548 .L4068:	.data.b	1

                    2549 	.text

                    2550 

                    2551 ;807:         }		


                    2552 ;808:     }


                    2553 ;809: }


                    2554 

                    2555 ;810: 


                    2556 ;811: void initReports(void)


                    2557 	.align	4

                    2558 	.align	4

                    2559 initReports::

000010e8 e92d4010   2560 	stmfd	[sp]!,{r4,lr}

                    2561 ;812: {


                    2562 

                    2563 ;813:     g_reportCount = 0;


                    2564 

000010ec e59f0424*  2565 	ldr	r0,.L4181

000010f0 e3a04000   2566 	mov	r4,0

000010f4 e5804000   2567 	str	r4,[r0]

                    2568 ;814:     registerAllRCB();


                    2569 

000010f8 eb000000*  2570 	bl	registerAllRCB

                    2571 ;815:     createThread(reportsThread,NULL);


                    2572 

000010fc e59f0418*  2573 	ldr	r0,.L4182

00001100 e1a01004   2574 	mov	r1,r4

00001104 eb000000*  2575 	bl	createThread

                    2576 ;816: 	Timers_setIntegrity1msCallBack(integrityTimerProc);


                    2577 

00001108 e59f0410*  2578 	ldr	r0,.L4183

0000110c e8bd4010   2579 	ldmfd	[sp]!,{r4,lr}

00001110 ea000000*  2580 	b	Timers_setIntegrity1msCallBack

                    2581 	.endf	initReports

                    2582 	.align	4

                    2583 

                    2584 	.section ".bss","awb"

                    2585 .L4174:

                    2586 	.data

                    2587 	.text

                    2588 

                    2589 ;817: }


                    2590 

                    2591 ;818: 


                    2592 ;819: PReporter getFreeReport(void)


                    2593 	.align	4

                    2594 	.align	4

                    2595 getFreeReport::

                    2596 ;820: {


                    2597 

                    2598 ;821: 	if (g_reportCount == MAX_REPORT_COUNT)


                    2599 

00001114 e59f13fc*  2600 	ldr	r1,.L4181

00001118 e5910000   2601 	ldr	r0,[r1]

0000111c e3500028   2602 	cmp	r0,40


                                                                      Page 49
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    2603 ;822: 	{


                    2604 

                    2605 ;823: 		return NULL;


                    2606 

00001120 03a00000   2607 	moveq	r0,0

00001124 0a000006   2608 	beq	.L4184

                    2609 ;824: 	}


                    2610 ;825: 


                    2611 ;826: 	return g_reports + g_reportCount;


                    2612 

00001128 e0800100   2613 	add	r0,r0,r0 lsl 2

0000112c e1a01100   2614 	mov	r1,r0 lsl 2

00001130 e0800001   2615 	add	r0,r0,r1

00001134 e0800381   2616 	add	r0,r0,r1 lsl 7

00001138 e0800501   2617 	add	r0,r0,r1 lsl 10

0000113c e59f13e0*  2618 	ldr	r1,.L4228

00001140 e0810100   2619 	add	r0,r1,r0 lsl 2

                    2620 .L4184:

00001144 e12fff1e*  2621 	ret	

                    2622 	.endf	getFreeReport

                    2623 	.align	4

                    2624 

                    2625 	.section ".bss","awb"

                    2626 .L4214:

                    2627 	.data

                    2628 	.text

                    2629 

                    2630 ;827: }


                    2631 

                    2632 ;828: 


                    2633 ;829: void finalizeReportRegistration(void)


                    2634 	.align	4

                    2635 	.align	4

                    2636 finalizeReportRegistration::

                    2637 ;830: {


                    2638 

                    2639 ;831: 	if (g_reportCount == MAX_REPORT_COUNT)


                    2640 

00001148 e59f13c8*  2641 	ldr	r1,.L4181

0000114c e5910000   2642 	ldr	r0,[r1]

00001150 e3500028   2643 	cmp	r0,40

                    2644 ;832: 	{


                    2645 

                    2646 ;833: 		ERROR_REPORT("Too many reports");


                    2647 ;834: 		return;


                    2648 

                    2649 ;835: 	}


                    2650 ;836: 	g_reportCount++;


                    2651 

00001154 12800001   2652 	addne	r0,r0,1

00001158 15810000   2653 	strne	r0,[r1]

0000115c e12fff1e*  2654 	ret	

                    2655 	.endf	finalizeReportRegistration

                    2656 	.align	4

                    2657 

                    2658 	.section ".bss","awb"

                    2659 .L4264:

                    2660 	.data

                    2661 	.text

                    2662 

                    2663 ;837: }



                                                                      Page 50
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    2664 

                    2665 ;838: 


                    2666 ;839: void disableDisconnectedReports(void)


                    2667 	.align	4

                    2668 	.align	4

                    2669 disableDisconnectedReports::

00001160 e92d4ff0   2670 	stmfd	[sp]!,{r4-fp,lr}

                    2671 ;840: {


                    2672 

                    2673 ;841: 	size_t i;


                    2674 ;842: 	Reporter* pReporter;


                    2675 ;843: 


                    2676 ;844: 	for (i = 0; i < g_reportCount; ++i)


                    2677 

00001164 e3a02b42   2678 	mov	r2,66<<10

00001168 e24dd00c   2679 	sub	sp,sp,12

0000116c e3a00000   2680 	mov	r0,0

00001170 e59f13a0*  2681 	ldr	r1,.L4181

00001174 e58d0004   2682 	str	r0,[sp,4]

00001178 e5910000   2683 	ldr	r0,[r1]

0000117c e282205c   2684 	add	r2,r2,92

00001180 e3500000   2685 	cmp	r0,0

00001184 b3a00000   2686 	movlt	r0,0

00001188 e58d0008   2687 	str	r0,[sp,8]

0000118c e1b031a0   2688 	movs	r3,r0 lsr 3

00001190 0a00007d   2689 	beq	.L4306

00001194 e59f038c*  2690 	ldr	r0,.L4821

00001198 e59fc384*  2691 	ldr	r12,.L4228

0000119c e08c9000   2692 	add	r9,r12,r0

000011a0 e28c0f96   2693 	add	r0,r12,0x0258

000011a4 e2804a87   2694 	add	r4,r0,135<<12

000011a8 e59f037c*  2695 	ldr	r0,.L4822

000011ac e08c5000   2696 	add	r5,r12,r0

000011b0 e28c0f64   2697 	add	r0,r12,0x0190

000011b4 e2806a5a   2698 	add	r6,r0,90<<12

000011b8 e59f0370*  2699 	ldr	r0,.L4823

000011bc e3a01000   2700 	mov	r1,0

000011c0 e08c7000   2701 	add	r7,r12,r0

000011c4 e28c0bb4   2702 	add	r0,r12,45<<12

000011c8 e280a0c8   2703 	add	r10,r0,200

000011cc e28c0b5a   2704 	add	r0,r12,90<<10

000011d0 e280b064   2705 	add	fp,r0,100

000011d4 e1a00183   2706 	mov	r0,r3 lsl 3

000011d8 e58d0004   2707 	str	r0,[sp,4]

                    2708 .L4307:

000011dc e1a0000c   2709 	mov	r0,r12

000011e0 e590e03c   2710 	ldr	lr,[r0,60]

000011e4 e35e0000   2711 	cmp	lr,0

000011e8 0a000007   2712 	beq	.L4312

000011ec e3a08bf2   2713 	mov	r8,242<<10

000011f0 e2888098   2714 	add	r8,r8,152

000011f4 e7d8e00e   2715 	ldrb	lr,[r8,lr]

000011f8 e35e0000   2716 	cmp	lr,0

000011fc 05c0e001   2717 	streqb	lr,[r0,1]

00001200 05c01002   2718 	streqb	r1,[r0,2]

00001204 0580103c   2719 	streq	r1,[r0,60]

00001208 07c01002   2720 	streqb	r1,[r0,r2]

                    2721 .L4312:

0000120c e1a0000b   2722 	mov	r0,fp

00001210 e590e03c   2723 	ldr	lr,[r0,60]

00001214 e35e0000   2724 	cmp	lr,0


                                                                      Page 51
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
00001218 0a000007   2725 	beq	.L4317

0000121c e3a08bf2   2726 	mov	r8,242<<10

00001220 e2888098   2727 	add	r8,r8,152

00001224 e7d8e00e   2728 	ldrb	lr,[r8,lr]

00001228 e35e0000   2729 	cmp	lr,0

0000122c 05c0e001   2730 	streqb	lr,[r0,1]

00001230 05c01002   2731 	streqb	r1,[r0,2]

00001234 0580103c   2732 	streq	r1,[r0,60]

00001238 07c01002   2733 	streqb	r1,[r0,r2]

                    2734 .L4317:

0000123c e1a0000a   2735 	mov	r0,r10

00001240 e590e03c   2736 	ldr	lr,[r0,60]

00001244 e35e0000   2737 	cmp	lr,0

00001248 0a000007   2738 	beq	.L4322

0000124c e3a08bf2   2739 	mov	r8,242<<10

00001250 e2888098   2740 	add	r8,r8,152

00001254 e7d8e00e   2741 	ldrb	lr,[r8,lr]

00001258 e35e0000   2742 	cmp	lr,0

0000125c 05c0e001   2743 	streqb	lr,[r0,1]

00001260 05c01002   2744 	streqb	r1,[r0,2]

00001264 0580103c   2745 	streq	r1,[r0,60]

00001268 07c01002   2746 	streqb	r1,[r0,r2]

                    2747 .L4322:

0000126c e1a00007   2748 	mov	r0,r7

00001270 e590e03c   2749 	ldr	lr,[r0,60]

00001274 e35e0000   2750 	cmp	lr,0

00001278 0a000007   2751 	beq	.L4327

0000127c e3a08bf2   2752 	mov	r8,242<<10

00001280 e2888098   2753 	add	r8,r8,152

00001284 e7d8e00e   2754 	ldrb	lr,[r8,lr]

00001288 e35e0000   2755 	cmp	lr,0

0000128c 05c0e001   2756 	streqb	lr,[r0,1]

00001290 05c01002   2757 	streqb	r1,[r0,2]

00001294 0580103c   2758 	streq	r1,[r0,60]

00001298 07c01002   2759 	streqb	r1,[r0,r2]

                    2760 .L4327:

0000129c e1a00006   2761 	mov	r0,r6

000012a0 e590e03c   2762 	ldr	lr,[r0,60]

000012a4 e35e0000   2763 	cmp	lr,0

000012a8 0a000007   2764 	beq	.L4332

000012ac e3a08bf2   2765 	mov	r8,242<<10

000012b0 e2888098   2766 	add	r8,r8,152

000012b4 e7d8e00e   2767 	ldrb	lr,[r8,lr]

000012b8 e35e0000   2768 	cmp	lr,0

000012bc 05c0e001   2769 	streqb	lr,[r0,1]

000012c0 05c01002   2770 	streqb	r1,[r0,2]

000012c4 0580103c   2771 	streq	r1,[r0,60]

000012c8 07c01002   2772 	streqb	r1,[r0,r2]

                    2773 .L4332:

000012cc e1a00005   2774 	mov	r0,r5

000012d0 e590e03c   2775 	ldr	lr,[r0,60]

000012d4 e35e0000   2776 	cmp	lr,0

000012d8 0a000007   2777 	beq	.L4337

000012dc e3a08bf2   2778 	mov	r8,242<<10

000012e0 e2888098   2779 	add	r8,r8,152

000012e4 e7d8e00e   2780 	ldrb	lr,[r8,lr]

000012e8 e35e0000   2781 	cmp	lr,0

000012ec 05c0e001   2782 	streqb	lr,[r0,1]

000012f0 05c01002   2783 	streqb	r1,[r0,2]

000012f4 0580103c   2784 	streq	r1,[r0,60]

000012f8 07c01002   2785 	streqb	r1,[r0,r2]


                                                                      Page 52
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    2786 .L4337:

000012fc e1a00004   2787 	mov	r0,r4

00001300 e590e03c   2788 	ldr	lr,[r0,60]

00001304 e35e0000   2789 	cmp	lr,0

00001308 0a000007   2790 	beq	.L4342

0000130c e3a08bf2   2791 	mov	r8,242<<10

00001310 e2888098   2792 	add	r8,r8,152

00001314 e7d8e00e   2793 	ldrb	lr,[r8,lr]

00001318 e35e0000   2794 	cmp	lr,0

0000131c 05c0e001   2795 	streqb	lr,[r0,1]

00001320 05c01002   2796 	streqb	r1,[r0,2]

00001324 0580103c   2797 	streq	r1,[r0,60]

00001328 07c01002   2798 	streqb	r1,[r0,r2]

                    2799 .L4342:

0000132c e1a00009   2800 	mov	r0,r9

00001330 e590e03c   2801 	ldr	lr,[r0,60]

00001334 e35e0000   2802 	cmp	lr,0

00001338 0a000007   2803 	beq	.L4346

0000133c e3a08bf2   2804 	mov	r8,242<<10

00001340 e2888098   2805 	add	r8,r8,152

00001344 e7d8e00e   2806 	ldrb	lr,[r8,lr]

00001348 e35e0000   2807 	cmp	lr,0

0000134c 05c0e001   2808 	streqb	lr,[r0,1]

00001350 05c01002   2809 	streqb	r1,[r0,2]

00001354 0580103c   2810 	streq	r1,[r0,60]

00001358 07c01002   2811 	streqb	r1,[r0,r2]

                    2812 .L4346:

0000135c e3a00fc8   2813 	mov	r0,0x0320

00001360 e2800ab4   2814 	add	r0,r0,45<<14

00001364 e0899000   2815 	add	r9,r9,r0

00001368 e0844000   2816 	add	r4,r4,r0

0000136c e0855000   2817 	add	r5,r5,r0

00001370 e0866000   2818 	add	r6,r6,r0

00001374 e0877000   2819 	add	r7,r7,r0

00001378 e08aa000   2820 	add	r10,r10,r0

0000137c e08bb000   2821 	add	fp,fp,r0

00001380 e08cc000   2822 	add	r12,r12,r0

00001384 e2533001   2823 	subs	r3,r3,1

00001388 1affff93   2824 	bne	.L4307

                    2825 .L4306:

0000138c e59d0008   2826 	ldr	r0,[sp,8]

00001390 e2103007   2827 	ands	r3,r0,7

00001394 0a000019   2828 	beq	.L4275

00001398 e59dc004   2829 	ldr	r12,[sp,4]

0000139c e3a05b5a   2830 	mov	r5,90<<10

000013a0 e08c010c   2831 	add	r0,r12,r12 lsl 2

000013a4 e1a01100   2832 	mov	r1,r0 lsl 2

000013a8 e0800001   2833 	add	r0,r0,r1

000013ac e0800381   2834 	add	r0,r0,r1 lsl 7

000013b0 e0800501   2835 	add	r0,r0,r1 lsl 10

000013b4 e59f1168*  2836 	ldr	r1,.L4228

000013b8 e3a0c000   2837 	mov	r12,0

000013bc e0811100   2838 	add	r1,r1,r0 lsl 2

000013c0 e2855064   2839 	add	r5,r5,100

                    2840 .L4349:

000013c4 e1a00001   2841 	mov	r0,r1

000013c8 e590e03c   2842 	ldr	lr,[r0,60]

000013cc e35e0000   2843 	cmp	lr,0

000013d0 0a000007   2844 	beq	.L4353

000013d4 e3a08bf2   2845 	mov	r8,242<<10

000013d8 e2888098   2846 	add	r8,r8,152


                                                                      Page 53
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
000013dc e7d8400e   2847 	ldrb	r4,[r8,lr]

000013e0 e3540000   2848 	cmp	r4,0

000013e4 05c04001   2849 	streqb	r4,[r0,1]

000013e8 05c0c002   2850 	streqb	r12,[r0,2]

000013ec 0580c03c   2851 	streq	r12,[r0,60]

000013f0 07c0c002   2852 	streqb	r12,[r0,r2]

                    2853 .L4353:

000013f4 e0811005   2854 	add	r1,r1,r5

000013f8 e2533001   2855 	subs	r3,r3,1

000013fc 1afffff0   2856 	bne	.L4349

                    2857 .L4275:

00001400 e28dd00c   2858 	add	sp,sp,12

00001404 e8bd8ff0   2859 	ldmfd	[sp]!,{r4-fp,pc}

                    2860 	.endf	disableDisconnectedReports

                    2861 	.align	4

                    2862 ;i	[sp,4]	local

                    2863 ;pReporter	r0	local

                    2864 

                    2865 	.section ".bss","awb"

                    2866 .L4723:

                    2867 	.data

                    2868 	.text

                    2869 

                    2870 ;855: 			}


                    2871 ;856: 		}


                    2872 ;857: 	}


                    2873 ;858: }


                    2874 

                    2875 ;859: 


                    2876 ;860: bool Reporter_isOwnerConnection(PReporter pReport, IsoConnection* conn)


                    2877 	.align	4

                    2878 	.align	4

                    2879 Reporter_isOwnerConnection::

                    2880 ;861: {


                    2881 

                    2882 ;862:     IsoConnection* owner = pReport->connection;


                    2883 

00001408 e590003c   2884 	ldr	r0,[r0,60]

                    2885 ;863:     return owner == conn;


                    2886 

0000140c e1500001   2887 	cmp	r0,r1

00001410 03a00001   2888 	moveq	r0,1

00001414 13a00000   2889 	movne	r0,0

00001418 e12fff1e*  2890 	ret	

                    2891 	.endf	Reporter_isOwnerConnection

                    2892 	.align	4

                    2893 ;owner	r0	local

                    2894 

                    2895 ;pReport	r0	param

                    2896 ;conn	r1	param

                    2897 

                    2898 	.section ".bss","awb"

                    2899 .L4849:

                    2900 	.data

                    2901 	.text

                    2902 

                    2903 ;864: }


                    2904 

                    2905 ;865: 


                    2906 ;866: bool Reporter_setEnable(PReporter rpt, IsoConnection* isoConn, bool enable)


                    2907 	.align	4


                                                                      Page 54
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    2908 	.align	4

                    2909 Reporter_setEnable::

0000141c e92d4070   2910 	stmfd	[sp]!,{r4-r6,lr}

                    2911 ;867: {    


                    2912 

                    2913 ;868: 	if (isRCBConnected(rpt))


                    2914 

00001420 e1a04001   2915 	mov	r4,r1

00001424 e1a06002   2916 	mov	r6,r2

00001428 e1a05000   2917 	mov	r5,r0

0000142c ebfffb0f*  2918 	bl	isRCBConnected

00001430 e3500000   2919 	cmp	r0,0

00001434 0a00000b   2920 	beq	.L4858

                    2921 ;869: 	{


                    2922 

                    2923 ;870: 		//Захваченный RCB


                    2924 ;871: 		if (rpt->connection == isoConn)


                    2925 

00001438 e595003c   2926 	ldr	r0,[r5,60]

0000143c e1500004   2927 	cmp	r0,r4

                    2928 ;877: 			}


                    2929 ;878: 			rpt->rcb.rptEna = enable;


                    2930 

                    2931 ;879: 		}


                    2932 ;880: 		else


                    2933 ;881: 		{


                    2934 

                    2935 ;882: 			//Чужой RCB


                    2936 ;883: 			TRACE("Attempt to write captured RCB");


                    2937 ;884: 			return FALSE;


                    2938 

00001440 13a00000   2939 	movne	r0,0

00001444 1a00000b   2940 	bne	.L4856

                    2941 ;872: 		{


                    2942 

                    2943 ;873: 			//Свой RCB


                    2944 ;874:             if (!enable && !rpt->rcb.resv)


                    2945 

00001448 e3560000   2946 	cmp	r6,0

0000144c 05d51002   2947 	ldreqb	r1,[r5,2]

00001450 03510000   2948 	cmpeq	r1,0

                    2949 ;875: 			{


                    2950 

                    2951 ;876: 				rpt->connection = NULL;


                    2952 

00001454 0585103c   2953 	streq	r1,[r5,60]

00001458 05c56001   2954 	streqb	r6,[r5,1]

                    2955 ;894: 		}


                    2956 ;895: 		rpt->rcb.rptEna = enable;


                    2957 

                    2958 ;896: 	}


                    2959 ;897: 	return TRUE;


                    2960 

0000145c 03a00001   2961 	moveq	r0,1

00001460 0a000004   2962 	beq	.L4856

00001464 ea000001   2963 	b	.L4867

                    2964 .L4858:

                    2965 ;885: 		}


                    2966 ;886: 	}


                    2967 ;887: 	else


                    2968 ;888: 	{



                                                                      Page 55
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    2969 

                    2970 ;889: 		//Свободный RCB


                    2971 ;890: 		if (enable)


                    2972 

00001468 e3560000   2973 	cmp	r6,0

                    2974 ;891: 		{


                    2975 

                    2976 ;892: 			//Захватываем RCB


                    2977 ;893: 			rpt->connection = isoConn;


                    2978 

0000146c 1585403c   2979 	strne	r4,[r5,60]

                    2980 .L4867:

00001470 e5c56001   2981 	strb	r6,[r5,1]

                    2982 ;894: 		}


                    2983 ;895: 		rpt->rcb.rptEna = enable;


                    2984 

                    2985 ;896: 	}


                    2986 ;897: 	return TRUE;


                    2987 

00001474 e3a00001   2988 	mov	r0,1

                    2989 .L4856:

00001478 e8bd8070   2990 	ldmfd	[sp]!,{r4-r6,pc}

                    2991 	.endf	Reporter_setEnable

                    2992 	.align	4

                    2993 

                    2994 ;rpt	r5	param

                    2995 ;isoConn	r4	param

                    2996 ;enable	r6	param

                    2997 

                    2998 	.section ".bss","awb"

                    2999 .L4993:

                    3000 	.data

                    3001 	.text

                    3002 

                    3003 ;898: }


                    3004 

                    3005 ;899: 


                    3006 ;900: bool Reporter_setResv(PReporter rpt, IsoConnection* isoConn, bool value)


                    3007 	.align	4

                    3008 	.align	4

                    3009 Reporter_setResv::

0000147c e92d4070   3010 	stmfd	[sp]!,{r4-r6,lr}

                    3011 ;901: {


                    3012 

                    3013 ;902:     if (isRCBConnected(rpt))


                    3014 

00001480 e1a04001   3015 	mov	r4,r1

00001484 e1a06002   3016 	mov	r6,r2

00001488 e1a05000   3017 	mov	r5,r0

0000148c ebfffaf7*  3018 	bl	isRCBConnected

00001490 e3500000   3019 	cmp	r0,0

00001494 0a00000b   3020 	beq	.L5023

                    3021 ;903:     {


                    3022 

                    3023 ;904:         //Захваченный RCB


                    3024 ;905:         if (rpt->connection == isoConn)


                    3025 

00001498 e595003c   3026 	ldr	r0,[r5,60]

0000149c e1500004   3027 	cmp	r0,r4

                    3028 ;911:             }


                    3029 ;912:             rpt->rcb.resv = value;



                                                                      Page 56
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    3030 

                    3031 ;913:         }


                    3032 ;914:         else


                    3033 ;915:         {


                    3034 

                    3035 ;916:             //Чужой RCB


                    3036 ;917:             TRACE("Attempt to write captured RCB");


                    3037 ;918:             return FALSE;


                    3038 

000014a0 13a00000   3039 	movne	r0,0

000014a4 1a00000b   3040 	bne	.L5021

                    3041 ;906:         {


                    3042 

                    3043 ;907:             //Свой RCB


                    3044 ;908:             if (!value && !rpt->rcb.rptEna)


                    3045 

000014a8 e3560000   3046 	cmp	r6,0

000014ac 05d51001   3047 	ldreqb	r1,[r5,1]

000014b0 03510000   3048 	cmpeq	r1,0

                    3049 ;909:             {


                    3050 

                    3051 ;910:                 rpt->connection = NULL;


                    3052 

000014b4 0585103c   3053 	streq	r1,[r5,60]

000014b8 05c56002   3054 	streqb	r6,[r5,2]

                    3055 ;928:         }


                    3056 ;929:         rpt->rcb.resv = value;


                    3057 

                    3058 ;930:     }


                    3059 ;931:     return TRUE;


                    3060 

000014bc 03a00001   3061 	moveq	r0,1

000014c0 0a000004   3062 	beq	.L5021

000014c4 ea000001   3063 	b	.L5032

                    3064 .L5023:

                    3065 ;919:         }


                    3066 ;920:     }


                    3067 ;921:     else


                    3068 ;922:     {


                    3069 

                    3070 ;923:         //Свободный RCB


                    3071 ;924:         if (value)


                    3072 

000014c8 e3560000   3073 	cmp	r6,0

                    3074 ;925:         {


                    3075 

                    3076 ;926:             //Захватываем RCB


                    3077 ;927:             rpt->connection = isoConn;


                    3078 

000014cc 1585403c   3079 	strne	r4,[r5,60]

                    3080 .L5032:

000014d0 e5c56002   3081 	strb	r6,[r5,2]

                    3082 ;928:         }


                    3083 ;929:         rpt->rcb.resv = value;


                    3084 

                    3085 ;930:     }


                    3086 ;931:     return TRUE;


                    3087 

000014d4 e3a00001   3088 	mov	r0,1

                    3089 .L5021:

000014d8 e8bd8070   3090 	ldmfd	[sp]!,{r4-r6,pc}


                                                                      Page 57
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    3091 	.endf	Reporter_setResv

                    3092 	.align	4

                    3093 

                    3094 ;rpt	r5	param

                    3095 ;isoConn	r4	param

                    3096 ;value	r6	param

                    3097 

                    3098 	.section ".bss","awb"

                    3099 .L5153:

                    3100 	.data

                    3101 	.text

                    3102 

                    3103 ;932: }


                    3104 

                    3105 ;933: 


                    3106 ;934: void Reporter_setDataSetName(PReporter rpt, StringView* name)


                    3107 	.align	4

                    3108 	.align	4

                    3109 Reporter_setDataSetName::

                    3110 ;935: {


                    3111 

                    3112 ;936: 	rpt->rcb.dataSetName = (uint8_t*)name->p;


                    3113 

000014dc e5912004   3114 	ldr	r2,[r1,4]

000014e0 e5911000   3115 	ldr	r1,[r1]

000014e4 e580200c   3116 	str	r2,[r0,12]

                    3117 ;937: 	rpt->rcb.dataSetNameLength = name->len;


                    3118 

000014e8 e5801010   3119 	str	r1,[r0,16]

000014ec e12fff1e*  3120 	ret	

                    3121 	.endf	Reporter_setDataSetName

                    3122 	.align	4

                    3123 

                    3124 ;rpt	r0	param

                    3125 ;name	r1	param

                    3126 

                    3127 	.section ".bss","awb"

                    3128 .L5198:

                    3129 	.data

                    3130 	.text

                    3131 

                    3132 ;938: }


                    3133 

                    3134 ;939: 


                    3135 ;940: void Reporter_setIntgPd(PReporter rpt, uint32_t intgPd)


                    3136 	.align	4

                    3137 	.align	4

                    3138 Reporter_setIntgPd::

                    3139 ;941: {


                    3140 

                    3141 ;942: 	//Если значение меньше минимального допустимого, пишем минимально допустимое


                    3142 ;943: 	//0 - специальное значение для выключения Integrity


                    3143 ;944: 	if (intgPd != 0 && intgPd < MIN_INTG_PD)


                    3144 

000014f0 e3510000   3145 	cmp	r1,0

                    3146 

                    3147 

000014f4 13510064   3148 	cmpne	r1,100

000014f8 33a01064   3149 	movlo	r1,100

                    3150 .L5207:

                    3151 ;947: 	}



                                                                      Page 58
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    3152 ;948: 


                    3153 ;949: 	//Останавливаем таймер


                    3154 ;950: 	rpt->rcb.intgPd = 0;


                    3155 

000014fc e3a02000   3156 	mov	r2,0

                    3157 ;951: 	//Сбрасываем таймер


                    3158 ;952: 	rpt->intgPdCounter = 0;


                    3159 

00001500 e580202c   3160 	str	r2,[r0,44]

                    3161 ;953: 	rpt->intgTimerAlam = false;


                    3162 

00001504 e5c02030   3163 	strb	r2,[r0,48]

                    3164 ;954: 	//Пишем новое значение	


                    3165 ;955: 	rpt->rcb.intgPd = intgPd;


                    3166 

00001508 e580101c   3167 	str	r1,[r0,28]

0000150c e12fff1e*  3168 	ret	

                    3169 	.endf	Reporter_setIntgPd

                    3170 	.align	4

                    3171 

                    3172 ;rpt	r0	param

                    3173 ;intgPd	r1	param

                    3174 

                    3175 	.section ".bss","awb"

                    3176 .L5248:

                    3177 	.data

                    3178 	.text

                    3179 

                    3180 ;956: }


                    3181 

                    3182 ;957: 


                    3183 ;958: void Reporter_setGI(PReporter rpt, bool gi)


                    3184 	.align	4

                    3185 	.align	4

                    3186 Reporter_setGI::

                    3187 ;959: {


                    3188 

                    3189 ;960:     rpt->rcb.gi = gi;


                    3190 

00001510 e5c01022   3191 	strb	r1,[r0,34]

00001514 e12fff1e*  3192 	ret	

                    3193 	.endf	Reporter_setGI

                    3194 	.align	4

                    3195 

                    3196 ;rpt	r0	param

                    3197 ;gi	r1	param

                    3198 

                    3199 	.section ".bss","awb"

                    3200 .L5278:

                    3201 	.data

                    3202 	.text

                    3203 

                    3204 ;961: }


                    3205 	.align	4

                    3206 .L4181:

00001518 00000000*  3207 	.data.w	g_reportCount

                    3208 	.type	.L4181,$object

                    3209 	.size	.L4181,4

                    3210 

                    3211 .L4182:

0000151c 00000000*  3212 	.data.w	reportsThread


                                                                      Page 59
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    3213 	.type	.L4182,$object

                    3214 	.size	.L4182,4

                    3215 

                    3216 .L4183:

00001520 00000000*  3217 	.data.w	integrityTimerProc

                    3218 	.type	.L4183,$object

                    3219 	.size	.L4183,4

                    3220 

                    3221 .L4228:

00001524 00000000*  3222 	.data.w	g_reports

                    3223 	.type	.L4228,$object

                    3224 	.size	.L4228,4

                    3225 

                    3226 .L4821:

00001528 0009dabc   3227 	.data.w	0x0009dabc

                    3228 	.type	.L4821,$object

                    3229 	.size	.L4821,4

                    3230 

                    3231 .L4822:

0000152c 000709f4   3232 	.data.w	0x000709f4

                    3233 	.type	.L4822,$object

                    3234 	.size	.L4822,4

                    3235 

                    3236 .L4823:

00001530 0004392c   3237 	.data.w	0x0004392c

                    3238 	.type	.L4823,$object

                    3239 	.size	.L4823,4

                    3240 

                    3241 	.align	4

                    3242 ;reportInclusionReasons	reportInclusionReasons	static

                    3243 ;reportValuesBuf	reportValuesBuf	static

                    3244 ;reportAccessResultsBuf	reportAccessResultsBuf	static

                    3245 ;reportMmsBuf	reportMmsBuf	static

                    3246 ;reportPresentationBuf	reportPresentationBuf	static

                    3247 

                    3248 	.data

00000001 000000    3249 	.space	3

                    3250 .L5300:

                    3251 	.globl	reportVarNameSequence

00000004 038005a1   3252 reportVarNameSequence:	.data.b	161,5,128,3

00000008 5052      3253 	.data.b	82,80

0000000a 54        3254 	.data.b	84

0000000b 00        3255 	.space	1

                    3256 	.type	reportVarNameSequence,$object

                    3257 	.size	reportVarNameSequence,8

                    3258 .L5301:

                    3259 	.globl	g_reportCount

0000000c 00000000   3260 g_reportCount:	.data.b	0,0,0,0

                    3261 	.type	g_reportCount,$object

                    3262 	.size	g_reportCount,4

                    3263 	.comm	g_reports,3690400,4

                    3264 	.type	g_reports,$object

                    3265 	.size	g_reports,3690400

                    3266 	.ghsnote version,6

                    3267 	.ghsnote tools,1

                    3268 	.ghsnote options,0

                    3269 	.text

                    3270 	.align	4

                    3271 	.data

                    3272 	.align	4

                    3273 	.section ".bss","awb"


                                                                      Page 60
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_aus1.s
                    3274 	.align	4

                    3275 	.text

