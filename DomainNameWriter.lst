                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2bs1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=DomainNameWriter.c -o gh_2bs1.o -list=DomainNameWriter.lst C:\Users\<USER>\AppData\Local\Temp\gh_2bs1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_2bs1.s
Source File: DomainNameWriter.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile

                      11 ;		DomainNameWriter.c -o DomainNameWriter.o

                      12 ;Source File:   DomainNameWriter.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:31:06 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "DomainNameWriter.h"


                      23 ;2: #include "AsnEncoding.h"


                      24 ;3: #include <string.h>


                      25 ;4: 


                      26 ;5: static void processFastForward(DomainNameWriter* self)


                      27 

                      28 ;17: 	}


                      29 ;18: }


                      30 

                      31 ;19: 


                      32 ;20: void DomainNameWriter_init(DomainNameWriter* self,


                      33 	.text

                      34 	.align	4

                      35 DomainNameWriter_init::

                      36 ;21: 	uint8_t* outBuf, int outBufSize)


                      37 ;22: {


                      38 

                      39 ;23: 	self->stackDepth = -1;


                      40 

00000000 e3e0c000     41 	mvn	r12,0

                      42 ;24: 	self->currDomainName[0] = 0;


                      43 

00000004 e3a03000     44 	mov	r3,0

00000008 e5c0303c     45 	strb	r3,[r0,60]

                      46 ;25: 	self->outBuf = outBuf;


                      47 

0000000c e480100c     48 	str	r1,[r0],12

                      49 ;26: 	self->totalSize = 0;


                      50 


                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2bs1.s
                      51 ;27: 	self->outBufSize = outBufSize;


                      52 

00000010 e8801004     53 	stmea	[r0],{r2,r12}

                      54 ;28: 	self->totalSize = 0;


                      55 

00000014 e5003008     56 	str	r3,[r0,-8]

                      57 ;29: 	self->bufferFull = FALSE;


                      58 

00000018 e5403004     59 	strb	r3,[r0,-4]

                      60 ;30: 	self->fastForward = FALSE;


                      61 

0000001c e5c030b1     62 	strb	r3,[r0,177]

00000020 e12fff1e*    63 	ret	

                      64 	.endf	DomainNameWriter_init

                      65 	.align	4

                      66 

                      67 ;self	r0	param

                      68 ;outBuf	r1	param

                      69 ;outBufSize	r2	param

                      70 

                      71 	.section ".bss","awb"

                      72 .L49:

                      73 	.data

                      74 	.text

                      75 

                      76 ;31: }


                      77 

                      78 ;32: 


                      79 ;33: void DomainNameWriter_setStartName(DomainNameWriter* self, 


                      80 	.align	4

                      81 	.align	4

                      82 DomainNameWriter_setStartName::

00000024 e92d4070     83 	stmfd	[sp]!,{r4-r6,lr}

                      84 ;34: 	uint8_t* startName, int startNameSize)


                      85 ;35: {


                      86 

                      87 ;36: 	memcpy(self->startingName, startName, startNameSize);


                      88 

00000028 e1a05002     89 	mov	r5,r2

0000002c e1a04000     90 	mov	r4,r0

00000030 e28460be     91 	add	r6,r4,190

00000034 e1a00006     92 	mov	r0,r6

00000038 eb000000*    93 	bl	memcpy

                      94 ;37: 	self->startingName[startNameSize] = 0;


                      95 

0000003c e3a01000     96 	mov	r1,0

00000040 e7c51006     97 	strb	r1,[r5,r6]

                      98 ;38: 	self->startingNameSize = startNameSize;


                      99 

00000044 e5845140    100 	str	r5,[r4,320]

                     101 ;39: 	self->fastForward = TRUE;


                     102 

00000048 e3a00001    103 	mov	r0,1

0000004c e5c400bd    104 	strb	r0,[r4,189]

00000050 e8bd8070    105 	ldmfd	[sp]!,{r4-r6,pc}

                     106 	.endf	DomainNameWriter_setStartName

                     107 	.align	4

                     108 

                     109 ;self	r4	param

                     110 ;startName	none	param

                     111 ;startNameSize	r5	param


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2bs1.s
                     112 

                     113 	.section ".bss","awb"

                     114 .L81:

                     115 	.data

                     116 	.text

                     117 

                     118 ;40: }


                     119 

                     120 ;41: 


                     121 ;42: void DomainNameWriter_pushName(DomainNameWriter* self, 


                     122 	.align	4

                     123 	.align	4

                     124 DomainNameWriter_pushName::

00000054 e92d40f0    125 	stmfd	[sp]!,{r4-r7,lr}

                     126 ;43: 	uint8_t* name, int nameLen)


                     127 ;44: {	


                     128 

00000058 e1a06002    129 	mov	r6,r2

0000005c e1a05000    130 	mov	r5,r0

00000060 e5950010    131 	ldr	r0,[r5,16]

00000064 e3a04000    132 	mov	r4,0

                     133 ;45: 	int currNameEnd = 0;


                     134 

                     135 ;46: 	if (self->stackDepth >= 0)


                     136 

00000068 e3500000    137 	cmp	r0,0

0000006c ba000005    138 	blt	.L90

                     139 ;47: 	{


                     140 

                     141 ;48: 		currNameEnd = self->nameEndStack[self->stackDepth];


                     142 

00000070 e0850100    143 	add	r0,r5,r0 lsl 2

00000074 e5904014    144 	ldr	r4,[r0,20]

                     145 ;49: 		self->currDomainName[currNameEnd++] = '$';


                     146 

00000078 e3a02024    147 	mov	r2,36

0000007c e0840005    148 	add	r0,r4,r5

00000080 e2844001    149 	add	r4,r4,1

00000084 e5c0203c    150 	strb	r2,[r0,60]

                     151 .L90:

                     152 ;50: 	}


                     153 ;51: 		


                     154 ;52: 	memcpy(self->currDomainName + currNameEnd, name, nameLen);


                     155 

00000088 e1a02006    156 	mov	r2,r6

0000008c e285703c    157 	add	r7,r5,60

00000090 e0840007    158 	add	r0,r4,r7

00000094 eb000000*   159 	bl	memcpy

                     160 ;53: 


                     161 ;54: 	currNameEnd += nameLen;


                     162 

00000098 e0842006    163 	add	r2,r4,r6

                     164 ;55: 	self->currDomainName[currNameEnd] = 0;	


                     165 

0000009c e3a01000    166 	mov	r1,0

000000a0 e7c21007    167 	strb	r1,[r2,r7]

                     168 ;56: 


                     169 ;57: 	self->stackDepth += 1;


                     170 

000000a4 e5950010    171 	ldr	r0,[r5,16]

000000a8 e2800001    172 	add	r0,r0,1


                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2bs1.s
000000ac e5850010    173 	str	r0,[r5,16]

                     174 ;58: 	self->nameEndStack[self->stackDepth] = currNameEnd;


                     175 

000000b0 e0850100    176 	add	r0,r5,r0 lsl 2

000000b4 e5802014    177 	str	r2,[r0,20]

000000b8 e8bd80f0    178 	ldmfd	[sp]!,{r4-r7,pc}

                     179 	.endf	DomainNameWriter_pushName

                     180 	.align	4

                     181 ;currNameEnd	r4	local

                     182 

                     183 ;self	r5	param

                     184 ;name	r1	param

                     185 ;nameLen	r6	param

                     186 

                     187 	.section ".bss","awb"

                     188 .L130:

                     189 	.data

                     190 	.text

                     191 

                     192 ;59: }


                     193 

                     194 ;60: 


                     195 ;61: void DomainNameWriter_discardName(DomainNameWriter* self)


                     196 	.align	4

                     197 	.align	4

                     198 DomainNameWriter_discardName::

                     199 ;62: {


                     200 

                     201 ;63: 	if (self->stackDepth < 0)


                     202 

000000bc e5901010    203 	ldr	r1,[r0,16]

000000c0 e3510000    204 	cmp	r1,0

000000c4 ba000007    205 	blt	.L141

                     206 ;64: 	{


                     207 

                     208 ;65: 		return;


                     209 

                     210 ;66: 	}


                     211 ;67: 	self->stackDepth--;


                     212 

000000c8 e2511001    213 	subs	r1,r1,1

000000cc e5801010    214 	str	r1,[r0,16]

                     215 ;68: 	if (self->stackDepth < 0)


                     216 

                     217 ;69: 	{


                     218 

                     219 ;70: 		self->currDomainName[0] = 0;


                     220 

000000d0 5280203c    221 	addpl	r2,r0,60

                     222 ;71: 	}


                     223 ;72: 	else


                     224 ;73: 	{


                     225 

                     226 ;74: 		int currNameEnd = self->nameEndStack[self->stackDepth];


                     227 

000000d4 50800101    228 	addpl	r0,r0,r1 lsl 2

000000d8 55900014    229 	ldrpl	r0,[r0,20]

                     230 ;75: 		self->currDomainName[currNameEnd] = 0;


                     231 

000000dc e3a01000    232 	mov	r1,0

000000e0 45c0103c    233 	strmib	r1,[r0,60]


                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2bs1.s
000000e4 57c01002    234 	strplb	r1,[r0,r2]

                     235 .L141:

000000e8 e12fff1e*   236 	ret	

                     237 	.endf	DomainNameWriter_discardName

                     238 	.align	4

                     239 ;currNameEnd	r0	local

                     240 

                     241 ;self	r0	param

                     242 

                     243 	.section ".bss","awb"

                     244 .L209:

                     245 	.data

                     246 	.text

                     247 

                     248 ;76: 	}


                     249 ;77: }


                     250 

                     251 ;78: 


                     252 ;79: int DomainNameWriter_encode(DomainNameWriter* self)


                     253 	.align	4

                     254 	.align	4

                     255 DomainNameWriter_encode::

000000ec e92d4030    256 	stmfd	[sp]!,{r4-r5,lr}

000000f0 e1a05000    257 	mov	r5,r0

                     258 ;80: {		


                     259 

                     260 ;81: 	int encodedSize;


                     261 ;82: 	if (self->bufferFull)


                     262 

000000f4 e5d50008    263 	ldrb	r0,[r5,8]

000000f8 e3500000    264 	cmp	r0,0

000000fc 1a00000f    265 	bne	.L232

                     266 ;83: 	{


                     267 

                     268 ;84: 		return 0;


                     269 

                     270 ;85: 	}


                     271 ;86: 


                     272 ;87: 	if (self->fastForward)


                     273 

00000100 e5d500bd    274 	ldrb	r0,[r5,189]

00000104 e3500000    275 	cmp	r0,0

00000108 0a00000e    276 	beq	.L230

                     277 ;88: 	{


                     278 

                     279 ;89: 		processFastForward(self);


                     280 

                     281 ;6: {


                     282 

                     283 ;7: 	int currNameEnd;


                     284 ;8: 	if (self->stackDepth == -1)


                     285 

0000010c e5950010    286 	ldr	r0,[r5,16]

00000110 e3700001    287 	cmn	r0,1

00000114 0a000009    288 	beq	.L232

                     289 ;9: 	{


                     290 

                     291 ;10: 		return;


                     292 

                     293 ;11: 	}


                     294 ;12: 	currNameEnd = self->nameEndStack[self->stackDepth];



                                                                      Page 6
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2bs1.s
                     295 

00000118 e0850100    296 	add	r0,r5,r0 lsl 2

0000011c e5902014    297 	ldr	r2,[r0,20]

                     298 ;13: 	if (currNameEnd == self->startingNameSize


                     299 

00000120 e5950140    300 	ldr	r0,[r5,320]

00000124 e1520000    301 	cmp	r2,r0

00000128 1a000004    302 	bne	.L232

0000012c e28510be    303 	add	r1,r5,190

00000130 e285003c    304 	add	r0,r5,60

00000134 eb000000*   305 	bl	memcmp

00000138 e3500000    306 	cmp	r0,0

                     307 ;14: 		&& memcmp(self->currDomainName, self->startingName, currNameEnd) == 0)


                     308 ;15: 	{


                     309 

                     310 ;16: 		self->fastForward = FALSE;


                     311 

0000013c 05c500bd    312 	streqb	r0,[r5,189]

                     313 .L232:

                     314 ;90: 		return 0;


                     315 

00000140 e3a00000    316 	mov	r0,0

00000144 ea000014    317 	b	.L225

                     318 .L230:

                     319 ;91: 	}


                     320 ;92: 


                     321 ;93: 	encodedSize = BerEncoder_determineEncodedStringSize(


                     322 

00000148 e285003c    323 	add	r0,r5,60

0000014c eb000000*   324 	bl	BerEncoder_determineEncodedStringSize

00000150 e1a04000    325 	mov	r4,r0

                     326 ;94: 		(char*)self->currDomainName);


                     327 ;95: 


                     328 ;96: 	if (self->totalSize + encodedSize > self->outBufSize)


                     329 

00000154 e5953004    330 	ldr	r3,[r5,4]

00000158 e595100c    331 	ldr	r1,[r5,12]

0000015c e0840003    332 	add	r0,r4,r3

00000160 e1500001    333 	cmp	r0,r1

                     334 ;97: 	{


                     335 

                     336 ;98: 		self->bufferFull = TRUE;


                     337 

00000164 c3a00001    338 	movgt	r0,1

00000168 c5c50008    339 	strgtb	r0,[r5,8]

                     340 ;99: 		return 0;


                     341 

0000016c c3a00000    342 	movgt	r0,0

00000170 ca000009    343 	bgt	.L225

                     344 ;100: 	}


                     345 ;101: 


                     346 ;102: 	if (self->outBuf != NULL)


                     347 

00000174 e5952000    348 	ldr	r2,[r5]

00000178 e3520000    349 	cmp	r2,0

                     350 ;105: 			(char*)self->currDomainName, self->outBuf, self->totalSize);


                     351 ;106: 	}


                     352 ;107: 	else


                     353 ;108: 	{


                     354 

                     355 ;109: 		self->totalSize += encodedSize;



                                                                      Page 7
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_2bs1.s
                     356 

                     357 ;110: 	}


                     358 ;111: 			


                     359 ;112: 	return encodedSize;


                     360 

0000017c 05850004    361 	streq	r0,[r5,4]

00000180 01a00004    362 	moveq	r0,r4

00000184 0a000004    363 	beq	.L225

                     364 ;103: 	{


                     365 

                     366 ;104: 		self->totalSize = BerEncoder_encodeStringWithTL(ASN_VISIBLE_STRING,


                     367 

00000188 e285103c    368 	add	r1,r5,60

0000018c e3a0001a    369 	mov	r0,26

00000190 eb000000*   370 	bl	BerEncoder_encodeStringWithTL

                     371 ;110: 	}


                     372 ;111: 			


                     373 ;112: 	return encodedSize;


                     374 

00000194 e5850004    375 	str	r0,[r5,4]

00000198 e1a00004    376 	mov	r0,r4

                     377 .L225:

0000019c e8bd8030    378 	ldmfd	[sp]!,{r4-r5,pc}

                     379 	.endf	DomainNameWriter_encode

                     380 	.align	4

                     381 ;encodedSize	r4	local

                     382 ;currNameEnd	r2	local

                     383 

                     384 ;self	r5	param

                     385 

                     386 	.section ".bss","awb"

                     387 .L403:

                     388 	.data

                     389 	.text

                     390 

                     391 ;113: }


                     392 	.align	4

                     393 

                     394 	.data

                     395 	.ghsnote version,6

                     396 	.ghsnote tools,3

                     397 	.ghsnote options,0

                     398 	.text

                     399 	.align	4

