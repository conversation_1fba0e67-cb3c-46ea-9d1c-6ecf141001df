                                                                      Page 1
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1jg1.s
Command Line: C:\GHS\arm423\asarm.exe -elf -b0 -I../../../../AT91CORE/CLIB -I../../../../AT91CORE/CLIB/ansi -I../../../../AT91CORE/modules/SYSDLL/include -I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform -If:\libs/lwip/src/include -I../../../../lwipport/lwiplib -I../../../../lwipport/include -I../../../../AT91CORE/modules/BayController/ethbus -I../../../../loggers/syslog -IC:\GHS\arm423\lib\arm4 -cpu=arm7tm -fpu=soft -source=main.c -o gh_1jg1.o -list=main.lst C:\Users\<USER>\AppData\Local\Temp\gh_1jg1.s 
Original File: C:\Users\<USER>\AppData\Local\Temp\gh_1jg1.s
Source File: main.c
Directory: F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer
Host OS: GHS_WIN32
Version: EASE 2.0 (c) 2001 Green Hills Software    Build: Feb 22 2006
Release: MULTI v4.2.3                                                                      
Revision Date: Fri Mar 24 12:14:01 2006

Release Date: Fri Mar 31 10:02:10 2006

                       1 ;Driver Command: ccarm -c -bigswitch -c -list -cpu=arm7tm -passsource -nostdinc

                       2 ;		-no_discard_zero_initializers --prototype_errors

                       3 ;		--quit_after_warnings -I../../../../AT91CORE/CLIB

                       4 ;		-I../../../../AT91CORE/CLIB/ansi

                       5 ;		-I../../../../AT91CORE/modules/SYSDLL/include

                       6 ;		-I../../../../AT91CORE/modules/net/include -I../../.. -Iplatform

                       7 ;		-If:\libs/lwip/src/include -I../../../../lwipport/lwiplib

                       8 ;		-I../../../../lwipport/include

                       9 ;		-I../../../../AT91CORE/modules/BayController/ethbus

                      10 ;		-I../../../../loggers/syslog -Ospeed -Oslowcompile main.c -o

                      11 ;		main.o

                      12 ;Source File:   main.c

                      13 ;Directory:     

                      14 ;		F:\work\SAPR_RZA_clones\MMS-make\IEC_61850\modules\MMS\MMSServer

                      15 ;Compile Date:  Mon Jul 28 12:30:54 2025

                      16 ;Host OS:       Win32

                      17 ;Version:       C-ARM 4.2.1 RELEASE VERSION

                      18 ;Release:       MULTI v4.2.3

                      19 ;Revision Date: Wed Mar 29 05:25:47 2006

                      20 ;Release Date:  Fri Mar 31 10:02:14 2006

                      21 

                      22 ;1: #include "main.h"


                      23 ;2: 


                      24 ;3: #include "MemoryManager.h"


                      25 ;4: #include "mms.h"


                      26 ;5: #include "iedmodel.h"


                      27 ;6: #include "pwin_access.h"


                      28 ;7: #include "DataSlice.h"


                      29 ;8: #include "out_buffers.h"


                      30 ;9: #include "reports.h"


                      31 ;10: #include "goose.h"


                      32 ;11: #include "file_system.h"


                      33 ;12: #include "server.h"


                      34 ;13: #include "timers.h"


                      35 ;14: #include "iedTree/iedTree.h"


                      36 ;15: #include "BusError.h"


                      37 ;16: 


                      38 ;17: #include <debug.h>


                      39 ;18: 


                      40 ;19: #include <stddef.h>


                      41 ;20: #include <Clib.h>


                      42 ;21: #include "netTools.h"


                      43 ;22: 


                      44 ;23: 


                      45 ;24: void serverMain(void)


                      46 	.text

                      47 	.align	4

                      48 serverMain::

00000000 e92d4000     49 	stmfd	[sp]!,{lr}

                      50 ;25: {



                                                                      Page 2
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1jg1.s
                      51 

00000004 e24dd004     52 	sub	sp,sp,4

00000008 e3a00000     53 	mov	r0,0

0000000c e58d0000     54 	str	r0,[sp]

                      55 ;26:     int iedModelSize = 0;


                      56 

                      57 ;27:     uint8_t* pIedModel;


                      58 ;28: 


                      59 ;29:     debugStart();


                      60 

00000010 eb000000*    61 	bl	debugStart

                      62 ;30: 


                      63 ;31:     TRACE("Init memory manager");


                      64 ;32: 


                      65 ;33:     if (!MM_init())


                      66 

00000014 eb000000*    67 	bl	MM_init

00000018 e3500000     68 	cmp	r0,0

0000001c 0a000026     69 	beq	.L2

                      70 ;34:     {


                      71 

                      72 ;35:         return;


                      73 

                      74 ;36:     }


                      75 ;37: 


                      76 ;38:     TRACE("Init PWin");


                      77 ;39: 


                      78 ;40:     if(!initPWin())


                      79 

00000020 eb000000*    80 	bl	initPWin

00000024 e3500000     81 	cmp	r0,0

00000028 0a000023     82 	beq	.L2

                      83 ;41:     {


                      84 

                      85 ;42:         TRACE("Error");


                      86 ;43:         return;


                      87 

                      88 ;44:     }


                      89 ;45: 


                      90 ;46:     TRACE("dataSliceInit");


                      91 ;47:     // dataSliceInit придерживает инициализацию до прихода первого DataSlice


                      92 ;48:     // поэтому надо рассмотреть возможность переноса этого вызова пониже


                      93 ;49:     // чтобы ускорить инициализацию в целом.


                      94 ;50:     if(!dataSliceInit())


                      95 

0000002c eb000000*    96 	bl	dataSliceInit

00000030 e3500000     97 	cmp	r0,0

00000034 0a000020     98 	beq	.L2

                      99 ;51:     {


                     100 

                     101 ;52:         TRACE("Error");


                     102 ;53:         return;


                     103 

                     104 ;54:     }


                     105 ;55: 


                     106 ;56:     TRACE("fs_init");


                     107 ;57:     if(!fs_init())


                     108 

00000038 eb000000*   109 	bl	fs_init

0000003c e3500000    110 	cmp	r0,0

00000040 0a00001d    111 	beq	.L2


                                                                      Page 3
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1jg1.s
                     112 ;58:     {


                     113 

                     114 ;59:         TRACE("Error");


                     115 ;60:         return;


                     116 

                     117 ;61:     }


                     118 ;62: 


                     119 ;63:     TRACE("NetTools_init");


                     120 ;64:     if( !NetTools_init())


                     121 

00000044 eb000000*   122 	bl	NetTools_init

00000048 e3500000    123 	cmp	r0,0

0000004c 0a00001a    124 	beq	.L2

                     125 ;65:     {


                     126 

                     127 ;66:         TRACE("Error");


                     128 ;67:         return;


                     129 

                     130 ;68:     }


                     131 ;69: 


                     132 ;70:     TRACE("BusError_init");


                     133 ;71:     BusError_init();


                     134 

00000050 eb000000*   135 	bl	BusError_init

                     136 ;72: 


                     137 ;73: 


                     138 ;74:     TRACE("Init sockets");


                     139 ;75: 


                     140 ;76:     if(!socketInit())


                     141 

00000054 eb000000*   142 	bl	socketInit

00000058 e3500000    143 	cmp	r0,0

0000005c 0a000016    144 	beq	.L2

                     145 ;77:     {


                     146 

                     147 ;78:         TRACE("Error");


                     148 ;79:         return;


                     149 

                     150 ;80:     }


                     151 ;81: 


                     152 ;82:     TRACE("Load IED model");


                     153 ;83: 


                     154 ;84:     // TODO Убедиться что правильно работает при отсутствии ROM-модуля


                     155 ;85:     pIedModel = loadIedModel(&iedModelSize);


                     156 

00000060 e1a0000d    157 	mov	r0,sp

00000064 eb000000*   158 	bl	loadIedModel

                     159 ;86: 


                     160 ;87:     //Если ROM-модуль не найден, останется информационная модель по умолчанию.


                     161 ;88:     if(pIedModel != NULL)


                     162 

00000068 e3500000    163 	cmp	r0,0

                     164 ;89:     {


                     165 

                     166 ;90:         TRACE("Set IED model");


                     167 ;91:         setIedModel(pIedModel, iedModelSize);


                     168 

0000006c 159d1000    169 	ldrne	r1,[sp]

00000070 1b000000*   170 	blne	setIedModel

                     171 ;92:     }


                     172 ;93: 



                                                                      Page 4
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1jg1.s
                     173 ;94:     // При инициализации в IEDTree элементов t (TimeStamp)


                     174 ;95:     // используется время из DataSlice, поэтому захватываем здесь


                     175 ;96:     dataSliceCapture();


                     176 

00000074 eb000000*   177 	bl	dataSliceCapture

                     178 ;97: 


                     179 ;98:     if(!IEDTree_init(iedModel, iedModelSize))


                     180 

00000078 e59f2044*   181 	ldr	r2,.L159

0000007c e59d1000    182 	ldr	r1,[sp]

00000080 e5920000    183 	ldr	r0,[r2]

00000084 eb000000*   184 	bl	IEDTree_init

00000088 e3500000    185 	cmp	r0,0

0000008c 1a000001    186 	bne	.L24

                     187 ;99:     {


                     188 

                     189 ;100:         dataSliceRelease();


                     190 

00000090 eb000000*   191 	bl	dataSliceRelease

                     192 ;101:         ERROR_REPORT("IEDTree_init error");


                     193 ;102:         return;


                     194 

00000094 ea000008    195 	b	.L2

                     196 .L24:

                     197 ;103:     }


                     198 ;104: 


                     199 ;105:     //Инициализируем начальные значения из DataSlice.


                     200 ;106:     //При этом установятся флаги изменения DA, но они здесь не анализируются,


                     201 ;107:     //и ни на что не влияют


                     202 ;108:     IEDTree_updateFromDataSlice();


                     203 

00000098 eb000000*   204 	bl	IEDTree_updateFromDataSlice

                     205 ;109: 


                     206 ;110:     dataSliceRelease();


                     207 

0000009c eb000000*   208 	bl	dataSliceRelease

                     209 ;111: 


                     210 ;112:     initReports();


                     211 

000000a0 eb000000*   212 	bl	initReports

                     213 ;113:     GOOSE_init();


                     214 

000000a4 eb000000*   215 	bl	GOOSE_init

                     216 ;114:     Timers_init();


                     217 

000000a8 eb000000*   218 	bl	Timers_init

                     219 ;115: 


                     220 ;116:     TRACE("Allocated MM memory %d KB", MM_getAllocated() / 1024);


                     221 ;117: 


                     222 ;118:     TRACE("Start listening");


                     223 ;119:     if(!startListening())


                     224 

000000ac eb000000*   225 	bl	startListening

000000b0 e3500000    226 	cmp	r0,0

000000b4 159f000c*   227 	ldrne	r0,.L160

                     228 ;120:     {


                     229 

                     230 ;121:         return;


                     231 

                     232 ;122:     }


                     233 ;123: 



                                                                      Page 5
                                                              C:\Users\<USER>\AppData\Local\Temp\gh_1jg1.s
                     234 ;124:     handleIncomingConnections(handleMMSConnection);


                     235 

000000b8 1b000000*   236 	blne	handleIncomingConnections

                     237 .L2:

000000bc e28dd004    238 	add	sp,sp,4

000000c0 e8bd8000    239 	ldmfd	[sp]!,{pc}

                     240 	.endf	serverMain

                     241 	.align	4

                     242 ;iedModelSize	[sp]	local

                     243 ;pIedModel	r1	local

                     244 

                     245 	.section ".bss","awb"

                     246 .L120:

                     247 	.data

                     248 	.text

                     249 

                     250 ;125: }


                     251 	.align	4

                     252 .L159:

000000c4 00000000*   253 	.data.w	iedModel

                     254 	.type	.L159,$object

                     255 	.size	.L159,4

                     256 

                     257 .L160:

000000c8 00000000*   258 	.data.w	handleMMSConnection

                     259 	.type	.L160,$object

                     260 	.size	.L160,4

                     261 

                     262 	.align	4

                     263 ;iedModel	iedModel	import

                     264 

                     265 	.data

                     266 	.ghsnote version,6

                     267 	.ghsnote tools,3

                     268 	.ghsnote options,0

                     269 	.text

                     270 	.align	4

