{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
       
    "tasks": [
      {
        "label": "build",
        "type": "shell",
        "command": "nmake",
        "args": [
          "PLATFORM=IEDNEXUS"
        ],
        "group": {
          "kind": "build",
          "isDefault": true
        },
        "problemMatcher": [
          "$msCompile"
        ],
        "detail": "Generated task to run nmake"
      }
    ]
}